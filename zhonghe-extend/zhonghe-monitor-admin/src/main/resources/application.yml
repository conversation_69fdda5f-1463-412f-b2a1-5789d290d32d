server:
  port: 29050
spring:
  application:
    name: <PERSON><PERSON><PERSON>e-Monitor-Admin
  profiles:
    active: @profiles.active@

--- # 监控中心服务端配置
spring:
  security:
    user:
      name: zhanghe
      password: 123456
  boot:
    admin:
      context-path: /admin

--- # Actuator 监控端点的配置项
management:
  endpoints:
    web:
      # Actuator 提供的 API 接口的根目录。默认为 /actuator
      base-path: /actuator
      exposure:
        # 需要开放的端点。默认值只打开 health 和 info 两个端点。通过设置 * ，可以开放所有端点。
        # 生产环境不建议放开所有 根据项目需求放开即可
        include: @endpoints.include@
  endpoint:
    logfile:
      external-file: ./logs/zhonghe-monitor-admin.log
