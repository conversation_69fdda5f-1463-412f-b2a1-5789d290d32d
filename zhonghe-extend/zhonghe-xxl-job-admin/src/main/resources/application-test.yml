--- # 监控配置
spring:
  boot:
    admin:
      # Spring Boot Admin Client 客户端的相关配置
      client:
        # 增加客户端开关
        enabled: true
        # 设置 Spring Boot Admin Server 地址
        url: http://localhost:29050/admin
        instance:
          service-host-type: IP
        username: zhonghe
        password: 123456

--- # 数据库配置
spring:
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driverClassName: org.postgresql.Driver
      # jdbc 所有参数配置参考 https://lionli.blog.csdn.net/article/details/122018562
    # rewriteBatchedStatements=true 批处理优化 大幅提升批量插入更新删除性能(对数据库有性能损耗 使用批量操作应考虑性能问题)
    url: jdbc:postgresql://*************:15432/zhonhe_test?useUnicode=true&characterEncoding=utf8&useSSL=true&autoReconnect=true&reWriteBatchedInserts=true
    username: postgis
    password: postgis@123.0
    hikari:
      auto-commit: true
      connection-test-query: SELECT 1
      connection-timeout: 10000
      idle-timeout: 30000
      max-lifetime: 900000
      maximum-pool-size: 30
      minimum-idle: 10
      pool-name: HikariCP
      validation-timeout: 1000

--- # 邮件配置
spring:
  mail:
    from: <EMAIL>
    host: smtp.qq.com
    username: 379659253
    password: Love.379659253
    port: 25
    properties:
      mail:
        smtp:
          auth: true
          socketFactory:
            class: javax.net.ssl.SSLSocketFactory
          starttls:
            enable: true
            required: true
