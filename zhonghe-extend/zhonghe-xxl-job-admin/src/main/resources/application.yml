--- # server 配置
server:
  port: 29060
  servlet:
    context-path: /xxl-job-admin
spring:
  application:
    name: zhonghe-xxl-job-admin
  profiles:
    active: @profiles.active@
  mvc:
    servlet:
      load-on-startup: 0
    static-path-pattern: /static/**
  web:
    resources:
      static-locations: classpath:/static/

--- # mybatis 配置
mybatis:
  mapper-locations: classpath:/mybatis-mapper/*Mapper.xml
  # 控制台打印sql日志
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

--- # 页面配置
spring:
  freemarker:
    charset: UTF-8
    request-context-attribute: request
    settings:
      number_format: 0.##########
    suffix: .ftl
    templateLoaderPath: classpath:/templates/

--- # Actuator 监控端点的配置项
management:
  health:
    mail:
      enabled: false
  endpoints:
    web:
      # Actuator 提供的 API 接口的根目录。默认为 /actuator
      base-path: /actuator
      exposure:
        # 需要开放的端点。默认值只打开 health 和 info 两个端点。通过设置 * ，可以开放所有端点。
        # 生产环境不建议放开所有 根据项目需求放开即可
        include: @endpoints.include@
  endpoint:
    logfile:
      external-file: ./logs/zhonghe-xxl-job-admin.log

--- # xxljob系统配置
xxl:
  job:
    # 鉴权token
    accessToken: xxl-job
    # 国际化
    i18n: zh_CN
    # 日志清理
    logretentiondays: 30
    triggerpool:
      fast:
        max: 200
      slow:
        max: 100
