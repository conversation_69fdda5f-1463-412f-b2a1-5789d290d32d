package com.zhonghe.query.domain.entity;

import java.util.Date;
import java.io.Serializable;

/**
 * (Oldentityinfo)实体类
 *
 * <AUTHOR>
 * @since 2022-10-27 09:41:05
 */
public class Oldentityinfo implements Serializable {
    private static final long serialVersionUID = -99252587928837260L;

    private String hisid;

    private String entityNo;

    private String registerID;

    private String entityName;

    private String principal;

    private Date approveDate;


    public String getHisid() {
        return hisid;
    }

    public void setHisid(String hisid) {
        this.hisid = hisid;
    }

    public String getEntityNo() {
        return entityNo;
    }

    public void setEntityNo(String entityNo) {
        this.entityNo = entityNo;
    }

    public String getRegisterID() {
        return registerID;
    }

    public void setRegisterID(String registerID) {
        this.registerID = registerID;
    }

    public String getEntityName() {
        return entityName;
    }

    public void setEntityName(String entityName) {
        this.entityName = entityName;
    }

    public String getPrincipal() {
        return principal;
    }

    public void setPrincipal(String principal) {
        this.principal = principal;
    }

    public Date getApproveDate() {
        return approveDate;
    }

    public void setApproveDate(Date approveDate) {
        this.approveDate = approveDate;
    }

}

