<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>zhonghe-extend</artifactId>
        <groupId>com.zhonghe</groupId>
        <version>2.0</version>
    </parent>
    <artifactId>zhonghe-xxl-job-admin</artifactId>
    <packaging>jar</packaging>

    <properties>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-parent</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>


        </dependencies>
    </dependencyManagement>

    <dependencies>

        <!-- starter-web：spring-webmvc + autoconfigure + logback + yaml + tomcat -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <!-- starter-test：junit + spring-test + mockito -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- PostgreSql -->
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
        </dependency>



        <!-- freemarker-starter -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-freemarker</artifactId>
        </dependency>

        <!-- mail-starter -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-mail</artifactId>
        </dependency>

        <!-- starter-actuator -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <!-- mybatis-starter：mybatis + mybatis-spring + hikari（default） -->
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
            <version>${spring-boot.mybatis}</version>
        </dependency>
        <!-- mysql -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <dependency>
            <groupId>de.codecentric</groupId>
            <artifactId>spring-boot-admin-starter-client</artifactId>
        </dependency>

        <!-- xxl-job-core -->
        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.7.20</version>
            <scope>compile</scope>
        </dependency>

    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <!-- docker -->
            <!--            <plugin>-->
            <!--                <groupId>com.spotify</groupId>-->
            <!--                <artifactId>docker-maven-plugin</artifactId>-->
            <!--                <version>${docker.plugin.version}</version>-->
            <!--                <configuration>-->
            <!--                    &lt;!&ndash; made of '[a-z0-9-_.]' &ndash;&gt;-->
            <!--                    <imageName>${docker.namespace}/${project.artifactId}:${project.version}</imageName>-->
            <!--                    <dockerDirectory>${project.basedir}</dockerDirectory>-->
            <!--                    <dockerHost>${docker.registry.host}</dockerHost>-->
            <!--                    <registryUrl>${docker.registry.url}</registryUrl>-->
            <!--                    <serverId>${docker.registry.url}</serverId>-->
            <!--                    <resources>-->
            <!--                        <resource>-->
            <!--                            <targetPath>/</targetPath>-->
            <!--                            <directory>${project.build.directory}</directory>-->
            <!--                            <include>${project.build.finalName}.jar</include>-->
            <!--                        </resource>-->
            <!--                    </resources>-->
            <!--                </configuration>-->
            <!--            </plugin>-->
        </plugins>
    </build>

</project>
