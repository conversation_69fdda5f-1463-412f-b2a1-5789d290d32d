package com.zhonghe.oss.constant;

import java.util.Arrays;
import java.util.List;

/**
 * 对象存储常量
 *
 * <AUTHOR> Li
 */
public interface OssConstant {

    /**
     * 云盘总容量40G
     */
    Long CLOUD_DISK_TOTAL_CAPACITY = 40 * 1024 * 1024 * 1024L;
    /**
     * OSS模块KEY
     */
    String SYS_OSS_KEY = "sys_oss:";

    /**
     * 对象存储配置KEY
     */
    String OSS_CONFIG_KEY = "OssConfig";

    /**
     * 缓存配置KEY
     */
    String CACHE_CONFIG_KEY = SYS_OSS_KEY + OSS_CONFIG_KEY;

    /**
     * 预览列表资源开关Key
     */
    String PEREVIEW_LIST_RESOURCE_KEY = "sys.oss.previewListResource";

    /**
     * 系统数据ids
     */
    List<Integer> SYSTEM_DATA_IDS = Arrays.asList(1, 2, 3, 4);

    /**
     * 云服务商
     */
    String[] CLOUD_SERVICE = new String[]{"aliyun", "qcloud", "qiniu"};

    /**
     * https 状态
     */
    String IS_HTTPS = "Y";

    /**
     * 类型标志 1:minio桶 2：minio文件夹 3：minio文件
     */
    int BUCKET_TYPE_BUCKET = 1;
    int BUCKET_TYPE_FOLD = 2;
    int BUCKET_TYPE_FILE = 3;

    /**
     * 桶所有权
     */
    String BUCKET_NAME_MINE = "owner";

    String BUCKET_NAME_VIEW = "others";

    /**
     * 是否是私有盘控制
     */
    Integer BUCKET_PERSONAL_NO = 0;
    Integer BUCKET_PERSONAL_YES = 1;

    /**
     * 用户删除tag
     */
    String DELETE_DELAY_TAG = "n2-del-7d";

    /**
     * 1 全部部门给读权限
     * */
    Integer BUCKET_ALL_READ = 1;

    /**
     * 云盘总容量
     */
    String BUCKET_TOTAL_SIZE = "bucketTotalSize";
    /**
     * 已使用容量
     */
    String BUCKET_USED_SIZE = "bucketUsedSize";

    /**
     * 云盘实际剩余容量
     */
    String BUCKET_REMAIN_SIZE = "bucketRemainSize";
    String BUCKET_REMAIN_Max_SIZE = "bucketRemainMaxSize";
    /**
     * 文件数量
     */
    String BUCKET_FILE_COUNT = "bucketFileCount";
    /**
     * 云盘总数
     */
    String BUCKET_COUNT = "bucketCount";
    /**
     * 云盘名称
     */
    String BUCKET_NAME = "bucketName";
    /**
     * 是否是私有盘
     */
    String PERSONAL = "personal";
    Boolean BUCKET_UPLOAD_FINISH = true;
    Boolean BUCKET_UPLOAD_FINISH_NOT = false;

    /**
     * 单位byte
     */
    String BUCKET_BASE_USED_SIZE = "bucketBaseSize";

    String HUGE_FILE_UPLOAD = "huge_file_upload";

    /**
     * 预览地址
     */
    String FILE_PREVIEW = "/home/";

    /**
     * 非私有云盘
     */
    Integer NO_PERSONAL = 0;
}
