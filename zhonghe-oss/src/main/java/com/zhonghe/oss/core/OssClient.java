package com.zhonghe.oss.core;

import cn.hutool.core.util.IdUtil;
import com.amazonaws.ClientConfiguration;
import com.amazonaws.Protocol;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.AWSCredentialsProvider;
import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.client.builder.AwsClientBuilder;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3Client;
import com.amazonaws.services.s3.model.AmazonS3Exception;
import com.amazonaws.services.s3.model.BucketLifecycleConfiguration;
import com.amazonaws.services.s3.model.BucketVersioningConfiguration;
import com.amazonaws.services.s3.model.CannedAccessControlList;
import com.amazonaws.services.s3.model.CompleteMultipartUploadRequest;
import com.amazonaws.services.s3.model.CompleteMultipartUploadResult;
import com.amazonaws.services.s3.model.CopyObjectRequest;
import com.amazonaws.services.s3.model.CopyObjectResult;
import com.amazonaws.services.s3.model.CreateBucketRequest;
import com.amazonaws.services.s3.model.GeneratePresignedUrlRequest;
import com.amazonaws.services.s3.model.GetObjectMetadataRequest;
import com.amazonaws.services.s3.model.GetObjectRequest;
import com.amazonaws.services.s3.model.InitiateMultipartUploadRequest;
import com.amazonaws.services.s3.model.InitiateMultipartUploadResult;
import com.amazonaws.services.s3.model.ListObjectsV2Request;
import com.amazonaws.services.s3.model.ListObjectsV2Result;
import com.amazonaws.services.s3.model.ListPartsRequest;
import com.amazonaws.services.s3.model.ObjectListing;
import com.amazonaws.services.s3.model.ObjectMetadata;
import com.amazonaws.services.s3.model.PartETag;
import com.amazonaws.services.s3.model.PartListing;
import com.amazonaws.services.s3.model.PartSummary;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.amazonaws.services.s3.model.S3Object;
import com.amazonaws.services.s3.model.S3ObjectSummary;
import com.amazonaws.services.s3.model.Tag;
import com.amazonaws.services.s3.model.UploadPartRequest;
import com.amazonaws.services.s3.model.UploadPartResult;
import com.amazonaws.services.s3.model.lifecycle.LifecycleFilter;
import com.amazonaws.services.s3.model.lifecycle.LifecycleTagPredicate;
import com.zhonghe.common.utils.DateUtils;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.oss.constant.OssConstant;
import com.zhonghe.oss.entity.UploadResult;
import com.zhonghe.oss.enumd.PolicyType;
import com.zhonghe.oss.exception.OssException;
import com.zhonghe.oss.properties.OssProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.fileupload.FileItem;
import org.springframework.http.MediaType;
import org.springframework.http.MediaTypeFactory;
import org.springframework.web.multipart.commons.CommonsMultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URL;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * S3 存储协议 所有兼容S3协议的云厂商均支持
 * 阿里云 腾讯云 七牛云 minio
 *
 * <AUTHOR> Li
 */
@Slf4j
public class OssClient {

    private final String configKey;

    private final OssProperties properties;

    private final AmazonS3 client;

    public OssClient(String configKey, OssProperties ossProperties) {
        this.configKey = configKey;
        properties = ossProperties;
        try {
            AwsClientBuilder.EndpointConfiguration endpointConfig =
                new AwsClientBuilder.EndpointConfiguration(properties.getEndpoint(), properties.getRegion());

            AWSCredentials credentials = new BasicAWSCredentials(properties.getAccessKey(), properties.getSecretKey());
            AWSCredentialsProvider credentialsProvider = new AWSStaticCredentialsProvider(credentials);
            ClientConfiguration clientConfig = new ClientConfiguration();
            if (OssConstant.IS_HTTPS.equals(properties.getIsHttps())) {
                clientConfig.setProtocol(Protocol.HTTPS);
            } else {
                clientConfig.setProtocol(Protocol.HTTP);
            }
            client = AmazonS3Client.builder()
                .withEndpointConfiguration(endpointConfig)
                .withClientConfiguration(clientConfig)
                .withCredentials(credentialsProvider)
                .disableChunkedEncoding()
                .build();

            //createBucket();
        } catch (Exception e) {
            if (e instanceof OssException) {
                throw e;
            }
            throw new OssException("配置错误! 请检查系统配置:[" + e.getMessage() + "]");
        }
    }

    private static String getPolicy(String bucketName, PolicyType policyType) {
        StringBuilder builder = new StringBuilder();
        builder.append("{\n\"Statement\": [\n{\n\"Action\": [\n");
        if (policyType == PolicyType.WRITE) {
            builder.append("\"s3:GetBucketLocation\",\n\"s3:ListBucketMultipartUploads\"\n");
        } else if (policyType == PolicyType.READ_WRITE) {
            builder.append("\"s3:GetBucketLocation\",\n\"s3:ListBucket\",\n\"s3:ListBucketMultipartUploads\"\n");
        } else {
            builder.append("\"s3:GetBucketLocation\"\n");
        }
        builder.append("],\n\"Effect\": \"Allow\",\n\"Principal\": \"*\",\n\"Resource\": \"arn:aws:s3:::");
        builder.append(bucketName);
        builder.append("\"\n},\n");
        if (policyType == PolicyType.READ) {
            builder.append("{\n\"Action\": [\n\"s3:ListBucket\"\n],\n\"Effect\": \"Deny\",\n\"Principal\": \"*\",\n\"Resource\": \"arn:aws:s3:::");
            builder.append(bucketName);
            builder.append("\"\n},\n");
        }
        builder.append("{\n\"Action\": ");
        switch (policyType) {
            case WRITE:
                builder.append("[\n\"s3:AbortMultipartUpload\",\n\"s3:DeleteObject\",\n\"s3:ListMultipartUploadParts\",\n\"s3:PutObject\"\n],\n");
                break;
            case READ_WRITE:
                builder.append("[\n\"s3:AbortMultipartUpload\",\n\"s3:DeleteObject\",\n\"s3:GetObject\",\n\"s3:ListMultipartUploadParts\",\n\"s3:PutObject\"\n],\n");
                break;
            default:
                builder.append("\"s3:GetObject\",\n");
                break;
        }
        builder.append("\"Effect\": \"Allow\",\n\"Principal\": \"*\",\n\"Resource\": \"arn:aws:s3:::");
        builder.append(bucketName);
        builder.append("/*\"\n}\n],\n\"Version\": \"2012-10-17\"\n}\n");
        return builder.toString();
    }

    public void createBucket(String bucketName) {
        try {
            if (client.doesBucketExistV2(bucketName)) {
                return;
            }
            CreateBucketRequest createBucketRequest = new CreateBucketRequest(bucketName);
            createBucketRequest.setCannedAcl(CannedAccessControlList.PublicReadWrite);

            client.createBucket(createBucketRequest);
            client.setBucketPolicy(bucketName, getPolicy(bucketName, PolicyType.READ_WRITE));

            BucketLifecycleConfiguration.Rule rule = new BucketLifecycleConfiguration.Rule()
                .withFilter(new LifecycleFilter(new LifecycleTagPredicate(new Tag(OssConstant.DELETE_DELAY_TAG, "true"))))
                .withId("delete-rule")
                .withExpirationInDays(7)
                .withStatus(BucketVersioningConfiguration.ENABLED);

            BucketLifecycleConfiguration lifecycleConfiguration = new BucketLifecycleConfiguration().withRules(rule);
            client.setBucketLifecycleConfiguration(bucketName, lifecycleConfiguration);
            /*BucketVersioningConfiguration configuration = new BucketVersioningConfiguration().withStatus(BucketVersioningConfiguration.ENABLED);
            SetBucketVersioningConfigurationRequest request = new SetBucketVersioningConfigurationRequest(bucketName, configuration);
            client.setBucketVersioningConfiguration(request);*/
        } catch (Exception e) {
            throw new OssException("创建Bucket失败, 请核对配置信息:[" + e.getMessage() + "]");
        }
    }

    public UploadResult upload(byte[] data, String backetName, String path, String contentType) {
        return upload(new ByteArrayInputStream(data), backetName, path, contentType);
    }

    public UploadResult upload(InputStream inputStream, String bucketName, String path, String contentType) {
        try {
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentType(contentType);
            metadata.setContentLength(inputStream.available());
            client.putObject(new PutObjectRequest(bucketName, path, inputStream, metadata));
        } catch (Exception e) {
            throw new OssException("上传文件失败，请检查配置信息:[" + e.getMessage() + "]");
        }
        return UploadResult.builder().url(path).filename(path).build();
    }

    public void delete(String path) {
        path = path.replace(getUrl() + "/", "");
        try {
            client.deleteObject(properties.getBucketName(), path);
        } catch (Exception e) {
            throw new OssException("上传文件失败，请检查配置信息:[" + e.getMessage() + "]");
        }
    }

    public UploadResult uploadSuffix(byte[] data, String bucketName, String suffix, String contentType) {
        return upload(data, bucketName == null ? properties.getBucketName() : bucketName, getPath(properties.getPrefix(), suffix), contentType);
    }

    public UploadResult uploadSuffix(InputStream inputStream, String bucket, String suffix, String contentType) {
        return upload(inputStream, bucket, getPath(properties.getPrefix(), suffix), contentType);
    }

    /**
     * 上传文件
     *
     * @param bucketName     桶名
     * @param tempFile       待上传文件
     * @param remoteFileName 文件名
     * @return
     */
    public boolean uploadToS3(String bucketName, File tempFile, String remoteFileName, CannedAccessControlList fileType) {

        try {

            if (!client.doesBucketExistV2(bucketName)) {
                client.createBucket(bucketName);
            }
            client.putObject(new PutObjectRequest(bucketName, remoteFileName, tempFile).withCannedAcl(fileType));
            return true;
        } catch (Exception ase) {
            log.error("amazonS3上传文件File模式异常 " + ase.getMessage(), ase);
        }
        return false;
    }

    /**
     * 上传文件
     *
     * @param bucketName     桶名
     * @param multipartFile  待上传文件
     * @param remoteFileName 文件名
     * @return
     */
    public boolean uploadToS3(String bucketName, CommonsMultipartFile multipartFile, String remoteFileName, CannedAccessControlList fileType) {

        InputStream in = null;
        try {
            in = multipartFile.getInputStream();
            FileItem fileItem = multipartFile.getFileItem();

            if (!client.doesBucketExistV2(bucketName)) {
                client.createBucket(bucketName);
            }
            ObjectMetadata omd = new ObjectMetadata();
            omd.setContentType(fileItem.getContentType());
            omd.setContentLength(fileItem.getSize());
            omd.setHeader("filename", fileItem.getName());

            client.putObject(new PutObjectRequest(bucketName, remoteFileName, in, omd).withCannedAcl(fileType));
            return true;

        } catch (Exception ase) {
            log.error("amazonS3上传文件InputStream模式异常 " + ase.getMessage(), ase);

        } finally {
            if (in != null) {
                try {
                    in.close();
                } catch (IOException e) {
                    log.error("amazonS3上传文件 关闭InputStream流异常 " + e.getMessage(), e);
                }
            }
        }
        return false;
    }

    /**
     * 获取文件字节流
     */
    public InputStream getInputStreamFromS3(String bucketName, String key) {
        try {
            GetObjectRequest request = new GetObjectRequest(bucketName, key);
            S3Object object = client.getObject(request);
            return object.getObjectContent();
        } catch (Exception ase) {
            log.error("amazonS3获取文件字节流异常 " + ase.getMessage(), ase);
        }
        return null;
    }

    /**
     * 下载文件
     *
     * @param bucketName     桶名
     * @param remoteFileName 文件名
     * @param path           下载路径
     */
    public boolean downFromS3(String bucketName, String remoteFileName, String path) {
        try {
            GetObjectRequest request = new GetObjectRequest(bucketName, remoteFileName);
            ObjectMetadata metadata = client.getObject(request, new File(path));
            return true;
        } catch (Exception ase) {
            log.error("amazonS3下载文件异常 " + ase.getMessage(), ase);
        }
        return false;
    }

    /**
     * 下载文件
     *
     * @param bucketName
     * @param url
     * @param response
     */
    public void downFromS3(String bucketName, String url, String fileName, HttpServletResponse response) {
        try {
            GetObjectRequest request = new GetObjectRequest(bucketName, url);
            S3Object object = client.getObject(request);
            InputStream in = object.getObjectContent();
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment;filename=" + (fileName == null ? url : fileName));
            OutputStream out = response.getOutputStream();
            byte[] b = new byte[1024];
            int len = 0;
            while ((len = in.read(b)) != -1) {
                out.write(b, 0, len);
            }
            out.flush();
            out.close();
            in.close();
        } catch (AmazonS3Exception ase) {
            throw new OssException("文件不存在");
        } catch (Exception ase) {
            throw new OssException("amazonS3下载文件异常:" + ase.getMessage());
        }

    }

    /**
     * Create a folder in S3
     *
     * @param bucketName
     * @param folderName
     */
    public void createFolder(String bucketName, String folderName) {
        try {
            // create meta-data for your folder and set content-length to 0
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentLength(0);
            // create empty content
            InputStream emptyContent = new ByteArrayInputStream(new byte[0]);
            // create a PutObjectRequest passing the folder name suffixed by /
            PutObjectRequest putObjectRequest = new PutObjectRequest(bucketName, folderName + "/", emptyContent, metadata);
            // send request to S3 to create folder
            client.putObject(putObjectRequest);
        } catch (Exception ase) {
            log.error("amazonS3创建文件夹异常 " + ase.getMessage(), ase);
        }
    }

    /**
     * 删除文件
     *
     * @param bucketName     桶名
     * @param remoteFileName 待删除文件名
     * @throws IOException
     */
    public void delFromS3(String bucketName, String remoteFileName) {
        try {
            client.deleteObject(bucketName, remoteFileName);
        } catch (Exception ase) {
            log.error("amazonS3删除文件异常 " + ase.getMessage(), ase);
        }
    }

    /**
     * 获取短链接 带过期时间
     *
     * @param bucketName     桶名称
     * @param remoteFileName 文件名称
     * @param expiration     过期时间/秒
     */
    public String getUrlFromS3(String bucketName, String remoteFileName, Long expiration) {
        try {
            GeneratePresignedUrlRequest httpRequest = new GeneratePresignedUrlRequest(bucketName, remoteFileName);
            httpRequest.setExpiration(new Date(System.currentTimeMillis() + expiration * 1000));

            URL url = client.generatePresignedUrl(httpRequest);
            return String.valueOf(url);
        } catch (Exception e) {
            log.error("amazonS3获取临时链接异常 " + e.getMessage(), e);
        }
        return null;
    }

    /**
     * 获取永久链接
     *
     * @param bucketName     桶名称
     * @param remoteFileName 文件名称
     */
    public String getUrlFromS3(String bucketName, String remoteFileName) {

        String url = "";
        try {
            GeneratePresignedUrlRequest urlRequest = new GeneratePresignedUrlRequest(bucketName, remoteFileName);
            url = String.valueOf(client.generatePresignedUrl(urlRequest)).split("\\?")[0];
            if (url.indexOf(remoteFileName) == -1) {
                throw new RuntimeException("url文件名称校验不合法");
            }

            return url;

        } catch (Exception e) {
            log.error("amazonS3获取永久链接异常 " + e.getMessage(), e);
            return "";
        }
    }

    /**
     * 获取永久链接-包含证书
     *
     * @param bucketName     桶名称
     * @param remoteFileName 文件名称
     */
    public String getUrlToS3(String bucketName, String remoteFileName) {
        try {
            GeneratePresignedUrlRequest urlRequest = new GeneratePresignedUrlRequest(bucketName, remoteFileName);
            URL url = client.generatePresignedUrl(urlRequest);
            return String.valueOf(url);

        } catch (Exception e) {
            log.error("amazonS3获取永久链接异常 " + e.getMessage(), e);
            return null;
        }
    }

    /**
     * 根据桶名称获取文件集合
     */
    public List<S3ObjectSummary> getFileMsgByBucketName(String bucketName) {
        ObjectListing objectListing = client.listObjects(bucketName);
        List<S3ObjectSummary> objectSummaries = objectListing.getObjectSummaries();
        return objectSummaries;
    }

    /**
     * 获取所有桶的使用量
     *
     * @return
     */
    public double getAllBucketSize() {
        return client.listBuckets().stream()
            .mapToDouble(bucket -> client.listObjectsV2(new ListObjectsV2Request().withBucketName(bucket.getName()))
                .getObjectSummaries()
                .stream()
                .mapToDouble(S3ObjectSummary::getSize)
                .sum())
            .sum();
    }

    /**
     * 获取某一个桶的使用量
     *
     * @param bucket
     * @return
     */
    public double getOneBucketSize(String bucket) {
        return client.listObjectsV2(new ListObjectsV2Request().withBucketName(bucket))
            .getObjectSummaries()
            .stream()
            .mapToDouble(S3ObjectSummary::getSize)
            .sum();
    }

    /**
     * 获取所有桶的对象数量
     *
     * @return
     */
    public int getAllBucketObjectsNum() {
        return client.listBuckets().stream()
            .mapToInt(bucket -> client.listObjectsV2(new ListObjectsV2Request().withBucketName(bucket.getName()))
                .getKeyCount())
            .sum();
    }

    /**
     * 获取某一个桶的对象数量
     *
     * @return
     */
    public double getOneBucketObjectsNum(String bucket) {
        return client.listObjectsV2(new ListObjectsV2Request().withBucketName(bucket))
            .getKeyCount();
    }

    /**
     * 获取桶数量
     *
     * @return
     */
    public int getBucketsNum() {
        return client.listBuckets().size();
    }

    public void getBucketLifecycle(String bucketName) {


        BucketLifecycleConfiguration bucketLifecycleConfiguration = client.getBucketLifecycleConfiguration(bucketName);
        List<BucketLifecycleConfiguration.Rule> rules = bucketLifecycleConfiguration.getRules();
        rules.forEach(rule -> {
            System.out.println(rule.getPrefix());
            System.out.println(rule.getExpirationInDays());
            System.out.println(rule.getExpirationDate());
            System.out.println(rule.getStatus());
        });
    }

    /**
     * 设置对象生命周期
     *
     * @return
     */
    public void setBucketLifecycle(String bucketName, String objectName, int days) {
        // 设置生命周期配置
       /* BucketLifecycleConfiguration.Rule rule = new BucketLifecycleConfiguration.Rule()
            .withPrefix(objectName)
            .withFilter(new LifecycleFilter(new LifecycleTagPredicate(new Tag("expire", "true"))))
            .withExpirationInDays(days)
            .withStatus(BucketLifecycleConfiguration.ENABLED);*/


        List<BucketLifecycleConfiguration.Rule> rules = new ArrayList<>();

        // Create a rule to delete objects with the prefix "logs/" after 30 days
        BucketLifecycleConfiguration.Rule rule1 = new BucketLifecycleConfiguration.Rule()
            .withPrefix(objectName)
            .withId("Delete logs after 30 days")
            .withStatus("Enabled")
            .withExpirationInDays(5);
        rules.add(rule1);


        // Create a bucket lifecycle configuration with the list of rules
        BucketLifecycleConfiguration configuration = new BucketLifecycleConfiguration().withRules(rules);
        client.setBucketLifecycleConfiguration(bucketName, configuration);
    }

    /**
     * 获取uploadId
     *
     * @param bucketName
     * @param key
     * @return
     */
    public String getUploadId(String bucketName, String key) {
        String contentType = MediaTypeFactory.getMediaType(key).orElse(MediaType.APPLICATION_OCTET_STREAM).toString();
        ObjectMetadata objectMetadata = new ObjectMetadata();
        objectMetadata.setContentType(contentType);
        InitiateMultipartUploadResult initiateMultipartUploadResult = client
            .initiateMultipartUpload(new InitiateMultipartUploadRequest(bucketName, key).withObjectMetadata(objectMetadata));
        return initiateMultipartUploadResult.getUploadId();
    }

    public String getUrl() {
        String domain = properties.getDomain();
        if (StringUtils.isNotBlank(domain)) {
            return domain;
        }
        String endpoint = properties.getEndpoint();
        String header = OssConstant.IS_HTTPS.equals(properties.getIsHttps()) ? "https://" : "http://";
        // 云服务商直接返回
        if (StringUtils.containsAny(endpoint, OssConstant.CLOUD_SERVICE)) {
            return header + properties.getBucketName() + "." + endpoint;
        }
        // minio 单独处理
        return header + endpoint + "/" + properties.getBucketName();
    }

    public String getPath(String prefix, String suffix) {
        // 生成uuid
        String uuid = IdUtil.fastSimpleUUID();
        // 文件路径
        String path = DateUtils.datePath() + "/" + uuid;
        if (StringUtils.isNotBlank(prefix)) {
            path = prefix + "/" + path;
        }
        return path + suffix;
    }

    public String getConfigKey() {
        return configKey;
    }

    public boolean setObjectDeleteTag(String bucketName, String url) {
        boolean isSuccess = false;
        try {
            // 获取已经存在的对象的元数据信息
            GetObjectMetadataRequest getObjectMetadataRequest = new GetObjectMetadataRequest(bucketName, url);
            ObjectMetadata objectMetadata = client.getObjectMetadata(getObjectMetadataRequest);

            // 将元数据信息添加新的标签
            objectMetadata.addUserMetadata(OssConstant.DELETE_DELAY_TAG, "true");

            // 将更新后的元数据信息拷贝到原有的对象上
            CopyObjectRequest copyObjectRequest = new CopyObjectRequest(bucketName, url, bucketName, url)
                .withNewObjectMetadata(objectMetadata);
            CopyObjectResult copyObjectResult = client.copyObject(copyObjectRequest);
            isSuccess = true;
        } catch (Exception ignored) {
        } finally {
            return isSuccess;
        }
    }

    public void setBucketContainObjectsDeleteTag(String bucketName) {
        if (client.doesBucketExistV2(bucketName)) {
            ListObjectsV2Result listObjectsV2Result = client.listObjectsV2(bucketName);
            List<S3ObjectSummary> objectSummaries = listObjectsV2Result.getObjectSummaries();

            objectSummaries.forEach(a -> {
                setObjectDeleteTag(bucketName, a.getKey());
            });
        }
    }

    public String copyHugeFileToDestination(String fromBucket, String objectKey, String toBucketName, String suffix) {
        String key = getPath(properties.getPrefix(), suffix);
        try {
            CopyObjectRequest copyObjectRequest = new CopyObjectRequest(fromBucket, objectKey, toBucketName, key);
            CopyObjectResult copyObjectResult = client.copyObject(copyObjectRequest);
        } catch (Exception e) {
            log.error("copyHugeFileToDestination error", e);
        }


        return key;
    }

    public List<PartSummary> listUploadChunk(String uploadId, String bucketName, String objectKey) {

        ListPartsRequest listPartsRequest = new ListPartsRequest(bucketName, objectKey, uploadId);
        PartListing partListing = client.listParts(listPartsRequest);
        return partListing.getParts();

    }

    /**
     * 初始化分片上传
     *
     * @param bucketName
     * @param key
     * @return
     */
    public String initiateMultipartUpload(String bucketName, String key) {
        String contentType = MediaTypeFactory.getMediaType(key).orElse(MediaType.APPLICATION_OCTET_STREAM).toString();
        ObjectMetadata objectMetadata = new ObjectMetadata();
        objectMetadata.setContentType(contentType);

        InitiateMultipartUploadRequest initRequest = new InitiateMultipartUploadRequest(bucketName, key).withObjectMetadata(objectMetadata);
        InitiateMultipartUploadResult initResponse = client.initiateMultipartUpload(initRequest);
        return initResponse.getUploadId();
    }


    public String createObjectKey(String suffix) {
        return getPath(properties.getPrefix(), suffix);
    }

    public void uploadChunkFile(InputStream inputStream, String uploadId, String bucketName, String objectKey, Integer chunk, long size) {
        long minPartSize = 5 * 1024 * 1024;
        UploadPartRequest uploadRequest = new UploadPartRequest()
            .withBucketName(bucketName)
            .withKey(objectKey)
            .withUploadId(uploadId)
            .withPartNumber(chunk)
            .withPartSize(size)
            .withInputStream(inputStream);
        log.info("upload chunk file, uploadId:{}, bucketName:{}, objectKey:{}, chunk:{}, size:{}", uploadId, bucketName, objectKey, chunk, size);
        UploadPartResult uploadResponse = client.uploadPart(uploadRequest);
    }

    public long mergeUploadFile(String bucketName, String objectKey, String uploadId, List<PartSummary> partSummaries) {
        List<PartETag> collect = new ArrayList<>();
        partSummaries.forEach(p -> {
            collect.add(new PartETag(p.getPartNumber(), p.getETag()));
        });

        CompleteMultipartUploadRequest compRequest = new CompleteMultipartUploadRequest(bucketName, objectKey, uploadId, collect);
        CompleteMultipartUploadResult compResponse = client.completeMultipartUpload(compRequest);

        GetObjectMetadataRequest request = new GetObjectMetadataRequest(bucketName, objectKey);
        ObjectMetadata metadata = client.getObjectMetadata(request);
        return metadata.getContentLength();
    }
}
