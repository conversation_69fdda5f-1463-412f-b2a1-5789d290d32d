<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.zhonghe</groupId>
    <artifactId>zhonghe-vue-plus</artifactId>
    <version>2.0</version>

    <name>zhonghe-vue-plus</name>
    <url></url>
    <description>ZhongHe-Vue-Plus后台管理系统</description>

    <properties>
        <zhonghe-vue-plus.version>2.0</zhonghe-vue-plus.version>
        <spring-boot.version>2.6.9</spring-boot.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <maven-jar-plugin.version>3.2.2</maven-jar-plugin.version>
        <spring-boot.mybatis>2.2.2</spring-boot.mybatis>
        <druid.version>1.2.11</druid.version>
        <knife4j.version>3.0.3</knife4j.version>
        <swagger-annotations.version>1.5.22</swagger-annotations.version>
        <poi.version>5.2.2</poi.version>
        <easyexcel.version>3.1.1</easyexcel.version>
        <velocity.version>2.3</velocity.version>
        <satoken.version>1.30.0</satoken.version>
        <mybatis-plus.version>3.5.2</mybatis-plus.version>
        <p6spy.version>3.9.1</p6spy.version>
        <hutool.version>5.8.3</hutool.version>
        <okhttp.version>4.9.3</okhttp.version>
        <spring-boot-admin.version>2.6.7</spring-boot-admin.version>
        <redisson.version>3.25.0</redisson.version>
        <lock4j.version>2.2.1</lock4j.version>
        <dynamic-ds.version>3.5.1</dynamic-ds.version>
        <tlog.version>1.4.3</tlog.version>
        <xxl-job.version>2.3.1</xxl-job.version>
        <flowable.version>6.8.0</flowable.version>
        <pdfbox.version>2.0.23</pdfbox.version>
        <jsoup.version>1.17.2</jsoup.version>
        <!-- 统一 guava 版本 解决隐式漏洞问题 -->
        <guava.version>30.0-jre</guava.version>

        <!-- OSS 配置 -->
        <aws-java-sdk-s3.version>1.12.248</aws-java-sdk-s3.version>
        <!-- SMS 配置 -->
        <aliyun.sms.version>2.0.9</aliyun.sms.version>
        <tencent.sms.version>3.1.537</tencent.sms.version>

        <!-- docker 配置 -->
        <docker.registry.url>localhost</docker.registry.url>
        <docker.registry.host>http://${docker.registry.url}:2375</docker.registry.host>
        <docker.namespace>ruoyi</docker.namespace>
        <docker.plugin.version>1.2.2</docker.plugin.version>

        <!-- 真 GIS-->
        <gis.jts.version>1.19.0</gis.jts.version>
        <gis.geotools.version>23.5</gis.geotools.version>
        <gis.postgis.version>2021.1.0</gis.postgis.version>


    </properties>

    <!-- 依赖声明 -->
    <dependencyManagement>
        <dependencies>

            <!-- SpringBoot的依赖配置-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- hutool 的依赖配置-->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-bom</artifactId>
                <version>${hutool.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- 阿里数据库连接池 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>

            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>knife4j-spring-boot-starter</artifactId>
                <version>${knife4j.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>swagger-annotations</artifactId>
                        <groupId>io.swagger</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-annotations</artifactId>
                <version>${swagger-annotations.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.apache.poi</groupId>
                        <artifactId>poi-ooxml-schemas</artifactId>
                    </exclusion>

                </exclusions>
            </dependency>

            <!-- velocity代码生成使用模板 -->
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity-engine-core</artifactId>
                <version>${velocity.version}</version>
            </dependency>

            <!-- Sa-Token 权限认证, 在线文档：http://sa-token.dev33.cn/ -->
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-spring-boot-starter</artifactId>
                <version>${satoken.version}</version>
            </dependency>
            <!-- Sa-Token 整合 jwt -->
            <dependency>
                <groupId>cn.dev33</groupId>
                <artifactId>sa-token-jwt</artifactId>
                <version>${satoken.version}</version>
            </dependency>

            <!-- dynamic-datasource 多数据源-->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
                <version>${dynamic-ds.version}</version>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-extension</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>
            <!-- sql性能分析插件 -->
            <dependency>
                <groupId>p6spy</groupId>
                <artifactId>p6spy</artifactId>
                <version>${p6spy.version}</version>
            </dependency>

            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp.version}</version>
            </dependency>

            <dependency>
                <groupId>com.amazonaws</groupId>
                <artifactId>aws-java-sdk-s3</artifactId>
                <version>${aws-java-sdk-s3.version}</version>
            </dependency>

            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>dysmsapi20170525</artifactId>
                <version>${aliyun.sms.version}</version>
            </dependency>

            <dependency>
                <groupId>com.tencentcloudapi</groupId>
                <artifactId>tencentcloud-sdk-java</artifactId>
                <version>${tencent.sms.version}</version>
            </dependency>

            <dependency>
                <groupId>de.codecentric</groupId>
                <artifactId>spring-boot-admin-starter-server</artifactId>
                <version>${spring-boot-admin.version}</version>
            </dependency>
            <dependency>
                <groupId>de.codecentric</groupId>
                <artifactId>spring-boot-admin-starter-client</artifactId>
                <version>${spring-boot-admin.version}</version>
            </dependency>

            <!--redisson-->
            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-boot-starter</artifactId>
                <version>${redisson.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.redisson</groupId>
                        <artifactId>redisson-spring-data-32</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>lock4j-redisson-spring-boot-starter</artifactId>
                <version>${lock4j.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.redisson</groupId>
                        <artifactId>redisson-spring-data-32</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.redisson</groupId>
                <artifactId>redisson-spring-data-27</artifactId>
                <version>3.25.0</version>
            </dependency>

            <!-- xxl-job-core -->
            <dependency>
                <groupId>com.xuxueli</groupId>
                <artifactId>xxl-job-core</artifactId>
                <version>${xxl-job.version}</version>
            </dependency>

            <dependency>
                <groupId>com.yomahub</groupId>
                <artifactId>tlog-web-spring-boot-starter</artifactId>
                <version>${tlog.version}</version>
                <exclusions>
                    <exclusion>
                        <artifactId>log4j</artifactId>
                        <groupId>log4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>dom4j</artifactId>
                        <groupId>dom4j</groupId>
                    </exclusion>
                    <exclusion>
                        <artifactId>commons-beanutils</artifactId>
                        <groupId>commons-beanutils</groupId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>com.yomahub</groupId>
                <artifactId>tlog-xxljob-spring-boot-starter</artifactId>
                <version>${tlog.version}</version>
            </dependency>

            <!-- 统一 guava 版本 解决隐式漏洞问题 -->
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>

            <!-- 定时任务 -->
            <dependency>
                <groupId>com.zhonghe</groupId>
                <artifactId>zhonghe-job</artifactId>
                <version>${zhonghe-vue-plus.version}</version>
            </dependency>

            <!-- 代码生成-->
            <dependency>
                <groupId>com.zhonghe</groupId>
                <artifactId>zhonghe-generator</artifactId>
                <version>${zhonghe-vue-plus.version}</version>
            </dependency>

            <!-- 核心模块-->
            <dependency>
                <groupId>com.zhonghe</groupId>
                <artifactId>zhonghe-framework</artifactId>
                <version>${zhonghe-vue-plus.version}</version>
            </dependency>

            <!-- 系统模块-->
            <dependency>
                <groupId>com.zhonghe</groupId>
                <artifactId>zhonghe-system</artifactId>
                <version>${zhonghe-vue-plus.version}</version>
            </dependency>

            <!-- 通用工具-->
            <dependency>
                <groupId>com.zhonghe</groupId>
                <artifactId>zhonghe-common</artifactId>
                <version>${zhonghe-vue-plus.version}</version>
            </dependency>

            <!-- OSS对象存储模块 -->
            <dependency>
                <groupId>com.zhonghe</groupId>
                <artifactId>zhonghe-oss</artifactId>
                <version>${zhonghe-vue-plus.version}</version>
            </dependency>

            <!-- SMS短信模块 -->
            <dependency>
                <groupId>com.zhonghe</groupId>
                <artifactId>zhonghe-sms</artifactId>
                <version>${zhonghe-vue-plus.version}</version>
            </dependency>

            <!-- flowable -->
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-spring-boot-starter-process</artifactId>
                <version>${flowable.version}</version>
            </dependency>


            <!-- demo模块 -->
            <dependency>
                <groupId>com.zhonghe</groupId>
                <artifactId>zhonghe-demo</artifactId>
                <version>${zhonghe-vue-plus.version}</version>
            </dependency>

            <!--石龙一张图-->
            <dependency>
                <groupId>com.zhonghe</groupId>
                <artifactId>zhonghe-onemap</artifactId>
                <version>${zhonghe-vue-plus.version}</version>
            </dependency>

            <!-- GIS模块功能 -->
            <dependency>
                <groupId>org.locationtech.jts</groupId>
                <artifactId>jts-core</artifactId>
                <version>${gis.jts.version}</version>
            </dependency>
            <dependency>
                <groupId>org.geotools</groupId>
                <artifactId>gt-main</artifactId>
                <version>${gis.geotools.version}</version>
            </dependency>
            <dependency>
                <groupId>org.geotools</groupId>
                <artifactId>gt-geojson</artifactId>
                <version>${gis.geotools.version}</version>
            </dependency>
            <dependency>
                <groupId>net.postgis</groupId>
                <artifactId>postgis-jdbc</artifactId>
                <version>${gis.postgis.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zhonghe</groupId>
                <artifactId>zhonghe-cop</artifactId>
                <version>${zhonghe-vue-plus.version}</version>
            </dependency>

            <dependency>
                <groupId>com.zhonghe</groupId>
                <artifactId>zhonghe-onlyoffice</artifactId>
                <version>${zhonghe-vue-plus.version}</version>
            </dependency>

            <dependency>
                <groupId>io.qdrant</groupId>
                <artifactId>client</artifactId>
                <version>1.13.0</version>
            </dependency>
            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-netty-shaded</artifactId>
                <version>1.65.1</version>
            </dependency>
            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-protobuf</artifactId>
                <version>1.65.1</version>
            </dependency>
            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-stub</artifactId>
                <version>1.65.1</version>
            </dependency>
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>1.18.22</version>
                <scope>compile</scope>
            </dependency>
            <dependency>
                <groupId>com.zhonghe</groupId>
                <artifactId>zhonghe-fengqiao</artifactId>
                <version>${zhonghe-vue-plus.version}</version>
            </dependency>
            <!-- PDFBox依赖 -->
            <dependency>
                <groupId>org.apache.pdfbox</groupId>
                <artifactId>pdfbox</artifactId>
                <version>${pdfbox.version}</version>
            </dependency>

            <dependency>
                <groupId>org.jsoup</groupId>
                <artifactId>jsoup</artifactId>
                <version>${jsoup.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <modules>
        <module>zhonghe-admin</module>
        <module>zhonghe-framework</module>
        <module>zhonghe-system</module>
        <module>zhonghe-job</module>
        <module>zhonghe-generator</module>
        <module>zhonghe-common</module>
        <module>zhonghe-demo</module>
        <module>zhonghe-extend</module>
        <module>zhonghe-oss</module>
        <module>zhonghe-sms</module>
        <module>zhonghe-ai</module>
        <module>zhonghe-onemap</module>
        <module>zhonghe-cop</module>
        <module>zhonghe-fengqiao</module>
        <module>zhonghe-onlyoffice</module>
    </modules>
    <packaging>pom</packaging>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.9.0</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <!-- 关闭过滤 -->
                <filtering>false</filtering>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <!-- 引入所有 匹配文件进行过滤 -->
                <includes>
                    <include>application*</include>
                    <include>bootstrap*</include>
                    <include>banner*</include>
                </includes>
                <!-- 启用过滤 即该资源中的变量将会被过滤器中的值替换 -->
                <filtering>true</filtering>
            </resource>
        </resources>
    </build>

    <repositories>
        <repository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>https://maven.aliyun.com/repository/public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
        <repository>
            <id>osgeo</id>
            <name>OSGeo Release Repository</name>
            <url>https://repo.osgeo.org/repository/release/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>https://maven.aliyun.com/repository/public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>

    <profiles>
        <!--开发环境-->
        <profile>
            <id>dev</id>
            <properties>
                <!-- 环境标识，需要与配置文件的名称相对应 -->
                <profiles.active>dev</profiles.active>
                <logging.level>debug</logging.level>
                <knife4j.production>false</knife4j.production>
                <endpoints.include>'*'</endpoints.include>
            </properties>
            <activation>
                <!-- 默认环境 -->
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <!--测试环境-->
        <profile>
            <id>test</id>
            <properties>
                <!-- 环境标识，需要与配置文件的名称相对应 -->
                <profiles.active>test</profiles.active>
                <logging.level>debug</logging.level>
                <knife4j.production>false</knife4j.production>
                <endpoints.include>'*'</endpoints.include>
            </properties>
        </profile>
        <!--正式环境-->
        <profile>
            <id>prod</id>
            <properties>
                <profiles.active>prod</profiles.active>
                <logging.level>warn</logging.level>
                <knife4j.production>false</knife4j.production>
                <endpoints.include>health, info, logfile</endpoints.include>
            </properties>
        </profile>
    </profiles>

</project>
