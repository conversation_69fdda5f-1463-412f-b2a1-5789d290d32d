/*
 Navicat Premium Data Transfer

 Source Server         : *************
 Source Server Type    : PostgreSQL
 Source Server Version : 130012
 Source Host           : *************:15432
 Source Catalog        : water_iot_platform
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 130012
 File Encoding         : 65001

 Date: 04/11/2024 11:16:29
*/


-- ----------------------------
-- Sequence structure for act_evt_log_log_nr__seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."act_evt_log_log_nr__seq";
CREATE SEQUENCE "public"."act_evt_log_log_nr__seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 2147483647
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for act_hi_tsk_log_id__seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."act_hi_tsk_log_id__seq";
CREATE SEQUENCE "public"."act_hi_tsk_log_id__seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 2147483647
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for infra_api_access_log_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."infra_api_access_log_id_seq";
CREATE SEQUENCE "public"."infra_api_access_log_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for infra_api_error_log_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."infra_api_error_log_id_seq";
CREATE SEQUENCE "public"."infra_api_error_log_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for read_the_information_information_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."read_the_information_information_id_seq";
CREATE SEQUENCE "public"."read_the_information_information_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for system_error_code_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."system_error_code_seq";
CREATE SEQUENCE "public"."system_error_code_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for system_sms_log_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "public"."system_sms_log_seq";
CREATE SEQUENCE "public"."system_sms_log_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Table structure for act_evt_log
-- ----------------------------
DROP TABLE IF EXISTS "public"."act_evt_log";
CREATE TABLE "public"."act_evt_log" (
  "log_nr_" int4 NOT NULL DEFAULT nextval('act_evt_log_log_nr__seq'::regclass),
  "type_" varchar(64) COLLATE "pg_catalog"."default",
  "proc_def_id_" varchar(64) COLLATE "pg_catalog"."default",
  "proc_inst_id_" varchar(64) COLLATE "pg_catalog"."default",
  "execution_id_" varchar(64) COLLATE "pg_catalog"."default",
  "task_id_" varchar(64) COLLATE "pg_catalog"."default",
  "time_stamp_" timestamp(6) NOT NULL,
  "user_id_" varchar(255) COLLATE "pg_catalog"."default",
  "data_" bytea,
  "lock_owner_" varchar(255) COLLATE "pg_catalog"."default",
  "lock_time_" timestamp(6),
  "is_processed_" int2 DEFAULT 0
)
;

-- ----------------------------
-- Records of act_evt_log
-- ----------------------------

-- ----------------------------
-- Table structure for act_ge_bytearray
-- ----------------------------
DROP TABLE IF EXISTS "public"."act_ge_bytearray";
CREATE TABLE "public"."act_ge_bytearray" (
  "id_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "rev_" int4,
  "name_" varchar(255) COLLATE "pg_catalog"."default",
  "deployment_id_" varchar(64) COLLATE "pg_catalog"."default",
  "bytes_" bytea,
  "generated_" bool
)
;

-- ----------------------------
-- Records of act_ge_bytearray
-- ----------------------------

-- ----------------------------
-- Table structure for act_ge_property
-- ----------------------------
DROP TABLE IF EXISTS "public"."act_ge_property";
CREATE TABLE "public"."act_ge_property" (
  "name_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "value_" varchar(300) COLLATE "pg_catalog"."default",
  "rev_" int4
)
;

-- ----------------------------
-- Records of act_ge_property
-- ----------------------------
INSERT INTO "public"."act_ge_property" VALUES ('common.schema.version', '*******', 1);
INSERT INTO "public"."act_ge_property" VALUES ('next.dbid', '1', 1);
INSERT INTO "public"."act_ge_property" VALUES ('identitylink.schema.version', '*******', 1);
INSERT INTO "public"."act_ge_property" VALUES ('entitylink.schema.version', '*******', 1);
INSERT INTO "public"."act_ge_property" VALUES ('eventsubscription.schema.version', '*******', 1);
INSERT INTO "public"."act_ge_property" VALUES ('task.schema.version', '*******', 1);
INSERT INTO "public"."act_ge_property" VALUES ('variable.schema.version', '*******', 1);
INSERT INTO "public"."act_ge_property" VALUES ('job.schema.version', '*******', 1);
INSERT INTO "public"."act_ge_property" VALUES ('batch.schema.version', '*******', 1);
INSERT INTO "public"."act_ge_property" VALUES ('schema.version', '*******', 1);
INSERT INTO "public"."act_ge_property" VALUES ('schema.history', 'create(*******)', 1);
INSERT INTO "public"."act_ge_property" VALUES ('cfg.execution-related-entities-count', 'true', 1);
INSERT INTO "public"."act_ge_property" VALUES ('cfg.task-related-entities-count', 'true', 1);

-- ----------------------------
-- Table structure for act_hi_actinst
-- ----------------------------
DROP TABLE IF EXISTS "public"."act_hi_actinst";
CREATE TABLE "public"."act_hi_actinst" (
  "id_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "rev_" int4 DEFAULT 1,
  "proc_def_id_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "proc_inst_id_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "execution_id_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "act_id_" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "task_id_" varchar(64) COLLATE "pg_catalog"."default",
  "call_proc_inst_id_" varchar(64) COLLATE "pg_catalog"."default",
  "act_name_" varchar(255) COLLATE "pg_catalog"."default",
  "act_type_" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "assignee_" varchar(255) COLLATE "pg_catalog"."default",
  "start_time_" timestamp(6) NOT NULL,
  "end_time_" timestamp(6),
  "transaction_order_" int4,
  "duration_" int8,
  "delete_reason_" varchar(4000) COLLATE "pg_catalog"."default",
  "tenant_id_" varchar(255) COLLATE "pg_catalog"."default" DEFAULT ''::character varying
)
;

-- ----------------------------
-- Records of act_hi_actinst
-- ----------------------------

-- ----------------------------
-- Table structure for act_hi_attachment
-- ----------------------------
DROP TABLE IF EXISTS "public"."act_hi_attachment";
CREATE TABLE "public"."act_hi_attachment" (
  "id_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "rev_" int4,
  "user_id_" varchar(255) COLLATE "pg_catalog"."default",
  "name_" varchar(255) COLLATE "pg_catalog"."default",
  "description_" varchar(4000) COLLATE "pg_catalog"."default",
  "type_" varchar(255) COLLATE "pg_catalog"."default",
  "task_id_" varchar(64) COLLATE "pg_catalog"."default",
  "proc_inst_id_" varchar(64) COLLATE "pg_catalog"."default",
  "url_" varchar(4000) COLLATE "pg_catalog"."default",
  "content_id_" varchar(64) COLLATE "pg_catalog"."default",
  "time_" timestamp(6)
)
;

-- ----------------------------
-- Records of act_hi_attachment
-- ----------------------------

-- ----------------------------
-- Table structure for act_hi_comment
-- ----------------------------
DROP TABLE IF EXISTS "public"."act_hi_comment";
CREATE TABLE "public"."act_hi_comment" (
  "id_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "type_" varchar(255) COLLATE "pg_catalog"."default",
  "time_" timestamp(6) NOT NULL,
  "user_id_" varchar(255) COLLATE "pg_catalog"."default",
  "task_id_" varchar(64) COLLATE "pg_catalog"."default",
  "proc_inst_id_" varchar(64) COLLATE "pg_catalog"."default",
  "action_" varchar(255) COLLATE "pg_catalog"."default",
  "message_" varchar(4000) COLLATE "pg_catalog"."default",
  "full_msg_" bytea
)
;

-- ----------------------------
-- Records of act_hi_comment
-- ----------------------------

-- ----------------------------
-- Table structure for act_hi_detail
-- ----------------------------
DROP TABLE IF EXISTS "public"."act_hi_detail";
CREATE TABLE "public"."act_hi_detail" (
  "id_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "type_" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "proc_inst_id_" varchar(64) COLLATE "pg_catalog"."default",
  "execution_id_" varchar(64) COLLATE "pg_catalog"."default",
  "task_id_" varchar(64) COLLATE "pg_catalog"."default",
  "act_inst_id_" varchar(64) COLLATE "pg_catalog"."default",
  "name_" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "var_type_" varchar(64) COLLATE "pg_catalog"."default",
  "rev_" int4,
  "time_" timestamp(6) NOT NULL,
  "bytearray_id_" varchar(64) COLLATE "pg_catalog"."default",
  "double_" float8,
  "long_" int8,
  "text_" varchar(4000) COLLATE "pg_catalog"."default",
  "text2_" varchar(4000) COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Records of act_hi_detail
-- ----------------------------

-- ----------------------------
-- Table structure for act_hi_entitylink
-- ----------------------------
DROP TABLE IF EXISTS "public"."act_hi_entitylink";
CREATE TABLE "public"."act_hi_entitylink" (
  "id_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "link_type_" varchar(255) COLLATE "pg_catalog"."default",
  "create_time_" timestamp(6),
  "scope_id_" varchar(255) COLLATE "pg_catalog"."default",
  "sub_scope_id_" varchar(255) COLLATE "pg_catalog"."default",
  "scope_type_" varchar(255) COLLATE "pg_catalog"."default",
  "scope_definition_id_" varchar(255) COLLATE "pg_catalog"."default",
  "parent_element_id_" varchar(255) COLLATE "pg_catalog"."default",
  "ref_scope_id_" varchar(255) COLLATE "pg_catalog"."default",
  "ref_scope_type_" varchar(255) COLLATE "pg_catalog"."default",
  "ref_scope_definition_id_" varchar(255) COLLATE "pg_catalog"."default",
  "root_scope_id_" varchar(255) COLLATE "pg_catalog"."default",
  "root_scope_type_" varchar(255) COLLATE "pg_catalog"."default",
  "hierarchy_type_" varchar(255) COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Records of act_hi_entitylink
-- ----------------------------

-- ----------------------------
-- Table structure for act_hi_identitylink
-- ----------------------------
DROP TABLE IF EXISTS "public"."act_hi_identitylink";
CREATE TABLE "public"."act_hi_identitylink" (
  "id_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "group_id_" varchar(255) COLLATE "pg_catalog"."default",
  "type_" varchar(255) COLLATE "pg_catalog"."default",
  "user_id_" varchar(255) COLLATE "pg_catalog"."default",
  "task_id_" varchar(64) COLLATE "pg_catalog"."default",
  "create_time_" timestamp(6),
  "proc_inst_id_" varchar(64) COLLATE "pg_catalog"."default",
  "scope_id_" varchar(255) COLLATE "pg_catalog"."default",
  "sub_scope_id_" varchar(255) COLLATE "pg_catalog"."default",
  "scope_type_" varchar(255) COLLATE "pg_catalog"."default",
  "scope_definition_id_" varchar(255) COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Records of act_hi_identitylink
-- ----------------------------

-- ----------------------------
-- Table structure for act_hi_procinst
-- ----------------------------
DROP TABLE IF EXISTS "public"."act_hi_procinst";
CREATE TABLE "public"."act_hi_procinst" (
  "id_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "rev_" int4 DEFAULT 1,
  "proc_inst_id_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "business_key_" varchar(255) COLLATE "pg_catalog"."default",
  "proc_def_id_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "start_time_" timestamp(6) NOT NULL,
  "end_time_" timestamp(6),
  "duration_" int8,
  "start_user_id_" varchar(255) COLLATE "pg_catalog"."default",
  "start_act_id_" varchar(255) COLLATE "pg_catalog"."default",
  "end_act_id_" varchar(255) COLLATE "pg_catalog"."default",
  "super_process_instance_id_" varchar(64) COLLATE "pg_catalog"."default",
  "delete_reason_" varchar(4000) COLLATE "pg_catalog"."default",
  "tenant_id_" varchar(255) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "name_" varchar(255) COLLATE "pg_catalog"."default",
  "callback_id_" varchar(255) COLLATE "pg_catalog"."default",
  "callback_type_" varchar(255) COLLATE "pg_catalog"."default",
  "reference_id_" varchar(255) COLLATE "pg_catalog"."default",
  "reference_type_" varchar(255) COLLATE "pg_catalog"."default",
  "propagated_stage_inst_id_" varchar(255) COLLATE "pg_catalog"."default",
  "business_status_" varchar(255) COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Records of act_hi_procinst
-- ----------------------------

-- ----------------------------
-- Table structure for act_hi_taskinst
-- ----------------------------
DROP TABLE IF EXISTS "public"."act_hi_taskinst";
CREATE TABLE "public"."act_hi_taskinst" (
  "id_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "rev_" int4 DEFAULT 1,
  "proc_def_id_" varchar(64) COLLATE "pg_catalog"."default",
  "task_def_id_" varchar(64) COLLATE "pg_catalog"."default",
  "task_def_key_" varchar(255) COLLATE "pg_catalog"."default",
  "proc_inst_id_" varchar(64) COLLATE "pg_catalog"."default",
  "execution_id_" varchar(64) COLLATE "pg_catalog"."default",
  "scope_id_" varchar(255) COLLATE "pg_catalog"."default",
  "sub_scope_id_" varchar(255) COLLATE "pg_catalog"."default",
  "scope_type_" varchar(255) COLLATE "pg_catalog"."default",
  "scope_definition_id_" varchar(255) COLLATE "pg_catalog"."default",
  "propagated_stage_inst_id_" varchar(255) COLLATE "pg_catalog"."default",
  "name_" varchar(255) COLLATE "pg_catalog"."default",
  "parent_task_id_" varchar(64) COLLATE "pg_catalog"."default",
  "description_" varchar(4000) COLLATE "pg_catalog"."default",
  "owner_" varchar(255) COLLATE "pg_catalog"."default",
  "assignee_" varchar(255) COLLATE "pg_catalog"."default",
  "start_time_" timestamp(6) NOT NULL,
  "claim_time_" timestamp(6),
  "end_time_" timestamp(6),
  "duration_" int8,
  "delete_reason_" varchar(4000) COLLATE "pg_catalog"."default",
  "priority_" int4,
  "due_date_" timestamp(6),
  "form_key_" varchar(255) COLLATE "pg_catalog"."default",
  "category_" varchar(255) COLLATE "pg_catalog"."default",
  "tenant_id_" varchar(255) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "last_updated_time_" timestamp(6)
)
;

-- ----------------------------
-- Records of act_hi_taskinst
-- ----------------------------

-- ----------------------------
-- Table structure for act_hi_tsk_log
-- ----------------------------
DROP TABLE IF EXISTS "public"."act_hi_tsk_log";
CREATE TABLE "public"."act_hi_tsk_log" (
  "id_" int4 NOT NULL DEFAULT nextval('act_hi_tsk_log_id__seq'::regclass),
  "type_" varchar(64) COLLATE "pg_catalog"."default",
  "task_id_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "time_stamp_" timestamp(6) NOT NULL,
  "user_id_" varchar(255) COLLATE "pg_catalog"."default",
  "data_" varchar(4000) COLLATE "pg_catalog"."default",
  "execution_id_" varchar(64) COLLATE "pg_catalog"."default",
  "proc_inst_id_" varchar(64) COLLATE "pg_catalog"."default",
  "proc_def_id_" varchar(64) COLLATE "pg_catalog"."default",
  "scope_id_" varchar(255) COLLATE "pg_catalog"."default",
  "scope_definition_id_" varchar(255) COLLATE "pg_catalog"."default",
  "sub_scope_id_" varchar(255) COLLATE "pg_catalog"."default",
  "scope_type_" varchar(255) COLLATE "pg_catalog"."default",
  "tenant_id_" varchar(255) COLLATE "pg_catalog"."default" DEFAULT ''::character varying
)
;

-- ----------------------------
-- Records of act_hi_tsk_log
-- ----------------------------

-- ----------------------------
-- Table structure for act_hi_varinst
-- ----------------------------
DROP TABLE IF EXISTS "public"."act_hi_varinst";
CREATE TABLE "public"."act_hi_varinst" (
  "id_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "rev_" int4 DEFAULT 1,
  "proc_inst_id_" varchar(64) COLLATE "pg_catalog"."default",
  "execution_id_" varchar(64) COLLATE "pg_catalog"."default",
  "task_id_" varchar(64) COLLATE "pg_catalog"."default",
  "name_" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "var_type_" varchar(100) COLLATE "pg_catalog"."default",
  "scope_id_" varchar(255) COLLATE "pg_catalog"."default",
  "sub_scope_id_" varchar(255) COLLATE "pg_catalog"."default",
  "scope_type_" varchar(255) COLLATE "pg_catalog"."default",
  "bytearray_id_" varchar(64) COLLATE "pg_catalog"."default",
  "double_" float8,
  "long_" int8,
  "text_" varchar(4000) COLLATE "pg_catalog"."default",
  "text2_" varchar(4000) COLLATE "pg_catalog"."default",
  "create_time_" timestamp(6),
  "last_updated_time_" timestamp(6)
)
;

-- ----------------------------
-- Records of act_hi_varinst
-- ----------------------------

-- ----------------------------
-- Table structure for act_procdef_info
-- ----------------------------
DROP TABLE IF EXISTS "public"."act_procdef_info";
CREATE TABLE "public"."act_procdef_info" (
  "id_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "proc_def_id_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "rev_" int4,
  "info_json_id_" varchar(64) COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Records of act_procdef_info
-- ----------------------------

-- ----------------------------
-- Table structure for act_re_deployment
-- ----------------------------
DROP TABLE IF EXISTS "public"."act_re_deployment";
CREATE TABLE "public"."act_re_deployment" (
  "id_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "name_" varchar(255) COLLATE "pg_catalog"."default",
  "category_" varchar(255) COLLATE "pg_catalog"."default",
  "key_" varchar(255) COLLATE "pg_catalog"."default",
  "tenant_id_" varchar(255) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "deploy_time_" timestamp(6),
  "derived_from_" varchar(64) COLLATE "pg_catalog"."default",
  "derived_from_root_" varchar(64) COLLATE "pg_catalog"."default",
  "parent_deployment_id_" varchar(255) COLLATE "pg_catalog"."default",
  "engine_version_" varchar(255) COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Records of act_re_deployment
-- ----------------------------

-- ----------------------------
-- Table structure for act_re_model
-- ----------------------------
DROP TABLE IF EXISTS "public"."act_re_model";
CREATE TABLE "public"."act_re_model" (
  "id_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "rev_" int4,
  "name_" varchar(255) COLLATE "pg_catalog"."default",
  "key_" varchar(255) COLLATE "pg_catalog"."default",
  "category_" varchar(255) COLLATE "pg_catalog"."default",
  "create_time_" timestamp(6),
  "last_update_time_" timestamp(6),
  "version_" int4,
  "meta_info_" varchar(4000) COLLATE "pg_catalog"."default",
  "deployment_id_" varchar(64) COLLATE "pg_catalog"."default",
  "editor_source_value_id_" varchar(64) COLLATE "pg_catalog"."default",
  "editor_source_extra_value_id_" varchar(64) COLLATE "pg_catalog"."default",
  "tenant_id_" varchar(255) COLLATE "pg_catalog"."default" DEFAULT ''::character varying
)
;

-- ----------------------------
-- Records of act_re_model
-- ----------------------------

-- ----------------------------
-- Table structure for act_re_procdef
-- ----------------------------
DROP TABLE IF EXISTS "public"."act_re_procdef";
CREATE TABLE "public"."act_re_procdef" (
  "id_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "rev_" int4,
  "category_" varchar(255) COLLATE "pg_catalog"."default",
  "name_" varchar(255) COLLATE "pg_catalog"."default",
  "key_" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "version_" int4 NOT NULL,
  "deployment_id_" varchar(64) COLLATE "pg_catalog"."default",
  "resource_name_" varchar(4000) COLLATE "pg_catalog"."default",
  "dgrm_resource_name_" varchar(4000) COLLATE "pg_catalog"."default",
  "description_" varchar(4000) COLLATE "pg_catalog"."default",
  "has_start_form_key_" bool,
  "has_graphical_notation_" bool,
  "suspension_state_" int4,
  "tenant_id_" varchar(255) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "derived_from_" varchar(64) COLLATE "pg_catalog"."default",
  "derived_from_root_" varchar(64) COLLATE "pg_catalog"."default",
  "derived_version_" int4 NOT NULL DEFAULT 0,
  "engine_version_" varchar(255) COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Records of act_re_procdef
-- ----------------------------

-- ----------------------------
-- Table structure for act_ru_actinst
-- ----------------------------
DROP TABLE IF EXISTS "public"."act_ru_actinst";
CREATE TABLE "public"."act_ru_actinst" (
  "id_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "rev_" int4 DEFAULT 1,
  "proc_def_id_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "proc_inst_id_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "execution_id_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "act_id_" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "task_id_" varchar(64) COLLATE "pg_catalog"."default",
  "call_proc_inst_id_" varchar(64) COLLATE "pg_catalog"."default",
  "act_name_" varchar(255) COLLATE "pg_catalog"."default",
  "act_type_" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "assignee_" varchar(255) COLLATE "pg_catalog"."default",
  "start_time_" timestamp(6) NOT NULL,
  "end_time_" timestamp(6),
  "duration_" int8,
  "transaction_order_" int4,
  "delete_reason_" varchar(4000) COLLATE "pg_catalog"."default",
  "tenant_id_" varchar(255) COLLATE "pg_catalog"."default" DEFAULT ''::character varying
)
;

-- ----------------------------
-- Records of act_ru_actinst
-- ----------------------------

-- ----------------------------
-- Table structure for act_ru_deadletter_job
-- ----------------------------
DROP TABLE IF EXISTS "public"."act_ru_deadletter_job";
CREATE TABLE "public"."act_ru_deadletter_job" (
  "id_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "rev_" int4,
  "category_" varchar(255) COLLATE "pg_catalog"."default",
  "type_" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "exclusive_" bool,
  "execution_id_" varchar(64) COLLATE "pg_catalog"."default",
  "process_instance_id_" varchar(64) COLLATE "pg_catalog"."default",
  "proc_def_id_" varchar(64) COLLATE "pg_catalog"."default",
  "element_id_" varchar(255) COLLATE "pg_catalog"."default",
  "element_name_" varchar(255) COLLATE "pg_catalog"."default",
  "scope_id_" varchar(255) COLLATE "pg_catalog"."default",
  "sub_scope_id_" varchar(255) COLLATE "pg_catalog"."default",
  "scope_type_" varchar(255) COLLATE "pg_catalog"."default",
  "scope_definition_id_" varchar(255) COLLATE "pg_catalog"."default",
  "correlation_id_" varchar(255) COLLATE "pg_catalog"."default",
  "exception_stack_id_" varchar(64) COLLATE "pg_catalog"."default",
  "exception_msg_" varchar(4000) COLLATE "pg_catalog"."default",
  "duedate_" timestamp(6),
  "repeat_" varchar(255) COLLATE "pg_catalog"."default",
  "handler_type_" varchar(255) COLLATE "pg_catalog"."default",
  "handler_cfg_" varchar(4000) COLLATE "pg_catalog"."default",
  "custom_values_id_" varchar(64) COLLATE "pg_catalog"."default",
  "create_time_" timestamp(6),
  "tenant_id_" varchar(255) COLLATE "pg_catalog"."default" DEFAULT ''::character varying
)
;

-- ----------------------------
-- Records of act_ru_deadletter_job
-- ----------------------------

-- ----------------------------
-- Table structure for act_ru_entitylink
-- ----------------------------
DROP TABLE IF EXISTS "public"."act_ru_entitylink";
CREATE TABLE "public"."act_ru_entitylink" (
  "id_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "rev_" int4,
  "create_time_" timestamp(6),
  "link_type_" varchar(255) COLLATE "pg_catalog"."default",
  "scope_id_" varchar(255) COLLATE "pg_catalog"."default",
  "sub_scope_id_" varchar(255) COLLATE "pg_catalog"."default",
  "scope_type_" varchar(255) COLLATE "pg_catalog"."default",
  "scope_definition_id_" varchar(255) COLLATE "pg_catalog"."default",
  "parent_element_id_" varchar(255) COLLATE "pg_catalog"."default",
  "ref_scope_id_" varchar(255) COLLATE "pg_catalog"."default",
  "ref_scope_type_" varchar(255) COLLATE "pg_catalog"."default",
  "ref_scope_definition_id_" varchar(255) COLLATE "pg_catalog"."default",
  "root_scope_id_" varchar(255) COLLATE "pg_catalog"."default",
  "root_scope_type_" varchar(255) COLLATE "pg_catalog"."default",
  "hierarchy_type_" varchar(255) COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Records of act_ru_entitylink
-- ----------------------------

-- ----------------------------
-- Table structure for act_ru_event_subscr
-- ----------------------------
DROP TABLE IF EXISTS "public"."act_ru_event_subscr";
CREATE TABLE "public"."act_ru_event_subscr" (
  "id_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "rev_" int4,
  "event_type_" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "event_name_" varchar(255) COLLATE "pg_catalog"."default",
  "execution_id_" varchar(64) COLLATE "pg_catalog"."default",
  "proc_inst_id_" varchar(64) COLLATE "pg_catalog"."default",
  "activity_id_" varchar(64) COLLATE "pg_catalog"."default",
  "configuration_" varchar(255) COLLATE "pg_catalog"."default",
  "created_" timestamp(6) NOT NULL,
  "proc_def_id_" varchar(64) COLLATE "pg_catalog"."default",
  "sub_scope_id_" varchar(64) COLLATE "pg_catalog"."default",
  "scope_id_" varchar(64) COLLATE "pg_catalog"."default",
  "scope_definition_id_" varchar(64) COLLATE "pg_catalog"."default",
  "scope_type_" varchar(64) COLLATE "pg_catalog"."default",
  "lock_time_" timestamp(6),
  "lock_owner_" varchar(255) COLLATE "pg_catalog"."default",
  "tenant_id_" varchar(255) COLLATE "pg_catalog"."default" DEFAULT ''::character varying
)
;

-- ----------------------------
-- Records of act_ru_event_subscr
-- ----------------------------

-- ----------------------------
-- Table structure for act_ru_execution
-- ----------------------------
DROP TABLE IF EXISTS "public"."act_ru_execution";
CREATE TABLE "public"."act_ru_execution" (
  "id_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "rev_" int4,
  "proc_inst_id_" varchar(64) COLLATE "pg_catalog"."default",
  "business_key_" varchar(255) COLLATE "pg_catalog"."default",
  "parent_id_" varchar(64) COLLATE "pg_catalog"."default",
  "proc_def_id_" varchar(64) COLLATE "pg_catalog"."default",
  "super_exec_" varchar(64) COLLATE "pg_catalog"."default",
  "root_proc_inst_id_" varchar(64) COLLATE "pg_catalog"."default",
  "act_id_" varchar(255) COLLATE "pg_catalog"."default",
  "is_active_" bool,
  "is_concurrent_" bool,
  "is_scope_" bool,
  "is_event_scope_" bool,
  "is_mi_root_" bool,
  "suspension_state_" int4,
  "cached_ent_state_" int4,
  "tenant_id_" varchar(255) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "name_" varchar(255) COLLATE "pg_catalog"."default",
  "start_act_id_" varchar(255) COLLATE "pg_catalog"."default",
  "start_time_" timestamp(6),
  "start_user_id_" varchar(255) COLLATE "pg_catalog"."default",
  "lock_time_" timestamp(6),
  "lock_owner_" varchar(255) COLLATE "pg_catalog"."default",
  "is_count_enabled_" bool,
  "evt_subscr_count_" int4,
  "task_count_" int4,
  "job_count_" int4,
  "timer_job_count_" int4,
  "susp_job_count_" int4,
  "deadletter_job_count_" int4,
  "external_worker_job_count_" int4,
  "var_count_" int4,
  "id_link_count_" int4,
  "callback_id_" varchar(255) COLLATE "pg_catalog"."default",
  "callback_type_" varchar(255) COLLATE "pg_catalog"."default",
  "reference_id_" varchar(255) COLLATE "pg_catalog"."default",
  "reference_type_" varchar(255) COLLATE "pg_catalog"."default",
  "propagated_stage_inst_id_" varchar(255) COLLATE "pg_catalog"."default",
  "business_status_" varchar(255) COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Records of act_ru_execution
-- ----------------------------

-- ----------------------------
-- Table structure for act_ru_external_job
-- ----------------------------
DROP TABLE IF EXISTS "public"."act_ru_external_job";
CREATE TABLE "public"."act_ru_external_job" (
  "id_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "rev_" int4,
  "category_" varchar(255) COLLATE "pg_catalog"."default",
  "type_" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "lock_exp_time_" timestamp(6),
  "lock_owner_" varchar(255) COLLATE "pg_catalog"."default",
  "exclusive_" bool,
  "execution_id_" varchar(64) COLLATE "pg_catalog"."default",
  "process_instance_id_" varchar(64) COLLATE "pg_catalog"."default",
  "proc_def_id_" varchar(64) COLLATE "pg_catalog"."default",
  "element_id_" varchar(255) COLLATE "pg_catalog"."default",
  "element_name_" varchar(255) COLLATE "pg_catalog"."default",
  "scope_id_" varchar(255) COLLATE "pg_catalog"."default",
  "sub_scope_id_" varchar(255) COLLATE "pg_catalog"."default",
  "scope_type_" varchar(255) COLLATE "pg_catalog"."default",
  "scope_definition_id_" varchar(255) COLLATE "pg_catalog"."default",
  "correlation_id_" varchar(255) COLLATE "pg_catalog"."default",
  "retries_" int4,
  "exception_stack_id_" varchar(64) COLLATE "pg_catalog"."default",
  "exception_msg_" varchar(4000) COLLATE "pg_catalog"."default",
  "duedate_" timestamp(6),
  "repeat_" varchar(255) COLLATE "pg_catalog"."default",
  "handler_type_" varchar(255) COLLATE "pg_catalog"."default",
  "handler_cfg_" varchar(4000) COLLATE "pg_catalog"."default",
  "custom_values_id_" varchar(64) COLLATE "pg_catalog"."default",
  "create_time_" timestamp(6),
  "tenant_id_" varchar(255) COLLATE "pg_catalog"."default" DEFAULT ''::character varying
)
;

-- ----------------------------
-- Records of act_ru_external_job
-- ----------------------------

-- ----------------------------
-- Table structure for act_ru_history_job
-- ----------------------------
DROP TABLE IF EXISTS "public"."act_ru_history_job";
CREATE TABLE "public"."act_ru_history_job" (
  "id_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "rev_" int4,
  "lock_exp_time_" timestamp(6),
  "lock_owner_" varchar(255) COLLATE "pg_catalog"."default",
  "retries_" int4,
  "exception_stack_id_" varchar(64) COLLATE "pg_catalog"."default",
  "exception_msg_" varchar(4000) COLLATE "pg_catalog"."default",
  "handler_type_" varchar(255) COLLATE "pg_catalog"."default",
  "handler_cfg_" varchar(4000) COLLATE "pg_catalog"."default",
  "custom_values_id_" varchar(64) COLLATE "pg_catalog"."default",
  "adv_handler_cfg_id_" varchar(64) COLLATE "pg_catalog"."default",
  "create_time_" timestamp(6),
  "scope_type_" varchar(255) COLLATE "pg_catalog"."default",
  "tenant_id_" varchar(255) COLLATE "pg_catalog"."default" DEFAULT ''::character varying
)
;

-- ----------------------------
-- Records of act_ru_history_job
-- ----------------------------

-- ----------------------------
-- Table structure for act_ru_identitylink
-- ----------------------------
DROP TABLE IF EXISTS "public"."act_ru_identitylink";
CREATE TABLE "public"."act_ru_identitylink" (
  "id_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "rev_" int4,
  "group_id_" varchar(255) COLLATE "pg_catalog"."default",
  "type_" varchar(255) COLLATE "pg_catalog"."default",
  "user_id_" varchar(255) COLLATE "pg_catalog"."default",
  "task_id_" varchar(64) COLLATE "pg_catalog"."default",
  "proc_inst_id_" varchar(64) COLLATE "pg_catalog"."default",
  "proc_def_id_" varchar(64) COLLATE "pg_catalog"."default",
  "scope_id_" varchar(255) COLLATE "pg_catalog"."default",
  "sub_scope_id_" varchar(255) COLLATE "pg_catalog"."default",
  "scope_type_" varchar(255) COLLATE "pg_catalog"."default",
  "scope_definition_id_" varchar(255) COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Records of act_ru_identitylink
-- ----------------------------

-- ----------------------------
-- Table structure for act_ru_job
-- ----------------------------
DROP TABLE IF EXISTS "public"."act_ru_job";
CREATE TABLE "public"."act_ru_job" (
  "id_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "rev_" int4,
  "category_" varchar(255) COLLATE "pg_catalog"."default",
  "type_" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "lock_exp_time_" timestamp(6),
  "lock_owner_" varchar(255) COLLATE "pg_catalog"."default",
  "exclusive_" bool,
  "execution_id_" varchar(64) COLLATE "pg_catalog"."default",
  "process_instance_id_" varchar(64) COLLATE "pg_catalog"."default",
  "proc_def_id_" varchar(64) COLLATE "pg_catalog"."default",
  "element_id_" varchar(255) COLLATE "pg_catalog"."default",
  "element_name_" varchar(255) COLLATE "pg_catalog"."default",
  "scope_id_" varchar(255) COLLATE "pg_catalog"."default",
  "sub_scope_id_" varchar(255) COLLATE "pg_catalog"."default",
  "scope_type_" varchar(255) COLLATE "pg_catalog"."default",
  "scope_definition_id_" varchar(255) COLLATE "pg_catalog"."default",
  "correlation_id_" varchar(255) COLLATE "pg_catalog"."default",
  "retries_" int4,
  "exception_stack_id_" varchar(64) COLLATE "pg_catalog"."default",
  "exception_msg_" varchar(4000) COLLATE "pg_catalog"."default",
  "duedate_" timestamp(6),
  "repeat_" varchar(255) COLLATE "pg_catalog"."default",
  "handler_type_" varchar(255) COLLATE "pg_catalog"."default",
  "handler_cfg_" varchar(4000) COLLATE "pg_catalog"."default",
  "custom_values_id_" varchar(64) COLLATE "pg_catalog"."default",
  "create_time_" timestamp(6),
  "tenant_id_" varchar(255) COLLATE "pg_catalog"."default" DEFAULT ''::character varying
)
;

-- ----------------------------
-- Records of act_ru_job
-- ----------------------------

-- ----------------------------
-- Table structure for act_ru_suspended_job
-- ----------------------------
DROP TABLE IF EXISTS "public"."act_ru_suspended_job";
CREATE TABLE "public"."act_ru_suspended_job" (
  "id_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "rev_" int4,
  "category_" varchar(255) COLLATE "pg_catalog"."default",
  "type_" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "exclusive_" bool,
  "execution_id_" varchar(64) COLLATE "pg_catalog"."default",
  "process_instance_id_" varchar(64) COLLATE "pg_catalog"."default",
  "proc_def_id_" varchar(64) COLLATE "pg_catalog"."default",
  "element_id_" varchar(255) COLLATE "pg_catalog"."default",
  "element_name_" varchar(255) COLLATE "pg_catalog"."default",
  "scope_id_" varchar(255) COLLATE "pg_catalog"."default",
  "sub_scope_id_" varchar(255) COLLATE "pg_catalog"."default",
  "scope_type_" varchar(255) COLLATE "pg_catalog"."default",
  "scope_definition_id_" varchar(255) COLLATE "pg_catalog"."default",
  "correlation_id_" varchar(255) COLLATE "pg_catalog"."default",
  "retries_" int4,
  "exception_stack_id_" varchar(64) COLLATE "pg_catalog"."default",
  "exception_msg_" varchar(4000) COLLATE "pg_catalog"."default",
  "duedate_" timestamp(6),
  "repeat_" varchar(255) COLLATE "pg_catalog"."default",
  "handler_type_" varchar(255) COLLATE "pg_catalog"."default",
  "handler_cfg_" varchar(4000) COLLATE "pg_catalog"."default",
  "custom_values_id_" varchar(64) COLLATE "pg_catalog"."default",
  "create_time_" timestamp(6),
  "tenant_id_" varchar(255) COLLATE "pg_catalog"."default" DEFAULT ''::character varying
)
;

-- ----------------------------
-- Records of act_ru_suspended_job
-- ----------------------------

-- ----------------------------
-- Table structure for act_ru_task
-- ----------------------------
DROP TABLE IF EXISTS "public"."act_ru_task";
CREATE TABLE "public"."act_ru_task" (
  "id_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "rev_" int4,
  "execution_id_" varchar(64) COLLATE "pg_catalog"."default",
  "proc_inst_id_" varchar(64) COLLATE "pg_catalog"."default",
  "proc_def_id_" varchar(64) COLLATE "pg_catalog"."default",
  "task_def_id_" varchar(64) COLLATE "pg_catalog"."default",
  "scope_id_" varchar(255) COLLATE "pg_catalog"."default",
  "sub_scope_id_" varchar(255) COLLATE "pg_catalog"."default",
  "scope_type_" varchar(255) COLLATE "pg_catalog"."default",
  "scope_definition_id_" varchar(255) COLLATE "pg_catalog"."default",
  "propagated_stage_inst_id_" varchar(255) COLLATE "pg_catalog"."default",
  "name_" varchar(255) COLLATE "pg_catalog"."default",
  "parent_task_id_" varchar(64) COLLATE "pg_catalog"."default",
  "description_" varchar(4000) COLLATE "pg_catalog"."default",
  "task_def_key_" varchar(255) COLLATE "pg_catalog"."default",
  "owner_" varchar(255) COLLATE "pg_catalog"."default",
  "assignee_" varchar(255) COLLATE "pg_catalog"."default",
  "delegation_" varchar(64) COLLATE "pg_catalog"."default",
  "priority_" int4,
  "create_time_" timestamp(6),
  "due_date_" timestamp(6),
  "category_" varchar(255) COLLATE "pg_catalog"."default",
  "suspension_state_" int4,
  "tenant_id_" varchar(255) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "form_key_" varchar(255) COLLATE "pg_catalog"."default",
  "claim_time_" timestamp(6),
  "is_count_enabled_" bool,
  "var_count_" int4,
  "id_link_count_" int4,
  "sub_task_count_" int4
)
;

-- ----------------------------
-- Records of act_ru_task
-- ----------------------------

-- ----------------------------
-- Table structure for act_ru_timer_job
-- ----------------------------
DROP TABLE IF EXISTS "public"."act_ru_timer_job";
CREATE TABLE "public"."act_ru_timer_job" (
  "id_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "rev_" int4,
  "category_" varchar(255) COLLATE "pg_catalog"."default",
  "type_" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "lock_exp_time_" timestamp(6),
  "lock_owner_" varchar(255) COLLATE "pg_catalog"."default",
  "exclusive_" bool,
  "execution_id_" varchar(64) COLLATE "pg_catalog"."default",
  "process_instance_id_" varchar(64) COLLATE "pg_catalog"."default",
  "proc_def_id_" varchar(64) COLLATE "pg_catalog"."default",
  "element_id_" varchar(255) COLLATE "pg_catalog"."default",
  "element_name_" varchar(255) COLLATE "pg_catalog"."default",
  "scope_id_" varchar(255) COLLATE "pg_catalog"."default",
  "sub_scope_id_" varchar(255) COLLATE "pg_catalog"."default",
  "scope_type_" varchar(255) COLLATE "pg_catalog"."default",
  "scope_definition_id_" varchar(255) COLLATE "pg_catalog"."default",
  "correlation_id_" varchar(255) COLLATE "pg_catalog"."default",
  "retries_" int4,
  "exception_stack_id_" varchar(64) COLLATE "pg_catalog"."default",
  "exception_msg_" varchar(4000) COLLATE "pg_catalog"."default",
  "duedate_" timestamp(6),
  "repeat_" varchar(255) COLLATE "pg_catalog"."default",
  "handler_type_" varchar(255) COLLATE "pg_catalog"."default",
  "handler_cfg_" varchar(4000) COLLATE "pg_catalog"."default",
  "custom_values_id_" varchar(64) COLLATE "pg_catalog"."default",
  "create_time_" timestamp(6),
  "tenant_id_" varchar(255) COLLATE "pg_catalog"."default" DEFAULT ''::character varying
)
;

-- ----------------------------
-- Records of act_ru_timer_job
-- ----------------------------

-- ----------------------------
-- Table structure for act_ru_variable
-- ----------------------------
DROP TABLE IF EXISTS "public"."act_ru_variable";
CREATE TABLE "public"."act_ru_variable" (
  "id_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "rev_" int4,
  "type_" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "name_" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "execution_id_" varchar(64) COLLATE "pg_catalog"."default",
  "proc_inst_id_" varchar(64) COLLATE "pg_catalog"."default",
  "task_id_" varchar(64) COLLATE "pg_catalog"."default",
  "scope_id_" varchar(255) COLLATE "pg_catalog"."default",
  "sub_scope_id_" varchar(255) COLLATE "pg_catalog"."default",
  "scope_type_" varchar(255) COLLATE "pg_catalog"."default",
  "bytearray_id_" varchar(64) COLLATE "pg_catalog"."default",
  "double_" float8,
  "long_" int8,
  "text_" varchar(4000) COLLATE "pg_catalog"."default",
  "text2_" varchar(4000) COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Records of act_ru_variable
-- ----------------------------

-- ----------------------------
-- Table structure for flw_channel_definition
-- ----------------------------
DROP TABLE IF EXISTS "public"."flw_channel_definition";
CREATE TABLE "public"."flw_channel_definition" (
  "id_" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "name_" varchar(255) COLLATE "pg_catalog"."default",
  "version_" int4,
  "key_" varchar(255) COLLATE "pg_catalog"."default",
  "category_" varchar(255) COLLATE "pg_catalog"."default",
  "deployment_id_" varchar(255) COLLATE "pg_catalog"."default",
  "create_time_" timestamp(3),
  "tenant_id_" varchar(255) COLLATE "pg_catalog"."default",
  "resource_name_" varchar(255) COLLATE "pg_catalog"."default",
  "description_" varchar(255) COLLATE "pg_catalog"."default",
  "type_" varchar(255) COLLATE "pg_catalog"."default",
  "implementation_" varchar(255) COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Records of flw_channel_definition
-- ----------------------------

-- ----------------------------
-- Table structure for flw_ev_databasechangelog
-- ----------------------------
DROP TABLE IF EXISTS "public"."flw_ev_databasechangelog";
CREATE TABLE "public"."flw_ev_databasechangelog" (
  "id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "author" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "filename" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "dateexecuted" timestamp(6) NOT NULL,
  "orderexecuted" int4 NOT NULL,
  "exectype" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
  "md5sum" varchar(35) COLLATE "pg_catalog"."default",
  "description" varchar(255) COLLATE "pg_catalog"."default",
  "comments" varchar(255) COLLATE "pg_catalog"."default",
  "tag" varchar(255) COLLATE "pg_catalog"."default",
  "liquibase" varchar(20) COLLATE "pg_catalog"."default",
  "contexts" varchar(255) COLLATE "pg_catalog"."default",
  "labels" varchar(255) COLLATE "pg_catalog"."default",
  "deployment_id" varchar(10) COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Records of flw_ev_databasechangelog
-- ----------------------------
INSERT INTO "public"."flw_ev_databasechangelog" VALUES ('1', 'flowable', 'org/flowable/eventregistry/db/liquibase/flowable-eventregistry-db-changelog.xml', '2024-11-04 09:38:31.459397', 1, 'EXECUTED', '8:1b0c48c9cf7945be799d868a2626d687', 'createTable tableName=FLW_EVENT_DEPLOYMENT; createTable tableName=FLW_EVENT_RESOURCE; createTable tableName=FLW_EVENT_DEFINITION; createIndex indexName=ACT_IDX_EVENT_DEF_UNIQ, tableName=FLW_EVENT_DEFINITION; createTable tableName=FLW_CHANNEL_DEFIN...', '', NULL, '4.5.0', NULL, NULL, '0684695120');
INSERT INTO "public"."flw_ev_databasechangelog" VALUES ('2', 'flowable', 'org/flowable/eventregistry/db/liquibase/flowable-eventregistry-db-changelog.xml', '2024-11-04 09:38:31.733854', 2, 'EXECUTED', '8:0ea825feb8e470558f0b5754352b9cda', 'addColumn tableName=FLW_CHANNEL_DEFINITION; addColumn tableName=FLW_CHANNEL_DEFINITION', '', NULL, '4.5.0', NULL, NULL, '0684695120');
INSERT INTO "public"."flw_ev_databasechangelog" VALUES ('3', 'flowable', 'org/flowable/eventregistry/db/liquibase/flowable-eventregistry-db-changelog.xml', '2024-11-04 09:38:31.949184', 3, 'EXECUTED', '8:3c2bb293350b5cbe6504331980c9dcee', 'customChange', '', NULL, '4.5.0', NULL, NULL, '0684695120');

-- ----------------------------
-- Table structure for flw_ev_databasechangeloglock
-- ----------------------------
DROP TABLE IF EXISTS "public"."flw_ev_databasechangeloglock";
CREATE TABLE "public"."flw_ev_databasechangeloglock" (
  "id" int4 NOT NULL,
  "locked" bool NOT NULL,
  "lockgranted" timestamp(6),
  "lockedby" varchar(255) COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Records of flw_ev_databasechangeloglock
-- ----------------------------
INSERT INTO "public"."flw_ev_databasechangeloglock" VALUES (1, 'f', NULL, NULL);

-- ----------------------------
-- Table structure for flw_event_definition
-- ----------------------------
DROP TABLE IF EXISTS "public"."flw_event_definition";
CREATE TABLE "public"."flw_event_definition" (
  "id_" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "name_" varchar(255) COLLATE "pg_catalog"."default",
  "version_" int4,
  "key_" varchar(255) COLLATE "pg_catalog"."default",
  "category_" varchar(255) COLLATE "pg_catalog"."default",
  "deployment_id_" varchar(255) COLLATE "pg_catalog"."default",
  "tenant_id_" varchar(255) COLLATE "pg_catalog"."default",
  "resource_name_" varchar(255) COLLATE "pg_catalog"."default",
  "description_" varchar(255) COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Records of flw_event_definition
-- ----------------------------

-- ----------------------------
-- Table structure for flw_event_deployment
-- ----------------------------
DROP TABLE IF EXISTS "public"."flw_event_deployment";
CREATE TABLE "public"."flw_event_deployment" (
  "id_" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "name_" varchar(255) COLLATE "pg_catalog"."default",
  "category_" varchar(255) COLLATE "pg_catalog"."default",
  "deploy_time_" timestamp(3),
  "tenant_id_" varchar(255) COLLATE "pg_catalog"."default",
  "parent_deployment_id_" varchar(255) COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Records of flw_event_deployment
-- ----------------------------

-- ----------------------------
-- Table structure for flw_event_resource
-- ----------------------------
DROP TABLE IF EXISTS "public"."flw_event_resource";
CREATE TABLE "public"."flw_event_resource" (
  "id_" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "name_" varchar(255) COLLATE "pg_catalog"."default",
  "deployment_id_" varchar(255) COLLATE "pg_catalog"."default",
  "resource_bytes_" bytea
)
;

-- ----------------------------
-- Records of flw_event_resource
-- ----------------------------

-- ----------------------------
-- Table structure for flw_ru_batch
-- ----------------------------
DROP TABLE IF EXISTS "public"."flw_ru_batch";
CREATE TABLE "public"."flw_ru_batch" (
  "id_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "rev_" int4,
  "type_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "search_key_" varchar(255) COLLATE "pg_catalog"."default",
  "search_key2_" varchar(255) COLLATE "pg_catalog"."default",
  "create_time_" timestamp(6) NOT NULL,
  "complete_time_" timestamp(6),
  "status_" varchar(255) COLLATE "pg_catalog"."default",
  "batch_doc_id_" varchar(64) COLLATE "pg_catalog"."default",
  "tenant_id_" varchar(255) COLLATE "pg_catalog"."default" DEFAULT ''::character varying
)
;

-- ----------------------------
-- Records of flw_ru_batch
-- ----------------------------

-- ----------------------------
-- Table structure for flw_ru_batch_part
-- ----------------------------
DROP TABLE IF EXISTS "public"."flw_ru_batch_part";
CREATE TABLE "public"."flw_ru_batch_part" (
  "id_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "rev_" int4,
  "batch_id_" varchar(64) COLLATE "pg_catalog"."default",
  "type_" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "scope_id_" varchar(64) COLLATE "pg_catalog"."default",
  "sub_scope_id_" varchar(64) COLLATE "pg_catalog"."default",
  "scope_type_" varchar(64) COLLATE "pg_catalog"."default",
  "search_key_" varchar(255) COLLATE "pg_catalog"."default",
  "search_key2_" varchar(255) COLLATE "pg_catalog"."default",
  "create_time_" timestamp(6) NOT NULL,
  "complete_time_" timestamp(6),
  "status_" varchar(255) COLLATE "pg_catalog"."default",
  "result_doc_id_" varchar(64) COLLATE "pg_catalog"."default",
  "tenant_id_" varchar(255) COLLATE "pg_catalog"."default" DEFAULT ''::character varying
)
;

-- ----------------------------
-- Records of flw_ru_batch_part
-- ----------------------------

-- ----------------------------
-- Table structure for gen_table
-- ----------------------------
DROP TABLE IF EXISTS "public"."gen_table";
CREATE TABLE "public"."gen_table" (
  "table_id" int8 NOT NULL,
  "table_name" varchar(200) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "table_comment" varchar(500) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "sub_table_name" varchar(64) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "sub_table_fk_name" varchar(64) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "class_name" varchar(100) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "tpl_category" varchar(200) COLLATE "pg_catalog"."default" DEFAULT 'crud'::character varying,
  "package_name" varchar(100) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "module_name" varchar(30) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "business_name" varchar(30) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "function_name" varchar(50) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "function_author" varchar(50) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "gen_type" char(1) COLLATE "pg_catalog"."default" NOT NULL DEFAULT '0'::bpchar,
  "gen_path" varchar(200) COLLATE "pg_catalog"."default" DEFAULT '/'::character varying,
  "options" varchar(1000) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "create_by" varchar(64) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "create_time" timestamp(6),
  "update_by" varchar(64) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "update_time" timestamp(6),
  "remark" varchar(500) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying
)
;
COMMENT ON COLUMN "public"."gen_table"."table_id" IS '编号';
COMMENT ON COLUMN "public"."gen_table"."table_name" IS '表名称';
COMMENT ON COLUMN "public"."gen_table"."table_comment" IS '表描述';
COMMENT ON COLUMN "public"."gen_table"."sub_table_name" IS '关联子表的表名';
COMMENT ON COLUMN "public"."gen_table"."sub_table_fk_name" IS '子表关联的外键名';
COMMENT ON COLUMN "public"."gen_table"."class_name" IS '实体类名称';
COMMENT ON COLUMN "public"."gen_table"."tpl_category" IS '使用的模板（CRUD单表操作 TREE树表操作）';
COMMENT ON COLUMN "public"."gen_table"."package_name" IS '生成包路径';
COMMENT ON COLUMN "public"."gen_table"."module_name" IS '生成模块名';
COMMENT ON COLUMN "public"."gen_table"."business_name" IS '生成业务名';
COMMENT ON COLUMN "public"."gen_table"."function_name" IS '生成功能名';
COMMENT ON COLUMN "public"."gen_table"."function_author" IS '生成功能作者';
COMMENT ON COLUMN "public"."gen_table"."gen_type" IS '生成代码方式（0zip压缩包 1自定义路径）';
COMMENT ON COLUMN "public"."gen_table"."gen_path" IS '生成路径（不填默认项目路径）';
COMMENT ON COLUMN "public"."gen_table"."options" IS '其它生成选项';
COMMENT ON COLUMN "public"."gen_table"."create_by" IS '创建者';
COMMENT ON COLUMN "public"."gen_table"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."gen_table"."update_by" IS '更新者';
COMMENT ON COLUMN "public"."gen_table"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."gen_table"."remark" IS '备注';
COMMENT ON TABLE "public"."gen_table" IS '代码生成业务表';

-- ----------------------------
-- Records of gen_table
-- ----------------------------

-- ----------------------------
-- Table structure for gen_table_column
-- ----------------------------
DROP TABLE IF EXISTS "public"."gen_table_column";
CREATE TABLE "public"."gen_table_column" (
  "column_id" int8 NOT NULL,
  "table_id" int8,
  "column_name" varchar(200) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "column_comment" varchar(500) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "column_type" varchar(100) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "java_type" varchar(500) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "java_field" varchar(200) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "is_pk" char(1) COLLATE "pg_catalog"."default" DEFAULT NULL::bpchar,
  "is_increment" char(1) COLLATE "pg_catalog"."default" DEFAULT NULL::bpchar,
  "is_required" char(1) COLLATE "pg_catalog"."default" DEFAULT NULL::bpchar,
  "is_insert" char(1) COLLATE "pg_catalog"."default" DEFAULT NULL::bpchar,
  "is_edit" char(1) COLLATE "pg_catalog"."default" DEFAULT NULL::bpchar,
  "is_list" char(1) COLLATE "pg_catalog"."default" DEFAULT NULL::bpchar,
  "is_query" char(1) COLLATE "pg_catalog"."default" DEFAULT NULL::bpchar,
  "query_type" varchar(200) COLLATE "pg_catalog"."default" DEFAULT 'EQ'::character varying,
  "html_type" varchar(200) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "dict_type" varchar(200) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "sort" int4,
  "create_by" varchar(64) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "create_time" timestamp(6),
  "update_by" varchar(64) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "update_time" timestamp(6)
)
;
COMMENT ON COLUMN "public"."gen_table_column"."column_id" IS '编号';
COMMENT ON COLUMN "public"."gen_table_column"."table_id" IS '归属表编号';
COMMENT ON COLUMN "public"."gen_table_column"."column_name" IS '列名称';
COMMENT ON COLUMN "public"."gen_table_column"."column_comment" IS '列描述';
COMMENT ON COLUMN "public"."gen_table_column"."column_type" IS '列类型';
COMMENT ON COLUMN "public"."gen_table_column"."java_type" IS 'JAVA类型';
COMMENT ON COLUMN "public"."gen_table_column"."java_field" IS 'JAVA字段名';
COMMENT ON COLUMN "public"."gen_table_column"."is_pk" IS '是否主键（1是）';
COMMENT ON COLUMN "public"."gen_table_column"."is_increment" IS '是否自增（1是）';
COMMENT ON COLUMN "public"."gen_table_column"."is_required" IS '是否必填（1是）';
COMMENT ON COLUMN "public"."gen_table_column"."is_insert" IS '是否为插入字段（1是）';
COMMENT ON COLUMN "public"."gen_table_column"."is_edit" IS '是否编辑字段（1是）';
COMMENT ON COLUMN "public"."gen_table_column"."is_list" IS '是否列表字段（1是）';
COMMENT ON COLUMN "public"."gen_table_column"."is_query" IS '是否查询字段（1是）';
COMMENT ON COLUMN "public"."gen_table_column"."query_type" IS '查询方式（等于、不等于、大于、小于、范围）';
COMMENT ON COLUMN "public"."gen_table_column"."html_type" IS '显示类型（文本框、文本域、下拉框、复选框、单选框、日期控件）';
COMMENT ON COLUMN "public"."gen_table_column"."dict_type" IS '字典类型';
COMMENT ON COLUMN "public"."gen_table_column"."sort" IS '排序';
COMMENT ON COLUMN "public"."gen_table_column"."create_by" IS '创建者';
COMMENT ON COLUMN "public"."gen_table_column"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."gen_table_column"."update_by" IS '更新者';
COMMENT ON COLUMN "public"."gen_table_column"."update_time" IS '更新时间';
COMMENT ON TABLE "public"."gen_table_column" IS '代码生成业务表字段';

-- ----------------------------
-- Records of gen_table_column
-- ----------------------------

-- ----------------------------
-- Table structure for infra_api_access_log
-- ----------------------------
DROP TABLE IF EXISTS "public"."infra_api_access_log";
CREATE TABLE "public"."infra_api_access_log" (
  "id" int8 NOT NULL DEFAULT nextval('infra_api_access_log_id_seq'::regclass),
  "trace_id" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "user_id" int8 NOT NULL DEFAULT 0,
  "user_type" int2 NOT NULL DEFAULT 0,
  "application_name" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "request_method" varchar(16) COLLATE "pg_catalog"."default" NOT NULL,
  "request_url" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "request_params" text COLLATE "pg_catalog"."default" NOT NULL,
  "user_ip" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "user_agent" varchar(512) COLLATE "pg_catalog"."default" NOT NULL,
  "begin_time" timestamp(6) NOT NULL,
  "end_time" timestamp(6) NOT NULL,
  "duration" int4 NOT NULL,
  "result_code" int4 NOT NULL,
  "result_msg" varchar(512) COLLATE "pg_catalog"."default",
  "creator" varchar(64) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6) NOT NULL,
  "updater" varchar(64) COLLATE "pg_catalog"."default",
  "update_time" timestamp(6) NOT NULL,
  "deleted" int2 NOT NULL DEFAULT 0,
  "tenant_id" int8 NOT NULL DEFAULT 0
)
;
COMMENT ON COLUMN "public"."infra_api_access_log"."id" IS '日志主键';
COMMENT ON COLUMN "public"."infra_api_access_log"."trace_id" IS '链路追踪编号';
COMMENT ON COLUMN "public"."infra_api_access_log"."user_id" IS '用户编号';
COMMENT ON COLUMN "public"."infra_api_access_log"."user_type" IS '用户类型';
COMMENT ON COLUMN "public"."infra_api_access_log"."application_name" IS '应用名';
COMMENT ON COLUMN "public"."infra_api_access_log"."request_method" IS '请求方法名';
COMMENT ON COLUMN "public"."infra_api_access_log"."request_url" IS '请求地址';
COMMENT ON COLUMN "public"."infra_api_access_log"."request_params" IS '请求参数';
COMMENT ON COLUMN "public"."infra_api_access_log"."user_ip" IS '用户 IP';
COMMENT ON COLUMN "public"."infra_api_access_log"."user_agent" IS '浏览器 UA';
COMMENT ON COLUMN "public"."infra_api_access_log"."begin_time" IS '开始请求时间';
COMMENT ON COLUMN "public"."infra_api_access_log"."end_time" IS '结束请求时间';
COMMENT ON COLUMN "public"."infra_api_access_log"."duration" IS '执行时长';
COMMENT ON COLUMN "public"."infra_api_access_log"."result_code" IS '结果码';
COMMENT ON COLUMN "public"."infra_api_access_log"."result_msg" IS '结果提示';
COMMENT ON COLUMN "public"."infra_api_access_log"."creator" IS '创建者';
COMMENT ON COLUMN "public"."infra_api_access_log"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."infra_api_access_log"."updater" IS '更新者';
COMMENT ON COLUMN "public"."infra_api_access_log"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."infra_api_access_log"."deleted" IS '是否删除';
COMMENT ON COLUMN "public"."infra_api_access_log"."tenant_id" IS '租户编号';
COMMENT ON TABLE "public"."infra_api_access_log" IS 'API 访问日志表';

-- ----------------------------
-- Records of infra_api_access_log
-- ----------------------------
INSERT INTO "public"."infra_api_access_log" VALUES (1, '3bf23472-f8b6-4f14-a5b7-17737bb20222', 1, 1, '获得短信日志分页', 'GET', '/com/zhonghe/dev-api/system/sms-log/page', '{}', '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36', '2024-11-04 09:21:46.535', '2024-11-04 09:21:46.535', 0, 200, '操作成功', NULL, '2024-11-04 09:21:46.535', NULL, '2024-11-04 09:21:46.535', 0, 0);
INSERT INTO "public"."infra_api_access_log" VALUES (2, '82eea935-3df2-48d5-b9cb-37dbab0038e4', 1, 1, '获得短信日志分页', 'GET', '/com/zhonghe/dev-api/system/sms-log/page', '{}', '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36', '2024-11-04 09:25:14.55', '2024-11-04 09:25:14.55', 0, 200, '操作成功', NULL, '2024-11-04 09:25:14.55', NULL, '2024-11-04 09:25:14.55', 0, 0);
INSERT INTO "public"."infra_api_access_log" VALUES (3, '3ce8f024-f427-477d-94d6-f49d0e84bb98', 1, 1, '对象存储桶', 'POST', '/com/zhonghe/system/bucket', '{"params":{},"id":"1853265219285508097","nickName":"测试","bucketType":"1","capacityType":"MB","maxCapacity":10.0,"personal":1}', '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36', '2024-11-04 10:37:15.793', '2024-11-04 10:37:15.793', 1, 200, '操作成功', NULL, '2024-11-04 10:37:15.793', NULL, '2024-11-04 10:37:15.793', 0, 0);
INSERT INTO "public"."infra_api_access_log" VALUES (4, 'b2df0937-37c5-4f76-9fd2-6a742ab1c775', 1, 1, '对象存储桶', 'DELETE', '/com/zhonghe/system/bucket/1853265219285508097', '{ids=1853265219285508097}', '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36', '2024-11-04 10:37:23.462', '2024-11-04 10:37:23.462', 3, 200, '操作成功', NULL, '2024-11-04 10:37:23.462', NULL, '2024-11-04 10:37:23.462', 0, 0);
INSERT INTO "public"."infra_api_access_log" VALUES (5, '742bc31b-abc4-4308-8554-b9ebdb773793', 1, 1, '菜单管理', 'PUT', '/com/zhonghe/system/menu', '{"updateBy":"admin","updateTime":"2024-11-04 10:49:44","params":{},"parentId":"1641692769984393217","children":[],"menuId":"1641698767885250561","menuName":"机器人配置","orderNum":3,"path":"/system/notice/robot","component":"System/Robot/Index","isFrame":"1","menuType":"C","visible":"0","status":"0","icon":"Dot","pageName":"Robot","isApp":1}', '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36', '2024-11-04 10:49:44.561', '2024-11-04 10:49:44.561', 2, 200, '操作成功', NULL, '2024-11-04 10:49:44.561', NULL, '2024-11-04 10:49:44.561', 0, 0);
INSERT INTO "public"."infra_api_access_log" VALUES (6, '38fad5fb-3cba-4b46-8da5-0b239b7dfc0b', 1, 1, '获得短信日志分页', 'GET', '/com/zhonghe/dev-api/system/sms-log/page', '{}', '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36', '2024-11-04 10:53:22.932', '2024-11-04 10:53:22.932', 0, 200, '操作成功', NULL, '2024-11-04 10:53:22.932', NULL, '2024-11-04 10:53:22.932', 0, 0);
INSERT INTO "public"."infra_api_access_log" VALUES (7, '0b23affa-696f-4a0c-9a8f-5150b135e18c', 1, 1, '菜单管理', 'PUT', '/com/zhonghe/system/menu', '{"updateBy":"admin","updateTime":"2024-11-04 10:54:41","params":{},"parentId":"0","children":[],"menuId":"1641691307338313730","menuName":"资源管理","orderNum":97,"path":"/system/resource","isFrame":"1","menuType":"M","visible":"0","status":"0","icon":"FolderFull","pageName":"Resource","isApp":1}', '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36', '2024-11-04 10:54:41.188', '2024-11-04 10:54:41.188', 2, 200, '操作成功', NULL, '2024-11-04 10:54:41.188', NULL, '2024-11-04 10:54:41.188', 0, 0);
INSERT INTO "public"."infra_api_access_log" VALUES (8, 'f74739dd-8954-4ebd-801b-ba1333a6c958', 1, 1, '菜单管理', 'PUT', '/com/zhonghe/system/menu', '{"updateBy":"admin","updateTime":"2024-11-04 10:54:55","params":{},"parentId":"0","children":[],"menuId":"2010","menuName":"系统监控","orderNum":999,"path":"/system/monitor","isFrame":"1","menuType":"M","visible":"0","status":"0","icon":"MonitorFull","pageName":"Monitor","isApp":1}', '************', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/129.0.0.0 Safari/537.36', '2024-11-04 10:54:55.64', '2024-11-04 10:54:55.64', 2, 200, '操作成功', NULL, '2024-11-04 10:54:55.64', NULL, '2024-11-04 10:54:55.64', 0, 0);
INSERT INTO "public"."infra_api_access_log" VALUES (9, '1949c4ba-ad19-4a91-8fe5-2eba17fee48c', 1, 1, 'OSS对象存储', 'POST', '/com/zhonghe/system/oss/upload', '', '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36', '2024-11-04 10:58:54.465', '2024-11-04 10:58:54.465', 1, 200, '操作成功', NULL, '2024-11-04 10:58:54.465', NULL, '2024-11-04 10:58:54.465', 0, 0);
INSERT INTO "public"."infra_api_access_log" VALUES (10, '9e97a144-7fe0-4c82-9536-05c974bb05c7', 1, 1, 'OSS对象存储', 'POST', '/com/zhonghe/system/oss/upload', '', '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36', '2024-11-04 10:59:00.133', '2024-11-04 10:59:00.133', 1, 200, '操作成功', NULL, '2024-11-04 10:59:00.133', NULL, '2024-11-04 10:59:00.133', 0, 0);
INSERT INTO "public"."infra_api_access_log" VALUES (11, '72142502-f89e-4c39-af7e-ec1c45d323d0', 1, 1, 'OSS对象存储', 'POST', '/com/zhonghe/system/oss/upload', '', '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36', '2024-11-04 10:59:08.208', '2024-11-04 10:59:08.208', 1, 200, '操作成功', NULL, '2024-11-04 10:59:08.208', NULL, '2024-11-04 10:59:08.208', 0, 0);
INSERT INTO "public"."infra_api_access_log" VALUES (12, 'dc6fcc5d-128b-4f6d-bd09-71e2edcabd54', 1, 1, 'OSS对象存储', 'POST', '/com/zhonghe/system/oss/upload', '', '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36', '2024-11-04 10:59:08.926', '2024-11-04 10:59:08.926', 1, 200, '操作成功', NULL, '2024-11-04 10:59:08.926', NULL, '2024-11-04 10:59:08.926', 0, 0);
INSERT INTO "public"."infra_api_access_log" VALUES (13, '25c065af-ed9e-49d3-af9c-17bb145a5862', 1, 1, 'OSS对象存储', 'POST', '/com/zhonghe/system/oss/upload', '', '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36', '2024-11-04 10:59:12.423', '2024-11-04 10:59:12.423', 1, 200, '操作成功', NULL, '2024-11-04 10:59:12.423', NULL, '2024-11-04 10:59:12.423', 0, 0);
INSERT INTO "public"."infra_api_access_log" VALUES (14, 'aabdfa91-fe90-47c9-a51e-40086c934771', 1, 1, 'OSS对象存储', 'POST', '/com/zhonghe/system/oss/upload', '', '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36', '2024-11-04 10:59:17.673', '2024-11-04 10:59:17.673', 1, 200, '操作成功', NULL, '2024-11-04 10:59:17.673', NULL, '2024-11-04 10:59:17.673', 0, 0);
INSERT INTO "public"."infra_api_access_log" VALUES (15, '793e0b2f-e835-454c-8bff-8234b4e4950f', 1, 1, 'OSS对象存储', 'POST', '/com/zhonghe/system/oss/upload', '', '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36', '2024-11-04 11:00:10.13', '2024-11-04 11:00:10.13', 1, 200, '操作成功', NULL, '2024-11-04 11:00:10.13', NULL, '2024-11-04 11:00:10.13', 0, 0);
INSERT INTO "public"."infra_api_access_log" VALUES (16, '9803c768-70fb-4347-883d-66cf733f15e9', 1, 1, 'OSS对象存储', 'POST', '/com/zhonghe/system/oss/upload', '', '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36', '2024-11-04 11:01:07.861', '2024-11-04 11:01:07.861', 1, 200, '操作成功', NULL, '2024-11-04 11:01:07.861', NULL, '2024-11-04 11:01:07.861', 0, 0);
INSERT INTO "public"."infra_api_access_log" VALUES (17, '83553574-2f84-41e7-9ac4-e69a191f5176', 1, 1, 'OSS对象存储', 'POST', '/com/zhonghe/system/oss/upload', '', '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36', '2024-11-04 11:01:22.733', '2024-11-04 11:01:22.733', 1, 200, '操作成功', NULL, '2024-11-04 11:01:22.733', NULL, '2024-11-04 11:01:22.733', 0, 0);
INSERT INTO "public"."infra_api_access_log" VALUES (18, '76b10f8a-546c-4090-80a7-a57f9e4ab669', 1, 1, 'OSS对象存储', 'POST', '/com/zhonghe/system/oss/upload', '', '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36', '2024-11-04 11:02:40.467', '2024-11-04 11:02:40.467', 1, 200, '操作成功', NULL, '2024-11-04 11:02:40.467', NULL, '2024-11-04 11:02:40.467', 0, 0);
INSERT INTO "public"."infra_api_access_log" VALUES (19, 'ca995ea8-a3a5-4412-8278-9eaec5567a41', 1, 1, 'OSS对象存储', 'POST', '/com/zhonghe/system/oss/upload', '', '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36', '2024-11-04 11:06:11.626', '2024-11-04 11:06:11.626', 1, 200, '操作成功', NULL, '2024-11-04 11:06:11.626', NULL, '2024-11-04 11:06:11.626', 0, 0);
INSERT INTO "public"."infra_api_access_log" VALUES (20, 'a542de57-0312-4224-a8bd-1e5d187544c1', 1, 1, 'OSS对象存储', 'POST', '/com/zhonghe/system/oss/upload', '', '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36', '2024-11-04 11:06:17.675', '2024-11-04 11:06:17.675', 1, 200, '操作成功', NULL, '2024-11-04 11:06:17.675', NULL, '2024-11-04 11:06:17.675', 0, 0);
INSERT INTO "public"."infra_api_access_log" VALUES (22, 'b0264db3-3f38-48ff-b527-80c5dd7701b9', 1, 1, 'OSS对象存储', 'POST', '/com/zhonghe/system/oss/upload', '', '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36', '2024-11-04 11:07:04.516', '2024-11-04 11:07:04.516', 1, 200, '操作成功', NULL, '2024-11-04 11:07:04.516', NULL, '2024-11-04 11:07:04.516', 0, 0);
INSERT INTO "public"."infra_api_access_log" VALUES (21, '0c604734-c49a-48f5-931d-682e4c2f67ca', 1, 1, 'OSS对象存储', 'POST', '/com/zhonghe/system/oss/upload', '', '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36', '2024-11-04 11:06:24.169', '2024-11-04 11:06:24.169', 1, 200, '操作成功', NULL, '2024-11-04 11:06:24.169', NULL, '2024-11-04 11:06:24.169', 0, 0);
INSERT INTO "public"."infra_api_access_log" VALUES (23, 'b0856249-31a6-49aa-99a6-ed1f66a5ad55', 1, 1, 'OSS对象存储', 'POST', '/com/zhonghe/system/oss/upload', '', '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36', '2024-11-04 11:07:09.513', '2024-11-04 11:07:09.513', 1, 200, '操作成功', NULL, '2024-11-04 11:07:09.513', NULL, '2024-11-04 11:07:09.513', 0, 0);

-- ----------------------------
-- Table structure for infra_api_error_log
-- ----------------------------
DROP TABLE IF EXISTS "public"."infra_api_error_log";
CREATE TABLE "public"."infra_api_error_log" (
  "id" int4 NOT NULL DEFAULT nextval('infra_api_error_log_id_seq'::regclass),
  "trace_id" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "user_id" int4 NOT NULL DEFAULT 0,
  "user_type" int2 NOT NULL DEFAULT 0,
  "application_name" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "request_method" varchar(16) COLLATE "pg_catalog"."default" NOT NULL,
  "request_url" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "request_params" varchar(8000) COLLATE "pg_catalog"."default" NOT NULL,
  "user_ip" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "user_agent" varchar(512) COLLATE "pg_catalog"."default" NOT NULL,
  "exception_time" timestamp(6) NOT NULL,
  "exception_name" varchar(128) COLLATE "pg_catalog"."default" NOT NULL,
  "exception_message" text COLLATE "pg_catalog"."default" NOT NULL,
  "exception_root_cause_message" text COLLATE "pg_catalog"."default" NOT NULL,
  "exception_stack_trace" text COLLATE "pg_catalog"."default" NOT NULL,
  "exception_class_name" varchar(512) COLLATE "pg_catalog"."default" NOT NULL,
  "exception_file_name" varchar(512) COLLATE "pg_catalog"."default" NOT NULL,
  "exception_method_name" varchar(512) COLLATE "pg_catalog"."default" NOT NULL,
  "exception_line_number" int4 NOT NULL,
  "process_status" int2 NOT NULL DEFAULT 0,
  "process_time" timestamp(6),
  "process_user_id" int4,
  "creator" varchar(64) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6) NOT NULL,
  "updater" varchar(64) COLLATE "pg_catalog"."default",
  "update_time" timestamp(6) NOT NULL,
  "deleted" int2 NOT NULL DEFAULT 0,
  "tenant_id" int8 NOT NULL DEFAULT 0
)
;
COMMENT ON COLUMN "public"."infra_api_error_log"."id" IS '编号';
COMMENT ON COLUMN "public"."infra_api_error_log"."trace_id" IS '链路追踪编号
    *
    * 一般来说，通过链路追踪编号，可以将访问日志，错误日志，链路追踪日志，logger 打印日志等，结合在一起，从而进行排错。';
COMMENT ON COLUMN "public"."infra_api_error_log"."user_id" IS '用户编号';
COMMENT ON COLUMN "public"."infra_api_error_log"."user_type" IS '用户类型';
COMMENT ON COLUMN "public"."infra_api_error_log"."application_name" IS '应用名
    *
    * 目前读取 spring.application.name';
COMMENT ON COLUMN "public"."infra_api_error_log"."request_method" IS '请求方法名';
COMMENT ON COLUMN "public"."infra_api_error_log"."request_url" IS '请求地址';
COMMENT ON COLUMN "public"."infra_api_error_log"."request_params" IS '请求参数';
COMMENT ON COLUMN "public"."infra_api_error_log"."user_ip" IS '用户 IP';
COMMENT ON COLUMN "public"."infra_api_error_log"."user_agent" IS '浏览器 UA';
COMMENT ON COLUMN "public"."infra_api_error_log"."exception_time" IS '异常发生时间';
COMMENT ON COLUMN "public"."infra_api_error_log"."exception_name" IS '异常名
    *
    * {@link Throwable#getClass()} 的类全名';
COMMENT ON COLUMN "public"."infra_api_error_log"."exception_message" IS '异常导致的消息
    *
    * {@link cn.iocoder.common.framework.util.ExceptionUtil#getMessage(Throwable)}';
COMMENT ON COLUMN "public"."infra_api_error_log"."exception_root_cause_message" IS '异常导致的根消息
    *
    * {@link cn.iocoder.common.framework.util.ExceptionUtil#getRootCauseMessage(Throwable)}';
COMMENT ON COLUMN "public"."infra_api_error_log"."exception_stack_trace" IS '异常的栈轨迹
    *
    * {@link cn.iocoder.common.framework.util.ExceptionUtil#getServiceException(Exception)}';
COMMENT ON COLUMN "public"."infra_api_error_log"."exception_class_name" IS '异常发生的类全名
    *
    * {@link StackTraceElement#getClassName()}';
COMMENT ON COLUMN "public"."infra_api_error_log"."exception_file_name" IS '异常发生的类文件
    *
    * {@link StackTraceElement#getFileName()}';
COMMENT ON COLUMN "public"."infra_api_error_log"."exception_method_name" IS '异常发生的方法名
    *
    * {@link StackTraceElement#getMethodName()}';
COMMENT ON COLUMN "public"."infra_api_error_log"."exception_line_number" IS '异常发生的方法所在行
    *
    * {@link StackTraceElement#getLineNumber()}';
COMMENT ON COLUMN "public"."infra_api_error_log"."process_status" IS '处理状态';
COMMENT ON COLUMN "public"."infra_api_error_log"."process_time" IS '处理时间';
COMMENT ON COLUMN "public"."infra_api_error_log"."process_user_id" IS '处理用户编号';
COMMENT ON COLUMN "public"."infra_api_error_log"."creator" IS '创建者';
COMMENT ON COLUMN "public"."infra_api_error_log"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."infra_api_error_log"."updater" IS '更新者';
COMMENT ON COLUMN "public"."infra_api_error_log"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."infra_api_error_log"."deleted" IS '是否删除';
COMMENT ON COLUMN "public"."infra_api_error_log"."tenant_id" IS '租户编号';
COMMENT ON TABLE "public"."infra_api_error_log" IS '系统异常日志';

-- ----------------------------
-- Records of infra_api_error_log
-- ----------------------------
INSERT INTO "public"."infra_api_error_log" VALUES (1, '101', 1, 1, 'OSS对象存储', 'POST', '/com/zhonghe/system/oss/upload', '', '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36', '2024-11-04 10:40:25.475', 'org.springframework.jdbc.support.SQLStateSQLExceptionTranslator', '
### Error updating database.  Cause: org.postgresql.util.PSQLException: ERROR: column "bucket_id" of relation "sys_oss" does not exist
  Position: 85
### The error may exist in com/zhonghe/system/mapper/SysOssMapper.java (best guess)
### The error may involve com.zhonghe.system.mapper.SysOssMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO sys_oss  ( oss_id, file_name, original_name, file_suffix, url, service, bucket_id, create_by, create_time, update_by, update_time )  VALUES  ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
### Cause: org.postgresql.util.PSQLException: ERROR: column "bucket_id" of relation "sys_oss" does not exist
  Position: 85
; bad SQL grammar []; nested exception is org.postgresql.util.PSQLException: ERROR: column "bucket_id" of relation "sys_oss" does not exist
  Position: 85', '
### Error updating database.  Cause: org.postgresql.util.PSQLException: ERROR: column "bucket_id" of relation "sys_oss" does not exist
  Position: 85
### The error may exist in com/zhonghe/system/mapper/SysOssMapper.java (best guess)
### The error may involve com.zhonghe.system.mapper.SysOssMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO sys_oss  ( oss_id, file_name, original_name, file_suffix, url, service, bucket_id, create_by, create_time, update_by, update_time )  VALUES  ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
### Cause: org.postgresql.util.PSQLException: ERROR: column "bucket_id" of relation "sys_oss" does not exist
  Position: 85
; bad SQL grammar []; nested exception is org.postgresql.util.PSQLException: ERROR: column "bucket_id" of relation "sys_oss" does not exist
  Position: 85', 'org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:101)
org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70)
org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:79)
org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:79)
org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91)
org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
com.sun.proxy.$Proxy161.insert(Unknown Source)
org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:272)
com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
com.sun.proxy.$Proxy214.insert(Unknown Source)
com.zhonghe.system.service.impl.SysOssServiceImpl.upload(SysOssServiceImpl.java:171)
com.zhonghe.web.controller.system.SysOssController.upload(SysOssController.java:101)
com.zhonghe.web.controller.system.SysOssController$$FastClassBySpringCGLIB$$513af3a9.invoke(<generated>)
org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:123)
org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64)
org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
com.zhonghe.web.controller.system.SysOssController$$EnhancerBySpringCGLIB$$a4882583.upload(<generated>)
sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
java.lang.reflect.Method.invoke(Method.java:498)
org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
com.github.xiaoymin.knife4j.spring.filter.SecurityBasicAuthFilter.doFilter(SecurityBasicAuthFilter.java:87)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
com.zhonghe.common.filter.SensitiveFilter.doFilter(SensitiveFilter.java:23)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
com.zhonghe.common.filter.RepeatableFilter.doFilter(RepeatableFilter.java:30)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1787)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.lang.Thread.run(Thread.java:748)
', 'org.springframework.jdbc.support.SQLStateSQLExceptionTranslator', 'SQLStateSQLExceptionTranslator.java', 'doTranslate', 101, 0, '2024-11-04 10:40:25.479', NULL, 'admin', '2024-11-04 10:40:25.479', 'admin', '2024-11-04 10:40:25.475', 0, 0);
INSERT INTO "public"."infra_api_error_log" VALUES (2, '101', 1, 1, 'OSS对象存储', 'POST', '/com/zhonghe/system/oss/upload', '', '*************', 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36', '2024-11-04 10:40:55.901', 'org.springframework.jdbc.support.SQLStateSQLExceptionTranslator', '
### Error updating database.  Cause: org.postgresql.util.PSQLException: ERROR: column "bucket_id" of relation "sys_oss" does not exist
  Position: 85
### The error may exist in com/zhonghe/system/mapper/SysOssMapper.java (best guess)
### The error may involve com.zhonghe.system.mapper.SysOssMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO sys_oss  ( oss_id, file_name, original_name, file_suffix, url, service, bucket_id, create_by, create_time, update_by, update_time )  VALUES  ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
### Cause: org.postgresql.util.PSQLException: ERROR: column "bucket_id" of relation "sys_oss" does not exist
  Position: 85
; bad SQL grammar []; nested exception is org.postgresql.util.PSQLException: ERROR: column "bucket_id" of relation "sys_oss" does not exist
  Position: 85', '
### Error updating database.  Cause: org.postgresql.util.PSQLException: ERROR: column "bucket_id" of relation "sys_oss" does not exist
  Position: 85
### The error may exist in com/zhonghe/system/mapper/SysOssMapper.java (best guess)
### The error may involve com.zhonghe.system.mapper.SysOssMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO sys_oss  ( oss_id, file_name, original_name, file_suffix, url, service, bucket_id, create_by, create_time, update_by, update_time )  VALUES  ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
### Cause: org.postgresql.util.PSQLException: ERROR: column "bucket_id" of relation "sys_oss" does not exist
  Position: 85
; bad SQL grammar []; nested exception is org.postgresql.util.PSQLException: ERROR: column "bucket_id" of relation "sys_oss" does not exist
  Position: 85', 'org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:101)
org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:70)
org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:79)
org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:79)
org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:91)
org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:441)
com.sun.proxy.$Proxy161.insert(Unknown Source)
org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:272)
com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)
com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:148)
com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)
com.sun.proxy.$Proxy214.insert(Unknown Source)
com.zhonghe.system.service.impl.SysOssServiceImpl.upload(SysOssServiceImpl.java:171)
com.zhonghe.web.controller.system.SysOssController.upload(SysOssController.java:101)
com.zhonghe.web.controller.system.SysOssController$$FastClassBySpringCGLIB$$513af3a9.invoke(<generated>)
org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218)
org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.invokeJoinpoint(CglibAopProxy.java:793)
org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
org.springframework.validation.beanvalidation.MethodValidationInterceptor.invoke(MethodValidationInterceptor.java:123)
org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
org.springframework.aop.aspectj.AspectJAfterThrowingAdvice.invoke(AspectJAfterThrowingAdvice.java:64)
org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:175)
org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
org.springframework.aop.framework.adapter.AfterReturningAdviceInterceptor.invoke(AfterReturningAdviceInterceptor.java:57)
org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:186)
org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:763)
org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:708)
com.zhonghe.web.controller.system.SysOssController$$EnhancerBySpringCGLIB$$a4882583.upload(<generated>)
sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
java.lang.reflect.Method.invoke(Method.java:498)
org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909)
javax.servlet.http.HttpServlet.service(HttpServlet.java:681)
org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
com.github.xiaoymin.knife4j.spring.filter.SecurityBasicAuthFilter.doFilter(SecurityBasicAuthFilter.java:87)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
org.springframework.web.filter.CorsFilter.doFilterInternal(CorsFilter.java:91)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
com.alibaba.druid.support.http.WebStatFilter.doFilter(WebStatFilter.java:114)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
com.zhonghe.common.filter.SensitiveFilter.doFilter(SensitiveFilter.java:23)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
com.zhonghe.common.filter.RepeatableFilter.doFilter(RepeatableFilter.java:30)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:541)
org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:360)
org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:399)
org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:890)
org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1787)
org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
java.lang.Thread.run(Thread.java:748)
', 'org.springframework.jdbc.support.SQLStateSQLExceptionTranslator', 'SQLStateSQLExceptionTranslator.java', 'doTranslate', 101, 0, '2024-11-04 10:40:55.903', NULL, 'admin', '2024-11-04 10:40:55.903', 'admin', '2024-11-04 10:40:55.901', 0, 0);

-- ----------------------------
-- Table structure for read_the_information
-- ----------------------------
DROP TABLE IF EXISTS "public"."read_the_information";
CREATE TABLE "public"."read_the_information" (
  "user_id" int8 NOT NULL DEFAULT '1'::bigint,
  "sms_id" int8,
  "noted_id" int8,
  "stuts" char(1) COLLATE "pg_catalog"."default",
  "extend" varchar(100) COLLATE "pg_catalog"."default",
  "information_id" int4 NOT NULL DEFAULT nextval('read_the_information_information_id_seq'::regclass)
)
;
COMMENT ON COLUMN "public"."read_the_information"."sms_id" IS '短信id';
COMMENT ON COLUMN "public"."read_the_information"."noted_id" IS '公告id';
COMMENT ON COLUMN "public"."read_the_information"."stuts" IS '读取状态:‘0’短信已读   ‘1’短信未读  ''2''公告已读 ''3''公告未读';
COMMENT ON COLUMN "public"."read_the_information"."extend" IS '扩展';
COMMENT ON COLUMN "public"."read_the_information"."information_id" IS '主键';

-- ----------------------------
-- Records of read_the_information
-- ----------------------------

-- ----------------------------
-- Table structure for sms_request_ebtity
-- ----------------------------
DROP TABLE IF EXISTS "public"."sms_request_ebtity";
CREATE TABLE "public"."sms_request_ebtity" (
  "out_id" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "owner_id" int8,
  "phone_numbers" char(50) COLLATE "pg_catalog"."default",
  "resource_owner_account" varchar(100) COLLATE "pg_catalog"."default",
  "resource_owner_id" int8,
  "sign_name" varchar(50) COLLATE "pg_catalog"."default",
  "sms_up_extend_code" varchar(100) COLLATE "pg_catalog"."default",
  "template_code" varchar(100) COLLATE "pg_catalog"."default",
  "template_param" varchar(255) COLLATE "pg_catalog"."default",
  "template_content" varchar(255) COLLATE "pg_catalog"."default",
  "sms_type" varchar(50) COLLATE "pg_catalog"."default",
  "id_open" int8,
  "node" varchar(500) COLLATE "pg_catalog"."default",
  "test_channel" varchar(20) COLLATE "pg_catalog"."default",
  "create_by" varchar COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "update_by" varchar COLLATE "pg_catalog"."default",
  "update_time" timestamp(6)
)
;
COMMENT ON COLUMN "public"."sms_request_ebtity"."out_id" IS 'uuid';
COMMENT ON COLUMN "public"."sms_request_ebtity"."phone_numbers" IS '电话';
COMMENT ON COLUMN "public"."sms_request_ebtity"."template_code" IS '模板代码';
COMMENT ON COLUMN "public"."sms_request_ebtity"."template_param" IS '模板参数';
COMMENT ON COLUMN "public"."sms_request_ebtity"."template_content" IS '内容';
COMMENT ON COLUMN "public"."sms_request_ebtity"."sms_type" IS '短信类型';
COMMENT ON COLUMN "public"."sms_request_ebtity"."id_open" IS '是否开启状态';
COMMENT ON COLUMN "public"."sms_request_ebtity"."node" IS '备注';
COMMENT ON COLUMN "public"."sms_request_ebtity"."test_channel" IS '测试渠道';

-- ----------------------------
-- Records of sms_request_ebtity
-- ----------------------------

-- ----------------------------
-- Table structure for sys_config
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_config";
CREATE TABLE "public"."sys_config" (
  "config_id" int8 NOT NULL,
  "config_name" varchar(100) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "config_key" varchar(100) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "config_value" varchar(500) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "config_type" char(1) COLLATE "pg_catalog"."default" DEFAULT 'N'::bpchar,
  "create_by" varchar(64) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "create_time" timestamp(6),
  "update_by" varchar(64) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "update_time" timestamp(6),
  "remark" varchar(500) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying
)
;
COMMENT ON COLUMN "public"."sys_config"."config_id" IS '参数主键';
COMMENT ON COLUMN "public"."sys_config"."config_name" IS '参数名称';
COMMENT ON COLUMN "public"."sys_config"."config_key" IS '参数键名';
COMMENT ON COLUMN "public"."sys_config"."config_value" IS '参数键值';
COMMENT ON COLUMN "public"."sys_config"."config_type" IS '系统内置（Y是 N否）';
COMMENT ON COLUMN "public"."sys_config"."create_by" IS '创建者';
COMMENT ON COLUMN "public"."sys_config"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."sys_config"."update_by" IS '更新者';
COMMENT ON COLUMN "public"."sys_config"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."sys_config"."remark" IS '备注';
COMMENT ON TABLE "public"."sys_config" IS '参数配置表';

-- ----------------------------
-- Records of sys_config
-- ----------------------------
INSERT INTO "public"."sys_config" VALUES (3, '主框架页-侧边栏主题', 'sys.index.sideTheme', 'theme-dark', 'Y', 'admin', '2022-07-05 08:12:40', 'admin', '2023-03-13 09:40:38.819', '深色主题theme-dark，浅色主题theme-light');
INSERT INTO "public"."sys_config" VALUES (1, '主框架页-默认皮肤样式名称', 'sys.index.skinName', 'skin-green44', 'Y', 'admin', '2022-07-05 08:12:40', 'admin', '2023-03-13 13:59:39.567', '蓝色 skin-blue、绿色 skin-green、紫色 skin-purple、红色 skin-red、黄色 skin-yellow');
INSERT INTO "public"."sys_config" VALUES (2, '用户管理-账号初始密码', 'sys.user.initPassword', '123456', 'Y', 'admin', '2022-07-05 08:12:40', 'admin', '2023-03-09 17:55:14.295', '初始化密码 123456');
INSERT INTO "public"."sys_config" VALUES (4, '账号自助-验证码开关', 'sys.account.captchaOnOff', 'false', 'Y', 'admin', '2022-07-05 08:12:40', 'admin', '2024-11-04 11:07:10.934', '是否开启验证码功能（true开启，false关闭）');

-- ----------------------------
-- Table structure for sys_dept
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_dept";
CREATE TABLE "public"."sys_dept" (
  "dept_id" int8 NOT NULL,
  "parent_id" int8 DEFAULT 0,
  "ancestors" varchar(500) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "dept_name" varchar(30) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "order_num" int4 DEFAULT 0,
  "leader" varchar(20) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "phone" varchar(11) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "email" varchar(50) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "status" char(1) COLLATE "pg_catalog"."default" DEFAULT '0'::bpchar,
  "del_flag" char(1) COLLATE "pg_catalog"."default" DEFAULT '0'::bpchar,
  "create_by" varchar(64) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "create_time" timestamp(6),
  "update_by" varchar(64) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "update_time" timestamp(6)
)
;
COMMENT ON COLUMN "public"."sys_dept"."dept_id" IS '部门ID';
COMMENT ON COLUMN "public"."sys_dept"."parent_id" IS '父部门ID';
COMMENT ON COLUMN "public"."sys_dept"."ancestors" IS '祖级列表';
COMMENT ON COLUMN "public"."sys_dept"."dept_name" IS '部门名称';
COMMENT ON COLUMN "public"."sys_dept"."order_num" IS '显示顺序';
COMMENT ON COLUMN "public"."sys_dept"."leader" IS '负责人';
COMMENT ON COLUMN "public"."sys_dept"."phone" IS '联系电话';
COMMENT ON COLUMN "public"."sys_dept"."email" IS '邮箱';
COMMENT ON COLUMN "public"."sys_dept"."status" IS '部门状态（0正常 1停用）';
COMMENT ON COLUMN "public"."sys_dept"."del_flag" IS '删除标志（0代表存在 2代表删除）';
COMMENT ON COLUMN "public"."sys_dept"."create_by" IS '创建者';
COMMENT ON COLUMN "public"."sys_dept"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."sys_dept"."update_by" IS '更新者';
COMMENT ON COLUMN "public"."sys_dept"."update_time" IS '更新时间';
COMMENT ON TABLE "public"."sys_dept" IS '部门表';

-- ----------------------------
-- Records of sys_dept
-- ----------------------------

-- ----------------------------
-- Table structure for sys_dict_data
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_dict_data";
CREATE TABLE "public"."sys_dict_data" (
  "dict_code" int8 NOT NULL,
  "dict_sort" int4 DEFAULT 0,
  "dict_label" varchar(100) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "dict_value" varchar(100) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "dict_type" varchar(100) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "css_class" varchar(100) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "list_class" varchar(100) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "is_default" char(1) COLLATE "pg_catalog"."default" DEFAULT 'N'::bpchar,
  "status" char(1) COLLATE "pg_catalog"."default" DEFAULT '0'::bpchar,
  "create_by" varchar(64) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "create_time" timestamp(6),
  "update_by" varchar(64) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "update_time" timestamp(6),
  "remark" varchar(500) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying
)
;
COMMENT ON COLUMN "public"."sys_dict_data"."dict_code" IS '字典编码';
COMMENT ON COLUMN "public"."sys_dict_data"."dict_sort" IS '字典排序';
COMMENT ON COLUMN "public"."sys_dict_data"."dict_label" IS '字典标签';
COMMENT ON COLUMN "public"."sys_dict_data"."dict_value" IS '字典键值';
COMMENT ON COLUMN "public"."sys_dict_data"."dict_type" IS '字典类型';
COMMENT ON COLUMN "public"."sys_dict_data"."css_class" IS '样式属性（其他样式扩展）';
COMMENT ON COLUMN "public"."sys_dict_data"."list_class" IS '表格回显样式';
COMMENT ON COLUMN "public"."sys_dict_data"."is_default" IS '是否默认（Y是 N否）';
COMMENT ON COLUMN "public"."sys_dict_data"."status" IS '状态（0正常 1停用）';
COMMENT ON COLUMN "public"."sys_dict_data"."create_by" IS '创建者';
COMMENT ON COLUMN "public"."sys_dict_data"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."sys_dict_data"."update_by" IS '更新者';
COMMENT ON COLUMN "public"."sys_dict_data"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."sys_dict_data"."remark" IS '备注';
COMMENT ON TABLE "public"."sys_dict_data" IS '字典数据表';

-- ----------------------------
-- Records of sys_dict_data
-- ----------------------------
INSERT INTO "public"."sys_dict_data" VALUES (1, 1, '男', '0', 'sys_user_sex', '', '', 'Y', '0', 'admin', '2024-11-01 07:28:33.067956', '', NULL, '性别男');
INSERT INTO "public"."sys_dict_data" VALUES (2, 2, '女', '1', 'sys_user_sex', '', '', 'N', '0', 'admin', '2024-11-01 07:28:33.067956', '', NULL, '性别女');
INSERT INTO "public"."sys_dict_data" VALUES (3, 3, '未知', '2', 'sys_user_sex', '', '', 'N', '0', 'admin', '2024-11-01 07:28:33.067956', '', NULL, '性别未知');
INSERT INTO "public"."sys_dict_data" VALUES (4, 1, '显示', '0', 'sys_show_hide', '', 'primary', 'Y', '0', 'admin', '2024-11-01 07:28:33.067956', '', NULL, '显示菜单');
INSERT INTO "public"."sys_dict_data" VALUES (5, 2, '隐藏', '1', 'sys_show_hide', '', 'danger', 'N', '0', 'admin', '2024-11-01 07:28:33.067956', '', NULL, '隐藏菜单');
INSERT INTO "public"."sys_dict_data" VALUES (6, 1, '正常', '0', 'sys_normal_disable', '', 'primary', 'Y', '0', 'admin', '2024-11-01 07:28:33.067956', '', NULL, '正常状态');
INSERT INTO "public"."sys_dict_data" VALUES (7, 2, '停用', '1', 'sys_normal_disable', '', 'danger', 'N', '0', 'admin', '2024-11-01 07:28:33.067956', '', NULL, '停用状态');
INSERT INTO "public"."sys_dict_data" VALUES (12, 1, '是', 'Y', 'sys_yes_no', '', 'primary', 'Y', '0', 'admin', '2024-11-01 07:28:33.067956', '', NULL, '系统默认是');
INSERT INTO "public"."sys_dict_data" VALUES (13, 2, '否', 'N', 'sys_yes_no', '', 'danger', 'N', '0', 'admin', '2024-11-01 07:28:33.067956', '', NULL, '系统默认否');
INSERT INTO "public"."sys_dict_data" VALUES (14, 1, '通知', '1', 'sys_notice_type', '', 'warning', 'Y', '0', 'admin', '2024-11-01 07:28:33.067956', '', NULL, '通知');
INSERT INTO "public"."sys_dict_data" VALUES (15, 2, '公告', '2', 'sys_notice_type', '', 'success', 'N', '0', 'admin', '2024-11-01 07:28:33.067956', '', NULL, '公告');
INSERT INTO "public"."sys_dict_data" VALUES (16, 1, '正常', '0', 'sys_notice_status', '', 'primary', 'Y', '0', 'admin', '2024-11-01 07:28:33.067956', '', NULL, '正常状态');
INSERT INTO "public"."sys_dict_data" VALUES (17, 2, '关闭', '1', 'sys_notice_status', '', 'danger', 'N', '0', 'admin', '2024-11-01 07:28:33.067956', '', NULL, '关闭状态');
INSERT INTO "public"."sys_dict_data" VALUES (29, 99, '其他', '0', 'sys_oper_type', '', 'info', 'N', '0', 'admin', '2024-11-01 07:28:33.067956', '', NULL, '其他操作');
INSERT INTO "public"."sys_dict_data" VALUES (18, 1, '新增', '1', 'sys_oper_type', '', 'info', 'N', '0', 'admin', '2024-11-01 07:28:33.067956', '', NULL, '新增操作');
INSERT INTO "public"."sys_dict_data" VALUES (19, 2, '修改', '2', 'sys_oper_type', '', 'info', 'N', '0', 'admin', '2024-11-01 07:28:33.067956', '', NULL, '修改操作');
INSERT INTO "public"."sys_dict_data" VALUES (20, 3, '删除', '3', 'sys_oper_type', '', 'danger', 'N', '0', 'admin', '2024-11-01 07:28:33.067956', '', NULL, '删除操作');
INSERT INTO "public"."sys_dict_data" VALUES (21, 4, '授权', '4', 'sys_oper_type', '', 'primary', 'N', '0', 'admin', '2024-11-01 07:28:33.067956', '', NULL, '授权操作');
INSERT INTO "public"."sys_dict_data" VALUES (22, 5, '导出', '5', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', '2024-11-01 07:28:33.067956', '', NULL, '导出操作');
INSERT INTO "public"."sys_dict_data" VALUES (23, 6, '导入', '6', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', '2024-11-01 07:28:33.067956', '', NULL, '导入操作');
INSERT INTO "public"."sys_dict_data" VALUES (24, 7, '强退', '7', 'sys_oper_type', '', 'danger', 'N', '0', 'admin', '2024-11-01 07:28:33.067956', '', NULL, '强退操作');
INSERT INTO "public"."sys_dict_data" VALUES (25, 8, '生成代码', '8', 'sys_oper_type', '', 'warning', 'N', '0', 'admin', '2024-11-01 07:28:33.067956', '', NULL, '生成操作');
INSERT INTO "public"."sys_dict_data" VALUES (26, 9, '清空数据', '9', 'sys_oper_type', '', 'danger', 'N', '0', 'admin', '2024-11-01 07:28:33.067956', '', NULL, '清空操作');
INSERT INTO "public"."sys_dict_data" VALUES (27, 1, '成功', '0', 'sys_common_status', '', 'primary', 'N', '0', 'admin', '2024-11-01 07:28:33.067956', '', NULL, '正常状态');
INSERT INTO "public"."sys_dict_data" VALUES (28, 2, '失败', '1', 'sys_common_status', '', 'danger', 'N', '0', 'admin', '2024-11-01 07:28:33.067956', '', NULL, '停用状态');
INSERT INTO "public"."sys_dict_data" VALUES (30, 1, '进行中', 'running', 'wf_process_status', '', 'primary', 'N', '0', 'admin', '2024-11-01 07:28:33.067956', '', NULL, '进行中状态');
INSERT INTO "public"."sys_dict_data" VALUES (31, 2, '已终止', 'terminated', 'wf_process_status', '', 'danger', 'N', '0', 'admin', '2024-11-01 07:28:33.067956', '', NULL, '已终止状态');
INSERT INTO "public"."sys_dict_data" VALUES (32, 3, '已完成', 'completed', 'wf_process_status', '', 'success', 'N', '0', 'admin', '2024-11-01 07:28:33.067956', '', NULL, '已完成状态');
INSERT INTO "public"."sys_dict_data" VALUES (33, 4, '已取消', 'canceled', 'wf_process_status', '', 'warning', 'N', '0', 'admin', '2024-11-01 07:28:33.067956', '', NULL, '已取消状态');

-- ----------------------------
-- Table structure for sys_dict_type
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_dict_type";
CREATE TABLE "public"."sys_dict_type" (
  "dict_id" int8 NOT NULL,
  "dict_name" varchar(100) COLLATE "pg_catalog"."default",
  "dict_type" varchar(100) COLLATE "pg_catalog"."default",
  "status" char(1) COLLATE "pg_catalog"."default",
  "create_by" varchar(64) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "update_by" varchar(64) COLLATE "pg_catalog"."default",
  "update_time" timestamp(6),
  "remark" varchar(500) COLLATE "pg_catalog"."default",
  "system_property" char(1) COLLATE "pg_catalog"."default" DEFAULT '1'::bpchar
)
;
COMMENT ON COLUMN "public"."sys_dict_type"."dict_id" IS '字典主键';
COMMENT ON COLUMN "public"."sys_dict_type"."dict_name" IS '字典名称';
COMMENT ON COLUMN "public"."sys_dict_type"."dict_type" IS '字典类型';
COMMENT ON COLUMN "public"."sys_dict_type"."status" IS '状态（0正常 1停用）';
COMMENT ON COLUMN "public"."sys_dict_type"."create_by" IS '创建者';
COMMENT ON COLUMN "public"."sys_dict_type"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."sys_dict_type"."update_by" IS '更新者';
COMMENT ON COLUMN "public"."sys_dict_type"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."sys_dict_type"."remark" IS '备注';
COMMENT ON COLUMN "public"."sys_dict_type"."system_property" IS '是否系统字典：0是1是否';
COMMENT ON TABLE "public"."sys_dict_type" IS '字典类型表';

-- ----------------------------
-- Records of sys_dict_type
-- ----------------------------
INSERT INTO "public"."sys_dict_type" VALUES (18, '紧急情况等级', 'sys_notice_level', '0', 'admin', '2023-03-29 10:02:27.845928', NULL, NULL, '紧急情况等级列表', '0');
INSERT INTO "public"."sys_dict_type" VALUES (19, '机器人消息类型', 'sys_webhook_type', '0', 'admin', '2023-03-29 16:29:08.374947', NULL, NULL, '机器人消息类型', '0');
INSERT INTO "public"."sys_dict_type" VALUES (1633037637288910850, '是否门户首页', 'sys_menu_is_home', '0', 'admin', '2023-03-07 17:31:27.759', 'admin', '2023-04-11 17:17:17.908', '菜单', '1');
INSERT INTO "public"."sys_dict_type" VALUES (3, '系统开关', 'sys_normal_disable', '0', 'admin', '2022-07-05 08:12:40', '', NULL, '系统开关列表', '0');
INSERT INTO "public"."sys_dict_type" VALUES (4, '任务状态', 'sys_job_status', '0', 'admin', '2022-07-05 08:12:40', '', NULL, '任务状态列表', '0');
INSERT INTO "public"."sys_dict_type" VALUES (5, '任务分组', 'sys_job_group', '0', 'admin', '2022-07-05 08:12:40', '', NULL, '任务分组列表', '0');
INSERT INTO "public"."sys_dict_type" VALUES (6, '系统是否', 'sys_yes_no', '0', 'admin', '2022-07-05 08:12:40', '', NULL, '系统是否列表', '0');
INSERT INTO "public"."sys_dict_type" VALUES (7, '通知类型', 'sys_notice_type', '0', 'admin', '2022-07-05 08:12:40', '', NULL, '通知类型列表', '0');
INSERT INTO "public"."sys_dict_type" VALUES (8, '通知状态', 'sys_notice_status', '0', 'admin', '2022-07-05 08:12:40', '', NULL, '通知状态列表', '0');
INSERT INTO "public"."sys_dict_type" VALUES (9, '操作类型', 'sys_oper_type', '0', 'admin', '2022-07-05 08:12:40', '', NULL, '操作类型列表', '0');
INSERT INTO "public"."sys_dict_type" VALUES (10, 'sys_common_status', 'sys_common_status', '0', 'admin', '2022-07-05 08:12:40', 'admin', '2022-07-13 06:47:08', '登录状态列表', '0');
INSERT INTO "public"."sys_dict_type" VALUES (2, 'sys_show_hide', 'sys_show_hides', '0', 'admin', '2022-07-05 08:12:40', 'admin', '2022-08-25 10:16:02.589', '菜单状态列表', '0');
INSERT INTO "public"."sys_dict_type" VALUES (25, '流程状态', 'wf_process_status', '0', 'admin', '2023-05-24 10:16:00.29474', '', NULL, '工作流程状态', '1');
INSERT INTO "public"."sys_dict_type" VALUES (1, 'sys_user_sex', 'sys_user_sex', '0', 'admin', '2022-07-05 08:12:40', 'admin', '2023-03-13 18:28:46.167', '用户性别列表', '0');
INSERT INTO "public"."sys_dict_type" VALUES (11, '数据权限范围', 'sys_data_scope', '0', 'admin', '2023-03-15 15:13:13.625466', NULL, NULL, '数据权限范围列表', '0');
INSERT INTO "public"."sys_dict_type" VALUES (12, '权限开启开关', 'sys_power_switch', '1', 'admin', '2023-03-17 09:57:23.08', 'admin', '2023-10-09 08:56:06.41', '是否开启全局权限，0开启，1关闭', '0');

-- ----------------------------
-- Table structure for sys_logininfor
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_logininfor";
CREATE TABLE "public"."sys_logininfor" (
  "info_id" int8 NOT NULL,
  "user_name" varchar(50) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "ipaddr" varchar(128) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "login_location" varchar(255) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "browser" varchar(50) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "os" varchar(50) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "status" char(1) COLLATE "pg_catalog"."default" DEFAULT '0'::bpchar,
  "msg" varchar(255) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "login_time" timestamp(6)
)
;
COMMENT ON COLUMN "public"."sys_logininfor"."info_id" IS '访问ID';
COMMENT ON COLUMN "public"."sys_logininfor"."user_name" IS '用户账号';
COMMENT ON COLUMN "public"."sys_logininfor"."ipaddr" IS '登录IP地址';
COMMENT ON COLUMN "public"."sys_logininfor"."login_location" IS '登录地点';
COMMENT ON COLUMN "public"."sys_logininfor"."browser" IS '浏览器类型';
COMMENT ON COLUMN "public"."sys_logininfor"."os" IS '操作系统';
COMMENT ON COLUMN "public"."sys_logininfor"."status" IS '登录状态（0成功 1失败）';
COMMENT ON COLUMN "public"."sys_logininfor"."msg" IS '提示消息';
COMMENT ON COLUMN "public"."sys_logininfor"."login_time" IS '访问时间';
COMMENT ON TABLE "public"."sys_logininfor" IS '系统访问记录';

-- ----------------------------
-- Records of sys_logininfor
-- ----------------------------
INSERT INTO "public"."sys_logininfor" VALUES (1852272152524972034, 'admin', '************', 'XX XX', 'Chrome', 'Windows 10 or Windows Server 2016', '0', '登录成功', '2024-11-01 16:51:09.368');
INSERT INTO "public"."sys_logininfor" VALUES (1852272166953377793, 'admin', '************', 'XX XX', 'Chrome', 'OSX', '1', '密码输入错误1次', '2024-11-01 16:51:12.811');
INSERT INTO "public"."sys_logininfor" VALUES (1852272198909779969, 'admin', '************', 'XX XX', 'Chrome', 'OSX', '0', '登录成功', '2024-11-01 16:51:20.43');
INSERT INTO "public"."sys_logininfor" VALUES (1852272293243871234, 'admin', '************', 'XX XX', 'Chrome', 'Windows 10 or Windows Server 2016', '0', '登录成功', '2024-11-01 16:51:42.921');
INSERT INTO "public"."sys_logininfor" VALUES (1852273002072858625, 'admin', '************', 'XX XX', 'Chrome', 'Windows 10 or Windows Server 2016', '0', '登录成功', '2024-11-01 16:54:31.919');
INSERT INTO "public"."sys_logininfor" VALUES (1852273397176295426, 'admin', '************', 'XX XX', 'Chrome', 'Windows 10 or Windows Server 2016', '0', '登录成功', '2024-11-01 16:56:06.117');
INSERT INTO "public"."sys_logininfor" VALUES (1852273606451093505, 'admin', '************', 'XX XX', 'Chrome', 'Windows 10 or Windows Server 2016', '0', '登录成功', '2024-11-01 16:56:56.014');
INSERT INTO "public"."sys_logininfor" VALUES (1852274399992438785, 'admin', '************', 'XX XX', 'Chrome', 'OSX', '0', '登录成功', '2024-11-01 17:00:05.209');
INSERT INTO "public"."sys_logininfor" VALUES (1853235961574023169, 'admin', '************', 'XX XX', 'Chrome', 'Windows 10 or Windows Server 2016', '0', '登录成功', '2024-11-04 08:40:59.355');
INSERT INTO "public"."sys_logininfor" VALUES (1853237661798064130, 'admin', '************', 'XX XX', 'Chrome', 'Windows 10 or Windows Server 2016', '0', '退出成功', '2024-11-04 08:47:44.72');
INSERT INTO "public"."sys_logininfor" VALUES (1853237680806649858, 'admin', '************', 'XX XX', 'Chrome', 'Windows 10 or Windows Server 2016', '0', '登录成功', '2024-11-04 08:47:49.252');
INSERT INTO "public"."sys_logininfor" VALUES (1853252984537980929, 'admin', '127.0.0.1', '内网IP', 'Chrome', 'Windows 10 or Windows Server 2016', '0', '登录成功', '2024-11-04 09:48:37.944');
INSERT INTO "public"."sys_logininfor" VALUES (1853258009750401025, 'admin', '127.0.0.1', '内网IP', 'Chrome', 'Windows 10 or Windows Server 2016', '0', '退出成功', '2024-11-04 10:08:36.046');
INSERT INTO "public"."sys_logininfor" VALUES (1853258068630040577, 'admin', '127.0.0.1', '内网IP', 'Chrome', 'Windows 10 or Windows Server 2016', '0', '登录成功', '2024-11-04 10:08:50.089');
INSERT INTO "public"."sys_logininfor" VALUES (1853265393017286658, 'admin', '127.0.0.1', '内网IP', 'Chrome', 'Windows 10 or Windows Server 2016', '0', '退出成功', '2024-11-04 10:37:56.355');
INSERT INTO "public"."sys_logininfor" VALUES (1853265440912044033, 'admin', '127.0.0.1', '内网IP', 'Chrome', 'Windows 10 or Windows Server 2016', '0', '登录成功', '2024-11-04 10:38:07.791');
INSERT INTO "public"."sys_logininfor" VALUES (1853264219426349057, 'admin', '************', 'XX XX', 'Chrome', 'Windows 10 or Windows Server 2016', '0', '登录成功', '2024-11-04 10:33:16.548');
INSERT INTO "public"."sys_logininfor" VALUES (1853264834034491393, 'admin', '*************', 'XX XX', 'Chrome', 'OSX', '0', '登录成功', '2024-11-04 10:35:43.087');
INSERT INTO "public"."sys_logininfor" VALUES (1853269042624290818, 'admin', '************', 'XX XX', 'Chrome', 'Windows 10 or Windows Server 2016', '0', '退出成功', '2024-11-04 10:52:26.492');
INSERT INTO "public"."sys_logininfor" VALUES (1853269053265240065, 'admin', '************', 'XX XX', 'Chrome', 'Windows 10 or Windows Server 2016', '0', '登录成功', '2024-11-04 10:52:29.03');
INSERT INTO "public"."sys_logininfor" VALUES (1853269711712247809, 'admin', '************', 'XX XX', 'Chrome', 'Windows 10 or Windows Server 2016', '0', '退出成功', '2024-11-04 10:55:06.015');
INSERT INTO "public"."sys_logininfor" VALUES (1853269721413672962, 'admin', '************', 'XX XX', 'Chrome', 'Windows 10 or Windows Server 2016', '0', '登录成功', '2024-11-04 10:55:08.329');
INSERT INTO "public"."sys_logininfor" VALUES (1853270490103767042, 'admin', '*************', 'XX XX', 'Chrome', 'OSX', '0', '登录成功', '2024-11-04 10:58:11.598');
INSERT INTO "public"."sys_logininfor" VALUES (1853270937501786113, 'admin', '*************', 'XX XX', 'Chrome', 'OSX', '0', '退出成功', '2024-11-04 10:59:58.266');
INSERT INTO "public"."sys_logininfor" VALUES (1853271017814319106, 'admin', '*************', 'XX XX', 'Chrome', 'OSX', '0', '登录成功', '2024-11-04 11:00:17.415');

-- ----------------------------
-- Table structure for sys_menu
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_menu";
CREATE TABLE "public"."sys_menu" (
  "menu_id" int8 NOT NULL,
  "menu_name" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "parent_id" int8,
  "order_num" int4,
  "path" varchar(200) COLLATE "pg_catalog"."default",
  "component" varchar(255) COLLATE "pg_catalog"."default",
  "query" varchar(255) COLLATE "pg_catalog"."default",
  "is_frame" varchar(10) COLLATE "pg_catalog"."default" DEFAULT 1,
  "is_cache" int4 DEFAULT 0,
  "menu_type" char(1) COLLATE "pg_catalog"."default",
  "visible" char(1) COLLATE "pg_catalog"."default",
  "status" char(1) COLLATE "pg_catalog"."default",
  "perms" varchar(100) COLLATE "pg_catalog"."default",
  "icon" varchar(100) COLLATE "pg_catalog"."default",
  "create_by" varchar(64) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "update_by" varchar(64) COLLATE "pg_catalog"."default",
  "update_time" timestamp(6),
  "remark" varchar(500) COLLATE "pg_catalog"."default",
  "query_param" varchar(255) COLLATE "pg_catalog"."default",
  "page_name" varchar(50) COLLATE "pg_catalog"."default",
  "is_home" int4 DEFAULT 1,
  "app" varchar(100) COLLATE "pg_catalog"."default",
  "app_url" varchar COLLATE "pg_catalog"."default",
  "is_app" int4 DEFAULT 1
)
;
COMMENT ON COLUMN "public"."sys_menu"."menu_id" IS '菜单ID';
COMMENT ON COLUMN "public"."sys_menu"."menu_name" IS '菜单名称';
COMMENT ON COLUMN "public"."sys_menu"."parent_id" IS '父菜单ID';
COMMENT ON COLUMN "public"."sys_menu"."order_num" IS '显示顺序';
COMMENT ON COLUMN "public"."sys_menu"."path" IS '路由地址';
COMMENT ON COLUMN "public"."sys_menu"."component" IS '组件路径';
COMMENT ON COLUMN "public"."sys_menu"."query" IS '路由参数';
COMMENT ON COLUMN "public"."sys_menu"."is_frame" IS '是否为外链（0是 1否）';
COMMENT ON COLUMN "public"."sys_menu"."is_cache" IS '是否缓存（0缓存 1不缓存）';
COMMENT ON COLUMN "public"."sys_menu"."menu_type" IS '菜单类型（M目录 C菜单 F按钮）';
COMMENT ON COLUMN "public"."sys_menu"."visible" IS '菜单状态（0显示 1隐藏）';
COMMENT ON COLUMN "public"."sys_menu"."status" IS '菜单状态（0正常 1停用）';
COMMENT ON COLUMN "public"."sys_menu"."perms" IS '权限标识';
COMMENT ON COLUMN "public"."sys_menu"."icon" IS '菜单图标';
COMMENT ON COLUMN "public"."sys_menu"."create_by" IS '创建者';
COMMENT ON COLUMN "public"."sys_menu"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."sys_menu"."update_by" IS '更新者';
COMMENT ON COLUMN "public"."sys_menu"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."sys_menu"."remark" IS '备注';
COMMENT ON COLUMN "public"."sys_menu"."query_param" IS '路由参数';
COMMENT ON COLUMN "public"."sys_menu"."page_name" IS '页面唯一标识';
COMMENT ON COLUMN "public"."sys_menu"."is_home" IS '门户首页(0是 1否)';
COMMENT ON COLUMN "public"."sys_menu"."app" IS '微应用-所属应用';
COMMENT ON COLUMN "public"."sys_menu"."app_url" IS '微应用-所属应用URL';
COMMENT ON COLUMN "public"."sys_menu"."is_app" IS '是否微应用（0-是 1-否）';
COMMENT ON TABLE "public"."sys_menu" IS '菜单权限表';

-- ----------------------------
-- Records of sys_menu
-- ----------------------------
INSERT INTO "public"."sys_menu" VALUES (1642714259651506178, '云盘管理', 0, 3, '/disk', 'Disk/Index', NULL, '1', 0, 'C', '0', '0', '07cd3b1f-00fe-468d-a785-e25a3b18f4dd', 'Disk', 'admin', '2023-04-03 10:22:54.313', 'admin', '2024-06-04 10:35:50.325', NULL, NULL, 'sys_error_page', 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (2000, '系统管理', 0, 98, '/system', NULL, NULL, '1', 0, 'M', '0', '0', 'system', 'ApplicationFull', 'admin', '2022-07-05 09:25:44', 'admin', '2024-04-07 17:58:12.786', '', NULL, 'System', 1, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637628530521145346, '门户首页', 0, -1, '/base/home', 'Base/Home/Index', NULL, '1', 0, 'C', '0', '0', 'e82f3632-7d69-4815-a185-f558a07f95f0', 'HomeFull', 'admin', '2023-03-20 09:34:02', 'admin', '2024-06-13 16:31:02.889', NULL, NULL, 'home', 0, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1641689049821294593, '日志管理', 0, 999, '/system/log', NULL, NULL, '1', 0, 'M', '0', '0', '3c4263e9-3803-4cd5-a3fa-8fdf58914553', 'CalendarFull', 'admin', '2023-03-31 14:29:05.242', 'admin', '2024-10-08 16:55:57.105', NULL, NULL, 'LogManagement', 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (2001, '基础数据', 2000, 0, '/system/base', NULL, NULL, '1', 0, 'M', '1', '1', 'system:base', 'RichText_AlignJustify', 'admin', '2022-07-05 09:30:34', 'admin', '2023-03-31 14:51:09.85', '', NULL, 'Base', 1, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (2005, '系统配置', 2000, 1, '/system/setting', NULL, NULL, '1', 0, 'M', '1', '1', 'system:setting', 'SecondMenuDark', 'admin', '2022-07-05 09:34:33', 'admin', '2023-04-27 10:39:10.407', '', NULL, 'Setting', 1, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (2035, '短信管理', 2000, 5, '/system/sms', NULL, NULL, '1', 0, 'M', '0', '0', 'system:sms', 'SecondMemuIcon', 'admin', '2022-07-26 01:53:30', 'admin', '2022-12-20 14:14:11.067', '', NULL, 'Sms', 1, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (2022, '系统日志', 1641689049821294593, 0, '/system/log', NULL, NULL, '1', 0, 'M', '0', '0', 'system:log', 'SecondMemuIcon', 'admin', '2022-07-15 02:46:55', 'admin', '2023-03-31 14:33:42.234', '', NULL, 'Log', 1, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1553982200411803650, 'API日志', 1641689049821294593, 1, '/system/api', '', NULL, '1', 0, 'M', '0', '0', 'system:api', '', 'admin', '2022-08-01 13:53:22.829', 'admin', '2023-03-31 14:33:47.164', NULL, NULL, 'Api', 1, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1641692769984393217, '消息管理', 2000, 3, '/system/notice', NULL, NULL, '1', 0, 'M', '0', '0', '0b3efd92-bce2-42fd-a634-1e53dcebaa19', 'SecondMemuIcon', 'admin', '2023-03-31 14:43:52.197', 'admin', '2023-04-12 18:07:00.966', NULL, NULL, 'NoticeManagement', 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1641692929208561665, '配置管理', 2000, 4, '/system/configuration', NULL, NULL, '1', 0, 'M', '0', '0', '46570535-6df1-41bc-b99c-1cc2c0ba1f70', 'SecondMemuIcon', 'admin', '2023-03-31 14:44:30.16', 'admin', '2023-03-31 14:44:30.16', NULL, NULL, 'Configuration', 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1641692189039734786, '组织管理', 2000, 0, '/system/organization', NULL, NULL, '1', 0, 'M', '0', '0', '01ec0f6a-5521-4c4c-8d31-756b56aff316', 'SecondMemuIcon', 'admin', '2023-03-31 14:41:33.69', 'admin', '2023-04-12 18:26:27.872', NULL, NULL, 'Organization', 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1641692330140315649, '用户管理', 2000, 1, '/system/user', NULL, NULL, '1', 0, 'M', '0', '0', '03aa286c-5f21-432e-895c-6f21f9eba41e', 'SecondMemuIcon', 'admin', '2023-03-31 14:42:07.331', 'admin', '2023-04-12 18:06:43.185', NULL, NULL, 'UserManagement', 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1641692471614189570, '权限管理', 2000, 2, '/system/authority', NULL, NULL, '1', 0, 'M', '0', '0', '28865568-ef87-4869-958f-8104cd0189d2', 'SecondMemuIcon', 'admin', '2023-03-31 14:42:41.061', 'admin', '2023-03-31 14:42:41.061', NULL, NULL, 'Authority', 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1554292052300374018, '错误日志', 1553982200411803650, 1, '/system/api/error_log', 'System/ApiLog/ApiErrorLog/Index', NULL, '1', 0, 'C', '0', '0', 'system:api:error_log', 'Dot', 'admin', '2022-08-02 10:24:37.276', 'test', '2023-04-27 14:24:32.164', NULL, NULL, 'Sys_error_log', 1, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (2006, '菜单管理', 1641692471614189570, 0, '/system/authority/menu', 'System/Menu/Index', NULL, '1', 0, 'C', '0', '0', 'system:setting:menu', 'Dot', 'admin', '2022-07-05 09:35:08', 'admin', '2023-03-31 15:38:57.25', '', NULL, 'Menu', 1, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (2003, '部门管理', 1641692189039734786, 1, '/system/base/department', 'System/Department/Index', NULL, '1', 0, 'C', '0', '0', 'system:base:department', 'Dot', 'admin', '2022-07-05 09:33:10', 'admin', '2023-03-31 14:46:43.051', '', NULL, 'Department', 1, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1638447982306467842, '消息模板', 1641692769984393217, 0, '/system/base/notice_template', 'System/NoticeTemplate/Index', NULL, '1', 0, 'C', '0', '0', 'c0442612-0e0b-4976-8286-70b334909780', 'Dot', 'admin', '2023-03-22 15:50:14.531', 'admin', '2023-04-13 11:39:21.733', NULL, NULL, 'noticeTemplate', 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1553982743687417858, '访问日志', 1553982200411803650, 0, '/system/api/access_log', 'System/ApiLog/AccessLog/Index', NULL, '1', 0, 'C', '0', '0', 'system:api:access_log', 'Dot', 'admin', '2022-08-01 13:55:32.355', 'admin', '2022-12-20 14:14:22.069', NULL, NULL, 'Access_Log', 1, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1559483045800763393, '角色管理', 1641692471614189570, 2, '/system/base/role', 'System/Role/Index', NULL, '1', 0, 'C', '0', '0', 'system:base:role', 'Dot', 'admin', '2022-08-16 10:11:46.57', 'admin', '2023-06-06 20:57:11.133', NULL, NULL, 'Role', 1, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (2007, '用户管理', 1641692330140315649, 0, '/system/setting/user', 'System/User/Index', NULL, '1', 0, 'C', '0', '0', 'system:setting:user', 'Dot', 'admin', '2022-07-05 09:37:57', 'admin', '2023-04-19 13:43:02.519', '', NULL, 'User', 1, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (2019, '通知公告', 1641692769984393217, 1, '/system/base/notice', 'System/Notice/Index', NULL, '1', 0, 'C', '0', '0', 'system:base:notice', 'Dot', 'admin', '2022-07-14 05:44:38', 'admin', '2023-04-12 17:39:19.661', '', NULL, 'Notice', 1, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (2002, '岗位管理', 1641692189039734786, 0, '/system/base/position', 'System/Position/Index', NULL, '1', 0, 'C', '0', '0', 'system:base:position', 'Dot', 'admin', '2022-07-05 09:32:16', 'admin', '2023-04-18 10:12:53.11', '', NULL, 'Position', 1, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1552896263871139842, '短信日志', 2035, 2, '/system/sms/sms_log', 'System/Sms/SmsLog/Index', NULL, '1', 0, 'C', '0', '0', 'system:sms:sms_log', 'Dot', 'admin', '2022-07-29 13:58:15.382', 'admin', '2023-03-14 12:11:56.837', NULL, NULL, 'Sms_Log', 1, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (2017, '参数设置', 1641692929208561665, 4, '/system/base/parameter', 'System/Parameter/lndex', NULL, '1', 0, 'C', '0', '0', 'System:Parameter:lndex', 'Dot', 'admin', '2022-07-13 09:30:06', 'admin', '2023-03-31 14:48:11.487', '', NULL, 'Parameter', 1, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (2037, '短信模版', 2035, 1, '/system/sms/sms_template', 'System/Sms/SmsTemplate/lndex', NULL, '1', 0, 'C', '0', '0', 'system:sms:sms_template', 'Dot', 'admin', '2022-07-26 05:57:48', 'admin', '2022-08-10 18:04:46.721', '', NULL, 'Sms_Template', 1, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (2024, '登录日志', 2022, 2, '/system/log/logininfor', 'System/Logininfor/lndex', NULL, '1', 0, 'C', '0', '0', 'system:log:logininfor', 'Dot', 'admin', '2022-07-18 02:01:44', 'admin', '2022-08-10 17:58:23.845', '', NULL, 'Logininfor', 1, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (2023, '操作日志', 2022, 0, '/system/log/operlog', 'System/Operlog/Index', NULL, '1', 0, 'C', '0', '0', 'system:log:operlog', 'Dot', 'admin', '2022-07-15 02:48:01', 'admin', '2022-08-18 09:48:53.49', '', NULL, 'Operlog', 1, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (2009, '字典管理', 1641692929208561665, 0, '/system/base/dict', 'System/Dict/Index', NULL, '1', 0, 'C', '0', '0', 'system:base:dict', 'Dot', 'admin', '2022-07-12 07:11:38', 'admin', '2023-07-10 16:43:47.339', '', NULL, 'Dict', 1, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1579761925094563842, '字典数据', 1641692929208561665, 0, '/system/base/dict/data', 'System/DictData/Index', NULL, '1', 0, 'C', '0', '0', 'system:base:dict:data', 'Dot', 'admin', '2022-10-11 17:12:48.157', 'admin', '2023-03-31 14:48:23.893', NULL, NULL, 'DictData', 1, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (20196, '获取通知公告列表', 2019, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:notice:index:list', NULL, 'admin', '2023-04-26 10:08:53.586734', 'admin', '2023-04-26 13:42:50.238', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637698328852561923, '导出操作日志 Excel', 2023, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:apilog:operlog:index:export', NULL, 'admin', '2023-03-20 14:11:23.22', 'admin', '2023-03-20 14:11:23.22', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637696209328144385, '更新错误日志的状态', 1554292052300374018, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:error:log:index:update', NULL, 'admin', '2023-03-20 14:02:57.885', 'admin', '2023-03-20 14:02:57.885', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637698328852561922, '查询操作日志', 2023, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:apilog:operlog:index:query', NULL, 'admin', '2023-03-20 14:11:23.219', 'admin', '2023-03-20 14:11:23.219', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637696209332338691, '导出错误日志', 1554292052300374018, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:error:log:index:export', NULL, 'admin', '2023-03-20 14:02:57.887', 'admin', '2023-03-20 14:02:57.887', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (163769620933233862, '错误日志列表', 1554292052300374018, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:error:log:index:list', NULL, 'admin', '2023-04-26 09:42:02.672713', 'admin', '2023-04-26 09:42:02.672713', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1646093671929401346, '添加岗位', 2002, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:position:index:add', NULL, 'admin', '2023-04-12 18:11:28.986', 'admin', '2023-04-12 18:28:13.747', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637698328848367618, '删除操作日志', 2023, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:apilog:operlog:index:delete', NULL, 'admin', '2023-03-20 14:11:23.218', 'admin', '2023-03-20 14:11:23.218', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1639144133907480578, '查询消息模版', 1638447982306467842, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:notice-template:query', NULL, 'admin', '2023-03-24 13:56:30.006', 'admin', '2023-04-12 18:32:30.748', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637696209332338690, '查询错误日志', 1554292052300374018, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:error:log:index:query', NULL, 'admin', '2023-03-20 14:02:57.886', 'admin', '2023-03-20 14:02:57.886', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1639144133911674882, '新增模版', 1638447982306467842, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:notice-template:add', NULL, 'admin', '2023-03-24 13:56:30.006', 'admin', '2023-04-12 18:32:30.755', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1639144133911674883, '修改模版', 1638447982306467842, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:notice-template:edit', NULL, 'admin', '2023-03-24 13:56:30.006', 'admin', '2023-04-12 18:32:30.763', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1639144133911674884, '删除模版', 1638447982306467842, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:notice-template:remove', NULL, 'admin', '2023-03-24 13:56:30.007', 'admin', '2023-04-12 18:32:30.77', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637699959157555205, '菜单列表', 2006, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:menu:index:list', NULL, 'admin', '2023-04-26 09:47:37.479919', 'admin', '2023-04-26 09:47:37.479919', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (20191, '新增通知公告', 2019, 5, '/system/base/notice/add', 'System/Notice/lndex/add', NULL, '1', 0, 'F', '0', '0', 'system:notice:index:add', 'Dot', 'admin', '2023-03-15 18:07:36.193', 'admin', '2023-04-26 13:42:50.182', '', NULL, '', NULL, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (20195, '导出通知公告', 2019, 5, '/system/base/notice/export', 'System/Notice/lndex/export', NULL, '1', 0, 'F', '0', '0', 'system:notice:index:export', 'Dot', 'admin', '2023-03-15 18:07:36.193', 'admin', '2023-04-26 13:42:50.196', '', NULL, '', NULL, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (20193, '删除通知公告', 2019, 5, '/system/base/notice/delete', 'System/Notice/lndex/delete', NULL, '1', 0, 'F', '0', '0', 'system:notice:index:delete', 'Dot', 'admin', '2023-03-15 18:07:36.193', 'admin', '2023-04-26 13:42:50.207', '', NULL, '', NULL, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (20194, '查询通知公告', 2019, 5, '/system/base/notice/query', 'System/Notice/lndex/query', NULL, '1', 0, 'F', '0', '0', 'system:notice:index:query', 'Dot', 'admin', '2023-03-15 18:07:36.193', 'admin', '2023-04-26 13:42:50.229', '', NULL, '', NULL, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637701165309362178, '添加角色', 1559483045800763393, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:role:index:add', NULL, 'admin', '2023-03-20 14:22:39.483', 'admin', '2023-03-20 14:22:39.483', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1646462916275777538, '消息发送', 2019, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:notice:index:send', NULL, 'admin', '2023-04-13 18:38:43.698', 'admin', '2023-04-26 13:42:50.218', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637695551556419586, '获取访问日志列表', 1553982743687417858, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:apiinfor:lndex:list', NULL, 'admin', '2023-04-26 10:03:59.264528', 'admin', '2023-04-26 10:03:59.264528', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637701165313556482, '修改角色/菜单权限/数据权限', 1559483045800763393, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:role:index:update', NULL, 'admin', '2023-03-20 14:22:39.484', 'admin', '2023-03-20 14:22:39.484', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1643854527852621825, '查询机器人配置', 1641698767885250561, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:robot-config:query', NULL, 'admin', '2023-04-06 13:53:55.442', 'admin', '2023-04-26 13:44:15.766', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1643854527852621826, '新增机器人配置', 1641698767885250561, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:robot-config:add', NULL, 'admin', '2023-04-06 13:53:55.444', 'admin', '2023-04-26 13:44:15.777', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637739617765687299, '部门列表', 2003, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:department:index:list', NULL, 'admin', '2023-04-26 09:55:46.629954', 'admin', '2023-04-26 09:55:46.629954', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1643854527852621827, '删除机器人配置', 1641698767885250561, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:robot-config:delete', NULL, 'admin', '2023-04-06 13:53:55.444', 'admin', '2023-04-26 13:44:15.786', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1646097108993757186, '岗位列表', 2002, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:position:index:list', NULL, 'admin', '2023-04-26 10:11:06.97385', 'admin', '2023-04-26 10:11:06.97385', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637701165317750787, '角色列表', 1559483045800763393, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:role:index:list', NULL, 'admin', '2023-04-26 10:13:04.511567', 'admin', '2023-04-26 10:13:04.511567', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1643854527852621828, '修改机器人配置', 1641698767885250561, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:robot-config:edit', NULL, 'admin', '2023-04-06 13:53:55.445', 'admin', '2023-04-26 13:44:15.795', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1643854527852621829, '导出机器人配置', 1641698767885250561, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:robot-config:export', NULL, 'admin', '2023-04-06 13:53:55.445', 'admin', '2023-04-26 13:44:15.804', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637692724117372931, '获取用户列表', 2007, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:user:index:list', NULL, 'admin', '2023-04-26 10:07:13.472002', 'admin', '2023-04-26 10:07:13.472002', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637701165313556483, '删除角色', 1559483045800763393, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:role:index:delete', NULL, 'admin', '2023-03-20 14:22:39.485', 'admin', '2023-03-20 14:22:39.485', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637701165313556484, '查询角色列表', 1559483045800763393, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:role:index:query', NULL, 'admin', '2023-03-20 14:22:39.485', 'admin', '2023-03-20 14:22:39.485', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637701165317750786, '导出角色列表', 1559483045800763393, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:role:index:export', NULL, 'admin', '2023-03-20 14:22:39.485', 'admin', '2023-03-20 14:22:39.485', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637694984398438401, '添加短信日志', 1552896263871139842, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:sms:smslog:index:add', NULL, 'admin', '2023-03-20 13:58:05.84', 'admin', '2023-03-20 13:58:05.84', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637694984402632705, '修改短信日志', 1552896263871139842, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:sms:smslog:index:update', NULL, 'admin', '2023-03-20 13:58:05.841', 'admin', '2023-03-20 13:58:05.841', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637694984406827010, '查询短信日志', 1552896263871139842, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:sms:smslog:index:query', NULL, 'admin', '2023-03-20 13:58:05.841', 'admin', '2023-03-20 13:58:05.841', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637694984402632706, '删除短信日志', 1552896263871139842, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:sms:smslog:index:delete', NULL, 'admin', '2023-03-20 13:58:05.841', 'admin', '2023-03-20 13:58:05.841', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1639144133899091970, '获取模版列表', 1638447982306467842, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:notice-template:list', NULL, 'admin', '2023-03-24 13:56:30.003', 'admin', '2023-04-12 18:32:30.741', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (20241, '新增登录日志', 2024, 2, '/system/log/logininfor/add', 'System/Logininfor/lndex/add', NULL, '1', 0, 'F', '0', '0', 'system:logininfor:lndex:add', 'Dot', 'admin', '2023-03-15 18:07:36.467', 'admin', '2023-04-26 13:54:28.795', '', NULL, '', 1, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (20242, '修改登录日志', 2024, 2, '/system/log/logininfor/update', 'System/Logininfor/lndex/update', NULL, '1', 0, 'F', '0', '0', 'system:logininfor:lndex:update', 'Dot', 'admin', '2023-03-15 18:07:36.467', 'admin', '2023-04-26 13:54:28.805', '', NULL, '', 1, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (20243, '删除登录日志', 2024, 2, '/system/log/logininfor/delete', 'System/Logininfor/lndex/delete', NULL, '1', 0, 'F', '0', '0', 'system:logininfor:lndex:delete', 'Dot', 'admin', '2023-03-15 18:07:36.467', 'admin', '2023-04-26 13:54:28.814', '', NULL, '', 1, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (20244, '查询登录日志', 2024, 2, '/system/log/logininfor/query', 'System/Logininfor/lndex/query', NULL, '1', 0, 'F', '0', '0', 'system:logininfor:lndex:query', 'Dot', 'admin', '2023-03-15 18:07:36.467', 'admin', '2023-04-26 13:54:28.823', '', NULL, '', 1, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (20245, '导出登录日志', 2024, 2, '/system/log/logininfor/export', 'System/Logininfor/lndex/export', NULL, '1', 0, 'F', '0', '0', 'system:logininfor:lndex:export', 'Dot', 'admin', '2023-03-15 18:07:36.467', 'admin', '2023-04-26 13:54:28.833', '', NULL, '', 1, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637740180549009409, '添加参数设置', 2017, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:parameter:index:add', NULL, 'admin', '2023-03-20 16:57:41.442', 'admin', '2023-03-20 16:57:41.442', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637740180553203714, '修改参数设置', 2017, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:parameter:index:update', NULL, 'admin', '2023-03-20 16:57:41.443', 'admin', '2023-03-20 16:57:41.443', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637740180557398017, '删除参数设置', 2017, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:parameter:index:delete', NULL, 'admin', '2023-03-20 16:57:41.443', 'admin', '2023-03-20 16:57:41.443', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637740180557398018, '查询参数设置', 2017, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:parameter:index:query', NULL, 'admin', '2023-03-20 16:57:41.443', 'admin', '2023-03-20 16:57:41.443', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637740180557398019, '导出参数设置', 2017, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:parameter:index:export', NULL, 'admin', '2023-03-20 16:57:41.444', 'admin', '2023-03-20 16:57:41.444', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637694984406827011, '导出短信日志', 1552896263871139842, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:sms:smslog:index:export', NULL, 'admin', '2023-03-20 13:58:05.842', 'admin', '2023-03-20 13:58:05.842', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637699207311785986, '添加短信模版', 2037, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:sms:smstemplate:lndex:add', NULL, 'admin', '2023-03-20 14:14:52.661', 'admin', '2023-03-20 14:14:52.661', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637699207315980289, '修改短信模版', 2037, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:sms:smstemplate:lndex:update', NULL, 'admin', '2023-03-20 14:14:52.662', 'admin', '2023-03-20 14:14:52.662', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637699207315980290, '删除短信模版', 2037, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:sms:smstemplate:lndex:delete', NULL, 'admin', '2023-03-20 14:14:52.662', 'admin', '2023-03-20 14:14:52.662', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637699207320174595, '导出短信模版', 2037, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:sms:smstemplate:lndex:export', NULL, 'admin', '2023-03-20 14:14:52.663', 'admin', '2023-03-20 14:14:52.663', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637699207320174596, '发送短信', 2037, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:sms:smslog:index:send', NULL, 'admin', '2023-03-20 14:14:52.663', 'admin', '2023-03-20 14:14:52.663', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637699959149166593, '新增菜单', 2006, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:menu:index:add', NULL, 'admin', '2023-03-20 14:17:51.913', 'admin', '2023-03-20 14:17:51.913', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637699959153360897, '修改菜单', 2006, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:menu:index:update', NULL, 'admin', '2023-03-20 14:17:51.914', 'admin', '2023-03-20 14:17:51.914', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637699959153360898, '删除菜单', 2006, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:menu:index:delete', NULL, 'admin', '2023-03-20 14:17:51.914', 'admin', '2023-03-20 14:17:51.914', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637699959157555202, '查询菜单列表', 2006, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:menu:index:query', NULL, 'admin', '2023-03-20 14:17:51.914', 'admin', '2023-03-20 14:17:51.914', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637699959157555203, '导出菜单列表', 2006, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:menu:index:export', NULL, 'admin', '2023-03-20 14:17:51.915', 'admin', '2023-03-20 14:17:51.915', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637699959157555204, '页面权限', 2006, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:menu:index:operation', NULL, 'admin', '2023-03-20 14:17:51.915', 'admin', '2023-03-20 14:17:51.915', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637699959161749506, '设为首页', 2006, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:menu:index:setHome', NULL, 'admin', '2023-03-20 14:17:51.915', 'admin', '2023-03-20 14:17:51.915', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1646096690096033793, '删除岗位', 2002, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:position:index:delete', NULL, 'admin', '2023-04-12 18:23:28.574', 'admin', '2023-04-12 18:28:13.755', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637739209894789122, '删除字典数据', 1579761925094563842, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:dictdata:index:delete', NULL, 'admin', '2023-03-20 16:53:50.02', 'admin', '2023-03-22 10:04:19.631', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637739209898983426, '导出字典数据', 1579761925094563842, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:dictdata:index:export', NULL, 'admin', '2023-03-20 16:53:50.021', 'admin', '2023-03-22 10:04:19.648', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637739209890594817, '新增字典数据', 1579761925094563842, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:dictdata:index:add', NULL, 'admin', '2023-03-20 16:53:50.018', 'admin', '2023-03-22 10:04:19.615', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637739209894789121, '修改字典数据', 1579761925094563842, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:dictdata:index:update', NULL, 'admin', '2023-03-20 16:53:50.019', 'admin', '2023-03-22 10:04:19.623', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637695551556419585, '导出访问日志', 1553982743687417858, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:apiinfor:lndex:export', NULL, 'admin', '2023-03-20 14:00:21.061', 'admin', '2023-03-20 14:00:21.061', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637699207320174594, '查询短信模板', 2037, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:sms:smstemplate:lndex:query', NULL, 'admin', '2023-03-20 14:14:52.662', 'admin', '2023-03-20 14:14:52.662', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1646096093397569537, '查询岗位', 2002, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:position:index:query', NULL, 'admin', '2023-04-12 18:21:06.31', 'admin', '2023-04-12 18:28:13.763', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1646096532964823041, '修改岗位 ', 2002, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:position:index:update', NULL, 'admin', '2023-04-12 18:22:51.111', 'admin', '2023-04-12 18:28:13.77', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637739209898983425, '查询字典数据', 1579761925094563842, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:dictdata:index:query', NULL, 'admin', '2023-03-20 16:53:50.02', 'admin', '2023-03-22 10:04:19.639', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637695551552225281, '查询访问日志', 1553982743687417858, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:apiinfor:lndex:query', NULL, 'admin', '2023-03-20 14:00:21.06', 'admin', '2023-03-20 14:00:21.06', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637694984402632707, '短信日志列表', 1552896263871139842, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:sms:smslog:index:list', NULL, 'admin', '2023-04-26 10:15:06.291584', 'admin', '2023-04-26 10:15:06.291584', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637692724108984322, '添加用户', 2007, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:user:index:add', NULL, 'admin', '2023-03-20 13:49:06.944', 'admin', '2023-03-20 13:49:06.944', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637692724108984323, '修改用户', 2007, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:user:index:update', NULL, 'admin', '2023-03-20 13:49:06.945', 'admin', '2023-03-20 13:49:06.945', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637692724113178626, '删除用户', 2007, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:user:index:delete', NULL, 'admin', '2023-03-20 13:49:06.946', 'admin', '2023-03-20 13:49:06.946', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637739938336342020, '获取字典列表', 2009, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:dict:index:list', NULL, 'admin', '2023-04-26 10:25:33.375767', 'admin', '2023-04-26 10:25:33.375767', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637692724117372929, '导出用户列表', 2007, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:user:index:export', NULL, 'admin', '2023-03-20 13:49:06.947', 'admin', '2023-03-20 13:49:06.947', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637692724117372930, '导入用户列表', 2007, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:user:index:import', NULL, 'admin', '2023-03-20 13:49:06.947', 'admin', '2023-03-20 13:49:06.947', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637692724121567233, '重置密码', 2007, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:user:index:resetPwd', NULL, 'admin', '2023-03-20 13:49:06.948', 'admin', '2023-03-20 13:49:06.948', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (20192, '修改通知公告', 2019, 5, '/system/base/notice/update', 'System/Notice/lndex/update', NULL, '1', 0, 'F', '0', '0', 'system:notice:index:update', 'Dot', 'admin', '2023-03-15 18:07:36.193', 'admin', '2023-04-26 13:42:50.249', '', NULL, '', NULL, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637740180557398020, '获取参数列表', 2017, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:parameter:index:list', NULL, 'admin', '2023-04-26 10:18:03.434763', 'admin', '2023-04-26 10:18:03.434763', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (20246, '获取登录日志列表', 2024, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:logininfor:lndex:list', NULL, 'admin', '2023-04-26 10:22:26.50131', 'admin', '2023-04-26 13:54:28.842', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637739617761492993, '添加部门', 2003, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:department:index:add', NULL, 'admin', '2023-03-20 16:55:27.262', 'admin', '2023-03-21 13:42:01.54', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637739617765687297, '修改部门', 2003, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:department:index:update', NULL, 'admin', '2023-03-20 16:55:27.263', 'admin', '2023-03-21 13:42:01.549', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637739617765687298, '删除部门', 2003, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:department:index:delete', NULL, 'admin', '2023-03-20 16:55:27.264', 'admin', '2023-03-21 13:42:01.558', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637739617769881601, '查询部门', 2003, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:department:index:query', NULL, 'admin', '2023-03-20 16:55:27.264', 'admin', '2023-03-21 13:42:01.567', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637739617774075906, '导出部门', 2003, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:department:index:export', NULL, 'admin', '2023-03-20 16:55:27.265', 'admin', '2023-03-21 13:42:01.575', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637692724113178627, '查询用户列表', 2007, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:user:index:query', NULL, 'admin', '2023-03-20 13:49:06.946', 'admin', '2023-03-20 13:49:06.946', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637739209898983427, '字典数据列表', 1579761925094563842, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:dict:index:list', NULL, 'admin', '2023-04-26 10:27:17.748019', 'admin', '2023-04-26 10:27:17.748019', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1646097108993757185, '导出岗位', 2002, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:position:index:export', NULL, 'admin', '2023-04-12 18:25:08.447', 'admin', '2023-04-12 18:28:13.778', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1643854527852621830, '获取机器人配置列表', 1641698767885250561, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:robot-config:list', NULL, 'admin', '2023-04-26 10:28:16.889737', 'admin', '2023-04-26 13:44:15.813', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637698328852561924, '获得操作日志列表', 2023, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:apilog:operlog:index:list', NULL, 'admin', '2023-04-26 10:23:32.055164', 'admin', '2023-04-26 10:23:32.055164', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637699207320174597, '短信模版列表', 2037, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:sms:smstemplate:lndex:list', NULL, 'admin', '2023-04-26 10:19:37.323012', 'admin', '2023-04-26 10:19:37.323012', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637739938336342019, '查询字典列表', 2009, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:dict:index:query', NULL, 'admin', '2023-03-20 16:56:43.694', 'admin', '2023-03-20 16:56:43.694', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637739938332147714, '添加字典', 2009, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:dict:index:add', NULL, 'admin', '2023-03-20 16:56:43.692', 'admin', '2023-03-20 16:56:43.692', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637739938332147715, '修改字典', 2009, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:dict:index:update', NULL, 'admin', '2023-03-20 16:56:43.693', 'admin', '2023-03-20 16:56:43.693', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637739938336342018, '删除字典', 2009, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:dict:index:delete', NULL, 'admin', '2023-03-20 16:56:43.693', 'admin', '2023-03-20 16:56:43.693', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637739938340536321, '导出字典列表', 2009, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:dict:index:export', NULL, 'admin', '2023-03-20 16:56:43.694', 'admin', '2023-03-20 16:56:43.694', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1552494335370690562, '文件管理', 1641691307338313730, 0, '/system/file', NULL, NULL, '1', 0, 'M', '0', '0', 'system:file', 'SecondMemuIcon', 'admin', '2022-07-28 11:21:08.159', 'admin', '2023-03-31 14:38:37.204', NULL, NULL, 'File', 1, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1641687530971541505, '监控管理', 2010, 0, '/monitor/monitor_management', NULL, NULL, '1', 0, 'M', '0', '0', '402cbb15-a040-48ec-b151-1ade98fed3a9', 'SecondMemuIcon', 'admin', '2023-03-31 14:23:03.12', 'admin', '2023-03-31 14:23:03.12', NULL, NULL, 'MonitorManagement', 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1641698767885250561, '机器人配置', 1641692769984393217, 3, '/system/notice/robot', 'System/Robot/Index', NULL, '1', 0, 'C', '0', '0', '1f564795-89e4-4f40-8b9c-d243715ff45e', 'Dot', 'admin', '2023-03-31 15:07:42.208', 'admin', '2024-11-04 10:49:44.553', NULL, NULL, 'Robot', 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (2015, '缓存监控', 1641687530971541505, 4, '/system/monitor/cache_monitor', 'System/Monitor/CacheMonitor/Index', NULL, '1', 0, 'C', '0', '0', 'system:monitor:cache_monitor', 'Dot', 'admin', '2022-07-12 08:36:15', 'admin', '2023-03-31 14:24:24.083', '', NULL, 'Cache_Monitor', 1, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (2014, '服务监控', 1641687530971541505, 3, '/system/monitor/servies', 'System/Monitor/Servies/Index', NULL, '1', 0, 'C', '0', '1', 'ttp:::39.108.190.250:19092:admin:applications', 'Dot', 'admin', '2022-07-12 08:35:48', 'admin', '2023-03-31 14:24:30.804', '', NULL, 'MonitorServices', 1, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (2012, '定时任务', 1641687530971541505, 1, '/system/monitor/jobs', 'System/Monitor/Jobs/Index', NULL, '1', 0, 'C', '0', '0', 'ttp:::39.108.190.250:19102:xxl-job-admin:', 'Dot', 'admin', '2022-07-12 08:34:39', 'admin', '2023-03-31 14:23:57.813', '', NULL, 'MonitorJobs', 1, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (2010, '系统监控', 0, 999, '/system/monitor', NULL, NULL, '1', 0, 'M', '0', '0', 'system:monitor', 'MonitorFull', 'admin', '2022-07-12 08:30:54', 'admin', '2024-11-04 10:54:55.634', '', NULL, 'Monitor', 1, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1552594437741445122, '文件配置', 1552494335370690562, 0, '/system/file/config', 'System/File/FileConfig/Index', NULL, '1', 0, 'C', '0', '0', 'system:file:config', 'Dot', 'admin', '2022-07-28 17:58:54.425', 'admin', '2023-03-06 17:47:18.6', NULL, NULL, 'Config', 1, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (2016, '缓存列表', 1641687530971541505, 5, '/system/monitor/cache_list', 'System/Monitor/Cache/lndex', NULL, '1', 0, 'C', '0', '0', 'system:monitor:cache_list', 'Dot', 'admin', '2022-07-12 08:37:08', 'admin', '2023-03-31 14:24:46.313', '', NULL, 'Cache_List', 1, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1552492618272964609, '错误码管理', 1641687530971541505, 7, '/system/monitor/error_code', 'System/Monitor/ErrorCode/Index', NULL, '1', 0, 'C', '0', '0', 'system:monitor:error_code', 'Dot', 'admin', '2022-07-28 11:14:18.772', 'admin', '2023-03-31 14:24:52.061', NULL, NULL, 'Error_Code', 1, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (2011, '在线用户', 1641687530971541505, 0, '/system/monitor/online_user', 'System/Monitor/OnlineUser/Index', NULL, '1', 0, 'C', '0', '0', 'system:monitor:online_user', 'Dot', 'admin', '2022-07-12 08:33:55', 'admin', '2023-03-31 14:23:35.169', '', NULL, 'Online_User', 1, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1554005889752735746, '文件列表', 1552494335370690562, 1, '/system/file/list', 'System/File/FileList/Index', NULL, '1', 0, 'C', '0', '0', 'system:file:list', 'Dot', 'admin', '2022-08-01 15:27:30.808', 'admin', '2022-12-20 14:14:27.733', NULL, NULL, 'List', 1, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637702341643862018, '获取在线用户列表', 2011, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:monitor:onlineuser:index:list', NULL, 'admin', '2023-04-26 10:04:50.072192', 'admin', '2023-04-26 13:46:28.347', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1648976771367776257, '文件列表查询', 1554005889752735746, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:file:filelist:index:query', NULL, 'admin', '2023-04-20 17:07:53.458', 'admin', '2023-04-20 17:07:53.458', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1648976771371970562, '文件下载', 1554005889752735746, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:file:filelist:index:download', NULL, 'admin', '2023-04-20 17:07:53.459', 'admin', '2023-04-20 17:07:53.459', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1648976771371970563, '文件删除', 1554005889752735746, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:file:filelist:index:delete', NULL, 'admin', '2023-04-20 17:07:53.459', 'admin', '2023-04-20 17:07:53.459', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1648976771376164865, '文件上传', 1554005889752735746, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:file:filelist:index:add', NULL, 'admin', '2023-04-20 17:07:53.459', 'admin', '2023-04-20 17:07:53.459', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (20126, '查询定时任务列表', 2012, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:monitor:jobs:index:list', NULL, 'admin', '2023-04-26 09:58:03.367924', 'admin', '2023-04-26 13:48:07.72', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (20146, '查询服务监控列表', 2014, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:monitor:servies:index:list', NULL, 'admin', '2023-04-26 09:53:33.637532', 'admin', '2023-04-26 13:49:14.732', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (20156, '查询缓存监控列表', 2015, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:monitor:cachemonitor:index:list', NULL, 'admin', '2023-04-26 09:50:51.845529', 'admin', '2023-04-26 13:50:23.685', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (20166, '查询缓存列表列表', 2016, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:monitor:cache:lndex:list', NULL, 'admin', '2023-04-26 10:00:37.744723', 'admin', '2023-04-26 13:51:38.228', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (155259447, '获取文件配置列表', 1552594437741445122, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:file:fileconfig:index:list', NULL, 'admin', '2023-04-26 09:59:26.225835', 'admin', '2023-04-26 13:56:07.29', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (155249266, '错误码管理列表', 1552492618272964609, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:monitor:errorcode:index:list', NULL, 'admin', '2023-04-26 10:01:39.34365', 'admin', '2023-04-26 13:58:38.311', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (155259444, '查询文件配置', 1552594437741445122, 0, '/system/file/config/query', 'System/File/FileConfig/Index/query', NULL, '1', 0, 'F', '0', '0', 'system:file:fileconfig:index:query', 'Dot', 'admin', '2023-03-15 18:07:36.404', 'admin', '2023-04-26 13:56:07.323', NULL, NULL, '', 1, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (20141, '新增服务监控', 2014, 3, '/system/monitor/servies/add', 'System/Monitor/Servies/Index/add', NULL, '1', 0, 'F', '0', '0', 'system:monitor:servies:index:add', 'Dot', 'admin', '2023-03-15 18:07:36.513', 'admin', '2023-04-26 13:49:14.742', '', NULL, '', 1, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (20142, '修改服务监控', 2014, 3, '/system/monitor/servies/update', 'System/Monitor/Servies/Index/update', NULL, '1', 0, 'F', '0', '0', 'system:monitor:servies:index:update', 'Dot', 'admin', '2023-03-15 18:07:36.513', 'admin', '2023-04-26 13:49:14.752', '', NULL, '', 1, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (20143, '删除服务监控', 2014, 3, '/system/monitor/servies/delete', 'System/Monitor/Servies/Index/delete', NULL, '1', 0, 'F', '0', '0', 'system:monitor:servies:index:delete', 'Dot', 'admin', '2023-03-15 18:07:36.513', 'admin', '2023-04-26 13:49:14.763', '', NULL, '', 1, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637702341639667715, '查询在线用户', 2011, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:monitor:onlineuser:index:query', NULL, 'admin', '2023-03-20 14:27:19.943', 'admin', '2023-04-26 13:46:28.36', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637702341639667716, '导出在线用户', 2011, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:monitor:onlineuser:index:export', NULL, 'admin', '2023-03-20 14:27:19.943', 'admin', '2023-04-26 13:46:28.372', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637702341643862017, '在线用户登出', 2011, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:monitor:onlineuser:index:kickout', NULL, 'admin', '2023-03-20 14:27:19.943', 'admin', '2023-04-26 13:46:28.383', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (20151, '新增缓存监控', 2015, 4, '/system/monitor/cache_monitor/add', 'System/Monitor/CacheMonitor/Index/add', NULL, '1', 0, 'F', '0', '0', 'system:monitor:cachemonitor:index:add', 'Dot', 'admin', '2023-03-15 18:07:36.513', 'admin', '2023-04-26 13:50:23.695', '', NULL, '', 1, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (20152, '修改缓存监控', 2015, 4, '/system/monitor/cache_monitor/update', 'System/Monitor/CacheMonitor/Index/update', NULL, '1', 0, 'F', '0', '0', 'system:monitor:cachemonitor:index:update', 'Dot', 'admin', '2023-03-15 18:07:36.513', 'admin', '2023-04-26 13:50:23.705', '', NULL, '', 1, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (20153, '删除缓存监控', 2015, 4, '/system/monitor/cache_monitor/delete', 'System/Monitor/CacheMonitor/Index/delete', NULL, '1', 0, 'F', '0', '0', 'system:monitor:cachemonitor:index:delete', 'Dot', 'admin', '2023-03-15 18:07:36.513', 'admin', '2023-04-26 13:50:23.715', '', NULL, '', 1, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (20154, '查询缓存监控', 2015, 4, '/system/monitor/cache_monitor/query', 'System/Monitor/CacheMonitor/Index/query', NULL, '1', 0, 'F', '0', '0', 'system:monitor:cachemonitor:index:query', 'Dot', 'admin', '2023-03-15 18:07:36.513', 'admin', '2023-04-26 13:50:23.725', '', NULL, '', 1, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (155259441, '新增文件配置', 1552594437741445122, 0, '/system/file/config/add', 'System/File/FileConfig/Index/add', NULL, '1', 0, 'F', '0', '0', 'system:file:fileconfig:index:add', 'Dot', 'admin', '2023-03-15 18:07:36.404', 'admin', '2023-04-26 13:56:07.298', NULL, NULL, '', 1, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (155259442, '修改文件配置', 1552594437741445122, 0, '/system/file/config/update', 'System/File/FileConfig/Index/update', NULL, '1', 0, 'F', '0', '0', 'system:file:fileconfig:index:update', 'Dot', 'admin', '2023-03-15 18:07:36.404', 'admin', '2023-04-26 13:56:07.306', NULL, NULL, '', 1, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (155259443, '删除文件配置', 1552594437741445122, 0, '/system/file/config/delete', 'System/File/FileConfig/Index/delete', NULL, '1', 0, 'F', '0', '0', 'system:file:fileconfig:index:delete', 'Dot', 'admin', '2023-03-15 18:07:36.404', 'admin', '2023-04-26 13:56:07.314', NULL, NULL, '', 1, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (155259445, '导出文件配置', 1552594437741445122, 0, '/system/file/config/export', 'System/File/FileConfig/Index/export', NULL, '1', 0, 'F', '0', '0', 'system:file:fileconfig:index:export', 'Dot', 'admin', '2023-03-15 18:07:36.404', 'admin', '2023-04-26 13:56:07.331', NULL, NULL, '', 1, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (20121, '新增定时任务', 2012, 1, '/system/monitor/jobs/add', 'System/Monitor/Jobs/Index/add', NULL, '1', 0, 'F', '0', '0', 'system:monitor:jobs:index:add', 'Dot', 'admin', '2023-03-15 18:07:36.513', 'admin', '2023-04-26 13:48:07.731', '', NULL, '', 1, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (20122, '修改定时任务', 2012, 1, '/system/monitor/jobs/update', 'System/Monitor/Jobs/Index/update', NULL, '1', 0, 'F', '0', '0', 'system:monitor:jobs:index:update', 'Dot', 'admin', '2023-03-15 18:07:36.513', 'admin', '2023-04-26 13:48:07.742', '', NULL, '', 1, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (20144, '查询服务监控', 2014, 3, '/system/monitor/servies/query', 'System/Monitor/Servies/Index/query', NULL, '1', 0, 'F', '0', '0', 'system:monitor:servies:index:query', 'Dot', 'admin', '2023-03-15 18:07:36.513', 'admin', '2023-04-26 13:49:14.772', '', NULL, '', 1, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (20145, '导出服务监控', 2014, 3, '/system/monitor/servies/export', 'System/Monitor/Servies/Index/export', NULL, '1', 0, 'F', '0', '0', 'system:monitor:servies:index:export', 'Dot', 'admin', '2023-03-15 18:07:36.513', 'admin', '2023-04-26 13:49:14.782', '', NULL, '', 1, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (20161, '新增缓存列表', 2016, 5, '/system/monitor/cache_list/add', 'System/Monitor/Cache/lndex/add', NULL, '1', 0, 'F', '0', '0', 'system:monitor:cache:lndex:add', 'Dot', 'admin', '2023-03-15 18:07:36.513', 'admin', '2023-04-26 13:51:38.238', '', NULL, '', 1, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (20162, '修改缓存列表', 2016, 5, '/system/monitor/cache_list/update', 'System/Monitor/Cache/lndex/update', NULL, '1', 0, 'F', '0', '0', 'system:monitor:cache:lndex:update', 'Dot', 'admin', '2023-03-15 18:07:36.513', 'admin', '2023-04-26 13:51:38.248', '', NULL, '', 1, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (20163, '删除缓存列表', 2016, 5, '/system/monitor/cache_list/delete', 'System/Monitor/Cache/lndex/delete', NULL, '1', 0, 'F', '0', '0', 'system:monitor:cache:lndex:delete', 'Dot', 'admin', '2023-03-15 18:07:36.513', 'admin', '2023-04-26 13:51:38.26', '', NULL, '', 1, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (20164, '查询缓存列表', 2016, 5, '/system/monitor/cache_list/query', 'System/Monitor/Cache/lndex/query', NULL, '1', 0, 'F', '0', '0', 'system:monitor:cache:lndex:query', 'Dot', 'admin', '2023-03-15 18:07:36.513', 'admin', '2023-04-26 13:51:38.27', '', NULL, '', 1, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (155249261, '新增错误码管理', 1552492618272964609, 7, '/system/monitor/error_code/add', 'System/Monitor/ErrorCode/Index/add', NULL, '1', 0, 'F', '0', '0', 'system:monitor:errorcode:index:add', 'Dot', 'admin', '2023-03-15 18:07:36.513', 'admin', '2023-04-26 13:58:38.321', NULL, NULL, '', 1, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (155249262, '修改错误码管理', 1552492618272964609, 7, '/system/monitor/error_code/update', 'System/Monitor/ErrorCode/Index/update', NULL, '1', 0, 'F', '0', '0', 'system:monitor:errorcode:index:update', 'Dot', 'admin', '2023-03-15 18:07:36.513', 'admin', '2023-04-26 13:58:38.331', NULL, NULL, '', 1, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (155249263, '删除错误码管理', 1552492618272964609, 7, '/system/monitor/error_code/delete', 'System/Monitor/ErrorCode/Index/delete', NULL, '1', 0, 'F', '0', '0', 'system:monitor:errorcode:index:delete', 'Dot', 'admin', '2023-03-15 18:07:36.513', 'admin', '2023-04-26 13:58:38.339', NULL, NULL, '', 1, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (155249264, '查询错误码管理', 1552492618272964609, 7, '/system/monitor/error_code/query', 'System/Monitor/ErrorCode/Index/query', NULL, '1', 0, 'F', '0', '0', 'system:monitor:errorcode:index:query', 'Dot', 'admin', '2023-03-15 18:07:36.513', 'admin', '2023-04-26 13:58:38.348', NULL, NULL, '', 1, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (155249265, '导出错误码管理', 1552492618272964609, 7, '/system/monitor/error_code/export', 'System/Monitor/ErrorCode/Index/export', NULL, '1', 0, 'F', '0', '0', 'system:monitor:errorcode:index:export', 'Dot', 'admin', '2023-03-15 18:07:36.513', 'admin', '2023-04-26 13:58:38.357', NULL, NULL, '', 1, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (20165, '导出缓存列表', 2016, 5, '/system/monitor/cache_list/export', 'System/Monitor/Cache/lndex/export', NULL, '1', 0, 'F', '0', '0', 'system:monitor:cache:lndex:export', 'Dot', 'admin', '2023-03-15 18:07:36.513', 'admin', '2023-04-26 13:51:38.279', '', NULL, '', 1, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637702341631279105, '新增在线用户', 2011, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:monitor:onlineuser:index:add', NULL, 'admin', '2023-03-20 14:27:19.941', 'admin', '2023-04-26 13:46:28.394', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637702341635473410, '修改在线用户', 2011, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:monitor:onlineuser:index:update', NULL, 'admin', '2023-03-20 14:27:19.942', 'admin', '2023-04-26 13:46:28.405', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1637702341639667714, '删除在线用户', 2011, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:monitor:onlineuser:index:delete', NULL, 'admin', '2023-03-20 14:27:19.942', 'admin', '2023-04-26 13:46:28.416', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (20123, '删除定时任务', 2012, 1, '/system/monitor/jobs/delete', 'System/Monitor/Jobs/Index/delete', NULL, '1', 0, 'F', '0', '0', 'system:monitor:jobs:index:delete', 'Dot', 'admin', '2023-03-15 18:07:36.513', 'admin', '2023-04-26 13:48:07.753', '', NULL, '', 1, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (20124, '查询定时任务', 2012, 1, '/system/monitor/jobs/query', 'System/Monitor/Jobs/Index/query', NULL, '1', 0, 'F', '0', '0', 'system:monitor:jobs:index:query', 'Dot', 'admin', '2023-03-15 18:07:36.513', 'admin', '2023-04-26 13:48:07.763', '', NULL, '', 1, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (20125, '导出定时任务', 2012, 1, '/system/monitor/jobs/export', 'System/Monitor/Jobs/Index/export', NULL, '1', 0, 'F', '0', '0', 'system:monitor:jobs:index:export', 'Dot', 'admin', '2023-03-15 18:07:36.513', 'admin', '2023-04-26 13:48:07.774', '', NULL, '', 1, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (20155, '导出缓存监控', 2015, 4, '/system/monitor/cache_monitor/export', 'System/Monitor/CacheMonitor/Index/export', NULL, '1', 0, 'F', '0', '0', 'system:monitor:cachemonitor:index:export', 'Dot', 'admin', '2023-03-15 18:07:36.513', 'admin', '2023-04-26 13:50:23.734', '', NULL, '', 1, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (155259446, '文件状态修改', 1552594437741445122, 1, '/system/file/config/status', 'System/File/FileConfig/Index/status', NULL, '1', 0, 'F', '0', '0', 'system:file:fileconfig:index:status', 'Dot', 'admin', '2023-03-20 14:00:31.83548', 'admin', '2023-04-26 13:56:07.34', NULL, NULL, NULL, 1, '1', NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1648976771367776258, '文件列表', 1554005889752735746, NULL, NULL, NULL, NULL, '1', 0, 'F', NULL, '0', 'system:file:filelist:index:list', NULL, 'admin', '2023-04-26 10:21:05.362435', 'admin', '2023-04-26 10:21:05.362435', NULL, NULL, NULL, 1, NULL, NULL, 1);
INSERT INTO "public"."sys_menu" VALUES (1641691307338313730, '资源管理', 0, 97, '/system/resource', NULL, NULL, '1', 0, 'M', '0', '0', 'c5c07962-dcaa-41a2-9cbc-f0e3072b09d8', 'FolderFull', 'admin', '2023-03-31 14:38:03.476', 'admin', '2024-11-04 10:54:41.181', NULL, NULL, 'Resource', 1, NULL, NULL, 1);

-- ----------------------------
-- Table structure for sys_notice
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_notice";
CREATE TABLE "public"."sys_notice" (
  "notice_id" int8 NOT NULL,
  "notice_title" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "notice_type" char(1) COLLATE "pg_catalog"."default" NOT NULL,
  "notice_content" text COLLATE "pg_catalog"."default",
  "status" char(1) COLLATE "pg_catalog"."default",
  "create_by" varchar(64) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "update_by" varchar(64) COLLATE "pg_catalog"."default",
  "update_time" timestamp(6),
  "remark" varchar(255) COLLATE "pg_catalog"."default",
  "send_time" timestamp(6),
  "type" int4,
  "send_status" int4,
  "template_id" int8,
  "template_values" varchar(255) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "level" int4 DEFAULT 0,
  "del_flag" char(1) COLLATE "pg_catalog"."default" DEFAULT '0'::bpchar,
  "nick_name" varchar(24) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying
)
;
COMMENT ON COLUMN "public"."sys_notice"."notice_id" IS '公告ID';
COMMENT ON COLUMN "public"."sys_notice"."notice_title" IS '公告标题';
COMMENT ON COLUMN "public"."sys_notice"."notice_type" IS '公告类型（1通知 2公告）';
COMMENT ON COLUMN "public"."sys_notice"."notice_content" IS '公告内容';
COMMENT ON COLUMN "public"."sys_notice"."status" IS '公告状态（0正常 1关闭）';
COMMENT ON COLUMN "public"."sys_notice"."create_by" IS '创建者';
COMMENT ON COLUMN "public"."sys_notice"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."sys_notice"."update_by" IS '更新者';
COMMENT ON COLUMN "public"."sys_notice"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."sys_notice"."remark" IS '备注';
COMMENT ON COLUMN "public"."sys_notice"."template_id" IS '模板表id';
COMMENT ON COLUMN "public"."sys_notice"."level" IS '消息紧急情况';
COMMENT ON TABLE "public"."sys_notice" IS '管理员管理消息中心表';

-- ----------------------------
-- Records of sys_notice
-- ----------------------------

-- ----------------------------
-- Table structure for sys_notice_dept
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_notice_dept";
CREATE TABLE "public"."sys_notice_dept" (
  "notice_id" int8 NOT NULL,
  "dept_id" int8 NOT NULL
)
;

-- ----------------------------
-- Records of sys_notice_dept
-- ----------------------------

-- ----------------------------
-- Table structure for sys_notice_template
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_notice_template";
CREATE TABLE "public"."sys_notice_template" (
  "template_id" int8 NOT NULL,
  "template_content" varchar(500) COLLATE "pg_catalog"."default" NOT NULL,
  "template_keywords" varchar(128) COLLATE "pg_catalog"."default",
  "create_by" varchar(64) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "update_by" varchar(64) COLLATE "pg_catalog"."default",
  "update_time" timestamp(6),
  "template_title" varchar(100) COLLATE "pg_catalog"."default",
  "status" char(1) COLLATE "pg_catalog"."default",
  "params_content" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."sys_notice_template"."template_content" IS '模板内容';
COMMENT ON COLUMN "public"."sys_notice_template"."template_keywords" IS '模板类型对应表';
COMMENT ON COLUMN "public"."sys_notice_template"."template_title" IS '模版标题';
COMMENT ON COLUMN "public"."sys_notice_template"."status" IS '状态 0-启用 1-关闭';
COMMENT ON COLUMN "public"."sys_notice_template"."params_content" IS '模版参数';
COMMENT ON TABLE "public"."sys_notice_template" IS '消息模板表';

-- ----------------------------
-- Records of sys_notice_template
-- ----------------------------

-- ----------------------------
-- Table structure for sys_oper_log
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_oper_log";
CREATE TABLE "public"."sys_oper_log" (
  "oper_id" int8 NOT NULL,
  "title" varchar(50) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "business_type" int4 DEFAULT 0,
  "method" varchar(100) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "request_method" varchar(10) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "operator_type" int4 DEFAULT 0,
  "oper_name" varchar(50) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "dept_name" varchar(50) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "oper_url" varchar(255) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "oper_ip" varchar(128) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "oper_location" varchar(255) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "oper_param" varchar(2000) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "json_result" varchar(2000) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "status" int4 DEFAULT 0,
  "error_msg" varchar(2000) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "oper_time" timestamp(6)
)
;
COMMENT ON COLUMN "public"."sys_oper_log"."oper_id" IS '日志主键';
COMMENT ON COLUMN "public"."sys_oper_log"."title" IS '模块标题';
COMMENT ON COLUMN "public"."sys_oper_log"."business_type" IS '业务类型（0其它 1新增 2修改 3删除）';
COMMENT ON COLUMN "public"."sys_oper_log"."method" IS '方法名称';
COMMENT ON COLUMN "public"."sys_oper_log"."request_method" IS '请求方式';
COMMENT ON COLUMN "public"."sys_oper_log"."operator_type" IS '操作类别（0其它 1后台用户 2手机端用户）';
COMMENT ON COLUMN "public"."sys_oper_log"."oper_name" IS '操作人员';
COMMENT ON COLUMN "public"."sys_oper_log"."dept_name" IS '部门名称';
COMMENT ON COLUMN "public"."sys_oper_log"."oper_url" IS '请求URL';
COMMENT ON COLUMN "public"."sys_oper_log"."oper_ip" IS '主机地址';
COMMENT ON COLUMN "public"."sys_oper_log"."oper_location" IS '操作地点';
COMMENT ON COLUMN "public"."sys_oper_log"."oper_param" IS '请求参数';
COMMENT ON COLUMN "public"."sys_oper_log"."json_result" IS '返回参数';
COMMENT ON COLUMN "public"."sys_oper_log"."status" IS '操作状态（0正常 1异常）';
COMMENT ON COLUMN "public"."sys_oper_log"."error_msg" IS '错误消息';
COMMENT ON COLUMN "public"."sys_oper_log"."oper_time" IS '操作时间';
COMMENT ON TABLE "public"."sys_oper_log" IS '操作日志记录';

-- ----------------------------
-- Records of sys_oper_log
-- ----------------------------
INSERT INTO "public"."sys_oper_log" VALUES (1853241950843002882, '获得短信日志分页', 0, 'com.zhonghe.web.controller.system.SmsLogController.getSmsLogPage()', 'GET', 1, 'admin', '', '/com/zhonghe/dev-api/system/sms-log/page', '************', 'XX XX', '{}', '', 1, '
### Error querying database.  Cause: org.postgresql.util.PSQLException: ERROR: relation "system_sms_log" does not exist
  Position: 31
### The error may exist in com/zhonghe/system/mapper/SmsLogConvert.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT COUNT(*) AS total FROM system_sms_log
### Cause: org.postgresql.util.PSQLException: ERROR: relation "system_sms_log" does not exist
  Position: 31
; bad SQL grammar []; nested exception is org.postgresql.util.PSQLException: ERROR: relation "system_sms_log" does not exist
  Position: 31', '2024-11-04 09:04:47.306');
INSERT INTO "public"."sys_oper_log" VALUES (1853242089414418434, '获得短信日志分页', 0, 'com.zhonghe.web.controller.system.SmsLogController.getSmsLogPage()', 'GET', 1, 'admin', '', '/com/zhonghe/dev-api/system/sms-log/page', '************', 'XX XX', '{}', '', 1, '
### Error querying database.  Cause: org.postgresql.util.PSQLException: ERROR: relation "system_sms_log" does not exist
  Position: 31
### The error may exist in com/zhonghe/system/mapper/SmsLogConvert.java (best guess)
### The error may involve defaultParameterMap
### The error occurred while setting parameters
### SQL: SELECT COUNT(*) AS total FROM system_sms_log
### Cause: org.postgresql.util.PSQLException: ERROR: relation "system_sms_log" does not exist
  Position: 31
; bad SQL grammar []; nested exception is org.postgresql.util.PSQLException: ERROR: relation "system_sms_log" does not exist
  Position: 31', '2024-11-04 09:05:20.346');
INSERT INTO "public"."sys_oper_log" VALUES (1853242441912115202, '获得短信日志分页', 0, 'com.zhonghe.web.controller.system.SmsLogController.getSmsLogPage()', 'GET', 1, 'admin', '', '/com/zhonghe/dev-api/system/sms-log/page', '************', 'XX XX', '{}', '{"code":200,"msg":"操作成功","data":{"records":[],"total":"0","size":"10","current":"1","orders":[],"optimizeCountSql":true,"searchCount":true,"pages":"0"}}', 0, '', '2024-11-04 09:06:44.387');
INSERT INTO "public"."sys_oper_log" VALUES (1853242451538042882, '获得短信日志分页', 0, 'com.zhonghe.web.controller.system.SmsLogController.getSmsLogPage()', 'GET', 1, 'admin', '', '/com/zhonghe/dev-api/system/sms-log/page', '************', 'XX XX', '{}', '{"code":200,"msg":"操作成功","data":{"records":[],"total":"0","size":"10","current":"1","orders":[],"optimizeCountSql":true,"searchCount":true,"pages":"0"}}', 0, '', '2024-11-04 09:06:46.683');
INSERT INTO "public"."sys_oper_log" VALUES (1853242592198221825, '获得短信日志分页', 0, 'com.zhonghe.web.controller.system.SmsLogController.getSmsLogPage()', 'GET', 1, 'admin', '', '/com/zhonghe/dev-api/system/sms-log/page', '************', 'XX XX', '{}', '{"code":200,"msg":"操作成功","data":{"records":[],"total":"0","size":"10","current":"1","orders":[],"optimizeCountSql":true,"searchCount":true,"pages":"0"}}', 0, '', '2024-11-04 09:07:20.22');
INSERT INTO "public"."sys_oper_log" VALUES (1853246225807663106, '获得短信日志分页', 0, 'com.zhonghe.web.controller.system.SmsLogController.getSmsLogPage()', 'GET', 1, 'admin', '', '/com/zhonghe/dev-api/system/sms-log/page', '************', 'XX XX', '{}', '{"code":200,"msg":"操作成功","data":{"records":[],"total":"0","size":"10","current":"1","orders":[],"optimizeCountSql":true,"searchCount":true,"pages":"0"}}', 0, '', '2024-11-04 09:21:46.539');
INSERT INTO "public"."sys_oper_log" VALUES (1853247098269032450, '获得短信日志分页', 0, 'com.zhonghe.web.controller.system.SmsLogController.getSmsLogPage()', 'GET', 1, 'admin', '', '/com/zhonghe/dev-api/system/sms-log/page', '************', 'XX XX', '{}', '{"code":200,"msg":"操作成功","data":{"records":[],"total":"0","size":"10","current":"1","orders":[],"optimizeCountSql":true,"searchCount":true,"pages":"0"}}', 0, '', '2024-11-04 09:25:14.55');
INSERT INTO "public"."sys_oper_log" VALUES (1853265222850666497, '对象存储桶', 1, 'com.zhonghe.web.controller.oss.SysOssBucketController.add()', 'POST', 1, 'admin', '', '/com/zhonghe/system/bucket', '************', 'XX XX', '{"params":{},"id":"1853265219285508097","nickName":"测试","bucketType":"1","capacityType":"MB","maxCapacity":10.0,"personal":1}', '{"code":200,"msg":"操作成功"}', 0, '', '2024-11-04 10:37:15.787');
INSERT INTO "public"."sys_oper_log" VALUES (1853265255037755394, '对象存储桶', 3, 'com.zhonghe.web.controller.oss.SysOssBucketController.remove()', 'DELETE', 1, 'admin', '', '/com/zhonghe/system/bucket/1853265219285508097', '************', 'XX XX', '{ids=1853265219285508097}', '{"code":200,"msg":"操作成功"}', 0, '', '2024-11-04 10:37:23.461');
INSERT INTO "public"."sys_oper_log" VALUES (1853266018455609345, 'OSS对象存储', 1, 'com.zhonghe.web.controller.system.SysOssController.upload()', 'POST', 1, 'admin', '', '/com/zhonghe/system/oss/upload', '*************', 'XX XX', '', '', 1, '
### Error updating database.  Cause: org.postgresql.util.PSQLException: ERROR: column "bucket_id" of relation "sys_oss" does not exist
  Position: 85
### The error may exist in com/zhonghe/system/mapper/SysOssMapper.java (best guess)
### The error may involve com.zhonghe.system.mapper.SysOssMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO sys_oss  ( oss_id, file_name, original_name, file_suffix, url, service, bucket_id, create_by, create_time, update_by, update_time )  VALUES  ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
### Cause: org.postgresql.util.PSQLException: ERROR: column "bucket_id" of relation "sys_oss" does not exist
  Position: 85
; bad SQL grammar []; nested exception is org.postgresql.util.PSQLException: ERROR: column "bucket_id" of relation "sys_oss" does not exist
  Position: 85', '2024-11-04 10:40:25.474');
INSERT INTO "public"."sys_oper_log" VALUES (1853266146071502849, 'OSS对象存储', 1, 'com.zhonghe.web.controller.system.SysOssController.upload()', 'POST', 1, 'admin', '', '/com/zhonghe/system/oss/upload', '*************', 'XX XX', '', '', 1, '
### Error updating database.  Cause: org.postgresql.util.PSQLException: ERROR: column "bucket_id" of relation "sys_oss" does not exist
  Position: 85
### The error may exist in com/zhonghe/system/mapper/SysOssMapper.java (best guess)
### The error may involve com.zhonghe.system.mapper.SysOssMapper.insert-Inline
### The error occurred while setting parameters
### SQL: INSERT INTO sys_oss  ( oss_id, file_name, original_name, file_suffix, url, service, bucket_id, create_by, create_time, update_by, update_time )  VALUES  ( ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ? )
### Cause: org.postgresql.util.PSQLException: ERROR: column "bucket_id" of relation "sys_oss" does not exist
  Position: 85
; bad SQL grammar []; nested exception is org.postgresql.util.PSQLException: ERROR: column "bucket_id" of relation "sys_oss" does not exist
  Position: 85', '2024-11-04 10:40:55.901');
INSERT INTO "public"."sys_oper_log" VALUES (1853268363432255489, '菜单管理', 2, 'com.zhonghe.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', '', '/com/zhonghe/system/menu', '*************', 'XX XX', '{"updateBy":"admin","updateTime":"2024-11-04 10:49:44","params":{},"parentId":"1641692769984393217","children":[],"menuId":"1641698767885250561","menuName":"机器人配置","orderNum":3,"path":"/system/notice/robot","component":"System/Robot/Index","isFrame":"1","menuType":"C","visible":"0","status":"0","icon":"Dot","pageName":"Robot","isApp":1}', '{"code":200,"msg":"操作成功"}', 0, '', '2024-11-04 10:49:44.561');
INSERT INTO "public"."sys_oper_log" VALUES (1853269279346614273, '获得短信日志分页', 0, 'com.zhonghe.web.controller.system.SmsLogController.getSmsLogPage()', 'GET', 1, 'admin', '', '/com/zhonghe/dev-api/system/sms-log/page', '*************', 'XX XX', '{}', '{"code":200,"msg":"操作成功","data":{"records":[],"total":"0","size":"10","current":"1","orders":[],"optimizeCountSql":true,"searchCount":true,"pages":"0"}}', 0, '', '2024-11-04 10:53:22.932');
INSERT INTO "public"."sys_oper_log" VALUES (1853269607576068097, '菜单管理', 2, 'com.zhonghe.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', '', '/com/zhonghe/system/menu', '************', 'XX XX', '{"updateBy":"admin","updateTime":"2024-11-04 10:54:41","params":{},"parentId":"0","children":[],"menuId":"1641691307338313730","menuName":"资源管理","orderNum":97,"path":"/system/resource","isFrame":"1","menuType":"M","visible":"0","status":"0","icon":"FolderFull","pageName":"Resource","isApp":1}', '{"code":200,"msg":"操作成功"}', 0, '', '2024-11-04 10:54:41.187');
INSERT INTO "public"."sys_oper_log" VALUES (1853269668192149506, '菜单管理', 2, 'com.zhonghe.web.controller.system.SysMenuController.edit()', 'PUT', 1, 'admin', '', '/com/zhonghe/system/menu', '************', 'XX XX', '{"updateBy":"admin","updateTime":"2024-11-04 10:54:55","params":{},"parentId":"0","children":[],"menuId":"2010","menuName":"系统监控","orderNum":999,"path":"/system/monitor","isFrame":"1","menuType":"M","visible":"0","status":"0","icon":"MonitorFull","pageName":"Monitor","isApp":1}', '{"code":200,"msg":"操作成功"}', 0, '', '2024-11-04 10:54:55.64');
INSERT INTO "public"."sys_oper_log" VALUES (1853270693670117377, 'OSS对象存储', 1, 'com.zhonghe.web.controller.system.SysOssController.upload()', 'POST', 1, 'admin', '', '/com/zhonghe/system/oss/upload', '*************', 'XX XX', '', '{"code":200,"msg":"操作成功","data":{"ossId":"1853270693649145857","url":"/home/<USER>","fileName":"编组 15.png"}}', 0, '', '2024-11-04 10:59:00.132');
INSERT INTO "public"."sys_oper_log" VALUES (1853270730550632450, 'OSS对象存储', 1, 'com.zhonghe.web.controller.system.SysOssController.upload()', 'POST', 1, 'admin', '', '/com/zhonghe/system/oss/upload', '*************', 'XX XX', '', '{"code":200,"msg":"操作成功","data":{"ossId":"1853270730529660929","url":"/home/<USER>","fileName":"编组.png"}}', 0, '', '2024-11-04 10:59:08.925');
INSERT INTO "public"."sys_oper_log" VALUES (1853270767238209537, 'OSS对象存储', 1, 'com.zhonghe.web.controller.system.SysOssController.upload()', 'POST', 1, 'admin', '', '/com/zhonghe/system/oss/upload', '*************', 'XX XX', '', '{"code":200,"msg":"操作成功","data":{"ossId":"1853270767217238018","url":"/home/<USER>","fileName":"左边插图@2x.png"}}', 0, '', '2024-11-04 10:59:17.672');
INSERT INTO "public"."sys_oper_log" VALUES (1853271617813700609, 'OSS对象存储', 1, 'com.zhonghe.web.controller.system.SysOssController.upload()', 'POST', 1, 'admin', '', '/com/zhonghe/system/oss/upload', '*************', 'XX XX', '', '{"code":200,"msg":"操作成功","data":{"ossId":"1853271617792729090","url":"/home/<USER>","fileName":"水务logo.png"}}', 0, '', '2024-11-04 11:02:40.466');
INSERT INTO "public"."sys_oper_log" VALUES (1853272528850083842, 'OSS对象存储', 1, 'com.zhonghe.web.controller.system.SysOssController.upload()', 'POST', 1, 'admin', '', '/com/zhonghe/system/oss/upload', '*************', 'XX XX', '', '{"code":200,"msg":"操作成功","data":{"ossId":"1853272528833306626","url":"/home/<USER>","fileName":"编组@2x.png"}}', 0, '', '2024-11-04 11:06:17.674');
INSERT INTO "public"."sys_oper_log" VALUES (1853272725319671809, 'OSS对象存储', 1, 'com.zhonghe.web.controller.system.SysOssController.upload()', 'POST', 1, 'admin', '', '/com/zhonghe/system/oss/upload', '*************', 'XX XX', '', '{"code":200,"msg":"操作成功","data":{"ossId":"1853272725298700290","url":"/home/<USER>","fileName":"水务***********"}}', 0, '', '2024-11-04 11:07:04.515');
INSERT INTO "public"."sys_oper_log" VALUES (1853270669892608002, 'OSS对象存储', 1, 'com.zhonghe.web.controller.system.SysOssController.upload()', 'POST', 1, 'admin', '', '/com/zhonghe/system/oss/upload', '*************', 'XX XX', '', '{"code":200,"msg":"操作成功","data":{"ossId":"1853270669871636482","url":"/home/<USER>","fileName":"编组 <EMAIL>"}}', 0, '', '2024-11-04 10:58:54.464');
INSERT INTO "public"."sys_oper_log" VALUES (1853270727539122177, 'OSS对象存储', 1, 'com.zhonghe.web.controller.system.SysOssController.upload()', 'POST', 1, 'admin', '', '/com/zhonghe/system/oss/upload', '*************', 'XX XX', '', '{"code":200,"msg":"操作成功","data":{"ossId":"1853270727518150657","url":"/home/<USER>","fileName":"左边插图@2x.png"}}', 0, '', '2024-11-04 10:59:08.207');
INSERT INTO "public"."sys_oper_log" VALUES (1853270745218113538, 'OSS对象存储', 1, 'com.zhonghe.web.controller.system.SysOssController.upload()', 'POST', 1, 'admin', '', '/com/zhonghe/system/oss/upload', '*************', 'XX XX', '', '{"code":200,"msg":"操作成功","data":{"ossId":"1853270745197142018","url":"/home/<USER>","fileName":"左边插图.png"}}', 0, '', '2024-11-04 10:59:12.423');
INSERT INTO "public"."sys_oper_log" VALUES (1853270987254620162, 'OSS对象存储', 1, 'com.zhonghe.web.controller.system.SysOssController.upload()', 'POST', 1, 'admin', '', '/com/zhonghe/system/oss/upload', '*************', 'XX XX', '', '{"code":200,"msg":"操作成功","data":{"ossId":"1853270987237842946","url":"/home/<USER>","fileName":"左边插图 (1).png"}}', 0, '', '2024-11-04 11:00:10.129');
INSERT INTO "public"."sys_oper_log" VALUES (1853271229412761601, 'OSS对象存储', 1, 'com.zhonghe.web.controller.system.SysOssController.upload()', 'POST', 1, 'admin', '', '/com/zhonghe/system/oss/upload', '*************', 'XX XX', '', '{"code":200,"msg":"操作成功","data":{"ossId":"1853271229379207170","url":"/home/<USER>","fileName":"logo.png"}}', 0, '', '2024-11-04 11:01:07.864');
INSERT INTO "public"."sys_oper_log" VALUES (1853271291777867778, 'OSS对象存储', 1, 'com.zhonghe.web.controller.system.SysOssController.upload()', 'POST', 1, 'admin', '', '/com/zhonghe/system/oss/upload', '*************', 'XX XX', '', '{"code":200,"msg":"操作成功","data":{"ossId":"1853271291761090562","url":"/home/<USER>","fileName":"logo.png"}}', 0, '', '2024-11-04 11:01:22.733');
INSERT INTO "public"."sys_oper_log" VALUES (1853272503482933249, 'OSS对象存储', 1, 'com.zhonghe.web.controller.system.SysOssController.upload()', 'POST', 1, 'admin', '', '/com/zhonghe/system/oss/upload', '*************', 'XX XX', '', '{"code":200,"msg":"操作成功","data":{"ossId":"1853272503466156033","url":"/home/<USER>","fileName":"左边插图@2x (1).png"}}', 0, '', '2024-11-04 11:06:11.626');
INSERT INTO "public"."sys_oper_log" VALUES (1853272556092088322, 'OSS对象存储', 1, 'com.zhonghe.web.controller.system.SysOssController.upload()', 'POST', 1, 'admin', '', '/com/zhonghe/system/oss/upload', '*************', 'XX XX', '', '{"code":200,"msg":"操作成功","data":{"ossId":"1853272556075311106","url":"/home/<USER>","fileName":"左边插图@2x (1).png"}}', 0, '', '2024-11-04 11:06:24.169');
INSERT INTO "public"."sys_oper_log" VALUES (1853272746278608897, 'OSS对象存储', 1, 'com.zhonghe.web.controller.system.SysOssController.upload()', 'POST', 1, 'admin', '', '/com/zhonghe/system/oss/upload', '*************', 'XX XX', '', '{"code":200,"msg":"操作成功","data":{"ossId":"1853272746261831681","url":"/home/<USER>","fileName":"水务***********"}}', 0, '', '2024-11-04 11:07:09.513');

-- ----------------------------
-- Table structure for sys_oss
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_oss";
CREATE TABLE "public"."sys_oss" (
  "oss_id" int8 NOT NULL,
  "file_name" varchar(255) COLLATE "pg_catalog"."default" NOT NULL DEFAULT ''::character varying,
  "original_name" varchar(255) COLLATE "pg_catalog"."default" NOT NULL DEFAULT ''::character varying,
  "file_suffix" varchar(10) COLLATE "pg_catalog"."default" NOT NULL DEFAULT ''::character varying,
  "url" varchar(500) COLLATE "pg_catalog"."default" NOT NULL DEFAULT ''::character varying,
  "create_by" varchar(64) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "create_time" timestamp(6),
  "update_by" varchar(64) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "update_time" timestamp(6),
  "service" varchar(20) COLLATE "pg_catalog"."default" DEFAULT 'minio'::character varying,
  "bucket_id" int8
)
;
COMMENT ON COLUMN "public"."sys_oss"."oss_id" IS '对象存储主键';
COMMENT ON COLUMN "public"."sys_oss"."file_name" IS '文件名';
COMMENT ON COLUMN "public"."sys_oss"."original_name" IS '原名';
COMMENT ON COLUMN "public"."sys_oss"."file_suffix" IS '文件后缀名';
COMMENT ON COLUMN "public"."sys_oss"."url" IS 'URL地址';
COMMENT ON COLUMN "public"."sys_oss"."create_by" IS '上传人';
COMMENT ON COLUMN "public"."sys_oss"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."sys_oss"."update_by" IS '更新者';
COMMENT ON COLUMN "public"."sys_oss"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."sys_oss"."service" IS '服务商';
COMMENT ON TABLE "public"."sys_oss" IS 'OSS对象存储表';

-- ----------------------------
-- Records of sys_oss
-- ----------------------------
INSERT INTO "public"."sys_oss" VALUES (1853270669871636482, '编组 <EMAIL>', '编组 <EMAIL>', '.png', '/home/<USER>', 'admin', '2024-11-04 10:58:54.459', 'admin', '2024-11-04 10:58:54.459', 'minio', 1853270669833887746);
INSERT INTO "public"."sys_oss" VALUES (1853270693649145857, '编组 15.png', '编组 15.png', '.png', '/home/<USER>', 'admin', '2024-11-04 10:59:00.128', 'admin', '2024-11-04 10:59:00.128', 'minio', 1853270693615591425);
INSERT INTO "public"."sys_oss" VALUES (1853270727518150657, '左边插图@2x.png', '左边插图@2x.png', '.png', '/home/<USER>', 'admin', '2024-11-04 10:59:08.203', 'admin', '2024-11-04 10:59:08.203', 'minio', 1853270727463624706);
INSERT INTO "public"."sys_oss" VALUES (1853270730529660929, '编组.png', '编组.png', '.png', '/home/<USER>', 'admin', '2024-11-04 10:59:08.921', 'admin', '2024-11-04 10:59:08.921', 'minio', 1853270730491912193);
INSERT INTO "public"."sys_oss" VALUES (1853270745197142018, '左边插图.png', '左边插图.png', '.png', '/home/<USER>', 'admin', '2024-11-04 10:59:12.418', 'admin', '2024-11-04 10:59:12.418', 'minio', 1853270745155198977);
INSERT INTO "public"."sys_oss" VALUES (1853270767217238018, '左边插图@2x.png', '左边插图@2x.png', '.png', '/home/<USER>', 'admin', '2024-11-04 10:59:17.668', 'admin', '2024-11-04 10:59:17.668', 'minio', 1853270767183683585);
INSERT INTO "public"."sys_oss" VALUES (1853270987237842946, '左边插图 (1).png', '左边插图 (1).png', '.png', '/home/<USER>', 'admin', '2024-11-04 11:00:10.125', 'admin', '2024-11-04 11:00:10.125', 'minio', 1853270987200094209);
INSERT INTO "public"."sys_oss" VALUES (1853271229379207170, 'logo.png', 'logo.png', '.png', '/home/<USER>', 'admin', '2024-11-04 11:01:07.856', 'admin', '2024-11-04 11:01:07.856', 'minio', 1853271229341458433);
INSERT INTO "public"."sys_oss" VALUES (1853271291761090562, 'logo.png', 'logo.png', '.png', '/home/<USER>', 'admin', '2024-11-04 11:01:22.728', 'admin', '2024-11-04 11:01:22.728', 'minio', 1853271291723341825);
INSERT INTO "public"."sys_oss" VALUES (1853271617792729090, '水务logo.png', '水务logo.png', '.png', '/home/<USER>', 'admin', '2024-11-04 11:02:40.461', 'admin', '2024-11-04 11:02:40.461', 'minio', 1853271617754980354);
INSERT INTO "public"."sys_oss" VALUES (1853272503466156033, '左边插图@2x (1).png', '左边插图@2x (1).png', '.png', '/home/<USER>', 'admin', '2024-11-04 11:06:11.622', 'admin', '2024-11-04 11:06:11.622', 'minio', 1853272503428407298);
INSERT INTO "public"."sys_oss" VALUES (1853272528833306626, '编组@2x.png', '编组@2x.png', '.png', '/home/<USER>', 'admin', '2024-11-04 11:06:17.67', 'admin', '2024-11-04 11:06:17.67', 'minio', 1853272528799752194);
INSERT INTO "public"."sys_oss" VALUES (1853272556075311106, '左边插图@2x (1).png', '左边插图@2x (1).png', '.png', '/home/<USER>', 'admin', '2024-11-04 11:06:24.165', 'admin', '2024-11-04 11:06:24.165', 'minio', 1853272556045950978);
INSERT INTO "public"."sys_oss" VALUES (1853272725298700290, '水务***********', '水务***********', '.png', '/home/<USER>', 'admin', '2024-11-04 11:07:04.512', 'admin', '2024-11-04 11:07:04.512', 'minio', 1853272725269340161);
INSERT INTO "public"."sys_oss" VALUES (1853272746261831681, '水务***********', '水务***********', '.png', '/home/<USER>', 'admin', '2024-11-04 11:07:09.509', 'admin', '2024-11-04 11:07:09.509', 'minio', 1853272746232471553);

-- ----------------------------
-- Table structure for sys_oss_bucket
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_oss_bucket";
CREATE TABLE "public"."sys_oss_bucket" (
  "id" int8 NOT NULL,
  "nick_name" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "bucket_name" varchar(100) COLLATE "pg_catalog"."default",
  "bucket_type" int4 DEFAULT 0,
  "minio_path" text COLLATE "pg_catalog"."default",
  "original_name" varchar(120) COLLATE "pg_catalog"."default",
  "file_suffix" varchar(9) COLLATE "pg_catalog"."default",
  "parent_id" int8 DEFAULT 0,
  "bucket_id" int8 DEFAULT 0,
  "expired_time" timestamp(6),
  "tags" varchar(32) COLLATE "pg_catalog"."default",
  "owner_id" int8,
  "status" varchar COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "update_time" timestamp(6),
  "create_by" varchar(12) COLLATE "pg_catalog"."default",
  "update_by" varchar(12) COLLATE "pg_catalog"."default",
  "del_flag" char(1) COLLATE "pg_catalog"."default" DEFAULT '0'::bpchar,
  "max_capacity" int8,
  "currentCapacity" int8,
  "personal" int2 DEFAULT 0,
  "file_size" int8,
  "link" varchar COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."sys_oss_bucket"."nick_name" IS '系统桶名';
COMMENT ON COLUMN "public"."sys_oss_bucket"."bucket_type" IS '类型1:minio桶 2：minio文件夹 3：minio文件';
COMMENT ON COLUMN "public"."sys_oss_bucket"."original_name" IS '原始名称';
COMMENT ON COLUMN "public"."sys_oss_bucket"."file_suffix" IS '文件后缀名';
COMMENT ON COLUMN "public"."sys_oss_bucket"."parent_id" IS '父节点';
COMMENT ON COLUMN "public"."sys_oss_bucket"."bucket_id" IS '所属桶节点';
COMMENT ON COLUMN "public"."sys_oss_bucket"."expired_time" IS '过期时间';
COMMENT ON COLUMN "public"."sys_oss_bucket"."tags" IS '标签';
COMMENT ON COLUMN "public"."sys_oss_bucket"."max_capacity" IS '最大容量';
COMMENT ON COLUMN "public"."sys_oss_bucket"."currentCapacity" IS '当前容量';
COMMENT ON COLUMN "public"."sys_oss_bucket"."file_size" IS '文件大小';
COMMENT ON COLUMN "public"."sys_oss_bucket"."link" IS '链接';

-- ----------------------------
-- Records of sys_oss_bucket
-- ----------------------------
INSERT INTO "public"."sys_oss_bucket" VALUES (1853265219285508097, '测试', NULL, 1, NULL, NULL, NULL, 0, 1853265219285508097, NULL, NULL, 1, '0', '2024-11-04 10:37:14.937', '2024-11-04 10:37:14.937', 'admin', 'admin', '2', 10485760, NULL, 1, NULL, NULL);
INSERT INTO "public"."sys_oss_bucket" VALUES (1853266018128453633, '2024/11/04/710a53903d014c47886a9d8a5e5ce5a2', NULL, 0, NULL, '2024/11/04/710a53903d014c47886a9d8a5e5ce5a2.png', '.png', 0, 0, NULL, NULL, 1, NULL, '2024-11-04 10:40:25.397', '2024-11-04 10:40:25.397', 'admin', 'admin', '0', NULL, NULL, 0, NULL, NULL);
INSERT INTO "public"."sys_oss_bucket" VALUES (1853266146012782593, '2024/11/04/a23129a5f85c4254b1f1dd8c185c2ea8', NULL, 0, NULL, '2024/11/04/a23129a5f85c4254b1f1dd8c185c2ea8.png', '.png', 0, 0, NULL, NULL, 1, NULL, '2024-11-04 10:40:55.887', '2024-11-04 10:40:55.887', 'admin', 'admin', '0', NULL, NULL, 0, NULL, NULL);
INSERT INTO "public"."sys_oss_bucket" VALUES (1853270669833887746, '2024/11/04/46f5e996f3b243689f3977794a9d1b1d', NULL, 0, NULL, '2024/11/04/46f5e996f3b243689f3977794a9d1b1d.png', '.png', 0, 0, NULL, NULL, 1, NULL, '2024-11-04 10:58:54.45', '2024-11-04 10:58:54.45', 'admin', 'admin', '0', NULL, NULL, 0, NULL, NULL);
INSERT INTO "public"."sys_oss_bucket" VALUES (1853270693615591425, '2024/11/04/91d76c688948451cb8488bc41c47d609', NULL, 0, NULL, '2024/11/04/91d76c688948451cb8488bc41c47d609.png', '.png', 0, 0, NULL, NULL, 1, NULL, '2024-11-04 10:59:00.12', '2024-11-04 10:59:00.12', 'admin', 'admin', '0', NULL, NULL, 0, NULL, NULL);
INSERT INTO "public"."sys_oss_bucket" VALUES (1853270727463624706, '2024/11/04/36deb436577c49ed9d3ce0cce691a9ff', NULL, 0, NULL, '2024/11/04/36deb436577c49ed9d3ce0cce691a9ff.png', '.png', 0, 0, NULL, NULL, 1, NULL, '2024-11-04 10:59:08.19', '2024-11-04 10:59:08.19', 'admin', 'admin', '0', NULL, NULL, 0, NULL, NULL);
INSERT INTO "public"."sys_oss_bucket" VALUES (1853270730491912193, '2024/11/04/7ea601ac09f04c2ca515af0a3f2de626', NULL, 0, NULL, '2024/11/04/7ea601ac09f04c2ca515af0a3f2de626.png', '.png', 0, 0, NULL, NULL, 1, NULL, '2024-11-04 10:59:08.912', '2024-11-04 10:59:08.912', 'admin', 'admin', '0', NULL, NULL, 0, NULL, NULL);
INSERT INTO "public"."sys_oss_bucket" VALUES (1853270745155198977, '2024/11/04/b1b1369a5c394c539cf8c3ea21f70258', NULL, 0, NULL, '2024/11/04/b1b1369a5c394c539cf8c3ea21f70258.png', '.png', 0, 0, NULL, NULL, 1, NULL, '2024-11-04 10:59:12.408', '2024-11-04 10:59:12.408', 'admin', 'admin', '0', NULL, NULL, 0, NULL, NULL);
INSERT INTO "public"."sys_oss_bucket" VALUES (1853270767183683585, '2024/11/04/1d1986ba58894c40818d45d2432aeba8', NULL, 0, NULL, '2024/11/04/1d1986ba58894c40818d45d2432aeba8.png', '.png', 0, 0, NULL, NULL, 1, NULL, '2024-11-04 10:59:17.66', '2024-11-04 10:59:17.66', 'admin', 'admin', '0', NULL, NULL, 0, NULL, NULL);
INSERT INTO "public"."sys_oss_bucket" VALUES (1853270987200094209, '2024/11/04/d9da7ae6fbe249b08222dad2346a59ef', NULL, 0, NULL, '2024/11/04/d9da7ae6fbe249b08222dad2346a59ef.png', '.png', 0, 0, NULL, NULL, 1, NULL, '2024-11-04 11:00:10.116', '2024-11-04 11:00:10.116', 'admin', 'admin', '0', NULL, NULL, 0, NULL, NULL);
INSERT INTO "public"."sys_oss_bucket" VALUES (1853271229341458433, '2024/11/04/886e2629262747249906dc6530b82ccb', NULL, 0, NULL, '2024/11/04/886e2629262747249906dc6530b82ccb.png', '.png', 0, 0, NULL, NULL, 1, NULL, '2024-11-04 11:01:07.847', '2024-11-04 11:01:07.847', 'admin', 'admin', '0', NULL, NULL, 0, NULL, NULL);
INSERT INTO "public"."sys_oss_bucket" VALUES (1853271291723341825, '2024/11/04/91c65829cb754ae798955000c1bc785d', NULL, 0, NULL, '2024/11/04/91c65829cb754ae798955000c1bc785d.png', '.png', 0, 0, NULL, NULL, 1, NULL, '2024-11-04 11:01:22.72', '2024-11-04 11:01:22.72', 'admin', 'admin', '0', NULL, NULL, 0, NULL, NULL);
INSERT INTO "public"."sys_oss_bucket" VALUES (1853271617754980354, '2024/11/04/e63e00f39a8f47339d226d7cbcc68a8e', NULL, 0, NULL, '2024/11/04/e63e00f39a8f47339d226d7cbcc68a8e.png', '.png', 0, 0, NULL, NULL, 1, NULL, '2024-11-04 11:02:40.452', '2024-11-04 11:02:40.452', 'admin', 'admin', '0', NULL, NULL, 0, NULL, NULL);
INSERT INTO "public"."sys_oss_bucket" VALUES (1853272503428407298, '2024/11/04/64fe74d2db8a46b0b457df8b6916d2b5', NULL, 0, NULL, '2024/11/04/64fe74d2db8a46b0b457df8b6916d2b5.png', '.png', 0, 0, NULL, NULL, 1, NULL, '2024-11-04 11:06:11.613', '2024-11-04 11:06:11.613', 'admin', 'admin', '0', NULL, NULL, 0, NULL, NULL);
INSERT INTO "public"."sys_oss_bucket" VALUES (1853272528799752194, '2024/11/04/44fdc03b4fbc4c5ab6746cbd65b5b0f8', NULL, 0, NULL, '2024/11/04/44fdc03b4fbc4c5ab6746cbd65b5b0f8.png', '.png', 0, 0, NULL, NULL, 1, NULL, '2024-11-04 11:06:17.662', '2024-11-04 11:06:17.662', 'admin', 'admin', '0', NULL, NULL, 0, NULL, NULL);
INSERT INTO "public"."sys_oss_bucket" VALUES (1853272556045950978, '2024/11/04/b19e71bdb2f444aa8d24b988dd92e284', NULL, 0, NULL, '2024/11/04/b19e71bdb2f444aa8d24b988dd92e284.png', '.png', 0, 0, NULL, NULL, 1, NULL, '2024-11-04 11:06:24.158', '2024-11-04 11:06:24.158', 'admin', 'admin', '0', NULL, NULL, 0, NULL, NULL);
INSERT INTO "public"."sys_oss_bucket" VALUES (1853272725269340161, '2024/11/04/bc005436d5944389b4dd043801e9bd95', NULL, 0, NULL, '2024/11/04/bc005436d5944389b4dd043801e9bd95.png', '.png', 0, 0, NULL, NULL, 1, NULL, '2024-11-04 11:07:04.504', '2024-11-04 11:07:04.504', 'admin', 'admin', '0', NULL, NULL, 0, NULL, NULL);
INSERT INTO "public"."sys_oss_bucket" VALUES (1853272746232471553, '2024/11/04/66cdfaea3e6445e98ba71f48760d0be2', NULL, 0, NULL, '2024/11/04/66cdfaea3e6445e98ba71f48760d0be2.png', '.png', 0, 0, NULL, NULL, 1, NULL, '2024-11-04 11:07:09.502', '2024-11-04 11:07:09.502', 'admin', 'admin', '0', NULL, NULL, 0, NULL, NULL);

-- ----------------------------
-- Table structure for sys_oss_bucket_dept
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_oss_bucket_dept";
CREATE TABLE "public"."sys_oss_bucket_dept" (
  "id" int8 NOT NULL,
  "dept_id" int8 NOT NULL,
  "bucket_id" int8 NOT NULL,
  "create_by" varchar(36) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "update_by" varchar(36) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "create_time" timestamp(6),
  "update_time" timestamp(6)
)
;
COMMENT ON COLUMN "public"."sys_oss_bucket_dept"."dept_id" IS '部门id';
COMMENT ON COLUMN "public"."sys_oss_bucket_dept"."bucket_id" IS '所属桶节点';

-- ----------------------------
-- Records of sys_oss_bucket_dept
-- ----------------------------

-- ----------------------------
-- Table structure for sys_oss_bucket_open
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_oss_bucket_open";
CREATE TABLE "public"."sys_oss_bucket_open" (
  "id" int8 NOT NULL,
  "bucket_id" int8 NOT NULL,
  "expired_time" timestamp(6),
  "create_by" varchar(24) COLLATE "pg_catalog"."default" NOT NULL,
  "create_time" timestamp(6) NOT NULL,
  "update_time" timestamp(6),
  "update_by" varchar(12) COLLATE "pg_catalog"."default",
  "del_flag" char(1) COLLATE "pg_catalog"."default" DEFAULT 0
)
;
COMMENT ON COLUMN "public"."sys_oss_bucket_open"."bucket_id" IS '所属桶节点';
COMMENT ON COLUMN "public"."sys_oss_bucket_open"."expired_time" IS '过期时间';

-- ----------------------------
-- Records of sys_oss_bucket_open
-- ----------------------------

-- ----------------------------
-- Table structure for sys_oss_bucket_sec
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_oss_bucket_sec";
CREATE TABLE "public"."sys_oss_bucket_sec" (
  "id" int8 NOT NULL,
  "bucket_id" int8 NOT NULL,
  "bucket_name" varchar(100) COLLATE "pg_catalog"."default",
  "bucket_type" int4 DEFAULT 0,
  "minio_path" text COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "update_time" timestamp(6),
  "create_by" varchar(12) COLLATE "pg_catalog"."default",
  "update_by" varchar(12) COLLATE "pg_catalog"."default",
  "current_capacity" int8 DEFAULT 0
)
;
COMMENT ON COLUMN "public"."sys_oss_bucket_sec"."bucket_id" IS '所属桶ID';
COMMENT ON COLUMN "public"."sys_oss_bucket_sec"."bucket_name" IS 'minio桶真实名称';
COMMENT ON COLUMN "public"."sys_oss_bucket_sec"."bucket_type" IS '类型1:minio桶 2：minio文件夹 3：minio文件';
COMMENT ON COLUMN "public"."sys_oss_bucket_sec"."minio_path" IS 'minio路径';
COMMENT ON COLUMN "public"."sys_oss_bucket_sec"."current_capacity" IS '当前容量';

-- ----------------------------
-- Records of sys_oss_bucket_sec
-- ----------------------------
INSERT INTO "public"."sys_oss_bucket_sec" VALUES (1853266018153619457, 1853266018128453633, 'lib', 0, '2024/11/04/710a53903d014c47886a9d8a5e5ce5a2.png', '2024-11-04 10:40:25.403', '2024-11-04 10:40:25.403', 'admin', 'admin', 0);
INSERT INTO "public"."sys_oss_bucket_sec" VALUES (1853266146029559810, 1853266146012782593, 'lib', 0, '2024/11/04/a23129a5f85c4254b1f1dd8c185c2ea8.png', '2024-11-04 10:40:55.891', '2024-11-04 10:40:55.891', 'admin', 'admin', 0);
INSERT INTO "public"."sys_oss_bucket_sec" VALUES (1853270669854859265, 1853270669833887746, 'lib', 0, '2024/11/04/46f5e996f3b243689f3977794a9d1b1d.png', '2024-11-04 10:58:54.455', '2024-11-04 10:58:54.455', 'admin', 'admin', 0);
INSERT INTO "public"."sys_oss_bucket_sec" VALUES (1853270693632368642, 1853270693615591425, 'lib', 0, '2024/11/04/91d76c688948451cb8488bc41c47d609.png', '2024-11-04 10:59:00.124', '2024-11-04 10:59:00.124', 'admin', 'admin', 0);
INSERT INTO "public"."sys_oss_bucket_sec" VALUES (1853270727497179137, 1853270727463624706, 'lib', 0, '2024/11/04/36deb436577c49ed9d3ce0cce691a9ff.png', '2024-11-04 10:59:08.198', '2024-11-04 10:59:08.198', 'admin', 'admin', 0);
INSERT INTO "public"."sys_oss_bucket_sec" VALUES (1853270730508689410, 1853270730491912193, 'lib', 0, '2024/11/04/7ea601ac09f04c2ca515af0a3f2de626.png', '2024-11-04 10:59:08.916', '2024-11-04 10:59:08.916', 'admin', 'admin', 0);
INSERT INTO "public"."sys_oss_bucket_sec" VALUES (1853270745180364801, 1853270745155198977, 'lib', 0, '2024/11/04/b1b1369a5c394c539cf8c3ea21f70258.png', '2024-11-04 10:59:12.414', '2024-11-04 10:59:12.414', 'admin', 'admin', 0);
INSERT INTO "public"."sys_oss_bucket_sec" VALUES (1853270767200460801, 1853270767183683585, 'lib', 0, '2024/11/04/1d1986ba58894c40818d45d2432aeba8.png', '2024-11-04 10:59:17.664', '2024-11-04 10:59:17.664', 'admin', 'admin', 0);
INSERT INTO "public"."sys_oss_bucket_sec" VALUES (1853270987221065729, 1853270987200094209, 'lib', 0, '2024/11/04/d9da7ae6fbe249b08222dad2346a59ef.png', '2024-11-04 11:00:10.121', '2024-11-04 11:00:10.121', 'admin', 'admin', 0);
INSERT INTO "public"."sys_oss_bucket_sec" VALUES (1853271229366624258, 1853271229341458433, 'lib', 0, '2024/11/04/886e2629262747249906dc6530b82ccb.png', '2024-11-04 11:01:07.853', '2024-11-04 11:01:07.853', 'admin', 'admin', 0);
INSERT INTO "public"."sys_oss_bucket_sec" VALUES (1853271291744313346, 1853271291723341825, 'lib', 0, '2024/11/04/91c65829cb754ae798955000c1bc785d.png', '2024-11-04 11:01:22.725', '2024-11-04 11:01:22.725', 'admin', 'admin', 0);
INSERT INTO "public"."sys_oss_bucket_sec" VALUES (1853271617775951874, 1853271617754980354, 'lib', 0, '2024/11/04/e63e00f39a8f47339d226d7cbcc68a8e.png', '2024-11-04 11:02:40.457', '2024-11-04 11:02:40.457', 'admin', 'admin', 0);
INSERT INTO "public"."sys_oss_bucket_sec" VALUES (1853272503453573122, 1853272503428407298, 'lib', 0, '2024/11/04/64fe74d2db8a46b0b457df8b6916d2b5.png', '2024-11-04 11:06:11.619', '2024-11-04 11:06:11.619', 'admin', 'admin', 0);
INSERT INTO "public"."sys_oss_bucket_sec" VALUES (1853272528820723713, 1853272528799752194, 'lib', 0, '2024/11/04/44fdc03b4fbc4c5ab6746cbd65b5b0f8.png', '2024-11-04 11:06:17.667', '2024-11-04 11:06:17.667', 'admin', 'admin', 0);
INSERT INTO "public"."sys_oss_bucket_sec" VALUES (1853272556062728194, 1853272556045950978, 'lib', 0, '2024/11/04/b19e71bdb2f444aa8d24b988dd92e284.png', '2024-11-04 11:06:24.162', '2024-11-04 11:06:24.162', 'admin', 'admin', 0);
INSERT INTO "public"."sys_oss_bucket_sec" VALUES (1853272725286117377, 1853272725269340161, 'lib', 0, '2024/11/04/bc005436d5944389b4dd043801e9bd95.png', '2024-11-04 11:07:04.508', '2024-11-04 11:07:04.508', 'admin', 'admin', 0);
INSERT INTO "public"."sys_oss_bucket_sec" VALUES (1853272746249248770, 1853272746232471553, 'lib', 0, '2024/11/04/66cdfaea3e6445e98ba71f48760d0be2.png', '2024-11-04 11:07:09.506', '2024-11-04 11:07:09.506', 'admin', 'admin', 0);

-- ----------------------------
-- Table structure for sys_oss_bucket_upload
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_oss_bucket_upload";
CREATE TABLE "public"."sys_oss_bucket_upload" (
  "bucket_upload_id" int8 NOT NULL,
  "bucket_id" int8 NOT NULL,
  "hashcode" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "total_count" int4 NOT NULL,
  "finish" bool DEFAULT false,
  "upload_id" varchar(90) COLLATE "pg_catalog"."default" NOT NULL,
  "create_by" varchar(24) COLLATE "pg_catalog"."default" NOT NULL,
  "create_time" timestamp(6) NOT NULL,
  "update_time" timestamp(6),
  "update_by" varchar(12) COLLATE "pg_catalog"."default",
  "del_flag" char(1) COLLATE "pg_catalog"."default" DEFAULT '0'::bpchar,
  "minio_key" varchar(120) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "minio_bucket" varchar(30) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "file_name" varchar(240) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying
)
;

-- ----------------------------
-- Records of sys_oss_bucket_upload
-- ----------------------------

-- ----------------------------
-- Table structure for sys_oss_config
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_oss_config";
CREATE TABLE "public"."sys_oss_config" (
  "oss_config_id" int8 NOT NULL,
  "config_key" varchar(20) COLLATE "pg_catalog"."default" NOT NULL DEFAULT ''::character varying,
  "access_key" varchar(255) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "secret_key" varchar(255) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "bucket_name" varchar(255) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "prefix" varchar(255) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "endpoint" varchar(255) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "domain" varchar(255) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "is_https" char(1) COLLATE "pg_catalog"."default" DEFAULT 'N'::bpchar,
  "region" varchar(255) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "access_policy" char(1) COLLATE "pg_catalog"."default" NOT NULL DEFAULT '1'::bpchar,
  "status" char(1) COLLATE "pg_catalog"."default" DEFAULT '1'::bpchar,
  "ext1" varchar(255) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "create_by" varchar(64) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "create_time" timestamp(6),
  "update_by" varchar(64) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "update_time" timestamp(6),
  "remark" varchar(500) COLLATE "pg_catalog"."default" DEFAULT ''::character varying
)
;
COMMENT ON COLUMN "public"."sys_oss_config"."oss_config_id" IS '主建';
COMMENT ON COLUMN "public"."sys_oss_config"."config_key" IS '配置key';
COMMENT ON COLUMN "public"."sys_oss_config"."access_key" IS 'accessKey';
COMMENT ON COLUMN "public"."sys_oss_config"."secret_key" IS '秘钥';
COMMENT ON COLUMN "public"."sys_oss_config"."bucket_name" IS '桶名称';
COMMENT ON COLUMN "public"."sys_oss_config"."prefix" IS '前缀';
COMMENT ON COLUMN "public"."sys_oss_config"."endpoint" IS '访问站点';
COMMENT ON COLUMN "public"."sys_oss_config"."domain" IS '自定义域名';
COMMENT ON COLUMN "public"."sys_oss_config"."is_https" IS '是否https（Y=是,N=否）';
COMMENT ON COLUMN "public"."sys_oss_config"."region" IS '域';
COMMENT ON COLUMN "public"."sys_oss_config"."access_policy" IS '桶权限类型(0=private 1=public 2=custom)';
COMMENT ON COLUMN "public"."sys_oss_config"."status" IS '状态（0=正常,1=停用）';
COMMENT ON COLUMN "public"."sys_oss_config"."ext1" IS '扩展字段';
COMMENT ON COLUMN "public"."sys_oss_config"."create_by" IS '创建者';
COMMENT ON COLUMN "public"."sys_oss_config"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."sys_oss_config"."update_by" IS '更新者';
COMMENT ON COLUMN "public"."sys_oss_config"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."sys_oss_config"."remark" IS '备注';
COMMENT ON TABLE "public"."sys_oss_config" IS '对象存储配置表';

-- ----------------------------
-- Records of sys_oss_config
-- ----------------------------
INSERT INTO "public"."sys_oss_config" VALUES (1, 'minio', 'admin', 'syzhkj123.0', 'lib', '', '*************:9001', '', 'N', '', '1', '0', '', 'admin', '2024-11-01 16:11:19.66', 'admin', '2024-11-01 16:11:19.66', '');

-- ----------------------------
-- Table structure for sys_oss_power_define
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_oss_power_define";
CREATE TABLE "public"."sys_oss_power_define" (
  "id" int8 NOT NULL,
  "power" varchar(8) COLLATE "pg_catalog"."default" NOT NULL,
  "status" char(1) COLLATE "pg_catalog"."default" DEFAULT '0'::bpchar,
  "create_by" varchar(50) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "update_by" varchar(50) COLLATE "pg_catalog"."default",
  "update_time" timestamp(6)
)
;
COMMENT ON COLUMN "public"."sys_oss_power_define"."power" IS '权限类别, read、write、share、delete';

-- ----------------------------
-- Records of sys_oss_power_define
-- ----------------------------
INSERT INTO "public"."sys_oss_power_define" VALUES (1, 'read', '0', NULL, NULL, NULL, NULL);
INSERT INTO "public"."sys_oss_power_define" VALUES (2, 'write', '0', NULL, NULL, NULL, NULL);
INSERT INTO "public"."sys_oss_power_define" VALUES (3, 'share', '0', NULL, NULL, NULL, NULL);
INSERT INTO "public"."sys_oss_power_define" VALUES (4, 'delete', '0', NULL, NULL, NULL, NULL);
INSERT INTO "public"."sys_oss_power_define" VALUES (5, 'manager', '0', NULL, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for sys_oss_power_relation
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_oss_power_relation";
CREATE TABLE "public"."sys_oss_power_relation" (
  "id" int8 NOT NULL,
  "bucket_dept_id" int8 NOT NULL,
  "power_define_id" int8 NOT NULL,
  "create_by" varchar(36) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "update_by" varchar(36) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "create_time" timestamp(6),
  "update_time" timestamp(6)
)
;

-- ----------------------------
-- Records of sys_oss_power_relation
-- ----------------------------

-- ----------------------------
-- Table structure for sys_post
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_post";
CREATE TABLE "public"."sys_post" (
  "post_id" int8 NOT NULL,
  "post_code" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "post_name" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "post_sort" int4 NOT NULL,
  "status" char(1) COLLATE "pg_catalog"."default" NOT NULL,
  "create_by" varchar(64) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "create_time" timestamp(6),
  "update_by" varchar(64) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "update_time" timestamp(6),
  "remark" varchar(500) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying
)
;
COMMENT ON COLUMN "public"."sys_post"."post_id" IS '岗位ID';
COMMENT ON COLUMN "public"."sys_post"."post_code" IS '岗位编码';
COMMENT ON COLUMN "public"."sys_post"."post_name" IS '岗位名称';
COMMENT ON COLUMN "public"."sys_post"."post_sort" IS '显示顺序';
COMMENT ON COLUMN "public"."sys_post"."status" IS '状态（0正常 1停用）';
COMMENT ON COLUMN "public"."sys_post"."create_by" IS '创建者';
COMMENT ON COLUMN "public"."sys_post"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."sys_post"."update_by" IS '更新者';
COMMENT ON COLUMN "public"."sys_post"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."sys_post"."remark" IS '备注';
COMMENT ON TABLE "public"."sys_post" IS '岗位信息表';

-- ----------------------------
-- Records of sys_post
-- ----------------------------

-- ----------------------------
-- Table structure for sys_robot_config
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_robot_config";
CREATE TABLE "public"."sys_robot_config" (
  "id" int8 NOT NULL,
  "robot_type" char(1) COLLATE "pg_catalog"."default",
  "webhook" varchar COLLATE "pg_catalog"."default",
  "remark" varchar COLLATE "pg_catalog"."default",
  "access_token" varchar COLLATE "pg_catalog"."default",
  "secret" varchar COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "update_time" timestamp(6),
  "create_by" varchar(64) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "update_by" varchar(64) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "name" varchar(24) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "robot_state" char(1) COLLATE "pg_catalog"."default" DEFAULT '1'::bpchar
)
;
COMMENT ON COLUMN "public"."sys_robot_config"."id" IS 'ID';
COMMENT ON COLUMN "public"."sys_robot_config"."robot_type" IS '机器人类型';
COMMENT ON COLUMN "public"."sys_robot_config"."webhook" IS '地址';
COMMENT ON COLUMN "public"."sys_robot_config"."remark" IS '备注';
COMMENT ON COLUMN "public"."sys_robot_config"."secret" IS '密匙';
COMMENT ON TABLE "public"."sys_robot_config" IS '机器人配置';

-- ----------------------------
-- Records of sys_robot_config
-- ----------------------------

-- ----------------------------
-- Table structure for sys_robot_msg
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_robot_msg";
CREATE TABLE "public"."sys_robot_msg" (
  "id" int8 NOT NULL,
  "msg_type" varchar(24) COLLATE "pg_catalog"."default",
  "msg_content" varchar COLLATE "pg_catalog"."default",
  "status" varchar COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "update_time" timestamp(6),
  "del_flag" char(1) COLLATE "pg_catalog"."default",
  "send_msg" text COLLATE "pg_catalog"."default",
  "send_result_code" int4,
  "msg_title" varchar(24) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "update_by" varchar(64) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "create_by" varchar(64) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying
)
;
COMMENT ON COLUMN "public"."sys_robot_msg"."msg_type" IS '消息内容';
COMMENT ON COLUMN "public"."sys_robot_msg"."msg_content" IS '消息内容';
COMMENT ON COLUMN "public"."sys_robot_msg"."status" IS '状态';

-- ----------------------------
-- Records of sys_robot_msg
-- ----------------------------

-- ----------------------------
-- Table structure for sys_robot_relevance_depart
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_robot_relevance_depart";
CREATE TABLE "public"."sys_robot_relevance_depart" (
  "id" int8 NOT NULL,
  "robot_id" int8,
  "depart_id" int8,
  "extend" varchar(100) COLLATE "pg_catalog"."default",
  "create_by" varchar(64) COLLATE "pg_catalog"."default",
  "update_by" varchar(64) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "update_time" timestamp(6)
)
;
COMMENT ON COLUMN "public"."sys_robot_relevance_depart"."id" IS '主键';
COMMENT ON COLUMN "public"."sys_robot_relevance_depart"."robot_id" IS '机器人id';
COMMENT ON COLUMN "public"."sys_robot_relevance_depart"."depart_id" IS '部门id';
COMMENT ON TABLE "public"."sys_robot_relevance_depart" IS '机器人配置关联部门表';

-- ----------------------------
-- Records of sys_robot_relevance_depart
-- ----------------------------

-- ----------------------------
-- Table structure for sys_role
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_role";
CREATE TABLE "public"."sys_role" (
  "role_id" int8 NOT NULL,
  "role_name" varchar(30) COLLATE "pg_catalog"."default" NOT NULL,
  "role_key" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "role_sort" int4 NOT NULL,
  "data_scope" char(1) COLLATE "pg_catalog"."default" DEFAULT '1'::bpchar,
  "menu_check_strictly" bool DEFAULT true,
  "dept_check_strictly" bool DEFAULT true,
  "status" char(1) COLLATE "pg_catalog"."default" NOT NULL,
  "del_flag" char(1) COLLATE "pg_catalog"."default" DEFAULT '0'::bpchar,
  "create_by" varchar(64) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "create_time" timestamp(6),
  "update_by" varchar(64) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "update_time" timestamp(6),
  "remark" varchar(500) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying
)
;
COMMENT ON COLUMN "public"."sys_role"."role_id" IS '角色ID';
COMMENT ON COLUMN "public"."sys_role"."role_name" IS '角色名称';
COMMENT ON COLUMN "public"."sys_role"."role_key" IS '角色权限字符串';
COMMENT ON COLUMN "public"."sys_role"."role_sort" IS '显示顺序';
COMMENT ON COLUMN "public"."sys_role"."data_scope" IS '数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限）';
COMMENT ON COLUMN "public"."sys_role"."menu_check_strictly" IS '菜单树选择项是否关联显示';
COMMENT ON COLUMN "public"."sys_role"."dept_check_strictly" IS '部门树选择项是否关联显示';
COMMENT ON COLUMN "public"."sys_role"."status" IS '角色状态（0正常 1停用）';
COMMENT ON COLUMN "public"."sys_role"."del_flag" IS '删除标志（0代表存在 2代表删除）';
COMMENT ON COLUMN "public"."sys_role"."create_by" IS '创建者';
COMMENT ON COLUMN "public"."sys_role"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."sys_role"."update_by" IS '更新者';
COMMENT ON COLUMN "public"."sys_role"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."sys_role"."remark" IS '备注';
COMMENT ON TABLE "public"."sys_role" IS '角色信息表';

-- ----------------------------
-- Records of sys_role
-- ----------------------------
INSERT INTO "public"."sys_role" VALUES (1, '超级管理员', 'admin', 1, '1', 't', 't', '0', '0', 'admin', '2024-11-01 07:28:33.067956', '', NULL, '超级管理员');
INSERT INTO "public"."sys_role" VALUES (2, '普通角色', 'common', 2, '2', 't', 't', '0', '0', 'admin', '2024-11-01 07:28:33.067956', '', NULL, '普通角色');

-- ----------------------------
-- Table structure for sys_role_dept
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_role_dept";
CREATE TABLE "public"."sys_role_dept" (
  "role_id" int8 NOT NULL,
  "dept_id" int8 NOT NULL
)
;
COMMENT ON COLUMN "public"."sys_role_dept"."role_id" IS '角色ID';
COMMENT ON COLUMN "public"."sys_role_dept"."dept_id" IS '部门ID';
COMMENT ON TABLE "public"."sys_role_dept" IS '角色和部门关联表';

-- ----------------------------
-- Records of sys_role_dept
-- ----------------------------
INSERT INTO "public"."sys_role_dept" VALUES (2, 100);
INSERT INTO "public"."sys_role_dept" VALUES (2, 101);
INSERT INTO "public"."sys_role_dept" VALUES (2, 105);

-- ----------------------------
-- Table structure for sys_role_menu
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_role_menu";
CREATE TABLE "public"."sys_role_menu" (
  "role_id" int8 NOT NULL,
  "menu_id" int8 NOT NULL
)
;
COMMENT ON COLUMN "public"."sys_role_menu"."role_id" IS '角色ID';
COMMENT ON COLUMN "public"."sys_role_menu"."menu_id" IS '菜单ID';
COMMENT ON TABLE "public"."sys_role_menu" IS '角色和菜单关联表';

-- ----------------------------
-- Records of sys_role_menu
-- ----------------------------

-- ----------------------------
-- Table structure for sys_topic_config
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_topic_config";
CREATE TABLE "public"."sys_topic_config" (
  "sys_topic_config_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "sys_name" varchar(100) COLLATE "pg_catalog"."default",
  "version" varchar COLLATE "pg_catalog"."default",
  "is_verify" int4 DEFAULT 0,
  "sys_title_icon" varchar COLLATE "pg_catalog"."default",
  "site_icon" varchar COLLATE "pg_catalog"."default",
  "sys_menubar_icon" varchar COLLATE "pg_catalog"."default",
  "page_background" varchar COLLATE "pg_catalog"."default",
  "left_login_pic" varchar COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."sys_topic_config"."sys_topic_config_id" IS '主题配置序号';
COMMENT ON COLUMN "public"."sys_topic_config"."sys_name" IS '系统名称';
COMMENT ON COLUMN "public"."sys_topic_config"."version" IS '版本';
COMMENT ON COLUMN "public"."sys_topic_config"."is_verify" IS '是否开启校验';
COMMENT ON COLUMN "public"."sys_topic_config"."sys_title_icon" IS '系统标题图标';
COMMENT ON COLUMN "public"."sys_topic_config"."site_icon" IS '站点图标';
COMMENT ON COLUMN "public"."sys_topic_config"."sys_menubar_icon" IS '系统菜单栏图标';
COMMENT ON COLUMN "public"."sys_topic_config"."page_background" IS '页面背景';
COMMENT ON COLUMN "public"."sys_topic_config"."left_login_pic" IS '登陆框左侧图';
COMMENT ON TABLE "public"."sys_topic_config" IS '系统主题配置';

-- ----------------------------
-- Records of sys_topic_config
-- ----------------------------
INSERT INTO "public"."sys_topic_config" VALUES ('1634017704298512385', '水务物联运维管理平台', '1.0', 1, 'http://*************:22429/home/<USER>', 'http://*************:22429/home/<USER>', 'http://*************:22429/home/<USER>', 'http://*************:22429/home/<USER>', 'http://*************:22429/home/<USER>');

-- ----------------------------
-- Table structure for sys_user
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_user";
CREATE TABLE "public"."sys_user" (
  "user_id" int8 NOT NULL,
  "dept_id" int8,
  "user_name" varchar(30) COLLATE "pg_catalog"."default" NOT NULL,
  "nick_name" varchar(30) COLLATE "pg_catalog"."default" NOT NULL,
  "user_type" varchar(10) COLLATE "pg_catalog"."default" DEFAULT 'sys_user'::character varying,
  "email" varchar(50) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "phonenumber" varchar(11) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "sex" char(1) COLLATE "pg_catalog"."default" DEFAULT '0'::bpchar,
  "avatar" varchar(100) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "password" varchar(100) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "status" char(1) COLLATE "pg_catalog"."default" DEFAULT '0'::bpchar,
  "del_flag" char(1) COLLATE "pg_catalog"."default" DEFAULT '0'::bpchar,
  "login_ip" varchar(128) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "login_date" timestamp(6),
  "create_by" varchar(64) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "create_time" timestamp(6),
  "update_by" varchar(64) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "update_time" timestamp(6),
  "remark" varchar(500) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying
)
;
COMMENT ON COLUMN "public"."sys_user"."user_id" IS '用户ID';
COMMENT ON COLUMN "public"."sys_user"."dept_id" IS '部门ID';
COMMENT ON COLUMN "public"."sys_user"."user_name" IS '用户账号';
COMMENT ON COLUMN "public"."sys_user"."nick_name" IS '用户昵称';
COMMENT ON COLUMN "public"."sys_user"."user_type" IS '用户类型（sys_user系统用户）';
COMMENT ON COLUMN "public"."sys_user"."email" IS '用户邮箱';
COMMENT ON COLUMN "public"."sys_user"."phonenumber" IS '手机号码';
COMMENT ON COLUMN "public"."sys_user"."sex" IS '用户性别（0男 1女 2未知）';
COMMENT ON COLUMN "public"."sys_user"."avatar" IS '头像地址';
COMMENT ON COLUMN "public"."sys_user"."password" IS '密码';
COMMENT ON COLUMN "public"."sys_user"."status" IS '帐号状态（0正常 1停用）';
COMMENT ON COLUMN "public"."sys_user"."del_flag" IS '删除标志（0代表存在 2代表删除）';
COMMENT ON COLUMN "public"."sys_user"."login_ip" IS '最后登陆IP';
COMMENT ON COLUMN "public"."sys_user"."login_date" IS '最后登陆时间';
COMMENT ON COLUMN "public"."sys_user"."create_by" IS '创建者';
COMMENT ON COLUMN "public"."sys_user"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."sys_user"."update_by" IS '更新者';
COMMENT ON COLUMN "public"."sys_user"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."sys_user"."remark" IS '备注';
COMMENT ON TABLE "public"."sys_user" IS '用户信息表';

-- ----------------------------
-- Records of sys_user
-- ----------------------------
INSERT INTO "public"."sys_user" VALUES (1, 100, 'admin', '超级管理员', 'sys_user', '<EMAIL>', '15888888888', '1', '', '$2a$10$4j2OsXY09zF2/udV9czJ9Our6XFzd5Pn08oZ8Wufn1czSZKu8Qw1O', '0', '0', '*************', '2024-11-04 11:00:17.414', 'admin', '2024-09-09 06:52:16', 'admin', '2024-11-04 11:00:17.414', '管理员');

-- ----------------------------
-- Table structure for sys_user_notice
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_user_notice";
CREATE TABLE "public"."sys_user_notice" (
  "user_notice_id" int8 NOT NULL,
  "notice_title" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "notice_type" char(1) COLLATE "pg_catalog"."default" NOT NULL,
  "notice_content" text COLLATE "pg_catalog"."default",
  "status" char(1) COLLATE "pg_catalog"."default",
  "create_by" varchar(64) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "update_by" varchar(64) COLLATE "pg_catalog"."default",
  "update_time" timestamp(6),
  "send_time" timestamp(6),
  "del_flag" char(1) COLLATE "pg_catalog"."default" DEFAULT '0'::bpchar,
  "notice_id" int8,
  "level" int4 DEFAULT 0,
  "nick_name" varchar(24) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying
)
;
COMMENT ON COLUMN "public"."sys_user_notice"."notice_id" IS '对应消息中的消息';
COMMENT ON COLUMN "public"."sys_user_notice"."level" IS '消息紧急情况';
COMMENT ON TABLE "public"."sys_user_notice" IS '用户收到的消息表';

-- ----------------------------
-- Records of sys_user_notice
-- ----------------------------

-- ----------------------------
-- Table structure for sys_user_post
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_user_post";
CREATE TABLE "public"."sys_user_post" (
  "user_id" int8 NOT NULL,
  "post_id" int8 NOT NULL
)
;
COMMENT ON COLUMN "public"."sys_user_post"."user_id" IS '用户ID';
COMMENT ON COLUMN "public"."sys_user_post"."post_id" IS '岗位ID';
COMMENT ON TABLE "public"."sys_user_post" IS '用户与岗位关联表';

-- ----------------------------
-- Records of sys_user_post
-- ----------------------------
INSERT INTO "public"."sys_user_post" VALUES (1, 1);
INSERT INTO "public"."sys_user_post" VALUES (2, 2);

-- ----------------------------
-- Table structure for sys_user_role
-- ----------------------------
DROP TABLE IF EXISTS "public"."sys_user_role";
CREATE TABLE "public"."sys_user_role" (
  "user_id" int8 NOT NULL,
  "role_id" int8 NOT NULL
)
;
COMMENT ON COLUMN "public"."sys_user_role"."user_id" IS '用户ID';
COMMENT ON COLUMN "public"."sys_user_role"."role_id" IS '角色ID';
COMMENT ON TABLE "public"."sys_user_role" IS '用户和角色关联表';

-- ----------------------------
-- Records of sys_user_role
-- ----------------------------
INSERT INTO "public"."sys_user_role" VALUES (1, 1);
INSERT INTO "public"."sys_user_role" VALUES (2, 2);

-- ----------------------------
-- Table structure for system_error_code
-- ----------------------------
DROP TABLE IF EXISTS "public"."system_error_code";
CREATE TABLE "public"."system_error_code" (
  "id" int8 NOT NULL DEFAULT nextval('system_error_code_seq'::regclass),
  "type" int2 NOT NULL,
  "application_name" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "code" int4 NOT NULL,
  "message" varchar(512) COLLATE "pg_catalog"."default" NOT NULL,
  "memo" varchar(512) COLLATE "pg_catalog"."default",
  "creator" varchar(64) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6) NOT NULL,
  "updater" varchar(64) COLLATE "pg_catalog"."default",
  "update_time" timestamp(6) NOT NULL,
  "deleted" int2 NOT NULL DEFAULT 0
)
;
COMMENT ON COLUMN "public"."system_error_code"."id" IS '错误码编号';
COMMENT ON COLUMN "public"."system_error_code"."type" IS '错误码类型';
COMMENT ON COLUMN "public"."system_error_code"."application_name" IS '应用名';
COMMENT ON COLUMN "public"."system_error_code"."code" IS '错误码编码';
COMMENT ON COLUMN "public"."system_error_code"."message" IS '错误码错误提示';
COMMENT ON COLUMN "public"."system_error_code"."memo" IS '备注';
COMMENT ON COLUMN "public"."system_error_code"."creator" IS '创建者';
COMMENT ON COLUMN "public"."system_error_code"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."system_error_code"."updater" IS '更新者';
COMMENT ON COLUMN "public"."system_error_code"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."system_error_code"."deleted" IS '是否删除';
COMMENT ON TABLE "public"."system_error_code" IS '错误码表';

-- ----------------------------
-- Records of system_error_code
-- ----------------------------

-- ----------------------------
-- Table structure for system_sms_log
-- ----------------------------
DROP TABLE IF EXISTS "public"."system_sms_log";
CREATE TABLE "public"."system_sms_log" (
  "id" int8 NOT NULL DEFAULT nextval('system_sms_log_seq'::regclass),
  "channel_id" int4,
  "channel_code" varchar(50) COLLATE "pg_catalog"."default",
  "template_code" varchar(50) COLLATE "pg_catalog"."default",
  "template_type" varchar(20) COLLATE "pg_catalog"."default",
  "template_content" varchar(255) COLLATE "pg_catalog"."default",
  "template_params" varchar(500) COLLATE "pg_catalog"."default",
  "api_template_id" varchar(50) COLLATE "pg_catalog"."default",
  "mobile" varchar(12) COLLATE "pg_catalog"."default",
  "user_id" int8,
  "user_type" int4,
  "send_status" int2,
  "send_time" date DEFAULT CURRENT_TIMESTAMP,
  "send_code" int4,
  "send_msg" varchar(500) COLLATE "pg_catalog"."default",
  "api_send_code" varchar(20) COLLATE "pg_catalog"."default",
  "api_send_msg" varchar(255) COLLATE "pg_catalog"."default",
  "api_request_id" varchar(50) COLLATE "pg_catalog"."default",
  "api_serial_no" varchar(255) COLLATE "pg_catalog"."default",
  "receive_status" int2,
  "receive_time" date DEFAULT CURRENT_TIMESTAMP,
  "api_receive_code" varchar(20) COLLATE "pg_catalog"."default",
  "api_receive_msg" varchar(255) COLLATE "pg_catalog"."default",
  "template_id" varchar(50) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."system_sms_log"."template_params" IS '模板属性';

-- ----------------------------
-- Records of system_sms_log
-- ----------------------------

-- ----------------------------
-- Table structure for wf_category
-- ----------------------------
DROP TABLE IF EXISTS "public"."wf_category";
CREATE TABLE "public"."wf_category" (
  "category_id" int8 NOT NULL,
  "category_name" varchar(64) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "code" varchar(64) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "remark" varchar(500) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "create_by" varchar(64) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "create_time" timestamp(6),
  "update_by" varchar(64) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "update_time" timestamp(6),
  "del_flag" char(1) COLLATE "pg_catalog"."default" DEFAULT '0'::bpchar
)
;
COMMENT ON COLUMN "public"."wf_category"."category_id" IS '流程分类id';
COMMENT ON COLUMN "public"."wf_category"."category_name" IS '流程分类名称';
COMMENT ON COLUMN "public"."wf_category"."code" IS '分类编码';
COMMENT ON COLUMN "public"."wf_category"."remark" IS '备注';
COMMENT ON COLUMN "public"."wf_category"."create_by" IS '创建者';
COMMENT ON COLUMN "public"."wf_category"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."wf_category"."update_by" IS '更新者';
COMMENT ON COLUMN "public"."wf_category"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."wf_category"."del_flag" IS '删除标志（0代表存在 2代表删除）';
COMMENT ON TABLE "public"."wf_category" IS '流程分类表';

-- ----------------------------
-- Records of wf_category
-- ----------------------------

-- ----------------------------
-- Table structure for wf_copy
-- ----------------------------
DROP TABLE IF EXISTS "public"."wf_copy";
CREATE TABLE "public"."wf_copy" (
  "copy_id" int8 NOT NULL,
  "title" varchar(255) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "process_id" varchar(64) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "process_name" varchar(255) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "category_id" varchar(255) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "deployment_id" varchar(64) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "instance_id" varchar(64) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "task_id" varchar(64) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "user_id" int8,
  "originator_id" int8,
  "originator_name" varchar(64) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "create_by" varchar(64) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "create_time" timestamp(6),
  "update_by" varchar(64) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "update_time" timestamp(6),
  "del_flag" char(1) COLLATE "pg_catalog"."default" DEFAULT '0'::bpchar
)
;
COMMENT ON COLUMN "public"."wf_copy"."copy_id" IS '抄送主键';
COMMENT ON COLUMN "public"."wf_copy"."title" IS '抄送标题';
COMMENT ON COLUMN "public"."wf_copy"."process_id" IS '流程主键';
COMMENT ON COLUMN "public"."wf_copy"."process_name" IS '流程名称';
COMMENT ON COLUMN "public"."wf_copy"."category_id" IS '流程分类主键';
COMMENT ON COLUMN "public"."wf_copy"."deployment_id" IS '部署主键';
COMMENT ON COLUMN "public"."wf_copy"."instance_id" IS '流程实例主键';
COMMENT ON COLUMN "public"."wf_copy"."task_id" IS '任务主键';
COMMENT ON COLUMN "public"."wf_copy"."user_id" IS '用户主键';
COMMENT ON COLUMN "public"."wf_copy"."originator_id" IS '发起人主键';
COMMENT ON COLUMN "public"."wf_copy"."originator_name" IS '发起人名称';
COMMENT ON COLUMN "public"."wf_copy"."create_by" IS '创建者';
COMMENT ON COLUMN "public"."wf_copy"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."wf_copy"."update_by" IS '更新者';
COMMENT ON COLUMN "public"."wf_copy"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."wf_copy"."del_flag" IS '删除标志（0代表存在 2代表删除）';
COMMENT ON TABLE "public"."wf_copy" IS '流程分类表';

-- ----------------------------
-- Records of wf_copy
-- ----------------------------

-- ----------------------------
-- Table structure for wf_deploy_form
-- ----------------------------
DROP TABLE IF EXISTS "public"."wf_deploy_form";
CREATE TABLE "public"."wf_deploy_form" (
  "deploy_id" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "form_key" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "node_key" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "form_name" varchar(64) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "node_name" varchar(255) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "content" text COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "public"."wf_deploy_form"."deploy_id" IS '流程实例主键';
COMMENT ON COLUMN "public"."wf_deploy_form"."form_key" IS '表单Key';
COMMENT ON COLUMN "public"."wf_deploy_form"."node_key" IS '节点Key';
COMMENT ON COLUMN "public"."wf_deploy_form"."form_name" IS '表单名称';
COMMENT ON COLUMN "public"."wf_deploy_form"."node_name" IS '节点名称';
COMMENT ON COLUMN "public"."wf_deploy_form"."content" IS '表单内容';
COMMENT ON TABLE "public"."wf_deploy_form" IS '流程实例关联表单';

-- ----------------------------
-- Records of wf_deploy_form
-- ----------------------------

-- ----------------------------
-- Table structure for wf_form
-- ----------------------------
DROP TABLE IF EXISTS "public"."wf_form";
CREATE TABLE "public"."wf_form" (
  "form_id" int8 NOT NULL,
  "form_name" varchar(64) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "content" text COLLATE "pg_catalog"."default",
  "create_by" varchar(64) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "create_time" timestamp(6),
  "update_by" varchar(64) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "update_time" timestamp(6),
  "remark" varchar(255) COLLATE "pg_catalog"."default",
  "del_flag" char(1) COLLATE "pg_catalog"."default" DEFAULT '0'::bpchar
)
;
COMMENT ON COLUMN "public"."wf_form"."form_id" IS '表单主键';
COMMENT ON COLUMN "public"."wf_form"."form_name" IS '表单名称';
COMMENT ON COLUMN "public"."wf_form"."content" IS '表单内容';
COMMENT ON COLUMN "public"."wf_form"."create_by" IS '创建者';
COMMENT ON COLUMN "public"."wf_form"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."wf_form"."update_by" IS '更新者';
COMMENT ON COLUMN "public"."wf_form"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."wf_form"."remark" IS '备注';
COMMENT ON COLUMN "public"."wf_form"."del_flag" IS '删除标志（0代表存在 2代表删除）';
COMMENT ON TABLE "public"."wf_form" IS '流程表单信息表';

-- ----------------------------
-- Records of wf_form
-- ----------------------------

-- ----------------------------
-- Function structure for cast_varchar_to_timestamp
-- ----------------------------
DROP FUNCTION IF EXISTS "public"."cast_varchar_to_timestamp"(varchar);
CREATE OR REPLACE FUNCTION "public"."cast_varchar_to_timestamp"(varchar)
  RETURNS "pg_catalog"."timestamptz" AS $BODY$
select to_timestamp($1, 'yyyy-mm-dd hh24:mi:ss');
$BODY$
  LANGUAGE sql VOLATILE STRICT
  COST 100;

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."act_evt_log_log_nr__seq"
OWNED BY "public"."act_evt_log"."log_nr_";
SELECT setval('"public"."act_evt_log_log_nr__seq"', 2, false);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "public"."act_hi_tsk_log_id__seq"
OWNED BY "public"."act_hi_tsk_log"."id_";
SELECT setval('"public"."act_hi_tsk_log_id__seq"', 2, false);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
SELECT setval('"public"."infra_api_access_log_id_seq"', 24, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
SELECT setval('"public"."infra_api_error_log_id_seq"', 3, true);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
SELECT setval('"public"."read_the_information_information_id_seq"', 2, false);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
SELECT setval('"public"."system_error_code_seq"', 2, false);

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
SELECT setval('"public"."system_sms_log_seq"', 2, false);

-- ----------------------------
-- Primary Key structure for table act_evt_log
-- ----------------------------
ALTER TABLE "public"."act_evt_log" ADD CONSTRAINT "act_evt_log_pkey" PRIMARY KEY ("log_nr_");

-- ----------------------------
-- Indexes structure for table act_ge_bytearray
-- ----------------------------
CREATE INDEX "act_idx_bytear_depl" ON "public"."act_ge_bytearray" USING btree (
  "deployment_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table act_ge_bytearray
-- ----------------------------
ALTER TABLE "public"."act_ge_bytearray" ADD CONSTRAINT "act_ge_bytearray_pkey" PRIMARY KEY ("id_");

-- ----------------------------
-- Primary Key structure for table act_ge_property
-- ----------------------------
ALTER TABLE "public"."act_ge_property" ADD CONSTRAINT "act_ge_property_pkey" PRIMARY KEY ("name_");

-- ----------------------------
-- Indexes structure for table act_hi_actinst
-- ----------------------------
CREATE INDEX "act_idx_hi_act_inst_end" ON "public"."act_hi_actinst" USING btree (
  "end_time_" "pg_catalog"."timestamp_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_hi_act_inst_exec" ON "public"."act_hi_actinst" USING btree (
  "execution_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "act_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_hi_act_inst_procinst" ON "public"."act_hi_actinst" USING btree (
  "proc_inst_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "act_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_hi_act_inst_start" ON "public"."act_hi_actinst" USING btree (
  "start_time_" "pg_catalog"."timestamp_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table act_hi_actinst
-- ----------------------------
ALTER TABLE "public"."act_hi_actinst" ADD CONSTRAINT "act_hi_actinst_pkey" PRIMARY KEY ("id_");

-- ----------------------------
-- Primary Key structure for table act_hi_attachment
-- ----------------------------
ALTER TABLE "public"."act_hi_attachment" ADD CONSTRAINT "act_hi_attachment_pkey" PRIMARY KEY ("id_");

-- ----------------------------
-- Primary Key structure for table act_hi_comment
-- ----------------------------
ALTER TABLE "public"."act_hi_comment" ADD CONSTRAINT "act_hi_comment_pkey" PRIMARY KEY ("id_");

-- ----------------------------
-- Indexes structure for table act_hi_detail
-- ----------------------------
CREATE INDEX "act_idx_hi_detail_act_inst" ON "public"."act_hi_detail" USING btree (
  "act_inst_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_hi_detail_name" ON "public"."act_hi_detail" USING btree (
  "name_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_hi_detail_proc_inst" ON "public"."act_hi_detail" USING btree (
  "proc_inst_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_hi_detail_task_id" ON "public"."act_hi_detail" USING btree (
  "task_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_hi_detail_time" ON "public"."act_hi_detail" USING btree (
  "time_" "pg_catalog"."timestamp_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table act_hi_detail
-- ----------------------------
ALTER TABLE "public"."act_hi_detail" ADD CONSTRAINT "act_hi_detail_pkey" PRIMARY KEY ("id_");

-- ----------------------------
-- Indexes structure for table act_hi_entitylink
-- ----------------------------
CREATE INDEX "act_idx_hi_ent_lnk_ref_scope" ON "public"."act_hi_entitylink" USING btree (
  "ref_scope_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "ref_scope_type_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "link_type_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_hi_ent_lnk_root_scope" ON "public"."act_hi_entitylink" USING btree (
  "root_scope_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "root_scope_type_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "link_type_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_hi_ent_lnk_scope" ON "public"."act_hi_entitylink" USING btree (
  "scope_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "scope_type_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "link_type_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_hi_ent_lnk_scope_def" ON "public"."act_hi_entitylink" USING btree (
  "scope_definition_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "scope_type_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "link_type_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table act_hi_entitylink
-- ----------------------------
ALTER TABLE "public"."act_hi_entitylink" ADD CONSTRAINT "act_hi_entitylink_pkey" PRIMARY KEY ("id_");

-- ----------------------------
-- Indexes structure for table act_hi_identitylink
-- ----------------------------
CREATE INDEX "act_idx_hi_ident_lnk_procinst" ON "public"."act_hi_identitylink" USING btree (
  "proc_inst_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_hi_ident_lnk_scope" ON "public"."act_hi_identitylink" USING btree (
  "scope_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "scope_type_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_hi_ident_lnk_scope_def" ON "public"."act_hi_identitylink" USING btree (
  "scope_definition_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "scope_type_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_hi_ident_lnk_sub_scope" ON "public"."act_hi_identitylink" USING btree (
  "sub_scope_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "scope_type_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_hi_ident_lnk_task" ON "public"."act_hi_identitylink" USING btree (
  "task_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_hi_ident_lnk_user" ON "public"."act_hi_identitylink" USING btree (
  "user_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table act_hi_identitylink
-- ----------------------------
ALTER TABLE "public"."act_hi_identitylink" ADD CONSTRAINT "act_hi_identitylink_pkey" PRIMARY KEY ("id_");

-- ----------------------------
-- Indexes structure for table act_hi_procinst
-- ----------------------------
CREATE INDEX "act_idx_hi_pro_i_buskey" ON "public"."act_hi_procinst" USING btree (
  "business_key_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_hi_pro_inst_end" ON "public"."act_hi_procinst" USING btree (
  "end_time_" "pg_catalog"."timestamp_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_hi_pro_super_procinst" ON "public"."act_hi_procinst" USING btree (
  "super_process_instance_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Uniques structure for table act_hi_procinst
-- ----------------------------
ALTER TABLE "public"."act_hi_procinst" ADD CONSTRAINT "act_hi_procinst_proc_inst_id__key" UNIQUE ("proc_inst_id_");

-- ----------------------------
-- Primary Key structure for table act_hi_procinst
-- ----------------------------
ALTER TABLE "public"."act_hi_procinst" ADD CONSTRAINT "act_hi_procinst_pkey" PRIMARY KEY ("id_");

-- ----------------------------
-- Indexes structure for table act_hi_taskinst
-- ----------------------------
CREATE INDEX "act_idx_hi_task_inst_procinst" ON "public"."act_hi_taskinst" USING btree (
  "proc_inst_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_hi_task_scope" ON "public"."act_hi_taskinst" USING btree (
  "scope_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "scope_type_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_hi_task_scope_def" ON "public"."act_hi_taskinst" USING btree (
  "scope_definition_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "scope_type_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_hi_task_sub_scope" ON "public"."act_hi_taskinst" USING btree (
  "sub_scope_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "scope_type_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table act_hi_taskinst
-- ----------------------------
ALTER TABLE "public"."act_hi_taskinst" ADD CONSTRAINT "act_hi_taskinst_pkey" PRIMARY KEY ("id_");

-- ----------------------------
-- Primary Key structure for table act_hi_tsk_log
-- ----------------------------
ALTER TABLE "public"."act_hi_tsk_log" ADD CONSTRAINT "act_hi_tsk_log_pkey" PRIMARY KEY ("id_");

-- ----------------------------
-- Indexes structure for table act_hi_varinst
-- ----------------------------
CREATE INDEX "act_idx_hi_procvar_exe" ON "public"."act_hi_varinst" USING btree (
  "execution_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_hi_procvar_name_type" ON "public"."act_hi_varinst" USING btree (
  "name_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "var_type_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_hi_procvar_proc_inst" ON "public"."act_hi_varinst" USING btree (
  "proc_inst_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_hi_procvar_task_id" ON "public"."act_hi_varinst" USING btree (
  "task_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_hi_var_scope_id_type" ON "public"."act_hi_varinst" USING btree (
  "scope_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "scope_type_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_hi_var_sub_id_type" ON "public"."act_hi_varinst" USING btree (
  "sub_scope_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "scope_type_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table act_hi_varinst
-- ----------------------------
ALTER TABLE "public"."act_hi_varinst" ADD CONSTRAINT "act_hi_varinst_pkey" PRIMARY KEY ("id_");

-- ----------------------------
-- Indexes structure for table act_procdef_info
-- ----------------------------
CREATE INDEX "act_idx_procdef_info_json" ON "public"."act_procdef_info" USING btree (
  "info_json_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_procdef_info_proc" ON "public"."act_procdef_info" USING btree (
  "proc_def_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Uniques structure for table act_procdef_info
-- ----------------------------
ALTER TABLE "public"."act_procdef_info" ADD CONSTRAINT "act_uniq_info_procdef" UNIQUE ("proc_def_id_");

-- ----------------------------
-- Primary Key structure for table act_procdef_info
-- ----------------------------
ALTER TABLE "public"."act_procdef_info" ADD CONSTRAINT "act_procdef_info_pkey" PRIMARY KEY ("id_");

-- ----------------------------
-- Primary Key structure for table act_re_deployment
-- ----------------------------
ALTER TABLE "public"."act_re_deployment" ADD CONSTRAINT "act_re_deployment_pkey" PRIMARY KEY ("id_");

-- ----------------------------
-- Indexes structure for table act_re_model
-- ----------------------------
CREATE INDEX "act_idx_model_deployment" ON "public"."act_re_model" USING btree (
  "deployment_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_model_source" ON "public"."act_re_model" USING btree (
  "editor_source_value_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_model_source_extra" ON "public"."act_re_model" USING btree (
  "editor_source_extra_value_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table act_re_model
-- ----------------------------
ALTER TABLE "public"."act_re_model" ADD CONSTRAINT "act_re_model_pkey" PRIMARY KEY ("id_");

-- ----------------------------
-- Uniques structure for table act_re_procdef
-- ----------------------------
ALTER TABLE "public"."act_re_procdef" ADD CONSTRAINT "act_uniq_procdef" UNIQUE ("key_", "version_", "derived_version_", "tenant_id_");

-- ----------------------------
-- Primary Key structure for table act_re_procdef
-- ----------------------------
ALTER TABLE "public"."act_re_procdef" ADD CONSTRAINT "act_re_procdef_pkey" PRIMARY KEY ("id_");

-- ----------------------------
-- Indexes structure for table act_ru_actinst
-- ----------------------------
CREATE INDEX "act_idx_ru_acti_end" ON "public"."act_ru_actinst" USING btree (
  "end_time_" "pg_catalog"."timestamp_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_ru_acti_exec" ON "public"."act_ru_actinst" USING btree (
  "execution_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_ru_acti_exec_act" ON "public"."act_ru_actinst" USING btree (
  "execution_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "act_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_ru_acti_proc" ON "public"."act_ru_actinst" USING btree (
  "proc_inst_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_ru_acti_proc_act" ON "public"."act_ru_actinst" USING btree (
  "proc_inst_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "act_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_ru_acti_start" ON "public"."act_ru_actinst" USING btree (
  "start_time_" "pg_catalog"."timestamp_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_ru_acti_task" ON "public"."act_ru_actinst" USING btree (
  "task_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table act_ru_actinst
-- ----------------------------
ALTER TABLE "public"."act_ru_actinst" ADD CONSTRAINT "act_ru_actinst_pkey" PRIMARY KEY ("id_");

-- ----------------------------
-- Indexes structure for table act_ru_deadletter_job
-- ----------------------------
CREATE INDEX "act_idx_deadletter_job_correlation_id" ON "public"."act_ru_deadletter_job" USING btree (
  "correlation_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_deadletter_job_custom_values_id" ON "public"."act_ru_deadletter_job" USING btree (
  "custom_values_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_deadletter_job_exception_stack_id" ON "public"."act_ru_deadletter_job" USING btree (
  "exception_stack_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_deadletter_job_execution_id" ON "public"."act_ru_deadletter_job" USING btree (
  "execution_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_deadletter_job_proc_def_id" ON "public"."act_ru_deadletter_job" USING btree (
  "proc_def_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_deadletter_job_process_instance_id" ON "public"."act_ru_deadletter_job" USING btree (
  "process_instance_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_djob_scope" ON "public"."act_ru_deadletter_job" USING btree (
  "scope_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "scope_type_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_djob_scope_def" ON "public"."act_ru_deadletter_job" USING btree (
  "scope_definition_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "scope_type_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_djob_sub_scope" ON "public"."act_ru_deadletter_job" USING btree (
  "sub_scope_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "scope_type_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table act_ru_deadletter_job
-- ----------------------------
ALTER TABLE "public"."act_ru_deadletter_job" ADD CONSTRAINT "act_ru_deadletter_job_pkey" PRIMARY KEY ("id_");

-- ----------------------------
-- Indexes structure for table act_ru_entitylink
-- ----------------------------
CREATE INDEX "act_idx_ent_lnk_ref_scope" ON "public"."act_ru_entitylink" USING btree (
  "ref_scope_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "ref_scope_type_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "link_type_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_ent_lnk_root_scope" ON "public"."act_ru_entitylink" USING btree (
  "root_scope_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "root_scope_type_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "link_type_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_ent_lnk_scope" ON "public"."act_ru_entitylink" USING btree (
  "scope_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "scope_type_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "link_type_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_ent_lnk_scope_def" ON "public"."act_ru_entitylink" USING btree (
  "scope_definition_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "scope_type_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "link_type_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table act_ru_entitylink
-- ----------------------------
ALTER TABLE "public"."act_ru_entitylink" ADD CONSTRAINT "act_ru_entitylink_pkey" PRIMARY KEY ("id_");

-- ----------------------------
-- Indexes structure for table act_ru_event_subscr
-- ----------------------------
CREATE INDEX "act_idx_event_subscr" ON "public"."act_ru_event_subscr" USING btree (
  "execution_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_event_subscr_config_" ON "public"."act_ru_event_subscr" USING btree (
  "configuration_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_event_subscr_scoperef_" ON "public"."act_ru_event_subscr" USING btree (
  "scope_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "scope_type_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table act_ru_event_subscr
-- ----------------------------
ALTER TABLE "public"."act_ru_event_subscr" ADD CONSTRAINT "act_ru_event_subscr_pkey" PRIMARY KEY ("id_");

-- ----------------------------
-- Indexes structure for table act_ru_execution
-- ----------------------------
CREATE INDEX "act_idx_exe_parent" ON "public"."act_ru_execution" USING btree (
  "parent_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_exe_procdef" ON "public"."act_ru_execution" USING btree (
  "proc_def_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_exe_procinst" ON "public"."act_ru_execution" USING btree (
  "proc_inst_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_exe_root" ON "public"."act_ru_execution" USING btree (
  "root_proc_inst_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_exe_super" ON "public"."act_ru_execution" USING btree (
  "super_exec_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_exec_buskey" ON "public"."act_ru_execution" USING btree (
  "business_key_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_exec_ref_id_" ON "public"."act_ru_execution" USING btree (
  "reference_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table act_ru_execution
-- ----------------------------
ALTER TABLE "public"."act_ru_execution" ADD CONSTRAINT "act_ru_execution_pkey" PRIMARY KEY ("id_");

-- ----------------------------
-- Indexes structure for table act_ru_external_job
-- ----------------------------
CREATE INDEX "act_idx_ejob_scope" ON "public"."act_ru_external_job" USING btree (
  "scope_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "scope_type_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_ejob_scope_def" ON "public"."act_ru_external_job" USING btree (
  "scope_definition_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "scope_type_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_ejob_sub_scope" ON "public"."act_ru_external_job" USING btree (
  "sub_scope_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "scope_type_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_external_job_correlation_id" ON "public"."act_ru_external_job" USING btree (
  "correlation_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_external_job_custom_values_id" ON "public"."act_ru_external_job" USING btree (
  "custom_values_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_external_job_exception_stack_id" ON "public"."act_ru_external_job" USING btree (
  "exception_stack_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table act_ru_external_job
-- ----------------------------
ALTER TABLE "public"."act_ru_external_job" ADD CONSTRAINT "act_ru_external_job_pkey" PRIMARY KEY ("id_");

-- ----------------------------
-- Primary Key structure for table act_ru_history_job
-- ----------------------------
ALTER TABLE "public"."act_ru_history_job" ADD CONSTRAINT "act_ru_history_job_pkey" PRIMARY KEY ("id_");

-- ----------------------------
-- Indexes structure for table act_ru_identitylink
-- ----------------------------
CREATE INDEX "act_idx_athrz_procedef" ON "public"."act_ru_identitylink" USING btree (
  "proc_def_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_ident_lnk_group" ON "public"."act_ru_identitylink" USING btree (
  "group_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_ident_lnk_scope" ON "public"."act_ru_identitylink" USING btree (
  "scope_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "scope_type_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_ident_lnk_scope_def" ON "public"."act_ru_identitylink" USING btree (
  "scope_definition_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "scope_type_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_ident_lnk_sub_scope" ON "public"."act_ru_identitylink" USING btree (
  "sub_scope_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "scope_type_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_ident_lnk_user" ON "public"."act_ru_identitylink" USING btree (
  "user_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_idl_procinst" ON "public"."act_ru_identitylink" USING btree (
  "proc_inst_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_tskass_task" ON "public"."act_ru_identitylink" USING btree (
  "task_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table act_ru_identitylink
-- ----------------------------
ALTER TABLE "public"."act_ru_identitylink" ADD CONSTRAINT "act_ru_identitylink_pkey" PRIMARY KEY ("id_");

-- ----------------------------
-- Indexes structure for table act_ru_job
-- ----------------------------
CREATE INDEX "act_idx_job_correlation_id" ON "public"."act_ru_job" USING btree (
  "correlation_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_job_custom_values_id" ON "public"."act_ru_job" USING btree (
  "custom_values_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_job_exception_stack_id" ON "public"."act_ru_job" USING btree (
  "exception_stack_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_job_execution_id" ON "public"."act_ru_job" USING btree (
  "execution_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_job_proc_def_id" ON "public"."act_ru_job" USING btree (
  "proc_def_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_job_process_instance_id" ON "public"."act_ru_job" USING btree (
  "process_instance_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_job_scope" ON "public"."act_ru_job" USING btree (
  "scope_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "scope_type_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_job_scope_def" ON "public"."act_ru_job" USING btree (
  "scope_definition_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "scope_type_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_job_sub_scope" ON "public"."act_ru_job" USING btree (
  "sub_scope_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "scope_type_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table act_ru_job
-- ----------------------------
ALTER TABLE "public"."act_ru_job" ADD CONSTRAINT "act_ru_job_pkey" PRIMARY KEY ("id_");

-- ----------------------------
-- Indexes structure for table act_ru_suspended_job
-- ----------------------------
CREATE INDEX "act_idx_sjob_scope" ON "public"."act_ru_suspended_job" USING btree (
  "scope_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "scope_type_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_sjob_scope_def" ON "public"."act_ru_suspended_job" USING btree (
  "scope_definition_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "scope_type_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_sjob_sub_scope" ON "public"."act_ru_suspended_job" USING btree (
  "sub_scope_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "scope_type_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_suspended_job_correlation_id" ON "public"."act_ru_suspended_job" USING btree (
  "correlation_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_suspended_job_custom_values_id" ON "public"."act_ru_suspended_job" USING btree (
  "custom_values_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_suspended_job_exception_stack_id" ON "public"."act_ru_suspended_job" USING btree (
  "exception_stack_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_suspended_job_execution_id" ON "public"."act_ru_suspended_job" USING btree (
  "execution_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_suspended_job_proc_def_id" ON "public"."act_ru_suspended_job" USING btree (
  "proc_def_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_suspended_job_process_instance_id" ON "public"."act_ru_suspended_job" USING btree (
  "process_instance_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table act_ru_suspended_job
-- ----------------------------
ALTER TABLE "public"."act_ru_suspended_job" ADD CONSTRAINT "act_ru_suspended_job_pkey" PRIMARY KEY ("id_");

-- ----------------------------
-- Indexes structure for table act_ru_task
-- ----------------------------
CREATE INDEX "act_idx_task_create" ON "public"."act_ru_task" USING btree (
  "create_time_" "pg_catalog"."timestamp_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_task_exec" ON "public"."act_ru_task" USING btree (
  "execution_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_task_procdef" ON "public"."act_ru_task" USING btree (
  "proc_def_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_task_procinst" ON "public"."act_ru_task" USING btree (
  "proc_inst_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_task_scope" ON "public"."act_ru_task" USING btree (
  "scope_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "scope_type_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_task_scope_def" ON "public"."act_ru_task" USING btree (
  "scope_definition_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "scope_type_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_task_sub_scope" ON "public"."act_ru_task" USING btree (
  "sub_scope_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "scope_type_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table act_ru_task
-- ----------------------------
ALTER TABLE "public"."act_ru_task" ADD CONSTRAINT "act_ru_task_pkey" PRIMARY KEY ("id_");

-- ----------------------------
-- Indexes structure for table act_ru_timer_job
-- ----------------------------
CREATE INDEX "act_idx_timer_job_correlation_id" ON "public"."act_ru_timer_job" USING btree (
  "correlation_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_timer_job_custom_values_id" ON "public"."act_ru_timer_job" USING btree (
  "custom_values_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_timer_job_duedate" ON "public"."act_ru_timer_job" USING btree (
  "duedate_" "pg_catalog"."timestamp_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_timer_job_exception_stack_id" ON "public"."act_ru_timer_job" USING btree (
  "exception_stack_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_timer_job_execution_id" ON "public"."act_ru_timer_job" USING btree (
  "execution_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_timer_job_proc_def_id" ON "public"."act_ru_timer_job" USING btree (
  "proc_def_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_timer_job_process_instance_id" ON "public"."act_ru_timer_job" USING btree (
  "process_instance_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_tjob_scope" ON "public"."act_ru_timer_job" USING btree (
  "scope_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "scope_type_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_tjob_scope_def" ON "public"."act_ru_timer_job" USING btree (
  "scope_definition_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "scope_type_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_tjob_sub_scope" ON "public"."act_ru_timer_job" USING btree (
  "sub_scope_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "scope_type_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table act_ru_timer_job
-- ----------------------------
ALTER TABLE "public"."act_ru_timer_job" ADD CONSTRAINT "act_ru_timer_job_pkey" PRIMARY KEY ("id_");

-- ----------------------------
-- Indexes structure for table act_ru_variable
-- ----------------------------
CREATE INDEX "act_idx_ru_var_scope_id_type" ON "public"."act_ru_variable" USING btree (
  "scope_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "scope_type_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_ru_var_sub_id_type" ON "public"."act_ru_variable" USING btree (
  "sub_scope_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "scope_type_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_var_bytearray" ON "public"."act_ru_variable" USING btree (
  "bytearray_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_var_exe" ON "public"."act_ru_variable" USING btree (
  "execution_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_var_procinst" ON "public"."act_ru_variable" USING btree (
  "proc_inst_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "act_idx_variable_task_id" ON "public"."act_ru_variable" USING btree (
  "task_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table act_ru_variable
-- ----------------------------
ALTER TABLE "public"."act_ru_variable" ADD CONSTRAINT "act_ru_variable_pkey" PRIMARY KEY ("id_");

-- ----------------------------
-- Indexes structure for table flw_channel_definition
-- ----------------------------
CREATE UNIQUE INDEX "act_idx_channel_def_uniq" ON "public"."flw_channel_definition" USING btree (
  "key_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "version_" "pg_catalog"."int4_ops" ASC NULLS LAST,
  "tenant_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table flw_channel_definition
-- ----------------------------
ALTER TABLE "public"."flw_channel_definition" ADD CONSTRAINT "FLW_CHANNEL_DEFINITION_pkey" PRIMARY KEY ("id_");

-- ----------------------------
-- Primary Key structure for table flw_ev_databasechangeloglock
-- ----------------------------
ALTER TABLE "public"."flw_ev_databasechangeloglock" ADD CONSTRAINT "flw_ev_databasechangeloglock_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table flw_event_definition
-- ----------------------------
CREATE UNIQUE INDEX "act_idx_event_def_uniq" ON "public"."flw_event_definition" USING btree (
  "key_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "version_" "pg_catalog"."int4_ops" ASC NULLS LAST,
  "tenant_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table flw_event_definition
-- ----------------------------
ALTER TABLE "public"."flw_event_definition" ADD CONSTRAINT "FLW_EVENT_DEFINITION_pkey" PRIMARY KEY ("id_");

-- ----------------------------
-- Primary Key structure for table flw_event_deployment
-- ----------------------------
ALTER TABLE "public"."flw_event_deployment" ADD CONSTRAINT "FLW_EVENT_DEPLOYMENT_pkey" PRIMARY KEY ("id_");

-- ----------------------------
-- Primary Key structure for table flw_event_resource
-- ----------------------------
ALTER TABLE "public"."flw_event_resource" ADD CONSTRAINT "FLW_EVENT_RESOURCE_pkey" PRIMARY KEY ("id_");

-- ----------------------------
-- Primary Key structure for table flw_ru_batch
-- ----------------------------
ALTER TABLE "public"."flw_ru_batch" ADD CONSTRAINT "flw_ru_batch_pkey" PRIMARY KEY ("id_");

-- ----------------------------
-- Indexes structure for table flw_ru_batch_part
-- ----------------------------
CREATE INDEX "flw_idx_batch_part" ON "public"."flw_ru_batch_part" USING btree (
  "batch_id_" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table flw_ru_batch_part
-- ----------------------------
ALTER TABLE "public"."flw_ru_batch_part" ADD CONSTRAINT "flw_ru_batch_part_pkey" PRIMARY KEY ("id_");

-- ----------------------------
-- Primary Key structure for table gen_table
-- ----------------------------
ALTER TABLE "public"."gen_table" ADD CONSTRAINT "gen_table_pk" PRIMARY KEY ("table_id");

-- ----------------------------
-- Primary Key structure for table gen_table_column
-- ----------------------------
ALTER TABLE "public"."gen_table_column" ADD CONSTRAINT "gen_table_column_pk" PRIMARY KEY ("column_id");

-- ----------------------------
-- Primary Key structure for table infra_api_access_log
-- ----------------------------
ALTER TABLE "public"."infra_api_access_log" ADD CONSTRAINT "infra_api_access_log_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table infra_api_error_log
-- ----------------------------
ALTER TABLE "public"."infra_api_error_log" ADD CONSTRAINT "infra_api_error_log_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table read_the_information
-- ----------------------------
ALTER TABLE "public"."read_the_information" ADD CONSTRAINT "read_the_information_pkey" PRIMARY KEY ("information_id");

-- ----------------------------
-- Primary Key structure for table sms_request_ebtity
-- ----------------------------
ALTER TABLE "public"."sms_request_ebtity" ADD CONSTRAINT "sms_request_ebtity_pkey" PRIMARY KEY ("out_id");

-- ----------------------------
-- Primary Key structure for table sys_config
-- ----------------------------
ALTER TABLE "public"."sys_config" ADD CONSTRAINT "sys_config_pk" PRIMARY KEY ("config_id");

-- ----------------------------
-- Primary Key structure for table sys_dept
-- ----------------------------
ALTER TABLE "public"."sys_dept" ADD CONSTRAINT "sys_dept_pk" PRIMARY KEY ("dept_id");

-- ----------------------------
-- Primary Key structure for table sys_dict_data
-- ----------------------------
ALTER TABLE "public"."sys_dict_data" ADD CONSTRAINT "sys_dict_data_pk" PRIMARY KEY ("dict_code");

-- ----------------------------
-- Primary Key structure for table sys_dict_type
-- ----------------------------
ALTER TABLE "public"."sys_dict_type" ADD CONSTRAINT "sys_dict_type_pkey" PRIMARY KEY ("dict_id");

-- ----------------------------
-- Primary Key structure for table sys_logininfor
-- ----------------------------
ALTER TABLE "public"."sys_logininfor" ADD CONSTRAINT "sys_logininfor_pk" PRIMARY KEY ("info_id");

-- ----------------------------
-- Primary Key structure for table sys_notice
-- ----------------------------
ALTER TABLE "public"."sys_notice" ADD CONSTRAINT "sys_notice_pkey" PRIMARY KEY ("notice_id");

-- ----------------------------
-- Primary Key structure for table sys_notice_dept
-- ----------------------------
ALTER TABLE "public"."sys_notice_dept" ADD CONSTRAINT "sys_notice_dept_pkey" PRIMARY KEY ("notice_id", "dept_id");

-- ----------------------------
-- Primary Key structure for table sys_notice_template
-- ----------------------------
ALTER TABLE "public"."sys_notice_template" ADD CONSTRAINT "sys_template_pkey" PRIMARY KEY ("template_id");

-- ----------------------------
-- Primary Key structure for table sys_oper_log
-- ----------------------------
ALTER TABLE "public"."sys_oper_log" ADD CONSTRAINT "sys_oper_log_pk" PRIMARY KEY ("oper_id");

-- ----------------------------
-- Primary Key structure for table sys_oss
-- ----------------------------
ALTER TABLE "public"."sys_oss" ADD CONSTRAINT "sys_oss_pk" PRIMARY KEY ("oss_id");

-- ----------------------------
-- Primary Key structure for table sys_oss_bucket
-- ----------------------------
ALTER TABLE "public"."sys_oss_bucket" ADD CONSTRAINT "sys_oss_bucket_pk" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table sys_oss_bucket_dept
-- ----------------------------
ALTER TABLE "public"."sys_oss_bucket_dept" ADD CONSTRAINT "sys_bucket_dept_pk" PRIMARY KEY ("bucket_id", "dept_id");

-- ----------------------------
-- Primary Key structure for table sys_oss_bucket_open
-- ----------------------------
ALTER TABLE "public"."sys_oss_bucket_open" ADD CONSTRAINT "sys_oss_bucket_open_pk" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table sys_oss_bucket_sec
-- ----------------------------
ALTER TABLE "public"."sys_oss_bucket_sec" ADD CONSTRAINT "sys_oss_bucket_bs" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table sys_oss_bucket_upload
-- ----------------------------
ALTER TABLE "public"."sys_oss_bucket_upload" ADD CONSTRAINT "sys_oss_bucket_upload_pk" PRIMARY KEY ("bucket_upload_id");

-- ----------------------------
-- Primary Key structure for table sys_oss_config
-- ----------------------------
ALTER TABLE "public"."sys_oss_config" ADD CONSTRAINT "sys_oss_config_pk" PRIMARY KEY ("oss_config_id");

-- ----------------------------
-- Primary Key structure for table sys_oss_power_define
-- ----------------------------
ALTER TABLE "public"."sys_oss_power_define" ADD CONSTRAINT "sys_oss_bucket_power_pk" PRIMARY KEY ("power");

-- ----------------------------
-- Primary Key structure for table sys_oss_power_relation
-- ----------------------------
ALTER TABLE "public"."sys_oss_power_relation" ADD CONSTRAINT "sys_oss_bucket_relation_pk" PRIMARY KEY ("bucket_dept_id", "power_define_id");

-- ----------------------------
-- Primary Key structure for table sys_post
-- ----------------------------
ALTER TABLE "public"."sys_post" ADD CONSTRAINT "sys_post_pk" PRIMARY KEY ("post_id");

-- ----------------------------
-- Primary Key structure for table sys_robot_config
-- ----------------------------
ALTER TABLE "public"."sys_robot_config" ADD CONSTRAINT "sys_robot_config_pk" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table sys_robot_msg
-- ----------------------------
ALTER TABLE "public"."sys_robot_msg" ADD CONSTRAINT "sys_robot_msg_pk" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table sys_robot_relevance_depart
-- ----------------------------
ALTER TABLE "public"."sys_robot_relevance_depart" ADD CONSTRAINT "sys_robot_relevance_depart_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table sys_role
-- ----------------------------
ALTER TABLE "public"."sys_role" ADD CONSTRAINT "sys_role_pk" PRIMARY KEY ("role_id");

-- ----------------------------
-- Primary Key structure for table sys_role_dept
-- ----------------------------
ALTER TABLE "public"."sys_role_dept" ADD CONSTRAINT "sys_role_dept_pk" PRIMARY KEY ("role_id", "dept_id");

-- ----------------------------
-- Primary Key structure for table sys_role_menu
-- ----------------------------
ALTER TABLE "public"."sys_role_menu" ADD CONSTRAINT "sys_role_menu_pk" PRIMARY KEY ("role_id", "menu_id");

-- ----------------------------
-- Primary Key structure for table sys_topic_config
-- ----------------------------
ALTER TABLE "public"."sys_topic_config" ADD CONSTRAINT "sys_topic_config_pk" PRIMARY KEY ("sys_topic_config_id");

-- ----------------------------
-- Primary Key structure for table sys_user
-- ----------------------------
ALTER TABLE "public"."sys_user" ADD CONSTRAINT "sys_user_pk" PRIMARY KEY ("user_id");

-- ----------------------------
-- Primary Key structure for table sys_user_notice
-- ----------------------------
ALTER TABLE "public"."sys_user_notice" ADD CONSTRAINT "sys_user_notice_pkey" PRIMARY KEY ("user_notice_id");

-- ----------------------------
-- Primary Key structure for table sys_user_post
-- ----------------------------
ALTER TABLE "public"."sys_user_post" ADD CONSTRAINT "sys_user_post_pk" PRIMARY KEY ("user_id", "post_id");

-- ----------------------------
-- Primary Key structure for table sys_user_role
-- ----------------------------
ALTER TABLE "public"."sys_user_role" ADD CONSTRAINT "sys_user_role_pk" PRIMARY KEY ("user_id", "role_id");

-- ----------------------------
-- Primary Key structure for table system_error_code
-- ----------------------------
ALTER TABLE "public"."system_error_code" ADD CONSTRAINT "system_error_code_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table system_sms_log
-- ----------------------------
ALTER TABLE "public"."system_sms_log" ADD CONSTRAINT "system_sms_log_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table wf_category
-- ----------------------------
ALTER TABLE "public"."wf_category" ADD CONSTRAINT "wf_category_pk" PRIMARY KEY ("category_id");

-- ----------------------------
-- Primary Key structure for table wf_copy
-- ----------------------------
ALTER TABLE "public"."wf_copy" ADD CONSTRAINT "wf_copy_pk" PRIMARY KEY ("copy_id");

-- ----------------------------
-- Primary Key structure for table wf_deploy_form
-- ----------------------------
ALTER TABLE "public"."wf_deploy_form" ADD CONSTRAINT "wf_deploy_form_pk" PRIMARY KEY ("deploy_id", "form_key", "node_key");

-- ----------------------------
-- Primary Key structure for table wf_form
-- ----------------------------
ALTER TABLE "public"."wf_form" ADD CONSTRAINT "wf_form_pk" PRIMARY KEY ("form_id");

-- ----------------------------
-- Foreign Keys structure for table act_ge_bytearray
-- ----------------------------
ALTER TABLE "public"."act_ge_bytearray" ADD CONSTRAINT "act_fk_bytearr_depl" FOREIGN KEY ("deployment_id_") REFERENCES "public"."act_re_deployment" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table act_procdef_info
-- ----------------------------
ALTER TABLE "public"."act_procdef_info" ADD CONSTRAINT "act_fk_info_json_ba" FOREIGN KEY ("info_json_id_") REFERENCES "public"."act_ge_bytearray" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "public"."act_procdef_info" ADD CONSTRAINT "act_fk_info_procdef" FOREIGN KEY ("proc_def_id_") REFERENCES "public"."act_re_procdef" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table act_re_model
-- ----------------------------
ALTER TABLE "public"."act_re_model" ADD CONSTRAINT "act_fk_model_deployment" FOREIGN KEY ("deployment_id_") REFERENCES "public"."act_re_deployment" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "public"."act_re_model" ADD CONSTRAINT "act_fk_model_source" FOREIGN KEY ("editor_source_value_id_") REFERENCES "public"."act_ge_bytearray" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "public"."act_re_model" ADD CONSTRAINT "act_fk_model_source_extra" FOREIGN KEY ("editor_source_extra_value_id_") REFERENCES "public"."act_ge_bytearray" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table act_ru_deadletter_job
-- ----------------------------
ALTER TABLE "public"."act_ru_deadletter_job" ADD CONSTRAINT "act_fk_deadletter_job_custom_values" FOREIGN KEY ("custom_values_id_") REFERENCES "public"."act_ge_bytearray" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "public"."act_ru_deadletter_job" ADD CONSTRAINT "act_fk_deadletter_job_exception" FOREIGN KEY ("exception_stack_id_") REFERENCES "public"."act_ge_bytearray" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "public"."act_ru_deadletter_job" ADD CONSTRAINT "act_fk_deadletter_job_execution" FOREIGN KEY ("execution_id_") REFERENCES "public"."act_ru_execution" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "public"."act_ru_deadletter_job" ADD CONSTRAINT "act_fk_deadletter_job_proc_def" FOREIGN KEY ("proc_def_id_") REFERENCES "public"."act_re_procdef" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "public"."act_ru_deadletter_job" ADD CONSTRAINT "act_fk_deadletter_job_process_instance" FOREIGN KEY ("process_instance_id_") REFERENCES "public"."act_ru_execution" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table act_ru_event_subscr
-- ----------------------------
ALTER TABLE "public"."act_ru_event_subscr" ADD CONSTRAINT "act_fk_event_exec" FOREIGN KEY ("execution_id_") REFERENCES "public"."act_ru_execution" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table act_ru_execution
-- ----------------------------
ALTER TABLE "public"."act_ru_execution" ADD CONSTRAINT "act_fk_exe_parent" FOREIGN KEY ("parent_id_") REFERENCES "public"."act_ru_execution" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "public"."act_ru_execution" ADD CONSTRAINT "act_fk_exe_procdef" FOREIGN KEY ("proc_def_id_") REFERENCES "public"."act_re_procdef" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "public"."act_ru_execution" ADD CONSTRAINT "act_fk_exe_procinst" FOREIGN KEY ("proc_inst_id_") REFERENCES "public"."act_ru_execution" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "public"."act_ru_execution" ADD CONSTRAINT "act_fk_exe_super" FOREIGN KEY ("super_exec_") REFERENCES "public"."act_ru_execution" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table act_ru_external_job
-- ----------------------------
ALTER TABLE "public"."act_ru_external_job" ADD CONSTRAINT "act_fk_external_job_custom_values" FOREIGN KEY ("custom_values_id_") REFERENCES "public"."act_ge_bytearray" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "public"."act_ru_external_job" ADD CONSTRAINT "act_fk_external_job_exception" FOREIGN KEY ("exception_stack_id_") REFERENCES "public"."act_ge_bytearray" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table act_ru_identitylink
-- ----------------------------
ALTER TABLE "public"."act_ru_identitylink" ADD CONSTRAINT "act_fk_athrz_procedef" FOREIGN KEY ("proc_def_id_") REFERENCES "public"."act_re_procdef" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "public"."act_ru_identitylink" ADD CONSTRAINT "act_fk_idl_procinst" FOREIGN KEY ("proc_inst_id_") REFERENCES "public"."act_ru_execution" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "public"."act_ru_identitylink" ADD CONSTRAINT "act_fk_tskass_task" FOREIGN KEY ("task_id_") REFERENCES "public"."act_ru_task" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table act_ru_job
-- ----------------------------
ALTER TABLE "public"."act_ru_job" ADD CONSTRAINT "act_fk_job_custom_values" FOREIGN KEY ("custom_values_id_") REFERENCES "public"."act_ge_bytearray" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "public"."act_ru_job" ADD CONSTRAINT "act_fk_job_exception" FOREIGN KEY ("exception_stack_id_") REFERENCES "public"."act_ge_bytearray" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "public"."act_ru_job" ADD CONSTRAINT "act_fk_job_execution" FOREIGN KEY ("execution_id_") REFERENCES "public"."act_ru_execution" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "public"."act_ru_job" ADD CONSTRAINT "act_fk_job_proc_def" FOREIGN KEY ("proc_def_id_") REFERENCES "public"."act_re_procdef" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "public"."act_ru_job" ADD CONSTRAINT "act_fk_job_process_instance" FOREIGN KEY ("process_instance_id_") REFERENCES "public"."act_ru_execution" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table act_ru_suspended_job
-- ----------------------------
ALTER TABLE "public"."act_ru_suspended_job" ADD CONSTRAINT "act_fk_suspended_job_custom_values" FOREIGN KEY ("custom_values_id_") REFERENCES "public"."act_ge_bytearray" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "public"."act_ru_suspended_job" ADD CONSTRAINT "act_fk_suspended_job_exception" FOREIGN KEY ("exception_stack_id_") REFERENCES "public"."act_ge_bytearray" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "public"."act_ru_suspended_job" ADD CONSTRAINT "act_fk_suspended_job_execution" FOREIGN KEY ("execution_id_") REFERENCES "public"."act_ru_execution" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "public"."act_ru_suspended_job" ADD CONSTRAINT "act_fk_suspended_job_proc_def" FOREIGN KEY ("proc_def_id_") REFERENCES "public"."act_re_procdef" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "public"."act_ru_suspended_job" ADD CONSTRAINT "act_fk_suspended_job_process_instance" FOREIGN KEY ("process_instance_id_") REFERENCES "public"."act_ru_execution" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table act_ru_task
-- ----------------------------
ALTER TABLE "public"."act_ru_task" ADD CONSTRAINT "act_fk_task_exe" FOREIGN KEY ("execution_id_") REFERENCES "public"."act_ru_execution" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "public"."act_ru_task" ADD CONSTRAINT "act_fk_task_procdef" FOREIGN KEY ("proc_def_id_") REFERENCES "public"."act_re_procdef" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "public"."act_ru_task" ADD CONSTRAINT "act_fk_task_procinst" FOREIGN KEY ("proc_inst_id_") REFERENCES "public"."act_ru_execution" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table act_ru_timer_job
-- ----------------------------
ALTER TABLE "public"."act_ru_timer_job" ADD CONSTRAINT "act_fk_timer_job_custom_values" FOREIGN KEY ("custom_values_id_") REFERENCES "public"."act_ge_bytearray" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "public"."act_ru_timer_job" ADD CONSTRAINT "act_fk_timer_job_exception" FOREIGN KEY ("exception_stack_id_") REFERENCES "public"."act_ge_bytearray" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "public"."act_ru_timer_job" ADD CONSTRAINT "act_fk_timer_job_execution" FOREIGN KEY ("execution_id_") REFERENCES "public"."act_ru_execution" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "public"."act_ru_timer_job" ADD CONSTRAINT "act_fk_timer_job_proc_def" FOREIGN KEY ("proc_def_id_") REFERENCES "public"."act_re_procdef" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "public"."act_ru_timer_job" ADD CONSTRAINT "act_fk_timer_job_process_instance" FOREIGN KEY ("process_instance_id_") REFERENCES "public"."act_ru_execution" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table act_ru_variable
-- ----------------------------
ALTER TABLE "public"."act_ru_variable" ADD CONSTRAINT "act_fk_var_bytearray" FOREIGN KEY ("bytearray_id_") REFERENCES "public"."act_ge_bytearray" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "public"."act_ru_variable" ADD CONSTRAINT "act_fk_var_exe" FOREIGN KEY ("execution_id_") REFERENCES "public"."act_ru_execution" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;
ALTER TABLE "public"."act_ru_variable" ADD CONSTRAINT "act_fk_var_procinst" FOREIGN KEY ("proc_inst_id_") REFERENCES "public"."act_ru_execution" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;

-- ----------------------------
-- Foreign Keys structure for table flw_ru_batch_part
-- ----------------------------
ALTER TABLE "public"."flw_ru_batch_part" ADD CONSTRAINT "flw_fk_batch_part_parent" FOREIGN KEY ("batch_id_") REFERENCES "public"."flw_ru_batch" ("id_") ON DELETE NO ACTION ON UPDATE NO ACTION;
