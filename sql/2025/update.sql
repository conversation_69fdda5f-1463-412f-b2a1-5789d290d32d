alter table public.sys_menu
    add column froze int4 default 1;
comment on column public.sys_menu.froze is '基础菜单不允许被删除 0：不允许 1允许';

-----流程相关
drop table if exists wf_form_template;
create table wf_form_template
(
    template_id      bigint primary key not null,
    name             varchar(32)  default null,
    template_desc    varchar(128) default null,
    oss_id           bigint       default null,
    template_content text         default null,
    create_by        varchar(64)  default ''::varchar,
    create_time      timestamp,
    update_by        varchar(64)  default ''::varchar,
    update_time      timestamp,
    del_flag         char(1)      default '0'::bpchar
);

comment on table wf_form_template is '流程表单模板';
comment on column wf_form_template.template_id is '模板ID';
comment on column wf_form_template.template_desc is '模板描述';
comment on column wf_form_template.oss_id is '文件ID';
comment on column wf_form_template.template_content is '表单内容';
