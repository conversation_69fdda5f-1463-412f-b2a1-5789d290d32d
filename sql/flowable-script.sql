
-- ----------------------------
-- wf_category流程分类表
-- ----------------------------
drop table if exists wf_category;
create table wf_category
(
    category_id    int8 not null,
    category_name  varchar(64)   default ''::varchar,
    code           varchar(64)   default ''::varchar,
    remark         varchar(500)  default ''::varchar,
    create_by      varchar(64)   default ''::varchar,
    create_time    timestamp,
    update_by      varchar(64)   default ''::varchar,
    update_time    timestamp,
    del_flag       char(1)       default '0'::bpchar,
    constraint wf_category_pk primary key (category_id)
);

comment on table wf_category is '流程分类表';
comment on column wf_category.category_id   is '流程分类id';
comment on column wf_category.category_name is '流程分类名称';
comment on column wf_category.code          is '分类编码';
comment on column wf_category.remark        is '备注';
comment on column wf_category.create_by     is '创建者';
comment on column wf_category.create_time   is '创建时间';
comment on column wf_category.update_by     is '更新者';
comment on column wf_category.update_time   is '更新时间';
comment on column wf_category.del_flag      is '删除标志（0代表存在 2代表删除）';

-- ----------------------------
-- wf_copy流程抄送表
-- ----------------------------
drop table if exists wf_copy;
create table wf_copy
(
    copy_id          int8 not null,
    title            varchar(255)  default ''::varchar,
    process_id       varchar(64)   default ''::varchar,
    process_name     varchar(255)  default ''::varchar,
    category_id      varchar(255)  default ''::varchar,
    deployment_id    varchar(64)   default ''::varchar,
    instance_id      varchar(64)   default ''::varchar,
    task_id          varchar(64)   default ''::varchar,
    user_id          int8,
    originator_id    int8,
    originator_name  varchar(64)   default ''::varchar,
    create_by        varchar(64)   default ''::varchar,
    create_time      timestamp,
    update_by        varchar(64)   default ''::varchar,
    update_time      timestamp,
    del_flag         char(1)       default '0'::bpchar,
    constraint wf_copy_pk primary key (copy_id)
);

comment on table wf_copy is '流程分类表';
comment on column wf_copy.copy_id         is '抄送主键';
comment on column wf_copy.title           is '抄送标题';
comment on column wf_copy.process_id      is '流程主键';
comment on column wf_copy.process_name    is '流程名称';
comment on column wf_copy.category_id     is '流程分类主键';
comment on column wf_copy.deployment_id   is '部署主键';
comment on column wf_copy.instance_id     is '流程实例主键';
comment on column wf_copy.task_id         is '任务主键';
comment on column wf_copy.user_id         is '用户主键';
comment on column wf_copy.originator_id   is '发起人主键';
comment on column wf_copy.originator_name is '发起人名称';
comment on column wf_copy.create_by       is '创建者';
comment on column wf_copy.create_time     is '创建时间';
comment on column wf_copy.update_by       is '更新者';
comment on column wf_copy.update_time     is '更新时间';
comment on column wf_copy.del_flag        is '删除标志（0代表存在 2代表删除）';

-- 字符串自动转时间 避免框架时间查询报错问题
create or replace function cast_varchar_to_timestamp(varchar) returns timestamptz as $$
select to_timestamp($1, 'yyyy-mm-dd hh24:mi:ss');
$$ language sql strict ;

create cast (varchar as timestamptz) with function cast_varchar_to_timestamp as IMPLICIT;


-- ----------------------------
-- wf_form流程表单信息表
-- ----------------------------
drop table if exists wf_form;
create table wf_form
(
    form_id       int8 not null,
    form_name     varchar(64)   default ''::varchar,
    content       text,
    create_by     varchar(64)   default ''::varchar,
    create_time   timestamp,
    update_by     varchar(64)   default ''::varchar,
    update_time   timestamp,
    remark        varchar(255),
    del_flag      char(1)       default '0'::bpchar,
    constraint wf_form_pk primary key (form_id)
);

comment on table wf_form is '流程表单信息表';
comment on column wf_form.form_id     is '表单主键';
comment on column wf_form.form_name   is '表单名称';
comment on column wf_form.content     is '表单内容';
comment on column wf_form.create_by   is '创建者';
comment on column wf_form.create_time is '创建时间';
comment on column wf_form.update_by   is '更新者';
comment on column wf_form.update_time is '更新时间';
comment on column wf_form.remark      is '备注';
comment on column wf_form.del_flag    is '删除标志（0代表存在 2代表删除）';

-- ----------------------------
-- wf_deploy_form流程实例关联表
-- ----------------------------
drop table if exists wf_deploy_form;
create table wf_deploy_form
(
    deploy_id varchar(64)   not null,
    form_key  varchar(64)   not null,
    node_key  varchar(64)   not null,
    form_name varchar(64)   default ''::varchar,
    node_name varchar(255)  default ''::varchar,
    content   text,
    constraint wf_deploy_form_pk primary key (deploy_id, form_key, node_key)
);

comment on table wf_deploy_form is '流程实例关联表单';
comment on column wf_deploy_form.deploy_id is '流程实例主键';
comment on column wf_deploy_form.form_key  is '表单Key';
comment on column wf_deploy_form.node_key  is '节点Key';
comment on column wf_deploy_form.form_name is '表单名称';
comment on column wf_deploy_form.node_name is '节点名称';
comment on column wf_deploy_form.content   is '表单内容';

insert into sys_dict_type values(25, '流程状态', 'wf_process_status',   '0', 'admin', now(), '', null, '工作流程状态');

insert into sys_dict_data values(40, 1,  '进行中',   'running',    'wf_process_status',   '',   'primary',  'N', '0', 'admin', now(), '', null, '进行中状态');
insert into sys_dict_data values(41, 2,  '已终止',   'terminated', 'wf_process_status',   '',   'danger',   'N', '0', 'admin', now(), '', null, '已终止状态');
insert into sys_dict_data values(42, 3,  '已完成',   'completed',  'wf_process_status',   '',   'success',  'N', '0', 'admin', now(), '', null, '已完成状态');
insert into sys_dict_data values(43, 4,  '已取消',   'canceled',   'wf_process_status',   '',   'warning',  'N', '0', 'admin', now(), '', null, '已取消状态');

