-- -------------- 2023-03-15 cq start-------------
-- 配置数据范围
insert into sys_dict_type(dict_id, dict_name, dict_type, status, create_by, create_time, remark, system_property)
values (11, '数据权限范围', 'sys_data_scope', 0, 'admin', now(), '数据权限范围列表', 0);

insert into sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, is_default, status, create_by,
                           create_time, remark)
values (30, 1, '全部数据权限', 1, 'sys_data_scope', 'N', 0, 'admin', now(), '全部数据权限');


insert into sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, is_default, status, create_by,
                           create_time, remark)
values (31, 2, '自定义数据权限', 2, 'sys_data_scope', 'N', 0, 'admin', now(), '自定义数据权限');


insert into sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, is_default, status, create_by,
                           create_time, remark)
values (32, 3, '本部门数据权限', 3, 'sys_data_scope', 'N', 0, 'admin', now(), '本部门数据权限');


insert into sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, is_default, status, create_by,
                           create_time, remark)
values (33, 4, '本部门及以下数据权限', 4, 'sys_data_scope', 'N', 0, 'admin', now(), '本部门及以下数据权限');


insert into sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, is_default, status, create_by,
                           create_time, remark)
values (34, 5, '仅本人数据权限', 5, 'sys_data_scope', 'N', 0, 'admin', now(), '仅本人数据权限');

------------------ 2023-03-15 cq end-------------------------

-- -------------- 2023-03-17 cq start-------------

insert into sys_dict_type(dict_id, dict_name, dict_type, status, create_by, create_time, remark, system_property)
values (12, '权限开启开关', 'sys_power_switch', 0, 'admin', now(), '是否开启全局权限，0开启，1关闭', 0);

------------------ 2023-03-17 cq end-------------------------

-- -------------- 2023-03-17 cq start-------------

insert into sys_menu(menu_id, menu_name, parent_id, order_num, "path", component, is_frame, is_cache, menu_type,
                     visible, status, perms, icon,
                     create_by, create_time, update_by, update_time, is_home, app, is_app)
values ('20076', '用户管理_import', 2007, 1, '/system/setting/user/import', 'System/User/Index/import', 1, 0, 'F', 0, 0,
        'system:user:index:import', 'Dot', 'admin', now(), 'admin', now(), 1, 1, 1);



insert into sys_menu(menu_id, menu_name, parent_id, order_num, "path", component, is_frame, is_cache, menu_type,
                     visible, status, perms, icon,
                     create_by, create_time, update_by, update_time, is_home, app, is_app)
values ('20077', '用户管理_template', 2007, 1, '/system/setting/user/importTemplate', 'System/User/Index/template', 1,
        0, 'F', 0, 0, 'system:user:index:import', 'Dot', 'admin', now(), 'admin', now(), 1, 1, 1);



insert into sys_menu(menu_id, menu_name, parent_id, order_num, "path", component, is_frame, is_cache, menu_type,
                     visible, status, perms, icon,
                     create_by, create_time, update_by, update_time, is_home, app, is_app)
values ('20078', '用户管理_reset', 2007, 1, '/system/setting/user/resetPwd', 'System/User/Index/restpwd', 1, 0, 'F', 0,
        0, 'system:user:index:resetPwd', 'Dot', 'admin', now(), 'admin', now(), 1, 1, 1);



-- -------------- 2023-03-17 cq end-------------

-- -------------- 2023-03-20 cq start-------------

insert into sys_menu(menu_id, menu_name, parent_id, order_num, "path", component, is_frame, is_cache, menu_type,
                     visible, status, perms, icon,
                     create_by, create_time, update_by, update_time, is_home, app, is_app)
values ('155400586', '文件列表_download', 1554005889752735746, 1, '/system/file/list/download',
        'System/File/FileList/Index/download', 1, 0, 'F', 0, 0, 'system:file:filelist:index:download', 'Dot', 'admin',
        now(), 'admin', now(), 1, 1, 1);

insert into sys_menu(menu_id, menu_name, parent_id, order_num, "path", component, is_frame, is_cache, menu_type,
                     visible, status, perms, icon,
                     create_by, create_time, update_by, update_time, is_home, app, is_app)
values ('155259446', '状态修改_status', 1552594437741445122, 1, '/system/file/config/status',
        'System/File/FileConfig/Index/status', 1, 0, 'F', 0, 0, 'system:file:fileconfig:index:status', 'Dot', 'admin',
        now(), 'admin', now(), 1, 1, 1);

-- -------------- 2023-03-20 cq end-------------


-- -------------- 2023-03-22 cq start-------------
-- 消息表模板表
CREATE TABLE public.sys_notice_template
(
    template_id       int8         NOT NULL,
    template_content  varchar(500) NOT NULL,
    template_keywords varchar(128) NULL,
    create_by         varchar(64)  NULL,
    create_time       timestamp    NULL,
    update_by         varchar(64)  NULL,
    update_time       timestamp    NULL,
    CONSTRAINT sys_template_pkey PRIMARY KEY (template_id)
);
COMMENT ON TABLE sys_notice_template IS '消息模板表';
COMMENT ON COLUMN sys_notice_template.template_content IS '模板内容';
COMMENT ON COLUMN sys_notice_template.template_keywords IS '模板类型对应表';

-- 消息表新增字段
ALTER TABLE sys_notice
    ADD COLUMN send_time timestamp default null;
ALTER TABLE sys_notice
    ADD COLUMN type integer default null;
ALTER TABLE sys_notice
    ADD COLUMN send_status integer default null;

-- 消息部门表 消息表 1：N 部门表sys_dept
CREATE TABLE public.sys_notice_dept
(
    notice_id int8 NOT NULL,
    dept_id   int8 NOT NULL,
    CONSTRAINT sys_notice_dept_pkey PRIMARY KEY (notice_id, dept_id)
);

ALTER TABLE sys_notice
    ADD COLUMN template_id integer default null;
COMMENT ON COLUMN sys_notice.template_id IS '模板表id';
-- -------------- 2023-03-22 cq end-------------

-- -------------- 2023-03-23 cq start-------------
-- 表sys_notice定义为管理中心管理的消息表，sys_user_notice为用户收到的消息表
COMMENT ON TABLE sys_notice IS '管理员管理消息中心表';

CREATE TABLE public.sys_user_notice
(
    user_notice_id int8        NOT NULL,
    notice_title   varchar(50) NOT NULL,
    notice_type    bpchar(1)   NOT NULL,
    notice_content text        NULL,
    status         bpchar(1)   NULL,
    create_by      varchar(64) NULL,
    create_time    timestamp   NULL,
    update_by      varchar(64) NULL,
    update_time    timestamp   NULL,
    send_time      timestamp   NULL,
    del_flag       bpchar(1)   NULL DEFAULT '0'::bpchar,
    CONSTRAINT sys_user_notice_pkey PRIMARY KEY (user_notice_id)
);
COMMENT ON TABLE sys_user_notice IS '用户收到的消息表';

ALTER TABLE sys_notice
    ADD COLUMN template_values varchar(255) default null;
ALTER TABLE sys_notice
    ADD COLUMN level int4 default 0;
COMMENT ON COLUMN sys_notice.level IS '消息紧急情况';
ALTER TABLE sys_notice
    ALTER COLUMN template_id TYPE int8;

ALTER TABLE sys_user_notice
    ADD COLUMN notice_id int8 default null;
COMMENT ON COLUMN sys_user_notice.notice_id IS '对应消息中的消息';
-- -------------- 2023-03-24 cq end-------------

-- -------------- 2023-03-25 cq start-------------
ALTER TABLE sys_user_notice
    ADD COLUMN level int4 default 0;
COMMENT ON COLUMN sys_user_notice.level IS '消息紧急情况';

-- -------------- 2023-03-25 cq end-------------

ALTER TABLE sys_notice
    ADD COLUMN del_flag bpchar(1) default '0';
ALTER TABLE sys_notice
    ADD COLUMN nick_name varchar(24) default null;
ALTER TABLE sys_user_notice
    ADD COLUMN nick_name varchar(24) default null;


------------------------2023-03-29 cq start ------------------
insert into sys_dict_type(dict_id, dict_name, dict_type, status, create_by, create_time, remark, system_property)
values ((select count(1) + 1 from sys_dict_type), '紧急情况等级', 'sys_notice_level', 0, 'admin', now(),
        '紧急情况等级列表', 0);


insert into sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, is_default, status, create_by,
                           create_time, remark)
values ((select count(1) + 1 from sys_dict_data), 1, '普通', 0, 'sys_notice_level', 'N', 0, 'admin', now(),
        '紧急情况等级-普通');


insert into sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, is_default, status, create_by,
                           create_time, remark)
values ((select count(1) + 1 from sys_dict_data), 1, '重要', 1, 'sys_notice_level', 'N', 0, 'admin', now(),
        '紧急情况等级-重要');

insert into sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, is_default, status, create_by,
                           create_time, remark)
values ((select count(1) + 1 from sys_dict_data), 1, '紧急', 2, 'sys_notice_level', 'N', 0, 'admin', now(),
        '紧急情况等级-紧急');
------------------------2023-03-29 cq end ------------------


------------------------2023-03-29 lp start-----------------
------------------------sys_robot_config 机器人配置表-------
CREATE TABLE public.sys_robot_config
(
    id           int8      NOT NULL, -- ID
    robot_type   bpchar(1) NULL,     -- 机器人类型
    webhook      varchar   NULL,     -- 地址
    remark       varchar   NULL,     -- 备注
    access_token varchar   NULL,
    secret       varchar   NULL,     -- 密匙
    create_time  timestamp NULL,
    update_time  timestamp NULL,
    CONSTRAINT sys_robot_config_pk PRIMARY KEY (id)
);
COMMENT ON TABLE public.sys_robot_config IS '机器人配置';

-- Column comments

COMMENT ON COLUMN public.sys_robot_config.id IS 'ID';
COMMENT ON COLUMN public.sys_robot_config."type" IS '机器人类型';
COMMENT ON COLUMN public.sys_robot_config.webhook IS '地址';
COMMENT ON COLUMN public.sys_robot_config.remark IS '备注';
COMMENT ON COLUMN public.sys_robot_config.secret IS '密匙';
------------------------2023-03-29 lp end-----------------

------------------------2023-03-29 lp start-----------------
------------------------sys_robot_msg 机器人消息表-------

CREATE TABLE public.sys_robot_msg
(
    id          int8      NOT NULL,
    msg_type    bpchar(1) NULL, -- 消息内容
    msg_content varchar   NULL, -- 消息内容
    status      varchar   NULL, -- 状态
    create_time timestamp NULL,
    update_time timestamp NULL,
    del_flag    bpchar(1) NULL,
    CONSTRAINT sys_robot_msg_pk PRIMARY KEY (id)
);

-- Column comments

COMMENT ON COLUMN public.sys_robot_msg."type" IS '消息内容';
COMMENT ON COLUMN public.sys_robot_msg."content" IS '消息内容';
COMMENT ON COLUMN public.sys_robot_msg.status IS '状态';
------------------------2023-03-29 lp end-----------------
------------------------2023-03-29 cq start-----------------

ALTER TABLE sys_robot_msg
    ADD COLUMN send_msg text default null;
ALTER TABLE sys_robot_msg
    ADD COLUMN send_result_code int4 default null;
ALTER TABLE sys_robot_msg
    ADD COLUMN msg_title varchar(24) default null;
ALTER TABLE sys_robot_config
    ADD COLUMN create_by varchar(64) default null;
ALTER TABLE sys_robot_config
    ADD COLUMN update_by varchar(64) default null;
ALTER TABLE sys_robot_msg
    ADD COLUMN create_by varchar(64) default null;
ALTER TABLE sys_robot_msg
    ADD COLUMN update_by varchar(64) default null;
ALTER TABLE sys_robot_config
    ADD COLUMN name varchar(24) default null;


insert into sys_dict_type(dict_id, dict_name, dict_type, status, create_by, create_time, remark, system_property)
values ((select count(1) + 1 from sys_dict_type), '机器人消息类型', 'sys_webhook_type', 0, 'admin', now(),
        '机器人消息类型', 0);


insert into sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, is_default, status, create_by,
                           create_time, remark)
values ((select count(1) + 1 from sys_dict_data), 0, '文字', 'text', 'sys_webhook_type', 'Y', 0, 'admin', now(),
        '机器人消息类型-文字');


insert into sys_dict_data (dict_code, dict_sort, dict_label, dict_value, dict_type, is_default, status, create_by,
                           create_time, remark)
values ((select count(1) + 1 from sys_dict_data), 1, 'markdown', 'markdown', 'sys_webhook_type', 'N', 0, 'admin', now(),
        '机器人消息类型-markdown');

ALTER TABLE sys_robot_msg
    ALTER COLUMN msg_type type varchar(24);
------------------------2023-03-29 cq end-----------------
------------------------2023-03-31 cq start-----------------
-- 桶表
CREATE TABLE public.sys_oss_bucket
(
    id            int8         NOT NULL,
    nick_name     varchar(24)  not null, -- 创建云盘时候的名称，不代表minio桶名称
    -- bucket_name   varchar(24)  NULL,     -- minio真实的名称
    bucket_type   int4      default 0,   -- 1:minio桶 2：minio文件夹 3：minio文件
    -- minio_path    text         null,     -- minio路径
    original_name varchar(120) null,     -- 原名
    file_suffix   varchar(9)   null,     -- 文件后缀名
    parent_id     int8      default 0,   -- 父节点
    bucket_id     int8      default 0,   -- 所属桶节点
    expired_time  timestamp    null,     -- 过期时间
    tags          varchar(32)  null,     -- 标签
    owner_id      int8         not null, -- 所有者 sys_user_id
    status        varchar      NULL,     -- 状态
    create_time   timestamp    NULL,
    update_time   timestamp    NULL,
    create_by     varchar(12)  NULL,
    update_by     varchar(12)  NULL,
    del_flag      bpchar(1) default '0',
    CONSTRAINT sys_oss_bucket_pk PRIMARY KEY (id)
);

COMMENT ON COLUMN public.sys_oss_bucket.nick_name IS '系统桶名';
-- COMMENT ON COLUMN public.sys_oss_bucket.bucket_name IS 'minio桶真实名称';
COMMENT ON COLUMN public.sys_oss_bucket.bucket_type IS '类型1:minio桶 2：minio文件夹 3：minio文件';
-- COMMENT ON COLUMN public.sys_oss_bucket.minio_path IS 'minio路径';
COMMENT ON COLUMN public.sys_oss_bucket.original_name IS '原始名称';
COMMENT ON COLUMN public.sys_oss_bucket.file_suffix IS '文件后缀名';
COMMENT ON COLUMN public.sys_oss_bucket.parent_id IS '父节点';
COMMENT ON COLUMN public.sys_oss_bucket.bucket_id IS '所属桶节点';
COMMENT ON COLUMN public.sys_oss_bucket.expired_time IS '过期时间';
COMMENT ON COLUMN public.sys_oss_bucket.tags IS '标签';

-- 桶描述表
CREATE TABLE public.sys_oss_bucket_sec
(
    id          int8        NOT NULL,
    bucket_id   int8        not null, -- 桶id
    bucket_name varchar(24) NULL,     -- minio真实的名称
    bucket_type int4 default 0,       -- 1:minio桶 2：minio文件夹 3：minio文件
    minio_path  text        null,     -- minio路径
    create_time timestamp   NULL,
    update_time timestamp   NULL,
    create_by   varchar(12) NULL,
    update_by   varchar(12) NULL,
    CONSTRAINT sys_oss_bucket_bs PRIMARY KEY (id)
);
COMMENT ON COLUMN public.sys_oss_bucket_sec.bucket_id IS '所属桶ID';
COMMENT ON COLUMN public.sys_oss_bucket_sec.bucket_name IS 'minio桶真实名称';
COMMENT ON COLUMN public.sys_oss_bucket_sec.bucket_type IS '类型1:minio桶 2：minio文件夹 3：minio文件';
COMMENT ON COLUMN public.sys_oss_bucket_sec.minio_path IS 'minio路径';

-- 桶的权限表
CREATE TABLE public.sys_oss_bucket_dept
(
    id        int8 not null,
    dept_id   int8 not null,
    bucket_id int8 not null,
    CONSTRAINT sys_bucket_dept_pk PRIMARY KEY (bucket_id, dept_id)
);

COMMENT ON COLUMN public.sys_oss_bucket_dept.bucket_id IS '所属桶节点';
COMMENT ON COLUMN public.sys_oss_bucket_dept.dept_id IS '部门id';

-- 桶权限，静态，先配置好
CREATE TABLE public.sys_oss_power_define
(
    id     int8       not null,
    power  varchar(8) not null,   -- 读 read、写 write、分享 share、删除 delete
    status bpchar(1) default '0', --状态
    CONSTRAINT sys_oss_bucket_power_pk PRIMARY KEY (power)
);

COMMENT ON COLUMN public.sys_oss_power_define.power IS '权限类别, read、write、share、delete';

-- 桶-部门-所拥有的权限
CREATE TABLE public.sys_oss_power_relation
(
    id              int8 not null,
    bucket_dept_id  int8 not null,
    power_define_id int8 not null,
    CONSTRAINT sys_oss_bucket_relation_pk PRIMARY KEY (bucket_dept_id, power_define_id)
);

-- 对外公布的文件配置
CREATE TABLE public.sys_oss_bucket_open
(
    id           int8        not null,
    bucket_id    int8        not null, -- 具体的文件id，这个可以重复
    expired_time timestamp   null,     -- 过期时间
    create_by    varchar(24) not null, -- 由谁创建
    create_time  timestamp   not null, -- 创建时间
    update_time  timestamp   NULL,
    update_by    varchar(12) NULL,
    CONSTRAINT sys_oss_bucket_open_pk PRIMARY KEY (id)
);
COMMENT ON COLUMN public.sys_oss_bucket_open.bucket_id IS '所属桶节点';
COMMENT ON COLUMN public.sys_oss_bucket_open.expired_time IS '过期时间';

-- 操作历史表

-- Column comments

ALTER TABLE sys_oss_bucket
    ADD COLUMN operators varchar(12) null;
COMMENT ON COLUMN sys_oss_bucket.operators IS '运营商';

insert into sys_oss_power_define (id, power, status)
values (1, 'read', '0'),
       (2, 'write', '0'),
       (3, 'share', '0'),
       (4, 'delete', '0'),
       (5, 'manager', '0');
------------------------2023-03-31 cq end-----------------
ALTER TABLE sys_oss_bucket_dept
    add COLUMN create_by varchar(36) default null;
ALTER TABLE sys_oss_bucket_dept
    add COLUMN update_by varchar(36) default null;
ALTER TABLE sys_oss_bucket_dept
    add COLUMN create_time timestamp default null;
ALTER TABLE sys_oss_bucket_dept
    add COLUMN update_time timestamp default null;

ALTER TABLE sys_oss_power_relation
    add COLUMN create_by varchar(36) default null;
ALTER TABLE sys_oss_power_relation
    add COLUMN update_by varchar(36) default null;
ALTER TABLE sys_oss_power_relation
    add COLUMN create_time timestamp default null;
ALTER TABLE sys_oss_power_relation
    add COLUMN update_time timestamp default null;


ALTER TABLE sys_oss_bucket
    ALTER COLUMN nick_name type varchar(120);

------------------------2023-04-04 cq start-----------------

ALTER TABLE sys_oss_bucket
    ADD COLUMN personal int2 default 0;

------------------------2023-04-04 cq end-----------------

------------------------2023-04-04 cq start-----------------

ALTER TABLE sys_oss_bucket_open
    ADD COLUMN del_flag bpchar(1) default '0';

------------------------2023-04-04 cq end-----------------
------------------------2023-04-07 cq start-----------------
CREATE TABLE public.sys_oss_bucket_upload
(
    bucket_upload_id int8        not null,
    bucket_id        int8        not null,  -- 具体的桶文件保证桶信息复用
    hashcode         varchar(64) not null,  -- 文件hashcode
    total_count      int4        not null,  -- 文件总分片数量
    finish           boolean default false, -- 是否完成
    upload_id        int8        not null,-- minio的upload_id
    create_by        varchar(24) not null,  -- 由谁创建
    create_time      timestamp   not null,  -- 创建时间
    update_time      timestamp   NULL,
    update_by        varchar(12) NULL,
    CONSTRAINT sys_oss_bucket_upload_pk PRIMARY KEY (bucket_upload_id)
);


ALTER TABLE sys_oss_bucket_upload ADD COLUMN del_flag bpchar(1) default '0';
ALTER TABLE sys_oss_bucket_upload ADD COLUMN minio_key varchar(120) default null;
ALTER TABLE sys_oss_bucket_upload ADD COLUMN minio_bucket varchar(30) default null;
ALTER TABLE sys_oss_bucket_upload ADD COLUMN file_name varchar(240) default null;
ALTER TABLE sys_oss_bucket_upload ALTER COLUMN upload_id type varchar(90);
------------------------2023-04-07cq end-----------------


------------------------2023-04-26 cq start-----------------------
-- 插入错误日志列表
insert into sys_menu(menu_id , menu_name,parent_id ,menu_type,status,perms,create_by,create_time,update_by,update_time)
values('163769620933233862', '错误日志列表','1554292052300374018','F','0','system:operlog:index:list','admin',now(),'admin',now());

-- 菜单列表
insert into sys_menu(menu_id , menu_name,parent_id ,menu_type,status,perms,create_by,create_time,update_by,update_time)
values('1637699959157555205', '菜单列表','2006','F','0','system:menu:index:list','admin',now(),'admin',now());

-- 缓存监控
insert into sys_menu(menu_id , menu_name,parent_id ,menu_type,status,perms,create_by,create_time,update_by,update_time)
values('20156', '缓存监控_list','2015','F','0','system:monitor:cachemonitor:index:list','admin',now(),'admin',now());

-- 服务监控(后端无代码）
insert into sys_menu(menu_id , menu_name,parent_id ,menu_type,status,perms,create_by,create_time,update_by,update_time)
values('20146', '服务监控_list','2014','F','0','system:monitor:servies:index:list','admin',now(),'admin',now());

-- 部门管理
insert into sys_menu(menu_id , menu_name,parent_id ,menu_type,status,perms,create_by,create_time,update_by,update_time)
values('1637739617765687299', '部门列表','2003','F','0','system:department:index:list','admin',now(),'admin',now());

-- 定时任务(后端无代码）
insert into sys_menu(menu_id , menu_name,parent_id ,menu_type,status,perms,create_by,create_time,update_by,update_time)
values('20126', '定时任务_list','2012','F','0','system:monitor:jobs:index:list','admin',now(),'admin',now());

-- 文件配置
insert into sys_menu(menu_id , menu_name,parent_id ,menu_type,status,perms,create_by,create_time,update_by,update_time)
values('155259447', '文件配置_list','1552594437741445122','F','0','system:file:fileconfig:index:list','admin',now(),'admin',now());

-- 缓存列表(后端无代码）
insert into sys_menu(menu_id , menu_name,parent_id ,menu_type,status,perms,create_by,create_time,update_by,update_time)
values('20166', '缓存列表_list','2016','F','0','system:monitor:cache:lndex:list','admin',now(),'admin',now());

-- 错误码管理
insert into sys_menu(menu_id , menu_name,parent_id ,menu_type,status,perms,create_by,create_time,update_by,update_time)
values('155249266', '错误代码管理_list','1552492618272964609','F','0','system:monitor:errorcode:index:list','admin',now(),'admin',now());

-- 访问日志
insert into sys_menu(menu_id , menu_name,parent_id ,menu_type,status,perms,create_by,create_time,update_by,update_time)
values('1637695551556419586', '获取访问日志列表','1553982743687417858','F','0','system:logininfor:lndex:list','admin',now(),'admin',now());

-- 在线用户
insert into sys_menu(menu_id , menu_name,parent_id ,menu_type,status,perms,create_by,create_time,update_by,update_time)
values('1637702341643862018', '在线用户_list','2011','F','0','system:monitor:onlineuser:index:list','admin',now(),'admin',now());

-- 用户管理
insert into sys_menu(menu_id , menu_name,parent_id ,menu_type,status,perms,create_by,create_time,update_by,update_time)
values('1637692724117372931', '查询用户列表','2007','F','0','system:user:index:list','admin',now(),'admin',now());

-- 通知公告
insert into sys_menu(menu_id , menu_name,parent_id ,menu_type,status,perms,create_by,create_time,update_by,update_time)
values('20196', '通知公告_list','2019','F','0','system:notice:index:list','admin',now(),'admin',now());

-- 岗位管理
insert into sys_menu(menu_id , menu_name,parent_id ,menu_type,status,perms,create_by,create_time,update_by,update_time)
values('1646097108993757186', '岗位列表','2002','F','0','system:position:index:list','admin',now(),'admin',now());

-- 角色管理
insert into sys_menu(menu_id , menu_name,parent_id ,menu_type,status,perms,create_by,create_time,update_by,update_time)
values('1637701165317750787', '角色列表','1559483045800763393','F','0','system:role:index:list','admin',now(),'admin',now());

-- 短信日志
insert into sys_menu(menu_id , menu_name,parent_id ,menu_type,status,perms,create_by,create_time,update_by,update_time)
values('1637694984402632707', '短信日志列表','1552896263871139842','F','0','system:sms:smslog:index:list','admin',now(),'admin',now());

-- 参数设置
insert into sys_menu(menu_id , menu_name,parent_id ,menu_type,status,perms,create_by,create_time,update_by,update_time)
values('1637740180557398020', '查询参数列表','2017','F','0','system:parameter:index:list','admin',now(),'admin',now());

-- 短信模版
insert into sys_menu(menu_id , menu_name,parent_id ,menu_type,status,perms,create_by,create_time,update_by,update_time)
values('1637699207320174597', '短信模版列表','2037','F','0','system:sms:smstemplate:lndex:list','admin',now(),'admin',now());

-- 文件列表
insert into sys_menu(menu_id , menu_name,parent_id ,menu_type,status,perms,create_by,create_time,update_by,update_time)
values('1648976771367776258', '文件列表','1554005889752735746','F','0','system:file:filelist:index:list','admin',now(),'admin',now());

-- 登录日志
insert into sys_menu(menu_id , menu_name,parent_id ,menu_type,status,perms,create_by,create_time,update_by,update_time)
values('20246', '登录日志_list','2024','F','0','system:logininfor:lndex:list','admin',now(),'admin',now());

-- 操作日志
insert into sys_menu(menu_id , menu_name,parent_id ,menu_type,status,perms,create_by,create_time,update_by,update_time)
values('1637698328852561924', '获得 API 错误日志列表','2023','F','0','system:operlog:index:list','admin',now(),'admin',now());

-- 字典管理
insert into sys_menu(menu_id , menu_name,parent_id ,menu_type,status,perms,create_by,create_time,update_by,update_time)
values('1637739938336342020', '查询字典','2009','F','0','system:dict:index:list','admin',now(),'admin',now());

-- 字典数据
insert into sys_menu(menu_id , menu_name,parent_id ,menu_type,status,perms,create_by,create_time,update_by,update_time)
values('1637739209898983427', '字典数据列表','1579761925094563842','F','0','system:dictdata:index:list','admin',now(),'admin',now());

-- 机器人配置
insert into sys_menu(menu_id , menu_name,parent_id ,menu_type,status,perms,create_by,create_time,update_by,update_time)
values('1643854527852621830', '机器人配置_list','1641698767885250561','F','0','system:robot-config:list','admin',now(),'admin',now());
------------------------2023-04-26 cq end-----------------------

