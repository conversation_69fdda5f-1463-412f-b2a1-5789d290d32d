create table cop.month_dangerous_enterprises
(
    enterprise_id       bigint
        primary key,
    enterprise_name     varchar(255 char),
    legal_person        varchar(255 char),
    contact_phone       varchar(20 char),
    enterprise_address  varchar(255 char),
    enterprise_property varchar(50 char),
    geom                geometry,
    enterprise_id_no    bigint,
    enterprise_type     varchar(10 char),
    update_date         date default NULL::timestamp without time zone,
    rep_person          varchar(64 char),
    rep_date            date,
    rep_reason          varchar(4000 char),
    problem             varchar(4000 char),
    copoversee          varchar(4000 char),
    copadvice           varchar(4000 char),
    copdeal             varchar(4000 char),
    copresult           varchar(4000 char),
    copafterwork        varchar(4000 char),
    cophelp             varchar(4000 char),
    rep_dept            varchar(64 char)
);

comment on table cop.month_dangerous_enterprises is '危险企业表';

comment on column cop.month_dangerous_enterprises.enterprise_id is '企业ID';

comment on column cop.month_dangerous_enterprises.enterprise_name is '企业名称';

comment on column cop.month_dangerous_enterprises.legal_person is '企业法人';

comment on column cop.month_dangerous_enterprises.contact_phone is '联系电话';

comment on column cop.month_dangerous_enterprises.enterprise_address is '企业地址';

comment on column cop.month_dangerous_enterprises.enterprise_property is '企业性质';

comment on column cop.month_dangerous_enterprises.geom is '地理位置';

comment on column cop.month_dangerous_enterprises.rep_person is '上报人';

comment on column cop.month_dangerous_enterprises.rep_date is '上报时间';

comment on column cop.month_dangerous_enterprises.rep_reason is '上报原因';

comment on column cop.month_dangerous_enterprises.problem is '其他问题';

comment on column cop.month_dangerous_enterprises.copoversee is '监控措施';

comment on column cop.month_dangerous_enterprises.copadvice is '建议措施';

comment on column cop.month_dangerous_enterprises.copdeal is '采取措施';

comment on column cop.month_dangerous_enterprises.copresult is '企业现状';

comment on column cop.month_dangerous_enterprises.copafterwork is '后续工作';

comment on column cop.month_dangerous_enterprises.cophelp is '请求协助';

comment on column cop.month_dangerous_enterprises.rep_dept is '上报bu''m';

alter table cop.month_dangerous_enterprises
    owner to ywtg;

create table cop.month_high_risk_enterprises
(
    enterprise_id       bigint
        primary key,
    enterprise_name     varchar(255 char),
    legal_person        varchar(255 char),
    contact_phone       varchar(20 char),
    enterprise_address  varchar(255 char),
    enterprise_property varchar(50 char),
    geom                geometry,
    enterprise_id_no    bigint,
    enterprise_type     varchar(10 char),
    update_date         date default NULL::timestamp without time zone,
    rep_person          varchar(64 char),
    rep_date            timestamp,
    rep_reason          varchar(4000 char),
    problem             varchar(4000 char),
    copoversee          varchar(4000 char),
    copadvice           varchar(4000 char),
    copdeal             varchar(4000 char),
    copresult           varchar(4000 char),
    copafterwork        varchar(4000 char),
    cophelp             varchar(4000 char),
    rep_dept            varchar(64 char)
);

comment on table cop.month_high_risk_enterprises is '高危企业表';

comment on column cop.month_high_risk_enterprises.enterprise_id is '企业ID';

comment on column cop.month_high_risk_enterprises.enterprise_name is '企业名称';

comment on column cop.month_high_risk_enterprises.legal_person is '企业法人';

comment on column cop.month_high_risk_enterprises.contact_phone is '联系电话';

comment on column cop.month_high_risk_enterprises.enterprise_address is '企业地址';

comment on column cop.month_high_risk_enterprises.enterprise_property is '企业性质';

comment on column cop.month_high_risk_enterprises.geom is '地理位置';

comment on column cop.month_high_risk_enterprises.rep_person is '上报人';

comment on column cop.month_high_risk_enterprises.rep_date is '上报时间';

comment on column cop.month_high_risk_enterprises.rep_reason is '上报原因';

comment on column cop.month_high_risk_enterprises.problem is '其他问题';

comment on column cop.month_high_risk_enterprises.copoversee is '监控措施';

comment on column cop.month_high_risk_enterprises.copadvice is '建议措施';

comment on column cop.month_high_risk_enterprises.copdeal is '采取措施';

comment on column cop.month_high_risk_enterprises.copresult is '企业现状';

comment on column cop.month_high_risk_enterprises.copafterwork is '后续工作';

comment on column cop.month_high_risk_enterprises.cophelp is '请求协助';

alter table cop.month_high_risk_enterprises
    owner to ywtg;


create table cop.month_problem_enterprises
(
    enterprise_id       bigint
        primary key,
    enterprise_name     varchar(255 char),
    legal_person        varchar(255 char),
    contact_phone       varchar(20 char),
    enterprise_address  varchar(255 char),
    enterprise_property varchar(50 char),
    geom                geometry,
    enterprise_id_no    bigint,
    enterprise_type     varchar(10 char),
    rep_person          varchar(64 char),
    rep_date            timestamp,
    rep_reason          varchar(4000 char),
    problem             varchar(4000 char),
    copoversee          varchar(4000 char),
    copadvice           varchar(4000 char),
    copdeal             varchar(4000 char),
    copresult           varchar(4000 char),
    copafterwork        varchar(4000 char),
    cophelp             varchar(4000 char),
    rep_dept            varchar(64 char)
);

comment on table cop.month_problem_enterprises is '问题企业表';

comment on column cop.month_problem_enterprises.enterprise_id is '企业ID';

comment on column cop.month_problem_enterprises.enterprise_name is '企业名称';

comment on column cop.month_problem_enterprises.legal_person is '企业法人';

comment on column cop.month_problem_enterprises.contact_phone is '联系电话';

comment on column cop.month_problem_enterprises.enterprise_address is '企业地址';

comment on column cop.month_problem_enterprises.enterprise_property is '企业性质';

comment on column cop.month_problem_enterprises.geom is '地理位置';

comment on column cop.month_problem_enterprises.rep_person is '上报人';

comment on column cop.month_problem_enterprises.rep_date is '上报时间';

comment on column cop.month_problem_enterprises.rep_reason is '上报原因';

comment on column cop.month_problem_enterprises.problem is '其他问题';

comment on column cop.month_problem_enterprises.copoversee is '监控措施';

comment on column cop.month_problem_enterprises.copadvice is '建议措施';

comment on column cop.month_problem_enterprises.copdeal is '采取措施';

comment on column cop.month_problem_enterprises.copresult is '企业现状';

comment on column cop.month_problem_enterprises.copafterwork is '后续工作';

comment on column cop.month_problem_enterprises.cophelp is '请求协助';

alter table cop.month_problem_enterprises
    owner to ywtg;

create table cop.month_risk_enterprises
(
    enterprise_id       bigint
        primary key,
    enterprise_name     varchar(255 char),
    legal_person        varchar(255 char),
    contact_phone       varchar(20 char),
    enterprise_address  varchar(255 char),
    enterprise_property varchar(50 char),
    geom                geometry,
    enterprise_id_no    bigint,
    enterprise_type     varchar(10 char),
    rep_person          varchar(64 char),
    rep_date            timestamp,
    rep_reason          varchar(4000 char),
    problem             varchar(4000 char),
    copoversee          varchar(4000 char),
    copadvice           varchar(4000 char),
    copdeal             varchar(4000 char),
    copresult           varchar(4000 char),
    copafterwork        varchar(4000 char),
    cophelp             varchar(4000 char),
    rep_dept            varchar(64 char)
);

comment on table cop.month_risk_enterprises is '风险企业表';

comment on column cop.month_risk_enterprises.enterprise_id is '企业ID';

comment on column cop.month_risk_enterprises.enterprise_name is '企业名称';

comment on column cop.month_risk_enterprises.legal_person is '企业法人';

comment on column cop.month_risk_enterprises.contact_phone is '联系电话';

comment on column cop.month_risk_enterprises.enterprise_address is '企业地址';

comment on column cop.month_risk_enterprises.enterprise_property is '企业性质';

comment on column cop.month_risk_enterprises.geom is '地理位置';

comment on column cop.month_risk_enterprises.rep_person is '上报人';

comment on column cop.month_risk_enterprises.rep_date is '上报时间';

comment on column cop.month_risk_enterprises.rep_reason is '上报原因';

comment on column cop.month_risk_enterprises.problem is '其他问题';

comment on column cop.month_risk_enterprises.copoversee is '监控措施';

comment on column cop.month_risk_enterprises.copadvice is '建议措施';

comment on column cop.month_risk_enterprises.copdeal is '采取措施';

comment on column cop.month_risk_enterprises.copresult is '企业现状';

comment on column cop.month_risk_enterprises.copafterwork is '后续工作';

comment on column cop.month_risk_enterprises.cophelp is '请求协助';

alter table cop.month_risk_enterprises
    owner to ywtg;

