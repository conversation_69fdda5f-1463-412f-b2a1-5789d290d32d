alter table cop.dangerous_enterprises
    add column rep_person varchar(64);
alter table cop.dangerous_enterprises
    add column rep_date timestamp;
alter table cop.dangerous_enterprises
    add column rep_reason varchar(4000);

comment on column cop.dangerous_enterprises.rep_person is '上报人';
comment on column cop.dangerous_enterprises.rep_date is '上报时间';
comment on column cop.dangerous_enterprises.rep_reason is '上报原因';



alter table cop.high_risk_enterprises
    add column rep_person varchar(64);
alter table cop.high_risk_enterprises
    add column rep_date timestamp;
alter table cop.high_risk_enterprises
    add column rep_reason varchar(4000);

comment on column cop.high_risk_enterprises.rep_person is '上报人';
comment on column cop.high_risk_enterprises.rep_date is '上报时间';
comment on column cop.high_risk_enterprises.rep_reason is '上报原因';


alter table cop.normal_enterprises
    add column rep_person varchar(64);
alter table cop.normal_enterprises
    add column rep_date timestamp;
alter table cop.normal_enterprises
    add column rep_reason varchar(4000);

comment on column cop.normal_enterprises.rep_person is '上报人';
comment on column cop.normal_enterprises.rep_date is '上报时间';
comment on column cop.normal_enterprises.rep_reason is '上报原因';



alter table cop.problem_enterprises
    add column rep_person varchar(64);
alter table cop.problem_enterprises
    add column rep_date timestamp;
alter table cop.problem_enterprises
    add column rep_reason varchar(4000);

comment on column cop.problem_enterprises.rep_person is '上报人';
comment on column cop.problem_enterprises.rep_date is '上报时间';
comment on column cop.problem_enterprises.rep_reason is '上报原因';


alter table cop.risk_enterprises
    add column rep_person varchar(64);
alter table cop.risk_enterprises
    add column rep_date timestamp;
alter table cop.risk_enterprises
    add column rep_reason varchar(4000);

comment on column cop.risk_enterprises.rep_person is '上报人';
comment on column cop.risk_enterprises.rep_date is '上报时间';
comment on column cop.risk_enterprises.rep_reason is '上报原因';



ALTER TABLE cop.dangerous_enterprises
    ADD COLUMN problem      VARCHAR(4000) NULL,
    ADD COLUMN copoversee   VARCHAR(4000) NULL,
    ADD COLUMN copadvice    VARCHAR(4000) NULL,
    ADD COLUMN copdeal      VARCHAR(4000) NULL,
    ADD COLUMN copresult    VARCHAR(4000) NULL,
    ADD COLUMN copafterwork VARCHAR(4000) NULL,
    ADD COLUMN cophelp      VARCHAR(4000) NULL;

comment on column cop.dangerous_enterprises.problem is '其他问题';
comment on column cop.dangerous_enterprises.copoversee is '监控措施';
comment on column cop.dangerous_enterprises.copadvice is '建议措施';
comment on column cop.dangerous_enterprises.copdeal is '采取措施';
comment on column cop.dangerous_enterprises.copresult is '企业现状';
comment on column cop.dangerous_enterprises.copafterwork is '后续工作';
comment on column cop.dangerous_enterprises.cophelp is '请求协助';


ALTER TABLE cop.high_risk_enterprises
    ADD COLUMN problem      VARCHAR(4000) NULL,
    ADD COLUMN copoversee   VARCHAR(4000) NULL,
    ADD COLUMN copadvice    VARCHAR(4000) NULL,
    ADD COLUMN copdeal      VARCHAR(4000) NULL,
    ADD COLUMN copresult    VARCHAR(4000) NULL,
    ADD COLUMN copafterwork VARCHAR(4000) NULL,
    ADD COLUMN cophelp      VARCHAR(4000) NULL;

comment on column cop.high_risk_enterprises.problem is '其他问题';
comment on column cop.high_risk_enterprises.copoversee is '监控措施';
comment on column cop.high_risk_enterprises.copadvice is '建议措施';
comment on column cop.high_risk_enterprises.copdeal is '采取措施';
comment on column cop.high_risk_enterprises.copresult is '企业现状';
comment on column cop.high_risk_enterprises.copafterwork is '后续工作';
comment on column cop.high_risk_enterprises.cophelp is '请求协助';


ALTER TABLE cop.normal_enterprises
    ADD COLUMN problem      VARCHAR(4000) NULL,
    ADD COLUMN copoversee   VARCHAR(4000) NULL,
    ADD COLUMN copadvice    VARCHAR(4000) NULL,
    ADD COLUMN copdeal      VARCHAR(4000) NULL,
    ADD COLUMN copresult    VARCHAR(4000) NULL,
    ADD COLUMN copafterwork VARCHAR(4000) NULL,
    ADD COLUMN cophelp      VARCHAR(4000) NULL;

comment on column cop.normal_enterprises.problem is '其他问题';
comment on column cop.normal_enterprises.copoversee is '监控措施';
comment on column cop.normal_enterprises.copadvice is '建议措施';
comment on column cop.normal_enterprises.copdeal is '采取措施';
comment on column cop.normal_enterprises.copresult is '企业现状';
comment on column cop.normal_enterprises.copafterwork is '后续工作';
comment on column cop.normal_enterprises.cophelp is '请求协助';



ALTER TABLE cop.problem_enterprises
    ADD COLUMN problem      VARCHAR(4000) NULL,
    ADD COLUMN copoversee   VARCHAR(4000) NULL,
    ADD COLUMN copadvice    VARCHAR(4000) NULL,
    ADD COLUMN copdeal      VARCHAR(4000) NULL,
    ADD COLUMN copresult    VARCHAR(4000) NULL,
    ADD COLUMN copafterwork VARCHAR(4000) NULL,
    ADD COLUMN cophelp      VARCHAR(4000) NULL;

comment on column cop.problem_enterprises.problem is '其他问题';
comment on column cop.problem_enterprises.copoversee is '监控措施';
comment on column cop.problem_enterprises.copadvice is '建议措施';
comment on column cop.problem_enterprises.copdeal is '采取措施';
comment on column cop.problem_enterprises.copresult is '企业现状';
comment on column cop.problem_enterprises.copafterwork is '后续工作';
comment on column cop.problem_enterprises.cophelp is '请求协助';



ALTER TABLE cop.risk_enterprises
    ADD COLUMN problem      VARCHAR(4000) NULL,
    ADD COLUMN copoversee   VARCHAR(4000) NULL,
    ADD COLUMN copadvice    VARCHAR(4000) NULL,
    ADD COLUMN copdeal      VARCHAR(4000) NULL,
    ADD COLUMN copresult    VARCHAR(4000) NULL,
    ADD COLUMN copafterwork VARCHAR(4000) NULL,
    ADD COLUMN cophelp      VARCHAR(4000) NULL;

comment on column cop.risk_enterprises.problem is '其他问题';
comment on column cop.risk_enterprises.copoversee is '监控措施';
comment on column cop.risk_enterprises.copadvice is '建议措施';
comment on column cop.risk_enterprises.copdeal is '采取措施';
comment on column cop.risk_enterprises.copresult is '企业现状';
comment on column cop.risk_enterprises.copafterwork is '后续工作';
comment on column cop.risk_enterprises.cophelp is '请求协助';









