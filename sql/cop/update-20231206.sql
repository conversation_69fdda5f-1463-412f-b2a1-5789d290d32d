-- t_rep_report
ALTER TABLE cop.t_rep_report
    RENAME COLUMN auto_id TO rep_report_id;
ALTER TABLE cop.t_rep_report
    ALTER COLUMN rep_report_id TYPE bigint USING rep_report_id::bigint;

ALTER TABLE cop.t_rep_report
    RENAME COLUMN insert_time TO create_time;
ALTER TABLE cop.t_rep_report
    RENAME COLUMN is_del TO del_flag;


ALTER TABLE cop.t_rep_report
    ADD COLUMN update_time timestamp;
ALTER TABLE cop.t_rep_report
    ADD COLUMN create_by varchar(64);
ALTER TABLE cop.t_rep_report
    ADD COLUMN update_by varchar(64);
-- t_rep_garrep
ALTER TABLE cop.t_rep_garrep
    RENAME COLUMN auto_id TO rep_garrep_id;
ALTER TABLE cop.t_rep_garrep
    ALTER COLUMN rep_garrep_id TYPE bigint USING rep_garrep_id::bigint;
ALTER TABLE cop.t_rep_garrep
    RENAME COLUMN insert_time TO create_time;
ALTER TABLE cop.t_rep_garrep
    RENAME COLUMN is_del TO del_flag;
ALTER TABLE cop.t_rep_garrep
    ADD COLUMN update_time timestamp;
ALTER TABLE cop.t_rep_garrep
    ADD COLUMN create_by varchar(64);
ALTER TABLE cop.t_rep_garrep
    ADD COLUMN update_by varchar(64);

-- t_rep_target
ALTER TABLE cop.t_rep_target
    ADD COLUMN update_time timestamp;
ALTER TABLE cop.t_rep_target
    ADD COLUMN create_by varchar(64);
ALTER TABLE cop.t_rep_target
    ADD COLUMN update_by varchar(64);

-- t_rep_deptar
ALTER TABLE cop.t_rep_deptar
    ADD COLUMN update_time timestamp;
ALTER TABLE cop.t_rep_deptar
    ADD COLUMN create_by varchar(64);
ALTER TABLE cop.t_rep_deptar
    ADD COLUMN update_by varchar(64);
-- t_cop_information
ALTER TABLE cop.t_cop_information
    ADD COLUMN update_time timestamp;
ALTER TABLE cop.t_cop_information
    ADD COLUMN create_by varchar(64);
ALTER TABLE cop.t_cop_information
    ADD COLUMN update_by varchar(64);
-- t_exi_handle
ALTER TABLE cop.t_exi_handle
    ADD COLUMN update_time timestamp;
ALTER TABLE cop.t_exi_handle
    ADD COLUMN create_by varchar(64);
ALTER TABLE cop.t_exi_handle
    ADD COLUMN update_by varchar(64);
-- t_exi_report
ALTER TABLE cop.t_exi_report
    ADD COLUMN update_time timestamp;
ALTER TABLE cop.t_exi_report
    ADD COLUMN create_by varchar(64);
ALTER TABLE cop.t_exi_report
    ADD COLUMN update_by varchar(64);
-- t_pla_info
ALTER TABLE cop.t_pla_info
    ADD COLUMN update_time timestamp;
ALTER TABLE cop.t_pla_info
    ADD COLUMN create_by varchar(64);
ALTER TABLE cop.t_pla_info
    ADD COLUMN update_by varchar(64);
-- t_cop_scoreyear
ALTER TABLE cop.t_cop_scoreyear
    ADD COLUMN update_time timestamp;
ALTER TABLE cop.t_cop_scoreyear
    ADD COLUMN create_by varchar(64);
ALTER TABLE cop.t_cop_scoreyear
    ADD COLUMN update_by varchar(64);
--t_cop_score_record
ALTER TABLE cop.t_cop_score_record
    ADD COLUMN update_time timestamp;
ALTER TABLE cop.t_cop_score_record
    ADD COLUMN create_by varchar(64);
ALTER TABLE cop.t_cop_score_record
    ADD COLUMN update_by varchar(64);
--t_pla_acc
ALTER TABLE cop.t_pla_acc
    ADD COLUMN update_time timestamp;
ALTER TABLE cop.t_pla_acc
    ADD COLUMN create_by varchar(64);
ALTER TABLE cop.t_pla_acc
    ADD COLUMN update_by varchar(64);
-- t_doc_info
ALTER TABLE cop.t_doc_info
    ADD COLUMN update_time timestamp;
ALTER TABLE cop.t_doc_info
    ADD COLUMN create_by varchar(64);
ALTER TABLE cop.t_doc_info
    ADD COLUMN update_by varchar(64);
-- t_doc_handle
ALTER TABLE cop.t_doc_handle
    ADD COLUMN update_time timestamp;
ALTER TABLE cop.t_doc_handle
    ADD COLUMN create_by varchar(64);
ALTER TABLE cop.t_doc_handle
    ADD COLUMN update_by varchar(64);
-- t_doc_acc
ALTER TABLE cop.t_doc_acc
    ADD COLUMN update_time timestamp;
ALTER TABLE cop.t_doc_acc
    ADD COLUMN create_by varchar(64);
ALTER TABLE cop.t_doc_acc
    ADD COLUMN update_by varchar(64);
-- t_mes_contact
ALTER TABLE cop.t_mes_contact
    ADD COLUMN update_time timestamp;
ALTER TABLE cop.t_mes_contact
    ADD COLUMN create_by varchar(64);
ALTER TABLE cop.t_mes_contact
    ADD COLUMN update_by varchar(64);
-- t_tem_template
ALTER TABLE cop.t_tem_template
    ADD COLUMN update_time timestamp;
ALTER TABLE cop.t_tem_template
    ADD COLUMN create_by varchar(64);
ALTER TABLE cop.t_tem_template
    ADD COLUMN update_by varchar(64);
-- t_dic_org
ALTER TABLE cop.t_dic_org
    ADD COLUMN update_time timestamp;
ALTER TABLE cop.t_dic_org
    ADD COLUMN create_by varchar(64);
ALTER TABLE cop.t_dic_org
    ADD COLUMN update_by varchar(64);
--t_mes_record
ALTER TABLE cop.t_mes_record
    ADD COLUMN update_time timestamp;
ALTER TABLE cop.t_mes_record
    ADD COLUMN create_by varchar(64);
ALTER TABLE cop.t_mes_record
    ADD COLUMN update_by varchar(64);
-- t_per_performance
ALTER TABLE cop.t_per_performance
    ADD COLUMN update_time timestamp;
ALTER TABLE cop.t_per_performance
    ADD COLUMN create_by varchar(64);
ALTER TABLE cop.t_per_performance
    ADD COLUMN update_by varchar(64);
-- t_per_performance


ALTER TABLE cop.t_rep_report
    ADD COLUMN problem      VARCHAR(4000) NULL,
    ADD COLUMN copoversee   VARCHAR(4000) NULL,
    ADD COLUMN copadvice    VARCHAR(4000) NULL,
    ADD COLUMN copdeal      VARCHAR(4000) NULL,
    ADD COLUMN copresult    VARCHAR(4000) NULL,
    ADD COLUMN copafterwork VARCHAR(4000) NULL,
    ADD COLUMN cophelp      VARCHAR(4000) NULL;


