insert into cop.t_cop_information
(cop_id, cop_name, cop_code, cop_owner, cop_incharge, corporate_code, business_licence, tax_registration,
 cop_population, cop_address, cop_phone, cop_fax, office_property, office_owner, office_owner_phone, property_id,
 org_id, lon, lat, is_cancel, cop_contacts, lease_term, create_time, del_flag)
select cop_id,
       cop_name,
       cop_code,
       cop_owner,
       cop_incharge,
       corporate_code,
       business_licence,
       tax_registration,
       cop_population,
       cop_address,
       cop_phone,
       cop_fax,
       office_property,
       office_owner,
       office_owner_phone,
       property_id,
       org_id,
       lon,
       lat,
       is_cancel,
       cop_contacts,
       lease_term,
       create_time,
       del_flag
from cop.t_cop_information_old old
where old.cop_id in (select distinct rep.cop_id
                     from cop.t_rep_garrep rep
                              left join cop.t_cop_information t2 on rep.cop_id = t2.cop_id
                     where t2.cop_id is null
                       and rep.del_flag = '0');
