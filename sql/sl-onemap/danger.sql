-- 创建正常企业表
CREATE TABLE cop.normal_enterprises (
                                        enterprise_id BIGSERIAL PRIMARY KEY,
                                        enterprise_name VARCHAR(255),
                                        legal_person VARCHAR(255),
                                        contact_phone VARCHAR(20),
                                        enterprise_address VARCHAR(255),
                                        enterprise_property VARCHAR(50),
                                        geom GEOMETRY(Point, 4326)
);

-- 添加表注释
COMMENT ON TABLE cop.normal_enterprises IS '正常企业表';

-- 添加字段注释
COMMENT ON COLUMN cop.normal_enterprises.enterprise_id IS '企业ID';
COMMENT ON COLUMN cop.normal_enterprises.enterprise_name IS '企业名称';
COMMENT ON COLUMN cop.normal_enterprises.legal_person IS '企业法人';
COMMENT ON COLUMN cop.normal_enterprises.contact_phone IS '联系电话';
COMMENT ON COLUMN cop.normal_enterprises.enterprise_address IS '企业地址';
COMMENT ON COLUMN cop.normal_enterprises.enterprise_property IS '企业性质';
COMMENT ON COLUMN cop.normal_enterprises.geom IS '地理位置';

-- 创建问题企业表（依次为其他表也添加注释）
CREATE TABLE cop.problem_enterprises (
                                         enterprise_id BIGSERIAL PRIMARY KEY,
                                         enterprise_name VARCHAR(255),
                                         legal_person VARCHAR(255),
                                         contact_phone VARCHAR(20),
                                         enterprise_address VARCHAR(255),
                                         enterprise_property VARCHAR(50),
                                         geom GEOMETRY(Point, 4326)
);

-- 添加表注释
COMMENT ON TABLE cop.problem_enterprises IS '问题企业表';

-- 添加字段注释
COMMENT ON COLUMN cop.problem_enterprises.enterprise_id IS '企业ID';
COMMENT ON COLUMN cop.problem_enterprises.enterprise_name IS '企业名称';
COMMENT ON COLUMN cop.problem_enterprises.legal_person IS '企业法人';
COMMENT ON COLUMN cop.problem_enterprises.contact_phone IS '联系电话';
COMMENT ON COLUMN cop.problem_enterprises.enterprise_address IS '企业地址';
COMMENT ON COLUMN cop.problem_enterprises.enterprise_property IS '企业性质';
COMMENT ON COLUMN cop.problem_enterprises.geom IS '地理位置';

-- 创建风险企业表（依次为其他表也添加注释）
CREATE TABLE cop.risk_enterprises (
                                      enterprise_id BIGSERIAL PRIMARY KEY,
                                      enterprise_name VARCHAR(255),
                                      legal_person VARCHAR(255),
                                      contact_phone VARCHAR(20),
                                      enterprise_address VARCHAR(255),
                                      enterprise_property VARCHAR(50),
                                      geom GEOMETRY(Point, 4326)
);

-- 添加表注释
COMMENT ON TABLE cop.risk_enterprises IS '风险企业表';

-- 添加字段注释
COMMENT ON COLUMN cop.risk_enterprises.enterprise_id IS '企业ID';
COMMENT ON COLUMN cop.risk_enterprises.enterprise_name IS '企业名称';
COMMENT ON COLUMN cop.risk_enterprises.legal_person IS '企业法人';
COMMENT ON COLUMN cop.risk_enterprises.contact_phone IS '联系电话';
COMMENT ON COLUMN cop.risk_enterprises.enterprise_address IS '企业地址';
COMMENT ON COLUMN cop.risk_enterprises.enterprise_property IS '企业性质';
COMMENT ON COLUMN cop.risk_enterprises.geom IS '地理位置';

-- 创建危险企业表（依次为其他表也添加注释）
CREATE TABLE cop.dangerous_enterprises (
                                           enterprise_id BIGSERIAL PRIMARY KEY,
                                           enterprise_name VARCHAR(255),
                                           legal_person VARCHAR(255),
                                           contact_phone VARCHAR(20),
                                           enterprise_address VARCHAR(255),
                                           enterprise_property VARCHAR(50),
                                           geom GEOMETRY(Point, 4326)
);

-- 添加表注释
COMMENT ON TABLE cop.dangerous_enterprises IS '危险企业表';

-- 添加字段注释
COMMENT ON COLUMN cop.dangerous_enterprises.enterprise_id IS '企业ID';
COMMENT ON COLUMN cop.dangerous_enterprises.enterprise_name IS '企业名称';
COMMENT ON COLUMN cop.dangerous_enterprises.legal_person IS '企业法人';
COMMENT ON COLUMN cop.dangerous_enterprises.contact_phone IS '联系电话';
COMMENT ON COLUMN cop.dangerous_enterprises.enterprise_address IS '企业地址';
COMMENT ON COLUMN cop.dangerous_enterprises.enterprise_property IS '企业性质';
COMMENT ON COLUMN cop.dangerous_enterprises.geom IS '地理位置';

-- 创建高危企业表（依次为其他表也添加注释）
CREATE TABLE cop.high_risk_enterprises (
                                           enterprise_id BIGSERIAL PRIMARY KEY,
                                           enterprise_name VARCHAR(255),
                                           legal_person VARCHAR(255),
                                           contact_phone VARCHAR(20),
                                           enterprise_address VARCHAR(255),
                                           enterprise_property VARCHAR(50),
                                           geom GEOMETRY(Point, 4326)
);

-- 添加表注释
COMMENT ON TABLE cop.high_risk_enterprises IS '高危企业表';

-- 添加字段注释
COMMENT ON COLUMN cop.high_risk_enterprises.enterprise_id IS '企业ID';
COMMENT ON COLUMN cop.high_risk_enterprises.enterprise_name IS '企业名称';
COMMENT ON COLUMN cop.high_risk_enterprises.legal_person IS '企业法人';
COMMENT ON COLUMN cop.high_risk_enterprises.contact_phone IS '联系电话';
COMMENT ON COLUMN cop.high_risk_enterprises.enterprise_address IS '企业地址';
COMMENT ON COLUMN cop.high_risk_enterprises.enterprise_property IS '企业性质';
COMMENT ON COLUMN cop.high_risk_enterprises.geom IS '地理位置';


INSERT INTO cop.risk_enterprises (enterprise_id ,enterprise_name, legal_person, contact_phone, enterprise_address, enterprise_property, geom)
SELECT t.auto_id,t.cop_name ,t.cop_owner, t.cop_phone, t.cop_address, t.cop_property, ST_SetSRID(ST_MakePoint(t.lon, t.lat), 4326) AS geom
FROM
    (select  distinct  tci.* , tdc.cop_property
     from cop.T_REP_Report trr
              left join cop.T_COP_Information tci on trr.cop_id = tci.auto_id
              left join cop.T_DIC_CopLevel coplv on trr."level" = coplv.level_value
              left join cop.t_dic_copproperty tdc on tdc.auto_id  = tci.property_id
     where DATE_PART('year', trr.rep_date ) = 2023 and coplv.level_name = '风险') t;

INSERT INTO cop.dangerous_enterprises  (enterprise_id ,enterprise_name, legal_person, contact_phone, enterprise_address, enterprise_property, geom)
SELECT t.auto_id,t.cop_name ,t.cop_owner, t.cop_phone, t.cop_address, t.cop_property, ST_SetSRID(ST_MakePoint(t.lon, t.lat), 4326) AS geom
FROM
    (select  distinct  tci.* , tdc.cop_property
     from cop.T_REP_Report trr
              left join cop.T_COP_Information tci on trr.cop_id = tci.auto_id
              left join cop.T_DIC_CopLevel coplv on trr."level" = coplv.level_value
              left join cop.t_dic_copproperty tdc on tdc.auto_id  = tci.property_id
     where DATE_PART('year', trr.rep_date ) = 2023 and coplv.level_name = '危险') t;

INSERT INTO cop.high_risk_enterprises (enterprise_id ,enterprise_name, legal_person, contact_phone, enterprise_address, enterprise_property, geom)
SELECT t.auto_id,t.cop_name ,t.cop_owner, t.cop_phone, t.cop_address, t.cop_property, ST_SetSRID(ST_MakePoint(t.lon, t.lat), 4326) AS geom
FROM
    (select  distinct  tci.* , tdc.cop_property
     from cop.T_REP_Report trr
              left join cop.T_COP_Information tci on trr.cop_id = tci.auto_id
              left join cop.T_DIC_CopLevel coplv on trr."level" = coplv.level_value
              left join cop.t_dic_copproperty tdc on tdc.auto_id  = tci.property_id
     where DATE_PART('year', trr.rep_date ) = 2023 and coplv.level_name = '高危') t;

INSERT INTO cop.problem_enterprises  (enterprise_id ,enterprise_name, legal_person, contact_phone, enterprise_address, enterprise_property, geom)
SELECT t.auto_id,t.cop_name ,t.cop_owner, t.cop_phone, t.cop_address, t.cop_property, ST_SetSRID(ST_MakePoint(t.lon, t.lat), 4326) AS geom
FROM
    (select  distinct  tci.* , tdc.cop_property
     from cop.T_REP_Report trr
              left join cop.T_COP_Information tci on trr.cop_id = tci.auto_id
              left join cop.T_DIC_CopLevel coplv on trr."level" = coplv.level_value
              left join cop.t_dic_copproperty tdc on tdc.auto_id  = tci.property_id
     where DATE_PART('year', trr.rep_date ) = 2023 and coplv.level_name = '问题') t;

-- 正常企业
INSERT INTO cop.normal_enterprises   (enterprise_id ,enterprise_name, legal_person, contact_phone, enterprise_address, enterprise_property, geom)
SELECT t.auto_id,t.cop_name ,t.cop_owner, t.cop_phone, t.cop_address, t.cop_property, ST_SetSRID(ST_MakePoint(t.lon, t.lat), 4326) AS geom
FROM
    (select distinct tci.* ,tdc.cop_property from cop.T_COP_Information tci left join cop.t_dic_copproperty tdc on tdc.auto_id  = tci.property_id
     where tci.auto_id  not in(select trr.cop_id  from cop.T_REP_Report trr where DATE_PART('year', trr.rep_date ) = 2023 ) and tci.is_del =0
       and tci.lon is not null and tci.lat is not null
     ) t;

-- 没有坐标的正常企业
INSERT INTO cop.normal_enterprises   (enterprise_id ,enterprise_name, legal_person, contact_phone, enterprise_address, enterprise_property)
SELECT t.auto_id,t.cop_name ,t.cop_owner, t.cop_phone, t.cop_address, t.cop_property
FROM
    (select distinct tci.* ,tdc.cop_property from cop.T_COP_Information tci left join cop.t_dic_copproperty tdc on tdc.auto_id  = tci.property_id
     where tci.auto_id  not in(select trr.cop_id  from cop.T_REP_Report trr where DATE_PART('year', trr.rep_date ) = 2023 ) and tci.is_del =0
       and tci.lon is null and tci.lat is null
    ) t;

ALTER TABLE cop.dangerous_enterprises  ADD COLUMN enterprise_id_no int8 ;
update cop.dangerous_enterprises set enterprise_id_no = enterprise_id ;

ALTER TABLE cop.high_risk_enterprises  ADD COLUMN enterprise_id_no int8 ;
update cop.high_risk_enterprises set enterprise_id_no = enterprise_id ;

ALTER TABLE cop.normal_enterprises  ADD COLUMN enterprise_id_no int8 ;
update cop.normal_enterprises set enterprise_id_no = enterprise_id ;

ALTER TABLE cop.problem_enterprises  ADD COLUMN enterprise_id_no int8 ;
update cop.problem_enterprises set enterprise_id_no = enterprise_id ;

ALTER TABLE cop.risk_enterprises  ADD COLUMN enterprise_id_no int8 ;
update cop.risk_enterprises set enterprise_id_no = enterprise_id ;

ALTER TABLE cop.dangerous_enterprises  ADD COLUMN enterprise_type varchar(10) ;
update cop.dangerous_enterprises set enterprise_type = '危险' ;

ALTER TABLE cop.high_risk_enterprises  ADD COLUMN enterprise_type varchar(10) ;
update cop.high_risk_enterprises set enterprise_type = '高危' ;

ALTER TABLE cop.normal_enterprises  ADD COLUMN enterprise_type varchar(10) ;
update cop.normal_enterprises set enterprise_type = '正常' ;

ALTER TABLE cop.problem_enterprises  ADD COLUMN enterprise_type varchar(10) ;
update cop.problem_enterprises set enterprise_type = '问题' ;

ALTER TABLE cop.risk_enterprises  ADD COLUMN enterprise_type varchar(10) ;
update cop.risk_enterprises set enterprise_type = '风险' ;
