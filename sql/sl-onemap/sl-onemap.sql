CREATE TABLE business.sl_gas_restaurant_gather
(
    gather_gas_id                int8 PRIMARY KEY NOT NULL,
    section_name                 varchar(32)      NULL,
    subject_name                 varchar(120)     NULL,
    subject_address              varchar(500)     NULL,
    subject_manager              varchar(12)      NULL,
    subject_phone                varchar(21)      NULL,
    grid_manager                 varchar(12)      NULL,
    power_type                   bpchar(1)        NULL,
    power_type_mark              varchar(12)      NULL,
    power_store_mark             varchar(60)      NULL,
    power_store_large_num        int2        default NULL,
    power_store_small_num        int2        default NULL,
    question_mark                varchar(60) default NULL,
    question_gas_bottle          bpchar(1)   default '0',
    question_gas_pipeline        bpchar(1)   default '0',
    question_gas_warming_machine bpchar(1)   default '0',
    question_gas_store_weight    bpchar(1)   default '0',
    question_gas_store_location  bpchar(1)   default '0',
    question_gas_illegal         bpchar(1)   default '0',
    question_rectification       bpchar(1)   default '0',
    gather_time                  timestamp        NULL,
    --
    del_flag                     bpchar(1)   default '0',
    status                       bpchar(1)   default '0',
    create_by                    varchar(24)      not null, -- 由谁创建
    create_time                  timestamp        not null, -- 创建时间
    update_time                  timestamp        NULL,
    update_by                    varchar(12)      NULL
);
COMMENT ON TABLE business.sl_gas_restaurant_gather IS '燃气统计表';
COMMENT ON COLUMN business.sl_gas_restaurant_gather.section_name IS '辖区';
COMMENT ON COLUMN business.sl_gas_restaurant_gather.subject_name IS '主体名称';
COMMENT ON COLUMN business.sl_gas_restaurant_gather.subject_address IS '地址';
COMMENT ON COLUMN business.sl_gas_restaurant_gather.subject_manager IS '管理人姓名';
COMMENT ON COLUMN business.sl_gas_restaurant_gather.subject_phone IS '联系电话';
COMMENT ON COLUMN business.sl_gas_restaurant_gather.grid_manager IS '网格员姓名';
COMMENT ON COLUMN business.sl_gas_restaurant_gather.question_rectification IS '是否整改';
COMMENT ON COLUMN business.sl_gas_restaurant_gather.question_gas_illegal IS '黑气标识';
COMMENT ON COLUMN business.sl_gas_restaurant_gather.question_gas_store_location IS '摆放隐患';
COMMENT ON COLUMN business.sl_gas_restaurant_gather.question_gas_store_weight IS '存储隐患';
COMMENT ON COLUMN business.sl_gas_restaurant_gather.question_gas_warming_machine IS '报警器隐患';
COMMENT ON COLUMN business.sl_gas_restaurant_gather.question_gas_pipeline IS '管道隐患';
COMMENT ON COLUMN business.sl_gas_restaurant_gather.question_gas_bottle IS '气瓶隐患';
COMMENT ON COLUMN business.sl_gas_restaurant_gather.question_mark IS '隐患描述、备注';
COMMENT ON COLUMN business.sl_gas_restaurant_gather.power_store_small_num IS '小气瓶数量';
COMMENT ON COLUMN business.sl_gas_restaurant_gather.power_store_large_num IS '大气瓶数量';
COMMENT ON COLUMN business.sl_gas_restaurant_gather.power_store_mark IS '燃气使用情况(存储情况)';
COMMENT ON COLUMN business.sl_gas_restaurant_gather.power_type_mark IS '燃气类型具体描述';
COMMENT ON COLUMN business.sl_gas_restaurant_gather.power_type IS '燃气类型描述：天然气、电、气瓶';
COMMENT ON COLUMN business.sl_gas_restaurant_gather.gather_time IS '收集时间';

ALTER TABLE business.sl_gas_restaurant_gather
    ADD COLUMN power_store_huge_num int2 DEFAULT NULL;
COMMENT ON COLUMN business.sl_gas_restaurant_gather.power_store_huge_num IS '特大瓶';
ALTER TABLE business.sl_gas_restaurant_gather
    ADD COLUMN question_gas_environment bpchar(1) default '0';
COMMENT ON COLUMN business.sl_gas_restaurant_gather.question_gas_environment IS '用户环境隐患';

CREATE TABLE business.sl_grid_structure
(
    grid_structure_id          int8 primary key not null,
    grid_structure_parent_name varchar(12)      null,
    gird_structure_name        varchar(12)      not null,
    grid_structure_parent_id   int8      default null,
    mark                       varchar(120)     null,
    --
    del_flag                   bpchar(1) default '0',
    status                     bpchar(1) default '0',
    create_by                  varchar(24)      not null, -- 由谁创建
    create_time                timestamp        not null, -- 创建时间
    update_time                timestamp        NULL,
    update_by                  varchar(12)      NULL
);
COMMENT ON TABLE business.sl_grid_structure IS '网格职位架构表';
COMMENT ON COLUMN business.sl_grid_structure.grid_structure_parent_name IS '父节点名称';
COMMENT ON COLUMN business.sl_grid_structure.gird_structure_name IS '节点名称';
COMMENT ON COLUMN business.sl_grid_structure.mark IS '描述';
COMMENT ON COLUMN business.sl_grid_structure.grid_structure_parent_id IS '父节点ID';

ALTER TABLE business.sl_gas_restaurant_gather
    ALTER COLUMN subject_phone TYPE varchar(30) USING subject_phone::varchar;
ALTER TABLE business.sl_gas_restaurant_gather
    ALTER COLUMN power_type_mark TYPE varchar(100) USING power_type_mark::varchar;


CREATE TABLE business.sl_grid_structure_character
(
    grid_character_id      int8 primary key not null,
    character_zone_name    varchar(12)      null,
    character_name         varchar(12)      null,
    character_sex          bpchar(1)        null,
    character_work_phone   varchar(35)      null,
    character_family_phone varchar(35)      null,
    mark                   varchar(120)     null,
    --
    del_flag               bpchar(1) default '0',
    status                 bpchar(1) default '0',
    create_by              varchar(24)      not null, -- 由谁创建
    create_time            timestamp        not null, -- 创建时间
    update_time            timestamp        NULL,
    update_by              varchar(12)      NULL
);
COMMENT ON TABLE business.sl_grid_structure_character IS '网格人员信息表';
COMMENT ON COLUMN business.sl_grid_structure_character.character_zone_name IS '所属区域名称';
COMMENT ON COLUMN business.sl_grid_structure_character.character_name IS '姓名';
COMMENT ON COLUMN business.sl_grid_structure_character.character_sex IS '性别';
COMMENT ON COLUMN business.sl_grid_structure_character.character_work_phone IS '工作手机';
COMMENT ON COLUMN business.sl_grid_structure_character.character_family_phone IS '家庭手机';
COMMENT ON COLUMN business.sl_grid_structure_character.mark IS '描述';


CREATE TABLE business.sl_grid_zone
(
    grid_zone_id    int8 primary key not null,
    grid_group_name varchar(12)      not null,
    grid_zone_name  varchar(12)      not null,
    --
    del_flag        bpchar(1) default '0',
    status          bpchar(1) default '0',
    create_by       varchar(24)      not null, -- 由谁创建
    create_time     timestamp        not null, -- 创建时间
    update_time     timestamp        NULL,
    update_by       varchar(12)      NULL
);
COMMENT ON TABLE business.sl_grid_zone IS '网格信息表';
COMMENT ON COLUMN business.sl_grid_zone.grid_group_name IS '所属区域名称';
COMMENT ON COLUMN business.sl_grid_zone.grid_zone_name IS '网格名称';


CREATE TABLE business.sl_grid_zone_character_rel
(
    zone_character_rel_id int8 primary key not null,
    grid_zone_id          int8             not null,
    grid_character_id     int8             not null,
    --
    del_flag              bpchar(1) default '0',
    status                bpchar(1) default '0',
    create_by             varchar(24)      not null, -- 由谁创建
    create_time           timestamp        not null, -- 创建时间
    update_time           timestamp        NULL,
    update_by             varchar(12)      NULL
);
CREATE INDEX idx_sl_grid_zone_character_rel ON business.sl_grid_zone_character_rel USING BTREE (grid_zone_id, grid_character_id);
COMMENT ON TABLE business.sl_grid_zone_character_rel IS '网格划分区与人物关系表';

CREATE TABLE business.sl_grid_structure_character_rel
(
    relation_id       int8 primary key not null,
    grid_character_id int8             not null,
    grid_structure_id int8             not null,
    --
    del_flag          bpchar(1) default '0',
    status            bpchar(1) default '0',
    create_by         varchar(24)      not null, -- 由谁创建
    create_time       timestamp        not null, -- 创建时间
    update_time       timestamp        NULL,
    update_by         varchar(12)      NULL
);
CREATE INDEX idx_sl_grid_structure_character_rel ON business.sl_grid_structure_character_rel USING BTREE (grid_character_id, grid_structure_id);
COMMENT ON TABLE business.sl_grid_structure_character_rel IS '职位与人物关系表';

ALTER TABLE business.sl_grid_structure
    ADD COLUMN grid_zone_name varchar(12) default null;
COMMENT ON COLUMN business.sl_grid_structure.grid_zone_name IS '所属区域名称';

-- 社工表
CREATE TABLE business.sl_social_worker
(
    social_worker_id  int8 primary key not null,
    worker_name       varchar(12)      null,
    worker_age        int2      default null,
    sex               bpchar(1)        null,
    worker_id_number  varchar(32)      null,
    worker_phone      varchar(20)      null,
    mark              varchar(120)     null,
    work_position     varchar(120)     null,
    standard_position varchar(120)     null,
    --
    del_flag          bpchar(1) default '0',
    status            bpchar(1) default '0',
    create_by         varchar(24)      not null, -- 由谁创建
    create_time       timestamp        not null, -- 创建时间
    update_time       timestamp        NULL,
    update_by         varchar(12)      NULL
);
COMMENT ON TABLE business.sl_social_worker IS '石龙社工表';
COMMENT ON COLUMN business.sl_social_worker.worker_name IS '社工名称';
COMMENT ON COLUMN business.sl_social_worker.worker_age IS '社工年龄';
COMMENT ON COLUMN business.sl_social_worker.sex IS '社工性别';
COMMENT ON COLUMN business.sl_social_worker.worker_id_number IS '社工身份证号';
COMMENT ON COLUMN business.sl_social_worker.worker_phone IS '社工手机';
COMMENT ON COLUMN business.sl_social_worker.mark IS '备注';
COMMENT ON COLUMN business.sl_social_worker.work_position IS '工作低点';
COMMENT ON COLUMN business.sl_social_worker.standard_position IS '二标地点';


-- 志愿者
CREATE TABLE business.sl_volunteer
(
    volunteer_id            int8 primary key not null,
    volunteer_name          varchar(12)      null,
    sex                     bpchar(1)        null,
    volunteer_login_code    varchar(60)      null,
    volunteer_id_number     varchar(32)      null,
    volunteer_phone         varchar(25)      null,
    volunteer_register_date timestamp        null,
    volunteer_verify        varchar(5)       null,
    volunteer_face_verify   varchar(10)      null,
    volunteer_pass          varchar(5)       null,
    volunteer_political     varchar(12)      null,
    volunteer_server_time   varchar(30)      null,
    volunteer_train_time    varchar(30)      null,
    --
    del_flag                bpchar(1) default '0',
    status                  bpchar(1) default '0',
    create_by               varchar(24)      not null, -- 由谁创建
    create_time             timestamp        not null, -- 创建时间
    update_time             timestamp        NULL,
    update_by               varchar(12)      NULL
);
COMMENT ON TABLE business.sl_volunteer IS '志愿者表';
COMMENT ON COLUMN business.sl_volunteer.volunteer_name IS '姓名';
COMMENT ON COLUMN business.sl_volunteer.sex IS '性别';
COMMENT ON COLUMN business.sl_volunteer.volunteer_login_code IS '登录账号';
COMMENT ON COLUMN business.sl_volunteer.volunteer_id_number IS '证件号码';
COMMENT ON COLUMN business.sl_volunteer.volunteer_phone IS '手机号码';
COMMENT ON COLUMN business.sl_volunteer.volunteer_register_date IS '注册时间';
COMMENT ON COLUMN business.sl_volunteer.volunteer_verify IS '校验信息';
COMMENT ON COLUMN business.sl_volunteer.volunteer_face_verify IS '是否面验';
COMMENT ON COLUMN business.sl_volunteer.volunteer_pass IS '审批状态';
COMMENT ON COLUMN business.sl_volunteer.volunteer_political IS '政治面貌';
COMMENT ON COLUMN business.sl_volunteer.volunteer_server_time IS '服务时长';
COMMENT ON COLUMN business.sl_volunteer.volunteer_train_time IS '培训时长';


ALTER TABLE business.sl_grid_structure_character
    ADD COLUMN character_service_type varchar(10) null;
COMMENT ON COLUMN business.sl_grid_structure_character.character_service_type IS '服务力量类型';

CREATE INDEX idx_sl_grid_structure_character ON business.sl_grid_structure_character USING hash (character_service_type);


-- 志愿者行为
CREATE TABLE business.sl_volunteer_actions
(
    action_id                int8 primary key not null,
    action_number            varchar(21)      null,
    action_name              varchar(60)      null,
    action_contract          varchar(60)      null,
    action_contract_phone    varchar(60)      null,
    action_publish_org       varchar(60)      null,
    action_publish_date      timestamp        null,
    action_additional_status varchar(60)      null,
    action_status            varchar(60)      null,
    action_type              varchar(60)      null,
    action_tag               varchar(60)      null,
    action_start_time        timestamp        null,
    action_end_time          timestamp        null,
    action_employ            varchar(5)       null,
    --
    del_flag                 bpchar(1) default '0',
    status                   bpchar(1) default '0',
    create_by                varchar(24)      not null, -- 由谁创建
    create_time              timestamp        not null, -- 创建时间
    update_time              timestamp        NULL,
    update_by                varchar(12)      NULL
);
COMMENT ON TABLE business.sl_volunteer_actions IS '志愿者活动表';
COMMENT ON COLUMN business.sl_volunteer_actions.action_number IS '活动编号';
COMMENT ON COLUMN business.sl_volunteer_actions.action_name IS '活动名称';
COMMENT ON COLUMN business.sl_volunteer_actions.action_contract IS '联系人';
COMMENT ON COLUMN business.sl_volunteer_actions.action_contract_phone IS '联系电话';
COMMENT ON COLUMN business.sl_volunteer_actions.action_publish_org IS '发布组织';
COMMENT ON COLUMN business.sl_volunteer_actions.action_publish_date IS '发布时间';
COMMENT ON COLUMN business.sl_volunteer_actions.action_additional_status IS '补录状态';
COMMENT ON COLUMN business.sl_volunteer_actions.action_status IS '活动状态';
COMMENT ON COLUMN business.sl_volunteer_actions.action_type IS '活动类型';
COMMENT ON COLUMN business.sl_volunteer_actions.action_tag IS '活动标签';
COMMENT ON COLUMN business.sl_volunteer_actions.action_start_time IS '活动时间-开始';
COMMENT ON COLUMN business.sl_volunteer_actions.action_end_time IS '活动时间-截止';
COMMENT ON COLUMN business.sl_volunteer_actions.action_employ IS '已录用';


CREATE SEQUENCE gis_fid_sequence START 1 INCREMENT 1;

CREATE TABLE business.sl_social_worker_onenet
(
    onenet_worker_id     int8 primary key      not null,
    social_worker_id     int8                  null,
    onenet_work_location varchar(90)           null,
    onenet_telephone     varchar(25)           null,
    onenet_sex           varchar(5)            null,
    onenet_id_no         varchar(32)           null,
    onenet_id_type       varchar(5)            null,
    onenet_json          text                  null,                         -- 安全不赋值，保留
    onenet_name          varchar(20)           null,
    social_worker_gem    geometry(point, 4326) NULL,
    fid                  bigint    DEFAULT nextval('gis_fid_sequence') null, -- 不要去处理该值
    --
    del_flag             bpchar(1) default '0',
    status               bpchar(1) default '0',
    create_by            varchar(24)           not null,                     -- 由谁创建
    create_time          timestamp             not null,                     -- 创建时间
    update_time          timestamp             NULL,
    update_by            varchar(12)           NULL
);
COMMENT ON TABLE business.sl_social_worker_onenet IS '社会工作者一网共享数据';
COMMENT ON COLUMN business.sl_social_worker_onenet.social_worker_id IS '社工表主键';
COMMENT ON COLUMN business.sl_social_worker_onenet.onenet_work_location IS '社工二标工作地址';
COMMENT ON COLUMN business.sl_social_worker_onenet.onenet_telephone IS '电话';
COMMENT ON COLUMN business.sl_social_worker_onenet.onenet_sex IS '性别';
COMMENT ON COLUMN business.sl_social_worker_onenet.onenet_id_no IS '证件号';
COMMENT ON COLUMN business.sl_social_worker_onenet.onenet_id_type IS '证件号类型';
COMMENT ON COLUMN business.sl_social_worker_onenet.onenet_json IS '数据json';
COMMENT ON COLUMN business.sl_social_worker_onenet.onenet_name IS '社工名';


ALTER TABLE business.sl_social_worker
    ADD COLUMN onenet_json text null;
COMMENT ON COLUMN business.sl_social_worker.onenet_json IS '数据json';

CREATE TABLE business.sl_gas_onenet
(
    onenet_gas_id    bigint primary key    not null,
    gas_id           bigint                null,
    gas_subject_name varchar(30)           null,
    gas_gem          geometry(point, 4490) NULL,
    fid              bigint    DEFAULT nextval('gis_fid_sequence') null, -- 不要去处理该值
    onenet_json      text                  null,                         -- 安全不赋值，保留
    --
    del_flag         bpchar(1) default '0',
    status           bpchar(1) default '0',
    create_by        varchar(24)           not null,                     -- 由谁创建
    create_time      timestamp             not null,                     -- 创建时间
    update_time      timestamp             NULL,
    update_by        varchar(12)           NULL
);
COMMENT ON TABLE business.sl_gas_onenet IS '燃气共享数据坐标';
COMMENT ON COLUMN business.sl_gas_onenet.gas_id IS '燃气表主键';
COMMENT ON COLUMN business.sl_gas_onenet.gas_subject_name IS 'gas表名称';
COMMENT ON COLUMN business.sl_gas_onenet.onenet_json IS '一网共享数据';


ALTER TABLE business.sl_gas_restaurant_gather
    ADD COLUMN standard_location varchar(120) null;
COMMENT ON COLUMN business.sl_gas_restaurant_gather.standard_location IS '二标标准地址';


CREATE TABLE business.sl_rental_house
(
    rental_house_id             bigint primary key not null,
    grid_zone                   varchar(20)        null,
    building_address            varchar(200)       null,
    building_type               varchar(10)        null,
    updated_time                timestamp          null,
    created_time                timestamp          null,
    updater                     varchar(10)        null,
    creator                     varchar(10)        null,
    rental_type                 varchar(20)        null,
    rental_lv                   varchar(10)        null,
    sign_name                   varchar(20)        null,
    belong_to_building          varchar(120)       null,
    main_subject_address        varchar(200)       null,
    has_fill_code               varchar(5)         null,
    rent_house_fill_code        varchar(50)        null,
    has_checked_address         varchar(5)         null,
    fill_address                varchar(200)       null,
    has_sign_safe_contract      varchar(5)         null,
    available_rent_room_count   int4               null,
    rented_room_count           int4               null,
    renter_count                int4               null,
    rental_building_floor_count int4               null,
    rented_floor_idx            int4               null,
    building_struct             varchar(9)         null,
    building_owner_name         varchar(30)        null,
    building_owner_tel          varchar(30)        null,
    has_manager                 varchar(3)         null,
    second_hand_manager         varchar(10)        null,
    second_hand_tel             varchar(30)        null,
    manager_name                varchar(30)        null,
    manager_tel                 varchar(30)        null,
    mark                        varchar(200)       null,
    working_tags                varchar(20)        null,
    supply_tags                 varchar(100)       null,
    --
    del_flag                    bpchar(1) default '0',
    status                      bpchar(1) default '0',
    create_by                   varchar(24)        not null, -- 由谁创建
    create_time                 timestamp          not null, -- 创建时间
    update_time                 timestamp          NULL,
    update_by                   varchar(12)        NULL
);

COMMENT ON TABLE business.sl_rental_house IS '出租屋信息表';
COMMENT ON COLUMN business.sl_rental_house.grid_zone IS '所属网格';
COMMENT ON COLUMN business.sl_rental_house.building_address IS '所属建筑地址';
COMMENT ON COLUMN business.sl_rental_house.building_type IS '房屋类型';
COMMENT ON COLUMN business.sl_rental_house.updated_time IS '更新时间';
COMMENT ON COLUMN business.sl_rental_house.created_time IS '创建时间';
COMMENT ON COLUMN business.sl_rental_house.updater IS '更新人';
COMMENT ON COLUMN business.sl_rental_house.creator IS '创建人';
COMMENT ON COLUMN business.sl_rental_house.rental_type IS '出租屋类别';
COMMENT ON COLUMN business.sl_rental_house.rental_lv IS '出租屋分级';
COMMENT ON COLUMN business.sl_rental_house.sign_name IS '招牌名称';
COMMENT ON COLUMN business.sl_rental_house.belong_to_building IS '所属建筑';
COMMENT ON COLUMN business.sl_rental_house.main_subject_address IS '主体地址';
COMMENT ON COLUMN business.sl_rental_house.has_fill_code IS '有无备案登记号';
COMMENT ON COLUMN business.sl_rental_house.rent_house_fill_code IS '出租屋备案号';
COMMENT ON COLUMN business.sl_rental_house.has_checked_address IS '核查实际地址是否与登记地址一致';
COMMENT ON COLUMN business.sl_rental_house.fill_address IS '备案登记地址';
COMMENT ON COLUMN business.sl_rental_house.has_sign_safe_contract IS '是否签订治安安全责任书';
COMMENT ON COLUMN business.sl_rental_house.available_rent_room_count IS '可供出租房间数';
COMMENT ON COLUMN business.sl_rental_house.rented_room_count IS '已出租房间数';
COMMENT ON COLUMN business.sl_rental_house.renter_count IS '租住人数';
COMMENT ON COLUMN business.sl_rental_house.rental_building_floor_count IS '最大楼层数';
COMMENT ON COLUMN business.sl_rental_house.rented_floor_idx IS '出租层数';
COMMENT ON COLUMN business.sl_rental_house.building_struct IS '建筑结构';
COMMENT ON COLUMN business.sl_rental_house.building_owner_name IS '房东姓名';
COMMENT ON COLUMN business.sl_rental_house.building_owner_tel IS '房东手机号';
COMMENT ON COLUMN business.sl_rental_house.has_manager IS '是否有二房东';
COMMENT ON COLUMN business.sl_rental_house.second_hand_manager IS '二手房东姓名';
COMMENT ON COLUMN business.sl_rental_house.second_hand_tel IS '二手房东手机号';
COMMENT ON COLUMN business.sl_rental_house.manager_name IS '管理员姓名';
COMMENT ON COLUMN business.sl_rental_house.manager_tel IS '管理员手机号';
COMMENT ON COLUMN business.sl_rental_house.mark IS '备注信息';
COMMENT ON COLUMN business.sl_rental_house.working_tags IS '作业标签';
COMMENT ON COLUMN business.sl_rental_house.supply_tags IS '辅助标签';



CREATE TABLE business.sl_rental_house_onenet
(
    rental_house_onenet_id int8 primary key      not null,
    rental_house_id        int8                  not null,
    smart_onenet_code      varchar(30)           null,
    rental_house_gem       geometry(point, 4490) NULL,
    fid                    bigint    DEFAULT nextval('gis_fid_sequence') null, -- 不要去处理该值
    onenet_json            text                  null,                         -- 安全不赋值，保留
    --
    del_flag               bpchar(1) default '0',
    status                 bpchar(1) default '0',
    create_by              varchar(24)           not null,                     -- 由谁创建
    create_time            timestamp             not null,                     -- 创建时间
    update_time            timestamp             NULL,
    update_by              varchar(12)           NULL
);
CREATE INDEX idx_sl_rental_house_onenet ON business.sl_rental_house_onenet USING btree (rental_house_id);

COMMENT ON TABLE business.sl_rental_house_onenet IS '出租屋信息表';
COMMENT ON COLUMN business.sl_rental_house_onenet.smart_onenet_code IS '智网系统编号';
COMMENT ON COLUMN business.sl_rental_house_onenet.onenet_json IS '请求结果';

CREATE TABLE business.sl_market_enterprise
(
    market_enterprise_id   int8 primary key not null,
    enterprise_credit_code varchar(100)     null,
    enterprise_name        varchar(100)     null,
    enterprise_address     varchar(200)     null,
    enterprise_type        varchar(100)     null,
    create_date            varchar(30)      null,
    operating_end_date     varchar(30)      null,
    operating_scope        text             null,
    legal_person           varchar(30)      null,
    id_type                varchar(50)      null,
    id_code                varchar(50)      null,
    currency_register      varchar(20)      null,
    --
    del_flag               bpchar(1) default '0',
    status                 bpchar(1) default '0',
    create_by              varchar(24)      not null, -- 由谁创建
    create_time            timestamp        not null, -- 创建时间
    update_time            timestamp        NULL,
    update_by              varchar(12)      NULL
);

CREATE TABLE business.sl_market_enterprise_onenet
(
    market_enterprise_onenet_id int8 primary key      not null,
    market_enterprise_id        int8                  not null,
    enterprise_credit_code      varchar(100)          null,
    enterprise_name             varchar(100)          null,
    enterprise_address          varchar(200)          null,
    market_enterprise_gem       geometry(point, 4490) NULL,
    fid                         bigint    DEFAULT nextval('gis_fid_sequence') null, -- 不要去处理该值
    onenet_json                 text                  null,                         -- 安全不赋值，保留
    --
    del_flag                    bpchar(1) default '0',
    status                      bpchar(1) default '0',
    create_by                   varchar(24)           not null,                     -- 由谁创建
    create_time                 timestamp             not null,                     -- 创建时间
    update_time                 timestamp             NULL,
    update_by                   varchar(12)           NULL
);


CREATE TABLE thirdparty.dgsscztxx
(
    XYML     varchar(50)      null,
    DLZJ     text             null,
    SCBZ     varchar(50)      null,
    ETLRQ    varchar(50)      null,
    CYRS     varchar(10)      null,
    ZJ       int8 primary key not null,
    FDDBRXM  varchar(50)      null,
    XYDM     varchar(50)      null,
    JYZZQXZ  timestamp        null,
    SCZTMC   varchar(100)     null,
    CS       varchar(100)     null,
    JSSJ     varchar(50)      null,
    KSSJ     varchar(50)      null,
    SCZTLX   varchar(50)      null,
    ZSDZBM   varchar(100)     null,
    TYSHXYDM varchar(100)     null,
    ZTSFDM   varchar(100)     null,
    GB       varchar(20)      null,
    HZRQ     timestamp        null,
    DJGXJG   varchar(100)     null,
    ZCZB     varchar(100)     null,
    JYZT     varchar(100)     null,
    GXSJ     varchar(50)      null,
    JYFW     text             null,
    JYZZQXZ1 timestamp        null,
    ZCH      varchar(50)      null,
    CLRQ     timestamp        null
);


CREATE TABLE business.sl_market_personal
(
    market_personal_id   int8 primary key not null,
    personal_credit_code varchar(100)     null,
    personal_name        varchar(100)     null,
    personal_address     varchar(200)     null,
    personal_type        varchar(100)     null,
    create_date          varchar(30)      null,
    operating_end_date   varchar(30)      null,
    operating_scope      text             null,
    legal_person         varchar(30)      null,
    id_type              varchar(50)      null,
    id_code              varchar(50)      null,
    currency_register    varchar(20)      null,
    --
    del_flag             bpchar(1) default '0',
    status               bpchar(1) default '0',
    create_by            varchar(24)      not null, -- 由谁创建
    create_time          timestamp        not null, -- 创建时间
    update_time          timestamp        NULL,
    update_by            varchar(12)      NULL
);

CREATE TABLE business.sl_market_personal_onenet
(
    market_personal_onenet_id int8 primary key      not null,
    market_personal_id        int8                  not null,
    personal_credit_code      varchar(100)          null,
    personal_name             varchar(100)          null,
    personal_address          varchar(200)          null,
    market_personal_gem       geometry(point, 4490) NULL,
    fid                       bigint    DEFAULT nextval('gis_fid_sequence') null, -- 不要去处理该值
    onenet_json               text                  null,                         -- 安全不赋值，保留
    --
    del_flag                  bpchar(1) default '0',
    status                    bpchar(1) default '0',
    create_by                 varchar(24)           not null,                     -- 由谁创建
    create_time               timestamp             not null,                     -- 创建时间
    update_time               timestamp             NULL,
    update_by                 varchar(12)           NULL
);


CREATE TABLE business.sl_case_event
(
    case_event_id            int8 primary key not null,
    case_event_code          varchar(50)      null,
    case_event_source        varchar(20)      null,
    case_event_street        varchar(30)      null,
    case_event_zone          varchar(30)      null,
    case_event_grid          varchar(10)      null,
    case_event_department    varchar(50)      null,
    case_event_reason        varchar(200)     null,
    case_event_subject       varchar(100)     null,
    case_event_address       varchar(300)     null,
    case_event_create_time   timestamp        null,
    case_event_end_date      timestamp        null,
    case_event_handler_org   varchar(50)      null,
    case_event_status        varchar(10)      null,
    case_event_mark          text             null,
    case_event_gather_source varchar(50)      null,
    --
    del_flag                 bpchar(1) default '0',
    status                   bpchar(1) default '0',
    create_by                varchar(24)      not null, -- 由谁创建
    create_time              timestamp        not null, -- 创建时间
    update_time              timestamp        NULL,
    update_by                varchar(12)      NULL
);
COMMENT ON TABLE business.sl_case_event IS '案时间表';
COMMENT ON COLUMN business.sl_case_event.case_event_code IS '案事件编号';
COMMENT ON COLUMN business.sl_case_event.case_event_source IS '案事件来源';
COMMENT ON COLUMN business.sl_case_event.case_event_street IS '镇街';
COMMENT ON COLUMN business.sl_case_event.case_event_zone IS '社区';
COMMENT ON COLUMN business.sl_case_event.case_event_grid IS '网格';
COMMENT ON COLUMN business.sl_case_event.case_event_department IS '所属部门';
COMMENT ON COLUMN business.sl_case_event.case_event_reason IS '隐患描述';
COMMENT ON COLUMN business.sl_case_event.case_event_subject IS '主体名称';
COMMENT ON COLUMN business.sl_case_event.case_event_address IS '地址';
COMMENT ON COLUMN business.sl_case_event.case_event_create_time IS '生成时间';
COMMENT ON COLUMN business.sl_case_event.case_event_end_date IS '处理期限';
COMMENT ON COLUMN business.sl_case_event.case_event_handler_org IS '处置部门';
COMMENT ON COLUMN business.sl_case_event.case_event_status IS '状态';
COMMENT ON COLUMN business.sl_case_event.case_event_mark IS '描述';
COMMENT ON COLUMN business.sl_case_event.case_event_gather_source IS '采集字段信息';

CREATE TABLE business.sl_case_event_onenet
(
    case_event_onenet_id int8 primary key      not null,
    case_event_code      varchar(50)           null,
    case_event_address   varchar(300)          null,
    case_event_id        int8                  not null,
    case_event_gem       geometry(point, 4490) NULL,
    fid                  bigint    DEFAULT nextval('gis_fid_sequence') null, -- 不要去处理该值
    onenet_json          text                  null,                         -- 安全不赋值，保留
    --
    del_flag             bpchar(1) default '0',
    status               bpchar(1) default '0',
    create_by            varchar(24)           not null,                     -- 由谁创建
    create_time          timestamp             not null,                     -- 创建时间
    update_time          timestamp             NULL,
    update_by            varchar(12)           NULL
);

CREATE TABLE thirdparty.sl_case_event
(
    FYWZJ  varchar(60) null, -- 非业务主键
    ASJID  varchar(60) null, -- 案事件id
    ZTID   varchar(60) null, -- 主体id
    ZTMC   varchar(60) null, -- 主体名称
    JZWDZ  varchar(60) null, -- 建筑物地址
    JD     varchar(60) null, -- 经度
    WD     varchar(60) null, -- 纬度
    YHMS   text        null, -- 隐患描述
    CZBMBM varchar(60) null, -- 处置部门编码
    CZBMMC varchar(60) null, -- 处置部门名称
    CZQX   varchar(60) null, -- 处置期限
    CLRXM  varchar(60) null, -- 处理人姓名
    YYLJ   varchar(60) null, -- 语音路径
    TPLJ   varchar(60) null, -- 图片路径
    CZJZRQ date        null, -- 处置截止日期
    SCSJ   timestamp   null, -- 生成时间
    DLZJ   varchar(60) null, -- 代理主键
    XZSJ   timestamp   null, -- 新增时间
    ZLBS   varchar(60) null, -- 增量标识
    ZLSJ   timestamp   null, -- 增量时间
    PCH    varchar(60) null  -- 批次号
);

CREATE INDEX idx_sl_case_event ON thirdparty.sl_case_event USING HASH (ASJID);


CREATE TABLE business.sl_weather_station_onenet
(
    station_onenet_id int8 primary key      not null,
    station_name      varchar(50)           null,
    station_gem       geometry(point, 4490) NULL,
    fid               bigint    DEFAULT nextval('gis_fid_sequence') null, -- 不要去处理该值
    --
    del_flag          bpchar(1) default '0',
    status            bpchar(1) default '0',
    create_by         varchar(24)           not null,                     -- 由谁创建
    create_time       timestamp             not null,                     -- 创建时间
    update_time       timestamp             NULL,
    update_by         varchar(12)           NULL
);
COMMENT ON TABLE business.sl_market_onenet IS '气象站点地图表';

CREATE TABLE business.sl_market_onenet
(
    market_id      int8 primary key      not null,
    market_data_id int8                  not null,
    market_gem     geometry(point, 4490) NULL,
    market_address varchar(500)          null,
    fid            bigint    DEFAULT nextval('gis_fid_sequence') null, -- 不要去处理该值
    --
    del_flag       bpchar(1) default '0',
    status         bpchar(1) default '0',
    create_by      varchar(24)           not null,                     -- 由谁创建
    create_time    timestamp             not null,                     -- 创建时间
    update_time    timestamp             NULL,
    update_by      varchar(12)           NULL
);
COMMENT ON TABLE business.sl_market_onenet IS '前置机市场地图表';



DROP FUNCTION find_records_with_point(double precision,double precision);



CREATE OR REPLACE FUNCTION find_records_with_point(x_coord double precision, y_coord double precision)
    RETURNS TABLE (id text) AS
$$
DECLARE
    rec record;
BEGIN
    FOR rec IN SELECT gird_name, geom
               FROM business.sl_business_zwwg
        LOOP
            -- 检查点是否包含在几何对象内
            IF ST_Intersects(rec.geom, ST_SetSRID(ST_MakePoint(x_coord, y_coord), '4490')) THEN
                -- 插入满足条件的 ID 到返回表中
                id := rec.gird_name::text;
                RETURN NEXT;
            END IF;
        END LOOP;
END;
$$
    LANGUAGE plpgsql;


CREATE TABLE business.sl_important_population
(
    population_id      int8 primary key      not null,
    grid_name varchar(10) default null,
    name varchar(15) default null,
    sex varchar(20) default null,
    brith_date timestamp default null,
    id_code varchar(60) default null,
    base_location varchar(500) default null,
    population_type varchar(20) default null,
    family_location varchar(500) default null,
    phone varchar(30) default null,
    standard_location varchar(500) default null,
    living_location varchar(500) default null,
    working_location varchar(500) default null,
    --
    del_flag       bpchar(1) default '0',
    status         bpchar(1) default '0',
    create_by      varchar(24)           not null,                     -- 由谁创建
    create_time    timestamp             not null,                     -- 创建时间
    update_time    timestamp             NULL,
    update_by      varchar(12)           NULL
);
COMMENT ON TABLE business.sl_important_population IS '重点优抚对象';
COMMENT ON COLUMN business.sl_important_population.grid_name IS '行政区划';
COMMENT ON COLUMN business.sl_important_population.name IS '姓名';
COMMENT ON COLUMN business.sl_important_population.sex IS '性别';
COMMENT ON COLUMN business.sl_important_population.brith_date IS '出生日期';
COMMENT ON COLUMN business.sl_important_population.id_code IS '身份证号';
COMMENT ON COLUMN business.sl_important_population.base_location IS '户籍地';
COMMENT ON COLUMN business.sl_important_population.population_type IS '人员类别';
COMMENT ON COLUMN business.sl_important_population.family_location IS '家庭住址';
COMMENT ON COLUMN business.sl_important_population.phone IS '手机号码';
COMMENT ON COLUMN business.sl_important_population.standard_location IS '二标地址';
COMMENT ON COLUMN business.sl_important_population.living_location IS '居住地址';
COMMENT ON COLUMN business.sl_important_population.working_location IS '工作地址';

CREATE TABLE business.sl_disable_population
(
    disable_id      int8 primary key      not null,
    name varchar(15) default null,
    id_code varchar(60) default null,
    sex varchar(20) default null,
    nation varchar(20) default null,
    brith_date timestamp default null,
    base_location varchar(500) default null,
    population_type varchar(20) default null,
    living_location varchar(500) default null,
    working_location varchar(500) default null,
    --
    del_flag       bpchar(1) default '0',
    status         bpchar(1) default '0',
    create_by      varchar(24)           not null,                     -- 由谁创建
    create_time    timestamp             not null,                     -- 创建时间
    update_time    timestamp             NULL,
    update_by      varchar(12)           NULL
);
COMMENT ON TABLE business.sl_disable_population IS '残疾人';
COMMENT ON COLUMN business.sl_disable_population.name IS '姓名';
COMMENT ON COLUMN business.sl_disable_population.id_code IS '身份证号';
COMMENT ON COLUMN business.sl_disable_population.sex IS '性别';
COMMENT ON COLUMN business.sl_disable_population.nation IS '民族';
COMMENT ON COLUMN business.sl_disable_population.brith_date IS '出生日期';
COMMENT ON COLUMN business.sl_disable_population.base_location IS '户籍地址';
COMMENT ON COLUMN business.sl_disable_population.population_type IS '类型';
COMMENT ON COLUMN business.sl_disable_population.living_location IS '居住地址';
COMMENT ON COLUMN business.sl_disable_population.working_location IS '工作地址';


CREATE TABLE business.sl_old_population
(
    old_id      int8 primary key      not null,
    grid_name varchar(20) default null,
    name varchar(15) default null,
    sex varchar(20) default null,
    id_code varchar(60) default null,
    age int4 default null,
    base_location varchar(500) default null,
    population_type varchar(20) default null,
    living_nursing varchar(20) default null,
    disable_lv varchar(20) default null,
    living_location varchar(500) default null,
    working_location varchar(500) default null,
    --
    del_flag       bpchar(1) default '0',
    status         bpchar(1) default '0',
    create_by      varchar(24)           not null,                     -- 由谁创建
    create_time    timestamp             not null,                     -- 创建时间
    update_time    timestamp             NULL,
    update_by      varchar(12)           NULL
);
COMMENT ON TABLE business.sl_old_population IS '老年失能';
COMMENT ON COLUMN business.sl_old_population.grid_name IS '社区、村';
COMMENT ON COLUMN business.sl_old_population.name IS '姓名';
COMMENT ON COLUMN business.sl_old_population.sex IS '性别';
COMMENT ON COLUMN business.sl_old_population.id_code IS '身份证号码';
COMMENT ON COLUMN business.sl_old_population.age IS '年龄';
COMMENT ON COLUMN business.sl_old_population.base_location IS '户籍地址';
COMMENT ON COLUMN business.sl_old_population.population_type IS '身份属性';
COMMENT ON COLUMN business.sl_old_population.living_nursing IS '是否入住养老机构';
COMMENT ON COLUMN business.sl_old_population.disable_lv IS '评定失能程度';
COMMENT ON COLUMN business.sl_old_population.living_location IS '现居地地址';
COMMENT ON COLUMN business.sl_old_population.working_location IS '就业单位地址';



CREATE TABLE business.sl_special_population
(
    special_id      int8 primary key      not null,
    grid_name varchar(20) default null,
    net_name varchar(20) default null,
    name varchar(15) default null,
    sex varchar(20) default null,
    age int4 default null,
    id_code varchar(60) default null,
    phone varchar(50) default null,
    living_location varchar(500) default null,
    living_type varchar(10) default null,
    population_type varchar(20) default null,
    vaccinum varchar(10) default null,
    health_type varchar(20) default null,
    body_type varchar(20) default null,
    medicine text default null,
    doctor varchar(500) default null,
    help_manager varchar(500) default null,
    grid_manager text default null,
    mark text   default null,
    --
    del_flag       bpchar(1) default '0',
    status         bpchar(1) default '0',
    create_by      varchar(24)           not null,                     -- 由谁创建
    create_time    timestamp             not null,                     -- 创建时间
    update_time    timestamp             NULL,
    update_by      varchar(12)           NULL
);
COMMENT ON TABLE business.sl_special_population IS '老年失能';
COMMENT ON COLUMN business.sl_special_population.grid_name IS '所属村（社区）';
COMMENT ON COLUMN business.sl_special_population.net_name IS '所属网格';
COMMENT ON COLUMN business.sl_special_population.name IS '姓名';
COMMENT ON COLUMN business.sl_special_population.sex IS '性别';
COMMENT ON COLUMN business.sl_special_population.age IS '年龄';
COMMENT ON COLUMN business.sl_special_population.id_code IS '身份证';
COMMENT ON COLUMN business.sl_special_population.phone IS '联系电话';
COMMENT ON COLUMN business.sl_special_population.living_location IS '现居住地址';
COMMENT ON COLUMN business.sl_special_population.living_type IS '居住类别';
COMMENT ON COLUMN business.sl_special_population.population_type IS '群体';
COMMENT ON COLUMN business.sl_special_population.vaccinum IS '疫苗接种';
COMMENT ON COLUMN business.sl_special_population.health_type IS '健康类别';
COMMENT ON COLUMN business.sl_special_population.body_type IS '身体状况';
COMMENT ON COLUMN business.sl_special_population.medicine IS '诊疗服药情况（就医频次及药品）';
COMMENT ON COLUMN business.sl_special_population.doctor IS '家庭医生（姓名、联系电话）';
COMMENT ON COLUMN business.sl_special_population.help_manager IS '紧急联系人';
COMMENT ON COLUMN business.sl_special_population.grid_manager IS '网格跟进工作人员';
COMMENT ON COLUMN business.sl_special_population.mark IS '备注';
update sl_business_zwwg set gird_name = '第一网格' where townid  = 1;
update sl_business_zwwg set gird_name = '第二网格' where townid  = 2;
update sl_business_zwwg set gird_name = '第三网格' where townid  = 3;
update sl_business_zwwg set gird_name = '第四网格' where townid  = 4;
update sl_business_zwwg set gird_name = '第五网格' where townid  = 5;
update sl_business_zwwg set gird_name = '第六网格' where townid  = 6;
update sl_business_zwwg set gird_name = '第七网格' where townid  = 7;
update sl_business_zwwg set gird_name = '第八网格' where townid  = 8;
update sl_business_zwwg set gird_name = '第九网格' where townid  = 9;
update sl_business_zwwg set gird_name = '第十网格' where townid  = 10;
update sl_business_zwwg set gird_name = '第十一网格' where townid  = 11;
update sl_business_zwwg set gird_name = '第十二网格' where townid  = 12;
update sl_business_zwwg set gird_name = '第十三网格' where townid  = 13;
update sl_business_zwwg set gird_name = '第十四网格' where townid  = 14;
update sl_business_zwwg set gird_name = '第十五网格' where townid  = 15;
update sl_business_zwwg set gird_name = '第十六网格' where townid  = 16;
update sl_business_zwwg set gird_name = '第十七网格' where townid  = 17;
update sl_business_zwwg set gird_name = '第十八网格' where townid  = 18;
update sl_business_zwwg set gird_name = '第十九网格' where townid  = 19;
update sl_business_zwwg set gird_name = '第二十网格' where townid  = 20;
update sl_business_zwwg set gird_name = '第二十一网格' where townid  = 21;
update sl_business_zwwg set gird_name = '第二十二网格' where townid  = 22;
update sl_business_zwwg set gird_name = '第二十三网格' where townid  = 23;
update sl_business_zwwg set gird_name = '第二十四网格' where townid  = 24;
update sl_business_zwwg set gird_name = '第二十五网格' where townid  = 25;
update sl_business_zwwg set gird_name = '第二十六网格' where townid  = 26;
update sl_business_zwwg set gird_name = '第二十七网格' where townid  = 27;
update sl_business_zwwg set gird_name = '第二十八网格' where townid  = 28;
update sl_business_zwwg set gird_name = '第二十九网格' where townid  = 29;
update sl_business_zwwg set gird_name = '第三十网格' where townid  = 30;


alter table  business.sl_market_onenet add column market_manager varchar(50) ;

alter table business.sl_market_onenet  add column market_code varchar(100);

alter table business.sl_market_onenet  add column market_name varchar(30);

alter table business.sl_market_onenet add business_scope text;


CREATE TABLE business.zsj_entreg_info_audit
(
    audit_id    SERIAL PRIMARY KEY,     -- 自增字段作为主键
    market_id   int8,                   -- int8 字段，用于关联其他表的 ID
    address     text,                   -- 地址信息字段
    del_flag     bpchar(1) default '0',
    inserted_at timestamp DEFAULT now() -- 时间戳字段，记录插入时间
);

COMMENT ON TABLE business.zsj_entreg_info_audit IS '市场审计表';
COMMENT ON COLUMN business.zsj_entreg_info_audit.market_id IS 'zsj_entreg_info表的id';
COMMENT ON COLUMN business.zsj_entreg_info_audit.address IS 'zsj_entreg_info表的dom字段记录地址';


GRANT USAGE, SELECT ON SEQUENCE business.zsj_entreg_info_audit_audit_id_seq TO ywtg;
GRANT insert,select ON TABLE business.zsj_entreg_info_audit TO ywtg;


CREATE OR REPLACE FUNCTION audit_market_func()
    RETURNS TRIGGER AS $$
BEGIN
    -- 检查是否存在相同的 otherId 和 address 记录
    IF EXISTS (
        SELECT 1
        FROM business.zsj_entreg_info_audit
        WHERE pripid = NEW.pripid
          AND address = NEW.dom
        ) THEN
        RETURN NEW;
    ELSE
        -- 如果不存在相同记录，则执行插入操作
        INSERT INTO business.zsj_entreg_info_audit(pripid, address) VALUES (new.pripid, new.dom);
        RETURN NEW;
    END IF;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER market_after_insert_trigger
    BEFORE INSERT ON public.zsj_entreg_info
    FOR EACH ROW
EXECUTE FUNCTION audit_market_func();

ALTER TABLE business.zsj_entreg_info_audit ADD COLUMN pripid VARCHAR(255) DEFAULT NULL;
ALTER TABLE  business.sl_market_onenet
    ALTER COLUMN market_data_id TYPE varchar(255) USING market_data_id::varchar;
ALTER TABLE  business.sl_market_personal_onenet
    ALTER COLUMN market_personal_id TYPE varchar(255) USING market_personal_id::varchar;
ALTER TABLE  business.sl_market_enterprise_onenet
    ALTER COLUMN market_enterprise_id TYPE varchar(255) USING market_enterprise_id::varchar;
