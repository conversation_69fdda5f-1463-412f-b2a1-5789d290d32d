CREATE TABLE business.sl_manual_define_gis
(
    define_id      int8 primary key      not null,
    define_type    varchar(20)           null,
    mark           text                  null,
    define_gis     geometry(point, 4326) NULL,
    define_address varchar(250)          null,
    --
    del_flag       bpchar(1) default '0',
    status         bpchar(1) default '0',
    create_by      varchar(24)           not null, -- 由谁创建
    create_time    timestamp             not null, -- 创建时间
    update_time    timestamp             NULL,
    update_by      varchar(12)           NULL
);
COMMENT ON TABLE business.sl_manual_define_gis IS '自定义输入GIS坐标点';
COMMENT ON COLUMN business.sl_manual_define_gis.define_type IS '类型';
COMMENT ON COLUMN business.sl_manual_define_gis.mark IS '说明';
COMMENT ON COLUMN business.sl_manual_define_gis.define_address IS '地址';


-- 石龙基础部位信息GIS表 ----
CREATE TABLE business.sl_prevention_disaster_gis
(
    gis_id      int8 primary key      not null,
    define_type varchar(20)           null,
    data_id     varchar(40)           null,
    gis_geom    geometry(point, 4326) NULL
);
COMMENT ON TABLE business.sl_prevention_disaster_gis IS '基础部件地理信息表';
COMMENT ON COLUMN business.sl_prevention_disaster_gis.define_type IS '类型';
COMMENT ON COLUMN business.sl_prevention_disaster_gis.data_id IS '数据ID';
COMMENT ON COLUMN business.sl_prevention_disaster_gis.gis_geom IS '地理数据';

-- 石龙救援力量表----
CREATE TABLE business.sl_rescue_forces_team
(
    forces_id   int8 primary key not null,
    team_name   varchar(30)      null,
    team_leader varchar(15)      null,
    team_phone  varchar(36)      null,
    team_org    varchar(30)      null,
    team_size   int4             null,
    --
    del_flag    bpchar(1) default '0',
    status      bpchar(1) default '0',
    create_by   varchar(24)      not null, -- 由谁创建
    create_time timestamp        not null, -- 创建时间
    update_time timestamp        NULL,
    update_by   varchar(12)      NULL
);
COMMENT ON TABLE business.sl_rescue_forces_team IS '石龙救援力量表';
COMMENT ON COLUMN business.sl_rescue_forces_team.team_name IS '队伍名称';
COMMENT ON COLUMN business.sl_rescue_forces_team.team_leader IS '领导姓名';
COMMENT ON COLUMN business.sl_rescue_forces_team.team_phone IS '联系电话';
COMMENT ON COLUMN business.sl_rescue_forces_team.team_org IS '队伍所属组织';
COMMENT ON COLUMN business.sl_rescue_forces_team.team_size IS '队伍规模';

--- 物资表 --------
CREATE TABLE business.sl_rescue_forces_material
(
    material_id      int8 primary key not null,
    material_name    varchar(30)      null,
    material_size    int8             null,
    storage_location varchar(50)      null,
    statistical_year varchar(10)      null,
    --
    del_flag         bpchar(1) default '0',
    status           bpchar(1) default '0',
    create_by        varchar(24)      not null, -- 由谁创建
    create_time      timestamp        not null, -- 创建时间
    update_time      timestamp        NULL,
    update_by        varchar(12)      NULL
);
COMMENT ON TABLE business.sl_rescue_forces_material IS '物资表';
COMMENT ON COLUMN business.sl_rescue_forces_material.material_name IS '物资名称';
COMMENT ON COLUMN business.sl_rescue_forces_material.material_size IS '材料总数';
COMMENT ON COLUMN business.sl_rescue_forces_material.storage_location IS '存储位置';
COMMENT ON COLUMN business.sl_rescue_forces_material.statistical_year IS '统计年份';
----

alter table business.sl_manual_define_gis
    add column manager varchar(30) default null;
alter table business.sl_manual_define_gis
    add column phone varchar(40) default null;
alter table business.sl_manual_define_gis
    add column org_name varchar(60) default null;
alter table business.sl_manual_define_gis
    add column define_name varchar(50) default null;
COMMENT ON COLUMN business.sl_manual_define_gis.manager IS '管理人';
COMMENT ON COLUMN business.sl_manual_define_gis.phone IS '电话';
COMMENT ON COLUMN business.sl_manual_define_gis.org_name IS '负责部门/村（社区）';
COMMENT ON COLUMN business.sl_manual_define_gis.define_name IS '名称';



ALTER TABLE business.sl_rescue_forces_material
    ALTER COLUMN material_size TYPE varchar(30) USING material_size::varchar;
ALTER TABLE business.sl_rescue_forces_material
    ADD COLUMN all_material_size varchar(30) default null;


--- 三防事件 ----
CREATE TABLE business.sl_rescue_event
(
    event_id    int8 primary key not null,
    event_type  varchar(30)      null,
    event_count int8             null,
    --
    del_flag    bpchar(1) default '0',
    status      bpchar(1) default '0',
    create_by   varchar(24)      not null, -- 由谁创建
    create_time timestamp        not null, -- 创建时间
    update_time timestamp        NULL,
    update_by   varchar(12)      NULL
);
COMMENT ON TABLE business.sl_rescue_event IS '事件统计表';
COMMENT ON COLUMN business.sl_rescue_event.event_type IS '事件名称';
COMMENT ON COLUMN business.sl_rescue_event.event_count IS '数量';

INSERT INTO business.sl_rescue_event
(event_id, event_type, event_count, del_flag, status, create_by, create_time, update_time, update_by)
VALUES (1, '台风高空坠物', 0, '0'::bpchar, '0'::bpchar, 'admin', now(), now(), 'admin');

INSERT INTO business.sl_rescue_event
(event_id, event_type, event_count, del_flag, status, create_by, create_time, update_time, update_by)
VALUES (2, '树木倒伏', 6, '0'::bpchar, '0'::bpchar, 'admin', now(), now(), 'admin');

INSERT INTO business.sl_rescue_event
(event_id, event_type, event_count, del_flag, status, create_by, create_time, update_time, update_by)
VALUES (3, '积水内涝', 40, '0'::bpchar, '0'::bpchar, 'admin', now(), now(), 'admin');


CREATE TABLE business.sl_rescue_warehouse_gis
(
    warehouse_id   serial8 primary key   not null,
    warehouse_name varchar(30)           null,
    geom           geometry(point, 4326) NULL
);
COMMENT ON TABLE business.sl_rescue_warehouse_gis IS '仓库GIS表';
COMMENT ON COLUMN business.sl_rescue_warehouse_gis.warehouse_name IS '仓库名称';

INSERT INTO business.sl_rescue_warehouse_gis (geom, warehouse_name)
VALUES (ST_GeomFromText('POINT(113.863914411296 23.118549385636)'), '镇防汛物资仓库');
INSERT INTO business.sl_rescue_warehouse_gis (geom, warehouse_name)
VALUES (ST_GeomFromText('POINT(113.861576471942 23.1182978553356)'), '新城区排涝站');

CREATE TABLE business.sl_rescue_ponding_gis
(
    ponding_id   serial8 primary key   not null,
    ponding_name varchar(120)          null,
    geom         geometry(point, 4326) NULL
);
INSERT INTO business.sl_rescue_ponding_gis (ponding_name, geom)
VALUES ('红海隧道', ST_GeomFromText('POINT(113.839515573966 23.121487538126)')),
       ('华南花园隧道', ST_GeomFromText('POINT(113.841268406699 23.105115815852)')),
       ('华南桥下穿隧道', ST_GeomFromText('POINT(113.844154063788 23.102628797465)')),
       ('黄家山二村球场', ST_GeomFromText('POINT(113.869831321026 23.125402701674)')),
       ('汇星中心路口', ST_GeomFromText('POINT(113.863988546821 23.111142083772)')),
       ('金塘（顺景大楼）隧道', ST_GeomFromText('POINT(113.*********** 23.108931397658)')),
       ('沙头角隧道', ST_GeomFromText('POINT(113.838322692849 23.111967996434)')),
       ('现代广场（富林酒店路口）', ST_GeomFromText('POINT(113.849871086319 23.111865095494)'));



update cop.t_cop_information
set lon  = '113.85679762108816',
    lat  = '23.101218939346523',
    geom = ST_GeomFromText('POINT(113.85679762108816 23.101218939346523)')
where cop_id = 18485;

update cop.problem_enterprises
set geom = ST_GeomFromText('POINT(113.85679762108816 23.101218939346523)')
where enterprise_id = 18485;


select cop_id, lon, lat, geom
from cop.t_cop_information
where cop_id = 22240;
select enterprise_id, geom
from cop.high_risk_enterprises
where enterprise_id = 22240;
