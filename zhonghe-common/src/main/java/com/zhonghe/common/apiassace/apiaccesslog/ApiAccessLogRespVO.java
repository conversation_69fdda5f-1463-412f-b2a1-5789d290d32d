package com.zhonghe.common.apiassace.apiaccesslog;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;

@ApiModel("管理后台 - API 访问日志 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ApiAccessLogRespVO extends ApiAccessLogBaseVO {

    @ApiModelProperty(value = "日志主键", required = true, example = "1024")
    private Long id;

    @ApiModelProperty(value = "创建时间", required = true)
    private Date createTime;

    @ApiModelProperty(value = "部门ID", example = "1")
    private Long deptId;

    @ApiModelProperty(value = "部门名称", example = "研发部")
    private String deptName;

    @ApiModelProperty(value = "用户昵称", example = "张三")
    private String nickName;

}
