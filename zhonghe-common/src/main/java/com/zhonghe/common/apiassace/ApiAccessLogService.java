package com.zhonghe.common.apiassace;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zhonghe.common.apiassace.apiaccesslog.ApiAccessLogExportReqVO;
import com.zhonghe.common.apiassace.apiaccesslog.ApiAccessLogPageReqVO;
import com.zhonghe.common.apiassace.apierrorlog.ApiAccessLogCreateReqDTO;
import com.zhonghe.common.apiassace.apierrorlog.ApiAccessLogDO;

import java.util.List;

/**
 * API 访问日志 Service 接口
 *
 * <AUTHOR>
 */
public interface ApiAccessLogService {

    /**
     * 创建 API 访问日志
     *
     * @param createReqDTO API 访问日志
     */
    void createApiAccessLog(ApiAccessLogCreateReqDTO createReqDTO);

    /**
     * 获得 API 访问日志分页
     *
     * @param pageReqVO 分页查询
     * @return API 访问日志分页
     */
    IPage getApiAccessLogPage(ApiAccessLogPageReqVO pageReqVO);

    /**
     * 获得 API 访问日志列表, 用于 Excel 导出
     *
     * @param exportReqVO 查询条件
     * @return API 访问日志分页
     */
    List<ApiAccessLogDO> getApiAccessLogList(ApiAccessLogExportReqVO exportReqVO);

    void ftests();

}
