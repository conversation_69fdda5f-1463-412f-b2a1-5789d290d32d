package com.zhonghe.common.apiassace.apierrorlog;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Date;

@ApiModel("管理后台 - API 错误日志 Response VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ApiErrorLogRespVO extends ApiErrorLogBaseVO {

    @ApiModelProperty(value = "编号", required = true, example = "1024")
    private Integer id;

    @ApiModelProperty(value = "用户昵称", example = "张三")
    private String nickName;

    @ApiModelProperty(value = "部门名称", example = "研发部")
    private String deptName;

    @ApiModelProperty(value = "创建时间", required = true)
    private Date createTime;

    @ApiModelProperty(value = "处理时间", required = true)
    private Date processTime;

    @ApiModelProperty(value = "处理用户编号", example = "233")
    private Integer processUserId;

}
