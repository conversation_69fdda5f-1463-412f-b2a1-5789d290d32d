package com.zhonghe.common.apiassace;

import cn.hutool.core.bean.BeanUtil;
import com.zhonghe.common.apiassace.apiaccesslog.ApiAccessLog;
import com.zhonghe.common.apiassace.apierrorlog.ApiAccessLogCreateReqDTO;
import lombok.RequiredArgsConstructor;
import org.springframework.scheduling.annotation.Async;

/**
 * API 访问日志 Framework Service 实现类
 *
 * 基于 {@link ApiAccessLogApi} 服务，记录访问日志
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
public class ApiAccessLogFrameworkServiceImpl implements ApiAccessLogFrameworkService {

    private final ApiAccessLogApi apiAccessLogApi;

    @Override
    @Async
    public void createApiAccessLog(ApiAccessLog apiAccessLog) {
        ApiAccessLogCreateReqDTO reqDTO = BeanUtil.copyProperties(apiAccessLog, ApiAccessLogCreateReqDTO.class);

        apiAccessLogApi.createApiAccessLog(reqDTO);
    }

}
