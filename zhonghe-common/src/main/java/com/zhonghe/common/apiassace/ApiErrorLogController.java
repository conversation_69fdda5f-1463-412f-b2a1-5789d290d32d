package com.zhonghe.common.apiassace;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaMode;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zhonghe.common.annotation.Log;
import com.zhonghe.common.apiassace.apierrorlog.ApiErrorLogDO;
import com.zhonghe.common.apiassace.apierrorlog.ApiErrorLogExcelVO;
import com.zhonghe.common.apiassace.apierrorlog.ApiErrorLogExportReqVO;
import com.zhonghe.common.apiassace.apierrorlog.ApiErrorLogPageReqVO;
import com.zhonghe.common.apiassace.apierrorlog.ApiErrorLogRespVO;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.common.core.domain.entity.SysUser;
import com.zhonghe.common.core.domain.entity.SysDept;
import com.zhonghe.common.mapper.CommonSysUserMapper;
import com.zhonghe.common.mapper.CommonSysDeptMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zhonghe.common.enums.BusinessType;
import com.zhonghe.common.utils.BeanCopyUtils;
import com.zhonghe.common.utils.poi.ExcelUtil;
import com.zhonghe.common.utils.redis.RedisUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Api(tags = "管理后台 - API 错误日志")
@RestController
@RequestMapping("/com/zhonghe/infra/api-error-log")
@Validated
public class ApiErrorLogController {

    @Resource
    private ApiErrorLogService apiErrorLogService;

    @Resource
    private CommonSysUserMapper userMapper;
    
    @Resource
    private CommonSysDeptMapper deptMapper;

    @PutMapping("/update-status")
    @SaCheckPermission("system:error:log:index:update")
    @ApiOperation("更新 API 错误日志的状态")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Long.class),
        @ApiImplicitParam(name = "processStatus", value = "处理状态", required = true, example = "1", dataTypeClass = Integer.class)
    })
    public R<Boolean> updateApiErrorLogProcess(@RequestParam("id") Long id,
                                               @RequestParam("processStatus") Integer processStatus) {
        Map<String, Object> ajax = RedisUtils.getCacheObject("ajax");
        SysUser user = (SysUser) ajax.get("user");
        apiErrorLogService.updateApiErrorLogProcess(id, processStatus, user.getUserId());
        return R.ok(true);
    }

    @GetMapping("/page")
    @SaCheckPermission(value = {"system:error:log:index:query","system:error:log:index:list"} , mode = SaMode.OR)
    @ApiOperation("获得 API 错误日志分页")
    public R<IPage<ApiErrorLogRespVO>> getApiErrorLogPage(@Valid ApiErrorLogPageReqVO pageVO) {
        IPage apiErrorLogPage = apiErrorLogService.getApiErrorLogPage(pageVO);
        List<ApiErrorLogRespVO> apiErrorLogRespVOS = convertPage(apiErrorLogPage);
        
        // 填充用户和部门信息
        apiErrorLogRespVOS = apiErrorLogRespVOS.stream().map(log -> {
            // 查询用户信息
            SysUser user = userMapper.selectOne(new LambdaQueryWrapper<SysUser>()
                .eq(SysUser::getUserId, log.getUserId()));
            
            if (user != null) {
                log.setNickName(user.getNickName());
                
                // 查询部门信息
                if (user.getDeptId() != null) {
                    SysDept dept = deptMapper.selectById(user.getDeptId());
                    if (dept != null) {
                        log.setDeptName(dept.getDeptName());
                    }
                }
            }
            
            return log;
        }).collect(Collectors.toList());
        
        apiErrorLogPage.setRecords(apiErrorLogRespVOS);
        return R.ok(apiErrorLogPage);
    }

    List<ApiErrorLogRespVO> convertPage(IPage<ApiErrorLogDO> pageResult ){
        List<ApiErrorLogDO> records = pageResult.getRecords();
        List<ApiErrorLogRespVO> apiErrorLogRespVOList= new ArrayList<>();
        records.stream().forEach( f ->{
            ApiErrorLogRespVO apiAccessLogRespVO = new ApiErrorLogRespVO();
            apiAccessLogRespVO.setCreateTime(f.getCreateTime());
            apiAccessLogRespVO.setApplicationName(f.getApplicationName());
            apiAccessLogRespVO.setRequestMethod(f.getRequestMethod());
            apiAccessLogRespVO.setRequestParams(f.getRequestParams());
            apiAccessLogRespVO.setRequestUrl(f.getRequestUrl());
            apiAccessLogRespVO.setTraceId(f.getTraceId());
            apiAccessLogRespVO.setUserAgent(f.getUserAgent());
            apiAccessLogRespVO.setUserType(f.getUserType());
            apiAccessLogRespVO.setUserId(f.getUserId().intValue());
            apiAccessLogRespVO.setProcessStatus(f.getProcessStatus());
            apiAccessLogRespVO.setUserIp(f.getUserIp());
            apiAccessLogRespVO.setExceptionFileName(f.getExceptionFileName());
            apiAccessLogRespVO.setProcessTime(f.getProcessTime());
            apiAccessLogRespVO.setProcessUserId(f.getProcessStatus());
            apiAccessLogRespVO.setExceptionClassName(f.getExceptionClassName());
            apiAccessLogRespVO.setExceptionLineNumber(f.getExceptionLineNumber());
            apiAccessLogRespVO.setExceptionMessage(f.getExceptionMessage());
            apiAccessLogRespVO.setExceptionMethodName(f.getExceptionMethodName());
            apiAccessLogRespVO.setExceptionRootCauseMessage(f.getExceptionRootCauseMessage());
            apiAccessLogRespVO.setExceptionStackTrace(f.getExceptionStackTrace());
            apiAccessLogRespVO.setExceptionName(f.getExceptionName());
            apiAccessLogRespVO.setExceptionTime(f.getExceptionTime());
            apiAccessLogRespVO.setId(f.getId().intValue());

            apiErrorLogRespVOList.add(apiAccessLogRespVO);
        });

        return apiErrorLogRespVOList;
    }

    @PostMapping("/export-excel")
    @ApiOperation("导出 API 错误日志 Excel")
    @SaCheckPermission("system:error:log:index:export")
            @Log(title = "导出 API 错误日志 Excel", businessType = BusinessType.EXPORT)
    public void exportApiErrorLogExcel(@Valid ApiErrorLogExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<ApiErrorLogDO> list = apiErrorLogService.getApiErrorLogList(exportReqVO);
        // 导出 Excel
//        List<ApiErrorLogExcelVO> datas = ApiErrorLogConvert.INSTANCE.convertList02(list);
        List<ApiErrorLogExcelVO> datas = convertList02(list);
        ExcelUtil.exportExcel(datas, "错误日志", ApiErrorLogExcelVO.class, response);

    }
    List<ApiErrorLogExcelVO> convertList02(List<ApiErrorLogDO> records){
        List<ApiErrorLogExcelVO> apiErrorLogRespVOList= new ArrayList<>();
        records.stream().forEach( f ->{
            ApiErrorLogExcelVO apiAccessLogRespVO = new ApiErrorLogExcelVO();
            BeanCopyUtils.copy(f, apiAccessLogRespVO);
            apiErrorLogRespVOList.add(apiAccessLogRespVO);
        });
        return apiErrorLogRespVOList;
    }

}
