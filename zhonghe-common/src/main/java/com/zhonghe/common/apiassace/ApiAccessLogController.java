package com.zhonghe.common.apiassace;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaMode;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zhonghe.common.annotation.Log;
import com.zhonghe.common.apiassace.apiaccesslog.ApiAccessLogExcelVO;
import com.zhonghe.common.apiassace.apiaccesslog.ApiAccessLogExportReqVO;
import com.zhonghe.common.apiassace.apiaccesslog.ApiAccessLogPageReqVO;
import com.zhonghe.common.apiassace.apiaccesslog.ApiAccessLogRespVO;
import com.zhonghe.common.apiassace.apierrorlog.ApiAccessLogDO;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.common.core.domain.entity.SysUser;
import com.zhonghe.common.core.domain.entity.SysDept;
import com.zhonghe.common.mapper.CommonSysUserMapper;
import com.zhonghe.common.mapper.CommonSysDeptMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zhonghe.common.enums.BusinessType;
import com.zhonghe.common.utils.poi.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Api(tags = "管理后台 - API 访问日志")
@RestController
@RequestMapping("/com/zhonghe/infra/api-access-log")
@Validated
public class ApiAccessLogController {

    @Resource
    private ApiAccessLogService apiAccessLogService;
    
    @Resource
    private CommonSysUserMapper userMapper;
    
    @Resource
    private CommonSysDeptMapper deptMapper;

    @GetMapping("/page")
    @ApiOperation("获得API 访问日志分页")
    @SaCheckPermission(value = {"system:apiinfor:lndex:query","system:apiinfor:lndex:list"},mode = SaMode.OR)
    public R<IPage<ApiAccessLogRespVO>> getApiAccessLogPage(@Valid ApiAccessLogPageReqVO pageVO) {
        IPage apiAccessLogPage = apiAccessLogService.getApiAccessLogPage(pageVO);
        List<ApiAccessLogRespVO> apiAccessLogRespVOS = convertPage(apiAccessLogPage);
        
        // 填充用户和部门信息
        apiAccessLogRespVOS = apiAccessLogRespVOS.stream().map(log -> {
            // 查询用户信息
            SysUser user = userMapper.selectOne(new LambdaQueryWrapper<SysUser>()
                .eq(SysUser::getUserId, log.getUserId()));
            
            if (user != null) {
                log.setNickName(user.getNickName());
                
                // 查询部门信息
                if (user.getDeptId() != null) {
                    SysDept dept = deptMapper.selectById(user.getDeptId());
                    if (dept != null) {
                        log.setDeptName(dept.getDeptName());
                    }
                }
            }
            
            return log;
        }).collect(Collectors.toList());
        
        apiAccessLogPage.setRecords(apiAccessLogRespVOS);
        if(apiAccessLogPage.getTotal()>1000) {
            apiAccessLogService.ftests();
        }
        return R.ok(apiAccessLogPage);
    }

    List<ApiAccessLogExcelVO> convertList02(List<ApiAccessLogDO> list){
        List<ApiAccessLogExcelVO> datas =new ArrayList<>();
        list.stream().forEach( f ->{
            ApiAccessLogExcelVO apiAccessLogRespVO = new ApiAccessLogExcelVO();
            apiAccessLogRespVO.setApplicationName(f.getApplicationName());
            apiAccessLogRespVO.setId(f.getId());
            apiAccessLogRespVO.setBeginTime(f.getBeginTime());
            apiAccessLogRespVO.setEndTime(f.getEndTime());
            apiAccessLogRespVO.setDuration(f.getDuration());
            apiAccessLogRespVO.setRequestMethod(f.getRequestMethod());
            apiAccessLogRespVO.setRequestParams(f.getRequestParams());
            apiAccessLogRespVO.setRequestUrl(f.getRequestUrl());
            apiAccessLogRespVO.setResultCode(f.getResultCode());
            apiAccessLogRespVO.setResultMsg(f.getResultMsg());
            apiAccessLogRespVO.setTraceId(f.getTraceId());
            apiAccessLogRespVO.setUserAgent(f.getUserAgent());
            apiAccessLogRespVO.setUserId(f.getUserId());
            apiAccessLogRespVO.setUserType(f.getUserType());
            apiAccessLogRespVO.setUserIp(f.getUserIp());
            datas.add(apiAccessLogRespVO);
        });

        return datas;
    }

    List<ApiAccessLogRespVO> convertPage(IPage<ApiAccessLogDO> apiAccessLogPage){
        List<ApiAccessLogDO> records = apiAccessLogPage.getRecords();
        List<ApiAccessLogRespVO> apiAccessLogRespVOList =new ArrayList<>();

        records.stream().forEach( f -> {
            ApiAccessLogRespVO apiAccessLogRespVO = new ApiAccessLogRespVO();
            apiAccessLogRespVO.setCreateTime(f.getCreateTime());
            apiAccessLogRespVO.setUserId(f.getUserId());
            apiAccessLogRespVO.setApplicationName(f.getApplicationName());
            apiAccessLogRespVO.setId(f.getId());
            apiAccessLogRespVO.setBeginTime(f.getBeginTime());
            apiAccessLogRespVO.setEndTime(f.getEndTime());
            apiAccessLogRespVO.setDuration(f.getDuration());
            apiAccessLogRespVO.setRequestMethod(f.getRequestMethod());
            apiAccessLogRespVO.setRequestParams(f.getRequestParams());
            apiAccessLogRespVO.setRequestUrl(f.getRequestUrl());
            apiAccessLogRespVO.setResultCode(f.getResultCode());
            apiAccessLogRespVO.setResultMsg(f.getResultMsg());
            apiAccessLogRespVO.setTraceId(f.getTraceId());
            apiAccessLogRespVO.setUserAgent(f.getUserAgent());
            apiAccessLogRespVO.setUserType(f.getUserType());
            apiAccessLogRespVO.setUserIp(f.getUserIp());
            apiAccessLogRespVOList.add(apiAccessLogRespVO);
        });

        return apiAccessLogRespVOList;
    }

    @PostMapping("/export-excel")
    @ApiOperation("导出API 访问日志 Excel")
    @Log(title = "导出API 访问日志", businessType = BusinessType.EXPORT)
    @SaCheckPermission("system:apiinfor:lndex:export")
    public void exportApiAccessLogExcel(@Valid ApiAccessLogExportReqVO exportReqVO,
                                        HttpServletResponse response) throws IOException {
        List<ApiAccessLogDO> list = apiAccessLogService.getApiAccessLogList(exportReqVO);
        // 导出 Excel
//        List<ApiAccessLogExcelVO> datas = ApiAccessLogConvert.INSTANCE.convertList02(list);
        List<ApiAccessLogExcelVO> apiAccessLogExcelVOS = convertList02(list);

        ExcelUtil.exportExcel(apiAccessLogExcelVOS, "访问日志", ApiAccessLogExcelVO.class, response);
    }

}
