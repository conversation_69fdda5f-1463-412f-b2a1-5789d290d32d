package com.zhonghe.common.annotation;

import com.zhonghe.common.enums.OssDataScopeType;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * @description:
 * @author: cq
 * @date: 2023/4/4 10:16
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface OssDataPermission {

    OssDataScopeType[] has() default {OssDataScopeType.OWNER, OssDataScopeType.READ};
}
