package com.zhonghe.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @description:
 * @author: cq
 * @date: 2023/3/29 11:12
 */
@Getter
@AllArgsConstructor
public enum WebHookType {

    UNKNOWN("0"),
    DINGDING("1"),
    VX("2");

    private final String v;

    public static WebHookType getType(String type) {
        for (WebHookType value : WebHookType.values()) {
            if (type.equals(value.getV())) {
                return value;
            }
        }
        return UNKNOWN;
    }
}
