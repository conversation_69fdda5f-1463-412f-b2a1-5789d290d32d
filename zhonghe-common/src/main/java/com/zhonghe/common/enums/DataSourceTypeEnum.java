package com.zhonghe.common.enums;

public enum DataSourceTypeEnum {
    /**
     * zhonhe_test
     */
    ZHONHE_TEST("zhonhe_test"),
    /**
     * platform
     */
    PLATFORM("platform"),
    /**
     * mydb
     */
    MY_DB("mydb");

    private final String name;

    DataSourceTypeEnum(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

}

