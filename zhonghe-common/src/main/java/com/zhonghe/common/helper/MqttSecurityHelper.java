package com.zhonghe.common.helper;

import com.zhonghe.common.utils.redis.RedisUtils;
import com.zhonghe.common.utils.spring.SpringUtils;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.redisson.client.codec.StringCodec;
import org.springframework.util.DigestUtils;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Random;
import java.util.Set;

/**
 * @description:
 * @author: cq
 * @date: 2023/3/27 14:06
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class MqttSecurityHelper {
    private final static String str = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";

    public static String GetRandomString(int length) {
        Random random = new Random();
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < length; i++) {
            int number = random.nextInt(62);
            sb.append(str.charAt(number));
        }
        return sb.toString();
    }


    /**
     * 按盐值生成前置密码
     *
     * @param password
     * @param salt
     * @return
     * @throws NoSuchAlgorithmException
     */
    public static String encode(String password, String salt) throws NoSuchAlgorithmException {
        MessageDigest messageDigest = MessageDigest.getInstance("SHA-256");
        byte[] saltBytes = salt.getBytes(StandardCharsets.UTF_8);
        byte[] passwordBytes = password.getBytes(StandardCharsets.UTF_8);
        byte[] bytesToHash = new byte[saltBytes.length + passwordBytes.length];
        System.arraycopy(saltBytes, 0, bytesToHash, 0, saltBytes.length);
        System.arraycopy(passwordBytes, 0, bytesToHash, saltBytes.length, passwordBytes.length);
        byte[] hashedBytes = messageDigest.digest(bytesToHash);
        StringBuilder stringBuilder = new StringBuilder();
        for (byte b : hashedBytes) {
            stringBuilder.append(String.format("%02x", b));
        }
        return stringBuilder.toString();
    }

    public static void registerAdminMqttUser(String username, String password, String salt) throws NoSuchAlgorithmException {
        String pwd = MqttSecurityHelper.encode(password, salt);
        RedisUtils.deleteObject("mqtt_user:" + username);
        RedisUtils.deleteObject("mqtt_user_acl:" + username);
        RedissonClient bean = SpringUtils.getBean(RedissonClient.class);
        RMap<Object, Object> v = bean.getMap("mqtt_user:" + username, StringCodec.INSTANCE);
        v.put("is_superuser", 1);
        v.put("salt", salt);
        v.put("password_hash", pwd);
        // 设置权限
        RMap<Object, Object> v_acl = bean.getMap("mqtt_user_acl:" + username, StringCodec.INSTANCE);
        v_acl.put("#", "all");
    }

    /**
     * 注册用户
     *
     * @param user
     * @param password
     * @param salt
     * @throws NoSuchAlgorithmException
     */
    public static String registerNormalUserMqttUser(String user, String password, String salt, Set<String> topics, Long timeout) throws NoSuchAlgorithmException {
        String hashPwd = DigestUtils.md5DigestAsHex((password).getBytes());
        String pwd = MqttSecurityHelper.encode(hashPwd, salt);
        RedissonClient bean = SpringUtils.getBean(RedissonClient.class);
        RMap<Object, Object> v = bean.getMap("mqtt_user:" + user, StringCodec.INSTANCE);
        v.put("is_superuser", 0);
        v.put("salt", salt);
        v.put("password_hash", pwd);
        // 设置监听权限
        RMap<Object, Object> v_acl = bean.getMap("mqtt_user_acl:" + user, StringCodec.INSTANCE);
        for (String topic : topics) {
            v_acl.put(topic, "subscribe");
        }
        RedisUtils.expire("mqtt_user_acl:" + user, timeout);
        RedisUtils.expire("mqtt_user:" + user, timeout);
        return hashPwd;
    }
}

