package com.zhonghe.common.helper;

import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * @description:
 * @author: cq
 * @date: 2023/3/14 15:18
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class SensitiveHelper {


    private static final ThreadLocal<Boolean> extra = new ThreadLocal<>();


    public static void set(boolean b) {
        extra.set(b);
    }

    public static void remove() {
        extra.remove();
    }

    public static boolean skip() {
        return extra.get() != null && extra.get();
    }
}

