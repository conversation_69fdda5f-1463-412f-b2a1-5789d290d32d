package com.zhonghe.common.helper;

import com.zhonghe.common.enums.WebHookType;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;
import org.apache.commons.codec.binary.Base64;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;

/**
 * @description:
 * @author: cq
 * @date: 2023/3/29 11:00
 */

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class WebHookRobotHelper {

    private static String CreateSign(WebHookType type, long timestamp, String secret) throws NoSuchAlgorithmException, UnsupportedEncodingException, InvalidKeyException {
        switch (type) {
            case DINGDING:
                String stringToSign = timestamp + "\n" + secret;
                Mac mac = Mac.getInstance("HmacSHA256");
                mac.init(new SecretKeySpec(secret.getBytes("UTF-8"), "HmacSHA256"));
                byte[] signData = mac.doFinal(stringToSign.getBytes("UTF-8"));
                return URLEncoder.encode(new String(Base64.encodeBase64(signData)), "UTF-8");
            case VX:
                break;
            case UNKNOWN:
                break;
        }
        return null;
    }

    /**
     * 签名，返回地址
     *
     * @param hookUrl
     * @param secret
     * @param type
     * @throws NoSuchAlgorithmException
     * @throws UnsupportedEncodingException
     * @throws InvalidKeyException
     */
    public static String Sign(String hookUrl, String secret, WebHookType type) throws UnsupportedEncodingException, NoSuchAlgorithmException, InvalidKeyException {
        Long timestamp = System.currentTimeMillis();

        StringBuilder sb = new StringBuilder(hookUrl);
        switch (type) {
            case DINGDING:
                sb.append("&timestamp=").append(timestamp).append("&sign=").append(WebHookRobotHelper.CreateSign(type, timestamp, secret));
                break;
            case VX:
            case UNKNOWN:
                break;
        }

        return sb.toString();
    }
}

