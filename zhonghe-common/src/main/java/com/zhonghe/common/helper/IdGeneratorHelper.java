package com.zhonghe.common.helper;

import cn.hutool.core.lang.generator.Generator;
import cn.hutool.core.lang.generator.SnowflakeGenerator;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * @description:
 * @author: cq
 * @date: 2023/4/7 16:03
 */

@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class IdGeneratorHelper {

    private static final Generator<Long> generator = new SnowflakeGenerator();

    public static Long next() {
        return generator.next();
    }

    public static void main(String[] args) {
        System.out.println(next());
    }
}

