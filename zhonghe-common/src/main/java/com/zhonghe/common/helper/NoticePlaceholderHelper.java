package com.zhonghe.common.helper;

import org.springframework.util.PropertyPlaceholderHelper;

import java.util.Map;

/**
 * @description: 处理消息占位符模板功能
 * @author: cq
 * @date: 2023/3/22 9:44
 */

public class NoticePlaceholderHelper {


    private static volatile NoticePlaceholderHelper instance;
    private final PropertyPlaceholderHelper helper;

    private NoticePlaceholderHelper() {
        this.helper = new PropertyPlaceholderHelper("{", "}");
    }

    public static NoticePlaceholderHelper getInstance() {
        if (instance == null) {
            synchronized (NoticePlaceholderHelper.class) {
                if (instance == null) {
                    instance = new NoticePlaceholderHelper();
                }
            }
        }
        return instance;
    }

    public String replacePlaceholders(String text, Map<String, String> params) {
        return helper.replacePlaceholders(text, placeholderName -> {
            Object value = params.get(placeholderName);
            return (value != null) ? value.toString() : null;
        });
    }
}

