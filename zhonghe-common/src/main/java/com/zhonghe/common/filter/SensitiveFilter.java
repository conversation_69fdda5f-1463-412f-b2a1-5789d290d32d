package com.zhonghe.common.filter;

import com.zhonghe.common.helper.SensitiveHelper;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import java.io.IOException;

/**
 * @description: 移除敏感过滤
 * @author: cq
 * @date: 2023/3/14 15:17
 */

public class SensitiveFilter implements Filter {

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        try {
            filterChain.doFilter(servletRequest, servletResponse);
        } finally {
            SensitiveHelper.remove();
        }
    }
}

