package com.zhonghe.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@NoArgsConstructor
@TableName("gis_services_params")
@ApiModel("服务参数表")
public class GisServicesParams implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 参数ID
     */
    @ApiModelProperty(value = "参数ID")
    @TableId(value = "params_id", type = IdType.AUTO)
    private Long paramsId;

    /**
     * 规则ID
     */
    @ApiModelProperty(value = "规则ID")
    private Long ruleId;

    /**
     * 服务ID
     */
    @ApiModelProperty(value = "服务ID")
    private Long serviceId;

    /**
     * 参数属性
     */
    @ApiModelProperty(value = "参数属性")
    private String paramsKey;

    /**
     * 参数属性值
     */
    @ApiModelProperty(value = "参数属性值")
    private String paramsValue;

    /**
     * 服务范围
     */
    @ApiModelProperty(value = "服务范围")
    private String serviceScope;

    /**
     * 是否删除
     */
    @ApiModelProperty(value = "是否删除")
    private Boolean doDelete;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

}
