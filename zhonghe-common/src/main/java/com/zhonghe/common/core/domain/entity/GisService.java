package com.zhonghe.common.core.domain.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 地图服务 gis_service
 *
 * <AUTHOR> Li
 */

@Data
@NoArgsConstructor
@TableName("gis_service")
@ApiModel("地图服务")
public class GisService implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 服务ID
     */
    @ApiModelProperty(value = "服务ID")
    @TableId(value = "service_id", type = IdType.AUTO)
    private Long serviceId;

    /**
     * 服务名
     */
    @ApiModelProperty(value = "服务名")
    private String serviceName;

    /**
     * 图层名
     */
    @ApiModelProperty(value = "图层名")
    private String graphName;

    /**
     * 服务类型
     */
    @ApiModelProperty(value = "服务类型")
    private String serviceType;


    @ApiModelProperty(value = "服务格式")
    private String serviceFormat;

    @ApiModelProperty(value = "接口地址")
    private String interfaceAddress;

    @ApiModelProperty(value = "数据源类型")
    private String dataSourceType;

    @ApiModelProperty(value = "服务范围")
    private String serviceScope;

    @ApiModelProperty(value = "坐标系")
    private String coordinate;

    @ApiModelProperty(value = "缩略图")
    private String thumbnail;

    @ApiModelProperty(value = "公开级别(0=共有,1=私有)")
    private Integer openLevel;

    @ApiModelProperty(value = "状态")
    private Boolean status;

    /**
     * 来源
     */
    private String source;

    /**
     * 网关代理
     */
    @ApiModelProperty(value = "网关代理(0=关闭,1=开启)")
    private Boolean gatewayProxy;

    /**
     * 网关代理地址
     */
    @ApiModelProperty(value = "网关代理地址")
    private String gatewayAddress;

    @ApiModelProperty(value = "时间")
    private Date createTime;

    @ApiModelProperty(value = "扩展属性集合")
    @TableField(exist = false)
    List<GisServiceExtend> serviceExtendList;

    @ApiModelProperty(value = "服务参数集合")
    @TableField(exist = false)
    List<GisServicesParams> servicesParamsList;

    @ApiModelProperty(value = "服务参数范围集合")
    @TableField(exist = false)
    List<String> ParamsScopeList;

    @ApiModelProperty(value = "图层集合")
    @TableField(exist = false)
    List<GraphService> serviceGraphList;

//    @ApiModelProperty(value = "字段集合")
//    @TableField(exist = false)
//    List<GisFields> serviceFieldsList;

}
