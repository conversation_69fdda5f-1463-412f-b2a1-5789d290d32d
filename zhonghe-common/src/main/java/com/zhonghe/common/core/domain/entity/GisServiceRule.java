package com.zhonghe.common.core.domain.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 服务权限表 gis_service
 *
 * <AUTHOR> Li
 */

@Data
@NoArgsConstructor
@TableName("gis_service_rule")
@ApiModel("服务权限表")
public class GisServiceRule implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 权限ID
     */
    @ApiModelProperty(value = "权限ID")
    @TableId(value = "rule_id", type = IdType.AUTO)
    private Long ruleId;

    /**
     * 规则ID
     */
    @ApiModelProperty(value = "规则ID")
    private Long authorityId;


    /**
     * 地图服务ID
     */
    @ApiModelProperty(value = "地图服务ID")
    private Long serviceId;

    /**
     * 加载循序
     */
    @ApiModelProperty(value = "加载循序")
    private Integer loadOrder;

    /**
     * 参数范围
     */
    @ApiModelProperty(value = "参数范围")
    private String paramsScope;

    /**
     * 加载状态
     */
    @ApiModelProperty(value = "加载状态")
    private Boolean loadStatus;


    @ApiModelProperty(value = "服务名")
    @TableField(exist = false)
    private String serviceName;

    @ApiModelProperty(value = "服务类型")
    @TableField(exist = false)
    private String serviceType;

    @ApiModelProperty(value = "数据源")
    @TableField(exist = false)
    private String dataSourceType;

    @ApiModelProperty(value = "查询权限")
    private Boolean isCheck;

    @ApiModelProperty(value = "新增权限")
    private Boolean isSave;

    @ApiModelProperty(value = "删除权限")
    private Boolean isDelete;

    @ApiModelProperty(value = "更新权限")
    private Boolean isUpdate;

    @TableField(exist = false)
    private List<String> ruleList;

}
