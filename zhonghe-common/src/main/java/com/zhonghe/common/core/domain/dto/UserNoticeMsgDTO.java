package com.zhonghe.common.core.domain.dto;

import com.zhonghe.common.constant.ServiceConstants;
import lombok.Getter;
import lombok.Setter;

/**
 * @description:
 * @author: cq
 * @date: 2023/3/27 18:36
 */
public class UserNoticeMsgDTO extends BaseMqttMsgDTO {

    public UserNoticeMsgDTO(String topic) {
        super(topic, ServiceConstants.NOTICE_MQTT_TYPE_ANNOUNCE);
    }

    @Getter
    @Setter
    private int level;

    @Getter
    @Setter
    private Long userNoticeId;

}

