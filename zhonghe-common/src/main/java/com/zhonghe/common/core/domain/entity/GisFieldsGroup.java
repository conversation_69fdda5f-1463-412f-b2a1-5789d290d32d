package com.zhonghe.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@NoArgsConstructor
@TableName("gis_fields_group")
@ApiModel("字段组关系表")
public class GisFieldsGroup implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 字段组ID
     */
    @ApiModelProperty(value = "字段组ID")
    @TableId(value = "fields_group_id", type = IdType.AUTO)
    private Long fieldsGroupId;

    /**
     * 图层ID
     */
    @ApiModelProperty(value = "图层ID")
    private Long graphId;

    /**
     * 字段ID集
     */
    @ApiModelProperty(value = "字段ID集")
    private String fieldsResult;

    @TableField(exist = false)
    private List<GisFields> fieldsList;

    /**
     * 规则类型
     */
    @ApiModelProperty(value = "规则类型")
    private String ruleType;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String groupName;

    /**
     * 标签
     */
    @ApiModelProperty(value = "标签")
    private String groupLabel;

    /**
     * 来源
     */
    @ApiModelProperty(value = "来源(0=自建,1=其他)")
    private Integer groupSource;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private Boolean groupStatus;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述")
    private String describe;

}
