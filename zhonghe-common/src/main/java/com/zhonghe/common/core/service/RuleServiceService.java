package com.zhonghe.common.core.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.domain.entity.RuleService;

import java.util.List;

public interface RuleServiceService {

    IPage<RuleService> selectAll(String serviceName, PageQuery pageQuery);

    RuleService selectById(Long serviceId);

    int updateEntity(RuleService ruleService);

    int saveEntity(RuleService ruleService);

    int deleteEntity(Long serviceId);

    List<Long> selectListById(Long ruleId);
}
