package com.zhonghe.common.core.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.domain.entity.GisService;
import com.zhonghe.common.core.domain.entity.GisServiceExtend;
import com.zhonghe.common.core.domain.entity.GisServiceRule;
import com.zhonghe.common.core.domain.entity.ServiceRelevGraph;
import org.springframework.scheduling.annotation.Async;

import java.util.List;
import java.util.Map;

public interface GisServiceService {

    IPage<GisService> selectAll(String serviceName, String serviceType, PageQuery pageQuery);

    GisService selectById(Long serviceId);

    int updateEntity(GisService gisService);

    Long saveEntity(GisService gisService);

    int deleteEntity(Long serviceId);

    List<String> getCoordinate();

    boolean exist(Long i);

    void saveBathe(List<GisServiceExtend> list);

    List<GisService> selectAllList();

    List<GisServiceExtend> selectExtend(Long serviceId);

    @Async
    void authoService(List<GisServiceRule> gisService);

    void deleteAutho(Long serviceId);

    void deleteBathe(Long serviceId);

    List<GisService> selectByIdList(List<Long> idList);

    List<GisServiceRule> serviceRuleLise(Long ruleId);

    List<GisServiceRule> selectListById(Long ruleId);

    void selectParamsById(List<GisService> gisService, Long ruleId);

    void selectGraphById(List<GisService> gisService, Long ruleId);

    List<ServiceRelevGraph> testGraphList(Long ruleId);

    Map<String, Object> pitchServer(List<Long> list);
}
