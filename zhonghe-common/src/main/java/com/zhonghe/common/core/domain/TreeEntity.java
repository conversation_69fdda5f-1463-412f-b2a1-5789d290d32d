package com.zhonghe.common.core.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;

/**
 * Tree基类
 *
 * <AUTHOR> Li
 */

@Data
@EqualsAndHashCode(callSuper = true)
public class TreeEntity<T> extends BaseEntity {

    private static final long serialVersionUID = 1L;


    @TableField(exist = false)
    private Long id;

    /**
     * 父菜单名称
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "父菜单名称")
    private String parentName;

    /**
     * 父菜单ID
     */
    @ApiModelProperty(value = "父菜单ID")
    private Long parentId;

    /**
     * 子部门
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "子部门")
    private List<T> children = new ArrayList<>();

}
