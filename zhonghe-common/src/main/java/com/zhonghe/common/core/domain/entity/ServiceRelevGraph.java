package com.zhonghe.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 服务图层关系表 service_selev_graph
 *
 * <AUTHOR> Li
 */

@Data
@NoArgsConstructor
@TableName("gis_related_graph")
@ApiModel("服务图层关系表")
public class ServiceRelevGraph implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 规则ID
     */
    @ApiModelProperty(value = "规则ID")
    private Long ruleId;

    /**
     * 服务ID
     */
    @ApiModelProperty(value = "服务ID")
    private Long serviceId;

    /**
     * 图层ID
     */
    @ApiModelProperty(value = "图层ID")
    private Long graphId;

    @ApiModelProperty(value = "权限集合")
    private String authorityList;

}
