package com.zhonghe.common.core.domain.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 信息通知关系表 sys_user
 *
 * <AUTHOR> Li
 */

@Data
@NoArgsConstructor
@TableName("read_the_information")
@ApiModel("信息通知关系表")
public class ReadTheInformation implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 主键ID
     */
    @ApiModelProperty(value = "主键ID")
    @TableId(value = "information_id", type = IdType.AUTO)
    private Long informationId;

    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID")
    private Long userId;

    /**
     * 短信ID
     */
    @ApiModelProperty(value = "短信ID")
    private Long smsId;

    /**
     * 公告ID
     */
    @ApiModelProperty(value = "公告ID")
    private Long notedId;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态 （0短信已读  1短信未读  2公告已读  3公告未读）")
    private String stuts;


    /**
     * 扩展
     */
    @ApiModelProperty(value = "扩展")
    private String extend;


}
