package com.zhonghe.common.core.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.domain.entity.GisFields;
import com.zhonghe.common.core.domain.entity.GisFieldsGroup;
import org.springframework.scheduling.annotation.Async;

import java.util.List;

public interface GisFieldsService {

    IPage<GisFieldsGroup> selectAll(String serviceName, PageQuery pageQuery);

    GisFieldsGroup selectById(Long serviceId);

    int updateEntity(GisFieldsGroup gisFieldsGroup);

    int saveEntity(GisFieldsGroup gisFieldsGroup);

    int deleteEntity(Long serviceId);

    List<GisFields> getFieldsList(List<Long> collect);

    void saveBathEntity(List<GisFields> gisFieldsList);

    IPage<GisFields> selectFieldById(Long graphId, String fieldName, PageQuery pageQuery,Long ruleId);

    @Async
    void saveFields(List<GisFields> gisService,Long ruleId);
}
