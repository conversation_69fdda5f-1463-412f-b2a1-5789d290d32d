package com.zhonghe.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 地图服务扩展属性 gis_service_extend
 *
 * <AUTHOR> Li
 */

@Data
@NoArgsConstructor
@TableName("gis_service_extend")
@ApiModel("地图服务扩展属性")
public class GisServiceExtend implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 扩展属性ID
     */
    @ApiModelProperty(value = "扩展属性ID")
    @TableId(value = "extend_id", type = IdType.AUTO)
    private Long extendId;

    /**
     * 地图服务ID
     */
    @ApiModelProperty(value = "地图服务ID")
    private Long serviceId;

    /**
     * 扩展属性参数
     */
    @ApiModelProperty(value = "扩展属性参数")
    private String extendKey;

    /**
     * 扩展属性值
     */
    @ApiModelProperty(value = "扩展属性值")
    private String extendValue;

}
