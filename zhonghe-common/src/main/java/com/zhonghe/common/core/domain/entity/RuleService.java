package com.zhonghe.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 服务规则表 rule_service
 *
 * <AUTHOR> Li
 */

@Data
@NoArgsConstructor
@TableName("rule_service")
@ApiModel("服务规则表")
public class RuleService implements Serializable {

    /**
     * 规则ID
     */
    @ApiModelProperty(value = "规则ID")
    @TableId(value = "rule_id", type = IdType.AUTO)
    private Long ruleId;

    /**
     * 规则名称
     */
    @ApiModelProperty(value = "规则名称")
    private String ruleName;

    /**
     * 服务权限
     */
    @ApiModelProperty(value = "服务权限")
    private Long serverPower;

    /**
     * 服务参数
     */
    @ApiModelProperty(value = "服务参数")
    private Long serverParams;

    /**
     * 图层权限
     */
    @ApiModelProperty(value = "图层权限")
    private Long graphPower;

    /**
     * 字段权限
     */
    @ApiModelProperty(value = "字段权限")
    private Long fieldPower;

    /**
     * 标签
     */
    @ApiModelProperty(value = "标签")
    private String label;

    /**
     * 来源
     */
    @ApiModelProperty(value = "来源(0=自建,1=其他)")
    private Integer source;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态")
    private Boolean status;

    /**
     * 规则描述
     */
    @ApiModelProperty(value = "规则描述")
    private String describe;

    /**
     * 三维权限
     */
    @ApiModelProperty(value = "三维权限")
    private Long diemsionsPower;

    @ApiModelProperty("权限集合")
    @TableField(exist = false)
    private List<GisServiceRule> gisService;

//    @ApiModelProperty(value = "字段集合")
//    @TableField(exist = false)
//    List<GisFields> serviceFieldsList;

    @ApiModelProperty("服务集合")
    @TableField(exist = false)
    private List<GisService> gisServiceList;

    /**
     * 请求参数
     */
    @ApiModelProperty(value = "请求参数")
    @TableField(exist = false)
    private Map<String, Object> fieldsParams = new HashMap<>();
}
