package com.zhonghe.common.core.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: lpg
 * @Date: 2023/04/11/12:59
 * @Description:
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SysDeptTreeVo {

    private Long id;

    private Long parentId;

    private String label;

    private List<SysDeptTreeVo> children = new ArrayList<>();

    public SysDeptTreeVo(Long deptId, Long parentId, String deptName) {
        this.id = deptId;
        this.parentId = parentId;
        this.label = deptName;
    }
}
