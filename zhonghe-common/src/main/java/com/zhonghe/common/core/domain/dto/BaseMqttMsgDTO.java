package com.zhonghe.common.core.domain.dto;

import com.zhonghe.common.utils.JsonUtils;
import lombok.Getter;

import java.io.Serializable;

/**
 * @description:
 * @author: cq
 * @date: 2023/3/27 18:32
 */

public abstract class BaseMqttMsgDTO implements Serializable {

    public BaseMqttMsgDTO(String topic, int type) {
        this.topic = topic;
        this.type = type;
    }

    @Getter
    private int type;

    @Getter
    private final String topic;// 发送的主题

    public String serialize() {
        return JsonUtils.toJsonString(this);
    }

}

