package com.zhonghe.common.core.domain.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 图层权限 graph_service
 *
 * <AUTHOR> Li
 */

@Data
@NoArgsConstructor
@TableName("gis_graph_service")
@ApiModel("图层权限")
public class GraphService implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 图层ID
     */
    @ApiModelProperty(value = "图层ID")
    @TableId(value = "graph_id", type = IdType.AUTO)
    private Long graphId;

    /**
     * 图层名
     */
    @ApiModelProperty(value = "图层名")
    private String graphName;

    /**
     * 表名
     */
    @ApiModelProperty(value = "表名")
    private String tableName;

    /**
     * 名称
     */
    @ApiModelProperty(value = "名称")
    private String nickName;

    /**
     * 图层类别
     */
    @ApiModelProperty(value = "图层类别(0=图层, 1=图层组)")
    private Integer graphType;

    /**
     * 父级图层
     */
    @ApiModelProperty(value = "父级图层")
    private Long graphParent;

    /**
     * 所属地图
     */
    @ApiModelProperty(value = "所属地图")
    private Long geographical;

    /**
     * 数据集
     */
    @ApiModelProperty(value = "数据集(0=矢量,1=OGC)")
    private Integer dataSet;

    /**
     * 图例样式
     */
    @ApiModelProperty(value = "图例样式")
    private String graphCss;

    @TableField(exist = false)
    private Long serviceId;

    /**
     * 图例
     */
    @ApiModelProperty(value = "图例")
    private String graphUrl;

    /**
     * 高亮图
     */
    @ApiModelProperty(value = "高亮图")
    private String graphHighlight;

    @TableField(exist = false)
    private List<GraphService> children;

    @TableField(exist = false)
    private List<String> ruleList;

    @TableField(exist = false)
    private List <GisFields> gisFieldsList;

}
