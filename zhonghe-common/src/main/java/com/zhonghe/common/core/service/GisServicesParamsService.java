package com.zhonghe.common.core.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.domain.entity.GisService;
import com.zhonghe.common.core.domain.entity.GisServiceRule;
import com.zhonghe.common.core.domain.entity.GisServicesParams;
import com.zhonghe.common.core.domain.entity.RuleService;
import org.springframework.scheduling.annotation.Async;

import java.util.List;

public interface GisServicesParamsService {

    IPage<GisServicesParams> selectAll(String serviceName, PageQuery pageQuery);

    GisServicesParams selectById(Long serviceId);

    int updateEntity(GisServicesParams gisService);

    int saveEntity(GisServicesParams gisService);

    int deleteEntity(Long serviceId);

    void selectByIdList(List<GisService> serviceList);

    List<GisService> selectAll(List<GisService> gisServicesList);

    @Async
    void saveButhEntity(List<GisServicesParams> serviceParams);

    void updateButhParamScope(List<GisServiceRule> gisService1);

    @Async
    void updateButhParam(RuleService gisService);

    void deleteButhEntity(List<GisService> gisServiceList);
}
