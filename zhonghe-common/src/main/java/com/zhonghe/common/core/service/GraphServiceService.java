package com.zhonghe.common.core.service;

import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.domain.entity.GisService;
import com.zhonghe.common.core.domain.entity.GraphService;
import com.zhonghe.common.core.domain.entity.ServiceRelevGraph;
import org.springframework.scheduling.annotation.Async;

import java.util.List;

public interface GraphServiceService {

    List<GraphService> selectAll(String serviceName, PageQuery pageQuery);

    GraphService selectById(Long serviceId);

    int updateEntity(GraphService gisService);

    int saveEntity(GraphService gisService);

    int deleteEntity(Long serviceId);


    void insertBathEntity(List<GraphService> graphServiceList);

    void deleteButhEntity(List<GisService> gisServiceList);

    @Async
    void updateButhGraph(Long ruleId);

    @Async
    void saveButhEntity(List<ServiceRelevGraph> paramsList);

    Boolean syncGraph(GisService gisService);

}
