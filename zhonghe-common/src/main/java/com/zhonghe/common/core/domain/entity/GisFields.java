package com.zhonghe.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 服务字段表 gis_fields
 *
 * <AUTHOR> Li
 */

@Data
@NoArgsConstructor
@TableName("gis_fields")
@ApiModel("服务字段表")
public class GisFields implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 字段ID
     */
    @ApiModelProperty(value = "字段ID")
    @TableId(value = "fields_id", type = IdType.AUTO)
    private Long fieldId;

    /**
     * 字段组ID
     */
    @ApiModelProperty(value = "字段组ID")
    private Long fieldGroupId;

    /**
     * 服务ID
     */
    @ApiModelProperty(value = "服务ID")
    private Long serviceId;

    /**
     * 图层ID
     */
    @ApiModelProperty(value = "图层ID")
    private Long graphId;

    /**
     * 图层名称
     */
    @ApiModelProperty(value = "图层名称")
    private String graphName;

    /**
     * 字段名
     */
    @ApiModelProperty(value = "字段名")
    private String fieldName;

    /**
     * 字段属性
     */
    @ApiModelProperty(value = "字段属性")
    private String fieldType;
}
