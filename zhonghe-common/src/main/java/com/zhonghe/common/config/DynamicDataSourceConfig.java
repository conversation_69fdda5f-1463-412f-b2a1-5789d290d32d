package com.zhonghe.common.config;

//@Configuration
public class DynamicDataSourceConfig {

//    @Bean(name="zhonhe_test")
//    @ConfigurationProperties("spring.datasource.druid.first")
//    public DataSource dataSource1(){
//        return DruidDataSourceBuilder.create().build();
//    }
//
//    @Bean(name ="platform")
//    @ConfigurationProperties("spring.datasource.druid.second")
//    public DataSource dataSource2(){
//        return DruidDataSourceBuilder.create().build();
//    }
//
////    @Bean(name="mydb")
////    @ConfigurationProperties("spring.datasource.druid.third")
////    public DataSource dataSource3(){
////        return DruidDataSourceBuilder.create().build();
////    }
//
//    @Bean(name="dynamicDataSource")
//    @Primary
//    public DynamicDataSource dataSource() {
//        Map<Object, Object> targetDataSources = new HashMap<>(5);
//        targetDataSources.put(DataSourceTypeEnum.ZHONHE_TEST.getName(), dataSource1());
//        targetDataSources.put(DataSourceTypeEnum.PLATFORM.getName(), dataSource2());
////        targetDataSources.put(DataSourceTypeEnum.MY_DB.getName(), dataSource3());
//        return new DynamicDataSource(dataSource1(), targetDataSources);
//    }
}

