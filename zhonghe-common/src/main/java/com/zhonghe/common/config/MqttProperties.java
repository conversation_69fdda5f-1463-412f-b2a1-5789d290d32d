package com.zhonghe.common.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * @description:
 * @author: cq
 * @date: 2023/3/27 13:55
 */

@Data
@Component
@ConfigurationProperties(prefix = "mqtt")
public class MqttProperties {

    @Value("${mqtt.enable}")
    private boolean enable;

    @Value("${mqtt.username}")
    private String username;

    @Value("${mqtt.password}")
    private String password;

    @Value("${mqtt.host}")
    private String host;

    @Value("${mqtt.tcp}")
    private int tcp;
    @Value("${mqtt.port}")
    private Integer port;

    @Value("${mqtt.remote}")
    private String remote;

    @Value("${mqtt.salt}")
    private String salt;

    @Value("${sa-token.timeout}")
    private Long timeout;
}

