package com.zhonghe.common.utils.file;

import cn.hutool.core.io.FileUtil;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import javax.servlet.http.HttpServletResponse;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

/**
 * 文件处理工具类
 *
 * <AUTHOR> Li
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class FileUtils extends FileUtil {

    /**
     * 下载文件名重新编码
     *
     * @param response     响应对象
     * @param realFileName 真实文件名
     */
    public static void setAttachmentResponseHeader(HttpServletResponse response, String realFileName) throws UnsupportedEncodingException {
        String percentEncodedFileName = percentEncode(realFileName);

        StringBuilder contentDispositionValue = new StringBuilder();
        contentDispositionValue.append("attachment; filename=")
            .append(percentEncodedFileName)
            .append(";")
            .append("filename*=")
            .append("utf-8''")
            .append(percentEncodedFileName);

        response.addHeader("Access-Control-Expose-Headers", "Content-Disposition,download-filename");
        response.setHeader("Content-disposition", contentDispositionValue.toString());
        response.setHeader("download-filename", percentEncodedFileName);


    }

    /**
     * 百分号编码工具方法
     *
     * @param s 需要百分号编码的字符串
     * @return 百分号编码后的字符串
     */
    public static String percentEncode(String s) throws UnsupportedEncodingException {
        String encode = URLEncoder.encode(s, StandardCharsets.UTF_8.toString());
        return encode.replaceAll("\\+", "%20");
    }

    //文件转换文件流响应页面
    public static void setAttachmentResponseHeader(HttpServletResponse response, String realFileName, String contentType) throws UnsupportedEncodingException {
        String percentEncodedFileName = percentEncode(realFileName);

        StringBuilder contentDispositionValue = new StringBuilder();
        contentDispositionValue.append("attachment; filename=")
            .append(percentEncodedFileName)
            .append(";")
            .append("filename*=")
            .append("utf-8''")
            .append(percentEncodedFileName);

        response.addHeader("Access-Control-Expose-Headers", "Content-Disposition,download-filename");
        response.setHeader("Content-disposition", contentDispositionValue.toString());
        response.setHeader("download-filename", percentEncodedFileName);
        response.setContentType(contentType);
    }

    /**
     * 响应pdf
     * @param response
     * @param fis
     * @throws IOException
     */
    public static void responsePdf(HttpServletResponse response, InputStream fis) throws IOException {
        response.reset();
        // 设置response的Header
        response.setCharacterEncoding("UTF-8");
        response.setContentType("application/pdf");
        OutputStream out = response.getOutputStream();
        byte[] b = new byte[1024 * 1024];
        while ((fis.read(b))!=-1) {
            out.write(b);
        }
        out.flush();
        out.close();
        fis.close();
    }

    /**
     * 响应pdf
     * @param response
     * @param fis
     * @throws IOException
     */
    public static void downloadPdf(HttpServletResponse response, FileInputStream fis,String fileName) throws IOException {
        response.reset();
        // 设置response的Header
        //预测操作
        response.setCharacterEncoding("UTF-8");
//        response.setContentType("application/pdf");//"application/x-msdownload"
        //下载操作
        response.setContentType("application/x-msdownload");
        response.addHeader("Content-Disposition", "attachment; filename=" + new String(fileName.getBytes(),"UTF-8"));
        //response.addHeader("Content-Length", "" + new File(pdfPath).length());
        OutputStream out = response.getOutputStream();
        byte[] b = new byte[1024 * 1024];
        while ((fis.read(b))!=-1) {
            out.write(b);
        }
        out.flush();
        out.close();
        fis.close();
    }

    /**
     * 根据类型计算byte
     * @param type
     * @param size
     * @return
     */
    public static double convertFileSize(String type, double size) {
        switch (type){
            case "B":
                return size;
            case "KB":
                return size * 1024;
            case "MB":
                return size * 1024 * 1024;
            case "GB":
                return size * 1024 * 1024 * 1024;
            case "TB":
                return size * 1024 * 1024 * 1024 * 1024;
            default:
                return size;
        }
    }

}
