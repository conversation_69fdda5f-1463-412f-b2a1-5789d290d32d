package com.zhonghe.common.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.lang.tree.parser.NodeParser;
import cn.hutool.core.util.ObjectUtil;
import com.zhonghe.common.core.domain.entity.SysDept;
import com.zhonghe.common.core.domain.vo.SysDeptTreeVo;
import com.zhonghe.common.utils.reflect.ReflectUtils;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 扩展 hutool TreeUtil 封装系统树构建
 *
 * <AUTHOR> Li
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class TreeBuildUtils extends TreeUtil {

    /**
     * 根据前端定制差异化字段
     */
    public static final TreeNodeConfig DEFAULT_CONFIG = TreeNodeConfig.DEFAULT_CONFIG.setNameKey("label");

    public static <T, K> List<Tree<K>> build(List<T> list, NodeParser<T, K> nodeParser) {
        if (CollUtil.isEmpty(list)) {
            return null;
        }
        K k = ReflectUtils.invokeGetter(list.get(0), "parentId");
        return TreeUtil.build(list, k, DEFAULT_CONFIG, nodeParser);
    }

    public static <T, K> List<Tree<K>> build(List<T> list, NodeParser<T, K> nodeParser, String propertyName) {
        if (CollUtil.isEmpty(list)) {
            return null;
        }
        K k = ReflectUtils.invokeGetter(list.get(0), propertyName);
        return TreeUtil.build(list, k, DEFAULT_CONFIG, nodeParser);
    }


    // 将数据库中查询出来的list集合传入此方法即可获得排成树形结构的list集合。
    public static List<SysDeptTreeVo> create(List<SysDeptTreeVo> lists) {
        List<SysDeptTreeVo> deptTreeList = lists.stream()
            .filter(item -> item.getParentId() == 0)
            .map(item -> {
                item.setChildren(getChildren(item, lists));
                return item;
            }).collect(Collectors.toList());
        return deptTreeList;
    }

    // 此方法将被递归调用
    private static List<SysDeptTreeVo> getChildren(SysDeptTreeVo dept, List<SysDeptTreeVo> lists) {
        List<SysDeptTreeVo> res = lists.stream()
            .filter(item -> item.getParentId().equals(dept.getId()))
            .map(item -> {
                item.setChildren(getChildren(item, lists));
                return item;
            }).collect(Collectors.toList());
        return res;
    }

    /**
     * 向上查询所有上级
     *
     * @param deptAll      所有集合
     * @param leafNodeDept 叶子节点
     */
    public static Set<Long> getDeptUpList(List<SysDept> deptAll, SysDept leafNodeDept) {
        if (ObjectUtil.isNotEmpty(leafNodeDept)) {
            Set<Long> set = new HashSet<>();
            Long parentId = leafNodeDept.getParentId();
            List<SysDept> parentDepts = deptAll.stream().filter(item -> item.getDeptId().equals(parentId)).collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(parentDepts)) {
                SysDept parentDept = parentDepts.get(0);
                set.add(parentDept.getDeptId());
                Set<Long> deptUpTree = getDeptUpList(deptAll, parentDept);
                if (deptUpTree != null) {
                    set.addAll(deptUpTree);
                }
                return set;
            }
        }
        return new HashSet<>();
    }

    /**
     * 根据父节点获取叶子节点
     *
     * @param list
     * @param parentId
     * @return
     */
  /*  public static List<SysMenu> getLeafNodes(List<SysMenu> list, Long parentId) {
        List<SysMenu> res = new ArrayList<>();
        for (SysMenu menu : list) {
            if (menu.getParentId().equals(parentId)) {
                List<SysMenu> children = getLeafNodes(list, menu.getMenuId());
                if (children.isEmpty()) {
                    res.add(menu);
                } else {
                    res.addAll(children);
                }
            }
        }
        return res;
    }*/

    /**
     * 根据父节点获取叶子节点
     *
     * @param list
     * @param parentId
     * @param <T>
     * @return
     */
    public static <T extends TreeNode> List<T> getLeafNodes(List<T> list, Long parentId) {
        List<T> res = new ArrayList<>();
        for (T t : list) {
            if (t.getParentId().equals(parentId)) {
                List<T> children = getLeafNodes(list, t.getId());
                if (children.isEmpty()) {
                    res.add(t);
                } else {
                    res.addAll(children);
                }
            }
        }
        return res;
    }

    /**
     * 获取所有叶子节点
     *
     * @param list
     * @param <T>
     * @return
     */
    public static <T extends TreeNode> List<T> getLeafNodeEntity(List<T> list) {
        List<T> nodeIds = new ArrayList<>();
        for (T node : list) {
            List<T> leafNodes = getLeafNodes(list, node.getParentId());
            nodeIds.addAll(leafNodes);
        }
        return nodeIds;
    }


}
