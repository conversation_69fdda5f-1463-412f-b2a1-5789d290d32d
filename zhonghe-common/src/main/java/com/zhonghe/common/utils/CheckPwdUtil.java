package com.zhonghe.common.utils;

import cn.dev33.satoken.secure.BCrypt;

/**
 * @Author: lpg
 * @Date: 2024/02/26
 * @Description: 密码校验工具类
 */
public class CheckPwdUtil {
    /**
     * 密码校验正则：密码必须包含字母、数字和特殊字符，且长度是8位以上
     */
    private static final String PWD_REGEX = "^(?=.*[0-9])(?=.*[a-zA-Z])(?=.*[^a-zA-Z0-9]).{8,}$";

    /**
     * 密码校验
     */
    public static boolean checkPwd(String pwd) {
        return pwd.matches(PWD_REGEX);
    }

    public static void main(String[] args) {
        boolean flag = BCrypt.checkpw("admin@123.0", "$2a$10$XMv5ZiwovDhwJWS1JPryrOSUgj2vD5vT6O2nwJDgTnLD8uYputxWO");
        String password = BCrypt.hashpw("$2a$10$XMv5ZiwovDhwJWS1JPryrOSUgj2vD5vT6O2nwJDgTnLD8uYputxWO");
        System.out.println(password);
        System.out.println(flag);
    }

}
