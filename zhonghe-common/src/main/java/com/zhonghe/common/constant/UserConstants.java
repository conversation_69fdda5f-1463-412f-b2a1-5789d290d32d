package com.zhonghe.common.constant;

/**
 * 用户常量信息
 *
 * <AUTHOR>
 */
public interface UserConstants {

    /**
     * 平台内系统用户的唯一标志
     */
    String SYS_USER = "SYS_USER";

    /**
     * 正常状态
     */
    String NORMAL = "0";

    /**
     * 异常状态
     */
    String EXCEPTION = "1";

    String String ="";

    /**
     * 用户正常状态
     */
    String USER_NORMAL = "0";

    /**
     * 用户封禁状态
     */
    String USER_DISABLE = "1";

    /**
     * 角色正常状态
     */
    String ROLE_NORMAL = "0";

    /**
     * 角色封禁状态
     */
    String ROLE_DISABLE = "1";

    String HOME_PAGE = "一级地图";

    String PAGE_HOME = "门户首页";

    String MENU_BUTTON = "F";

    /**
     * 部门正常状态
     */
    String DEPT_NORMAL = "0";

    /**
     * 部门停用状态
     */
    String DEPT_DISABLE = "1";

    /**
     * 字典正常状态
     */
    String DICT_NORMAL = "0";

    /**
     * 是否为系统默认（是）
     */
    String YES = "Y";

    /**
     * 是否菜单外链（是）
     */
    String YES_FRAME = "0";

    /**
     * 是否菜单外链（否）
     */
    String NO_FRAME = "1";

    /**
     * 菜单正常状态
     */
    String MENU_NORMAL = "0";

    /**
     * 菜单停用状态
     */
    String MENU_DISABLE = "1";

    /**
     * 菜单隐藏状态
     */
    String MENU_VISIBLE_HIDDEN = "1";

    /**
     * 菜单类型（目录）
     */
    String TYPE_DIR = "M";

    /**
     * 菜单类型（菜单）
     */
    String TYPE_MENU = "C";

    /**
     * 菜单类型（按钮）
     */
    String TYPE_BUTTON = "F";

    /**
     * Layout组件标识
     */
    String LAYOUT = "Layout";

    /**
     * ParentView组件标识
     */
    String PARENT_VIEW = "ParentView";

    /**
     * InnerLink组件标识
     */
    String INNER_LINK = "InnerLink";

    /**
     * 校验返回结果码
     */
    String UNIQUE = "0";
    String NOT_UNIQUE = "1";

    /**
     * 用户名长度限制
     */
    int USERNAME_MIN_LENGTH = 2;
    int USERNAME_MAX_LENGTH = 20;

    /**
     * 密码长度限制
     */
    int PASSWORD_MIN_LENGTH = 5;
    int PASSWORD_MAX_LENGTH = 20;

    /**
     * 机器人状态常量
     */
    String ROBOT_OFF = "0";
    String ROBOT_NO = "1";

    /**
     * 机器人消息参量
     */
    String ROBOT_MARKDOWN = "markdown";
    String ROBOT_TEXT = "text";

    /**
     * 实体软删除参量
     */
    String DEL_VIEW = "0";
    String DEL_HIDDEN = "2";

    /**
     * 管理员ID
     */
    Long ADMIN_ID = 1L;

    String SYS_POWER_SWITCH = "sys_power_switch";

    /**
     * 用户数据权限范围
     * 1.全部数据
     * 2.自定义部门范围数据
     * 3.自己部门数据
     * 4.自己及以子部门数据
     * 5.自己的数据
     */
    String USER_DATA_SCOPE_ALL = "1";
    String USER_DATA_SCOPE_CUSTOM = "2";
    String USER_DATA_SCOPE_DEPT = "3";
    String USER_DATA_SCOPE_BELOW = "4";
    String USER_DATA_SCOPE_SELF = "5";
}
