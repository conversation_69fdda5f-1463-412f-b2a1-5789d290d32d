package com.zhonghe.common.constant;

/**
 * @description: 业务常量
 * @author: cq
 * @date: 2023/3/23 11:04
 */

public interface ServiceConstants {


    /***
     * sys_notice表sendStatus状态码
     */
    int NOTICE_SEND_STATUS_WAITING = 0;
    int NOTICE_SEND_STATUS_OK = 1;
    int NOTICE_SEND_STATUS_CANCEL = 2;

    String NOTICE_SEND_STATUS_START = "0";
    String NOTICE_SEND_STATUS_STOP = "1";

    /**
     * 发送类型常量
     * 0：正常发送
     * 1：延迟发送
     */
    int NOTICE_SEND_TYPE_NORMAL = 0;
    int NOTICE_SEND_TYPE_DELAY = 1;


    /**
     * 1:消息、2:保留
     */
    String NOTICE_TYPE_ANNOUNCE = "1";

    String NOTICE_TYPE_MESSAGE = "2";

    int NOTICE_MQTT_TYPE_ANNOUNCE = 1;

    /**
     * 公告已读
     */
    int USER_NOTICE_READED = 1;
}
