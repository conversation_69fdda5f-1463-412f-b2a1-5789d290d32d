package com.zhonghe;

import cn.dev33.satoken.SaManager;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.metrics.buffering.BufferingApplicationStartup;
import org.springframework.scheduling.annotation.EnableScheduling;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

/**
 * 启动程序
 *
 * <AUTHOR>
 */
@EnableSwagger2
@SpringBootApplication
@EnableScheduling
public class ZhongHeApplication {

    public static void main(String[] args) {
        System.setProperty("spring.devtools.restart.enabled", "false");
        SpringApplication application = new SpringApplication(ZhongHeApplication.class);
        application.setApplicationStartup(new BufferingApplicationStartup(2048));
        application.run(args);
        System.out.println("(.人.)ﾞ  ZhongHe-Vue-Plus启动成功   ლ(´ڡ`ლ)ﾞ");
        System.out.println(SaManager.getConfig());
    }

}
