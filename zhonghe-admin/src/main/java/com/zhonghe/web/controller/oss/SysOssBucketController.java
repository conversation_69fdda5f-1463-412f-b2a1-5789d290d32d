package com.zhonghe.web.controller.oss;

import cn.hutool.core.util.ObjectUtil;
import com.zhonghe.common.annotation.Log;
import com.zhonghe.common.annotation.OssDataPermission;
import com.zhonghe.common.annotation.RepeatSubmit;
import com.zhonghe.common.core.controller.BaseController;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.enums.BusinessType;
import com.zhonghe.common.enums.OssDataScopeType;
import com.zhonghe.common.exception.ServiceException;
import com.zhonghe.common.utils.redis.RedisUtils;
import com.zhonghe.oss.constant.OssConstant;
import com.zhonghe.system.domain.SysOss;
import com.zhonghe.system.domain.bo.SysOssBucketBo;
import com.zhonghe.system.domain.dto.BucketFileHashDto;
import com.zhonghe.system.domain.vo.SysOssBucketVo;
import com.zhonghe.system.domain.vo.SysOssMenuTreeAndPowerVo;
import com.zhonghe.system.service.ISysOssBucketService;
import com.zhonghe.system.service.ISysOssService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotEmpty;
import java.io.File;
import java.util.Arrays;
import java.util.Map;

/**
 * 对象存储桶控制器
 *
 * <AUTHOR>
 * @date 2023-03-31
 */
@Validated
@Api(value = "对象存储桶控制器", tags = {"对象存储桶管理"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/com/zhonghe/system/bucket")
public class SysOssBucketController extends BaseController {

    private final ISysOssBucketService iSysOssBucketService;

    private final ISysOssService iSysOssService;


    /**
     * 新增对象存储桶
     */
    @ApiOperation("新增对象存储桶")
    //@SaCheckPermission("system:bucket:add")
    @Log(title = "对象存储桶", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @OssDataPermission(has = {OssDataScopeType.OWNER, OssDataScopeType.WRITE})
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SysOssBucketBo bo) {
        return toAjax(iSysOssBucketService.insertByBo(bo) ? 1 : 0);
    }


    /**
     * 获取当前的层级的管理权限
     */
    @ApiOperation("查询对象存储桶权限")
    //@SaCheckPermission("system:bucket:manager:depts")
    @Log(title = "对象存储桶", businessType = BusinessType.OTHER)
    @OssDataPermission(has = {OssDataScopeType.OWNER, OssDataScopeType.MANAGE})
    @GetMapping("manager/{id}")
    public R<SysOssMenuTreeAndPowerVo> listNodePowers(@ApiParam("指定的id") @PathVariable Long id) {
        return R.ok(iSysOssBucketService.queryManagerDeptPowers(id));
    }

    /**
     * 获取云盘信息
     */
    @ApiOperation("获取云盘信息")
    //@SaCheckPermission("system:bucket:manager:depts")
    @Log(title = "获取云盘信息", businessType = BusinessType.OTHER)
    @OssDataPermission(has = {OssDataScopeType.OWNER, OssDataScopeType.MANAGE})
    @GetMapping("bucketInfo/{id}")
    public R<SysOssBucketVo> bucketInfo(@ApiParam("指定的云盘id") @PathVariable Long id) {
        return R.ok(iSysOssBucketService.queryBucketInfo(id));
    }


    /**
     * 获取分享的路径
     */
    @ApiOperation("分享文件")
    //@SaCheckPermission("system:bucket:share")
    @Log(title = "对象存储桶分享文件", businessType = BusinessType.OTHER)
    @RepeatSubmit()
    @OssDataPermission(has = {OssDataScopeType.OWNER, OssDataScopeType.SHARE})
    @GetMapping("share/{id}")
    public R<String> share(@ApiParam("指定的id") @PathVariable Long id) {
        return R.ok(iSysOssBucketService.share(id));
    }

    /**
     * 查询对象存储桶列表,只查询桶的名称，自己和被授权访问的列表
     */
    @ApiOperation("查询对象存储桶列表")
    //@SaCheckPermission("system:bucket:list")
    @GetMapping("/list")
    public R<Map<String, ?>> list() {
        return R.ok(iSysOssBucketService.queryBucketList());
    }

    @ApiOperation("查询对象存储桶内部文件列表")
    //@SaCheckPermission("system:bucket:tree")
    @OssDataPermission(has = {OssDataScopeType.OWNER, OssDataScopeType.READ})
    @GetMapping("/tree/{id}")
    public R<Object> nextTree(@PathVariable Long id, @ApiParam("文件名称")
    @RequestParam(required = false) String nickName, @RequestParam(value = "isAsc", defaultValue = "desc") String isAsc) {
        return R.ok(iSysOssBucketService.queryNextTree(id, nickName, isAsc));
    }

    /**
     * 修改对象存储桶
     */
    @ApiOperation("修改对象存储桶")
    //@SaCheckPermission("system:bucket:edit")
    @OssDataPermission(has = {OssDataScopeType.OWNER, OssDataScopeType.MANAGE})
    @Log(title = "对象存储桶", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@RequestBody SysOssBucketBo bo) {
        return toAjax(iSysOssBucketService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除对象存储桶
     */
    @ApiOperation("删除对象存储桶")
    //@SaCheckPermission("system:bucket:remove")
    @Log(title = "对象存储桶", businessType = BusinessType.DELETE)
    @OssDataPermission(has = {OssDataScopeType.OWNER, OssDataScopeType.WRITE})
    @DeleteMapping("/{ids}")
    public R<Void> remove(@ApiParam("主键串")
                          @NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iSysOssBucketService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }


    /**
     * 删除对象存储桶
     */
    @ApiOperation("编辑对象存储桶权限")
    //@SaCheckPermission("system:bucket:power:update")
    @Log(title = "对象存储桶", businessType = BusinessType.UPDATE)
    @PostMapping("/power")
    @OssDataPermission(has = {OssDataScopeType.OWNER, OssDataScopeType.MANAGE, OssDataScopeType.WRITE})
    public R<Void> power(@RequestBody SysOssBucketBo bo) {
        return toAjax(iSysOssBucketService.updateBucketPower(bo));
    }


    /**
     * 上传OSS对象存储
     */
    @ApiOperation("上传OSS对象存储")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "file", value = "文件", paramType = "query", dataTypeClass = File.class, required = true)
    })
    @OssDataPermission(has = {OssDataScopeType.OWNER, OssDataScopeType.WRITE})
    @Log(title = "OSS对象存储", businessType = BusinessType.INSERT)
    @PostMapping("/upload/{id}")
    public R<?> upload(@PathVariable Long id, @RequestPart("file") MultipartFile file) {
        if (ObjectUtil.isNull(file)) {
            throw new ServiceException("上传文件不能为空");
        }
        SysOss oss = iSysOssService.uploadAndCreateEntity(id, file);
        return R.ok();
    }


    @ApiOperation("断点续传对象")
    @Log(title = "OSS对象存储", businessType = BusinessType.INSERT)
    @PostMapping("/upload/{hash}/{chunk}")
    public R<Void> uploadChunks(@PathVariable String hash, @PathVariable Integer chunk, @RequestPart("file") MultipartFile file) {
        Object dto = RedisUtils.getCacheMapValue(OssConstant.HUGE_FILE_UPLOAD, hash);
        if (ObjectUtil.isEmpty(dto)) {
            throw new ServiceException("文件不存在");
        }
        iSysOssService.uploadChunkFileAndCreateEntity((BucketFileHashDto) dto, chunk, file);
        return R.ok("上传成功");
    }
}
