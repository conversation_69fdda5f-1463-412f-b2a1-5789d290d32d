package com.zhonghe.web.controller.oss;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.zhonghe.common.annotation.Log;
import com.zhonghe.common.annotation.RepeatSubmit;
import com.zhonghe.common.core.controller.BaseController;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import com.zhonghe.common.enums.BusinessType;
import com.zhonghe.common.utils.poi.ExcelUtil;
import com.zhonghe.system.domain.bo.SysOssBucketDeptBo;
import com.zhonghe.system.domain.vo.SysOssBucketDeptVo;
import com.zhonghe.system.service.ISysOssBucketDeptService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 部门存储桶控制器
 *
 * <AUTHOR>
 * @date 2023-03-31
 */
@Validated
@Api(value = "部门存储桶控制器", tags = {"部门存储桶管理"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/部门存储桶")
public class SysOssBucketDeptController extends BaseController {

    private final ISysOssBucketDeptService iSysOssBucketDeptService;

    /**
     * 查询部门存储桶列表
     */
    @ApiOperation("查询部门存储桶列表")
    @SaCheckPermission("system:部门存储桶:list")
    @GetMapping("/list")
    public TableDataInfo<SysOssBucketDeptVo> list(SysOssBucketDeptBo bo, PageQuery pageQuery) {
        return iSysOssBucketDeptService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出部门存储桶列表
     */
    @ApiOperation("导出部门存储桶列表")
    @SaCheckPermission("system:部门存储桶:export")
    @Log(title = "部门存储桶", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SysOssBucketDeptBo bo, HttpServletResponse response) {
        List<SysOssBucketDeptVo> list = iSysOssBucketDeptService.queryList(bo);
        ExcelUtil.exportExcel(list, "部门存储桶", SysOssBucketDeptVo.class, response);
    }

    /**
     * 获取部门存储桶详细信息
     */
    @ApiOperation("获取部门存储桶详细信息")
    @SaCheckPermission("system:部门存储桶:query")
    @GetMapping("/{deptId}")
    public R<SysOssBucketDeptVo> getInfo(@ApiParam("主键")
                                     @NotNull(message = "主键不能为空")
                                     @PathVariable("deptId") Long deptId) {
        return R.ok(iSysOssBucketDeptService.queryById(deptId));
    }

    /**
     * 新增部门存储桶
     */
    @ApiOperation("新增部门存储桶")
    @SaCheckPermission("system:部门存储桶:add")
    @Log(title = "部门存储桶", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SysOssBucketDeptBo bo) {
        return toAjax(iSysOssBucketDeptService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改部门存储桶
     */
    @ApiOperation("修改部门存储桶")
    @SaCheckPermission("system:部门存储桶:edit")
    @Log(title = "部门存储桶", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SysOssBucketDeptBo bo) {
        return toAjax(iSysOssBucketDeptService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除部门存储桶
     */
    @ApiOperation("删除部门存储桶")
    @SaCheckPermission("system:部门存储桶:remove")
    @Log(title = "部门存储桶", businessType = BusinessType.DELETE)
    @DeleteMapping("/{deptIds}")
    public R<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable Long[] deptIds) {
        return toAjax(iSysOssBucketDeptService.deleteWithValidByIds(Arrays.asList(deptIds), true) ? 1 : 0);
    }
}
