package com.zhonghe.web.controller.system;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaMode;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.util.ObjectUtil;
import com.zhonghe.common.annotation.Log;
import com.zhonghe.common.constant.UserConstants;
import com.zhonghe.common.core.controller.BaseController;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.common.core.domain.entity.SysMenu;
import com.zhonghe.common.enums.BusinessType;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.system.domain.bo.SysMenuPermissionBo;
import com.zhonghe.system.service.ISysMenuService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 菜单信息
 *
 * <AUTHOR> Li
 */
@Validated
@Api(value = "菜单信息控制器", tags = {"菜单信息管理"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/com/zhonghe/system/menu")
public class SysMenuController extends BaseController {

    private final ISysMenuService menuService;

    /**
     * 获取当前菜单中所有权限
     * @param menu
     * @return
     */
    @ApiOperation("获取当前菜单中所有权限")
    @SaCheckPermission(value = {"system:menu:index:query", "system:menu:index:list"},mode = SaMode.OR)
    @GetMapping("/permissionList")
    public R<List<SysMenu>> permissionList(SysMenu menu) {
        List<SysMenu> menus = menuService.permissionList(menu);
        return R.ok(menus);
    }

    /**
     * 菜单权限绑定角色
     * @param menu
     * @return
     */
    @ApiOperation("菜单权限绑定角色")
    @SaCheckPermission("system:menu:list")
    @PostMapping("/bingPermissionRole")
    public R<String> bingPermissionRole(@RequestBody(required = false) List<SysMenu> menu ,Long role) {
        Integer i = menuService.bingPermissionRole(menu,role);
        return R.ok(i>0 ? "成功": "失败");
    }

    /**
     * 新增自定义权限
     * @param menu
     * @return
     */
    @ApiOperation("新增自定义权限")
    @SaCheckPermission("system:menu:list")
    @PostMapping("/addPermissionList")
    public R<String> addPermissionList(@RequestBody(required = false) List<SysMenu> menu) {

        if (CollUtil.isEmpty(menu)) {
            return R.fail("数据权限不能为空");
        }

        Integer i = menuService.addPermissionList(menu);
        return R.ok(i > 0 ? "成功" : "失败");
    }

    /**
     * 获取菜单列表
     */
    @ApiOperation("获取菜单列表")
    @SaCheckPermission(value = {"system:menu:index:query", "system:menu:index:list"},mode = SaMode.OR)
    @GetMapping("/list")
    public R<List<SysMenu>> list(SysMenu menu) {
        List<SysMenu> menus = menuService.selectMenuList(menu, getUserId());
        return R.ok(menus);
    }

    /**
     * 根据菜单编号获取详细信息
     */
    @ApiOperation("根据菜单编号获取详细信息")
    @SaCheckPermission(value = {"system:menu:index:query", "system:menu:index:list"},mode = SaMode.OR)
    @GetMapping(value = "/{menuId}")
    public R<SysMenu> getInfo(@ApiParam("菜单ID") @PathVariable Long menuId) {
        return R.ok(menuService.selectMenuById(menuId));
    }

    /**
     * 保存菜单权限
     *
     * @param menuPermissionBo
     * @return
     */
    @ApiOperation("保存菜单页面权限")
    @SaCheckLogin
    @PostMapping("/page-permission")
    public R<Void> childrenList(@RequestBody SysMenuPermissionBo menuPermissionBo) {
        return toAjax(menuService.saveMenuPermission(menuPermissionBo));
    }

    @ApiOperation("查询下级菜单列表及权限标识")
    @SaCheckLogin
    @GetMapping("/children-list/{parentId}/{roleId}")
    public R<Map<String, Object>> childrenList(@ApiParam("上级菜单id") @PathVariable Long parentId, @ApiParam("角色id") @PathVariable Long roleId) {
        return R.ok(menuService.selectMenuByParentId(parentId, roleId));
    }

    @ApiOperation("角色配置设置的菜单的可用的树形菜单")
    @SaCheckLogin
    @GetMapping("/roleMenu-tree/{roleId}")
    public R<List<Tree<Long>>> roleMenuList(@ApiParam("角色id") @PathVariable Long roleId) {
        return R.ok(menuService.selectRoleMenuTreeByRoleId(roleId));
    }

    /**
     * 获取菜单下拉树列表
     */
    @ApiOperation("获取菜单下拉树列表")
    @SaCheckPermission(value = {"system:menu:index:query", "system:menu:index:list"},mode = SaMode.OR)
    @GetMapping("/treeselect")
    public R<List<Tree<Long>>> treeselect(SysMenu menu) {
        List<SysMenu> menus = menuService.selectMenuList(menu, getUserId()).stream().filter(a ->
            UserConstants.MENU_NORMAL.equals(a.getStatus())
                && !a.getMenuType().equals(UserConstants.MENU_BUTTON)
                && !UserConstants.MENU_VISIBLE_HIDDEN.equals(a.getVisible())
        ).collect(Collectors.toList());
        // !a.getMenuType().equals(UserConstants.MENU_BUTTON)
        return R.ok(menuService.buildMenuTreeSelect(menus));
    }

    /**
     * 加载对应角色菜单列表树
     */
    @ApiOperation("加载对应角色菜单列表树")
    @SaCheckLogin
    @GetMapping(value = "/roleMenuTreeselect")
    public R<Map<String, Object>> roleMenuTreeSelect(@ApiParam("角色ID") @RequestParam(value = "roleId", required = false) Long roleId) {
        List<SysMenu> menus = menuService.selectMenuList(getUserId()).stream().filter(a -> !a.getMenuType().equals(UserConstants.MENU_BUTTON)).collect(Collectors.toList());
        Map<String, Object> ajax = new HashMap<>();
        if (ObjectUtil.isNull(roleId)) {
            // 新增父节点勾选
            ajax.put("checkedKeys", new Arrays[0]);
            ajax.put("menus", menuService.buildMenuTreeSelect(menus));
            return R.ok(ajax);
        } else {
            // 修改父节点不勾选
            ajax.put("checkedKeys", menuService.selectMenuListExcludeParentId(roleId));
            ajax.put("menus", menuService.buildMenuTreeSelect(menus));
            return R.ok(ajax);
        }

    }

    /**
     * 新增菜单
     */
    @ApiOperation("新增菜单")
    @SaCheckPermission("system:menu:index:add")
    @Log(title = "菜单管理", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Long> add(@Validated @RequestBody SysMenu menu) {
        if (UserConstants.NOT_UNIQUE.equals(menuService.checkMenuNameUnique(menu))) {
            return R.fail("新增菜单'" + menu.getMenuName() + "'失败，菜单名称已存在");
        } else if (UserConstants.YES_FRAME.equals(menu.getIsFrame()) && !StringUtils.ishttp(menu.getPath())) {
            return R.fail("新增菜单'" + menu.getMenuName() + "'失败，地址必须以http(s)://开头");
        } else if (UserConstants.NOT_UNIQUE.equals(menuService.checkMenuPathUnique(menu))) {
            return R.fail("新增菜单'" + menu.getMenuName() + "'失败，菜单路径已存在");
        } else if (UserConstants.NOT_UNIQUE.equals(menuService.checkMenuIdentifier(menu))) {
            return R.fail("新增菜单'" + menu.getMenuName() + "'失败，菜单标识已存在");
        }
        if(StringUtils.isBlank(menu.getPerms())) {
            menu.setPerms(UUID.randomUUID().toString());
        }
        return R.ok(menuService.insertMenu(menu)) ;
    }

    /**
     * 修改菜单
     */
    @ApiOperation("修改菜单")
    @SaCheckPermission("system:menu:index:update")
    @Log(title = "菜单管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Void> edit(@Validated @RequestBody SysMenu menu) {
        if (UserConstants.NOT_UNIQUE.equals(menuService.checkMenuNameUnique(menu))) {
            return R.fail("修改菜单'" + menu.getMenuName() + "'失败，菜单名称已存在");
        } else if (UserConstants.YES_FRAME.equals(menu.getIsFrame()) && !StringUtils.ishttp(menu.getPath())) {
            return R.fail("修改菜单'" + menu.getMenuName() + "'失败，地址必须以http(s)://开头");
        } else if (menu.getMenuId().equals(menu.getParentId())) {
            return R.fail("修改菜单'" + menu.getMenuName() + "'失败，上级菜单不能选择自己");
        } else if (UserConstants.NOT_UNIQUE.equals(menuService.checkMenuPathUnique(menu))) {
            return R.fail("修改菜单'" + menu.getMenuName() + "'失败，菜单路径已存在");
        } else if (UserConstants.NOT_UNIQUE.equals(menuService.checkMenuIsHome(menu))) {
            return R.fail("修改菜单'" + menu.getMenuName() + "'失败，菜单为首页，无法被禁用");
        }
//        else if (UserConstants.NOT_UNIQUE.equals(menuService.checkMenuIdentifier(menu))) {
//            return R.fail("修改菜单'" + menu.getMenuName() + "'失败，菜单标识已存在");
//        }

        return toAjax(menuService.updateMenu(menu));
    }

    /**
     * 删除菜单
     */
    @ApiOperation("删除菜单")
    @SaCheckPermission("system:menu:index:delete")
    @Log(title = "菜单管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{menuId}")
    public R<Void> remove(@ApiParam("菜单ID") @PathVariable("menuId") Long menuId) {
        if (menuService.hasChildByMenuId(menuId)) {
            return R.fail("存在子菜单,不允许删除");
        }
        if (menuService.checkMenuExistRole(menuId)) {
            return R.fail("菜单已分配,不允许删除");
        }
        return toAjax(menuService.deleteMenuById(menuId));
    }

    /**
     * 设置菜单门户首页
     */
    @ApiOperation("设置菜单门户首页")
    @SaCheckPermission("system:menu:setHome")
    @Log(title = "菜单管理", businessType = BusinessType.UPDATE)
    @PutMapping("/setHome")
    public R<Void> setHome(@ApiParam("菜单首页配置")@RequestBody SysMenu sysMenu) {
        if (!menuService.checkMenuStatus(sysMenu)) {
            return R.fail("菜单已禁用，无法设置为首页");
        }
        //获取当前菜单
        SysMenu afterHomeMenu = menuService.selectMenuById(sysMenu.getMenuId());
        //修改历史菜单
        SysMenu prevHomeMenu = menuService.selectOneMenuByIsHome(sysMenu.getIsHome());
        if (!ObjectUtil.isNull(prevHomeMenu)) {
            prevHomeMenu.setIsHome(1);
            menuService.updateMenu(prevHomeMenu);
        }

        afterHomeMenu.setIsHome(sysMenu.getIsHome());
        return toAjax(menuService.updateMenu(afterHomeMenu));
    }

}
