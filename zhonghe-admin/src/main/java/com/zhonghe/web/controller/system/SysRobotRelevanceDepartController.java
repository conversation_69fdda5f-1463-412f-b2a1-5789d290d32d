package com.zhonghe.web.controller.system;

import java.util.List;
import java.util.Arrays;

import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.system.domain.SysRobotRelevanceDepart;
import com.zhonghe.system.domain.bo.SysRobotRelevanceDepartBo;
import com.zhonghe.system.domain.vo.SysRobotRelevanceDepartVo;
import com.zhonghe.system.service.ISysRobotRelevanceDepartService;
import lombok.RequiredArgsConstructor;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;

import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.zhonghe.common.annotation.RepeatSubmit;
import com.zhonghe.common.annotation.Log;
import com.zhonghe.common.core.controller.BaseController;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import com.zhonghe.common.enums.BusinessType;
import com.zhonghe.common.utils.poi.ExcelUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiOperation;

/**
 * 机器人配置关联部门Controller
 *
 * <AUTHOR>
 * @date 2023-04-04
 */
@Validated
@Api(value = "机器人配置关联部门控制器", tags = {"机器人配置关联部门管理"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/robotRelevanceDepart")
public class SysRobotRelevanceDepartController extends BaseController {

    private final ISysRobotRelevanceDepartService iSysRobotRelevanceDepartService;

    /**
     * 查询机器人配置关联部门列表
     */
    @ApiOperation("查询机器人配置关联部门列表")
    @SaCheckPermission("system:robotRelevanceDepart:list")
    @GetMapping("/list")
    public TableDataInfo<SysRobotRelevanceDepart> list (SysRobotRelevanceDepart robotRelevanceDepart, PageQuery pageQuery){

        return iSysRobotRelevanceDepartService.selectPageRoleList(robotRelevanceDepart, pageQuery);
    }

    /**
     * 导出机器人配置关联部门列表
     */
    @ApiOperation("导出机器人配置关联部门列表")
//    @SaCheckPermission("system:robotRelevanceDepart:export")
    @Log(title = "机器人配置关联部门", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SysRobotRelevanceDepartBo bo, HttpServletResponse response) {
        List<SysRobotRelevanceDepartVo> list = iSysRobotRelevanceDepartService.queryList(bo);
        ExcelUtil.exportExcel(list, "机器人配置关联部门", SysRobotRelevanceDepartVo.class, response);
    }

    /**
     * 获取机器人配置关联部门详细信息
     */
    @ApiOperation("获取机器人配置关联部门详细信息")
    @SaCheckPermission("system:robotRelevanceDepart:query")
    @GetMapping("/{id}")
    public R<SysRobotRelevanceDepartVo> getInfo(@ApiParam("主键")
                                     @NotNull(message = "主键不能为空")
                                     @PathVariable("id") Long id) {
        return R.ok(iSysRobotRelevanceDepartService.queryById(id));
    }

    /**
     * 新增机器人配置关联部门
     */
    @ApiOperation("新增机器人配置关联部门")
    @SaCheckPermission("system:robotRelevanceDepart:add")
    @Log(title = "机器人配置关联部门", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SysRobotRelevanceDepartBo bo) {
        return toAjax(iSysRobotRelevanceDepartService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改机器人配置关联部门
     */
    @ApiOperation("修改机器人配置关联部门")
    @SaCheckPermission("system:robotRelevanceDepart:edit")
    @Log(title = "机器人配置关联部门", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SysRobotRelevanceDepartBo bo) {
        return toAjax(iSysRobotRelevanceDepartService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除机器人配置关联部门
     */
    @ApiOperation("删除机器人配置关联部门")
    @SaCheckPermission("system:robotRelevanceDepart:remove")
    @Log(title = "机器人配置关联部门", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable Long[] ids) {
        return toAjax(iSysRobotRelevanceDepartService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
