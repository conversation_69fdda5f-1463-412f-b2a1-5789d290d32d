package com.zhonghe.web.controller.system;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaMode;
import com.zhonghe.common.annotation.Log;
import com.zhonghe.common.annotation.RepeatSubmit;
import com.zhonghe.common.core.controller.BaseController;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import com.zhonghe.common.core.validate.QueryGroup;
import com.zhonghe.common.enums.BusinessType;
import com.zhonghe.system.domain.bo.SysOssConfigBo;
import com.zhonghe.system.domain.vo.SysOssConfigVo;
import com.zhonghe.system.service.ISysOssConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;

/**
 * 对象存储配置Controller
 *
 * <AUTHOR> Li
 * <AUTHOR>
 * @date 2021-08-13
 */
@Validated
@Api(value = "对象存储配置控制器", tags = {"对象存储配置管理"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/com/zhonghe/system/oss/config")
public class SysOssConfigController extends BaseController {

    private final ISysOssConfigService iSysOssConfigService;

    /**
     * 查询对象存储配置列表
     */
    @ApiOperation("查询对象存储配置列表")
    @SaCheckPermission(value = {"system:file:fileconfig:index:query","system:file:fileconfig:index:list"},mode = SaMode.OR)
    @GetMapping("/list")
    public TableDataInfo<SysOssConfigVo> list(@Validated(QueryGroup.class) SysOssConfigBo bo, PageQuery pageQuery) {

        return iSysOssConfigService.queryPageList(bo, pageQuery);
    }

    /**
     * 获取对象存储配置详细信息
     */
    @ApiOperation("获取对象存储配置详细信息")
    @SaCheckPermission(value = {"system:file:fileconfig:index:query","system:file:fileconfig:index:list"},mode = SaMode.OR)
    @GetMapping("/{ossConfigId}")
    public R<SysOssConfigVo> getInfo(@ApiParam("OSS配置ID")
                                     @NotNull(message = "主键不能为空")
                                     @PathVariable("ossConfigId") Long ossConfigId) {
        return R.ok(iSysOssConfigService.queryById(ossConfigId));
    }

    /**
     * 新增对象存储配置
     */
    @ApiOperation("新增对象存储配置")
    @SaCheckPermission("system:file:fileconfig:index:add")
    @Log(title = "对象存储配置", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SysOssConfigBo bo) {
        return toAjax(iSysOssConfigService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改对象存储配置
     */
    @ApiOperation("修改对象存储配置")
    @SaCheckPermission("system:file:fileconfig:index:update")
    @Log(title = "对象存储配置", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SysOssConfigBo bo) {
        return toAjax(iSysOssConfigService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除对象存储配置
     */
    @ApiOperation("删除对象存储配置")
    @SaCheckPermission("system:file:fileconfig:index:delete")
    @Log(title = "对象存储配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ossConfigIds}")
    public R<Void> remove(@ApiParam("OSS配置ID串")
                          @NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ossConfigIds) {
        return toAjax(iSysOssConfigService.deleteWithValidByIds(Arrays.asList(ossConfigIds), true) ? 1 : 0);
    }

    /**
     * 状态修改
     */
    @ApiOperation("状态修改")
    @SaCheckPermission("system:file:fileconfig:index:status")
    @Log(title = "对象存储状态修改", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public R<Void> changeStatus(@RequestBody SysOssConfigBo bo) {
        if (iSysOssConfigService.updateOssConfigStatus(bo) > 0) {
            return "1".equals(bo.getStatus()) ? R.ok("已关闭！") : R.ok("已启用！");
        }
        return R.fail();
    }
}
