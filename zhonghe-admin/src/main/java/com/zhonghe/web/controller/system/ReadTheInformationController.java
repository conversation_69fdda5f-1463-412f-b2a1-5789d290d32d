package com.zhonghe.web.controller.system;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.common.core.domain.entity.ReadTheInformation;
import com.zhonghe.common.helper.LoginHelper;
import com.zhonghe.system.domain.SysNotice;
import com.zhonghe.system.service.ISysNoticeService;
import com.zhonghe.system.service.ReadTheInformationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * 通知控制器
 *
 * <AUTHOR>
 */
@Validated
@Api(value = "通知控制器", tags = {"通知公告"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/com/zhonghe/system/information")
public class ReadTheInformationController {

    private final ReadTheInformationService readTheInformationService;
    private final ISysNoticeService noticeService;

    @ApiOperation("查询所有公告")
    @GetMapping("/allList")
    public R<IPage<SysNotice>> allList(PageQuery pageQuery) {
        return R.ok(noticeService.selectList(pageQuery));
    }

    @ApiOperation("查询用户所有未读的公告")
    @GetMapping("/list")
    public R<List<SysNotice>> list() {
        List<Long> notices = readTheInformationService.selectByUserIds(LoginHelper.getUserId());
        return R.ok(noticeService.selectLists(notices,false));
    }

    @ApiOperation("查询用户所有已读的公告")
    @GetMapping("/readList")
    public R<List<SysNotice>> readList() {
        List<Long> notices = readTheInformationService.selectByUserIds(LoginHelper.getUserId());
        return R.ok(noticeService.selectLists(notices,true));
    }

    @ApiOperation("读取公告")
    @PostMapping("/readNotice/noticeID")
    public R<Void> readNotice(@RequestBody List<Long> noticeIds) {
        if(noticeIds.isEmpty()) return R.fail("noticeId不能为空");

        List<ReadTheInformation> list = new ArrayList<>();
        for (Long noticeId : noticeIds) {
            if(readTheInformationService.extist(noticeId)){

            }else {
                ReadTheInformation readTheInformation = new ReadTheInformation();
                readTheInformation.setUserId(LoginHelper.getUserId());
                readTheInformation.setNotedId(noticeId);
//                readTheInformationService.seave(readTheInformation);
                list.add(readTheInformation);
            }
        }
        if(!list.isEmpty()) readTheInformationService.saveBath(list);

        return R.ok("公告已阅读");
    }

}
