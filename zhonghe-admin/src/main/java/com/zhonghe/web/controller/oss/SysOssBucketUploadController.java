package com.zhonghe.web.controller.oss;

import com.zhonghe.common.annotation.OssDataPermission;
import com.zhonghe.common.core.controller.BaseController;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.common.enums.OssDataScopeType;
import com.zhonghe.system.domain.bo.SysOssBucketUploadCheckBo;
import com.zhonghe.system.service.ISysOssBucketUploadService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 桶文件上传Controller
 *
 * <AUTHOR>
 * @date 2023-04-07
 */
@Validated
@Api(value = "桶文件上传控制器", tags = {"桶文件上传管理"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/ossBucketUpload")
public class SysOssBucketUploadController extends BaseController {

    private final ISysOssBucketUploadService iSysOssBucketUploadService;


    /**
     * 预检操作
     */
    @ApiOperation("查询桶文件上传列表")
    @OssDataPermission(has = {OssDataScopeType.OWNER, OssDataScopeType.WRITE})
    @PostMapping("/check")
    public R<?> check(@ApiParam("大文件上传预检操作")@RequestBody SysOssBucketUploadCheckBo bo){
        return R.ok(iSysOssBucketUploadService.checkHugeFileExist(bo));
    }

}
