package com.zhonghe.web.controller.system;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.system.domain.SysTopicConfig;
import com.zhonghe.system.service.ISysTopicConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Comparator;

/**
 * @Author: lpg
 * @Date: 2023/03/09/12:50
 * @Description:
 */
@Validated
@Api(value = "主题配置", tags = {"主题配置管理器"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/com/zhonghe/topicConfig")
public class SysTopicConfigController {
    private final ISysTopicConfigService topicConfigService;

    @ApiOperation("获取主题配置信息列表")
    @SaCheckPermission("system:topic:list")
    @GetMapping("/getInfo")
    public SysTopicConfig getInfo() {
        return topicConfigService.getOne(new QueryWrapper<SysTopicConfig>().lambda());
    }

    @ApiOperation("获取主题配置版本最大信息")
    @SaCheckPermission("system:topic:maxVersion")
    @GetMapping("/maxVersion")
    public SysTopicConfig maxVersion() {
        return topicConfigService.list().stream().max(Comparator.comparing(SysTopicConfig::getVersion)).get();
    }

    @ApiOperation("保存主题配置信息")
    @SaCheckPermission("system:topic:maxVersion")
    @PostMapping
    public R<String> add(@RequestBody SysTopicConfig sysTopicConfig) {
        topicConfigService.save(sysTopicConfig);
        return R.ok("保存成功");
    }
}
