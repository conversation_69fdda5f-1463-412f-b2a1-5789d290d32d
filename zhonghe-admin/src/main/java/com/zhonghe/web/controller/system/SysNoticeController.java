package com.zhonghe.web.controller.system;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaMode;
import com.zhonghe.common.annotation.Log;
import com.zhonghe.common.core.controller.BaseController;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.enums.BusinessType;
import com.zhonghe.common.exception.notice.NoticeException;
import com.zhonghe.system.domain.SysNotice;
import com.zhonghe.system.domain.dto.SysNoticeDto;
import com.zhonghe.system.service.ISysNoticeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 公告、信息 管理员操作处理发布中心
 *
 * <AUTHOR> Li
 */
@Validated
@Api(value = "公告信息控制器", tags = {"公告信息管理"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/com/zhonghe/system/notice")
public class SysNoticeController extends BaseController {

    private final ISysNoticeService noticeService;

    /**
     * 获取通知公告列表
     */
    @ApiOperation("获取通知公告列表")
    @SaCheckPermission(value = {"system:notice:index:query","system:notice:index:list"},mode = SaMode.OR)
    @GetMapping("/list")
    public TableDataInfo<SysNotice> list(SysNotice notice, PageQuery pageQuery) {
        return noticeService.selectPageNoticeList(notice, pageQuery);
    }

    /**
     * 根据通知公告编号获取详细信息
     */
    @ApiOperation("根据通知公告编号获取详细信息")
    @SaCheckPermission(value = {"system:notice:index:query","system:notice:index:list"},mode = SaMode.OR)
    @GetMapping(value = "/{noticeId}")
    public R<SysNotice> getInfo(@ApiParam("公告ID") @PathVariable Long noticeId) {
        return R.ok(noticeService.selectNoticeById(noticeId));
    }

    /**
     * 新增通知公告
     */
    @ApiOperation("新增通知公告")
    @SaCheckPermission("system:notice:index:add")
    @Log(title = "通知公告", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Void> add(@Validated @RequestBody SysNoticeDto notice) {
        return toAjax(noticeService.insertNotice(notice));
    }

    /**
     * 修改通知公告
     */
    @ApiOperation("修改通知公告")
    @SaCheckPermission("system:notice:index:update")
//    @Log(title = "通知公告", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Void> edit(@RequestBody SysNoticeDto notice) {
        if (notice.getNoticeId() == null) {
            throw new NoticeException("not.null", new Object[]{"noticeId"});
        }
        return toAjax(noticeService.updateNotice(notice));
    }

    /**
     * 删除通知公告
     */
    @ApiOperation("删除通知公告")
    @SaCheckPermission("system:notice:index:delete")
    @Log(title = "通知公告", businessType = BusinessType.DELETE)
    @DeleteMapping("/{noticeIds}")
    public R<Void> remove(@ApiParam("公告ID串") @PathVariable Long[] noticeIds) {
        return toAjax(noticeService.deleteNoticeByIds(noticeIds));
    }

    @ApiOperation("发送通知公告")
    @SaCheckPermission(value = {"system:notice:index:send","system:notice:index:publish"}, mode = SaMode.OR)
    @Log(title = "通知公告", businessType = BusinessType.UPDATE)
    @PutMapping("publish/{noticeIds}")
    public R<Void> publish(@ApiParam("公告ID串") @PathVariable Long[] noticeIds) {
        if (noticeIds == null || noticeIds.length == 0) {
            throw new NoticeException("not.null", new Object[]{"noticeId"});
        }
        return toAjax(noticeService.publishNotices(noticeIds));
    }

}
