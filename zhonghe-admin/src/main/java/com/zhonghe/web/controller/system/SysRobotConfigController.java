package com.zhonghe.web.controller.system;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaMode;
import com.zhonghe.common.annotation.Log;
import com.zhonghe.common.annotation.RepeatSubmit;
import com.zhonghe.common.core.controller.BaseController;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.enums.BusinessType;
import com.zhonghe.common.utils.poi.ExcelUtil;
import com.zhonghe.system.domain.bo.SysRobotConfigBo;
import com.zhonghe.system.domain.vo.SysRobotConfigVo;
import com.zhonghe.system.service.ISysRobotConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 机器人配置
 *
 * <AUTHOR>
 * @date 2023-03-29
 */
@Validated
@Api(value = "机器人配置控制器", tags = {"机器人配置管理"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/robot-config")
public class SysRobotConfigController extends BaseController {

    private final ISysRobotConfigService iSysRobotConfigService;

    private final ISysRobotConfigService robotConfigService;


    @ApiOperation("查询机器人配置信息")
    @SaCheckPermission(value = {"system:robot-config:query", "system:robot-config:list"}, mode = SaMode.OR)
    @GetMapping("/list")
    public TableDataInfo<SysRobotConfigVo> list(SysRobotConfigBo post, PageQuery pageQuery) {
        return robotConfigService.queryPageList(post, pageQuery);
    }


    /**
     * 导出机器人配置列表
     */
    @ApiOperation("导出机器人配置列表")
    @SaCheckPermission("system:robot-config:export")
    @Log(title = "机器人配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SysRobotConfigBo bo, HttpServletResponse response) {
        List<SysRobotConfigVo> list = iSysRobotConfigService.queryList(bo);
        ExcelUtil.exportExcel(list, "机器人配置", SysRobotConfigVo.class, response);
    }

    /**
     * 获取机器人配置详细信息
     */
    @ApiOperation("获取机器人配置详细信息")
    @SaCheckPermission(value = {"system:robot-config:query", "system:robot-config:list"}, mode = SaMode.OR)
    @GetMapping("/{id}")
    public R<SysRobotConfigVo> getInfo(@ApiParam("主键")
                                       @NotNull(message = "主键不能为空")
                                       @PathVariable("id") Long id) {
        return R.ok(iSysRobotConfigService.queryById(id));
    }

    /**
     * 新增机器人配置
     */
    @ApiOperation("新增机器人配置")
    @SaCheckPermission("system:robot-config:add")
    @Log(title = "机器人配置", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated @RequestBody SysRobotConfigBo bo) {
        return toAjax(iSysRobotConfigService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改机器人配置
     */
    @ApiOperation("修改机器人配置")
    @SaCheckPermission("system:robot-config:edit")
    @Log(title = "机器人配置", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated @RequestBody SysRobotConfigBo bo) {
        return toAjax(iSysRobotConfigService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除机器人配置
     */
    @ApiOperation("删除机器人配置")
    @SaCheckPermission("system:robot-config:remove")
    @Log(title = "机器人配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@ApiParam("主键串")
                          @NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iSysRobotConfigService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
