package com.zhonghe.web.controller.system;

import cn.dev33.satoken.exception.NotLoginException;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zhonghe.common.annotation.Anonymous;
import com.zhonghe.common.constant.Constants;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.common.core.domain.entity.SysMenu;
import com.zhonghe.common.core.domain.entity.SysUser;
import com.zhonghe.common.core.domain.model.LoginBody;
import com.zhonghe.common.core.domain.model.SmsLoginBody;
import com.zhonghe.common.helper.LoginHelper;
import com.zhonghe.common.utils.CheckPwdUtil;
import com.zhonghe.common.utils.redis.RedisUtils;
import com.zhonghe.framework.satoken.service.SaPermissionImpl;
import com.zhonghe.system.domain.SysTopicConfig;
import com.zhonghe.system.domain.vo.RouterVo;
import com.zhonghe.system.service.ISysMenuService;
import com.zhonghe.system.service.ISysTopicConfigService;
import com.zhonghe.system.service.ISysUserService;
import com.zhonghe.system.service.SysLoginService;
import com.zhonghe.system.service.SysPermissionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotBlank;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

/**
 * 登录验证
 *
 * <AUTHOR> Li
 */
//@Validated
@Api(value = "登录验证控制器", tags = {"登录验证管理"})
@RequiredArgsConstructor
@RestController
public class SysLoginController {

    private final SysLoginService loginService;
    private final ISysMenuService menuService;
    private final ISysUserService userService;
    private final SysPermissionService permissionService;

    private final SaPermissionImpl saPermission;

    private final ISysTopicConfigService sysTopicConfigService;

    private final ISysUserService sysUserService;

    /**
     * 登录方法
     *
     * @param loginBody 登录信息
     * @return 结果
     */
    @Anonymous
    @ApiOperation("登录方法")
    @PostMapping("/login")
    public R<Map<String, Object>> login(@Validated @RequestBody LoginBody loginBody) {
        Map<String, Object> ajax = new HashMap<>();
        // 生成令牌
        String token = loginService.login(loginBody.getUsername(), loginBody.getPassword(), loginBody.getCode(),
            loginBody.getUuid());
        ajax.put(Constants.TOKEN, token);
        ajax.put("checkPwd", CheckPwdUtil.checkPwd(loginBody.getPassword()));
//        System.exit(0);
        return R.ok(ajax);
    }

    @Anonymous
    @ApiOperation("H5登录方法")
    @PostMapping("/h5Login")
    public R<Map<String, Object>> h5Login(@Validated @RequestBody LoginBody loginBody) {
        Map<String, Object> ajax = new HashMap<>();
        // 生成令牌
        String token = loginService.h5Login(loginBody.getUsername(), loginBody.getPassword(), loginBody.getCode(),
            loginBody.getUuid());
        ajax.put(Constants.TOKEN, token);
        ajax.put("checkPwd", CheckPwdUtil.checkPwd(loginBody.getPassword()));
//        System.exit(0);
        return R.ok(ajax);
    }

    /**
     * 短信登录(示例)
     *
     * @param smsLoginBody 登录信息
     * @return 结果
     */
    @Anonymous
    @ApiOperation("短信登录(示例)")
    @PostMapping("/smsLogin")
    public R<Map<String, Object>> smsLogin(@Validated @RequestBody SmsLoginBody smsLoginBody) {
        Map<String, Object> ajax = new HashMap<>();
        // 生成令牌
        String token = loginService.smsLogin(smsLoginBody.getPhonenumber(), smsLoginBody.getSmsCode());
        ajax.put(Constants.TOKEN, token);
        return R.ok(ajax);
    }

    /**
     * 小程序登录(示例)
     *
     * @param xcxCode 小程序code
     * @return 结果
     */
    @Anonymous
    @ApiOperation("小程序登录(示例)")
    @PostMapping("/xcxLogin")
    public R<Map<String, Object>> xcxLogin(@NotBlank(message = "{xcx.code.not.blank}") String xcxCode) {
        Map<String, Object> ajax = new HashMap<>();
        // 生成令牌
        String token = loginService.xcxLogin(xcxCode);
        ajax.put(Constants.TOKEN, token);
        return R.ok(ajax);
    }

    @Anonymous
    @ApiOperation("登出方法")
    @PostMapping("/logout")
    public R<Void> logout() {
        try {
            String username = LoginHelper.getUsername();
            StpUtil.logout();
            loginService.logout(username);
        } catch (NotLoginException e) {
        }
        return R.ok("退出成功");
    }

    @Anonymous
    @PostMapping("/getUserIdByToken")
    public R<SysUser> logogetUserIdByToken(String token) {
        //根据token 获取登入用户ID
        String loginIdObj = (String) StpUtil.getLoginIdByToken(token);
        String userId = loginIdObj.substring(loginIdObj.lastIndexOf(":") + 1);
        SysUser sysUser = sysUserService.selectUserById(Long.parseLong(userId));
        return R.ok("获取成功", sysUser);
    }


    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @ApiOperation("获取用户信息")
    @GetMapping("getInfo")
    public R<Map<String, Object>> getInfo() {
        SysUser user = userService.selectUserById(LoginHelper.getUserId());
        if (ObjectUtil.isNull(user)) {
            return R.fail(401, "用户未登陆");
        }
        // 角色集合
        Set<String> roles = permissionService.getRolePermission(user);
        //List<String> roleList = saPermission.getRoleList(null,null);

        // 权限集合
        Set<String> permissions = permissionService.getMenuPermission(user);
        Map<String, Object> ajax = new HashMap<>();
        ajax.put("user", user);
        ajax.put("roles", roles);
        ajax.put("permissions", permissions);

        // 是否权限控制被关闭
        ajax.put("powerSwitch", permissionService.getPowerSwitchStatus());

        //微应用集合
        Map<String, Object> appMenusPermission = permissionService.getAppMenusPermission(user);
        ajax.put("apps", appMenusPermission);

        //主题配置信息
        SysTopicConfig sysTopicConfig = sysTopicConfigService.getOne(new QueryWrapper<SysTopicConfig>().lambda());
        ajax.put("topic", sysTopicConfig);

        //首页信息
        SysMenu sysMenu = menuService.selectOneMenuByIsHome(0);
        Map<String, Object> home = new HashMap<>();
        if (sysMenu != null) {
            home.put("title", sysMenu.getMenuName());
            home.put("name", sysMenu.getPageName());
            home.put("path", sysMenu.getPath());
        }
        ajax.put("home", home);

        RedisUtils.setCacheObject("ajax", ajax);
        return R.ok(ajax);
    }

    /**
     * 获取主题配置信息
     *
     * @return 主题配置信息
     */
    @Anonymous
    @ApiOperation("获取主题配置信息")
    @GetMapping("getTopicInfo")
    public R<SysTopicConfig> getTopicInfo() {
        return R.ok(sysTopicConfigService.getOne(new QueryWrapper<SysTopicConfig>().lambda()));
    }

    /**
     * 获取路由信息
     *
     * @return 路由信息
     */
    @ApiOperation("获取路由信息")
    @GetMapping("getRouters")
    public R<List<RouterVo>> getRouters() {
        Long userId = LoginHelper.getUserId();
        List<SysMenu> menus = menuService.selectMenuTreeByUserId(userId);
        return R.ok(menuService.buildMenus(menus));
    }

    /**
     * 系统参数操作
     *
     * @return
     */
    @Anonymous
    @GetMapping("getSystemPropertie")
    public R<String> getSystemPropertie() {

        Properties properties = System.getProperties();
        properties.list(System.out);
        Map<String, String> getenv = System.getenv();
        System.out.println(getenv.size());
//        System.exit(0);退出程序
        return R.ok("");
    }
}
