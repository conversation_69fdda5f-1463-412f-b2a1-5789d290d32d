package com.zhonghe.web.controller.system;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaMode;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhonghe.common.annotation.Log;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.common.enums.BusinessType;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.common.utils.poi.ExcelUtil;
import com.zhonghe.common.utils.spring.SpringUtils;
import com.zhonghe.sms.config.properties.SmsProperties;
import com.zhonghe.sms.core.SmsTemplate;
import com.zhonghe.sms.entity.SmsChannelDO;
import com.zhonghe.sms.entity.SmsLogDO;
import com.zhonghe.sms.entity.SmsRequestEbtity;
import com.zhonghe.system.mapper.SmsChannelDOMapper;
import com.zhonghe.web.controller.common.SmsLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;


@Api(tags = "管理后台 - 短信日志")
@RestController
@RequestMapping("/com/zhonghe/dev-api/system/sms-log")
@Validated
public class SmsLogController {

    @Resource
    private SmsLogService smsLogService;

    @Resource
    private SmsProperties smsProperties;

    @Resource
    private SmsChannelDOMapper smsChannelDOMapper;

//    @Resource
//    private AliyunSmsTemplate smsTemplate;

    @GetMapping("/page")
    @SaCheckPermission(value = {"system:sms:smslog:index:query","system:sms:smslog:index:list"},mode = SaMode.OR)
    @Log(title = "获得短信日志分页", businessType = BusinessType.OTHER)
    @ApiOperation("获得短信日志分页")
    public R<Page<SmsLogDO>> getSmsLogPage(@Valid SmsLogDO pageVO, PageQuery pageQuery) {
        Page<SmsLogDO> pageResult = smsLogService.getSmsLogPage(pageVO,pageQuery);
        return R.ok(pageResult);
    }

    @PostMapping("/export-excel")
    @ApiOperation("导出短信日志 Excel")
    @SaCheckPermission("system:sms:smslog:index:export")
    @Log(title = "导出短信日志", businessType = BusinessType.EXPORT)
    public void exportSmsLogExcel(@Valid SmsLogDO exportReqVO,
                                  HttpServletResponse response) throws IOException {
        List<SmsLogDO> list = smsLogService.getSmsLogList(exportReqVO);
        // 导出 Excel
        ExcelUtil.exportExcel(list, "短信日志", SmsLogDO.class, response);
    }


    @ApiOperation("发送短信")
    @GetMapping("/sendSms")
    @SaCheckPermission("system:sms:smslog:index:send")
    public R<Object> sendAliyun(@ApiParam("电话号") String phones,
                                @ApiParam("模板ID") String templateId) {
        if (!smsProperties.getEnabled()) {
            return R.fail("当前系统没有开启短信功能！");
        }
        if (!SpringUtils.containsBean("aliyunSmsTemplate")) {
            return R.fail("阿里云依赖未引入！");
        }
        SmsTemplate smsTemplate = SpringUtils.getBean(SmsTemplate.class);
        Map<String, String> map = new HashMap<>(1);
        map.put("code", "1234");
        Object send = smsTemplate.send(phones, templateId, map);  //普通发送
//        SmsResult send = this.smsTemplate.send(phones, templateId, map); // 阿里云模板发送


        //插入发送日志
        SmsRequestEbtity smsRequestEbtity = new SmsRequestEbtity();
        smsRequestEbtity.setOutId(UUID.randomUUID().toString().replace("-", ""));
        smsRequestEbtity.setSmsType("通知");
        smsRequestEbtity.setTemplateCode("6be9f198-a787");
        smsRequestEbtity.setResourceOwnerId(2345678L);
        smsRequestEbtity.setTestChannel("测试渠道");
        smsLogService.createSmsLog(phones,new Date().getTime(),1,true,smsRequestEbtity,smsRequestEbtity.getOutId(),map);
        return R.ok(send);
    }

    @ApiOperation("新建短信渠道")
    @PostMapping()
    public R<Integer> insertChannel(@RequestBody SmsChannelDO smsChannelDO) {
        return R.ok(smsChannelDOMapper.insert(smsChannelDO));
    }

    @ApiOperation("查询所有短信渠道")
    @Log(title = "查询所有短信渠道", businessType = BusinessType.OTHER)
    @GetMapping("/sendSmsChannel")
    @SaCheckPermission(value = {"system:sms:smslog:index:query","system:sms:smslog:index:list"},mode = SaMode.OR)
    public R<Object> sendSmsChannel(@RequestBody SmsChannelDO smsChannelDO, PageQuery pageQuery) {
        LambdaQueryWrapper<SmsChannelDO> lqw = buildQueryWrapper(smsChannelDO);
        IPage<SmsChannelDO> smsChannelDOIPage = smsChannelDOMapper.selectVoPage(pageQuery.build(), lqw);
        return R.ok(smsChannelDOIPage);
    }

    @ApiOperation("查询短信渠道")
    @GetMapping()
    @SaCheckPermission(value = {"system:sms:smslog:index:query","system:sms:smslog:index:list"},mode = SaMode.OR)
    public R<SmsChannelDO> sendSmsChannel(Long id) {
        return R.ok(smsChannelDOMapper.selectVoById(id));
    }

    @ApiOperation("修改短信渠道")
    @PutMapping()
    @SaCheckPermission("system:sms:smslog:index:update")
    public R<Integer> putChannel(@RequestBody SmsChannelDO smsChannelDO) {
        return R.ok(smsChannelDOMapper.update(smsChannelDO,new QueryWrapper<>()));
    }

    @ApiOperation("删除短信渠道")
    @SaCheckPermission("system:sms:smslog:index:delete")
    @DeleteMapping()
    public R<Integer> deleteChannel(Long id) {
        return R.ok(smsChannelDOMapper.deleteById(id));
    }

    private LambdaQueryWrapper<SmsChannelDO> buildQueryWrapper(SmsChannelDO bo) {
        LambdaQueryWrapper<SmsChannelDO> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getSignature()), SmsChannelDO::getSignature, bo.getSignature());
        lqw.eq(bo.getStatus()!=null, SmsChannelDO::getStatus, bo.getStatus());
        lqw.ge(bo.getStartTime()!=null, SmsChannelDO::getStartTime, bo.getStartTime());
        lqw.le(bo.getEndTime()!=null, SmsChannelDO::getEndTime, bo.getEndTime());
        return lqw;
    }

}
