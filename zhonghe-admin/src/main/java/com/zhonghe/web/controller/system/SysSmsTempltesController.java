package com.zhonghe.web.controller.system;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaMode;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhonghe.common.annotation.Anonymous;
import com.zhonghe.common.annotation.Log;
import com.zhonghe.common.annotation.RepeatSubmit;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.common.enums.BusinessType;
import com.zhonghe.common.utils.poi.ExcelUtil;
import com.zhonghe.sms.entity.SmsRequestEbtity;
import com.zhonghe.system.service.SmsTemplatesService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

@Validated
@Api(value = "短信模板", tags = {"短信模板管理器"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/com/zhonghe/SysSmsTempltes")
public class SysSmsTempltesController {

    private final SmsTemplatesService smsTemplatesService;

    @ApiOperation("获取所有模板")
    @GetMapping("/getAll")
    @SaCheckPermission(value = {"system:sms:smstemplate:lndex:query","system:sms:smstemplate:lndex:list"},mode = SaMode.OR)
    public R<Page> getAll(SmsRequestEbtity smsRequestEbtity, PageQuery pageQuery){
        return R.ok(smsTemplatesService.getAll(smsRequestEbtity,pageQuery));
    }

    @ApiOperation("获取单个模板")
    @GetMapping("/getById/{id}")
    @SaCheckPermission(value = {"system:sms:smstemplate:lndex:query","system:sms:smstemplate:lndex:list"},mode = SaMode.OR)
    public R<SmsRequestEbtity> getById(@PathVariable("id") String id){
        return R.ok(smsTemplatesService.getById(id));
    }

    @ApiOperation("修改模板")
    @PostMapping("/UpdateSmsRequestEbtity")
    @SaCheckPermission("system:sms:smstemplate:lndex:update")
    @RepeatSubmit//不能重复提交注解默认5秒
    public R<Integer> UpdateSmsRequestEbtity(@RequestBody SmsRequestEbtity smsRequestEbtity){
        return R.ok(smsTemplatesService.UpdateSmsRequestEbtity(smsRequestEbtity));
    }

    @Anonymous
    @ApiOperation("保存模板")
    @PostMapping("/SaveSms")
    @SaCheckPermission("system:sms:smstemplate:lndex:update")
    @RepeatSubmit
    public R<Integer> SaveSms(@RequestBody SmsRequestEbtity smsRequestEbtity, HttpServletRequest request){
/*        if(smsRequestEbtity.getOutId()==null){
            smsRequestEbtity.setOutId(UUID.randomUUID().toString().replace("-", ""));
        }*/
        return R.ok(smsTemplatesService.SaveSms(smsRequestEbtity));
    }

    @ApiOperation("删除模板")
    @SaCheckPermission("system:sms:smstemplate:lndex:delete")
    @GetMapping("/DeleteSms/{id}")
    public R<Integer> DeleteSms(@PathVariable("id") String id){
        return R.ok(smsTemplatesService.DeleteSms(id));
    }


    @ApiOperation("导出短信模板")
    @SaCheckPermission("system:sms:smstemplate:lndex:export")
    @Log(title = "短信模板", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SmsRequestEbtity smsRequestEbtity, HttpServletResponse response) {
        List<SmsRequestEbtity> smsList = smsTemplatesService.getSmsList(smsRequestEbtity);

        ExcelUtil.exportExcel(smsList, "短信模板", SmsRequestEbtity.class, response);
    }
}
