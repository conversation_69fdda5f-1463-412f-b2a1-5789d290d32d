package com.zhonghe.web.controller.system;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaMode;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zhonghe.common.annotation.Log;
import com.zhonghe.common.constant.UserConstants;
import com.zhonghe.common.core.controller.BaseController;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.common.core.domain.entity.SysMenu;
import com.zhonghe.common.core.domain.entity.SysRole;
import com.zhonghe.common.core.domain.entity.SysUser;
import com.zhonghe.common.core.domain.vo.SysRoleVo;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.enums.BusinessType;
import com.zhonghe.common.utils.poi.ExcelUtil;
import com.zhonghe.system.domain.SysUserRole;
import com.zhonghe.system.event.SysRolePermissionUpdateEvent;
import com.zhonghe.system.mapper.SysMenuMapper;
import com.zhonghe.system.service.ISysRoleService;
import com.zhonghe.system.service.ISysUserService;
import com.zhonghe.system.service.SysPermissionService;
import lombok.extern.slf4j.Slf4j;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * 角色信息
 *
 * <AUTHOR> Li
 */
@Validated
@Api(value = "角色信息控制器", tags = {"角色信息管理"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/com/zhonghe/system/role")
@Slf4j
public class SysRoleController extends BaseController {

    private final ISysRoleService roleService;
    private final ISysUserService userService;
    private final SysPermissionService permissionService;

    private final ApplicationEventPublisher applicationEventPublisher;

    @ApiOperation("查询角色信息列表")
    @SaCheckPermission(value = {"system:role:index:query","system:role:index:list"},mode = SaMode.OR)
    @GetMapping("/list")
    public TableDataInfo<SysRole> list(SysRole role, PageQuery pageQuery) {
        return roleService.selectPageRoleList(role, pageQuery);
    }


    @ApiOperation("导出角色信息列表")
    @Log(title = "角色管理", businessType = BusinessType.EXPORT)
    @SaCheckPermission("system:role:index:export")
    @PostMapping("/export")
    public void export(SysRole role, HttpServletResponse response) {
        List<SysRole> list = roleService.selectRoleList(role);

        ExcelUtil.exportExcel(list, "角色数据", SysRole.class, response);
    }

    /**
     * 根据角色编号获取详细信息
     */
    @ApiOperation("根据角色编号获取详细信息")
    @SaCheckPermission(value = {"system:role:index:query","system:role:index:list"},mode = SaMode.OR)
    @GetMapping(value = "/{roleId}")
    public R<SysRole> getInfo(@ApiParam("角色ID") @PathVariable Long roleId) {
        roleService.checkRoleDataScope(roleId);
        return R.ok(roleService.selectRoleById(roleId));
    }

    /**
     * 新增角色
     */
    @ApiOperation("新增角色")
    @SaCheckPermission("system:role:index:add")
    @Log(title = "角色管理", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Void> add(@Validated @RequestBody SysRole role) {
        if (UserConstants.NOT_UNIQUE.equals(roleService.checkRoleNameUnique(role))) {
            return R.fail("新增角色'" + role.getRoleName() + "'失败，角色名称已存在");
        } else if (UserConstants.NOT_UNIQUE.equals(roleService.checkRoleKeyUnique(role))) {
            return R.fail("新增角色'" + role.getRoleName() + "'失败，角色权限已存在");
        }
        // 这个角色权限一定需要初始化的值，否则会报错
        if (ObjectUtil.isEmpty(role.getDataScope())) {
            role.setDataScope(UserConstants.USER_DATA_SCOPE_SELF);
        }

        Long[] menuIds = role.getMenuIds();
        Set<Long> set = new HashSet<>();
        if (menuIds != null && menuIds.length > 0) {
            List<SysMenu> sysMenus = baseMapper.selectVoList(new LambdaQueryWrapper<SysMenu>()
                .in(SysMenu::getMenuId, role.getMenuIds()));
            sysMenus.forEach(m -> {
                set.add(m.getParentId());
            });
            Collections.addAll(set, menuIds);
        }
        role.setMenuIds(set.toArray(new Long[0]));

        return toAjax(roleService.insertRole(role));

    }

    private final SysMenuMapper baseMapper;

    /**
     * 修改保存角色
     */
    @ApiOperation("修改保存角色")
    @Transactional(rollbackFor = Exception.class)
    @SaCheckPermission("system:role:index:update")
    @Log(title = "角色管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Void> edit(@Validated @RequestBody SysRoleVo rolev) {
        System.out.println("进入修改保存角色");
        log.info("修改保存角色,{}", rolev);
        if (rolev.getRoleId() == null) {
            return R.fail("修改角色失败，参数错误缺少角色id信息");
        }
        SysRole role = BeanUtil.toBean(rolev, SysRole.class);
        log.info("转换后的角色对象: {}", role);
        roleService.checkRoleAllowed(role);
        roleService.checkRoleDataScope(role.getRoleId());
        if (ObjectUtil.isNotEmpty(role.getRoleName()) && UserConstants.NOT_UNIQUE.equals(roleService.checkRoleNameUnique(role))) {
            return R.fail("修改角色'" + role.getRoleName() + "'失败，角色名称已存在");
        } else if (ObjectUtil.isNotNull(role.getRoleKey()) && UserConstants.NOT_UNIQUE.equals(roleService.checkRoleKeyUnique(role))) {
            return R.fail("修改角色'" + role.getRoleName() + "'失败，角色权限已存在");
        }else if(UserConstants.NOT_UNIQUE.equals(role.getStatus()) && roleService.checkStatusReuse(role)){
            return R.fail("修改角色'" + role.getRoleName() + "'失败，角色无法停用,已有用户绑定");
        }

        boolean updatePower = false;
        // 部门控制,修改菜单与修改角色不可以同时进行
        if (role.getDataScope() != null) {
            roleService.updateRoleDeptRelation(role.getRoleId(), role.getDataScope(), role.getDeptIds());
            updatePower = true;
        } else {
            //将父节点菜单加入
            Long[] menuIds = role.getMenuIds();
            Set<Long> set = new HashSet<>();
            if (menuIds != null && menuIds.length > 0) {
                log.info("开始处理菜单关系, 原始菜单IDs: {}", Arrays.toString(menuIds));
                List<SysMenu> sysMenus = baseMapper.selectVoList(new LambdaQueryWrapper<SysMenu>()
                    .in(SysMenu::getMenuId, role.getMenuIds()));
                sysMenus.forEach(m -> {
                    set.add(m.getParentId());
                });
                Collections.addAll(set, menuIds);
            }
            /*set.add(2000L);
            set.add(0L);*/
            role.setMenuIds(set.toArray(new Long[0]));
            log.info("更新角色信息, roleId: {}, menuIds: {}", role.getRoleId(), Arrays.toString(role.getMenuIds()));
            if (roleService.updateRole(role) > 0) {
                updatePower = true;
                log.info("角色信息更新成功");
            }
        }
        if (updatePower) {
            // 查询这个角色涉及的范围
            applicationEventPublisher.publishEvent(new SysRolePermissionUpdateEvent(this, role.getRoleId()));
            return R.ok();
        }
        return R.fail("修改角色'" + role.getRoleName() + "'失败，请联系管理员");
    }

    /**
     * 修改保存数据权限
     */
    @ApiOperation("修改保存数据权限")
    @SaCheckPermission("system:role:index:update")
    @Log(title = "角色管理", businessType = BusinessType.UPDATE)
    @PutMapping("/dataScope")
    public R<Void> dataScope(@RequestBody SysRole role) {
        roleService.checkRoleAllowed(role);
        roleService.checkRoleDataScope(role.getRoleId());
        return toAjax(roleService.authDataScope(role));
    }

    /**
     * 状态修改
     */
    @ApiOperation("状态修改")
    @SaCheckPermission("system:role:index:update")
    @Log(title = "角色管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public R<Void> changeStatus(@RequestBody SysRole role) {
        roleService.checkRoleAllowed(role);
        roleService.checkRoleDataScope(role.getRoleId());
        return toAjax(roleService.updateRoleStatus(role));
    }

    /**
     * 删除角色
     */
    @ApiOperation("删除角色")
    @SaCheckPermission("system:role:index:delete")
    @Log(title = "角色管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{roleIds}")
    public R<Void> remove(@ApiParam("角色ID串") @PathVariable Long[] roleIds) {
        return toAjax(roleService.deleteRoleByIds(roleIds));
    }

    /**
     * 获取角色选择框列表
     */
    @ApiOperation("获取角色选择框列表")
    @SaCheckPermission(value = {"system:role:index:query","system:role:index:list"},mode = SaMode.OR)
    @GetMapping("/optionselect")
    public R<List<SysRole>> optionselect() {
        return R.ok(roleService.selectRoleAll());
    }

    /**
     * 查询已分配用户角色列表
     */
    @ApiOperation("查询已分配用户角色列表")
    @SaCheckPermission(value = {"system:role:index:query","system:role:index:list"},mode = SaMode.OR)
    @GetMapping("/authUser/allocatedList")
    public TableDataInfo<SysUser> allocatedList(SysUser user, PageQuery pageQuery) {
        return userService.selectAllocatedList(user, pageQuery);
    }

    /**
     * 查询未分配用户角色列表
     */
    @ApiOperation("查询未分配用户角色列表")
    @SaCheckPermission(value = {"system:role:index:query","system:role:index:list"},mode = SaMode.OR)
    @GetMapping("/authUser/unallocatedList")
    public TableDataInfo<SysUser> unallocatedList(SysUser user, PageQuery pageQuery) {
        return userService.selectUnallocatedList(user, pageQuery);
    }

    /**
     * 取消授权用户
     */
    @ApiOperation("取消授权用户")
    @SaCheckPermission("system:role:index:update")
    @Log(title = "角色管理", businessType = BusinessType.GRANT)
    @PutMapping("/authUser/cancel")
    public R<Void> cancelAuthUser(@RequestBody SysUserRole userRole) {
        return toAjax(roleService.deleteAuthUser(userRole));
    }

    /**
     * 批量取消授权用户
     */
    @ApiOperation("批量取消授权用户")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "roleId", value = "角色ID", paramType = "query", dataTypeClass = String.class),
        @ApiImplicitParam(name = "userIds", value = "用户ID串", paramType = "query", dataTypeClass = String.class)
    })
    @SaCheckPermission("system:role:index:update")
    @Log(title = "角色管理", businessType = BusinessType.GRANT)
    @PutMapping("/authUser/cancelAll")
    public R<Void> cancelAuthUserAll(Long roleId, Long[] userIds) {
        return toAjax(roleService.deleteAuthUsers(roleId, userIds));
    }

    /**
     * 批量选择用户授权
     */
    @ApiOperation("批量选择用户授权")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "roleId", value = "角色ID", paramType = "query", dataTypeClass = String.class),
        @ApiImplicitParam(name = "userIds", value = "用户ID串", paramType = "query", dataTypeClass = String.class)
    })
    @SaCheckPermission("system:role:index:update")
    @Log(title = "角色管理", businessType = BusinessType.GRANT)
    @PutMapping("/authUser/selectAll")
    public R<Void> selectAuthUserAll(Long roleId, Long[] userIds) {
        roleService.checkRoleDataScope(roleId);
        return toAjax(roleService.insertAuthUsers(roleId, userIds));
    }
}
