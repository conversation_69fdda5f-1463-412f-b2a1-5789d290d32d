package com.zhonghe.web.controller.thridparty;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.zhonghe.common.core.controller.BaseController;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.system.domain.SysRobotMsg;
import com.zhonghe.system.domain.bo.SysRobotConfigBo;
import com.zhonghe.system.domain.vo.SysRobotConfigVo;
import com.zhonghe.system.service.IPushRobotMessageService;
import com.zhonghe.system.service.ISysRobotConfigService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.UnsupportedEncodingException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Collections;

/**
 * @description:
 * @author: cq
 * @date: 2023/3/29 14:13
 */

@Api(value = "WEBHOOK模块", tags = {"WEBHOOK管理"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/com/zhonghe/system/webhook")
public class WebHookMsgController extends BaseController {

    private final ISysRobotConfigService robotConfigService;

    private final IPushRobotMessageService robotMessageService;

    @ApiOperation("查询机器人配置信息")
    @SaCheckPermission("system:webhook:index:list")
    @GetMapping("/list")
    public TableDataInfo<SysRobotConfigVo> list(SysRobotConfigBo post, PageQuery pageQuery) {
        return robotConfigService.queryPageList(post, pageQuery);
    }

    @ApiOperation("发送机器人消息")
    @SaCheckPermission("system:webhook:index:send")
    @PostMapping("/send/{robotId}")
    public boolean send(@PathVariable Long robotId, @RequestBody SysRobotMsg template) throws UnsupportedEncodingException, NoSuchAlgorithmException, InvalidKeyException {
        return robotMessageService.sendMessage(Collections.singletonList(robotId), template);
    }
}

