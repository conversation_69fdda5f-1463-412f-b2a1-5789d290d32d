package com.zhonghe.web.controller.system;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaMode;
import com.zhonghe.common.annotation.Log;
import com.zhonghe.common.annotation.RepeatSubmit;
import com.zhonghe.common.core.controller.BaseController;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import com.zhonghe.common.enums.BusinessType;
import com.zhonghe.common.utils.poi.ExcelUtil;
import com.zhonghe.system.domain.bo.SysNoticeTemplateBo;
import com.zhonghe.system.domain.vo.SysNoticeTemplateVo;
import com.zhonghe.system.service.ISysNoticeTemplateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 系统消息通知模版
 *
 * <AUTHOR>
 * @date 2023-03-22
 */
@Validated
@Api(value = "系统消息通知模版", tags = {"系统消息通知模版"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/com/zhonghe/system/notice-template")
public class SysNoticeTemplateController extends BaseController {

    private final ISysNoticeTemplateService iSysNoticeTemplateService;

    /**
     * 查询模版列表
     */
    @ApiOperation("查询模版列表")
    @SaCheckPermission(value = {"system:notice-template:query", "system:notice-template:list"},mode = SaMode.OR)
    @GetMapping("/list")
    public TableDataInfo<SysNoticeTemplateVo> list(SysNoticeTemplateBo bo, PageQuery pageQuery) {
        return iSysNoticeTemplateService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出列表
     */
    @ApiOperation("导出模版列表")
    @SaCheckPermission("system:notice-template:export")
    @Log(title = "系统消息通知模版", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SysNoticeTemplateBo bo, HttpServletResponse response) {
        List<SysNoticeTemplateVo> list = iSysNoticeTemplateService.queryList(bo);
        ExcelUtil.exportExcel(list, "1", SysNoticeTemplateVo.class, response);
    }

    /**
     * 获取详细信息
     */
    @ApiOperation("获取模版详细信息")
    @SaCheckPermission(value = {"system:notice-template:query", "system:notice-template:list"},mode = SaMode.OR)
    @GetMapping("/{templateId}")
    public R<SysNoticeTemplateVo> getInfo(@ApiParam("主键")
                                     @NotNull(message = "主键不能为空")
                                     @PathVariable("templateId") Long templateId) {
        return R.ok(iSysNoticeTemplateService.queryById(templateId));
    }

    /**
     * 新增
     */
    @ApiOperation("新增模版")
    @SaCheckPermission("system:notice-template:add")
    @Log(title = "系统消息通知模版", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SysNoticeTemplateBo bo) {
        return toAjax(iSysNoticeTemplateService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改
     */
    @ApiOperation("修改模版")
    @SaCheckPermission("system:notice-template:edit")
    @Log(title = "系统消息通知模版", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SysNoticeTemplateBo bo) {
        return toAjax(iSysNoticeTemplateService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除
     */
    @ApiOperation("删除模版")
    @SaCheckPermission("system:notice-template:remove")
    @Log(title = "系统消息通知模版", businessType = BusinessType.DELETE)
    @DeleteMapping("/{templateIds}")
    public R<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable Long[] templateIds) {
        return toAjax(iSysNoticeTemplateService.deleteWithValidByIds(Arrays.asList(templateIds), true) ? 1 : 0);
    }
}
