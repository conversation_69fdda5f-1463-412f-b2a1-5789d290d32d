package com.zhonghe.web.controller.monitor;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaMode;
import com.zhonghe.common.constant.Constants;
import com.zhonghe.common.core.controller.BaseController;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.common.core.domain.dto.UserOnlineDTO;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.utils.redis.RedisUtils;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.system.domain.vo.SysUserOnlineVo;
import com.zhonghe.common.core.domain.entity.SysUser;
import com.zhonghe.common.core.domain.entity.SysDept;
import com.zhonghe.system.mapper.SysUserMapper;
import com.zhonghe.system.mapper.SysDeptMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import cn.hutool.core.bean.BeanUtil;
import cn.dev33.satoken.exception.NotLoginException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import cn.dev33.satoken.stp.StpUtil;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 在线用户监控
 *
 * <AUTHOR> Li
 */
@Api(value = "在线用户监控", tags = {"在线用户监控管理"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/com/zhonghe/monitor/online")
public class SysUserOnlineController extends BaseController {

    private final SysUserMapper userMapper;
    private final SysDeptMapper deptMapper;

    @ApiOperation("在线用户列表")
    @SaCheckPermission( value = {"system:monitor:onlineuser:index:query","system:monitor:onlineuser:index:list"},mode = SaMode.OR)
    @GetMapping("/list")
    public TableDataInfo<SysUserOnlineVo> list(String loginLocation, String userName) {
        // 获取所有未过期的 token
        List<String> keys = StpUtil.searchTokenValue("", -1, 0);
        List<UserOnlineDTO> userOnlineDTOList = new ArrayList<>();
        for (String key : keys) {
            String token = key.replace(Constants.LOGIN_TOKEN_KEY, "");
            // 如果已经过期则踢下线
            if (StpUtil.stpLogic.getTokenActivityTimeoutByToken(token) < 0) {
                continue;
            }
            userOnlineDTOList.add(RedisUtils.getCacheObject(Constants.ONLINE_TOKEN_KEY + token));
        }
        if (StringUtils.isNotEmpty(loginLocation) && StringUtils.isNotEmpty(userName)) {
            userOnlineDTOList = search(loginLocation, userOnlineDTOList);
            userOnlineDTOList = search2(userName, userOnlineDTOList);
        } else if (StringUtils.isNotEmpty(loginLocation)) {
            userOnlineDTOList = search(loginLocation, userOnlineDTOList);
        } else if (StringUtils.isNotEmpty(userName)) {
            userOnlineDTOList = search2(userName, userOnlineDTOList);
        }
        Collections.reverse(userOnlineDTOList);
        userOnlineDTOList.removeAll(Collections.singleton(null));

        // 转换为VO对象并填充用户和部门信息
        List<SysUserOnlineVo> voList = userOnlineDTOList.stream().map(online -> {
            SysUserOnlineVo vo = BeanUtil.toBean(online, SysUserOnlineVo.class);

            // 查询用户信息
            SysUser user = userMapper.selectOne(new LambdaQueryWrapper<SysUser>()
                .eq(SysUser::getUserName, online.getUserName()));

            if (user != null) {
                vo.setNickName(user.getNickName());

                // 查询部门信息
                if (user.getDeptId() != null) {
                    SysDept dept = deptMapper.selectById(user.getDeptId());
                    if (dept != null) {
                        vo.setDeptName(dept.getDeptName());
                    }
                }
            }

            return vo;
        }).collect(Collectors.toList());

        return TableDataInfo.build(voList);
    }

    /**
     * List模糊查询
     * */
    public List search(String name,List list){
        List results = new ArrayList();
        Pattern pattern = Pattern.compile(name);
        for(int i=0; i < list.size(); i++){
            Matcher matcher = pattern.matcher(((UserOnlineDTO)list.get(i)).getLoginLocation());
            if(matcher.find()){
                results.add(list.get(i));
            }
        }
        return results;
    }

    public List search2(String name,List list){
        List results = new ArrayList();
        Pattern pattern = Pattern.compile(name);
        for(int i=0; i < list.size(); i++){
            Matcher matcher = pattern.matcher(((UserOnlineDTO)list.get(i)).getUserName());
            if(matcher.find()){
                results.add(list.get(i));
            }
        }
        return results;
    }

    /**
     * 强退用户
     */
    @ApiOperation("强退用户")
    @DeleteMapping("/{tokenId}")
    public R<Void> forceLogout(@PathVariable String tokenId) {
        try {
            StpUtil.kickoutByTokenValue(tokenId);
        } catch (NotLoginException e) {
        }
        return R.ok();
    }
}
