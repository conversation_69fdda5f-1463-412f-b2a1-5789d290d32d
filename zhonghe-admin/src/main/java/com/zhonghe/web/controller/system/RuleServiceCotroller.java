package com.zhonghe.web.controller.system;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.common.core.domain.entity.RuleService;
import com.zhonghe.common.core.service.RuleServiceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@Api(tags = "gis平台 - 规则服务")
@RestController
@RequiredArgsConstructor
@RequestMapping("/com/zhonghe/system/ruleService")
@Validated
public class RuleServiceCotroller {

    private final RuleServiceService ruleServiceService;

    @GetMapping("/selectAll")
    @ApiOperation("全部规则服务")
    public R<IPage<RuleService>> selectAll(String ruleName, PageQuery pageQuery){
        return R.ok(ruleServiceService.selectAll(ruleName, pageQuery));
    }

    @GetMapping("/{serviceId}")
    @ApiOperation("单个规则服务")
    public R<RuleService> selectById(@PathVariable(value = "serviceId", required = false) Long serviceId){
        return R.ok(ruleServiceService.selectById(serviceId));
    }

    @PostMapping()
    @ApiOperation("添加规则服务")
    public R<Void> saveEntity(RuleService gisService){
        try{
            int i = ruleServiceService.saveEntity(gisService);
            if (i ==-1) throw new RuntimeException("规则名称已存在");
        } catch (Exception e){
          e.printStackTrace();
          return R.ok(e.getMessage());
        }
        return R.ok("添加成功！");
    }

    @DeleteMapping("/{serviceId}")
    @ApiOperation("删除规则服务")
    public R<Void> deleteEntity(@PathVariable("serviceId") Long serviceId){
        try{
            ruleServiceService.deleteEntity(serviceId);
        } catch (Exception e){
            e.printStackTrace();
            return R.ok("删除失败，请加检查参数");
        }
        return R.ok("删除成功！");
    }

    @PutMapping("/updateEntity")
    @ApiOperation("修改规则服务")
    public R<Void> updateEntity(RuleService gisService){
        ruleServiceService.updateEntity(gisService);
        return R.ok("更新成功");
    }


}



