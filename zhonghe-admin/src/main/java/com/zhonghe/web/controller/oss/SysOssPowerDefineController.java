package com.zhonghe.web.controller.oss;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.zhonghe.common.annotation.Log;
import com.zhonghe.common.annotation.RepeatSubmit;
import com.zhonghe.common.core.controller.BaseController;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import com.zhonghe.common.enums.BusinessType;
import com.zhonghe.common.utils.poi.ExcelUtil;
import com.zhonghe.system.domain.bo.SysOssPowerDefineBo;
import com.zhonghe.system.domain.vo.SysOssPowerDefineVo;
import com.zhonghe.system.service.ISysOssPowerDefineService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 存储桶权限Controller
 *
 * <AUTHOR>
 * @date 2023-03-31
 */
@Validated
@Api(value = "存储桶权限控制器", tags = {"存储桶权限管理"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/ossPowerDefine")
public class SysOssPowerDefineController extends BaseController {

    private final ISysOssPowerDefineService iSysOssPowerDefineService;

    /**
     * 查询存储桶权限列表
     */
    @ApiOperation("查询存储桶权限列表")
    @SaCheckPermission("system:ossPowerDefine:list")
    @GetMapping("/list")
    public TableDataInfo<SysOssPowerDefineVo> list(SysOssPowerDefineBo bo, PageQuery pageQuery) {
        return iSysOssPowerDefineService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出存储桶权限列表
     */
    @ApiOperation("导出存储桶权限列表")
    @SaCheckPermission("system:ossPowerDefine:export")
    @Log(title = "存储桶权限", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SysOssPowerDefineBo bo, HttpServletResponse response) {
        List<SysOssPowerDefineVo> list = iSysOssPowerDefineService.queryList(bo);
        ExcelUtil.exportExcel(list, "存储桶权限", SysOssPowerDefineVo.class, response);
    }

    /**
     * 获取存储桶权限详细信息
     */
    @ApiOperation("获取存储桶权限详细信息")
    @SaCheckPermission("system:ossPowerDefine:query")
    @GetMapping("/{power}")
    public R<SysOssPowerDefineVo> getInfo(@ApiParam("主键")
                                     @NotNull(message = "主键不能为空")
                                     @PathVariable("power") String power) {
        return R.ok(iSysOssPowerDefineService.queryById(power));
    }

    /**
     * 新增存储桶权限
     */
    @ApiOperation("新增存储桶权限")
    @SaCheckPermission("system:ossPowerDefine:add")
    @Log(title = "存储桶权限", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SysOssPowerDefineBo bo) {
        return toAjax(iSysOssPowerDefineService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改存储桶权限
     */
    @ApiOperation("修改存储桶权限")
    @SaCheckPermission("system:ossPowerDefine:edit")
    @Log(title = "存储桶权限", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SysOssPowerDefineBo bo) {
        return toAjax(iSysOssPowerDefineService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除存储桶权限
     */
    @ApiOperation("删除存储桶权限")
    @SaCheckPermission("system:ossPowerDefine:remove")
    @Log(title = "存储桶权限", businessType = BusinessType.DELETE)
    @DeleteMapping("/{powers}")
    public R<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable String[] powers) {
        return toAjax(iSysOssPowerDefineService.deleteWithValidByIds(Arrays.asList(powers), true) ? 1 : 0);
    }
}
