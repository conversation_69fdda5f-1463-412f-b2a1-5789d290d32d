package com.zhonghe.web.controller.oss;

import java.util.List;
import java.util.Arrays;

import lombok.RequiredArgsConstructor;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;

import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.zhonghe.common.annotation.RepeatSubmit;
import com.zhonghe.common.annotation.Log;
import com.zhonghe.common.core.controller.BaseController;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import com.zhonghe.common.enums.BusinessType;
import com.zhonghe.common.utils.poi.ExcelUtil;
import com.zhonghe.system.domain.vo.SysOssBucketSecVo;
import com.zhonghe.system.domain.bo.SysOssBucketSecBo;
import com.zhonghe.system.service.ISysOssBucketSecService;
import com.zhonghe.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiOperation;

/**
 * 存储桶描述控制器
 *
 * <AUTHOR>
 * @date 2023-04-03
 */
@Validated
@Api(value = "存储桶描述控制器", tags = {"存储桶描述管理"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/oss/ossBucketSec")
public class SysOssBucketSecController extends BaseController {

    private final ISysOssBucketSecService iSysOssBucketSecService;

    /**
     * 查询存储桶描述列表
     */
    @ApiOperation("查询存储桶描述列表")
    @SaCheckPermission("system:ossBucketSec:list")
    @GetMapping("/list")
    public TableDataInfo<SysOssBucketSecVo> list(SysOssBucketSecBo bo, PageQuery pageQuery) {
        return iSysOssBucketSecService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出存储桶描述列表
     */
    @ApiOperation("导出存储桶描述列表")
    @SaCheckPermission("system:ossBucketSec:export")
    @Log(title = "存储桶描述", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SysOssBucketSecBo bo, HttpServletResponse response) {
        List<SysOssBucketSecVo> list = iSysOssBucketSecService.queryList(bo);
        ExcelUtil.exportExcel(list, "存储桶描述", SysOssBucketSecVo.class, response);
    }

    /**
     * 获取存储桶描述详细信息
     */
    @ApiOperation("获取存储桶描述详细信息")
    @SaCheckPermission("system:ossBucketSec:query")
    @GetMapping("/{id}")
    public R<SysOssBucketSecVo> getInfo(@ApiParam("主键")
                                     @NotNull(message = "主键不能为空")
                                     @PathVariable("id") Long id) {
        return R.ok(iSysOssBucketSecService.queryById(id));
    }

    /**
     * 新增存储桶描述
     */
    @ApiOperation("新增存储桶描述")
    @SaCheckPermission("system:ossBucketSec:add")
    @Log(title = "存储桶描述", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SysOssBucketSecBo bo) {
        return toAjax(iSysOssBucketSecService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改存储桶描述
     */
    @ApiOperation("修改存储桶描述")
    @SaCheckPermission("system:ossBucketSec:edit")
    @Log(title = "存储桶描述", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SysOssBucketSecBo bo) {
        return toAjax(iSysOssBucketSecService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除存储桶描述
     */
    @ApiOperation("删除存储桶描述")
    @SaCheckPermission("system:ossBucketSec:remove")
    @Log(title = "存储桶描述", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@ApiParam("主键串")
                          @NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(iSysOssBucketSecService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
