package com.zhonghe.web.controller.oss;

/**
 * @Author: lpg
 * @Date: 2023/03/31/11:23
 * @Description:
 */

import cn.dev33.satoken.annotation.SaCheckLogin;
import com.zhonghe.common.annotation.Anonymous;
import com.zhonghe.common.config.ServerConfig;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.system.domain.vo.OSSInfoVo;
import com.zhonghe.system.service.ISysOssService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 对象存储链接信息管理
 *
 * <AUTHOR>
 */
@Validated
@Api(value = "生成对象存储链接", tags = {"对象存储链接信息管理"})
@RequiredArgsConstructor
@RestController
//@RequestMapping("/com/zhonghe/system/oss-info")
public class SysOssInfoController {

    private final ISysOssService sysOssService;
    @Resource
    private ServerConfig serverConfig;

    @ApiOperation("查询OSS存储统计")
    @SaCheckLogin
    @GetMapping
    public R<OSSInfoVo> getBucketSize(@ApiParam("云盘名称") @RequestParam(required = false) String bucketName) {
        return R.ok(sysOssService.getOssInfo(bucketName));
    }

    @ApiOperation("生成链接")
    @Anonymous
    @GetMapping("/s")
    public R<String> buildUrl(@ApiParam("文件id") @RequestParam Long bucketId) {
        return R.ok(null, serverConfig.getIp() + "/s/" + sysOssService.buildUrl(bucketId));
    }

    @ApiOperation("对外预览文件")
    @Anonymous
    @GetMapping("/s/{param}")
    public void preViewFile(@PathVariable String param, HttpServletResponse response) {
        sysOssService.preViewFile(param, response);
    }

    @ApiOperation("对内预览文件")
    @Anonymous
    @GetMapping("/home/<USER>")
    //@OssDataPermission(has = {OssDataScopeType.OWNER, OssDataScopeType.READ})
    public void preViewFileFromHome(@PathVariable String param, HttpServletResponse response) {
        sysOssService.preViewFileFromHome(param, response);
    }

    @ApiOperation("下载桶内文件")
    @Anonymous
    @GetMapping("/download/{id}")
    public void downloadFromBucket(@ApiParam("文件id") @PathVariable Long id, HttpServletRequest request, HttpServletResponse response) {
        sysOssService.downloadFromBucket(id, request, response);
    }

}
