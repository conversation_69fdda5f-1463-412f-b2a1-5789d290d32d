package com.zhonghe.web.controller.oss;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.zhonghe.common.annotation.Log;
import com.zhonghe.common.annotation.RepeatSubmit;
import com.zhonghe.common.core.controller.BaseController;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import com.zhonghe.common.enums.BusinessType;
import com.zhonghe.common.utils.poi.ExcelUtil;
import com.zhonghe.system.domain.bo.SysOssPowerRelationBo;
import com.zhonghe.system.domain.vo.SysOssPowerRelationVo;
import com.zhonghe.system.service.ISysOssPowerRelationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 存储桶部门关系Controller
 *
 * <AUTHOR>
 * @date 2023-03-31
 */
@Validated
@Api(value = "存储桶部门关系控制器", tags = {"存储桶部门关系管理"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/ossPowerRelation")
public class SysOssPowerRelationController extends BaseController {

    private final ISysOssPowerRelationService iSysOssPowerRelationService;

    /**
     * 查询存储桶部门关系列表
     */
    @ApiOperation("查询存储桶部门关系列表")
    @SaCheckPermission("system:ossPowerRelation:list")
    @GetMapping("/list")
    public TableDataInfo<SysOssPowerRelationVo> list(SysOssPowerRelationBo bo, PageQuery pageQuery) {
        return iSysOssPowerRelationService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出存储桶部门关系列表
     */
    @ApiOperation("导出存储桶部门关系列表")
    @SaCheckPermission("system:ossPowerRelation:export")
    @Log(title = "存储桶部门关系", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SysOssPowerRelationBo bo, HttpServletResponse response) {
        List<SysOssPowerRelationVo> list = iSysOssPowerRelationService.queryList(bo);
        ExcelUtil.exportExcel(list, "存储桶部门关系", SysOssPowerRelationVo.class, response);
    }

    /**
     * 获取存储桶部门关系详细信息
     */
    @ApiOperation("获取存储桶部门关系详细信息")
    @SaCheckPermission("system:ossPowerRelation:query")
    @GetMapping("/{bucketDeptId}")
    public R<SysOssPowerRelationVo> getInfo(@ApiParam("主键")
                                     @NotNull(message = "主键不能为空")
                                     @PathVariable("bucketDeptId") Long bucketDeptId) {
        return R.ok(iSysOssPowerRelationService.queryById(bucketDeptId));
    }

    /**
     * 新增存储桶部门关系
     */
    @ApiOperation("新增存储桶部门关系")
    @SaCheckPermission("system:ossPowerRelation:add")
    @Log(title = "存储桶部门关系", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SysOssPowerRelationBo bo) {
        return toAjax(iSysOssPowerRelationService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改存储桶部门关系
     */
    @ApiOperation("修改存储桶部门关系")
    @SaCheckPermission("system:ossPowerRelation:edit")
    @Log(title = "存储桶部门关系", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SysOssPowerRelationBo bo) {
        return toAjax(iSysOssPowerRelationService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除存储桶部门关系
     */
    @ApiOperation("删除存储桶部门关系")
    @SaCheckPermission("system:ossPowerRelation:remove")
    @Log(title = "存储桶部门关系", businessType = BusinessType.DELETE)
    @DeleteMapping("/{bucketDeptIds}")
    public R<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable Long[] bucketDeptIds) {
        return toAjax(iSysOssPowerRelationService.deleteWithValidByIds(Arrays.asList(bucketDeptIds), true) ? 1 : 0);
    }
}
