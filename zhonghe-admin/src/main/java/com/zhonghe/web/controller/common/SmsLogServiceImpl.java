package com.zhonghe.web.controller.common;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.sms.entity.SmsLogDO;
import com.zhonghe.sms.entity.SmsRequestEbtity;
import com.zhonghe.system.mapper.SmsLogConvert;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 短信日志 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class SmsLogServiceImpl implements SmsLogService {

    @Resource
    private SmsLogConvert smsLogMapper;

    @Override
    public Long createSmsLog(String mobile, Long userId, Integer userType, Boolean isSend,
                             SmsRequestEbtity template, String templateContent, Map templateParams) {
        SmsLogDO.SmsLogDOBuilder logBuilder = SmsLogDO.builder();
        // 根据是否要发送，设置状态
        logBuilder.sendStatus(Objects.equals(isSend, true) ? SmsSendStatusEnum.INIT.getStatus()
                : SmsSendStatusEnum.IGNORE.getStatus());
        // 设置手机相关字段
        logBuilder.mobile(mobile).userId(userId).userType(userType);
        // 设置模板相关字段
        logBuilder.templateId(template.getOutId()).templateCode(template.getOutId()).templateType(template.getSmsType());
        logBuilder.templateContent(templateContent).templateParams(templateParams)
                .apiTemplateId(template.getTemplateCode());
        // 设置渠道相关字段
        logBuilder.channelId(template.getResourceOwnerId()).channelCode(template.getTestChannel());
        // 设置接收相关字段
        logBuilder.receiveStatus(SmsReceiveStatusEnum.INIT.getStatus());

        // 插入数据库
        SmsLogDO logDO = logBuilder.build();
        smsLogMapper.insert(logDO);
        return logDO.getId();
    }

    @Override
    public void updateSmsSendResult(Long id, Integer sendCode, String sendMsg,
                                    String apiSendCode, String apiSendMsg,
                                    String apiRequestId, String apiSerialNo) {
//        SmsSendStatusEnum sendStatus = CommonResult.isSuccess(sendCode) ?
//                SmsSendStatusEnum.SUCCESS : SmsSendStatusEnum.FAILURE;  判断发送成功还是失败
        SmsSendStatusEnum sendStatus = SmsSendStatusEnum.SUCCESS;

        smsLogMapper.updateById(SmsLogDO.builder().id(id).sendStatus(sendStatus.getStatus())
                .sendTime(new Date()).sendCode(sendCode).sendMsg(sendMsg)
                .apiSendCode(apiSendCode).apiSendMsg(apiSendMsg)
                .apiRequestId(apiRequestId).apiSerialNo(apiSerialNo).build());
    }

    @Override
    public void updateSmsReceiveResult(Long id, Boolean success, Date receiveTime,
                                       String apiReceiveCode, String apiReceiveMsg) {
        SmsReceiveStatusEnum receiveStatus = Objects.equals(success, true) ?
                SmsReceiveStatusEnum.SUCCESS : SmsReceiveStatusEnum.FAILURE;
        smsLogMapper.updateById(SmsLogDO.builder().id(id).receiveStatus(receiveStatus.getStatus())
                .receiveTime(receiveTime).apiReceiveCode(apiReceiveCode).apiReceiveMsg(apiReceiveMsg).build());
    }

    @Override
    public Page<SmsLogDO> getSmsLogPage(SmsLogDO pageReqVO,PageQuery pageQuery) {
        LambdaQueryWrapper<SmsLogDO> lqw = buildQueryWrapper(pageReqVO);
        Page<SmsLogDO> result = smsLogMapper.selectVoPage(pageQuery.build(), lqw);
//        List<SmsLogDO> smsLogDOS = smsLogMapper.selectList();

        return result;
    }

    @SneakyThrows
    private LambdaQueryWrapper<SmsLogDO> buildQueryWrapper(SmsLogDO bo) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
//        Date parse = simpleDateFormat.parse("2021-11-09");
        LambdaQueryWrapper<SmsLogDO> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getMobile()), SmsLogDO::getMobile, bo.getMobile());
        lqw.like(StringUtils.isNotBlank(bo.getChannelCode()), SmsLogDO::getChannelCode, bo.getChannelCode());
        lqw.eq(bo.getSendStatus() != null, SmsLogDO::getSendStatus, bo.getSendStatus());
        lqw.like(StringUtils.isNotBlank(bo.getTemplateId()), SmsLogDO::getTemplateId, bo.getTemplateId());
        if (bo.getBeginSendTime() != null) {
            lqw.ge(true, SmsLogDO::getSendTime, simpleDateFormat.parse(bo.getBeginSendTime()));
        }
        lqw.eq(bo.getReceiveStatus() != null, SmsLogDO::getReceiveStatus, bo.getReceiveStatus());
        if (bo.getEndSendTime() != null) {
            lqw.le(bo.getEndSendTime() != null, SmsLogDO::getSendTime, simpleDateFormat.parse(bo.getEndSendTime()));
        }
        if (bo.getBeginReceiveTime() != null && bo.getEndReceiveTime() != null) {
            lqw.between(SmsLogDO::getReceiveTime, bo.getBeginReceiveTime(), DateUtil.offsetDay(bo.getEndReceiveTime(), 1));
        }
        Map<String, Object> params = bo.getParams();
        if (!CollectionUtils.isEmpty(params)) {
            if (params.get("beginReceiveTime") != null && params.get("endReceiveTime") != null) {
                lqw.between(SmsLogDO::getReceiveTime, DateUtil.parse(params.get("beginReceiveTime").toString()),
                    DateUtil.offsetDay(DateUtil.parse(params.get("endReceiveTime").toString()), 1));
            }
            if (params.get("beginSendTime") != null && params.get("endSendTime") != null) {
                lqw.between(SmsLogDO::getSendTime, DateUtil.parse(params.get("beginSendTime").toString()),
                    DateUtil.offsetDay(DateUtil.parse(params.get("endSendTime").toString()), 1));
            }
        }
        return lqw;
    }

    @Override
    public List<SmsLogDO> getSmsLogList(SmsLogDO exportReqVO) {
        List<SmsLogDO> smsLogDOS = smsLogMapper.selectList();
        return smsLogDOS;
    }

}
