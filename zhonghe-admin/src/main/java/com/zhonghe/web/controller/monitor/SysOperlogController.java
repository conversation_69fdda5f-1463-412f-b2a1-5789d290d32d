package com.zhonghe.web.controller.monitor;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaMode;
import com.zhonghe.common.annotation.Log;
import com.zhonghe.common.core.controller.BaseController;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.enums.BusinessType;
import com.zhonghe.common.utils.poi.ExcelUtil;
import com.zhonghe.common.utils.poi.MultiThread;
import com.zhonghe.system.domain.SysOperLog;
import com.zhonghe.system.domain.vo.SysOperLogVo;
import com.zhonghe.system.service.ISysOperLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * 操作日志记录
 *
 * <AUTHOR> Li
 */
@Validated
@Api(value = "操作日志记录", tags = {"操作日志记录管理"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/com/zhonghe/monitor/operlog")
public class SysOperlogController extends BaseController {

    private final ISysOperLogService operLogService;

    @ApiOperation("查询操作日志记录列表")
    @SaCheckPermission(value = {"system:apilog:operlog:index:query","system:apilog:operlog:index:list"} ,mode = SaMode.OR)
    @GetMapping("/list")
    public TableDataInfo<SysOperLogVo> list(SysOperLog operLog, PageQuery pageQuery) {
        TableDataInfo<SysOperLogVo> sysOperLogTableDataInfo = operLogService.selectPageOperLogList(operLog, pageQuery);
        if(sysOperLogTableDataInfo.getTotal()>1000) {
            operLogService.ftest();
        }
        return sysOperLogTableDataInfo;
    }

    @ApiOperation("导出操作日志记录列表")
    @Log(title = "操作日志", businessType = BusinessType.EXPORT)
    @SaCheckPermission("system:apilog:operlog:index:export")
    @PostMapping("/export")
    public void export(SysOperLog operLog, HttpServletResponse response) {
        List<SysOperLog> list = operLogService.selectOperLogList(operLog);
//        List<SysOperLog> sysOperLogs = list.subList(0, 100);
        List<SysOperLog> sysOperLogs = subList(list);
        ExcelUtil.exportExcel(list, "操作日志", SysOperLog.class, response);
    }

    /**
     * 自定义分页list集合
     *
     * @param dataList
     * @return
     */
    public List<SysOperLog> subList(List<SysOperLog> dataList) {
        List<SysOperLog> resultlist = new ArrayList<>();//存放批量数据处理的总结果集
        if (null != dataList && dataList.size() > 0) {
            int pointsDataLimit = 100;//限制条数 10条一批  也是线程池线程数量
            Integer size = dataList.size();
            //判断是否有必要分批
            if (pointsDataLimit < size) {//大于10条
                int part = size / pointsDataLimit;//分批数
                for (int i = 0; i < part; i++) {
                    List<SysOperLog> listPage = dataList.subList(0, pointsDataLimit);  //10条数据
//                    Integer start = cardlist.size();//10条数据在总结果集的开始下标
                    List<SysOperLog> sysOperLogs = testTime(listPage, resultlist);//返回拼接后的总结果集
                    Integer end = resultlist .size();//10条数据在总结果集的结束下标
//                    for (i = start; i < end; i++) {
//                        //对总数据集的10条数据进行业务处理
//                    }
                    //剔除已经处理过的10条数据
                    dataList.subList(0, pointsDataLimit).clear();
                }
                if (!dataList.isEmpty()) {//小于10条
//                    cardlist = testTime(dataList, cardlist);//处理最后的数据
                }
            } else {
            }
        }

        return resultlist ;
    }
    /**
     * 多线程处理批量数据
     *
     * @param splitList 处理数据
     *
     * @return
     */
    public List<SysOperLog> testTime(List<SysOperLog> splitList, List<SysOperLog> cardlistr) {
        List<SysOperLog> list = null;
        try {
            MultiThread<SysOperLog, SysOperLog> multiThread = new MultiThread<SysOperLog, SysOperLog>(splitList) {
                @Override
                public SysOperLog outExecute(int currentThread, SysOperLog data) {
                    //业务处理
                   /* String iccid = data.getIccid();
                    String allOrder = cardServerService.findAllOrder(iccid);
                    String allFlow = cardServerService.allFlowByiccid(iccid);
                    String allUseFlow = cardServerService.allUseFlowByiccid(iccid);
                    Card card = cardMapper.findByIccid(iccid);
                    String monthFlow = card.getMonthFlow();
                    data.setMonthFlow(monthFlow);
                    data.setAllOrder(allOrder);
                    data.setAllFlow(allFlow);
                    data.setAllUseFlow(allUseFlow);
                    return data;*/
                    //业务处理end
                    return data;
                }
            };
            list = multiThread.getResult();//返回结果
            for (SysOperLog ccar : list) {
                splitList.add(ccar);//批量数据遍历放入总结果
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return splitList;

    }


    @ApiOperation("删除操作日志记录")
    @Log(title = "操作日志", businessType = BusinessType.DELETE)
    @SaCheckPermission("system:apilog:operlog:index:delete")
    @DeleteMapping("/{operIds}")
    public R<Void> remove(@PathVariable Long[] operIds) {
        return toAjax(operLogService.deleteOperLogByIds(operIds));
    }

    @ApiOperation("清空操作日志记录")
    @Log(title = "操作日志", businessType = BusinessType.CLEAN)
    @SaCheckPermission("system:apilog:operlog:index:delete")
    @DeleteMapping("/clean")
    public R<Void> clean() {
        operLogService.cleanOperLog();
        return R.ok();
    }
}
