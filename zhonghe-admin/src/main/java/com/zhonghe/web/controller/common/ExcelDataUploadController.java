package com.zhonghe.web.controller.common;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.zhonghe.common.annotation.Log;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.common.enums.BusinessType;
import com.zhonghe.system.service.IUploadExcelToDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * @description:
 * @author: cq
 * @date: 2023/7/26 10:59
 */
@Validated
@Api(value = "EXCEL上传", tags = {"EXCEL上传"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/com/zhonghe/system/upload")
public class ExcelDataUploadController {

    private final IUploadExcelToDataService uploaderService;

    @ApiOperation("上传EXCEL文件")
    @PostMapping("{type}")
    @SaCheckPermission("system:uploader:base")
    @Log(title = "Excel上传", businessType = BusinessType.INSERT)
    public R<?> upload(@PathVariable("type") String type, MultipartFile file) {
        uploaderService.upload(type, file);
        return R.ok();
    }
}

