package com.zhonghe.web.controller.monitor;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaMode;
import com.alibaba.fastjson.JSON;
import com.zhonghe.common.constant.Constants;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.common.utils.redis.RedisUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.data.redis.connection.RedisServerCommands;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.Set;

/**
 * 缓存监控
 *
 * <AUTHOR> Li
 */
@Api(value = "缓存监控", tags = {"缓存监控管理"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/com/zhonghe/monitor/cache")
public class CacheController {

    private final RedisTemplate<String, String> redisTemplate;

    private final static List<HashMap<String,Object>> caches = new ArrayList<>();
    {
        caches.add(new HashMap<String, Object>() {{
            put("key",Constants.LOGIN_TOKEN_KEY);
            put("value","用户信息");}});
        caches.add(new HashMap<String, Object>() {{
            put("key",Constants.SYS_CONFIG_KEY);
            put("value","参数管理");}});
        caches.add(new HashMap<String, Object>() {{
            put("key",Constants.SYS_DICT_KEY);
            put("value","字典管理");}});
        caches.add(new HashMap<String, Object>() {{
            put("key",Constants.CAPTCHA_CODE_KEY);
            put("value","验证码");}});
        caches.add(new HashMap<String, Object>() {{
            put("key",Constants.REPEAT_SUBMIT_KEY);
            put("value","防重提交");}});
        caches.add(new HashMap<String, Object>() {{
            put("key",Constants.RATE_LIMIT_KEY);
            put("value","限流处理");}});
        caches.add(new HashMap<String, Object>() {{
            put("key",Constants.PWD_ERR_CNT_KEY);
            put("value","密码错误次数");}});
    }

    @ApiOperation("获取缓存监控详细信息")
    @SaCheckPermission(value = {"system:monitor:cachemonitor:index:query","system:monitor:cachemonitor:index:list"}, mode = SaMode.OR)
    @GetMapping()
    public R<Map<String, Object>> getInfo() throws Exception {
        Properties info = (Properties) redisTemplate.execute((RedisCallback<Object>) RedisServerCommands::info);
        Properties commandStats = (Properties) redisTemplate.execute((RedisCallback<Object>) connection -> connection.info("commandstats"));
        Object dbSize = redisTemplate.execute((RedisCallback<Object>) RedisServerCommands::dbSize);

        Map<String, Object> result = new HashMap<>(3);
        result.put("info", info);
        result.put("dbSize", dbSize);

        info.getProperty("sys.account.registerUser");
        Set<Object> objects = info.keySet();

        List<Map<String, String>> pieList = new ArrayList<>();
        if (commandStats != null) {
            commandStats.stringPropertyNames().forEach(key -> {
                Map<String, String> data = new HashMap<>(2);
                String property = commandStats.getProperty(key);
                data.put("name", StringUtils.removeStart(key, "cmdstat_"));
                data.put("value", StringUtils.substringBetween(property, "calls=", ",usec"));
                pieList.add(data);
            });
        }
//        Thread.sleep(2000);
        result.put("commandStats", pieList);
        return R.ok(result);
    }

//    @ApiOperation("获取缓存监控详细信息")
//    @SaCheckPermission("monitor:cache:list")
//    @GetMapping("/getNames")
//    public R<Map<String, Object>> getNames() throws Exception{
//
//        return R.ok();
//    }

    @ApiOperation("获取缓存列表")
    @GetMapping("/getNames")
    @SaCheckPermission(value = {"system:monitor:cachemonitor:index:query","system:monitor:cachemonitor:index:list"}, mode = SaMode.OR)
    public R cache()
    {
        return R.ok(caches);
    }

    @ApiOperation("获取键名列表")
    @GetMapping("/getKeys/{cacheName}")
    @SaCheckPermission(value = {"system:monitor:cachemonitor:index:query","system:monitor:cachemonitor:index:list"}, mode = SaMode.OR)
    public R getCacheKeys(@PathVariable String cacheName)
    {
        Set<String> cacheKeys = redisTemplate.keys(cacheName + "*");
        return R.ok(cacheKeys);
    }

    @ApiOperation("获取键名内容")
    @GetMapping("/getValue/{cacheName}/{cacheKey}")
    @SaCheckPermission(value = {"system:monitor:cachemonitor:index:query","system:monitor:cachemonitor:index:list"}, mode = SaMode.OR)
    public R getCacheValue(@PathVariable String cacheName, @PathVariable String cacheKey)
    {
//        String cacheKey1 = getCacheKey(cacheName);
        String cacheValue = redisTemplate.opsForValue().get(cacheName);
//        Map sysCache = RedisUtils.getCacheObject(cacheKey);

        //使用JSON将对象转成String
        String s = JSON.toJSONString(RedisUtils.getCacheObject(cacheKey));

        return R.ok(new HashMap<String, Object>(){{
            put("cacheName",cacheName);
            put("cacheKey",cacheKey);
            put("cacheValue",s); }});
    }

    @DeleteMapping("/clearCacheName/{cacheName}")
    @ApiOperation("删除单个缓存")
    @SaCheckPermission("system:monitor:cachemonitor:index:delete")
    public R clearCacheName(@PathVariable String cacheName)
    {
        Collection<String> cacheKeys = redisTemplate.keys(cacheName + "*");
//        boolean b = RedisUtils.deleteObject(cacheName);
        redisTemplate.delete(cacheKeys);
        redisTemplate.delete(cacheName);
        caches.removeIf( f -> f.get("key").toString().equals(cacheName));
        return R.ok();
    }

    @DeleteMapping("/clearCacheKey/{cacheKey}")
    @ApiOperation("删除键名")
    @SaCheckPermission("system:monitor:cachemonitor:index:delete")
    public R clearCacheKey(@PathVariable String cacheKey)
    {
        redisTemplate.delete(cacheKey);
        return R.ok();
    }

    @DeleteMapping("/clearCacheAll")
    @SaCheckPermission("system:monitor:cachemonitor:index:delete")
    public R clearCacheAll()
    {
        Collection<String> cacheKeys = redisTemplate.keys("*");
        redisTemplate.delete(cacheKeys);
        return R.ok();
    }
    String getCacheKey(String configKey) {
        return Constants.SYS_DICT_KEY + configKey;
    }
}
