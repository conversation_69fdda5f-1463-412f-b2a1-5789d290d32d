package com.zhonghe.web.controller.system;

import java.io.UnsupportedEncodingException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Collections;
import java.util.List;
import java.util.Arrays;

import com.zhonghe.system.domain.SysRobotMsg;
import com.zhonghe.system.service.IPushRobotMessageService;
import lombok.RequiredArgsConstructor;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.*;

import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.web.bind.annotation.*;
import org.springframework.validation.annotation.Validated;
import com.zhonghe.common.annotation.RepeatSubmit;
import com.zhonghe.common.annotation.Log;
import com.zhonghe.common.core.controller.BaseController;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import com.zhonghe.common.enums.BusinessType;
import com.zhonghe.common.utils.poi.ExcelUtil;
import com.zhonghe.system.domain.vo.SysRobotMsgVo;
import com.zhonghe.system.domain.bo.SysRobotMsgBo;
import com.zhonghe.system.service.ISysRobotMsgService;
import com.zhonghe.common.core.page.TableDataInfo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiOperation;

/**
 * 机器人消息
 *
 * <AUTHOR>
 * @date 2023-03-29
 */
@Validated
@Api(value = "机器人消息控制器", tags = {"机器人消息管理"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/com/zhonghe/system/robot-msg/")
public class SysRobotMsgController extends BaseController {

    private final ISysRobotMsgService iSysRobotMsgService;

    private final IPushRobotMessageService robotMessageService;

    @ApiOperation("发送机器人消息")
    @SaCheckPermission("system:robot-msg:send")
    @PostMapping("/send/{robotId}")
    public boolean send(@PathVariable Long robotId, @RequestBody SysRobotMsg template) throws UnsupportedEncodingException, NoSuchAlgorithmException, InvalidKeyException {
        return robotMessageService.sendMessage(Collections.singletonList(robotId), template);
    }

    /**
     * 查询机器人消息列表
     */
    @ApiOperation("查询机器人消息列表")
    @SaCheckPermission("csystem:robot-msg:list")
    @GetMapping("/list")
    public TableDataInfo<SysRobotMsgVo> list(SysRobotMsgBo bo, PageQuery pageQuery) {
        return iSysRobotMsgService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出机器人消息列表
     */
    @ApiOperation("导出机器人消息列表")
    @SaCheckPermission("system:robot-msg:export")
    @Log(title = "机器人消息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SysRobotMsgBo bo, HttpServletResponse response) {
        List<SysRobotMsgVo> list = iSysRobotMsgService.queryList(bo);
        ExcelUtil.exportExcel(list, "机器人消息", SysRobotMsgVo.class, response);
    }

    /**
     * 获取机器人消息详细信息
     */
    @ApiOperation("获取机器人消息详细信息")
    @SaCheckPermission("system:robot-msg:query")
    @GetMapping("/{id}")
    public R<SysRobotMsgVo> getInfo(@ApiParam("主键")
                                     @NotNull(message = "主键不能为空")
                                     @PathVariable("id") Long id) {
        return R.ok(iSysRobotMsgService.queryById(id));
    }

    /**
     * 新增机器人消息
     */
    @ApiOperation("新增机器人消息")
    @SaCheckPermission("system:robot-msg:add")
    @Log(title = "机器人消息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SysRobotMsgBo bo) {
        return toAjax(iSysRobotMsgService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改机器人消息
     */
    @ApiOperation("修改机器人消息")
    @SaCheckPermission("system:robot-msg:edit")
    @Log(title = "机器人消息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SysRobotMsgBo bo) {
        return toAjax(iSysRobotMsgService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除机器人消息
     */
    @ApiOperation("删除机器人消息")
    @SaCheckPermission("system:robot-msg:remove")
    @Log(title = "机器人消息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable Long[] ids) {
        return toAjax(iSysRobotMsgService.deleteWithValidByIds(Arrays.asList(ids), true) ? 1 : 0);
    }
}
