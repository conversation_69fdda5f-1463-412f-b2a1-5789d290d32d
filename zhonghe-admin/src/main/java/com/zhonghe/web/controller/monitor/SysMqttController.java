package com.zhonghe.web.controller.monitor;

import cn.dev33.satoken.annotation.SaCheckLogin;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.system.domain.dto.SysMqttLoginDto;
import com.zhonghe.system.service.ISysMqttService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.security.NoSuchAlgorithmException;

/**
 * @description: 获取mqtt登录权限
 * @author: cq
 * @date: 2023/3/27 16:08
 */

@Validated
@Api(value = "MQTT模块", tags = {"MQTT注册管理"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/com/zhonghe/system/mqtt")
public class SysMqttController {

    private final ISysMqttService mqttService;

    @ApiOperation("查询MQTT登录信息")
    //@SaCheckPermission("system:mqtt:index:info")
    @SaCheckLogin
    @GetMapping("/getInfo")
    public R<SysMqttLoginDto> getInfo() throws NoSuchAlgorithmException {
        return R.ok(mqttService.registerCustomerUser());
    }

}

