package com.zhonghe.web.controller.system;

import cn.dev33.satoken.annotation.SaCheckLogin;
import com.zhonghe.common.annotation.Log;
import com.zhonghe.common.core.controller.BaseController;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.enums.BusinessType;
import com.zhonghe.system.domain.SysUserNotice;
import com.zhonghe.system.service.ISysUserNoticeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @description: 用户消息中心
 * @author: cq
 * @date: 2023/3/24 9:53
 */

@Api(value = "用户消息", tags = {"消息公告"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/com/zhonghe/system/user-notice")
public class SysUserNoticeController extends BaseController {


    private final ISysUserNoticeService userNoticeService;


    @ApiOperation("查询消息")
    @GetMapping("/list")
    @SaCheckLogin
    public TableDataInfo<SysUserNotice> list(SysUserNotice notice, PageQuery pageQuery) {
        return userNoticeService.selectPageNoticeList(notice, pageQuery);
    }

    @ApiOperation("全部未读列表")
    @GetMapping("/all/unread")
    @SaCheckLogin
    public R<List<SysUserNotice>> allUnRead() {
        return R.ok(userNoticeService.selectAllUnRead());
    }

    @ApiOperation("查询未读数量")
    @GetMapping("/all/unread-count")
    @SaCheckLogin
    public R<Long> allUnReadCount() {
        return R.ok(userNoticeService.selectAllUnReadCount());
    }

    @ApiOperation("读消息")
    @PutMapping("/read")
    @Log(title = "用户消息", businessType = BusinessType.UPDATE)
    @SaCheckLogin
    public R<Void> read(@ApiParam("消息数组") @RequestBody List<Long> userNoticeIds) {
        userNoticeService.readNotices(userNoticeIds);
        return R.ok();
    }

    @ApiOperation("查询消息内容")
    @GetMapping("/detail/{userNoticeId}")
    @SaCheckLogin
    public R<SysUserNotice> detail(@ApiParam("MQTT推送的id") @PathVariable Long userNoticeId) {
        return R.ok(userNoticeService.selectByUserNoticeId(userNoticeId));
    }

}

