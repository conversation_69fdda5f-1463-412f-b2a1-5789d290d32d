package com.zhonghe.web.aspectj;

import com.zhonghe.common.core.domain.R;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

/**
 * @function 统一处理返回结果提示信息，add、delete、modify操作
 * @auther cq
 * @date 2023-03-09
 */

@Component
@Aspect
@Slf4j
public class AjaxResultAspect {

    private final String executorExpr = "execution(* com.zhonghe.web.controller..*.*(..))";

    /**
     * 处理返回结果
     *
     * @param joinPoint
     * @param r
     */
    @AfterReturning(value = executorExpr, returning = "r")
    public void processAjaxResult(JoinPoint joinPoint, Object r) {
        if (r instanceof R && ((R<?>) r).getCode() == R.SUCCESS) {
            switch (joinPoint.getSignature().getName()) {
                case "add":
                    ((R<?>) r).setMsg("新增成功！");
                    break;
                case "remove":
                    ((R<?>) r).setMsg("删除成功！");
                    break;
                case "edit":
                    ((R<?>) r).setMsg("修改成功！");
                    break;
                case "updateProfile":
                    ((R<?>) r).setMsg("保存成功！");
                    break;
            }
        }
    }
}
