<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<bindings version="2.1" xmlns="http://java.sun.com/xml/ns/jaxb">
  <!--

此文件是由 JavaTM Architecture for XML Binding (JAXB) 引用实现 v2.3.2 生成的
请访问 <a href="https://javaee.github.io/jaxb-v2/">https://javaee.github.io/jaxb-v2/</a> 
在重新编译源模式时, 对此文件的所有修改都将丢失。
生成时间: 2023.11.08 时间 04:56:51 PM CST 

  -->
  <bindings scd="x-schema::tns" xmlns:tns="http://zhonghe.com/web/soap/xsd">
    <schemaBindings map="false">
      <package name="com.zhonghe.web.soap.xsd"/>
    </schemaBindings>
    <bindings scd="tns:getCapabilities">
      <class ref="com.zhonghe.web.soap.xsd.GetCapabilities"/>
    </bindings>
    <bindings scd="tns:getCapabilitiesResponse">
      <class ref="com.zhonghe.web.soap.xsd.GetCapabilitiesResponse"/>
    </bindings>
    <bindings scd="~tns:ModelInfo">
      <class ref="com.zhonghe.web.soap.xsd.ModelInfo"/>
    </bindings>
    <bindings scd="tns:addModelData">
      <class ref="com.zhonghe.web.soap.xsd.AddModelData"/>
    </bindings>
    <bindings scd="~tns:InputParameter">
      <class ref="com.zhonghe.web.soap.xsd.InputParameter"/>
    </bindings>
    <bindings scd="tns:addModelDataResponse">
      <class ref="com.zhonghe.web.soap.xsd.AddModelDataResponse"/>
    </bindings>
    <bindings scd="~tns:Info">
      <class ref="com.zhonghe.web.soap.xsd.Info"/>
    </bindings>
    <bindings scd="tns:executeModel">
      <class ref="com.zhonghe.web.soap.xsd.ExecuteModel"/>
    </bindings>
    <bindings scd="tns:executeModelResponse">
      <class ref="com.zhonghe.web.soap.xsd.ExecuteModelResponse"/>
    </bindings>
    <bindings scd="~tns:GlobalScoreCalculateProcess">
      <class ref="com.zhonghe.web.soap.xsd.GlobalScoreCalculateProcess"/>
    </bindings>
    <bindings scd="tns:getDeployInfoResponse">
      <class ref="com.zhonghe.web.soap.xsd.GetDeployInfoResponse"/>
    </bindings>
    <bindings scd="~tns:DeployInfo">
      <class ref="com.zhonghe.web.soap.xsd.DeployInfo"/>
    </bindings>
    <bindings scd="tns:modelRegister">
      <class ref="com.zhonghe.web.soap.xsd.ModelRegister"/>
    </bindings>
    <bindings scd="tns:modelRegisterResponse">
      <class ref="com.zhonghe.web.soap.xsd.ModelRegisterResponse"/>
    </bindings>
    <bindings scd="tns:globalScoreCalculateProcessRequest">
      <class ref="com.zhonghe.web.soap.xsd.GlobalScoreCalculateProcessRequest"/>
    </bindings>
    <bindings scd="tns:GlobalScoreCalculateProcessResponse">
      <class ref="com.zhonghe.web.soap.xsd.GlobalScoreCalculateProcessResponse"/>
    </bindings>
    <bindings scd="~tns:Check">
      <class ref="com.zhonghe.web.soap.xsd.Check"/>
    </bindings>
    <bindings scd="~tns:OutputParameter">
      <class ref="com.zhonghe.web.soap.xsd.OutputParameter"/>
    </bindings>
  </bindings>
</bindings>

