--- # 监控中心配置
spring.boot.admin.client:
  # 增加客户端开关
  enabled: true
  url: http://localhost:29050/admin
  instance:
    service-host-type: IP
  username: zhonghe
  password: 123456

--- # xxl-job 配置
xxl.job:
  # 执行器开关
  enabled: false
  # 调度中心地址：如调度中心集群部署存在多个地址则用逗号分隔。
  admin-addresses: http://localhost:29060/xxl-job-admin
  # 执行器通讯TOKEN：非空时启用
  access-token: xxl-job
  executor:
    # 执行器AppName：执行器心跳注册分组依据；为空则关闭自动注册
    appname: xxl-job-executor
    # 执行器端口号 执行器从9101开始往后写
    port: 9103
    # 执行器注册：默认IP:PORT
    address:
    # 执行器IP：默认自动获取IP
    ip:
    # 执行器运行日志文件存储磁盘路径
    logpath: ./logs/xxl-job
    # 执行器日志文件保存天数：大于3生效
    logretentiondays: 7

--- # 数据源配置
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource

    # 动态数据源文档 https://www.kancloud.cn/tracy5546/dynamic-datasource/content
    dynamic:
      # 性能分析插件(有性能损耗 不建议生产环境使用)
      p6spy: true
      # 设置默认的数据源或者数据源组,默认值即为 master
      primary: master
      # 严格模式 匹配不到数据源则报错
      strict: true
      datasource:
        # 主库数据源
        master:
          driverClassName: org.postgresql.Driver
          # jdbc 所有参数配置参考 https://lionli.blog.csdn.net/article/details/122018562
          # rewriteBatchedStatements=true 批处理优化 大幅提升批量插入更新删除性能(对数据库有性能损耗 使用批量操作应考虑性能问题)
          url: **************************************************************************************************************************************************
          username: postgis
          password: postgis@123.0
          # 前置机数据库
        data:
          driverClassName: org.postgresql.Driver
          url: ****************************************************************************************************************************************
          username: postgres
          password: zhsz!@#2023SL

      #        kingbase:
      #          driverClassName: com.kingbase8.Driver
      #          url: ******************************************************************************************************************
      #          username: root
      #          password: root

      #        Mysql:
      #          driverClassName: com.mysql.cj.jdbc.Driver
      #          url: ************************************************************************************************************************************************************************************************
      #          username: root
      #          password: root

      #        sybase:
      #          driverClassName: net.sourceforge.jtds.jdbc.Driver
      #          url: ***************************************************
      #          username: aicscan
      #          password: dggsuser
      #          hikari:
      #            connection-test-query: SELECT 1
      #            idle-timeout: 60000
      #            minimum-idle: 5
      #            maximum-pool-size: 20


      druid:
        # 初始连接数
        initialSize: 5
        # 最小连接池数量
        minIdle: 10
        # 最大连接池数量
        maxActive: 20
        # 配置获取连接等待超时的时间
        maxWait: 60000
        # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
        timeBetweenEvictionRunsMillis: 60000
        # 配置一个连接在池中最小生存的时间，单位是毫秒
        minEvictableIdleTimeMillis: 300000
        # 配置一个连接在池中最大生存的时间，单位是毫秒
        maxEvictableIdleTimeMillis: 900000
        # 配置检测连接是否有效
        validationQuery: SELECT 1
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        # 注意这个值和druid原生不一致，默认启动了stat
        filters: stat

  task:
    scheduling:
      pool:
        size: 10
      thread-name-prefix: 数据同步任务
    execution:
      shutdown:
        #        线程关闭时是否等所有任务结束
        await-termination: false
        #        线程最大的等待时间，防止线程陷入循环关不掉
        await-termination-period: 10s

--- # druid 配置
spring.datasource.druid:
  webStatFilter:
    enabled: true
  statViewServlet:
    enabled: true
    # 设置白名单，不填则允许所有访问
    allow:
    url-pattern: /druid/*
    # 控制台管理用户名和密码
    login-username: zhonghe
    login-password: 123456
  filter:
    stat:
      enabled: true
      # 慢SQL记录
      log-slow-sql: true
      slow-sql-millis: 1000
      merge-sql: true
    wall:
      config:
        multi-statement-allow: true
--- #flowable配置
flowable:
  async-executor-activate: false #关闭定时任务JOB
  #  将databaseSchemaUpdate设置为true。当Flowable发现库与数据库表结构不一致时，会自动将数据库表结构升级至新版本。
  database-schema-update: true
  activity-font-name: 宋体
  label-font-name: 宋体
  annotation-font-name: 宋体
  # 关闭各个模块生成表，目前只使用工作流基础表
  idm:
    enabled: false
  cmmn:
    enabled: false
  dmn:
    enabled: false
  content:
    enabled: false
  form:
    enabled: false
  app:
    enabled: false
--- # redis 单机配置(单机与集群只能开启一个另一个需要注释掉)
spring:
  redis:
    # 地址
    #host: 127.0.0.1
    host: *************
    # 端口，默认为6379
    port: 29080
    #port: 6379
    # 数据库索引
    database: 5
    # 密码(如没有密码请注释掉)
    password: zhsz,./123
    # 连接超时时间
    timeout: 10s
    # 是否开启ssl
    ssl: false

#  rabbitmq:
#    virtual-host: /guest  #虚拟主机名称
#    stream:
#      host: localhost  #mq服务器ip,默认为localhost
#      port: 15672          #mq服务器port,默认为5672
#      username: guest     #mq服务器username,默认为guest
#      password: guest     #mq服务器password,默认为guest

redisson:
  # 线程池数量
  threads: 4
  # Netty线程池数量
  nettyThreads: 8
  # 单节点配置
  singleServerConfig:
    # 客户端名称
    clientName: ${zhonghe.name}
    # 最小空闲连接数
    connectionMinimumIdleSize: 8
    # 连接池大小
    connectionPoolSize: 32
    # 连接空闲超时，单位：毫秒
    idleConnectionTimeout: 10000
    # 命令等待超时，单位：毫秒
    timeout: 3000
    # 发布和订阅连接池大小
    subscriptionConnectionPoolSize: 50

--- # mail 邮件发送
mail:
  enabled: false
  host: smtp.163.com
  port: 465
  # 是否需要用户名密码验证
  auth: true
  # 发送方，遵循RFC-822标准
  from: <EMAIL>
  # 用户名（注意：如果使用foxmail邮箱，此处user为qq号）
  user: 379659253
  # 密码（注意，某些邮箱需要为SMTP服务单独设置密码，详情查看相关帮助）
  pass: LOVE.379659253
  # 使用 STARTTLS安全连接，STARTTLS是对纯文本通信协议的扩展。
  starttlsEnable: true
  # 使用SSL安全连接
  sslEnable: true
  # SMTP超时时长，单位毫秒，缺省值不超时
  timeout: 0
  # Socket连接超时值，单位毫秒，缺省值不超时
  connectionTimeout: 0

--- # sms 短信
sms:
  enabled: false
  # 阿里云 dysmsapi.aliyuncs.com
  # 腾讯云 sms.tencentcloudapi.com
  endpoint: "dysmsapi.aliyuncs.com"
  accessKeyId: xxxxxxx
  accessKeySecret: xxxxxx
  signName: 测试
  # 腾讯专用
  sdkAppId:
mqtt:
  enable: true
  username: admin
  password: zhonghe123@
  host: *************
  tcp: 29021
  port: 29022
  remote: *************
  salt: slat_foo123 #默认前置加盐
onenet:
  passId: C90-44002774
  token: d178f1161a254ca88a20f08796a1abc7
  url: https://ztn-data.gdgov.cn:8581/GatewayMsg/http/api/proxy/invoke
  #url: https://dg-ztn-data.gdgov.cn:443/GatewayMsg/http/api/proxy/invoke
  personId: 441900197810291326
  personName: 李玉贞
  fromIp: ***************
map:
  client_id: shilong_ywtg
  client_secret: NmVhMjA2NjdkYzczNDU3Mg
  url: http://ktd.dg:20002/gateway/client-point/oauth/token

# OCR服务配置
ocr:
  service:
    # OCR服务类型: baidu(百度OCR) 或 paddle(PaddleOCR)
    type: baidu
  # 百度OCR配置
  baidu:
    appId: 119463079
    apiKey: SSVeuJtvUTU9REsBKh8fi9Lq
    secretKey: sdCOcdZrYLluInxtVjmD0oYdJFwMyljU
  # PaddleOCR服务配置
  paddle:
    # PaddleOCR服务地址
    url: http://localhost:8080/api/ocr/table
    # API密钥（可选）
    api-key: test-api-key
