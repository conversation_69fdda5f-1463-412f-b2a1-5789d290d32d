--- # 监控中心配置
spring.boot.admin.client:
  # 增加客户端开关
  enabled: false
  url: http://localhost:29050/admin
  instance:
    service-host-type: IP
  username: zhonghe
  password: 123456

--- # xxl-job 配置
xxl.job:
  # 执行器开关
  enabled: false
  # 调度中心地址：如调度中心集群部署存在多个地址则用逗号分隔。
  admin-addresses: http://localhost:29060/xxl-job-admin
  # 执行器通讯TOKEN：非空时启用
  access-token: xxl-job
  executor:
    # 执行器AppName：执行器心跳注册分组依据；为空则关闭自动注册
    appname: xxl-job-executor
    # 执行器端口号 执行器从9101开始往后写
    port: 9103
    # 执行器注册：默认IP:PORT
    address:
    # 执行器IP：默认自动获取IP
    ip:
    # 执行器运行日志文件存储磁盘路径
    logpath: ./logs/xxl-job
    # 执行器日志文件保存天数：大于3生效
    logretentiondays: 7

--- # 数据源配置
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource

    # 动态数据源文档 https://www.kancloud.cn/tracy5546/dynamic-datasource/content
    dynamic:
      # 性能分析插件(有性能损耗 不建议生产环境使用)
      p6spy: true
      # 设置默认的数据源或者数据源组,默认值即为 master
      primary: master
      # 严格模式 匹配不到数据源则报错
      strict: true
      datasource:
        # 主库数据源
        master:
          driverClassName: org.postgresql.Driver
          # jdbc 所有参数配置参考 https://lionli.blog.csdn.net/article/details/122018562
          #         rewriteBatchedStatements=true 批处理优化 大幅提升批量插入更新删除性能(对数据库有性能损耗 使用批量操作应考虑性能问题)
          url: ***************************************************************************************************************************************************
          username: postgis
          password: ZHSZdatabase@123.0

        #          driverClassName: org.postgresql.Driver
        #          url: **************************************************************************************************************************************************
        #          username: postgis
        #          password: postgis@123.0
        data:
          driverClassName: org.postgresql.Driver
          url: *******************************************************************************************************************************************
          username: postgis
          password: postgis@123.0


      #        kingbase:
      #          driverClassName: com.kingbase8.Driver
      #          url: ******************************************************************************************************************
      #          username: root
      #          password: root

      #        Mysql:
      #          driverClassName: com.mysql.cj.jdbc.Driver
      #          url: ************************************************************************************************************************************************************************************************
      #          username: root
      #          password: root

      #        sybase:
      #          driverClassName: net.sourceforge.jtds.jdbc.Driver
      #          url: ***************************************************
      #          username: aicscan
      #          password: dggsuser
      #          hikari:
      #            connection-test-query: SELECT 1
      #            idle-timeout: 60000
      #            minimum-idle: 5
      #            maximum-pool-size: 20


      druid:
        # 初始连接数
        initialSize: 5
        # 最小连接池数量
        minIdle: 10
        # 最大连接池数量
        maxActive: 20
        # 配置获取连接等待超时的时间
        maxWait: 60000
        # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
        timeBetweenEvictionRunsMillis: 60000
        # 配置一个连接在池中最小生存的时间，单位是毫秒
        minEvictableIdleTimeMillis: 300000
        # 配置一个连接在池中最大生存的时间，单位是毫秒
        maxEvictableIdleTimeMillis: 900000
        # 配置检测连接是否有效
        validationQuery: SELECT 1
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        # 注意这个值和druid原生不一致，默认启动了stat
        filters: stat

  task:
    scheduling:
      pool:
        size: 10
      thread-name-prefix: 数据同步任务
    execution:
      shutdown:
        #        线程关闭时是否等所有任务结束
        await-termination: false
        #        线程最大的等待时间，防止线程陷入循环关不掉
        await-termination-period: 10s

--- # druid 配置
spring.datasource.druid:
  webStatFilter:
    enabled: true
  statViewServlet:
    enabled: true
    # 设置白名单，不填则允许所有访问
    allow:
    url-pattern: /druid/*
    # 控制台管理用户名和密码
    login-username: zhonghe
    login-password: 123456
  filter:
    stat:
      enabled: true
      # 慢SQL记录
      log-slow-sql: true
      slow-sql-millis: 1000
      merge-sql: true
    wall:
      config:
        multi-statement-allow: true

--- # redis 单机配置(单机与集群只能开启一个另一个需要注释掉)
spring:
  redis:
    # 地址
    #    host: 127.0.0.1
    host: **************
    # 端口，默认为6379
    port: 29080
    #    port: 6379
    # 数据库索引
    database: 12
    # 密码(如没有密码请注释掉)
    password: zhsz,./123
    # 连接超时时间
    timeout: 10s
    # 是否开启ssl
    ssl: false

#  rabbitmq:
#    virtual-host: /guest  #虚拟主机名称
#    stream:
#      host: localhost  #mq服务器ip,默认为localhost
#      port: 15672          #mq服务器port,默认为5672
#      username: guest     #mq服务器username,默认为guest
#      password: guest     #mq服务器password,默认为guest

redisson:
  # 线程池数量
  threads: 4
  # Netty线程池数量
  nettyThreads: 8
  # 单节点配置
  singleServerConfig:
    # 客户端名称
    clientName: ${zhonghe.name}
    # 最小空闲连接数
    connectionMinimumIdleSize: 8
    # 连接池大小
    connectionPoolSize: 32
    # 连接空闲超时，单位：毫秒
    idleConnectionTimeout: 10000
    # 命令等待超时，单位：毫秒
    timeout: 3000
    # 发布和订阅连接池大小
    subscriptionConnectionPoolSize: 50

--- # mail 邮件发送
mail:
  enabled: false
  host: smtp.163.com
  port: 465
  # 是否需要用户名密码验证
  auth: true
  # 发送方，遵循RFC-822标准
  from: <EMAIL>
  # 用户名（注意：如果使用foxmail邮箱，此处user为qq号）
  user: 379659253
  # 密码（注意，某些邮箱需要为SMTP服务单独设置密码，详情查看相关帮助）
  pass: LOVE.379659253
  # 使用 STARTTLS安全连接，STARTTLS是对纯文本通信协议的扩展。
  starttlsEnable: true
  # 使用SSL安全连接
  sslEnable: true
  # SMTP超时时长，单位毫秒，缺省值不超时
  timeout: 0
  # Socket连接超时值，单位毫秒，缺省值不超时
  connectionTimeout: 0

--- # sms 短信
sms:
  enabled: false
  # 阿里云 dysmsapi.aliyuncs.com
  # 腾讯云 sms.tencentcloudapi.com
  endpoint: "dysmsapi.aliyuncs.com"
  accessKeyId: xxxxxxx
  accessKeySecret: xxxxxx
  signName: 测试
  # 腾讯专用
  sdkAppId:
mqtt:
  enable: false
  username: admin
  password: zhonghe123@
  host: **************
  tcp: 29021
  port: 29022
  remote: **************
  salt: slat_foo123 #默认前置加盐
geoserver:
  #  url: http://**********:8100/geoserver
  #  username: admin
  #  password: geoserver
  #  workspace: water_ws
  #  #叶绿素的工作空间
  #  lvworkspace: lv_water_ws
  #  tifFolder: home/geoserver/tif/
  url: http://127.0.0.1:9080/geoserver
  username: admin
  password: geoserver
  workspace: water_ws
  tifFolder: E:\\target alpha tif\\
  saveFolder: E:\\target alpha tif\\
  lvworkspace: lv_water_ws
  moveFolder: E:\\target alpha tif\\
# OCR服务配置
ocr:
  service:
    # OCR服务类型: baidu(百度OCR) 或 paddle(PaddleOCR)
    type: paddle
  # 百度OCR配置
  baidu:
    appId: 119463079
    apiKey: SSVeuJtvUTU9REsBKh8fi9Lq
    secretKey: sdCOcdZrYLluInxtVjmD0oYdJFwMyljU
  # PaddleOCR服务配置
  paddle:
    # PaddleOCR服务地址
    url: http://localhost:9999/api/v1/structure/file
    # API密钥（可选）
    api-key:

# onlyoffice
office:
  # 本机地址，需要注意【这个ip需要与[api]中的ip能ping通】
  ip: http://*************:${server.port}
  # onlyoffice服务器的js文件，ip是服务器地址
  api: http://*************:9000/web-apps/apps/api/documents/api.js
  # 交互地址
  callbackUrl: /onlyoffice/callback?fileId=
  # 文件下载地址
  downloadUrl: /onlyoffice/downloadFile/


  lang: zh
  logo:
    image: https://www.risencn.com/r/cms/www/default/risen/public/img/header-logo1.png
    imageEmbedded: https://www.risencn.com/r/cms/www/default/risen/public/img/header-logo1.png
    url:
  # 回退地址
  goBack: ${office.ip}
  # 可查看类型
  viewedDocs: [ .pdf,.djvu,.xps,.oxps ]
  #  可编辑类型
  editedDocs: [ .docx,.xlsx,.csv,.pptx,.txt,.docxf ]
  # 可类型转换类型
  convertDocs: [ .docm,.dotx,.dotm,.dot,.doc,.odt,.fodt,.ott,.xlsm,.xlsb,.xltx,.xltm,.xlt,.xls,.ods,.fods,.ots,.pptm,.ppt,.ppsx,.ppsm,.pps,.potx,.potm,.pot,.odp,.fodp,.otp,.rtf,.mht,.html,.htm,.xml,.epub,.fb2 ]
