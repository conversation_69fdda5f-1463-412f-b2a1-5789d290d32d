import cn.dev33.satoken.secure.BCrypt;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zhonghe.ZhongHeApplication;
import com.zhonghe.common.core.domain.entity.SysUser;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.cop.bam.domain.TCopInformation;
import com.zhonghe.cop.bam.mapper.TCopInformationMapper;
import com.zhonghe.cop.bam.service.ITPerPerformanceService;
import com.zhonghe.cop.bam.service.TICopInformationService;
import com.zhonghe.cop.plan.service.ITPlaInfoService;
import com.zhonghe.cop.rep.domain.TRepReport;
import com.zhonghe.cop.rep.mapper.TRepReportMapper;
import com.zhonghe.system.mapper.SysUserMapper;
import lombok.extern.slf4j.Slf4j;
import net.postgis.jdbc.geometry.Point;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: lpg
 * @Date: 2023/12/18/10:24
 * @Description:
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ZhongHeApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Slf4j
public class PointTest {

    @Autowired
    private ITPlaInfoService plaInfoService;

    @Autowired
    private TCopInformationMapper tCopInformationMapper;

    @Autowired
    private TICopInformationService tiCopInformationService;

    @Autowired
    private SysUserMapper sysUserMapper;

    @Autowired
    private TRepReportMapper repReportMapper;

    @Autowired
    private ITPerPerformanceService perPerformanceService;

    @Test
    public void test() {
        plaInfoService.planTask();
    }

    @Test
    public void genUserPassword() {
        List<SysUser> sysUsers =
            sysUserMapper.selectList();
        for (SysUser sysUser : sysUsers) {
            if (StringUtils.isBlank(sysUser.getRemark())) {
                String password = sysUser.getPassword();
                String newPassword = BCrypt.hashpw(password);
                log.info("原密码：{}，新密码：{}", password, newPassword);
                sysUser.setRemark(password);
                sysUser.setPassword(newPassword);
                sysUserMapper.updateById(sysUser);
            }
        }
    }

    @Test
    public void deleteRepReport() {
        List<TRepReport> tRepReports = repReportMapper.selectList(Wrappers.<TRepReport>lambdaQuery()
            .isNotNull(TRepReport::getBatchNum));
        Map<String, List<TRepReport>> collect =
            tRepReports.stream().collect(Collectors.groupingBy(TRepReport::getBatchNum));

        //查询批次号相同的数据,保留最新的一条
        for (Map.Entry<String, List<TRepReport>> entry : collect.entrySet()) {
            List<TRepReport> value = entry.getValue();
            if (value.size() > 1) {
                for (int i = 0; i < value.size() - 1; i++) {
                    repReportMapper.deleteById(value.get(i).getRepReportId());
                }
            }
        }

    }

    @Test
    public void setGeo() {
        List<TCopInformation> tCopInformations = tCopInformationMapper.selectListDel();
        for (TCopInformation tCopInformation : tCopInformations) {
            Map<String, Object> addressInfo = tiCopInformationService.getAddressInfo(tCopInformation.getCopName());
            if (addressInfo != null) {
                tCopInformation.setCopAddress(addressInfo.get("address").toString());
                if (addressInfo.get("lng") != null && addressInfo.get("lat") != null) {
                    tCopInformation.setLon(addressInfo.get("lng").toString());
                    tCopInformation.setLat(addressInfo.get("lat").toString());

                    Point point = new Point(Double.parseDouble(addressInfo.get("lng").toString()),
                        Double.parseDouble(addressInfo.get("lat").toString()));
                    tCopInformation.setGeom(point);
                }

                tCopInformationMapper.updateById(tCopInformation);
            }
        }
    }

    @Test
    public void setDeptPer() {
        perPerformanceService.deptPerformanceTask();
    }
}
