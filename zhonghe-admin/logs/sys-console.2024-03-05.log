2024-03-05 09:59:29 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [BusinessTest], using SpringBootContextLoader
2024-03-05 09:59:29 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [BusinessTest]: no resource found for suffixes {-context.xml, Context.groovy}.
2024-03-05 09:59:30 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.boot.test.autoconfigure.webservices.client.MockWebServiceServerTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2024-03-05 09:59:30 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@eb6449b, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@7c351808, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@180e6ac4, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@42b64ab8, org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@7e985ce9, org.springframework.test.context.support.DirtiesContextTestExecutionListener@2a39fe6a, org.springframework.test.context.transaction.TransactionalTestExecutionListener@410ae9a3, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@319988b0, org.springframework.test.context.event.EventPublishingTestExecutionListener@d5ae57e, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@68759011, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@7e242b4d, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@305f031, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@592e843a, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@1d1f7216, org.springframework.boot.test.autoconfigure.webservices.client.MockWebServiceServerTestExecutionListener@423e4cbb]
2024-03-05 09:59:30 [main] INFO  BusinessTest - Starting BusinessTest using Java 11.0.18 on MacBook-Pro.local with PID 35530 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-admin)
2024-03-05 09:59:30 [main] INFO  BusinessTest - The following 1 profile is active: "prod"
2024-03-05 09:59:30 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2024-03-05 09:59:33 [main] INFO  org.redisson.Version - Redisson 3.17.4
2024-03-05 09:59:33 [redisson-netty-2-5] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2024-03-05 09:59:33 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2024-03-05 09:59:34 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2024-03-05 09:59:34 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2024-03-05 09:59:34 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2024-03-05 09:59:34 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2024-03-05 09:59:35 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2024-03-05 09:59:36 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-2,data} inited
2024-03-05 09:59:36 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [data] success
2024-03-05 09:59:36 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2024-03-05 09:59:36 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2024-03-05 09:59:37 [main] WARN  c.b.m.core.injector.AbstractMethod - [com.zhonghe.system.mapper.SysUserNoticeMapper.selectPage] Has been loaded by XML or SqlProvider or Mybatis's Annotation, so ignoring this injection for [class com.baomidou.mybatisplus.core.injector.methods.SelectPage]
2024-03-05 09:59:37 [main] INFO  c.f.common.spring.SpringContextUtil - ------SpringContextUtil setApplicationContext-------
2024-03-05 09:59:41 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2024-03-05 09:59:44 [main] INFO  BusinessTest - Started BusinessTest in 13.955 seconds (JVM running for 14.896)
2024-03-05 09:59:59 [registrationTask1] WARN  d.c.b.a.c.r.ApplicationRegistrator - Failed to register application as Application(name=ZhongHe-Vue-Plus, managementUrl=http://127.0.0.1:59218/actuator, healthUrl=http://127.0.0.1:59218/actuator/health, serviceUrl=http://127.0.0.1:59218/) at spring-boot-admin ([http://localhost:29050/admin/instances]): I/O error on POST request for "http://localhost:29050/admin/instances": Connect to localhost:29050 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused (Connection refused); nested exception is org.apache.http.conn.HttpHostConnectException: Connect to localhost:29050 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused (Connection refused). Further attempts are logged on DEBUG level
