2024-01-08 15:25:54 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [PointTest], using SpringBootContextLoader
2024-01-08 15:25:54 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [PointTest]: no resource found for suffixes {-context.xml, Context.groovy}.
2024-01-08 15:25:54 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.boot.test.autoconfigure.webservices.client.MockWebServiceServerTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2024-01-08 15:25:54 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@67389cb8, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@419a20a6, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@533377b, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@3383649e, org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@10fde30a, org.springframework.test.context.support.DirtiesContextTestExecutionListener@f27ea3, org.springframework.test.context.transaction.TransactionalTestExecutionListener@1ce61929, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@346939bf, org.springframework.test.context.event.EventPublishingTestExecutionListener@4bf3798b, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@58670130, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@74e47444, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@9bd0fa6, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@59d2103b, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@39dcf4b0, org.springframework.boot.test.autoconfigure.webservices.client.MockWebServiceServerTestExecutionListener@6e4de19b]
2024-01-08 15:25:54 [main] INFO  PointTest - Starting PointTest using Java 11.0.18 on MacBook-Pro.local with PID 52728 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-admin)
2024-01-08 15:25:54 [main] INFO  PointTest - The following 1 profile is active: "dev"
2024-01-08 15:25:54 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2024-01-08 15:25:56 [main] INFO  c.z.framework.config.JacksonConfig - 初始化 jackson 配置
2024-01-08 15:25:56 [main] INFO  c.z.framework.config.RedisConfig - 初始化 redis 配置
2024-01-08 15:25:56 [main] INFO  org.redisson.Version - Redisson 3.17.4
2024-01-08 15:25:56 [redisson-netty-2-3] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2024-01-08 15:25:57 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2024-01-08 15:25:57 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2024-01-08 15:25:57 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2024-01-08 15:25:57 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2024-01-08 15:25:57 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2024-01-08 15:25:58 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2024-01-08 15:25:58 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2024-01-08 15:25:59 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-2,data} inited
2024-01-08 15:25:59 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [data] success
2024-01-08 15:25:59 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2024-01-08 15:25:59 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2024-01-08 15:26:00 [main] INFO  c.f.common.spring.SpringContextUtil - ------SpringContextUtil setApplicationContext-------
2024-01-08 15:26:04 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2024-01-08 15:26:06 [main] INFO  PointTest - Started PointTest in 12.259 seconds (JVM running for 12.954)
2024-01-08 15:26:06 [main] INFO  c.z.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => 下
2024-01-08 15:26:06 [main] INFO  c.z.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => xx
2024-01-08 15:26:06 [main] INFO  c.z.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => xxx
2024-01-08 15:26:06 [main] INFO  c.z.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qcloud
2024-01-08 15:26:07 [main] INFO  c.z.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => image
2024-01-08 15:26:07 [main] INFO  c.z.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qiniu
2024-01-08 15:26:07 [main] INFO  c.z.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => tttt
2024-01-08 15:26:07 [main] INFO  c.z.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => tttttt
2024-01-08 15:26:07 [main] INFO  c.z.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => ttttttt
2024-01-08 15:26:07 [main] INFO  c.z.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => yy
2024-01-08 15:26:07 [main] INFO  c.z.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => minio
2024-01-08 15:26:07 [main] INFO  com.zhonghe.oss.factory.OssFactory - 初始化OSS工厂
2024-01-08 15:26:07 [main] INFO  c.z.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2024-01-08 15:26:07 [main] INFO  c.z.s.runner.SystemApplicationRunner - 加载参数缓存数据成功
2024-01-08 15:26:17 [main] INFO  c.z.s.runner.SystemApplicationRunner - 加载字典缓存数据成功
2024-01-08 15:26:17 [main] INFO  c.z.s.runner.SystemApplicationRunner - 链接mqtt服务器成功
2024-01-08 15:26:17 [main] INFO  c.z.s.runner.SystemApplicationRunner - 加载OSS权限配置成功
2024-01-08 15:26:17 [main] INFO  c.z.s.runner.SystemApplicationRunner - 加载Excel导入功能
2024-01-08 15:26:18 [SpringApplicationShutdownHook] INFO  c.z.f.manager.ShutdownManager - ====关闭后台任务任务线程池====
2024-01-08 15:28:18 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2024-01-08 15:28:18 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-2} closing ...
2024-01-08 15:28:18 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-2} closed
2024-01-08 15:28:18 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2024-01-08 15:28:18 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2024-01-08 15:28:18 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
