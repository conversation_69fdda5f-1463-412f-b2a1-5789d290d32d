2024-03-01 09:10:47 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [BusinessTest], using SpringBootContextLoader
2024-03-01 09:10:48 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [BusinessTest]: no resource found for suffixes {-context.xml, Context.groovy}.
2024-03-01 09:10:48 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.boot.test.autoconfigure.webservices.client.MockWebServiceServerTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2024-03-01 09:10:48 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@5049d8b2, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@6d0b5baf, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@631e06ab, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@2a3591c5, org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@34a75079, org.springframework.test.context.support.DirtiesContextTestExecutionListener@346a361, org.springframework.test.context.transaction.TransactionalTestExecutionListener@107ed6fc, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@1643d68f, org.springframework.test.context.event.EventPublishingTestExecutionListener@186978a6, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@2e029d61, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@482d776b, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@4052274f, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@132ddbab, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@297ea53a, org.springframework.boot.test.autoconfigure.webservices.client.MockWebServiceServerTestExecutionListener@acb0951]
2024-03-01 09:10:48 [main] INFO  BusinessTest - Starting BusinessTest using Java 11.0.18 on MacBook-Pro.local with PID 42058 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-admin)
2024-03-01 09:10:48 [main] INFO  BusinessTest - The following 1 profile is active: "dev"
2024-03-01 09:10:48 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2024-03-01 09:10:50 [main] INFO  c.z.framework.config.JacksonConfig - 初始化 jackson 配置
2024-03-01 09:10:50 [main] INFO  c.z.framework.config.RedisConfig - 初始化 redis 配置
2024-03-01 09:10:50 [main] INFO  org.redisson.Version - Redisson 3.17.4
2024-03-01 09:10:51 [redisson-netty-2-6] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2024-03-01 09:10:51 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2024-03-01 09:10:52 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2024-03-01 09:10:52 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2024-03-01 09:10:52 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2024-03-01 09:10:52 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2024-03-01 09:10:54 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2024-03-01 09:10:54 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2024-03-01 09:10:54 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2024-03-01 09:10:56 [main] INFO  c.f.common.spring.SpringContextUtil - ------SpringContextUtil setApplicationContext-------
2024-03-01 09:10:59 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2024-03-01 09:11:02 [main] INFO  BusinessTest - Started BusinessTest in 14.274 seconds (JVM running for 15.184)
2024-03-01 09:11:02 [main] INFO  c.z.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => image
2024-03-01 09:11:02 [main] INFO  c.z.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qcloud
2024-03-01 09:11:02 [main] INFO  c.z.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qiniu
2024-03-01 09:11:03 [main] INFO  c.z.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => minio
2024-03-01 09:11:03 [main] INFO  com.zhonghe.oss.factory.OssFactory - 初始化OSS工厂
2024-03-01 09:11:03 [main] INFO  c.z.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2024-03-01 09:11:03 [main] INFO  c.z.s.runner.SystemApplicationRunner - 加载参数缓存数据成功
2024-03-01 09:11:17 [main] INFO  c.z.s.runner.SystemApplicationRunner - 加载字典缓存数据成功
2024-03-01 09:11:17 [main] INFO  c.z.s.runner.SystemApplicationRunner - 链接mqtt服务器成功
2024-03-01 09:11:17 [main] INFO  c.z.s.runner.SystemApplicationRunner - 加载OSS权限配置成功
2024-03-01 09:11:17 [main] INFO  c.z.s.runner.SystemApplicationRunner - 加载Excel导入功能
2024-03-01 09:12:58 [SpringApplicationShutdownHook] INFO  c.z.f.manager.ShutdownManager - ====关闭后台任务任务线程池====
2024-03-01 09:13:44 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [BusinessTest], using SpringBootContextLoader
2024-03-01 09:13:44 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [BusinessTest]: no resource found for suffixes {-context.xml, Context.groovy}.
2024-03-01 09:13:44 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.boot.test.autoconfigure.webservices.client.MockWebServiceServerTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2024-03-01 09:13:44 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@18f20260, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@4ae33a11, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@7a48e6e2, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@b40bb6e, org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@3a94964, org.springframework.test.context.support.DirtiesContextTestExecutionListener@5049d8b2, org.springframework.test.context.transaction.TransactionalTestExecutionListener@6d0b5baf, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@631e06ab, org.springframework.test.context.event.EventPublishingTestExecutionListener@2a3591c5, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@34a75079, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@346a361, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@107ed6fc, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@1643d68f, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@186978a6, org.springframework.boot.test.autoconfigure.webservices.client.MockWebServiceServerTestExecutionListener@2e029d61]
2024-03-01 09:13:44 [main] INFO  BusinessTest - Starting BusinessTest using Java 11.0.18 on MacBook-Pro.local with PID 42179 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-admin)
2024-03-01 09:13:44 [main] INFO  BusinessTest - The following 1 profile is active: "dev"
2024-03-01 09:13:44 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2024-03-01 09:13:46 [main] INFO  c.z.framework.config.JacksonConfig - 初始化 jackson 配置
2024-03-01 09:13:46 [main] INFO  c.z.framework.config.RedisConfig - 初始化 redis 配置
2024-03-01 09:13:46 [main] INFO  org.redisson.Version - Redisson 3.17.4
2024-03-01 09:13:47 [redisson-netty-2-5] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2024-03-01 09:13:47 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2024-03-01 09:13:48 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2024-03-01 09:13:48 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2024-03-01 09:13:48 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2024-03-01 09:13:48 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2024-03-01 09:13:50 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2024-03-01 09:13:50 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2024-03-01 09:13:50 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2024-03-01 09:13:52 [main] INFO  c.f.common.spring.SpringContextUtil - ------SpringContextUtil setApplicationContext-------
2024-03-01 09:13:55 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2024-03-01 09:13:58 [main] INFO  BusinessTest - Started BusinessTest in 14.228 seconds (JVM running for 15.128)
2024-03-01 09:13:58 [main] INFO  c.z.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => image
2024-03-01 09:13:58 [main] INFO  c.z.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qcloud
2024-03-01 09:13:58 [main] INFO  c.z.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qiniu
2024-03-01 09:13:59 [main] INFO  c.z.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => minio
2024-03-01 09:13:59 [main] INFO  com.zhonghe.oss.factory.OssFactory - 初始化OSS工厂
2024-03-01 09:13:59 [main] INFO  c.z.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2024-03-01 09:13:59 [main] INFO  c.z.s.runner.SystemApplicationRunner - 加载参数缓存数据成功
2024-03-01 09:14:13 [main] INFO  c.z.s.runner.SystemApplicationRunner - 加载字典缓存数据成功
2024-03-01 09:14:13 [main] INFO  c.z.s.runner.SystemApplicationRunner - 链接mqtt服务器成功
2024-03-01 09:14:13 [main] INFO  c.z.s.runner.SystemApplicationRunner - 加载OSS权限配置成功
2024-03-01 09:14:13 [main] INFO  c.z.s.runner.SystemApplicationRunner - 加载Excel导入功能
2024-03-01 09:14:28 [SpringApplicationShutdownHook] INFO  c.z.f.manager.ShutdownManager - ====关闭后台任务任务线程池====
2024-03-01 09:15:14 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [BusinessTest], using SpringBootContextLoader
2024-03-01 09:15:14 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [BusinessTest]: no resource found for suffixes {-context.xml, Context.groovy}.
2024-03-01 09:15:14 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.boot.test.autoconfigure.webservices.client.MockWebServiceServerTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2024-03-01 09:15:14 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@6722db6e, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@18f20260, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@4ae33a11, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@7a48e6e2, org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@b40bb6e, org.springframework.test.context.support.DirtiesContextTestExecutionListener@3a94964, org.springframework.test.context.transaction.TransactionalTestExecutionListener@5049d8b2, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@6d0b5baf, org.springframework.test.context.event.EventPublishingTestExecutionListener@631e06ab, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@2a3591c5, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@34a75079, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@346a361, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@107ed6fc, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@1643d68f, org.springframework.boot.test.autoconfigure.webservices.client.MockWebServiceServerTestExecutionListener@186978a6]
2024-03-01 09:15:15 [main] INFO  BusinessTest - Starting BusinessTest using Java 11.0.18 on MacBook-Pro.local with PID 42246 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-admin)
2024-03-01 09:15:15 [main] INFO  BusinessTest - The following 1 profile is active: "prod"
2024-03-01 09:15:15 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2024-03-01 09:15:17 [main] INFO  org.redisson.Version - Redisson 3.17.4
2024-03-01 09:15:18 [redisson-netty-2-5] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2024-03-01 09:15:18 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2024-03-01 09:15:18 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2024-03-01 09:15:18 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2024-03-01 09:15:18 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2024-03-01 09:15:18 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2024-03-01 09:15:19 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2024-03-01 09:15:20 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-2,data} inited
2024-03-01 09:15:20 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [data] success
2024-03-01 09:15:20 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2024-03-01 09:15:20 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2024-03-01 09:15:21 [main] INFO  c.f.common.spring.SpringContextUtil - ------SpringContextUtil setApplicationContext-------
2024-03-01 09:15:25 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2024-03-01 09:15:27 [main] INFO  BusinessTest - Started BusinessTest in 12.996 seconds (JVM running for 14.013)
2024-03-01 09:16:09 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [BusinessTest], using SpringBootContextLoader
2024-03-01 09:16:09 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [BusinessTest]: no resource found for suffixes {-context.xml, Context.groovy}.
2024-03-01 09:16:09 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.boot.test.autoconfigure.webservices.client.MockWebServiceServerTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2024-03-01 09:16:09 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@18f20260, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@4ae33a11, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@7a48e6e2, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@b40bb6e, org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@3a94964, org.springframework.test.context.support.DirtiesContextTestExecutionListener@5049d8b2, org.springframework.test.context.transaction.TransactionalTestExecutionListener@6d0b5baf, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@631e06ab, org.springframework.test.context.event.EventPublishingTestExecutionListener@2a3591c5, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@34a75079, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@346a361, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@107ed6fc, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@1643d68f, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@186978a6, org.springframework.boot.test.autoconfigure.webservices.client.MockWebServiceServerTestExecutionListener@2e029d61]
2024-03-01 09:16:09 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2024-03-01 09:16:09 [main] INFO  BusinessTest - Starting BusinessTest using Java 11.0.18 on MacBook-Pro.local with PID 42311 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-admin)
2024-03-01 09:16:09 [main] INFO  BusinessTest - The following 1 profile is active: "prod"
2024-03-01 09:16:11 [main] INFO  org.redisson.Version - Redisson 3.17.4
2024-03-01 09:16:12 [redisson-netty-2-5] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2024-03-01 09:16:12 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2024-03-01 09:16:13 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2024-03-01 09:16:13 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2024-03-01 09:16:13 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2024-03-01 09:16:13 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2024-03-01 09:16:14 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2024-03-01 09:16:14 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-2,data} inited
2024-03-01 09:16:14 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [data] success
2024-03-01 09:16:14 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2024-03-01 09:16:14 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2024-03-01 09:16:16 [main] INFO  c.f.common.spring.SpringContextUtil - ------SpringContextUtil setApplicationContext-------
2024-03-01 09:16:19 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2024-03-01 09:16:22 [main] INFO  BusinessTest - Started BusinessTest in 12.817 seconds (JVM running for 13.806)
2024-03-01 09:17:13 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [BusinessTest], using SpringBootContextLoader
2024-03-01 09:17:13 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [BusinessTest]: no resource found for suffixes {-context.xml, Context.groovy}.
2024-03-01 09:17:13 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.boot.test.autoconfigure.webservices.client.MockWebServiceServerTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2024-03-01 09:17:13 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@18f20260, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@4ae33a11, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@7a48e6e2, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@b40bb6e, org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@3a94964, org.springframework.test.context.support.DirtiesContextTestExecutionListener@5049d8b2, org.springframework.test.context.transaction.TransactionalTestExecutionListener@6d0b5baf, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@631e06ab, org.springframework.test.context.event.EventPublishingTestExecutionListener@2a3591c5, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@34a75079, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@346a361, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@107ed6fc, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@1643d68f, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@186978a6, org.springframework.boot.test.autoconfigure.webservices.client.MockWebServiceServerTestExecutionListener@2e029d61]
2024-03-01 09:17:13 [main] INFO  BusinessTest - Starting BusinessTest using Java 11.0.18 on MacBook-Pro.local with PID 42341 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-admin)
2024-03-01 09:17:13 [main] INFO  BusinessTest - The following 1 profile is active: "prod"
2024-03-01 09:17:13 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2024-03-01 09:17:16 [main] INFO  org.redisson.Version - Redisson 3.17.4
2024-03-01 09:17:16 [redisson-netty-2-6] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2024-03-01 09:17:16 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2024-03-01 09:17:17 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2024-03-01 09:17:17 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2024-03-01 09:17:17 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2024-03-01 09:17:17 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2024-03-01 09:17:18 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2024-03-01 09:17:18 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-2,data} inited
2024-03-01 09:17:18 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [data] success
2024-03-01 09:17:18 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2024-03-01 09:17:18 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2024-03-01 09:17:20 [main] INFO  c.f.common.spring.SpringContextUtil - ------SpringContextUtil setApplicationContext-------
2024-03-01 09:17:24 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2024-03-01 09:17:26 [main] INFO  BusinessTest - Started BusinessTest in 13.186 seconds (JVM running for 14.17)
2024-03-01 09:20:29 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2024-03-01 09:20:29 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-2} closing ...
2024-03-01 09:20:29 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-2} closed
2024-03-01 09:20:29 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2024-03-01 09:20:29 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2024-03-01 09:20:29 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2024-03-01 09:23:19 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [BusinessTest], using SpringBootContextLoader
2024-03-01 09:23:19 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [BusinessTest]: no resource found for suffixes {-context.xml, Context.groovy}.
2024-03-01 09:23:19 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.boot.test.autoconfigure.webservices.client.MockWebServiceServerTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2024-03-01 09:23:19 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@18f20260, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@4ae33a11, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@7a48e6e2, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@b40bb6e, org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@3a94964, org.springframework.test.context.support.DirtiesContextTestExecutionListener@5049d8b2, org.springframework.test.context.transaction.TransactionalTestExecutionListener@6d0b5baf, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@631e06ab, org.springframework.test.context.event.EventPublishingTestExecutionListener@2a3591c5, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@34a75079, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@346a361, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@107ed6fc, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@1643d68f, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@186978a6, org.springframework.boot.test.autoconfigure.webservices.client.MockWebServiceServerTestExecutionListener@2e029d61]
2024-03-01 09:23:19 [main] INFO  BusinessTest - Starting BusinessTest using Java 11.0.18 on MacBook-Pro.local with PID 42732 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-admin)
2024-03-01 09:23:19 [main] INFO  BusinessTest - The following 1 profile is active: "prod"
2024-03-01 09:23:19 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2024-03-01 09:23:22 [main] INFO  org.redisson.Version - Redisson 3.17.4
2024-03-01 09:23:22 [redisson-netty-2-5] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2024-03-01 09:23:22 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2024-03-01 09:23:23 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2024-03-01 09:23:23 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2024-03-01 09:23:23 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2024-03-01 09:23:23 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2024-03-01 09:23:24 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2024-03-01 09:23:24 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-2,data} inited
2024-03-01 09:23:24 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [data] success
2024-03-01 09:23:24 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2024-03-01 09:23:24 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2024-03-01 09:23:26 [main] INFO  c.f.common.spring.SpringContextUtil - ------SpringContextUtil setApplicationContext-------
2024-03-01 09:23:29 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2024-03-01 09:23:33 [main] INFO  BusinessTest - Started BusinessTest in 13.562 seconds (JVM running for 14.298)
2024-03-01 09:26:46 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [BusinessTest], using SpringBootContextLoader
2024-03-01 09:26:46 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [BusinessTest]: no resource found for suffixes {-context.xml, Context.groovy}.
2024-03-01 09:26:46 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.boot.test.autoconfigure.webservices.client.MockWebServiceServerTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2024-03-01 09:26:46 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@18f20260, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@4ae33a11, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@7a48e6e2, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@b40bb6e, org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@3a94964, org.springframework.test.context.support.DirtiesContextTestExecutionListener@5049d8b2, org.springframework.test.context.transaction.TransactionalTestExecutionListener@6d0b5baf, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@631e06ab, org.springframework.test.context.event.EventPublishingTestExecutionListener@2a3591c5, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@34a75079, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@346a361, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@107ed6fc, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@1643d68f, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@186978a6, org.springframework.boot.test.autoconfigure.webservices.client.MockWebServiceServerTestExecutionListener@2e029d61]
2024-03-01 09:26:46 [main] INFO  BusinessTest - Starting BusinessTest using Java 11.0.18 on MacBook-Pro.local with PID 42853 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-admin)
2024-03-01 09:26:46 [main] INFO  BusinessTest - The following 1 profile is active: "prod"
2024-03-01 09:26:46 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2024-03-01 09:26:49 [main] INFO  org.redisson.Version - Redisson 3.17.4
2024-03-01 09:26:49 [redisson-netty-2-5] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2024-03-01 09:26:49 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2024-03-01 09:26:50 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2024-03-01 09:26:50 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2024-03-01 09:26:50 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2024-03-01 09:26:50 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2024-03-01 09:26:51 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2024-03-01 09:26:52 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-2,data} inited
2024-03-01 09:26:52 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [data] success
2024-03-01 09:26:52 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2024-03-01 09:26:52 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2024-03-01 09:26:53 [main] INFO  c.f.common.spring.SpringContextUtil - ------SpringContextUtil setApplicationContext-------
2024-03-01 09:26:57 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2024-03-01 09:26:59 [main] INFO  BusinessTest - Started BusinessTest in 13.042 seconds (JVM running for 13.885)
2024-03-01 09:29:25 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2024-03-01 09:29:25 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-2} closing ...
2024-03-01 09:29:25 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-2} closed
2024-03-01 09:29:25 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2024-03-01 09:29:25 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2024-03-01 09:29:25 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2024-03-01 11:44:19 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [BusinessTest], using SpringBootContextLoader
2024-03-01 11:44:19 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [BusinessTest]: no resource found for suffixes {-context.xml, Context.groovy}.
2024-03-01 11:44:19 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.boot.test.autoconfigure.webservices.client.MockWebServiceServerTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2024-03-01 11:44:19 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@4ba534b0, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@6f0ca692, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@2c104774, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@2cb3d0f7, org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@4e517165, org.springframework.test.context.support.DirtiesContextTestExecutionListener@44e3760b, org.springframework.test.context.transaction.TransactionalTestExecutionListener@6a66a204, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@5860f3d7, org.springframework.test.context.event.EventPublishingTestExecutionListener@1d7f7be7, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@42f3156d, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@1ddae9b5, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@427b5f92, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@24bdb479, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@7e3f95fe, org.springframework.boot.test.autoconfigure.webservices.client.MockWebServiceServerTestExecutionListener@34625ccd]
2024-03-01 11:44:19 [main] INFO  BusinessTest - Starting BusinessTest using Java 11.0.18 on MacBook-Pro.local with PID 48088 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-admin)
2024-03-01 11:44:19 [main] INFO  BusinessTest - The following 1 profile is active: "prod"
2024-03-01 11:44:19 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2024-03-01 11:44:21 [main] INFO  org.redisson.Version - Redisson 3.17.4
2024-03-01 11:44:22 [redisson-netty-2-6] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2024-03-01 11:44:22 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2024-03-01 11:44:22 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2024-03-01 11:44:22 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2024-03-01 11:44:22 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2024-03-01 11:44:22 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2024-03-01 11:44:23 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2024-03-01 11:44:24 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-2,data} inited
2024-03-01 11:44:24 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [data] success
2024-03-01 11:44:24 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2024-03-01 11:44:24 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2024-03-01 11:44:25 [main] INFO  c.f.common.spring.SpringContextUtil - ------SpringContextUtil setApplicationContext-------
2024-03-01 11:44:28 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2024-03-01 11:46:18 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [BusinessTest], using SpringBootContextLoader
2024-03-01 11:46:18 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [BusinessTest]: no resource found for suffixes {-context.xml, Context.groovy}.
2024-03-01 11:46:18 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.boot.test.autoconfigure.webservices.client.MockWebServiceServerTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2024-03-01 11:46:18 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@4ba534b0, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@6f0ca692, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@2c104774, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@2cb3d0f7, org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@4e517165, org.springframework.test.context.support.DirtiesContextTestExecutionListener@44e3760b, org.springframework.test.context.transaction.TransactionalTestExecutionListener@6a66a204, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@5860f3d7, org.springframework.test.context.event.EventPublishingTestExecutionListener@1d7f7be7, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@42f3156d, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@1ddae9b5, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@427b5f92, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@24bdb479, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@7e3f95fe, org.springframework.boot.test.autoconfigure.webservices.client.MockWebServiceServerTestExecutionListener@34625ccd]
2024-03-01 11:46:19 [main] INFO  BusinessTest - Starting BusinessTest using Java 11.0.18 on MacBook-Pro.local with PID 48169 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-admin)
2024-03-01 11:46:19 [main] INFO  BusinessTest - The following 1 profile is active: "prod"
2024-03-01 11:46:19 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2024-03-01 11:46:21 [main] INFO  org.redisson.Version - Redisson 3.17.4
2024-03-01 11:46:21 [redisson-netty-2-6] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2024-03-01 11:46:21 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2024-03-01 11:46:22 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2024-03-01 11:46:22 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2024-03-01 11:46:22 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2024-03-01 11:46:22 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2024-03-01 11:46:23 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2024-03-01 11:46:23 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-2,data} inited
2024-03-01 11:46:23 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [data] success
2024-03-01 11:46:23 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2024-03-01 11:46:23 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2024-03-01 11:46:25 [main] INFO  c.f.common.spring.SpringContextUtil - ------SpringContextUtil setApplicationContext-------
2024-03-01 11:46:28 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2024-03-01 11:46:31 [main] INFO  BusinessTest - Started BusinessTest in 12.493 seconds (JVM running for 13.18)
2024-03-01 11:48:27 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [BusinessTest], using SpringBootContextLoader
2024-03-01 11:48:27 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [BusinessTest]: no resource found for suffixes {-context.xml, Context.groovy}.
2024-03-01 11:48:27 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.boot.test.autoconfigure.webservices.client.MockWebServiceServerTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2024-03-01 11:48:27 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@18f20260, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@4ae33a11, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@7a48e6e2, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@b40bb6e, org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@3a94964, org.springframework.test.context.support.DirtiesContextTestExecutionListener@5049d8b2, org.springframework.test.context.transaction.TransactionalTestExecutionListener@6d0b5baf, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@631e06ab, org.springframework.test.context.event.EventPublishingTestExecutionListener@2a3591c5, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@34a75079, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@346a361, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@107ed6fc, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@1643d68f, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@186978a6, org.springframework.boot.test.autoconfigure.webservices.client.MockWebServiceServerTestExecutionListener@2e029d61]
2024-03-01 11:48:28 [main] INFO  BusinessTest - Starting BusinessTest using Java 11.0.18 on MacBook-Pro.local with PID 48254 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-admin)
2024-03-01 11:48:28 [main] INFO  BusinessTest - The following 1 profile is active: "prod"
2024-03-01 11:48:28 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2024-03-01 11:48:30 [main] INFO  org.redisson.Version - Redisson 3.17.4
2024-03-01 11:48:30 [redisson-netty-2-6] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2024-03-01 11:48:31 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2024-03-01 11:48:31 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2024-03-01 11:48:31 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2024-03-01 11:48:31 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2024-03-01 11:48:31 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2024-03-01 11:48:33 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2024-03-01 11:48:33 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-2,data} inited
2024-03-01 11:48:33 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [data] success
2024-03-01 11:48:33 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2024-03-01 11:48:33 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2024-03-01 11:48:35 [main] INFO  c.f.common.spring.SpringContextUtil - ------SpringContextUtil setApplicationContext-------
2024-03-01 11:48:38 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2024-03-01 11:48:41 [main] INFO  BusinessTest - Started BusinessTest in 13.361 seconds (JVM running for 14.22)
2024-03-01 11:49:38 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [BusinessTest], using SpringBootContextLoader
2024-03-01 11:49:38 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [BusinessTest]: no resource found for suffixes {-context.xml, Context.groovy}.
2024-03-01 11:49:38 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.boot.test.autoconfigure.webservices.client.MockWebServiceServerTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2024-03-01 11:49:38 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@18f20260, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@4ae33a11, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@7a48e6e2, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@b40bb6e, org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@3a94964, org.springframework.test.context.support.DirtiesContextTestExecutionListener@5049d8b2, org.springframework.test.context.transaction.TransactionalTestExecutionListener@6d0b5baf, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@631e06ab, org.springframework.test.context.event.EventPublishingTestExecutionListener@2a3591c5, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@34a75079, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@346a361, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@107ed6fc, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@1643d68f, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@186978a6, org.springframework.boot.test.autoconfigure.webservices.client.MockWebServiceServerTestExecutionListener@2e029d61]
2024-03-01 11:49:38 [main] INFO  BusinessTest - Starting BusinessTest using Java 11.0.18 on MacBook-Pro.local with PID 48294 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-admin)
2024-03-01 11:49:38 [main] INFO  BusinessTest - The following 1 profile is active: "prod"
2024-03-01 11:49:38 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2024-03-01 11:49:40 [main] INFO  org.redisson.Version - Redisson 3.17.4
2024-03-01 11:49:41 [redisson-netty-2-5] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2024-03-01 11:49:41 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2024-03-01 11:49:42 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2024-03-01 11:49:42 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2024-03-01 11:49:42 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2024-03-01 11:49:42 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2024-03-01 11:49:43 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2024-03-01 11:49:43 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-2,data} inited
2024-03-01 11:49:43 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [data] success
2024-03-01 11:49:43 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2024-03-01 11:49:43 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2024-03-01 11:49:45 [main] INFO  c.f.common.spring.SpringContextUtil - ------SpringContextUtil setApplicationContext-------
2024-03-01 11:49:48 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2024-03-01 11:49:50 [main] INFO  BusinessTest - Started BusinessTest in 12.061 seconds (JVM running for 12.802)
2024-03-01 11:52:50 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2024-03-01 11:52:50 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-2} closing ...
2024-03-01 11:52:50 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-2} closed
2024-03-01 11:52:50 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2024-03-01 11:52:50 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2024-03-01 11:52:50 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2024-03-01 17:05:15 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [BusinessTest], using SpringBootContextLoader
2024-03-01 17:05:15 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [BusinessTest]: no resource found for suffixes {-context.xml, Context.groovy}.
2024-03-01 17:05:15 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.boot.test.autoconfigure.webservices.client.MockWebServiceServerTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2024-03-01 17:05:15 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@18f20260, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@4ae33a11, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@7a48e6e2, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@b40bb6e, org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@3a94964, org.springframework.test.context.support.DirtiesContextTestExecutionListener@5049d8b2, org.springframework.test.context.transaction.TransactionalTestExecutionListener@6d0b5baf, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@631e06ab, org.springframework.test.context.event.EventPublishingTestExecutionListener@2a3591c5, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@34a75079, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@346a361, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@107ed6fc, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@1643d68f, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@186978a6, org.springframework.boot.test.autoconfigure.webservices.client.MockWebServiceServerTestExecutionListener@2e029d61]
2024-03-01 17:05:17 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2024-03-01 17:05:17 [main] INFO  BusinessTest - Starting BusinessTest using Java 11.0.18 on MacBook-Pro.local with PID 3049 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-admin)
2024-03-01 17:05:17 [main] INFO  BusinessTest - The following 1 profile is active: "prod"
2024-03-01 17:05:19 [main] INFO  org.redisson.Version - Redisson 3.17.4
2024-03-01 17:05:19 [redisson-netty-2-5] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2024-03-01 17:05:20 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2024-03-01 17:05:20 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2024-03-01 17:05:20 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2024-03-01 17:05:20 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2024-03-01 17:05:20 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2024-03-01 17:05:21 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2024-03-01 17:05:22 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-2,data} inited
2024-03-01 17:05:22 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [data] success
2024-03-01 17:05:22 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2024-03-01 17:05:22 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2024-03-01 17:05:24 [main] INFO  c.f.common.spring.SpringContextUtil - ------SpringContextUtil setApplicationContext-------
2024-03-01 17:05:26 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2024-03-01 17:05:29 [main] INFO  BusinessTest - Started BusinessTest in 13.678 seconds (JVM running for 14.886)
2024-03-01 17:08:24 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2024-03-01 17:08:24 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-2} closing ...
2024-03-01 17:08:24 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-2} closed
2024-03-01 17:08:24 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2024-03-01 17:08:24 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2024-03-01 17:08:24 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
