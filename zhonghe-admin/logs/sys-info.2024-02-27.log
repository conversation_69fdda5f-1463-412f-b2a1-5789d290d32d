2024-02-27 08:58:37 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [BusinessTest], using SpringBootContextLoader
2024-02-27 08:58:37 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [BusinessTest]: no resource found for suffixes {-context.xml, Context.groovy}.
2024-02-27 08:58:37 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.boot.test.autoconfigure.webservices.client.MockWebServiceServerTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2024-02-27 08:58:37 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@58670130, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@74e47444, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@9bd0fa6, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@59d2103b, org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@39dcf4b0, org.springframework.test.context.support.DirtiesContextTestExecutionListener@6e4de19b, org.springframework.test.context.transaction.TransactionalTestExecutionListener@f6c03cb, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@46f699d5, org.springframework.test.context.event.EventPublishingTestExecutionListener@18518ccf, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@1991f767, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@768ccdc5, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@4c6daf0, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@10650953, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@659eef7, org.springframework.boot.test.autoconfigure.webservices.client.MockWebServiceServerTestExecutionListener@162be91c]
2024-02-27 08:58:37 [main] INFO  BusinessTest - Starting BusinessTest using Java 11.0.18 on MacBook-Pro.local with PID 26081 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-admin)
2024-02-27 08:58:37 [main] INFO  BusinessTest - The following 1 profile is active: "prod"
2024-02-27 08:58:37 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2024-02-27 08:58:39 [main] INFO  org.redisson.Version - Redisson 3.17.4
2024-02-27 08:58:40 [redisson-netty-2-5] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2024-02-27 08:58:40 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2024-02-27 08:58:41 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2024-02-27 08:58:41 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2024-02-27 08:58:41 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2024-02-27 08:58:41 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2024-02-27 08:58:41 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2024-02-27 08:58:42 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-2,data} inited
2024-02-27 08:58:42 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [data] success
2024-02-27 08:58:42 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2024-02-27 08:58:42 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2024-02-27 08:58:43 [main] INFO  c.f.common.spring.SpringContextUtil - ------SpringContextUtil setApplicationContext-------
2024-02-27 08:58:46 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2024-02-27 08:58:48 [main] INFO  BusinessTest - Started BusinessTest in 11.455 seconds (JVM running for 12.126)
2024-02-27 09:22:56 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [BusinessTest], using SpringBootContextLoader
2024-02-27 09:22:56 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [BusinessTest]: no resource found for suffixes {-context.xml, Context.groovy}.
2024-02-27 09:22:56 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.boot.test.autoconfigure.webservices.client.MockWebServiceServerTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2024-02-27 09:22:56 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@18f20260, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@4ae33a11, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@7a48e6e2, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@b40bb6e, org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@3a94964, org.springframework.test.context.support.DirtiesContextTestExecutionListener@5049d8b2, org.springframework.test.context.transaction.TransactionalTestExecutionListener@6d0b5baf, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@631e06ab, org.springframework.test.context.event.EventPublishingTestExecutionListener@2a3591c5, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@34a75079, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@346a361, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@107ed6fc, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@1643d68f, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@186978a6, org.springframework.boot.test.autoconfigure.webservices.client.MockWebServiceServerTestExecutionListener@2e029d61]
2024-02-27 09:22:56 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2024-02-27 09:22:56 [main] INFO  BusinessTest - Starting BusinessTest using Java 11.0.18 on MacBook-Pro.local with PID 28150 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-admin)
2024-02-27 09:22:56 [main] INFO  BusinessTest - The following 1 profile is active: "prod"
2024-02-27 09:22:58 [main] INFO  org.redisson.Version - Redisson 3.17.4
2024-02-27 09:22:59 [redisson-netty-2-5] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2024-02-27 09:22:59 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2024-02-27 09:23:00 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2024-02-27 09:23:00 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2024-02-27 09:23:00 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2024-02-27 09:23:00 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2024-02-27 09:23:01 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2024-02-27 09:23:01 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-2,data} inited
2024-02-27 09:23:01 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [data] success
2024-02-27 09:23:01 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2024-02-27 09:23:01 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2024-02-27 09:23:03 [main] INFO  c.f.common.spring.SpringContextUtil - ------SpringContextUtil setApplicationContext-------
2024-02-27 09:23:06 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2024-02-27 09:23:08 [main] INFO  BusinessTest - Started BusinessTest in 12.056 seconds (JVM running for 12.866)
2024-02-27 09:24:18 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [BusinessTest], using SpringBootContextLoader
2024-02-27 09:24:18 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [BusinessTest]: no resource found for suffixes {-context.xml, Context.groovy}.
2024-02-27 09:24:18 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.boot.test.autoconfigure.webservices.client.MockWebServiceServerTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2024-02-27 09:24:18 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@18f20260, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@4ae33a11, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@7a48e6e2, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@b40bb6e, org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@3a94964, org.springframework.test.context.support.DirtiesContextTestExecutionListener@5049d8b2, org.springframework.test.context.transaction.TransactionalTestExecutionListener@6d0b5baf, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@631e06ab, org.springframework.test.context.event.EventPublishingTestExecutionListener@2a3591c5, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@34a75079, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@346a361, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@107ed6fc, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@1643d68f, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@186978a6, org.springframework.boot.test.autoconfigure.webservices.client.MockWebServiceServerTestExecutionListener@2e029d61]
2024-02-27 09:24:18 [main] INFO  BusinessTest - Starting BusinessTest using Java 11.0.18 on MacBook-Pro.local with PID 28291 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-admin)
2024-02-27 09:24:18 [main] INFO  BusinessTest - The following 1 profile is active: "prod"
2024-02-27 09:24:18 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2024-02-27 09:24:20 [main] INFO  org.redisson.Version - Redisson 3.17.4
2024-02-27 09:24:21 [redisson-netty-2-5] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2024-02-27 09:24:21 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2024-02-27 09:24:21 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2024-02-27 09:24:21 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2024-02-27 09:24:21 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2024-02-27 09:24:22 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2024-02-27 09:24:22 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2024-02-27 09:24:23 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-2,data} inited
2024-02-27 09:24:23 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [data] success
2024-02-27 09:24:23 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2024-02-27 09:24:23 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2024-02-27 09:24:24 [main] INFO  c.f.common.spring.SpringContextUtil - ------SpringContextUtil setApplicationContext-------
2024-02-27 09:24:28 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2024-02-27 09:24:30 [main] INFO  BusinessTest - Started BusinessTest in 12.612 seconds (JVM running for 13.387)
2024-02-27 09:25:33 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [BusinessTest], using SpringBootContextLoader
2024-02-27 09:25:33 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [BusinessTest]: no resource found for suffixes {-context.xml, Context.groovy}.
2024-02-27 09:25:33 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.boot.test.autoconfigure.webservices.client.MockWebServiceServerTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2024-02-27 09:25:33 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@18f20260, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@4ae33a11, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@7a48e6e2, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@b40bb6e, org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@3a94964, org.springframework.test.context.support.DirtiesContextTestExecutionListener@5049d8b2, org.springframework.test.context.transaction.TransactionalTestExecutionListener@6d0b5baf, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@631e06ab, org.springframework.test.context.event.EventPublishingTestExecutionListener@2a3591c5, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@34a75079, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@346a361, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@107ed6fc, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@1643d68f, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@186978a6, org.springframework.boot.test.autoconfigure.webservices.client.MockWebServiceServerTestExecutionListener@2e029d61]
2024-02-27 09:25:34 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2024-02-27 09:25:34 [main] INFO  BusinessTest - Starting BusinessTest using Java 11.0.18 on MacBook-Pro.local with PID 28413 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-admin)
2024-02-27 09:25:34 [main] INFO  BusinessTest - The following 1 profile is active: "prod"
2024-02-27 09:25:36 [main] INFO  org.redisson.Version - Redisson 3.17.4
2024-02-27 09:25:36 [redisson-netty-2-5] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2024-02-27 09:25:36 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2024-02-27 09:25:37 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2024-02-27 09:25:37 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2024-02-27 09:25:37 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2024-02-27 09:25:37 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2024-02-27 09:25:38 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2024-02-27 09:25:38 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-2,data} inited
2024-02-27 09:25:38 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [data] success
2024-02-27 09:25:38 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2024-02-27 09:25:38 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2024-02-27 09:25:40 [main] INFO  c.f.common.spring.SpringContextUtil - ------SpringContextUtil setApplicationContext-------
2024-02-27 09:25:43 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2024-02-27 09:25:45 [main] INFO  BusinessTest - Started BusinessTest in 11.807 seconds (JVM running for 12.499)
2024-02-27 09:27:04 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [BusinessTest], using SpringBootContextLoader
2024-02-27 09:27:04 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [BusinessTest]: no resource found for suffixes {-context.xml, Context.groovy}.
2024-02-27 09:27:04 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.boot.test.autoconfigure.webservices.client.MockWebServiceServerTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2024-02-27 09:27:04 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@18f20260, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@4ae33a11, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@7a48e6e2, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@b40bb6e, org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@3a94964, org.springframework.test.context.support.DirtiesContextTestExecutionListener@5049d8b2, org.springframework.test.context.transaction.TransactionalTestExecutionListener@6d0b5baf, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@631e06ab, org.springframework.test.context.event.EventPublishingTestExecutionListener@2a3591c5, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@34a75079, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@346a361, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@107ed6fc, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@1643d68f, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@186978a6, org.springframework.boot.test.autoconfigure.webservices.client.MockWebServiceServerTestExecutionListener@2e029d61]
2024-02-27 09:27:04 [main] INFO  BusinessTest - Starting BusinessTest using Java 11.0.18 on MacBook-Pro.local with PID 28539 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-admin)
2024-02-27 09:27:04 [main] INFO  BusinessTest - The following 1 profile is active: "prod"
2024-02-27 09:27:04 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2024-02-27 09:27:06 [main] INFO  org.redisson.Version - Redisson 3.17.4
2024-02-27 09:27:07 [redisson-netty-2-5] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2024-02-27 09:27:07 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2024-02-27 09:27:07 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2024-02-27 09:27:07 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2024-02-27 09:27:07 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2024-02-27 09:27:07 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2024-02-27 09:27:08 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2024-02-27 09:27:09 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-2,data} inited
2024-02-27 09:27:09 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [data] success
2024-02-27 09:27:09 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2024-02-27 09:27:09 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2024-02-27 09:27:10 [main] INFO  c.f.common.spring.SpringContextUtil - ------SpringContextUtil setApplicationContext-------
2024-02-27 09:27:14 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2024-02-27 09:27:16 [main] INFO  BusinessTest - Started BusinessTest in 12.263 seconds (JVM running for 13.064)
2024-02-27 09:29:32 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2024-02-27 09:29:32 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-2} closing ...
2024-02-27 09:29:32 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-2} closed
2024-02-27 09:29:32 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2024-02-27 09:29:32 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2024-02-27 09:29:32 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2024-02-27 09:32:48 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [BusinessTest], using SpringBootContextLoader
2024-02-27 09:32:48 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [BusinessTest]: no resource found for suffixes {-context.xml, Context.groovy}.
2024-02-27 09:32:48 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener, org.springframework.boot.test.autoconfigure.webservices.client.MockWebServiceServerTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2024-02-27 09:32:48 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@4ba534b0, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@6f0ca692, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@2c104774, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@2cb3d0f7, org.springframework.boot.test.autoconfigure.SpringBootDependencyInjectionTestExecutionListener@4e517165, org.springframework.test.context.support.DirtiesContextTestExecutionListener@44e3760b, org.springframework.test.context.transaction.TransactionalTestExecutionListener@6a66a204, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@5860f3d7, org.springframework.test.context.event.EventPublishingTestExecutionListener@1d7f7be7, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@42f3156d, org.springframework.boot.test.autoconfigure.restdocs.RestDocsTestExecutionListener@1ddae9b5, org.springframework.boot.test.autoconfigure.web.client.MockRestServiceServerResetTestExecutionListener@427b5f92, org.springframework.boot.test.autoconfigure.web.servlet.MockMvcPrintOnlyOnFailureTestExecutionListener@24bdb479, org.springframework.boot.test.autoconfigure.web.servlet.WebDriverTestExecutionListener@7e3f95fe, org.springframework.boot.test.autoconfigure.webservices.client.MockWebServiceServerTestExecutionListener@34625ccd]
2024-02-27 09:32:48 [main] INFO  BusinessTest - Starting BusinessTest using Java 11.0.18 on MacBook-Pro.local with PID 29030 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-admin)
2024-02-27 09:32:48 [main] INFO  BusinessTest - The following 1 profile is active: "prod"
2024-02-27 09:32:48 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2024-02-27 09:32:50 [main] INFO  org.redisson.Version - Redisson 3.17.4
2024-02-27 09:32:51 [redisson-netty-2-5] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2024-02-27 09:32:51 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2024-02-27 09:32:51 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2024-02-27 09:32:51 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2024-02-27 09:32:51 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2024-02-27 09:32:51 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2024-02-27 09:32:52 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2024-02-27 09:32:53 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-2,data} inited
2024-02-27 09:32:53 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [data] success
2024-02-27 09:32:53 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2024-02-27 09:32:53 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2024-02-27 09:32:54 [main] INFO  c.f.common.spring.SpringContextUtil - ------SpringContextUtil setApplicationContext-------
2024-02-27 09:32:57 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2024-02-27 09:32:59 [main] INFO  BusinessTest - Started BusinessTest in 11.379 seconds (JVM running for 12.035)
