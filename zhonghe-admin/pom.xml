<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>zhonghe-vue-plus</artifactId>
        <groupId>com.zhonghe</groupId>
        <version>2.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <packaging>jar</packaging>
    <artifactId>zhonghe-admin</artifactId>

    <description>
        web服务入口
    </description>

    <dependencies>
        <!-- spring-boot-devtools -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <optional>true</optional> <!-- 表示依赖不会传递 -->
        </dependency>

        <!--  去掉内置的tomcat-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-tomcat</artifactId>
            <scope>provided</scope>
        </dependency>


        <!-- Mysql驱动包 -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>
        <!-- Oracle -->
        <dependency>
            <groupId>com.oracle.database.jdbc</groupId>
            <artifactId>ojdbc8</artifactId>
        </dependency>
        <!-- PostgreSql -->
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
        </dependency>
        <!-- 人大金仓数据库 -->
        <!--<dependency>
            <groupId>com.kingbase8</groupId>
            <artifactId>kingbase8</artifactId>
            <version>8-8.2.0</version>
        </dependency>-->

        <!-- SqlServer -->
        <dependency>
            <groupId>com.microsoft.sqlserver</groupId>
            <artifactId>mssql-jdbc</artifactId>
        </dependency>

        <!-- Sybase -->
        <dependency>
            <groupId>net.sourceforge.jtds</groupId>
            <artifactId>jtds</artifactId>
        </dependency>

        <!-- 核心模块-->
        <dependency>
            <groupId>com.zhonghe</groupId>
            <artifactId>zhonghe-framework</artifactId>
        </dependency>

        <!--   <dependency>
               <groupId>com.zhonghe</groupId>
               <artifactId>zhonghe-system</artifactId>
           </dependency>-->

        <dependency>
            <groupId>com.zhonghe</groupId>
            <artifactId>zhonghe-job</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zhonghe</groupId>
            <artifactId>zhonghe-oss</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>commons-fileupload</artifactId>
                    <groupId>commons-fileupload</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>commons-io</artifactId>
                    <groupId>commons-io</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.zhonghe</groupId>
            <artifactId>zhonghe-ai</artifactId>
            <version>2.0</version>
            <exclusions>
                <exclusion>
                    <groupId>com.zhonghe</groupId>
                    <artifactId>zhonghe-system</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--<dependency>
            <groupId>com.zhonghe</groupId>
            <artifactId>zhonghe-fengqiao</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.zhonghe</groupId>
                    <artifactId>zhonghe-system</artifactId>
                </exclusion>
            </exclusions>
        </dependency>-->
        <!-- 代码生成-->
        <dependency>
            <groupId>com.zhonghe</groupId>
            <artifactId>zhonghe-generator</artifactId>
        </dependency>

        <!--  demo模块  -->
        <dependency>
            <groupId>com.zhonghe</groupId>
            <artifactId>zhonghe-demo</artifactId>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
        </dependency>

        <!-- springboot测试模块-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <!--企业风险预警-->
        <dependency>
            <groupId>com.zhonghe</groupId>
            <artifactId>zhonghe-cop</artifactId>
        </dependency>

        <!--onlyoffcie-->
        <dependency>
            <groupId>com.zhonghe</groupId>
            <artifactId>zhonghe-onlyoffice</artifactId>
        </dependency>

        <!-- junit测试组件-->
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.13.2</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.zhonghe</groupId>
            <artifactId>zhonghe-fengqiao</artifactId>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <configuration>
                    <fork>true</fork> <!-- 如果没有该配置，devtools不会生效 -->
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>3.2.2</version>
                <configuration>
                    <failOnMissingWebXml>false</failOnMissingWebXml>
                    <warName>${project.artifactId}</warName>
                </configuration>
            </plugin>
            <!--            <plugin>-->
            <!--                <groupId>com.spotify</groupId>-->
            <!--                <artifactId>docker-maven-plugin</artifactId>-->
            <!--                <version>${docker.plugin.version}</version>-->
            <!--                <configuration>-->
            <!--                    <imageName>${docker.namespace}/ruoyi-server:${project.version}</imageName>-->
            <!--                    <dockerDirectory>${project.basedir}</dockerDirectory>-->
            <!--                    <dockerHost>${docker.registry.host}</dockerHost>-->
            <!--                    <registryUrl>${docker.registry.url}</registryUrl>-->
            <!--                    <serverId>${docker.registry.url}</serverId>-->
            <!--                    <resources>-->
            <!--                        <resource>-->
            <!--                            <targetPath>/</targetPath>-->
            <!--                            <directory>${project.build.directory}</directory>-->
            <!--                            <include>${project.build.finalName}.jar</include>-->
            <!--                        </resource>-->
            <!--                    </resources>-->
            <!--                </configuration>-->
            <!--            </plugin>-->

        </plugins>
    </build>
</project>
