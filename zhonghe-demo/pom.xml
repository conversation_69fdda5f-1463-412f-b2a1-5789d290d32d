<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>zhonghe-vue-plus</artifactId>
        <groupId>com.zhonghe</groupId>
        <version>2.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>zhonghe-demo</artifactId>

    <description>
        demo模块
    </description>

    <dependencies>

        <!-- 通用工具-->
        <dependency>
            <groupId>com.zhonghe</groupId>
            <artifactId>zhonghe-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.zhonghe</groupId>
            <artifactId>zhonghe-sms</artifactId>
        </dependency>

        <!-- 短信 用哪个导入哪个依赖 -->
<!--        <dependency>-->
<!--            <groupId>com.aliyun</groupId>-->
<!--            <artifactId>dysmsapi20170525</artifactId>-->
<!--        </dependency>-->

<!--        <dependency>-->
<!--            <groupId>com.tencentcloudapi</groupId>-->
<!--            <artifactId>tencentcloud-sdk-java</artifactId>-->
<!--        </dependency>-->

    </dependencies>

</project>
