package com.zhonghe.demo.mapper;

import com.zhonghe.common.annotation.DataColumn;
import com.zhonghe.common.annotation.DataPermission;
import com.zhonghe.common.core.mapper.BaseMapperPlus;
import com.zhonghe.demo.domain.TestTree;
import com.zhonghe.demo.domain.vo.TestTreeVo;

/**
 * 测试树表Mapper接口
 *
 * <AUTHOR>
 * @date 2021-07-26
 */
@DataPermission({
    @DataColumn(key = "deptName", value = "dept_id"),
    @DataColumn(key = "userName", value = "user_id")
})
public interface TestTreeMapper extends BaseMapperPlus<TestTreeMapper, TestTree, TestTreeVo> {

}
