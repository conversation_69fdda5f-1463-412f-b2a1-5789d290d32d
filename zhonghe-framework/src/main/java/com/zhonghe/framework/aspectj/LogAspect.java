package com.zhonghe.framework.aspectj;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.zhonghe.common.annotation.Log;
import com.zhonghe.common.apiassace.ApiAccessLogService;
import com.zhonghe.common.apiassace.ApiErrorLogService;
import com.zhonghe.common.apiassace.apiaccesslog.ApiErrorLogCreateReqDTO;
import com.zhonghe.common.apiassace.apierrorlog.ApiAccessLogCreateReqDTO;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.common.core.domain.dto.OperLogDTO;
import com.zhonghe.common.core.domain.entity.SysUser;
import com.zhonghe.common.core.service.OperLogService;
import com.zhonghe.common.enums.BusinessStatus;
import com.zhonghe.common.enums.HttpMethod;
import com.zhonghe.common.helper.LoginHelper;
import com.zhonghe.common.utils.JsonUtils;
import com.zhonghe.common.utils.ServletUtils;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.common.utils.redis.RedisUtils;
import com.zhonghe.common.utils.spring.SpringUtils;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;
import org.springframework.validation.BindingResult;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.HandlerMapping;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Collection;
import java.util.Date;
import java.util.Map;
import java.util.UUID;

/**
 * 操作日志记录处理
 *
 * <AUTHOR> Li
 */
@Slf4j
@Aspect
@Component
public class LogAspect {


    /**
     * 处理完请求后执行
     *
     * @param joinPoint 切点
     */
    @AfterReturning(pointcut = "@annotation(controllerLog)", returning = "jsonResult")
    public void doAfterReturning(JoinPoint joinPoint, Log controllerLog, Object jsonResult) {
        handleLog(joinPoint, controllerLog, null, jsonResult, true);

    }

    /**
     * 拦截异常操作
     *
     * @param joinPoint 切点
     * @param e         异常
     */
    @AfterThrowing(value = "@annotation(controllerLog)", throwing = "e")
    public void doAfterThrowing(JoinPoint joinPoint, Log controllerLog, Exception e) {
        handleLog(joinPoint, controllerLog, e, null, false);

    }

    protected void handleLog(final JoinPoint joinPoint, Log controllerLog, final Exception e, Object jsonResult, Boolean idException) {
        try {
            HttpServletRequest request = ServletUtils.getRequest();
            // *========数据库日志=========*//
            OperLogDTO operLog = new OperLogDTO();
            operLog.setStatus(BusinessStatus.SUCCESS.ordinal());
            // 请求的地址
            String ip = ServletUtils.getClientIP();
            operLog.setOperIp(ip);
            operLog.setOperUrl(ServletUtils.getRequest().getRequestURI());
            operLog.setOperName(LoginHelper.getUsername());

            StackTraceElement[] stackTrace = null;
            String stackTraces = "";
            if (e != null) {
                operLog.setStatus(BusinessStatus.FAIL.ordinal());
                operLog.setErrorMsg(StringUtils.substring(e.getMessage(), 0, 2000));
                stackTrace = e.getStackTrace();
                StringBuffer sb = new StringBuffer("");
                for (StackTraceElement stackTraceElement : stackTrace) {
                    sb.append(stackTraceElement.toString() + "\n");
                }
                stackTraces = sb.toString();
            }
            // 设置方法名称
            String className = joinPoint.getTarget().getClass().getName();
            String methodName = joinPoint.getSignature().getName();
            operLog.setMethod(className + "." + methodName + "()");
            // 设置请求方式
            operLog.setRequestMethod(ServletUtils.getRequest().getMethod());
            // 处理设置注解上的参数
            getControllerMethodDescription(joinPoint, controllerLog, operLog, jsonResult);
            // 保存数据库
            SpringUtils.getBean(OperLogService.class).recordOper(operLog);
            //获取请求结果
            R<?> r = null;
            if (jsonResult instanceof R) {
                r = (R<?>) jsonResult;
            }

            Map<String, Object> ajax = RedisUtils.getCacheObject("ajax");
            if (CollectionUtils.isNotEmpty(ajax)) {
                SysUser user = (SysUser) ajax.get("user");
                if (idException) {//正常api日志
                    ApiAccessLogCreateReqDTO apiAccessLogCreateReqDTO = new ApiAccessLogCreateReqDTO();
                    apiAccessLogCreateReqDTO.setApplicationName(operLog.getTitle());
                    apiAccessLogCreateReqDTO.setBeginTime(operLog.getOperTime() != null ? operLog.getOperTime() : new Date());
                    apiAccessLogCreateReqDTO.setDuration(operLog.getBusinessType());
                    apiAccessLogCreateReqDTO.setEndTime(operLog.getOperTime() != null ? operLog.getOperTime() : new Date());
                    apiAccessLogCreateReqDTO.setRequestMethod(operLog.getRequestMethod());
                    apiAccessLogCreateReqDTO.setRequestUrl(operLog.getOperUrl());
                    apiAccessLogCreateReqDTO.setUserIp(operLog.getOperIp());
                    apiAccessLogCreateReqDTO.setRequestParams(operLog.getOperParam());
                    apiAccessLogCreateReqDTO.setResultCode(r != null ? r.getCode() : 200);
                    apiAccessLogCreateReqDTO.setResultMsg(r != null ? r.getMsg() : "请求失败");
                    apiAccessLogCreateReqDTO.setTraceId(UUID.randomUUID().toString());
                    apiAccessLogCreateReqDTO.setUserAgent(request.getHeader("User-Agent"));
                    apiAccessLogCreateReqDTO.setUserId(user.getUserId());
                    apiAccessLogCreateReqDTO.setUserType(1);
                    SpringUtils.getBean(ApiAccessLogService.class).createApiAccessLog(apiAccessLogCreateReqDTO);

                } else {//异常日志
                    ApiErrorLogCreateReqDTO apiAccessLogCreateReqDTO = new ApiErrorLogCreateReqDTO();
                    apiAccessLogCreateReqDTO.setApplicationName(operLog.getTitle());
                    apiAccessLogCreateReqDTO.setRequestMethod(operLog.getRequestMethod());
                    apiAccessLogCreateReqDTO.setRequestUrl(operLog.getOperUrl());
                    apiAccessLogCreateReqDTO.setRequestParams(operLog.getOperParam());
                    apiAccessLogCreateReqDTO.setTraceId(stackTrace != null ? stackTrace[0].getLineNumber() + "" : 0 + "");
                    apiAccessLogCreateReqDTO.setUserAgent(request.getHeader("User-Agent"));
                    apiAccessLogCreateReqDTO.setUserIp(operLog.getOperIp());
//                apiAccessLogCreateReqDTO.setUserId(user.getUserId());
                    apiAccessLogCreateReqDTO.setUserType(1);
                    apiAccessLogCreateReqDTO.setExceptionClassName(stackTrace != null ? stackTrace[0].getClassName() : e.getClass().toString());
                    apiAccessLogCreateReqDTO.setExceptionFileName(stackTrace != null ? stackTrace[0].getFileName() : "");
                    apiAccessLogCreateReqDTO.setExceptionLineNumber(stackTrace != null ? stackTrace[0].getLineNumber() : 0);//e.getStackTrace()
                    apiAccessLogCreateReqDTO.setExceptionMethodName(stackTrace != null ? stackTrace[0].getMethodName() : "");
                    apiAccessLogCreateReqDTO.setExceptionRootCauseMessage(e.getLocalizedMessage());
                    apiAccessLogCreateReqDTO.setExceptionName(stackTrace != null ? stackTrace[0].getClassName() : e.getClass().toString());
                    apiAccessLogCreateReqDTO.setExceptionStackTrace(StringUtils.isNotBlank(stackTraces) ? stackTraces : e.toString());
                    apiAccessLogCreateReqDTO.setExceptionTime(operLog.getOperTime() != null ? operLog.getOperTime() : new Date());
                    apiAccessLogCreateReqDTO.setExceptionMessage(operLog.getErrorMsg());

                    SpringUtils.getBean(ApiErrorLogService.class).createApiErrorLog(apiAccessLogCreateReqDTO);
                }
            }
        } catch (Exception exp) {
            // 记录本地异常日志
            log.error("==前置通知异常==");
            log.error("异常信息:{}", exp.getMessage());
            exp.printStackTrace();
        }
    }

    private static final ThreadLocal<String> KEY_CACHE = new ThreadLocal<>();

    /**
     * 获取注解中对方法的描述信息 用于Controller层注解
     *
     * @param log     日志
     * @param operLog 操作日志
     * @throws Exception
     */
    public void getControllerMethodDescription(JoinPoint joinPoint, Log log, OperLogDTO operLog, Object jsonResult) throws Exception {
        // 设置action动作
        operLog.setBusinessType(log.businessType().ordinal());
        // 设置标题
        operLog.setTitle(log.title());
        // 设置操作人类别
        operLog.setOperatorType(log.operatorType().ordinal());
        // 是否需要保存request，参数和值
        if (log.isSaveRequestData()) {
            // 获取参数的信息，传入到数据库中。
            setRequestValue(joinPoint, operLog);
        }
        // 是否需要保存response，参数和值
        if (log.isSaveResponseData() && ObjectUtil.isNotNull(jsonResult)) {
            operLog.setJsonResult(StringUtils.substring(JsonUtils.toJsonString(jsonResult), 0, 2000));
        }
    }

    /**
     * 获取请求的参数，放到log中
     *
     * @param operLog 操作日志
     * @throws Exception 异常
     */
    private void setRequestValue(JoinPoint joinPoint, OperLogDTO operLog) throws Exception {
        String requestMethod = operLog.getRequestMethod();
        if (HttpMethod.PUT.name().equals(requestMethod) || HttpMethod.POST.name().equals(requestMethod)) {
            String params = argsArrayToString(joinPoint.getArgs());
            operLog.setOperParam(StringUtils.substring(params, 0, 2000));
        } else {
            Map<?, ?> paramsMap = (Map<?, ?>) ServletUtils.getRequest().getAttribute(HandlerMapping.URI_TEMPLATE_VARIABLES_ATTRIBUTE);
            operLog.setOperParam(StringUtils.substring(paramsMap.toString(), 0, 2000));
        }
    }

    /**
     * 参数拼装
     */
    private String argsArrayToString(Object[] paramsArray) {
        StringBuilder params = new StringBuilder();
        if (paramsArray != null && paramsArray.length > 0) {
            for (Object o : paramsArray) {
                if (ObjectUtil.isNotNull(o) && !isFilterObject(o)) {
                    try {
                        params.append(JsonUtils.toJsonString(o)).append(" ");
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        }
        return params.toString().trim();
    }

    /**
     * 判断是否需要过滤的对象。
     *
     * @param o 对象信息。
     * @return 如果是需要过滤的对象，则返回true；否则返回false。
     */
    @SuppressWarnings("rawtypes")
    public boolean isFilterObject(final Object o) {
        Class<?> clazz = o.getClass();
        if (clazz.isArray()) {
            return clazz.getComponentType().isAssignableFrom(MultipartFile.class);
        } else if (Collection.class.isAssignableFrom(clazz)) {
            Collection collection = (Collection) o;
            for (Object value : collection) {
                return value instanceof MultipartFile;
            }
        } else if (Map.class.isAssignableFrom(clazz)) {
            Map map = (Map) o;
            for (Object value : map.entrySet()) {
                Map.Entry entry = (Map.Entry) value;
                return entry.getValue() instanceof MultipartFile;
            }
        }
        return o instanceof MultipartFile || o instanceof HttpServletRequest || o instanceof HttpServletResponse
            || o instanceof BindingResult;
    }
}
