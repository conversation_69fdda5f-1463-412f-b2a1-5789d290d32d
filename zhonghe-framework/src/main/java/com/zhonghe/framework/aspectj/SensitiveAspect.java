package com.zhonghe.framework.aspectj;

import com.zhonghe.common.annotation.SensitiveExtra;
import com.zhonghe.common.helper.SensitiveHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.springframework.stereotype.Component;

/**
 * @description:
 * @author: cq
 * @date: 2023/3/14 15:21
 */

@Slf4j
@RequiredArgsConstructor
@Aspect
@Component
public class SensitiveAspect {


    @Before("@annotation(extra)")
    public void before(SensitiveExtra extra) {
        if (extra.skip()) {
            SensitiveHelper.set(true);
        }
    }
}

