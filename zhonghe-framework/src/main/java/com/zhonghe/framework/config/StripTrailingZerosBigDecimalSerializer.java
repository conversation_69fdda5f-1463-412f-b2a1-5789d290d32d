package com.zhonghe.framework.config;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;

import java.io.IOException;
import java.math.BigDecimal;

/**
 * @Author: lpg
 * @Date: 2024/04/26
 * @Description:
 */
public class StripTrailingZerosBigDecimalSerializer extends JsonSerializer<BigDecimal> {
    @Override
    public void serialize(BigDecimal value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
        if (value != null) {
            // 调用 stripTrailingZeros() 来去除末尾的零，并转换为字符串
            gen.writeString(value.stripTrailingZeros().toPlainString());
        }
    }
}
