package com.zhonghe.framework.config;

import com.zhonghe.framework.interceptor.PlusWebInvokeTimeInterceptor;
import com.yomahub.tlog.web.interceptor.TLogWebInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.Locale;

/**
 * 通用配置
 *
 * <AUTHOR> Li
 */
@Configuration
public class ResourcesConfig implements WebMvcConfigurer {

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 全局链路跟踪拦截器
        registry.addInterceptor(new TLogWebInterceptor());
        // 全局访问性能拦截
        registry.addInterceptor(new PlusWebInvokeTimeInterceptor());
    }

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 获取当前操作系统
        String osName = System.getProperties().get("os.name").toString().toLowerCase(Locale.ROOT);
        String saveFile;
        if(osName.startsWith("win")){
            saveFile = "file:/D:/xiangmu/RuoYi-Vue-Plus/";
        }else{
            saveFile = "opt/temp";
        }
        //本地路径并映射成url
        registry.addResourceHandler("/OTA/**").addResourceLocations(saveFile);
    }

    /**
     * 跨域配置
     */
    @Bean
    public CorsFilter corsFilter() {
        CorsConfiguration config = new CorsConfiguration();
        config.setAllowCredentials(true);
        // 设置访问源地址
        config.addAllowedOriginPattern("*");
        //允许任何域名使用
//        config.addAllowedOrigin("*");
        // 设置访问源请求头
        config.addAllowedHeader("*");
//        config.addAllowedHeader("Access-Control-Allow-Origin");
        // 设置访问源请求方法
        config.addAllowedMethod("*");
        // 有效期 1800秒
        config.setMaxAge(1800L);
        // 添加映射路径，拦截一切请求
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", config);
        // 返回新的CorsFilter
        return new CorsFilter(source);
    }

    /**
     * 创建 CorsFilter Bean，解决跨域问题
     */
//    @Bean
//    public FilterRegistrationBean<CorsFilter> corsFilterBean() {
//        // 创建 CorsConfiguration 对象
//        CorsConfiguration config = new CorsConfiguration();
//        config.setAllowCredentials(true);
//        config.addAllowedOriginPattern("*"); // 设置访问源地址
//        config.addAllowedHeader("*"); // 设置访问源请求头
//        config.addAllowedMethod("*"); // 设置访问源请求方法
//        // 创建 UrlBasedCorsConfigurationSource 对象
//        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
//        source.registerCorsConfiguration("/**", config); // 对接口配置跨域设置
//        return createFilterBean(new CorsFilter(source), Integer.MIN_VALUE);
//    }
//    private static <T extends Filter> FilterRegistrationBean<T> createFilterBean(T filter, Integer order) {
//        FilterRegistrationBean<T> bean = new FilterRegistrationBean<>(filter);
//        bean.setOrder(order);
//        return bean;
//    }
}
