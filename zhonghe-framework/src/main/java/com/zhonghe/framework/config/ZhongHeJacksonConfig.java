package com.zhonghe.framework.config;


import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.zhonghe.framework.jackson.BigNumberSerializer;
import org.springframework.context.annotation.Bean;

import java.text.SimpleDateFormat;

/**
 * @Author: lpg
 * @Date: 2023/04/14/11:39
 * @Description:1636305774021914
 */
//@Configuration
public class ZhongHeJacksonConfig {
    @Bean
    public ObjectMapper objectMapper() {
        ObjectMapper objectMapper = new ObjectMapper();
        // 序列化时忽略空属性
        //objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        // 配置Long类型序列化为字符串类型
        SimpleModule simpleModule = new SimpleModule();
        simpleModule.addSerializer(Long.class, BigNumberSerializer.INSTANCE);
        simpleModule.addSerializer(Long.TYPE, BigNumberSerializer.INSTANCE);
        objectMapper.registerModule(simpleModule);
        // 配置日期序列化格式
        objectMapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
        objectMapper.setDateFormat(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));
        return objectMapper;
    }
}
