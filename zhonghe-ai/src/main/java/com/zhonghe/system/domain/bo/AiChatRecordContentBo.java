package com.zhonghe.system.domain.bo;

import com.zhonghe.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 对话记录业务对象 ai_chat_record_content
 *
 * <AUTHOR>
 * @date 2025-02-24
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("对话记录业务对象")
public class AiChatRecordContentBo extends BaseEntity {

private Long contentId;

private Long chatId;

private String chatContent;
}
