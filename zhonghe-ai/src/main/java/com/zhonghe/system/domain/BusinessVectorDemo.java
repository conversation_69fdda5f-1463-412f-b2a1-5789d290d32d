package com.zhonghe.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;


/**
 * 对话记录对象 ai_chat_record
 *
 * <AUTHOR>
 * @date 2025-02-24
 */
@Data
@TableName("ai.business_vector_demo")
public class BusinessVectorDemo {

    private static final long serialVersionUID=1L;

    @TableId("vector_id")
    private Long vectorId;


    private String enterpriseName;


    private String creditCode;

    private String enterpriseType;

    private String license;

    private String address;

    private String telphone;

    private String employee;

    private String businessScope;

    private Date uploadTime;

    private String question;


    private String dangerLv;



}
