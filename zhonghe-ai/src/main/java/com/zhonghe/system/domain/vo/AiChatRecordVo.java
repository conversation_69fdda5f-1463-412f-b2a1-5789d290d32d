package com.zhonghe.system.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.Date;


/**
 * 对话记录视图对象 ai_chat_record
 *
 * <AUTHOR>
 * @date 2025-02-24
 */
@Data
@ApiModel("对话记录视图对象")
@ExcelIgnoreUnannotated
public class AiChatRecordVo {

    private static final long serialVersionUID = 1L;

    private Long chatId;

    private String chatName;

    private String context;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
}
