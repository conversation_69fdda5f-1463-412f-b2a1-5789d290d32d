package com.zhonghe.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zhonghe.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 对话记录对象 ai_chat_record
 *
 * <AUTHOR>
 * @date 2025-02-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("ai.ai_chat_record")
public class AiChatRecord extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * $column.columnComment
     */
    @TableId(value = "chat_id")
    private Long chatId;

    private String chatName;

    private String context;

}
