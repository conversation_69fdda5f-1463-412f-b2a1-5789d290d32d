package com.zhonghe.system.domain.vo;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zhonghe.ai.service.IVectorService;
import com.zhonghe.common.utils.StringUtils;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Vector;


/**
 * 对话记录对象 ai_chat_record
 *
 * <AUTHOR>
 * @date 2025-02-24
 */
@Data
@ApiModel("对话记录视图对象")
@ExcelIgnoreUnannotated
public class BusinessVectorDemoVo implements IVectorService {

    private static final long serialVersionUID = 1L;

    private static final String[] keys = new String[]{
        "enterpriseName", "creditCode", "enterpriseType"
        , "license", "address", "telphone"
        , "employee", "businessScope", "question", "dangerLv"

    };

    public static List<String> getKeys() {
        return ListUtil.list(false, keys);
    }

    private Long vectorId;

    private String enterpriseName;


    private String creditCode;

    private String enterpriseType;

    private String license;

    private String address;

    private String telphone;

    private String employee;

    private String businessScope;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date uploadTime;

    private String question;

    private String dangerLv;


    @Override
    public String getKeys(int idx) {
        return keys[idx];
    }

    @Override
    public Map<String, String> toMap() {
        Map<String, String> r = new HashMap<>();
        if (StringUtils.isNotEmpty(this.enterpriseName)) {
            r.put(this.getKeys(0), this.enterpriseName);
        }
        if (StringUtils.isNotEmpty(this.creditCode)) {
            r.put(this.getKeys(1), this.creditCode);
        }
        if (StringUtils.isNotEmpty(this.enterpriseType)) {
            r.put(this.getKeys(2), this.enterpriseType);
        }
        if (StringUtils.isNotEmpty(this.license)) {
            r.put(this.getKeys(3), this.license);
        }
        if (StringUtils.isNotEmpty(this.address)) {
            r.put(this.getKeys(4), this.address);
        }
        if (StringUtils.isNotEmpty(this.telphone)) {
            r.put(this.getKeys(5), this.telphone);
        }
        if (ObjectUtil.isNotEmpty(this.employee)) {
            r.put(this.getKeys(6), this.employee);
        }
        if (StringUtils.isNotEmpty(this.businessScope)) {
            r.put(this.getKeys(7), this.businessScope);
        }
        if (StringUtils.isNotEmpty(this.question)) {
            r.put(this.getKeys(8), this.question);
        }
        if (StringUtils.isNotEmpty(this.dangerLv)) {
            r.put(this.getKeys(9), this.dangerLv);
        }
        return r;
    }

    @Override
    public Map<String, Vector<Float>> toVectors(Vector<Vector<Float>> embdding) {
        Map<String, Vector<Float>> r = new HashMap<>(embdding.size());

        if (StringUtils.isNotEmpty(this.enterpriseName)) {
            r.put(this.getKeys(0), embdding.get(0));
        }
        if (StringUtils.isNotEmpty(this.creditCode)) {
            r.put(this.getKeys(1), embdding.get(1));
        }
        if (StringUtils.isNotEmpty(this.enterpriseType)) {
            r.put(this.getKeys(2), embdding.get(2));
        }
        if (StringUtils.isNotEmpty(this.license)) {
            r.put(this.getKeys(3), embdding.get(3));
        }
        if (StringUtils.isNotEmpty(this.address)) {
            r.put(this.getKeys(4), embdding.get(4));
        }
        if (StringUtils.isNotEmpty(this.telphone)) {
            r.put(this.getKeys(5), embdding.get(5));
        }
        if (ObjectUtil.isNotEmpty(this.employee)) {
            r.put(this.getKeys(6), embdding.get(6));
        }
        if (StringUtils.isNotEmpty(this.businessScope)) {
            r.put(this.getKeys(7), embdding.get(7));
        }
        if (StringUtils.isNotEmpty(this.question)) {
            r.put(this.getKeys(8), embdding.get(8));
        }
        if (StringUtils.isNotEmpty(this.dangerLv)) {
            r.put(this.getKeys(9), embdding.get(9));
        }

        return r;
    }


}
