package com.zhonghe.system.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @description:
 * @author: cq
 * @date: 2025/2/25 8:46
 */
@Data
@Component
public class BaseConfig {

    @Value("${zhonghe.ai.url}")
    private String url;

    @Value("${qdrant.url}")
    private String qdrantUrl;

    @Value("${qdrant.port}")
    private Integer port;

    @Value("${zhonghe.deepseek.url}")
    private String deepseekUrl;
}

