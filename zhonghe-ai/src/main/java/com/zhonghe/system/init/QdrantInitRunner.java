package com.zhonghe.system.init;

import com.zhonghe.system.mapper.BusinessVectorDemoMapper;
import lombok.AllArgsConstructor;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;

/**
 * @description:
 * @author: cq
 * @date: 2025/3/3 16:20
 */

//@Component
@AllArgsConstructor
public class QdrantInitRunner implements ApplicationRunner {

    private final BusinessVectorDemoMapper vectorDemoMapper;


    @Override
    public void run(ApplicationArguments args) throws Exception {
        /*List<BusinessVectorDemoVo> businessVectorDemos = vectorDemoMapper.selectVoList(new LambdaQueryWrapper<>());

        Map<Long, Map<String, Vector<Float>>> data = new HashMap<>(businessVectorDemos.size());

        businessVectorDemos.forEach(a -> {
            data.put(a.getVectorId(), new HashMap<>());
            Map<String, String> objMap = a.toMap();
            objMap.forEach((k, v1) -> {
                Vector<Vector<Float>> embdding = QdrantClientUtil.getEmbedding(v1);
                data.get(a.getVectorId()).put(k, embdding.get(0));
            });
        });

        QdrantClientUtil.upsertEmbedding("vector_demo_field", data, BusinessVectorDemoVo.getKeys());*/
    }
}

