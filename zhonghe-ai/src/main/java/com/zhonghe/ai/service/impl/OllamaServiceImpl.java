package com.zhonghe.ai.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zhonghe.ai.dto.ChatRequestDto;
import com.zhonghe.ai.dto.ollama.OllamaBase;
import com.zhonghe.ai.dto.ollama.OllamaChatMessage;
import com.zhonghe.ai.dto.ollama.OllamaChatRequestBody;
import com.zhonghe.ai.dto.ollama.OllamaFactory;
import com.zhonghe.ai.dto.ollama.OllamaGenerateRequestBody;
import com.zhonghe.ai.service.IOllamaService;
import com.zhonghe.ai.util.QdrantClientUtil;
import com.zhonghe.common.helper.IdGeneratorHelper;
import com.zhonghe.common.helper.LoginHelper;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.system.config.BaseConfig;
import com.zhonghe.system.domain.AiChatRecord;
import com.zhonghe.system.domain.AiChatRecordContent;
import com.zhonghe.system.domain.bo.AiChatRecordBo;
import com.zhonghe.system.domain.vo.AiChatRecordContentVo;
import com.zhonghe.system.domain.vo.AiChatRecordVo;
import com.zhonghe.system.domain.vo.BusinessVectorDemoVo;
import com.zhonghe.system.mapper.AiChatRecordContentMapper;
import com.zhonghe.system.mapper.AiChatRecordMapper;
import com.zhonghe.system.mapper.BusinessVectorDemoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import reactor.core.publisher.Flux;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.Vector;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.atomic.AtomicReference;

/**
 * @description:
 * @author: cq
 * @date: 2025/2/24 10:35
 */

@Service
@Slf4j
public class OllamaServiceImpl implements IOllamaService {

    private final WebClient webClient;

    private final WebClient deepseekClient;
    @Resource
    private BusinessVectorDemoMapper vectorDemoMapper;

    @Value("${zhonghe.ai.model}")
    private String model;

    @Resource
    private AiChatRecordMapper chatRecordMapper;

    @Resource
    private AiChatRecordContentMapper chatRecordContentMapper;

    private final static String Header = "{\"chatId\": \"%s\",\"done\": false}";

    public OllamaServiceImpl(WebClient.Builder builder, @Autowired BaseConfig config) {
        this.webClient = builder.baseUrl(config.getUrl()).build();
        this.deepseekClient = builder.baseUrl(config.getDeepseekUrl()).build();
    }


    @Override
    public SseEmitter chatStream(ChatRequestDto dto) throws JsonProcessingException {
        SseEmitter emitter = new SseEmitter();

        Long chatId = dto.getChatId();
        String context = null;
        if (!ObjectUtil.isNull(dto.getChatId())) {
            // 创建一个新对话
            AiChatRecord aiChatRecord = chatRecordMapper.selectById(chatId);
            if (ObjectUtil.isNull(aiChatRecord)) {
                chatId = null;
            } else {
                context = aiChatRecord.getContext();
            }

        }

        if (ObjectUtil.isNull(chatId)) {
            chatId = IdGeneratorHelper.next();
            AiChatRecord newOne = new AiChatRecord();
            newOne.setChatId(chatId);
            newOne.setChatName(dto.getPrompt().length() > 31 ? dto.getPrompt().substring(0, 31) : dto.getPrompt());
            chatRecordMapper.insert(newOne);

            try {
                emitter.send(String.format(Header, chatId));
            } catch (IOException e) {
                emitter.completeWithError(e);
            }
        }

        // 保存会话记录
        AiChatRecordContent chatRecordContent = new AiChatRecordContent();
        chatRecordContent.setChatId(chatId);
        chatRecordContent.setChatContent(dto.getPrompt());
        chatRecordContent.setQuestion(1);
        chatRecordContent.setContentId(IdGeneratorHelper.next());

        chatRecordContentMapper.insert(chatRecordContent);

        OllamaGenerateRequestBody body = new OllamaGenerateRequestBody(dto.isStream());
        body.setStream(dto.isStream());
        body.addMessage(dto.getPrompt());
        body.setModel(model);
        if (StringUtils.isNotEmpty(context)) {
            ObjectMapper mapper = new ObjectMapper();
            List<Integer> numberList = mapper.readValue(context, new TypeReference<List<Integer>>() {
            });
            body.setContext(numberList);

        }

        Flux<String> streamFlux = webClient.post()
                .uri("/api/generate")
                .body(BodyInserters.fromValue(body))
                .retrieve()
                .bodyToFlux(String.class);

        //StringBuilder response = new StringBuilder();
        JSONArray response = new JSONArray();
        AtomicReference<String> lastData = new AtomicReference<>();
        Long finalChatId = chatId;
        streamFlux.subscribe(
                data -> {
                    try {
                        emitter.send(data);
                        response.add(data);
                        lastData.set(data);
                    } catch (IOException e) {
                        log.error(e.getMessage());
                        emitter.completeWithError(e);
                    }
                },
                (e) -> {
                    AiChatRecordContent responseContent = new AiChatRecordContent();
                    responseContent.setChatId(finalChatId);
                    responseContent.setChatContent(response.toJSONString(0));
                    responseContent.setContentId(IdGeneratorHelper.next());
                    responseContent.setQuestion(0);

                    chatRecordContentMapper.insert(responseContent);

                    emitter.completeWithError(e);
                },
                () -> {
                    if (StringUtils.isNotEmpty(lastData.get())) {
                        JSONObject jsonObject = JSONUtil.parseObj(lastData.get());
                        Boolean done = jsonObject.getBool("done", false);
                        if (done) {
                            String newContext = jsonObject.getStr("context");
                            chatRecordMapper.update(null, new LambdaUpdateWrapper<AiChatRecord>()
                                    .eq(AiChatRecord::getChatId, finalChatId)
                                    .set(AiChatRecord::getContext, newContext)
                                    .set(AiChatRecord::getUpdateTime, new Date()));
                        }
                    }

                    AiChatRecordContent responseContent = new AiChatRecordContent();
                    responseContent.setChatId(finalChatId);
                    responseContent.setChatContent(response.toJSONString(0));
                    responseContent.setContentId(IdGeneratorHelper.next());
                    responseContent.setQuestion(0);

                    chatRecordContentMapper.insert(responseContent);

                    emitter.complete();
                }

        );

        return emitter;
    }

    @Override
    public List<AiChatRecordVo> chatHistoryList() {
        return chatRecordMapper.selectVoList(new LambdaQueryWrapper<AiChatRecord>()
                .eq(AiChatRecord::getCreateBy, LoginHelper.getUsername())
                .orderByDesc(AiChatRecord::getUpdateTime)
                .select(AiChatRecord::getChatName, AiChatRecord::getChatId, AiChatRecord::getUpdateTime));
    }

    @Override
    public List<AiChatRecordContentVo> chatDetail(Long chatId) {
        List<AiChatRecordContentVo> aiChatRecordContentVos = chatRecordContentMapper.selectVoList(new LambdaQueryWrapper<AiChatRecordContent>()
                .eq(AiChatRecordContent::getChatId, chatId)
                .orderByAsc(AiChatRecordContent::getCreateTime));

        aiChatRecordContentVos.forEach(a -> {
            if (a.getQuestion() == 0) {
                a.setAnswerDetail(JSONUtil.parseArray(a.getChatContent()));
            }
        });

        return aiChatRecordContentVos;
    }

    @Override
    public Boolean rename(AiChatRecordBo bo) {
        AiChatRecord aiChatRecord = chatRecordMapper.selectById(bo.getChatId());
        if (ObjectUtil.isNotNull(aiChatRecord)) {
            aiChatRecord.setChatName(bo.getChatName());
            return chatRecordMapper.updateById(aiChatRecord) > 0;
        }
        return false;
    }

    @Override
    public Boolean deleteById(Long chatId) {
        chatRecordMapper.deleteById(chatId);
        chatRecordContentMapper.delete(new LambdaQueryWrapper<AiChatRecordContent>()
                .eq(AiChatRecordContent::getChatId, chatId));
        return true;
    }

    @Override
    public SseEmitter talking(ChatRequestDto dto) {
        SseEmitter emitter = new SseEmitter();

        Long chatId = dto.getChatId();

        if (!ObjectUtil.isNull(dto.getChatId())) {
            // 创建一个新对话
            AiChatRecord aiChatRecord = chatRecordMapper.selectById(chatId);
            if (ObjectUtil.isNull(aiChatRecord)) {
                chatId = null;
            }
        }

        if (ObjectUtil.isNull(chatId)) {
            chatId = IdGeneratorHelper.next();
            AiChatRecord newOne = new AiChatRecord();
            newOne.setChatId(chatId);
            newOne.setChatName(dto.getPrompt().length() > 15 ? dto.getPrompt().substring(0, 15) : dto.getPrompt());
            chatRecordMapper.insert(newOne);

            try {
                emitter.send(String.format(Header, chatId));
            } catch (IOException e) {
                emitter.completeWithError(e);
            }
        }

        List<AiChatRecordContentVo> aiChatRecordContentVos = chatRecordContentMapper.selectVoList(new LambdaQueryWrapper<AiChatRecordContent>()
                .eq(AiChatRecordContent::getChatId, chatId)
                .eq(AiChatRecordContent::getQuestion, 1).orderByAsc(AiChatRecordContent::getCreateTime));
        OllamaBase o = OllamaFactory.create("chat", dto.isStream());
        o.setModel(model);
        for (AiChatRecordContentVo aiChatRecordContentVo : aiChatRecordContentVos) {
            o.addMessage(aiChatRecordContentVo.getChatContent());
        }
        o.addMessage(dto.getPrompt());

        // 保存会话记录
        AiChatRecordContent chatRecordContent = new AiChatRecordContent();
        chatRecordContent.setChatId(chatId);
        chatRecordContent.setChatContent(dto.getPrompt());
        chatRecordContent.setQuestion(1);
        chatRecordContent.setContentId(IdGeneratorHelper.next());
        chatRecordContentMapper.insert(chatRecordContent);
        this.chat(emitter, o, chatId, "chat");

        return emitter;
    }

    private final static String prompt = "Using this data: %s ,读取出数据的{enterpriseName}字段值按列表输出给我 .";

    @Override
    public SseEmitter chatWithRag(String question) throws IOException, ExecutionException, InterruptedException, TimeoutException {
        SseEmitter emitter = new SseEmitter();
        Vector<Vector<Float>> embedding = QdrantClientUtil.getEmbedding(question);


        Set<Long> vectorDemoField = QdrantClientUtil.query("vector_demo_field", embedding.get(0), BusinessVectorDemoVo.getKeys());

        if (CollectionUtil.isNotEmpty(vectorDemoField)) {

            List<BusinessVectorDemoVo> businessVectorDemoVos = vectorDemoMapper.selectVoBatchIds(vectorDemoField, BusinessVectorDemoVo.class);

            OllamaChatRequestBody body = new OllamaChatRequestBody(true);
            body.setModel("deepseek");
            body.getMessages().add(new OllamaChatMessage("system",
                    "#### 定位\n" +
                            "- 智能助手名称 ：分析师\n" +
                            "- 主要任务 ：对输入的文本进行自动分类，识别其企业名字：enterpriseName。\n" +
                            "\n" +
                            "#### 能力\n" +
                            "- 文本分析 ：能够准确分析文本的内容和结构。\n" +
                            "- 分类识别 ：根据分析结果，将文本分类到预定义的种类中。\n" +
                            "\n" +
                            "#### 知识储备\n" +
                            "- 风险种类 ：\n" +
                            "  - 高危\n" +
                            "  - 风险\n" +
                            "  - 问题\n" +
                            "\n" +
                            "#### 使用说明\n" +
                            "- 输入 ：企业的详细列表，包括企业名称（enterpriseName）、风险总类(dangerLv)。\n" +
                            "- 输出 ：只输出文本企业名称字段：enterpriseName，不需要额外解释。"));

            body.getMessages().add(new OllamaChatMessage("system", "系统的知识库：" + JSONUtil.toJsonStr(businessVectorDemoVos)));
            body.addMessage(question);

            Flux<String> streamFlux = deepseekClient.post()
                    .uri("/v1/chat/completions")
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(BodyInserters.fromValue(body))
                    .retrieve()
                    .bodyToFlux(String.class);

            streamFlux.subscribe(
                    data -> {
                        try {
                            emitter.send(data);
                        } catch (IOException e) {
                            emitter.completeWithError(e);
                        }
                    },
                    (e) -> {
                        emitter.completeWithError(e);
                    },
                    () -> {
                        emitter.complete();
                    }
            );
        }

        return emitter;
    }


    private void chat(SseEmitter emitter, OllamaBase message, Long chatId, String type) {
        Flux<String> streamFlux = webClient.post()
                .uri("chat".equals(type) ? "/api/chat" : "/api/generate")
                .body(BodyInserters.fromValue(message))
                .retrieve()
                .bodyToFlux(String.class);

        JSONArray response = new JSONArray();
        AtomicReference<String> lastData = new AtomicReference<>();
        Long finalChatId = chatId;
        streamFlux.subscribe(
                data -> {
                    try {
                        emitter.send(data);
                        response.add(data);
                        lastData.set(data);
                    } catch (IOException e) {
                        emitter.completeWithError(e);
                    }
                },
                (e) -> {
                    AiChatRecordContent responseContent = new AiChatRecordContent();
                    responseContent.setChatId(finalChatId);
                    responseContent.setChatContent(response.toJSONString(0));
                    responseContent.setContentId(IdGeneratorHelper.next());
                    responseContent.setQuestion(0);
                    chatRecordContentMapper.insert(responseContent);
                    emitter.completeWithError(e);
                },
                () -> {
                    if (StringUtils.isNotEmpty(lastData.get())) {
                        JSONObject jsonObject = JSONUtil.parseObj(lastData.get());
                        Boolean done = jsonObject.getBool("done", false);
                        if (done) {
                            String newContext = jsonObject.getStr("context");
                            chatRecordMapper.update(null, new LambdaUpdateWrapper<AiChatRecord>()
                                    .eq(AiChatRecord::getChatId, finalChatId)
                                    .set(AiChatRecord::getContext, newContext)
                                    .set(AiChatRecord::getUpdateTime, new Date()));
                        }
                    }

                    AiChatRecordContent responseContent = new AiChatRecordContent();
                    responseContent.setChatId(finalChatId);
                    responseContent.setChatContent(response.toJSONString(0));
                    responseContent.setContentId(IdGeneratorHelper.next());
                    responseContent.setQuestion(0);

                    chatRecordContentMapper.insert(responseContent);

                    emitter.complete();
                }

        );
    }
}

