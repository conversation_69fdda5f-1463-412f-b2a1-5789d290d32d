package com.zhonghe.ai.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.zhonghe.ai.dto.ChatRequestDto;
import com.zhonghe.system.domain.bo.AiChatRecordBo;
import com.zhonghe.system.domain.vo.AiChatRecordContentVo;
import com.zhonghe.system.domain.vo.AiChatRecordVo;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeoutException;

/**
 * @description:
 * @author: cq
 * @date: 2025/2/24 10:35
 */

public interface IOllamaService {
    SseEmitter chatStream(ChatRequestDto dto) throws JsonProcessingException;

    List<AiChatRecordVo> chatHistoryList();

    List<AiChatRecordContentVo> chatDetail(Long chatId);

    Boolean rename(AiChatRecordBo bo);

    Boolean deleteById(Long chatId);

    SseEmitter talking(ChatRequestDto dto);

    SseEmitter chatWithRag(String question) throws IOException, ExecutionException, InterruptedException, TimeoutException;
}
