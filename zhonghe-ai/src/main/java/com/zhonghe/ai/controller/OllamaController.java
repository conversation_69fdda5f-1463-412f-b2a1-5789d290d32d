package com.zhonghe.ai.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.zhonghe.ai.dto.ChatRequestDto;
import com.zhonghe.ai.service.IOllamaService;
import com.zhonghe.common.annotation.Log;
import com.zhonghe.common.annotation.RepeatSubmit;
import com.zhonghe.common.core.controller.BaseController;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.common.enums.BusinessType;
import com.zhonghe.system.domain.bo.AiChatRecordBo;
import com.zhonghe.system.domain.vo.AiChatRecordContentVo;
import com.zhonghe.system.domain.vo.AiChatRecordVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeoutException;

/**
 * @description:
 * @author: cq
 * @date: 2025/2/24 10:32
 */
@Validated
@Api(value = "ollama接口", tags = {"AI聊天生成接口"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/ai/ollama")
public class OllamaController extends BaseController {

    private final IOllamaService ollamaService;


    @ApiOperation("对话")
    @SaCheckLogin
    @Log(title = "开始对话", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("chat")
    public SseEmitter chat(@RequestBody ChatRequestDto dto) throws JsonProcessingException {
        return ollamaService.chatStream(dto);
    }

    @ApiOperation("保留对话")
    @SaCheckLogin
    @Log(title = "开始保留对话", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("taking")
    public SseEmitter talking(@RequestBody ChatRequestDto dto) {
        return ollamaService.talking(dto);
    }


    @ApiOperation("对话历史列表")
    @SaCheckLogin
    @Log(title = "对话历史列表", businessType = BusinessType.OTHER)
    @GetMapping("history/list")
    public R<List<AiChatRecordVo>> list() {
        return R.ok(ollamaService.chatHistoryList());
    }

    @ApiOperation("对话历史详情")
    @SaCheckLogin
    @Log(title = "对话历史详情", businessType = BusinessType.OTHER)
    @GetMapping("history/{chatId}")
    public R<List<AiChatRecordContentVo>> detail(@PathVariable("chatId") Long chatId) {
        return R.ok(ollamaService.chatDetail(chatId));
    }

    @ApiOperation("对话历史重命名")
    @SaCheckLogin
    @Log(title = "对话历史重命名", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("rename")
    public R<Boolean> rename(@RequestBody AiChatRecordBo bo) {
        return R.ok(ollamaService.rename(bo));
    }

    @ApiOperation("删除对话历史")
    @SaCheckLogin
    @Log(title = "删除对话历史", businessType = BusinessType.DELETE)
    @RepeatSubmit()
    @DeleteMapping("del/{chatId}")
    public R<Boolean> rename(@PathVariable Long chatId) {
        return R.ok(ollamaService.deleteById(chatId));
    }


    @ApiOperation("测试")
    @SaCheckLogin
    @Log(title = "测试", businessType = BusinessType.OTHER)
    @GetMapping("promot")
    public SseEmitter test(@RequestParam("question") String question) throws IOException, ExecutionException, InterruptedException, TimeoutException {
        return ollamaService.chatWithRag(question);
    }

}

