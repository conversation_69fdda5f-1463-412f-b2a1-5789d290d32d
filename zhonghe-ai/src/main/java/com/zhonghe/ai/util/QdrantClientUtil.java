package com.zhonghe.ai.util;

import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.google.common.util.concurrent.ListenableFuture;
import com.zhonghe.ai.dto.EmbedVectorDto;
import com.zhonghe.common.utils.spring.SpringUtils;
import com.zhonghe.system.config.BaseConfig;
import io.qdrant.client.QdrantClient;
import io.qdrant.client.QdrantGrpcClient;
import io.qdrant.client.grpc.Collections;
import io.qdrant.client.grpc.Points;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.Vector;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

import static io.qdrant.client.PointIdFactory.id;
import static io.qdrant.client.QueryFactory.nearest;
import static io.qdrant.client.VectorsFactory.namedVectors;

/**
 * @description:
 * @author: cq
 * @date: 2025/2/27 9:32
 */

public class QdrantClientUtil {

    private static final QdrantClient client = new QdrantClient(QdrantGrpcClient.newBuilder(getOllamaConfig().getQdrantUrl()
        , getOllamaConfig().getPort(), false).build());


    private static BaseConfig getOllamaConfig() {
        return SpringUtils.getBean(BaseConfig.class);
    }


    public static Set<Long> query(String collectionName, Vector<Float> vector, List<String> using) throws IOException, ExecutionException, InterruptedException, TimeoutException {
        Set<Long> ids = new HashSet<>();
        for (String s : using) {
            List<Points.ScoredPoint> scoredPoints = client.queryAsync(Points.QueryPoints.newBuilder()
                .setCollectionName(collectionName)
                .setQuery(nearest(vector))
                .setUsing(s)
                .build()).get(5, TimeUnit.MINUTES);
            System.out.println(scoredPoints);

            scoredPoints.forEach(a -> {
                if (a.getScore() > 0.8) {
                    ids.add(a.getId().getNum());
                }
            });
        }
        return ids;
    }

    public static boolean upsertEmbedding(String collectionName, Map<Long, Map<String, Vector<Float>>> vectors, List<String> directions) throws Exception {

        if (!QdrantClientUtil.checkCollection(collectionName)) {
            if (!QdrantClientUtil.createCollection(collectionName, directions)) {
                throw new Exception("向量库创建失败：" + collectionName);
            }
        }

        List<Points.PointStruct> namedVectors = new ArrayList<>();

        vectors.forEach((id, vector) -> {
            Map<String, Points.Vector> vectorMap = new HashMap<>(vector.size());
            vector.forEach((k, v) -> {
                Points.Vector.Builder builder = Points.Vector.newBuilder().addAllData(v);
                vectorMap.put(k, builder.build());
            });

            namedVectors.add(Points.PointStruct.newBuilder()
                .setId(id(id))
                .setVectors(namedVectors(vectorMap))
                .build());
        });

        ListenableFuture<Points.UpdateResult> updateResultListenableFuture = client.upsertAsync(collectionName, namedVectors);


        Points.UpdateResult updateResult = updateResultListenableFuture.get(5, TimeUnit.MINUTES);
        if (updateResult.getStatus() != Points.UpdateStatus.Completed) {
            throw new Exception("数据存储异常" + updateResult.getStatus());
        }
        return true;
    }

    private static boolean checkCollection(String collectionName) throws ExecutionException, InterruptedException, TimeoutException {
        return client.collectionExistsAsync(collectionName).get(1, TimeUnit.MINUTES);
    }

    private static boolean createCollection(String collectionName, List<String> vectors) throws ExecutionException, InterruptedException, TimeoutException {

        Map<String, Collections.VectorParams> r = new HashMap<>(vectors.size());

        vectors.forEach(a -> r.put(a, Collections.VectorParams.newBuilder().setSize(768).setDistance(Collections.Distance.Dot).build()));

        Collections.CollectionOperationResponse vectorOne = client.createCollectionAsync(
            collectionName, r).get(1, TimeUnit.MINUTES);
                /*MapUtil.of(  "vector_one",
                    Collections.VectorParams.newBuilder().setSize(768).setDistance(Collections.Distance.Dot).build()),
                )*/
                /*java.util.Collections.singletonMap(

                ))*/


        return vectorOne.getResult();
    }


    public static Vector<Vector<Float>> getEmbedding(String text) {
        JSONObject body = new JSONObject();
        body.putIfAbsent("model", "nomic-embed-text");
        body.putIfAbsent("input", text);

        HttpResponse response = HttpUtil.createPost(getOllamaConfig().getUrl() + "/api/embed").body(JSONUtil.toJsonStr(body)).execute();

        EmbedVectorDto vectorDto = JSONUtil.toBean(response.body(), EmbedVectorDto.class);
        return vectorDto.getEmbeddings();
    }


    public static Vector<Vector<Float>> getEmbedding(Collection<String> texts) {
        JSONObject body = new JSONObject();
        body.putIfAbsent("model", "nomic-embed-text");
        body.putIfAbsent("input", JSONUtil.toJsonStr(texts));

        HttpResponse response = HttpUtil.createPost(getOllamaConfig().getUrl() + "/api/embed").body(JSONUtil.toJsonStr(body)).execute();

        EmbedVectorDto vectorDto = JSONUtil.toBean(response.body(), EmbedVectorDto.class);
        return vectorDto.getEmbeddings();
    }

}

