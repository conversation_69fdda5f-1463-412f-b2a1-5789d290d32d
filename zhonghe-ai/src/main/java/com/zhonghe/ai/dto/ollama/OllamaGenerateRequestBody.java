package com.zhonghe.ai.dto.ollama;

import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * @description:
 * @author: cq
 * @date: 2025/2/24 11:24
 */


public class OllamaGenerateRequestBody extends OllamaBase {

    public OllamaGenerateRequestBody(boolean stream) {
        super(stream);
    }

    @Getter
    @Setter
    private String model;
    @Getter
    @Setter
    private String prompt;
    @Getter
    @Setter
    private List<Integer> context;

    @Override
    public void addMessage(String msg) {
        this.prompt = msg;
    }
}

