package com.zhonghe.ai.dto.ollama;

import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * @description:
 * @author: cq
 * @date: 2025/2/24 18:12
 */

public class OllamaChatRequestBody extends OllamaBase {

    public OllamaChatRequestBody(boolean stream) {
        super(stream);
    }

    @Getter
    private final List<OllamaChatMessage> messages = new ArrayList<>();

    @Override
    public void addMessage(String msg) {
        messages.add(new OllamaChatMessage("user", msg));
    }
}

