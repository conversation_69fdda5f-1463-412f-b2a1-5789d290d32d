package com.zhonghe.ai.dto.ollama;

import com.zhonghe.common.exception.base.BaseException;

/**
 * @description:
 * @author: cq
 * @date: 2025/2/24 18:18
 */

public class OllamaFactory {


    public static OllamaBase create(String type, boolean stream) {
        if ("chat".equals(type)) {
            OllamaChatRequestBody ollamaChatRequestBody = new OllamaChatRequestBody(stream);
            ollamaChatRequestBody.getMessages().add(new OllamaChatMessage("system", "你是AI助手。"));
            return ollamaChatRequestBody;
        } else if ("generate".equals(type)) {
            return new OllamaGenerateRequestBody(stream);
        }
        throw new BaseException("创建异常");
    }
}

