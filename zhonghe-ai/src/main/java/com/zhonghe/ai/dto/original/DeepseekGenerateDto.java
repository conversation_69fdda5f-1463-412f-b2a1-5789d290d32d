package com.zhonghe.ai.dto.original;

import lombok.Data;

/**
 * @description:
 * @author: cq
 * @date: 2025/3/4 9:26
 */

@Data
public class DeepseekGenerateDto {

    private String prompt;

    private String context;
    /**
     *
     *  "max_tokens": 512,
     *  "repetition_penalty": 1.03,
     *  "presence_penalty": 1.2,
     *  "frequency_penalty": 1.2,
     *  "temperature": 0.5,
     *  "top_k": 10,
     *  "top_p": 0.95,
     *  "stream": false
     *
     */

    private int max_tokens = 2048;

    private float repetition_penalty = 1.03f;
    private float presence_penalty = 1.2f;
    private float frequency_penalty = 1.2f;
    private float temperature = 0.5f;
    private int top_k = 10;
    private float top_p = 0.95f;
    private boolean stream = true;

}

