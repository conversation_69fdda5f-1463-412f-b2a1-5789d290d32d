<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhonghe.system.mapper.AiChatRecordMapper">

    <resultMap type="com.zhonghe.system.domain.AiChatRecord" id="AiChatRecordResult">
        <result property="chatId" column="chat_id"/>
        <result property="chatName" column="chat_name"/>
        <result property="context" column="context"/>
    </resultMap>
    <delete id="deleteById">
        delete from ai.ai_chat_record
        <where>chat_id = #{chatId}</where>
    </delete>
    <select id="selectById" resultType="com.zhonghe.system.domain.AiChatRecord">
        select * from ai.ai_chat_record
        <where>
            chat_id =#{chatId}
        </where>
    </select>


</mapper>
