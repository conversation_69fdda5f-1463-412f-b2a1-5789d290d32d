CREATE TABLE ai.ai_chat_record
(
    chat_id     bigint primary key not null,
    chat_name   varchar(32) default null,
    context     text        default null,
    create_by   varchar(64)        NULL,
    create_time timestamp          NULL,
    update_by   varchar(64)        NULL,
    update_time timestamp          NULL
);
COMMENT ON TABLE ai.ai_chat_record IS '对话记录';
COMMENT ON COLUMN ai.ai_chat_record.chat_name IS '对话名称';

CREATE TABLE ai.ai_chat_record_content
(
    content_id   bigint primary key not null,
    chat_id      bigint             not null,
    chat_content text default null,
    question     int2 default 1,
    create_by    varchar(64)        NULL,
    create_time  timestamp          NULL,
    update_by    varchar(64)        NULL,
    update_time  timestamp          NULL
);
COMMENT ON TABLE ai.ai_chat_record_content IS '对话记录';
COMMENT ON COLUMN ai.ai_chat_record_content.chat_content IS '对话内容';

