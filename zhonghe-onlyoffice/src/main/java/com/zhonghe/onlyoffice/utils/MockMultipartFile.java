package com.zhonghe.onlyoffice.utils;

import org.springframework.lang.Nullable;
import org.springframework.util.Assert;
import org.springframework.util.FileCopyUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;

public class MockMultipartFile implements MultipartFile {
    private final String name;
    private final byte[] content;
    private String originalFilename;
    @Nullable
    private String contentType;

    public MockMultipartFile(String name, @Nullable byte[] content) {
        this(name, "", (String) null, (byte[]) content);
    }

    public MockMultipartFile(String name, InputStream contentStream) throws IOException {
        this(name, "", (String) null, (byte[]) FileCopyUtils.copyToByteArray(contentStream));
    }

    public MockMultipartFile(String name, @Nullable String originalFilename, @Nullable String contentType, @Nullable byte[] content) {
        Assert.hasLength(name, "Name must not be null");
        this.name = name;
        this.originalFilename = originalFilename != null ? originalFilename : "";
        this.contentType = contentType;
        this.content = content != null ? content : new byte[0];
    }

    public MockMultipartFile(String name, @Nullable String originalFilename, @Nullable String contentType, InputStream contentStream) throws IOException {
        this(name, originalFilename, contentType, FileCopyUtils.copyToByteArray(contentStream));
    }

    public String getName() {
        return name;
    }

    public String getOriginalFilename() {
        return originalFilename;
    }

    @Nullable
    public String getContentType() {
        return contentType;
    }

    public boolean isEmpty() {
        return content.length == 0;
    }

    public long getSize() {
        return (long) content.length;
    }

    public byte[] getBytes() throws IOException {
        return content;
    }

    public InputStream getInputStream() throws IOException {
        return new ByteArrayInputStream(content);
    }

    public void transferTo(File dest) throws IOException, IllegalStateException {
        FileCopyUtils.copy(content, dest);
    }
}
