package com.zhonghe.onlyoffice.utils;

import java.net.URLConnection;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: lpg
 * @Description: {文件类型工具类}
 */
public class ContentTypeUtils {
    private static final Map<String, String> MIME_MAP = new HashMap<>();

    static {
        // 初始化常见MIME类型映射
        MIME_MAP.put("pdf", "application/pdf");
        MIME_MAP.put("doc", "application/msword");
        MIME_MAP.put("docx", "application/vnd.openxmlformats-officedocument.wordprocessingml.document");
        MIME_MAP.put("xls", "application/vnd.ms-excel");
        MIME_MAP.put("xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        MIME_MAP.put("ppt", "application/vnd.ms-powerpoint");
        MIME_MAP.put("pptx", "application/vnd.openxmlformats-officedocument.presentationml.presentation");
        MIME_MAP.put("jpg", "image/jpeg");
        MIME_MAP.put("jpeg", "image/jpeg");
        MIME_MAP.put("png", "image/png");
        MIME_MAP.put("gif", "image/gif");
        MIME_MAP.put("bmp", "image/bmp");
        MIME_MAP.put("svg", "image/svg+xml");
        MIME_MAP.put("txt", "text/plain");
        MIME_MAP.put("html", "text/html");
        MIME_MAP.put("htm", "text/html");
        MIME_MAP.put("css", "text/css");
        MIME_MAP.put("js", "application/javascript");
        MIME_MAP.put("json", "application/json");
        MIME_MAP.put("xml", "application/xml");
        MIME_MAP.put("mp3", "audio/mpeg");
        MIME_MAP.put("wav", "audio/wav");
        MIME_MAP.put("mp4", "video/mp4");
        MIME_MAP.put("avi", "video/x-msvideo");
        MIME_MAP.put("zip", "application/zip");
        MIME_MAP.put("rar", "application/x-rar-compressed");
        MIME_MAP.put("7z", "application/x-7z-compressed");
    }

    /**
     * 根据文件名获取Content-Type
     *
     * @param fileName 文件名
     * @return MIME类型
     */
    public static String getContentType(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return "application/octet-stream";
        }

        // 获取文件扩展名
        int dotIndex = fileName.lastIndexOf('.');
        if (dotIndex == -1 || dotIndex == fileName.length() - 1) {
            return "application/octet-stream";
        }

        String extension = fileName.substring(dotIndex + 1).toLowerCase();

        // 1. 先从自定义映射中查找
        String contentType = MIME_MAP.get(extension);
        if (contentType != null) {
            return contentType;
        }

        // 2. 使用URLConnection内置方法
        contentType = URLConnection.guessContentTypeFromName(fileName);
        if (contentType != null) {
            return contentType;
        }

        // 3. 返回默认类型
        return "application/octet-stream";
    }
}
