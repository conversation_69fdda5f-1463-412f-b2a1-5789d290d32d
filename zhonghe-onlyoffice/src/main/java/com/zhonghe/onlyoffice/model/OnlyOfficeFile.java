package com.zhonghe.onlyoffice.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class OnlyOfficeFile implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 文件id
     */
    private Long fileId;

    /**
     * 文件唯一标识
     */
    private String fileKey;

    /**
     * 文件名称
     */
    private String title;

    /**
     * 文件名称
     */
    private String name;

    /**
     * 文件后缀
     */
    private String suffix;

    /**
     * 文件content-type
     */
    private String contentType;

    /**
     * 文件大小
     */
    private Long length;

    /**
     * 文件真实地址
     */
    private String url;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 跟新时间
     */
    private Date updateTime;

    /**
     * 跟新人
     */
    private String updateBy;


    /**
     * md5值
     */
    private String md5;


}
