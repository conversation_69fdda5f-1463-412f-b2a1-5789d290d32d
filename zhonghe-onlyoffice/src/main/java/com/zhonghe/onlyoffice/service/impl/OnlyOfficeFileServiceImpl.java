package com.zhonghe.onlyoffice.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zhonghe.onlyoffice.model.OnlyOfficeFile;
import com.zhonghe.onlyoffice.model.documentServer.Config;
import com.zhonghe.onlyoffice.model.vo.OnlyOfficeFileVo;
import com.zhonghe.onlyoffice.service.OnlyOfficeFileService;
import com.zhonghe.onlyoffice.utils.ContentTypeUtils;
import com.zhonghe.system.domain.SysOss;
import com.zhonghe.system.service.ISysOssService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;


@RequiredArgsConstructor
@Service
public class OnlyOfficeFileServiceImpl implements OnlyOfficeFileService {

    private final ISysOssService sysOssService;

    @Override
    public List<OnlyOfficeFile> queryByIds(List<Long> ids) {
        LambdaQueryWrapper<OnlyOfficeFile> wrapper = new LambdaQueryWrapper<>();
        return new ArrayList<>();
    }

    @Override
    public OnlyOfficeFile queryById(Long fileId) {
        OnlyOfficeFile onlyOfficeFile = new OnlyOfficeFile();
        SysOss sysOss = sysOssService.getById(fileId);
        onlyOfficeFile.setFileId(sysOss.getOssId());
        onlyOfficeFile.setFileKey(String.valueOf(sysOss.getOssId()));
        onlyOfficeFile.setTitle(sysOss.getOriginalName());
        onlyOfficeFile.setName(sysOss.getOriginalName());
        onlyOfficeFile.setSuffix(sysOss.getFileSuffix().replaceAll("\\.", ""));
        onlyOfficeFile.setContentType(ContentTypeUtils.getContentType(sysOss.getFileName()));
        onlyOfficeFile.setUrl(sysOss.getFileName());
        onlyOfficeFile.setCreateTime(sysOss.getCreateTime());
        onlyOfficeFile.setCreateBy(sysOss.getCreateBy());
        onlyOfficeFile.setUpdateTime(sysOss.getUpdateTime());
        onlyOfficeFile.setUpdateBy(sysOss.getUpdateBy());

        return onlyOfficeFile;
    }

    @Override
    public OnlyOfficeFile saveOnlyOfficeFile(OnlyOfficeFileVo onlyOfficeFileVo) {
        OnlyOfficeFile onlyOfficeFile = new OnlyOfficeFile();
        BeanUtils.copyProperties(onlyOfficeFileVo, onlyOfficeFile);
        onlyOfficeFile.setFileId(null);
        if (StringUtils.isNotBlank(onlyOfficeFileVo.getTitle())) {
            onlyOfficeFile.setName(onlyOfficeFileVo.getName());
            onlyOfficeFile.setSuffix(onlyOfficeFileVo.getSuffix());
        }
        return null;
    }

    @Override
    public Config buildConfigByFileId(Long fileId) {
        Config config = new Config();


        return config;
    }
}
