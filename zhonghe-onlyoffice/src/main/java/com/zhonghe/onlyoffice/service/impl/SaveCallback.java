package com.zhonghe.onlyoffice.service.impl;


import com.zhonghe.onlyoffice.dto.CallbackDTO;
import com.zhonghe.onlyoffice.model.OnlyOfficeFile;
import com.zhonghe.onlyoffice.model.documentServer.Status;
import com.zhonghe.onlyoffice.service.Callback;
import com.zhonghe.onlyoffice.service.OnlyOfficeFileService;
import com.zhonghe.onlyoffice.utils.MD5Util;
import com.zhonghe.onlyoffice.utils.MockMultipartFile;
import com.zhonghe.system.domain.SysOss;
import com.zhonghe.system.service.ISysOssService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

@Service
public class SaveCallback implements Callback {
    @Autowired
    private OnlyOfficeFileService onlyOfficeFileService;
    @Autowired
    private ISysOssService sysOssService;

    @Autowired
    private MD5Util md5Util;

    @Override
    public int handle(CallbackDTO body, Long fileId) {
        OnlyOfficeFile onlyOfficeFile = onlyOfficeFileService.queryById(fileId);
        byte[] bytes = sysOssService.getBytesById(fileId);
        MultipartFile multipartFile = new MockMultipartFile(onlyOfficeFile.getTitle(), onlyOfficeFile.getTitle(), onlyOfficeFile.getContentType(), bytes);
        SysOss sysOss = sysOssService.uploadFile(multipartFile);



    /*    OnlyOfficeFileVo temp = new OnlyOfficeFileVo();
        temp.setId(onlyOfficeFile.get());
        temp.setUrl("");
        String md5 = md5Util.encrypt(bytes);
        temp.setMd5(md5);
        temp.setFileKey(md5Util.key(md5));
        temp.setTitle(onlyOfficeFile.getTitle());
        onlyOfficeFileService.updateById(temp);*/
        // TODO 历史记录信息
        return 0;
    }

    @Override
    public int getStatus() {
        return Status.SAVE.getCode();
    }
}
