package com.zhonghe.onlyoffice.service.impl;

import com.zhonghe.onlyoffice.config.OnlyOfficeProperties;
import com.zhonghe.onlyoffice.model.documentServer.DefaultCustomizationWrapper;
import com.zhonghe.onlyoffice.model.documentServer.EditorConfig;
import com.zhonghe.onlyoffice.service.CustomizationConfigurer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class CustomizationConfigurerImpl implements CustomizationConfigurer<DefaultCustomizationWrapper> {
    @Autowired
    private OnlyOfficeProperties onlyOfficeProperties;

    @Override
    public void configure(EditorConfig.Customization customization, DefaultCustomizationWrapper wrapper) {
        customization.setSubmitForm(false);
        customization.setLogo(onlyOfficeProperties.getLogo());
        EditorConfig.Customization.Goback goback = new EditorConfig.Customization.Goback();
        goback.setUrl(onlyOfficeProperties.getGoBack());
        customization.setGoback(goback);
    }
}
