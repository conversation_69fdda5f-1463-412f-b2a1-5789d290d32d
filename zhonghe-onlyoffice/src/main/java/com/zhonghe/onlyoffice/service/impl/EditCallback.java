package com.zhonghe.onlyoffice.service.impl;

import com.zhonghe.onlyoffice.dto.CallbackDTO;
import com.zhonghe.onlyoffice.model.documentServer.Status;
import com.zhonghe.onlyoffice.service.Callback;
import org.springframework.stereotype.Service;

@Service
public class EditCallback implements Callback {
    @Override
    public int handle(CallbackDTO body, Long fileId) {
        return 0;
    }

    @Override
    public int getStatus() {
        return Status.EDITING.getCode();
    }
}
