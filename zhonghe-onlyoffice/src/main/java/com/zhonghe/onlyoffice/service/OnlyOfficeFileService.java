package com.zhonghe.onlyoffice.service;

import com.zhonghe.onlyoffice.model.OnlyOfficeFile;
import com.zhonghe.onlyoffice.model.documentServer.Config;
import com.zhonghe.onlyoffice.model.vo.OnlyOfficeFileVo;

import java.util.List;

public interface OnlyOfficeFileService {

    List<OnlyOfficeFile> queryByIds(List<Long> ids);

    OnlyOfficeFile queryById(Long id);

    OnlyOfficeFile saveOnlyOfficeFile(OnlyOfficeFileVo onlyOfficeFileVo);

    /**
     * 根据文件的id构建文档编辑器需要的config对象
     */
    Config buildConfigByFileId(Long fileId);

}
