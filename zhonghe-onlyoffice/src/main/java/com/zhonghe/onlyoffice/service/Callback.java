package com.zhonghe.onlyoffice.service;

import com.zhonghe.onlyoffice.dto.CallbackDTO;
import com.zhonghe.onlyoffice.service.impl.CallbackHandler;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * 处理文档服务器返回的数据
 */
public interface Callback {
    int handle(CallbackDTO body, Long fileId);

    int getStatus();

    @Autowired
    default void selfRegistration(CallbackHandler callbackHandler) {
        callbackHandler.register(getStatus(), this);
    }
}
