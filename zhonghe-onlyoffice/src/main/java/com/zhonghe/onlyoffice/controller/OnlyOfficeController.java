package com.zhonghe.onlyoffice.controller;

import com.zhonghe.onlyoffice.config.OnlyOfficeProperties;
import com.zhonghe.onlyoffice.dto.CallbackDTO;
import com.zhonghe.onlyoffice.model.OnlyOfficeFile;
import com.zhonghe.onlyoffice.model.documentServer.Action;
import com.zhonghe.onlyoffice.model.documentServer.Config;
import com.zhonghe.onlyoffice.model.documentServer.DefaultFileWrapper;
import com.zhonghe.onlyoffice.model.documentServer.EditorConfig;
import com.zhonghe.onlyoffice.service.FileConfigurer;
import com.zhonghe.onlyoffice.service.OnlyOfficeFileService;
import com.zhonghe.onlyoffice.service.impl.CallbackHandler;
import com.zhonghe.onlyoffice.utils.MD5Util;
import com.zhonghe.system.service.ISysOssService;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Controller
@RequestMapping("/onlyoffice")
public class OnlyOfficeController {

    private final Logger logger = LoggerFactory.getLogger(OnlyOfficeController.class);

    @Autowired
    private OnlyOfficeFileService onlyOfficeFileService;
    @Autowired
    private ISysOssService sysOssService;
    @Autowired
    private MD5Util md5Util;
    @Autowired
    private OnlyOfficeProperties onlyOfficeProperties;
    @Autowired
    private FileConfigurer<DefaultFileWrapper> fileConfigurer;
    @Autowired
    private CallbackHandler callbackHandler;

    /**
     *
     */

    /**
     * 编辑页面
     */
    @GetMapping("/index/{fileId}")
    public String index(@PathVariable("fileId") Long fileId,
                        @RequestParam(value = "type", defaultValue = "desktop") String type,
                        @RequestParam(value = "mode", defaultValue = "view") String mode,
                        Model model) {
        OnlyOfficeFile onlyOfficeFile = onlyOfficeFileService.queryById(fileId);
        Assert.isTrue(ObjectUtils.isNotEmpty(onlyOfficeFile), "file is not fond!");
        Action action = mode.equals("view") ? Action.view : Action.edit;

        Config config = fileConfigurer.getFileModel(DefaultFileWrapper.builder()
            .fileName(onlyOfficeFile.getTitle())
            .onlyOfficeFile(onlyOfficeFile)
            .action(action)
            .user(getUser())
            .type(type)
            .build());
        model.addAttribute("config", config);
        model.addAttribute("api", onlyOfficeProperties.getApi());
        return "onlyoffice/index";
    }

    @GetMapping("/downloadFile/{fileId}")
    public void downloadFile(@PathVariable("fileId") Long fileId, HttpServletRequest request, HttpServletResponse response) throws Exception {
        OnlyOfficeFile onlyOfficeFile = onlyOfficeFileService.queryById(fileId);
        if (ObjectUtils.isNotEmpty(onlyOfficeFile)) {
            sysOssService.downloadFromOss(fileId, request, response);
        } else {
            response.getWriter().write("error");
        }
    }

    @ResponseBody
    @PostMapping("/callback")
    public String track(HttpServletRequest request,
                        @RequestParam("fileId") Long fileId,
                        @RequestBody CallbackDTO body) {
        logger.info("============fileId:{} ===callback==={}", fileId, body);
        int error = callbackHandler.handle(body, fileId);
        return "{\"error\":" + error + "}";
    }

    /**
     * 根据当前登录人设置登录人、名称、分组      本系统只接入编辑与协同
     */
    private EditorConfig.User getUser() {
        EditorConfig.User user = new EditorConfig.User();
        user.setFavorite(true);
        user.setId(String.valueOf(1));
        user.setName(String.valueOf(1));
        user.setGroup("default");
        return user;
    }

}
