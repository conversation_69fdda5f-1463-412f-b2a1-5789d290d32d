package com.zhonghe.onlyoffice.controller;


import com.zhonghe.common.core.domain.R;
import com.zhonghe.onlyoffice.model.OnlyOfficeFile;
import com.zhonghe.onlyoffice.model.vo.Flag;
import com.zhonghe.onlyoffice.model.vo.OnlyOfficeFileVo;
import com.zhonghe.onlyoffice.service.OnlyOfficeFileService;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


@RestController("/onlyoffice/file")
public class OnlyOfficeFileController {
    private final OnlyOfficeFileService onlyOfficeFileService;

    public OnlyOfficeFileController(OnlyOfficeFileService onlyOfficeFileService) {
        this.onlyOfficeFileService = onlyOfficeFileService;
    }


    @GetMapping("/{id}")
    public R<OnlyOfficeFile> queryById(@PathVariable("id") Long id) {
        OnlyOfficeFile onlyOfficeFile = onlyOfficeFileService.queryById(id);
        if (ObjectUtils.isEmpty(onlyOfficeFile)) {
            return R.fail("query file is not fond by id: " + id);
        }
        return R.ok(onlyOfficeFile);
    }

    @PostMapping("/ids")
    public R<List<OnlyOfficeFile>> queryByIds(@RequestParam("ids") List<Long> ids) {
        List<OnlyOfficeFile> items = onlyOfficeFileService.queryByIds(ids);
        if (CollectionUtils.isEmpty(items)) {
            return R.fail("query files is not fond by ids: " + ids);
        }
        return R.ok(items);
    }

    @PostMapping("")
    public R<OnlyOfficeFile> saveOnlyOfficeFile(@Validated(Flag.class) @RequestBody OnlyOfficeFileVo onlyOfficeFileVo) {
        OnlyOfficeFile onlyOfficeFile = onlyOfficeFileService.saveOnlyOfficeFile(onlyOfficeFileVo);
        if (ObjectUtils.isEmpty(onlyOfficeFile)) {
            return R.fail("save OnlyOfficeFile is error");
        }
        return R.ok(onlyOfficeFile);
    }
}

