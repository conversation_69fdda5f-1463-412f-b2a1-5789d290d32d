<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.zhonghe</groupId>
        <artifactId>zhonghe-vue-plus</artifactId>
        <version>2.0</version>
    </parent>

    <artifactId>zhonghe-fengqiao</artifactId>
    <packaging>jar</packaging>

    <name>zhonghe-fengqiao</name>
    <url>http://maven.apache.org</url>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>3.8.1</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.zhonghe</groupId>
            <artifactId>zhonghe-system</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zhonghe</groupId>
            <artifactId>zhonghe-cop</artifactId>
        </dependency>
    </dependencies>
</project>
