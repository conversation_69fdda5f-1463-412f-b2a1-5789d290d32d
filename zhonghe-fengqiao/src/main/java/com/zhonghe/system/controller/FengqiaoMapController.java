package com.zhonghe.system.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.zhonghe.common.core.controller.BaseController;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.system.domain.dto.FengqiaoDangerEnterpriseStatisticDto;
import com.zhonghe.system.domain.dto.FengqiaoEnterpriseDangerDto;
import com.zhonghe.system.service.IBigScreenInformationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @description: 风险一张图统计接口功能
 * @author: cq
 * @date: 2023/11/10 11:28
 */

@Api(value = "风险一张图统计接口功能", tags = {"风险一张图统计接口功能"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/com/zhonghe/system/fengqiao")
public class FengqiaoMapController extends BaseController {

    private final IBigScreenInformationService itCopInformationService;

    @ApiOperation("风险预警")
    @SaCheckPermission("system:fengqiao:query")
    @GetMapping("/danger/statistic")
    public R<List<FengqiaoEnterpriseDangerDto>> dangerStatistic(@RequestParam(value = "queryMode") Integer queryMode, @RequestParam(value = "keyWord", required = false) String keyWord) {
        return R.ok(itCopInformationService.reportDangerList(queryMode, keyWord));
    }

    @ApiOperation("企业风险数量统计")
    @SaCheckPermission("system:fengqiao:query")
    @GetMapping("/danger/enterprise/statistic")
    public R<FengqiaoDangerEnterpriseStatisticDto> dangerEnterpriseStatistic() {
        return R.ok(itCopInformationService.dangerEnterpriseStatistic());
    }
}

