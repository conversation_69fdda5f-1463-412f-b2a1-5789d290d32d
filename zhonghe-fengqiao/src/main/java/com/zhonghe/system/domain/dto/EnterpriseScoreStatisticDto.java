package com.zhonghe.system.domain.dto;

import com.zhonghe.common.utils.JsonUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * @description:
 * @author: cq
 * @date: 2023/11/10 14:33
 */

@Data
@ApiModel("企业信用评分统计")
public class EnterpriseScoreStatisticDto {

    public String toData() {
        Map<String, Long> data = new HashMap<>();
        data.put("总数", count);
        data.put("0-60得分数", below60);
        data.put("60-80的分数", below80);
        data.put("80-100得分数", below100);
        data.put("100-110得分数", below110);
        data.put("110-120得分数", below120);


        return JsonUtils.toJsonString(data);
    }

    @ApiModelProperty("总数")
    private Long count;


    @ApiModelProperty("0-60得分数")
    private Long below60;

    @ApiModelProperty("60-80的分数")
    private Long below80;


    @ApiModelProperty("80-100得分数")
    private Long below100;

    @ApiModelProperty("100-110得分数")
    private Long below110;

    @ApiModelProperty("110-120得分数")
    private Long below120;
}

