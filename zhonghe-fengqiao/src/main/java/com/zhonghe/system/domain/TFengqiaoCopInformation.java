package com.zhonghe.system.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zhonghe.common.core.domain.BaseEntity;
import com.zhonghe.system.mappedtypes.GeometryTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.postgis.jdbc.geometry.Geometry;


/**
 * 企业信息对象 t_cop_information
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("cop.t_cop_information")
public class TFengqiaoCopInformation extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 企业ID
     */
    @TableId
    private Long copId;
    /**
     * 是否删除
     */
    @TableLogic
    private String delFlag;
    /**
     * 备注
     */
    private String note;
    /**
     * 企业名称
     */
    private String copName;
    /**
     * 企业编号
     */
    private String copCode;
    /**
     * 企业法人
     */
    private String copOwner;
    /**
     * 企业负责人
     */
    private String copIncharge;
    /**
     * 组织机构代码证
     */
    private String corporateCode;
    /**
     * 营业执照注册号
     */
    private String businessLicence;
    /**
     * 税务登记证编号
     */
    private String taxRegistration;
    /**
     * 企业人员规模
     */
    private Integer copPopulation;
    /**
     * 企业地址
     */
    private String copAddress;
    /**
     * 企业电话
     */
    private String copPhone;
    /**
     * 企业传真
     */
    private String copFax;
    /**
     * 办公场地属性（默认0，0——租用、1——自建、2——转租）
     */
    private Integer officeProperty;
    /**
     * 厂房业主
     */
    private String officeOwner;
    /**
     * 厂房业主电话
     */
    private String officeOwnerPhone;
    /**
     * 企业性质ID
     */
    private Long propertyId;
    /**
     * 机构ID
     */
    private Long orgId;
    /**
     * 经度
     */
    private String lon;
    /**
     * 纬度
     */
    private String lat;
    /**
     * 是否注销（默认0，0——未注销、1——已注销）
     */
    private Integer isCancel;
    /**
     * 经营范围
     */
    private String scopeOfBusiness;
    /**
     * 企业联系人
     */
    private String copContacts;
    /**
     * 厂房租赁期限
     */
    private String leaseTerm;
    /**
     * 备用字段1
     */
    private String by1;
    /**
     * 备用字段2
     */
    private String by2;

    @TableField(typeHandler = GeometryTypeHandler.class)
    private Geometry geom;

}
