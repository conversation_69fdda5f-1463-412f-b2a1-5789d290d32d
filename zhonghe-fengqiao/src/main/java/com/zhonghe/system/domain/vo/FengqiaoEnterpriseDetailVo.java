package com.zhonghe.system.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zhonghe.system.mappedtypes.GeometryTypeHandler;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.postgis.jdbc.geometry.Geometry;

import java.util.Date;

/**
 * @description:
 * @author: cq
 * @date: 2023/12/16 18:14
 */

@Data
@ApiModel("企业详情")
@JsonSerialize(include = JsonSerialize.Inclusion.ALWAYS)
public class FengqiaoEnterpriseDetailVo {

    @ApiModelProperty("id")
    private Long enterpriseId;

    @ApiModelProperty("企业名称")
    private String name;

    @ApiModelProperty("企业类型")
    private String enterpriseType;


    @ApiModelProperty("企业地址")
    private String address;

    @ApiModelProperty("企业电话")
    private String phone;

    @TableField(typeHandler = GeometryTypeHandler.class)
    @JsonIgnore
    private Geometry geom;

    /**
     * 企业法人
     */
    @ApiModelProperty("企业法人")
    private String legalPerson;

    /**
     * 上报原因
     */
    @ApiModelProperty(value = "上报原因")
    private String repReason;
    /**
     * 上报日期
     */
    @ApiModelProperty(value = "上报日期")
    @JsonFormat(pattern = "yyyy-MM", timezone = "GMT+8")
    private Date repDate;

    /**
     * 其他问题
     */
    @ApiModelProperty(value = "其他问题")
    private String problem;

    /**
     * 监管措施
     */
    @ApiModelProperty(value = "监管措施")
    private String copoversee;

    /**
     * 建议措施
     */
    @ApiModelProperty(value = "建议措施")
    private String copadvice;

    /**
     * 采取措施
     */
    @ApiModelProperty(value = "采取措施")
    private String copdeal;

    /**
     * 企业现状
     */
    @ApiModelProperty(value = "企业现状")
    private String copresult;

    /**
     * 后续工作
     */
    @ApiModelProperty(value = "后续工作")
    private String copafterwork;

    /**
     * 请求协助
     */
    @ApiModelProperty(value = "请求协助")
    private String cophelp;
    /**
     * 上报部门
     */
    @ApiModelProperty(value = "上报部门")
    private String repDept;


}

