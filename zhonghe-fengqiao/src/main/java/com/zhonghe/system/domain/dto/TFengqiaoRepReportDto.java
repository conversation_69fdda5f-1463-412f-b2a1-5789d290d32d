package com.zhonghe.system.domain.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.zhonghe.common.annotation.ExcelDictFormat;
import com.zhonghe.common.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @description:
 * @author: cq
 * @date: 2023/12/18 15:59
 */

@Data
@ApiModel("部门上报视图对象")
public class TFengqiaoRepReportDto {

    /**
     * 部门上报ID
     */
    @ExcelProperty(value = "部门上报ID")
    @ApiModelProperty("部门上报ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long repReportId;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    @ApiModelProperty("备注")
    private String note;

    /**
     * 上报日期
     */
    @ExcelProperty(value = "上报日期")
    @ApiModelProperty("上报日期")
    @JsonFormat(pattern = "yyyy-MM-dd",timezone = "GMT+8")
    private Date repDate;

    /**
     * 上报用户ID
     */
    @ExcelProperty(value = "上报用户ID")
    @ApiModelProperty("上报用户ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    /**
     * 企业ID
     */
    @ExcelProperty(value = "企业ID")
    @ApiModelProperty("企业ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long copId;

    /**
     * 部门ID
     */
    @ExcelProperty(value = "部门ID")
    @ApiModelProperty("部门ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long deptId;
    /**
     * 部门名称
     */
    @ExcelProperty(value = "部门名称")
    @ApiModelProperty("部门名称")
    private String deptName;

    /**
     * 指标ID（对应t_rep_target表主键）
     */
    @ExcelProperty(value = "指标ID")
    @ApiModelProperty("指标ID（对应t_rep_target表主键）")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long tarId;

    /**
     * 指标值(数值)
     */
    @ExcelProperty(value = "指标值(数值)")
    @ApiModelProperty("指标值(数值)")
    private String tarDecimal;

    /**
     * 指标值(描述)
     */
    @ExcelProperty(value = "指标值(描述)")
    @ApiModelProperty("指标值(描述)")
    private String tarNvarchar;

    /**
     * 处理结果(默认0,0-未处理，1-已处理)
     */
    @ExcelProperty(value = "处理结果")
    @ExcelDictFormat(readConverterExp = "0=未处理,1=已处理")
    @ApiModelProperty("处理结果(默认0,0-未处理，1-已处理)")
    private String despacho;
    /**
     * 审核状态（默认0，0——未审核、1——已审核、2退回）
     */
    @ExcelProperty(value = "审核状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "默=认0，0——未审核、1——已审核、2退回")
    @ApiModelProperty("审核状态（默认0，0——未审核、1——已审核、2退回）")
    private String auditing;

    /**
     * 处理结果审核（默认0，0——未处理、1——未审核、2——已审核、3退回）
     */
    @ExcelProperty(value = "处理结果审核")
    @ApiModelProperty("处理结果审核（默认0，0——未处理、1——未审核、2——已审核、3退回）")
    private String resultAuditing;

    /**
     * 审核意见
     */
    @ApiModelProperty(value = "审核意见")
    private String auditingComment;

    /**
     * 处理结果审核意见
     */
    @ApiModelProperty(value = "处理结果审核意见")
    private String resultAuditingComment;

    /**
     * 风险级别（默认0，0——正常、1——问题、2——风险险、3——危险、4——高危）
     */
    @ExcelProperty(value = "风险级别", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "默=认0，0——正常、1——问题、2——风险险、3——危险、4——高危")
    @ApiModelProperty("风险级别（默认0，0——正常、1——问题、2——风险险、3——危险、4——高危）")
    private String level;
    /**
     * 拖欠工资月数（月）
     */
    @ApiModelProperty(value = "拖欠工资月数（月）")
    private BigDecimal propaymonth;

    /**
     * 拖欠工资涉及人数（人）
     */
    @ApiModelProperty(value = "拖欠工资涉及人数（人）")
    private BigDecimal propaypopulation;

    /**
     * 拖欠工资金额（元）
     */
    @ApiModelProperty(value = "拖欠工资金额（元）")
    private BigDecimal propaymoney;

    /**
     * 拖欠税款月数（月）
     */
    @ApiModelProperty(value = "拖欠税款月数（月）")
    private BigDecimal protaxmonth;

    /**
     * 拖欠税款金额（元）
     */
    @ApiModelProperty(value = "拖欠税款金额（元）")
    private BigDecimal protaxmoney;

    /**
     * 拖欠社保月数（月）
     */
    @ApiModelProperty(value = "拖欠社保月数（月）")
    private BigDecimal proinsuremonth;

    /**
     * 拖欠社保金额（元）
     */
    @ApiModelProperty(value = "拖欠社保金额（元）")
    private BigDecimal proinsuremoney;

    /**
     * 拖欠水费月数（月）
     */
    @ApiModelProperty(value = "拖欠水费月数（月）")
    private BigDecimal prowatermonth;

    /**
     * 拖欠水费金额（元）
     */
    @ApiModelProperty(value = "拖欠水费金额（元）")
    private BigDecimal prowatermoney;

    /**
     * 拖欠电费月数（月）
     */
    @ApiModelProperty(value = "拖欠电费月数（月）")
    private BigDecimal proenergymonth;

    /**
     * 拖欠电费金额（元）
     */
    @ApiModelProperty(value = "拖欠电费金额（元）")
    private BigDecimal proenergymoney;

    /**
     * 拖欠租金月数（月）
     */
    @ApiModelProperty(value = "拖欠租金月数（月）")
    private BigDecimal prorentmonth;

    /**
     * 拖欠租金金额（元）
     */
    @ApiModelProperty(value = "拖欠租金金额（元）")
    private BigDecimal prorentmoney;

    /**
     * 其他问题
     */
    @ApiModelProperty(value = "其他问题")
    private String problem;

    /**
     * 监管措施
     */
    @ApiModelProperty(value = "监管措施")
    private String copoversee;

    /**
     * 建议措施
     */
    @ApiModelProperty(value = "建议措施")
    private String copadvice;

    /**
     * 采取措施
     */
    @ApiModelProperty(value = "采取措施")
    private String copdeal;

    /**
     * 企业现状
     */
    @ApiModelProperty(value = "企业现状")
    private String copresult;

    /**
     * 后续工作
     */
    @ApiModelProperty(value = "后续工作")
    private String copafterwork;

    /**
     * 请求协助
     */
    @ApiModelProperty(value = "请求协助")
    private String cophelp;


    @ApiModelProperty("企业性质")
    private String propertyName;

    @ApiModelProperty("企业名称")
    private String copName;

    @ApiModelProperty("企业编号")
    private String copCode;

    @ApiModelProperty("企业法人")
    private String copOwner;

    @ApiModelProperty("企业负责人")
    private String copIncharge;

    @ApiModelProperty("组织机构代码证")
    private String corporateCode;

    @ApiModelProperty("营业执照注册号")
    private String businessLicence;

    @ApiModelProperty("企业地址")
    private String copAddress;

    @ApiModelProperty("企业电话")
    private String copPhone;

    @ApiModelProperty("传真号码")
    private String copFax;
}

