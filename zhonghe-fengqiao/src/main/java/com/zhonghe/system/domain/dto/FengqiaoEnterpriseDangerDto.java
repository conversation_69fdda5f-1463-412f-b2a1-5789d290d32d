package com.zhonghe.system.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @description:
 * @author: cq
 * @date: 2023/11/10 11:49
 */

@Data
@ApiModel("企业风险")
public class FengqiaoEnterpriseDangerDto {

    @ApiModelProperty("企业id")
    private Long enterpriseId;

    @ApiModelProperty("风险问题")
    private String note;

    @ApiModelProperty("企业名称")
    private String copName;

    @ApiModelProperty("风险等级")
    private String dangerLv;

    @ApiModelProperty("上报日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date reportDate;


    @ApiModelProperty("审核状态")
    private String state;

    @ApiModelProperty("上报部门")
    private String deptName;

    @ApiModelProperty("处理状态")
    private String handlerState;

    @ApiModelProperty("汇报事件主键，用于详情查询")
    private String repReportId;

    @ApiModelProperty("地址")
    private String address;

    private Integer isDel;
}

