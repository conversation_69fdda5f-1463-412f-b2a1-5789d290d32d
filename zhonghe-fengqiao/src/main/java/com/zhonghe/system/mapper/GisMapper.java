package com.zhonghe.system.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zhonghe.system.domain.vo.FengqiaoEnterpriseDetailVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @description:
 * @author: cq
 * @date: 2023/11/10 17:35
 */

public interface GisMapper {


    Long countHighRisk();

    Long countRisk();

    Long countDangerous();

    Long countProblem();


    Long countEnterprise(@Param("tableName") String tableName);

    IPage<FengqiaoEnterpriseDetailVo> selectScale(IPage pageQuery, @Param("keyword") String keyWord);

    IPage<FengqiaoEnterpriseDetailVo> selectList(IPage pageQuery, @Param("keyword") String keyWord);

    IPage<FengqiaoEnterpriseDetailVo> selectTop500(IPage pageQuery, @Param("keyword") String keyWord);

    IPage<FengqiaoEnterpriseDetailVo> selectHighTech(IPage pageQuery, @Param("keyword") String keyWord);

    /**
     * 高危企业
     */
    List<FengqiaoEnterpriseDetailVo> selectHighRisk(@Param("keyword") String keyWord);

    /**
     * 危险企业
     */
    List<FengqiaoEnterpriseDetailVo> selectDangerous(@Param("keyword") String keyWord);

    /**
     * 风险企业
     */
    List<FengqiaoEnterpriseDetailVo> selectRisk(@Param("keyword") String keyWord);

    /**
     * 问题企业
     */
    List<FengqiaoEnterpriseDetailVo> selectProblem(@Param("keyword") String keyWord);

}
