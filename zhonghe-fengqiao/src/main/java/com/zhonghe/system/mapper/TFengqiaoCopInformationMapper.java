package com.zhonghe.system.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhonghe.common.core.mapper.BaseMapperPlus;
import com.zhonghe.system.domain.TFengqiaoCopInformation;
import com.zhonghe.system.domain.dto.FengqiaoEnterpriseDangerDto;
import com.zhonghe.system.domain.dto.EnterpriseStatisticDto;
import com.zhonghe.system.domain.vo.FengqiaoCopInformationVo;
import com.zhonghe.system.domain.vo.TFenqgqiaoCopScoreVo;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

/**
 * 【请填写功能名称】Mapper接口
 *
 * <AUTHOR>
 * @date 2023-11-10
 */
public interface TFengqiaoCopInformationMapper extends BaseMapperPlus<TFengqiaoCopInformationMapper, TFengqiaoCopInformation, FengqiaoCopInformationVo> {

    List<EnterpriseStatisticDto> enterpriseStatistic();

    List<FengqiaoEnterpriseDangerDto> selectEnterpriseDangerByDate(@Param("start") LocalDate startDate, @Param("end") LocalDate endDate, @Param("keyword") String keyWord);

    List<TFenqgqiaoCopScoreVo> scoreByDateRange(@Param("start") LocalDate startDate, @Param("end") LocalDate endDate);

    IPage<FengqiaoEnterpriseDangerDto> selectEnterpriseDanger(@Param("page") Page<FengqiaoEnterpriseDangerDto> build, @Param("id") Long id);

    List<TFenqgqiaoCopScoreVo> scoreByYear(@Param("year") int year);

    List<FengqiaoEnterpriseDangerDto> selectMaxRepReportId(@Param("start") LocalDate startDate, @Param("end") LocalDate endDate, @Param("keyword") String keyWord);

    List<FengqiaoEnterpriseDangerDto> selectMaxRepReportIdByEnterpriseIds(@Param("id") Long enterpriseIds);

    String selectPropertyName(@Param("id") Long propertyId);
}
