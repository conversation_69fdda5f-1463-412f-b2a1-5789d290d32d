package com.zhonghe.system.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.system.constants.FengqiaoConstant;
import com.zhonghe.system.domain.TFengqiaoCopInformation;
import com.zhonghe.system.domain.bo.TFengqiaoCopInformationBo;
import com.zhonghe.system.domain.dto.FengqiaoDangerEnterpriseStatisticDto;
import com.zhonghe.system.domain.dto.FengqiaoEnterpriseDangerDto;
import com.zhonghe.system.mapper.GisMapper;
import com.zhonghe.system.mapper.TFengqiaoCopInformationMapper;
import com.zhonghe.system.service.IBigScreenInformationService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.Month;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 【请填写功能名称】Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-11-10
 */
@RequiredArgsConstructor
@Service
public class BigScreenInformationServiceImpl implements IBigScreenInformationService {

    private final TFengqiaoCopInformationMapper baseMapper;

    private final GisMapper gisMapper;

    private static LocalDate[] generateQueryDateRange(int queryMode) {
        LocalDate startDate;
        LocalDate endDate;

        if (queryMode == FengqiaoConstant.QUERY_TYPE_QUARTER) {
            LocalDate[] startOrEndDayOfQuarter = getStartOrEndDayOfQuarter();
            startDate = startOrEndDayOfQuarter[0];
            endDate = startOrEndDayOfQuarter[1];
        } else {
            LocalDate currentDate = LocalDate.now();
            // 获取当前年份和月份
            int year = currentDate.getYear();
            Month month = currentDate.getMonth();

            startDate = LocalDate.of(year, month, 1);
            endDate = LocalDate.of(year, month, month.length(currentDate.isLeapYear()));
        }

        return new LocalDate[]{startDate, endDate};
    }

    /**
     * 获取当前季度的起始日期和结束日期
     */
    public static LocalDate[] getStartOrEndDayOfQuarter() {
        LocalDate today = LocalDate.now();
        Month month = today.getMonth();
        Month firstMonthOfQuarter = month.firstMonthOfQuarter();
        Month endMonthOfQuarter = Month.of(firstMonthOfQuarter.getValue() + 2);
        LocalDate startDate = LocalDate.of(today.getYear(), firstMonthOfQuarter, 1);
        LocalDate endDate = LocalDate.of(today.getYear(), endMonthOfQuarter, endMonthOfQuarter.length(today.isLeapYear()));
        return new LocalDate[]{startDate, endDate};
    }



    private LambdaQueryWrapper<TFengqiaoCopInformation> buildQueryWrapper(TFengqiaoCopInformationBo bo) {
        LambdaQueryWrapper<TFengqiaoCopInformation> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getNote()), TFengqiaoCopInformation::getNote, bo.getNote());
        lqw.like(StringUtils.isNotBlank(bo.getCopName()), TFengqiaoCopInformation::getCopName, bo.getCopName());
        lqw.eq(StringUtils.isNotBlank(bo.getCopCode()), TFengqiaoCopInformation::getCopCode, bo.getCopCode());
        lqw.eq(StringUtils.isNotBlank(bo.getCopOwner()), TFengqiaoCopInformation::getCopOwner, bo.getCopOwner());
        lqw.eq(StringUtils.isNotBlank(bo.getCopIncharge()), TFengqiaoCopInformation::getCopIncharge, bo.getCopIncharge());
        lqw.eq(StringUtils.isNotBlank(bo.getCorporateCode()), TFengqiaoCopInformation::getCorporateCode, bo.getCorporateCode());
        lqw.eq(StringUtils.isNotBlank(bo.getBusinessLicence()), TFengqiaoCopInformation::getBusinessLicence, bo.getBusinessLicence());
        lqw.eq(StringUtils.isNotBlank(bo.getTaxRegistration()), TFengqiaoCopInformation::getTaxRegistration, bo.getTaxRegistration());
        lqw.eq(bo.getCopPopulation() != null, TFengqiaoCopInformation::getCopPopulation, bo.getCopPopulation());
        lqw.eq(StringUtils.isNotBlank(bo.getCopAddress()), TFengqiaoCopInformation::getCopAddress, bo.getCopAddress());
        lqw.eq(StringUtils.isNotBlank(bo.getCopPhone()), TFengqiaoCopInformation::getCopPhone, bo.getCopPhone());
        lqw.eq(StringUtils.isNotBlank(bo.getCopFax()), TFengqiaoCopInformation::getCopFax, bo.getCopFax());
        lqw.eq(bo.getOfficeProperty() != null, TFengqiaoCopInformation::getOfficeProperty, bo.getOfficeProperty());
        lqw.eq(StringUtils.isNotBlank(bo.getOfficeOwner()), TFengqiaoCopInformation::getOfficeOwner, bo.getOfficeOwner());
        lqw.eq(StringUtils.isNotBlank(bo.getOfficeOwnerPhone()), TFengqiaoCopInformation::getOfficeOwnerPhone, bo.getOfficeOwnerPhone());
        lqw.eq(bo.getPropertyId() != null, TFengqiaoCopInformation::getPropertyId, bo.getPropertyId());
        lqw.eq(bo.getOrgId() != null, TFengqiaoCopInformation::getOrgId, bo.getOrgId());
        lqw.eq(StringUtils.isNotBlank(bo.getLon()), TFengqiaoCopInformation::getLon, bo.getLon());
        lqw.eq(StringUtils.isNotBlank(bo.getLat()), TFengqiaoCopInformation::getLat, bo.getLat());
        lqw.eq(bo.getIsCancel() != null, TFengqiaoCopInformation::getIsCancel, bo.getIsCancel());
        lqw.eq(StringUtils.isNotBlank(bo.getScopeOfBusiness()), TFengqiaoCopInformation::getScopeOfBusiness, bo.getScopeOfBusiness());
        lqw.eq(StringUtils.isNotBlank(bo.getCopContacts()), TFengqiaoCopInformation::getCopContacts, bo.getCopContacts());
        lqw.eq(StringUtils.isNotBlank(bo.getLeaseTerm()), TFengqiaoCopInformation::getLeaseTerm, bo.getLeaseTerm());
        lqw.eq(StringUtils.isNotBlank(bo.getBy1()), TFengqiaoCopInformation::getBy1, bo.getBy1());
        lqw.eq(StringUtils.isNotBlank(bo.getBy2()), TFengqiaoCopInformation::getBy2, bo.getBy2());
        return lqw;
    }



    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(TFengqiaoCopInformation entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public List<FengqiaoEnterpriseDangerDto> reportDangerList(int queryMode, String keyWord) {
        LocalDate[] localDates = generateQueryDateRange(queryMode);
        List<FengqiaoEnterpriseDangerDto> enterpriseDangerDtos = baseMapper.selectEnterpriseDangerByDate(localDates[0], localDates[1], keyWord);
        List<FengqiaoEnterpriseDangerDto> repReportIds = baseMapper.selectMaxRepReportId(localDates[0], localDates[1], keyWord);
        Map<Long, List<FengqiaoEnterpriseDangerDto>> collect = repReportIds.stream().collect(Collectors.groupingBy(FengqiaoEnterpriseDangerDto::getEnterpriseId));

        enterpriseDangerDtos.forEach(f -> {
            if (collect.containsKey(f.getEnterpriseId())) {
                List<FengqiaoEnterpriseDangerDto> enterpriseDangerDtos1 = collect.get(f.getEnterpriseId());
                if (CollectionUtil.isNotEmpty(enterpriseDangerDtos1)) {
                    f.setRepReportId(enterpriseDangerDtos1.get(0).getRepReportId());
                }
            }
        });

        return enterpriseDangerDtos;
    }

    @Override
    public FengqiaoDangerEnterpriseStatisticDto dangerEnterpriseStatistic() {
        FengqiaoDangerEnterpriseStatisticDto result = new FengqiaoDangerEnterpriseStatisticDto();
        result.setHighRisk(gisMapper.countHighRisk());
        result.setRisk(gisMapper.countRisk());
        result.setDanger(gisMapper.countDangerous());
        result.setProblem(gisMapper.countProblem());
        return result;
    }
}
