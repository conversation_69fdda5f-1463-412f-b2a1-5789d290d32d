<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhonghe.system.mapper.GisMapper">


    <select id="countHighRisk" resultType="java.lang.Long">
        select count(1)
        from cop.high_risk_enterprises
    </select>

    <select id="countRisk" resultType="java.lang.Long">
        select count(1)
        from cop.risk_enterprises
    </select>

    <select id="countDangerous" resultType="java.lang.Long">
        select count(1)
        from cop.dangerous_enterprises
    </select>

    <select id="countProblem" resultType="java.lang.Long">
        select count(1)
        from cop.problem_enterprises
    </select>
    <select id="countEnterprise" resultType="java.lang.Long">
        select count(1) as count
        from cop.${tableName}
        where del_flag = '0'
    </select>
    <select id="selectScale" resultType="com.zhonghe.system.domain.vo.FengqiaoEnterpriseDetailVo">
        select * from cop.sl_enterprise_scale
        <where>
            del_flag = '0'
            <if test="keyword !=null and keyword !=''">
                and ("name" like concat('%', #{keyword} , '%')
                or enterprise_type like concat('%', #{keyword} , '%')
                or address like concat('%', #{keyword} , '%')
                or phone like concat('%', #{keyword} , '%'))
            </if>
        </where>
    </select>
    <select id="selectList" resultType="com.zhonghe.system.domain.vo.FengqiaoEnterpriseDetailVo">
        select * from cop.sl_enterprise_listed
        <where>
            del_flag = '0'
            <if test="keyword !=null and keyword !=''">
                and ("name" like concat('%', #{keyword} , '%')
                or enterprise_type like concat('%', #{keyword} , '%')
                or address like concat('%', #{keyword} , '%')
                or phone like concat('%', #{keyword} , '%'))
            </if>
        </where>
    </select>
    <select id="selectTop500" resultType="com.zhonghe.system.domain.vo.FengqiaoEnterpriseDetailVo">
        select * from cop.sl_enterprise_top_500
        <where>
            del_flag = '0'
            <if test="keyword !=null and keyword !=''">
                and ("name" like concat('%', #{keyword} , '%')
                or enterprise_type like concat('%', #{keyword} , '%')
                or address like concat('%', #{keyword} , '%')
                or phone like concat('%', #{keyword} , '%'))
            </if>
        </where>
    </select>
    <select id="selectHighTech" resultType="com.zhonghe.system.domain.vo.FengqiaoEnterpriseDetailVo">
        select * from cop.sl_enterprise_high_tech
        <where>
            del_flag = '0'
            <if test="keyword !=null and keyword !=''">
                and ("name" like concat('%', #{keyword} , '%')
                or enterprise_type like concat('%', #{keyword} , '%')
                or address like concat('%', #{keyword} , '%')
                or phone like concat('%', #{keyword} , '%'))
            </if>
        </where>
    </select>
    <select id="selectHighRisk" resultType="com.zhonghe.system.domain.vo.FengqiaoEnterpriseDetailVo">
        select enterprise_id,
        enterprise_name as "name",
        enterprise_type,
        enterprise_address as address ,
        contact_phone as phone,
        geom,
        legal_person,
        rep_person,
        rep_date,
        rep_reason,
        problem,
        copoversee,
        copadvice,
        copdeal,
        copresult,
        copafterwork,
        cophelp,
        rep_dept
        from cop.high_risk_enterprises
        <where>
            <if test="keyword !=null and keyword !=''">
                and (enterprise_name like concat('%', #{keyword} , '%')
                or enterprise_type like concat('%', #{keyword} , '%')
                or enterprise_address like concat('%', #{keyword} , '%')
                or contact_phone like concat('%', #{keyword} , '%'))
            </if>
        </where>
    </select>
    <select id="selectDangerous" resultType="com.zhonghe.system.domain.vo.FengqiaoEnterpriseDetailVo">
        select enterprise_id,
        enterprise_name as "name",
        enterprise_type,
        enterprise_address as address ,
        contact_phone as phone,
        geom,
        legal_person,
        rep_person,
        rep_date,
        rep_reason,
        problem,
        copoversee,
        copadvice,
        copdeal,
        copresult,
        copafterwork,
        cophelp,
        rep_dept
        from cop.dangerous_enterprises
        <where>
            <if test="keyword !=null and keyword !=''">
                and (enterprise_name like concat('%', #{keyword} , '%')
                or enterprise_type like concat('%', #{keyword} , '%')
                or enterprise_address like concat('%', #{keyword} , '%')
                or contact_phone like concat('%', #{keyword} , '%'))
            </if>
        </where>
    </select>
    <select id="selectRisk" resultType="com.zhonghe.system.domain.vo.FengqiaoEnterpriseDetailVo">
        select enterprise_id,
        enterprise_name as "name",
        enterprise_type,
        enterprise_address as address ,
        contact_phone as phone,
        geom,
        legal_person,
        rep_person,
        rep_date,
        rep_reason,
        problem,
        copoversee,
        copadvice,
        copdeal,
        copresult,
        copafterwork,
        cophelp,
        rep_dept
        from cop.risk_enterprises
        <where>
            <if test="keyword !=null and keyword !=''">
                and (enterprise_name like concat('%', #{keyword} , '%')
                or enterprise_type like concat('%', #{keyword} , '%')
                or enterprise_address like concat('%', #{keyword} , '%')
                or contact_phone like concat('%', #{keyword} , '%'))
            </if>
        </where>
    </select>
    <select id="selectProblem" resultType="com.zhonghe.system.domain.vo.FengqiaoEnterpriseDetailVo">
        select enterprise_id,
        enterprise_name as "name",
        enterprise_type,
        enterprise_address as address ,
        contact_phone as phone,
        geom,
        legal_person,
        rep_person,
        rep_date,
        rep_reason,
        problem,
        copoversee,
        copadvice,
        copdeal,
        copresult,
        copafterwork,
        cophelp,
        rep_dept
        from cop.problem_enterprises
        <where>
            <if test="keyword !=null and keyword !=''">
                and (enterprise_name like concat('%', #{keyword} , '%')
                or enterprise_type like concat('%', #{keyword} , '%')
                or enterprise_address like concat('%', #{keyword} , '%')
                or contact_phone like concat('%', #{keyword} , '%'))
            </if>
        </where>
    </select>
</mapper>
