<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhonghe.system.mapper.TFengqiaoCopInformationMapper">

    <resultMap type="com.zhonghe.system.domain.TFengqiaoCopInformation" id="TCopInformationResult">
        <result property="note" column="note"/>
        <result property="copName" column="cop_name"/>
        <result property="copCode" column="cop_code"/>
        <result property="copOwner" column="cop_owner"/>
        <result property="copIncharge" column="cop_incharge"/>
        <result property="corporateCode" column="corporate_code"/>
        <result property="businessLicence" column="business_licence"/>
        <result property="taxRegistration" column="tax_registration"/>
        <result property="copPopulation" column="cop_population"/>
        <result property="copAddress" column="cop_address"/>
        <result property="copPhone" column="cop_phone"/>
        <result property="copFax" column="cop_fax"/>
        <result property="officeProperty" column="office_property"/>
        <result property="officeOwner" column="office_owner"/>
        <result property="officeOwnerPhone" column="office_owner_phone"/>
        <result property="propertyId" column="property_id"/>
        <result property="orgId" column="org_id"/>
        <result property="lon" column="lon"/>
        <result property="lat" column="lat"/>
        <result property="isCancel" column="is_cancel"/>
        <result property="scopeOfBusiness" column="scope_of_business"/>
        <result property="copContacts" column="cop_contacts"/>
        <result property="leaseTerm" column="lease_term"/>
        <result property="by1" column="by1"/>
        <result property="by2" column="by2"/>
        <result property="geom" column="geom"/>
    </resultMap>

    <sql id="enterpriseDangerSql">
        select distinct tci.cop_id        as enterpriseId,
                        trr.rep_report_id as repReportId,
                        trr.note          as note,
                        tci.cop_name      as copName,
                        trr.rep_date      as reportDate,
                        coplv.level_name  as dangerLv,
                        sd.dept_name      as deptName,
                        trr.del_flag      as isDel,
                        tci.cop_address   as address,
                        CASE
                            WHEN trr.despacho = 0 THEN '未处理'
                            WHEN trr.despacho = 1 THEN '已处理'
                            end           as handlerState,
                        CASE
                            WHEN trr.auditing = 0 THEN '未审核'
                            WHEN trr.auditing = 1 THEN '已审核'
                            WHEN trr.auditing = 2 THEN '退回'
                            end           as state
        from cop.T_REP_Report trr
                 left join cop.T_COP_Information tci on trr.cop_id = tci.cop_id
                 left join cop.T_DIC_CopLevel coplv on trr."level" = coplv.level_value
                 left join public.sys_dept sd on sd.dept_id = trr.dept_id
    </sql>


    <sql id="enterpriseDangerFuckSql">
        select distinct tci.cop_id        as enterpriseId,
                        trr.rep_report_id as rep_report_id,
                        trr.rep_date      as reportDate,
                        CASE
                            WHEN trr.despacho = 0 THEN '未处理'
                            WHEN trr.despacho = 1 THEN '已处理'
                            end           as handlerState,
                        CASE
                            WHEN trr.auditing = 0 THEN '未审核'
                            WHEN trr.auditing = 1 THEN '已审核'
                            WHEN trr.auditing = 2 THEN '退回'
                            end           as state
        from cop.T_REP_Report trr
                 left join cop.T_COP_Information tci on trr.cop_id = tci.cop_id
                 left join cop.T_DIC_CopLevel coplv on trr."level" = coplv.level_value
                 left join cop.t_dic_department tdd on tdd.auto_id = trr.dept_id
    </sql>

    <sql id="enterpriseRepReportIdSql">
        select distinct tci.cop_id        as enterpriseId,
                        trr.rep_report_id as rep_report_id
        from cop.T_REP_Report trr
                 left join cop.T_COP_Information tci on trr.cop_id = tci.cop_id
                 left join cop.T_DIC_CopLevel coplv on trr."level" = coplv.level_value
                 left join cop.t_dic_department tdd on tdd.auto_id = trr.dept_id
    </sql>


    <select id="enterpriseStatistic" resultType="com.zhonghe.system.domain.dto.EnterpriseStatisticDto">
        SELECT count(1) as count, tdcp.cop_property as mode
        from cop.T_COP_Information tci
                 inner join cop.T_DIC_CopProperty tdcp on tci.property_id = tdcp.property_id
        where is_cancel = 0
        group by tdcp.cop_property
        order by count desc
    </select>
    <select id="selectEnterpriseDangerByDate" resultType="com.zhonghe.system.domain.dto.FengqiaoEnterpriseDangerDto">
        <include refid="enterpriseDangerSql"/>
        where trr.del_flag = '0' and trr.auditing = 1
        <if test="keyword !=null and keyword !=''">
            and (tdd.dep_name like concat('%', #{keyword} , '%') or tci.cop_name like concat('%', #{keyword} , '%') or
            tci.cop_address like concat('%', #{keyword} , '%'))
        </if>
        and trr.rep_date BETWEEN #{start} AND #{end}
        order by trr.rep_date desc
    </select>
    <select id="scoreByDateRange" resultType="com.zhonghe.system.domain.vo.TFenqgqiaoCopScoreVo">
        select sum(coalesce(tcs.deduct_score, 0)) as score,
               tci.cop_name                       as copName
        from cop.t_cop_information tci
                 left join cop.t_cop_score_record tcs on
            tcs.cop_id = tci.cop_id AND tcs.del_flag = '0'
        where tci.del_flag = '0'
          and (tcs.create_time is null or tcs.create_time BETWEEN #{start} AND #{end})
        group by tci.cop_name
    </select>
    <select id="selectEnterpriseDanger" resultType="com.zhonghe.system.domain.dto.FengqiaoEnterpriseDangerDto">
        <include refid="enterpriseDangerSql"/>
        where tci.del_flag = '0' and trr.del_flag = '0' and tci.cop_id = #{id} and trr.auditing = 1
        order by trr.rep_date desc
    </select>
    <select id="scoreByYear" resultType="com.zhonghe.system.domain.vo.TFenqgqiaoCopScoreVo">
        select sum(coalesce(tcs.deduct_score, 0)) as score,
               tci.cop_name                       as copName
        from cop.t_cop_information tci
                 left join cop.t_cop_score_record tcs on
            tcs.cop_id = tci.cop_id AND tcs.del_flag = '0'
        where tci.del_flag = '0'
          and tci.is_cancel = 0
          and (tcs.create_time is null or DATE_PART('year', tcs.create_time) = #{year})
        group by tci.cop_name
    </select>
    <select id="selectMaxRepReportId" resultType="com.zhonghe.system.domain.dto.FengqiaoEnterpriseDangerDto">
        <include refid="enterpriseRepReportIdSql"/>
        where trr.del_flag = '0' and trr.auditing = 1
        <if test="keyword !=null and keyword !=''">
            and (tdd.dep_name like concat('%', #{keyword} , '%') or tci.cop_name like concat('%', #{keyword} , '%') or
            tci.cop_address like concat('%', #{keyword} , '%'))
        </if>
        and trr.rep_date BETWEEN #{start} AND #{end}
    </select>
    <select id="selectMaxRepReportIdByEnterpriseIds"
            resultType="com.zhonghe.system.domain.dto.FengqiaoEnterpriseDangerDto">
        <include refid="enterpriseDangerFuckSql"/>
        where tci.del_flag = '0' and tci.cop_id = #{id} and trr.auditing = 1
        order by trr.rep_date desc
    </select>
    <select id="selectPropertyName" resultType="java.lang.String">
        select cop_property
        from cop.t_dic_copproperty
        <where>
            property_id =#{id}
        </where>
    </select>


</mapper>
