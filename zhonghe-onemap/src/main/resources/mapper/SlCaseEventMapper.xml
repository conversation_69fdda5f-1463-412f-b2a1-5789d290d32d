<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhonghe.system.mapper.SlCaseEventMapper">

    <resultMap type="com.zhonghe.system.domain.SlCaseEvent" id="SlCaseEventResult">
        <result property="caseEventId" column="case_event_id"/>
        <result property="caseEventCode" column="case_event_code"/>
        <result property="caseEventSource" column="case_event_source"/>
        <result property="caseEventStreet" column="case_event_street"/>
        <result property="caseEventZone" column="case_event_zone"/>
        <result property="caseEventGrid" column="case_event_grid"/>
        <result property="caseEventDepartment" column="case_event_department"/>
        <result property="caseEventReason" column="case_event_reason"/>
        <result property="caseEventSubject" column="case_event_subject"/>
        <result property="caseEventAddress" column="case_event_address"/>
        <result property="caseEventCreateTime" column="case_event_create_time"/>
        <result property="caseEventEndDate" column="case_event_end_date"/>
        <result property="caseEventHandlerOrg" column="case_event_handler_org"/>
        <result property="caseEventStatus" column="case_event_status"/>
        <result property="caseEventMark" column="case_event_mark"/>
        <result property="caseEventGatherSource" column="case_event_gather_source"/>
        <result property="delFlag" column="del_flag"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>


</mapper>
