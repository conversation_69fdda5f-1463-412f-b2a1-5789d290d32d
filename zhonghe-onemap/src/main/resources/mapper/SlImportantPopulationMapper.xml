<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhonghe.system.mapper.SlImportantPopulationMapper">

    <resultMap type="com.zhonghe.system.domain.SlImportantPopulation" id="SlImportantPopulationResult">
        <result property="populationId" column="population_id"/>
        <result property="gridName" column="grid_name"/>
        <result property="name" column="name"/>
        <result property="sex" column="sex"/>
        <result property="brithDate" column="brith_date"/>
        <result property="idCode" column="id_code"/>
        <result property="baseLocation" column="base_location"/>
        <result property="populationType" column="population_type"/>
        <result property="familyLocation" column="family_location"/>
        <result property="phone" column="phone"/>
        <result property="standardLocation" column="standard_location"/>
        <result property="livingLocation" column="living_location"/>
        <result property="workingLocation" column="working_location"/>
        <result property="delFlag" column="del_flag"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>
    <select id="queryByAddress" resultType="com.zhonghe.system.domain.thirdparty.vo.SpecialistVo">
        select important.name as name , important.id_code as code , important.phone as phone , important.population_type as type,important.standard_location as address
        from business.sl_important_population important
        <where>
            important.standard_location = #{address} or important.living_location = #{address} or important.working_location = #{address}
        </where>
    </select>


</mapper>
