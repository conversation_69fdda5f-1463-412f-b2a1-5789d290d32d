<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhonghe.system.mapper.SlCaseEventOnenetMapper">

    <resultMap type="com.zhonghe.system.domain.SlCaseEventOnenet" id="SlCaseEventOnenetResult">
        <result property="caseEventOnenetId" column="case_event_onenet_id"/>
        <result property="caseEventCode" column="case_event_code"/>
        <result property="caseEventAddress" column="case_event_address"/>
        <result property="caseEventId" column="case_event_id"/>
        <result property="caseEventGem" column="case_event_gem"/>
        <result property="fid" column="fid"/>
        <result property="onenetJson" column="onenet_json"/>
        <result property="delFlag" column="del_flag"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="asjid" column="asjid"/>
    </resultMap>


</mapper>
