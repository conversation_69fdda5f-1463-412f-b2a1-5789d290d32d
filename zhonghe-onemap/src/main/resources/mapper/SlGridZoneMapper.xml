<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhonghe.system.mapper.SlGridZoneMapper">

    <resultMap type="com.zhonghe.system.domain.SlGridZone" id="SlGridZoneResult">
        <result property="gridZoneId" column="grid_zone_id"/>
        <result property="gridGroupName" column="grid_group_name"/>
        <result property="gridZoneName" column="grid_zone_name"/>
        <result property="delFlag" column="del_flag"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>
    <select id="grid" resultType="java.lang.String">
        SELECT * FROM find_records_with_point(#{lng} ,#{lat})
    </select>


</mapper>
