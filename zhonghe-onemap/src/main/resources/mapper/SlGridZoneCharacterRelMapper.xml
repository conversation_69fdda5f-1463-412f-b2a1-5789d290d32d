<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhonghe.system.mapper.SlGridZoneCharacterRelMapper">

    <resultMap type="com.zhonghe.system.domain.SlGridZoneCharacterRel" id="SlGridZoneCharacterRelResult">
        <result property="zoneCharacterRelId" column="zone_character_rel_id"/>
        <result property="gridZoneId" column="grid_zone_id"/>
        <result property="gridCharacterId" column="grid_character_id"/>
        <result property="delFlag" column="del_flag"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>


</mapper>
