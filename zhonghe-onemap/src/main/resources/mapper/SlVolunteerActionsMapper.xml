<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhonghe.system.mapper.SlVolunteerActionsMapper">

    <resultMap type="com.zhonghe.system.domain.SlVolunteerActions" id="SlVolunteerActionsResult">
        <result property="actionId" column="action_id"/>
        <result property="actionNumber" column="action_number"/>
        <result property="actionName" column="action_name"/>
        <result property="actionContract" column="action_contract"/>
        <result property="actionContractPhone" column="action_contract_phone"/>
        <result property="actionPublishOrg" column="action_publish_org"/>
        <result property="actionPublishDate" column="action_publish_date"/>
        <result property="actionAdditionalStatus" column="action_additional_status"/>
        <result property="actionStatus" column="action_status"/>
        <result property="actionType" column="action_type"/>
        <result property="actionTag" column="action_tag"/>
        <result property="actionStartTime" column="action_start_time"/>
        <result property="actionEndTime" column="action_end_time"/>
        <result property="actionEmploy" column="action_employ"/>
        <result property="delFlag" column="del_flag"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>


</mapper>
