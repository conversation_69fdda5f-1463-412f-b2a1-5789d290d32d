<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhonghe.system.mapper.SlGridStructureCharacterMapper">

    <resultMap type="com.zhonghe.system.domain.SlGridStructureCharacter" id="SlGridStructureCharacterResult">
        <result property="gridCharacterId" column="grid_character_id"/>
        <result property="characterZoneName" column="character_zone_name"/>
        <result property="characterServiceType" column="character_service_type"/>
        <result property="characterName" column="character_name"/>
        <result property="characterSex" column="character_sex"/>
        <result property="characterWorkPhone" column="character_work_phone"/>
        <result property="characterFamilyPhone" column="character_family_phone"/>
        <result property="mark" column="mark"/>
        <result property="delFlag" column="del_flag"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>

    <sql id="characterDetail">
        select
             distinct sgsc.*
             , sgz.grid_zone_name
             , sgs.grid_structure_id
             , sgs.grid_structure_parent_id
             , sgs.gird_structure_name
        from (select *
              from business.sl_grid_structure_character r
              where r.character_service_type = '一支队伍') as sgsc
                 left join business.sl_grid_structure_character_rel sgscr on
            sgsc.grid_character_id = sgscr.grid_character_id
                 left join business.sl_grid_structure sgs on
            sgscr.grid_structure_id = sgs.grid_structure_id
                 left join business.sl_grid_zone_character_rel sgzcr on
            sgsc.grid_character_id = sgzcr.grid_character_id
                 left join business.sl_grid_zone sgz on
            sgzcr.grid_zone_id = sgz.grid_zone_id
    </sql>

    <sql id="servicePower">
        select
            distinct sgsc.*,
            sgz.grid_zone_name
        from business.sl_grid_structure_character sgsc
                 left join business.sl_grid_zone_character_rel sgzcr on
            sgsc.grid_character_id = sgzcr.grid_character_id
                 left join business.sl_grid_zone sgz on
            sgzcr.grid_zone_id = sgz.grid_zone_id
    </sql>

    <select id="queryCharacterDetailVo" resultType="com.zhonghe.system.domain.vo.grid.SlGridStructureCharacterVo">
        <include refid="characterDetail"></include>
        ${ew.getCustomSqlSegment}
    </select>


    <select id="queryPageCharacterDetailVo" resultType="com.zhonghe.system.domain.vo.grid.SlGridStructureCharacterVo">
        <include refid="characterDetail"></include>
        ${ew.getCustomSqlSegment}
    </select>
    <select id="queryServicePowerCharacters"
            resultType="com.zhonghe.system.domain.vo.grid.SlGridStructureCharacterVo">
        <include refid="servicePower"></include>
        ${ew.getCustomSqlSegment}
    </select>
    <select id="selectGridLeader" resultType="com.zhonghe.system.domain.vo.grid.SlGridStructureCharacterVo">
        <include refid="characterDetail"></include>
        <where>
            sgz.grid_zone_name = #{grid} and sgs.gird_structure_name = '网格长'
        </where>
    </select>
    <select id="queryServicePowerCharactersByCharacterId"
            resultType="com.zhonghe.system.domain.vo.grid.SlGridStructureCharacterVo">
        <include refid="servicePower"></include>
        <where>
            sgsc.grid_character_id = #{id}
        </where>
    </select>
</mapper>
