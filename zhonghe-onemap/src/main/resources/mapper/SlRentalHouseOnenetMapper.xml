<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhonghe.system.mapper.SlRentalHouseOnenetMapper">

    <resultMap type="com.zhonghe.system.domain.SlRentalHouseOnenet" id="SlRentalHouseOnenetResult">
        <result property="rentalHouseOnenetId" column="rental_house_onenet_id"/>
        <result property="rentalHouseId" column="rental_house_id"/>
        <result property="smartOnenetCode" column="smart_onenet_code"/>
        <result property="rentalHouseGem" column="rental_house_gem"/>
        <result property="fid" column="fid"/>
        <result property="onenetJson" column="onenet_json"/>
        <result property="delFlag" column="del_flag"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="gridZone" column="grid_zone"/>
        <result property="buildingAddress" column="building_address"/>
        <result property="buildingType" column="building_type"/>
        <result property="updatedTime" column="updated_time"/>
        <result property="createdTime" column="created_time"/>
        <result property="updater" column="updater"/>
        <result property="creator" column="creator"/>
        <result property="rentalType" column="rental_type"/>
        <result property="rentalLv" column="rental_lv"/>
        <result property="signName" column="sign_name"/>
        <result property="belongToBuilding" column="belong_to_building"/>
        <result property="mainSubjectAddress" column="main_subject_address"/>
        <result property="hasFillCode" column="has_fill_code"/>
        <result property="rentHouseFillCode" column="rent_house_fill_code"/>
        <result property="hasCheckedAddress" column="has_checked_address"/>
        <result property="fillAddress" column="fill_address"/>
        <result property="hasSignSafeContract" column="has_sign_safe_contract"/>
        <result property="availableRentRoomCount" column="available_rent_room_count"/>
        <result property="rentedRoomCount" column="rented_room_count"/>
        <result property="renterCount" column="renter_count"/>
        <result property="rentalBuildingFloorCount" column="rental_building_floor_count"/>
        <result property="rentedFloorIdx" column="rented_floor_idx"/>
        <result property="buildingStruct" column="building_struct"/>
        <result property="buildingOwnerName" column="building_owner_name"/>
        <result property="buildingOwnerTel" column="building_owner_tel"/>
        <result property="hasManager" column="has_manager"/>
        <result property="secondHandManager" column="second_hand_manager"/>
        <result property="secondHandTel" column="second_hand_tel"/>
        <result property="managerName" column="manager_name"/>
        <result property="managerTel" column="manager_tel"/>
        <result property="mark" column="mark"/>
        <result property="workingTags" column="working_tags"/>
        <result property="supplyTags" column="supply_tags"/>
    </resultMap>


</mapper>
