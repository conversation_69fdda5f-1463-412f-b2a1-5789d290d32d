<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhonghe.system.mapper.SlVolunteerMapper">

    <resultMap type="com.zhonghe.system.domain.SlVolunteer" id="SlVolunteerResult">
        <result property="volunteerId" column="volunteer_id"/>
        <result property="volunteerName" column="volunteer_name"/>
        <result property="sex" column="sex"/>
        <result property="volunteerLoginCode" column="volunteer_login_code"/>
        <result property="volunteerIdNumber" column="volunteer_id_number"/>
        <result property="volunteerPhone" column="volunteer_phone"/>
        <result property="volunteerRegisterDate" column="volunteer_register_date"/>
        <result property="volunteerVerify" column="volunteer_verify"/>
        <result property="volunteerFaceVerify" column="volunteer_face_verify"/>
        <result property="volunteerPass" column="volunteer_pass"/>
        <result property="volunteerPolitical" column="volunteer_political"/>
        <result property="volunteerServerTime" column="volunteer_server_time"/>
        <result property="volunteerTrainTime" column="volunteer_train_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>


</mapper>
