<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhonghe.system.mapper.SlCaseEventProxyMapper">

    <resultMap type="com.zhonghe.system.domain.thirdparty.SlCaseEventProxy" id="CaseEventProxyResult">
        <result property="fywzj" column="fywzj"/>
        <result property="asjid" column="asjid"/>
        <result property="ztid" column="ztid"/>
        <result property="ztmc" column="ztmc"/>
        <result property="jzwdz" column="jzwdz"/>
        <result property="jd" column="jd"/>
        <result property="wd" column="wd"/>
        <result property="yhms" column="yhms"/>
        <result property="czbmbm" column="czbmbm"/>
        <result property="czbmmc" column="czbmmc"/>
        <result property="czqx" column="czqx"/>
        <result property="clrxm" column="clrxm"/>
        <result property="yylj" column="yylj"/>
        <result property="tplj" column="tplj"/>
        <result property="czjzrq" column="czjzrq"/>
        <result property="scsj" column="scsj"/>
        <result property="dlzj" column="dlzj"/>
        <result property="xzsj" column="xzsj"/>
        <result property="zlbs" column="zlbs"/>
        <result property="zlsj" column="zlsj"/>
        <result property="pch" column="pch"/>
    </resultMap>


    <select id="queryDateByYear"
            resultType="com.zhonghe.system.domain.thirdparty.vo.SlCaseEventProxyGroupStatisticsVo">
        select count(1) as count, to_char(date_trunc('MONTH', SCSJ), 'YYYY-MM') as date
        from business.case_event
        where to_char(SCSJ, 'yyyy') = #{year}
        group by date_trunc('MONTH', SCSJ)
        order by date asc
    </select>
    <select id="queryQuarterByYear"
            resultType="com.zhonghe.system.domain.thirdparty.vo.SlCaseEventProxyGroupStatisticsVo">
        select count(1) as count, date_trunc('QUARTER', SCSJ) as date
        from business.case_event
        where to_char(SCSJ, 'yyyy') = #{year}
        group by date_trunc('QUARTER', SCSJ)
        order by date asc

    </select>
    <select id="queryGroupByType"
            resultType="com.zhonghe.system.domain.thirdparty.vo.SlCaseEventMainSubjectStatisticsVo">
        select count(1) as count, d.scztlx as name
        from business.case_event e
                 left join business.dgsscztxx d on e.ZTMC = d.scztmc
        group by d.scztlx
    </select>
    <select id="queryGroupByAddress"
            resultType="com.zhonghe.system.domain.thirdparty.vo.SlCaseEventStatisticsVo">
        select count(1) as count
        from public.t_integration_clue e
        <where>
            e.build_address in
            <foreach collection="addresses" separator="," open="(" close=")" item="address">
                #{address}
            </foreach>
        </where>
    </select>

    <select id="queryOneMonthOfToDay"
            resultType="com.zhonghe.system.domain.thirdparty.vo.SlCaseEventProxyGroupStatisticsVo">
        select count(1) as count, to_char(SCSJ, 'YYYY-MM-dd') as date
        from business.case_event
        where  <![CDATA[ SCSJ <= #{now}
          and SCSJ >= #{old}]]>
        group by date
        order by date asc
    </select>
    <select id="selectMarkerCaseEventCount" resultType="java.lang.Long">
        select count(1) from t_integration_clue tic inner join zsj_entreg_info zei on tic.build_address  = zei.dom
    </select>
    <select id="queryOneDay"
            resultType="java.lang.Long">
        select count(1)
        from business.case_event
        where to_char(SCSJ, 'YYYY-MM-dd') = #{date}
    </select>


</mapper>
