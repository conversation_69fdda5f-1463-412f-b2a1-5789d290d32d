<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhonghe.system.mapper.IntegrationClueMapper">

    <resultMap type="com.zhonghe.system.domain.thirdparty.IntegrationClue" id="IntegrationClueResult">
        <result property="id" column="id"/>
        <result property="dangerId" column="danger_id"/>
        <result property="subjectId" column="subject_id"/>
        <result property="subjectName" column="subject_name"/>
        <result property="buildAddress" column="build_address"/>
        <result property="lng" column="lng"/>
        <result property="lat" column="lat"/>
        <result property="dangerDescribe" column="danger_describe"/>
        <result property="disposalDeptNo" column="disposal_dept_no"/>
        <result property="disposalDeptName" column="disposal_dept_name"/>
        <result property="disPeriod" column="dis_period"/>
        <result property="createName" column="create_name"/>
        <result property="soundUrl" column="sound_url"/>
        <result property="imageUrl" column="image_url"/>
        <result property="verTerm" column="ver_term"/>
        <result property="createDate" column="create_date"/>
        <result property="dsesUuid" column="dses_uuid"/>
        <result property="addTime" column="add_time"/>
        <result property="cdOperation" column="cd_operation"/>
        <result property="cdTime" column="cd_time"/>
        <result property="cdBatch" column="cd_batch"/>
        <result property="dmpShareCdTimeIdatat" column="dmp_share_cd_time_idatat"/>
        <result property="dataSharingCdTimeIdatat" column="data_sharing_cd_time_idatat"/>
    </resultMap>
    <select id="queryOneDay" resultType="java.lang.Long">
        select count(1)
        from public.t_integration_clue tic
        where to_char(create_date, 'YYYY-MM-dd') = #{date}
    </select>
    <select id="queryQuarterByYear"
            resultType="com.zhonghe.system.domain.thirdparty.vo.SlCaseEventProxyGroupStatisticsVo">
        select count(1) as count, date_trunc('QUARTER', create_date) as date
        from public.t_integration_clue
        where to_char(create_date, 'yyyy') = #{year}
        group by date_trunc('QUARTER', create_date)
        order by date asc
    </select>


</mapper>
