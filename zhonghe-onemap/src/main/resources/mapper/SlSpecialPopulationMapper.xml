<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhonghe.system.mapper.SlSpecialPopulationMapper">

    <resultMap type="com.zhonghe.system.domain.SlSpecialPopulation" id="SlSpecialPopulationResult">
        <result property="specialId" column="special_id"/>
        <result property="gridName" column="grid_name"/>
        <result property="netName" column="net_name"/>
        <result property="name" column="name"/>
        <result property="sex" column="sex"/>
        <result property="age" column="age"/>
        <result property="idCode" column="id_code"/>
        <result property="phone" column="phone"/>
        <result property="livingLocation" column="living_location"/>
        <result property="livingType" column="living_type"/>
        <result property="populationType" column="population_type"/>
        <result property="vaccinum" column="vaccinum"/>
        <result property="healthType" column="health_type"/>
        <result property="bodyType" column="body_type"/>
        <result property="medicine" column="medicine"/>
        <result property="doctor" column="doctor"/>
        <result property="helpManager" column="help_manager"/>
        <result property="gridManager" column="grid_manager"/>
        <result property="mark" column="mark"/>
        <result property="delFlag" column="del_flag"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>
    <select id="queryAddress" resultType="com.zhonghe.system.domain.thirdparty.vo.SpecialistVo">
        select important.name as name , important.id_code as code , important.phone as phone , important.population_type as type,important.living_location as address
        from business.sl_special_population important
        <where>
            important.living_location = #{address}
        </where>

    </select>


</mapper>
