<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhonghe.system.mapper.SlMarketEnterpriseOnenetMapper">

    <resultMap type="com.zhonghe.system.domain.SlMarketEnterpriseOnenet" id="SlMarketEnterpriseOnenetResult">
        <result property="marketEnterpriseOnenetId" column="market_enterprise_onenet_id"/>
        <result property="marketEnterpriseId" column="market_enterprise_id"/>
        <result property="enterpriseCreditCode" column="enterprise_credit_code"/>
        <result property="enterpriseName" column="enterprise_name"/>
        <result property="enterpriseAddress" column="enterprise_address"/>
        <result property="marketEnterpriseGem" column="market_enterprise_gem"/>
        <result property="fid" column="fid"/>
        <result property="onenetJson" column="onenet_json"/>
        <result property="delFlag" column="del_flag"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="enterpriseType" column="enterprise_type"/>
        <result property="createDate" column="create_date"/>
        <result property="operatingEndDate" column="operating_end_date"/>
        <result property="operatingScope" column="operating_scope"/>
        <result property="legalPerson" column="legal_person"/>
        <result property="idType" column="id_type"/>
        <result property="idCode" column="id_code"/>
        <result property="currencyRegister" column="currency_register"/>
    </resultMap>
    <delete id="deleteBatchIds">
        delete from business.sl_market_enterprise_onenet
        <where>
            market_enterprise_id in
            <if test="dataIds!=null and dataIds.size()>0">
                <foreach collection="dataIds" item="id" close=")" open="(" separator=",">
                    #{id}
                </foreach>
            </if>
        </where>
    </delete>


</mapper>
