<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhonghe.system.mapper.SlOldPopulationMapper">

    <resultMap type="com.zhonghe.system.domain.SlOldPopulation" id="SlOldPopulationResult">
        <result property="oldId" column="old_id"/>
        <result property="gridName" column="grid_name"/>
        <result property="name" column="name"/>
        <result property="sex" column="sex"/>
        <result property="idCode" column="id_code"/>
        <result property="age" column="age"/>
        <result property="baseLocation" column="base_location"/>
        <result property="populationType" column="population_type"/>
        <result property="livingNursing" column="living_nursing"/>
        <result property="disableLv" column="disable_lv"/>
        <result property="livingLocation" column="living_location"/>
        <result property="workingLocation" column="working_location"/>
        <result property="delFlag" column="del_flag"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>

    <select id="queryAddress" resultType="com.zhonghe.system.domain.thirdparty.vo.SpecialistVo">
        select important.name as name , important.id_code as code , '/' as phone , important.population_type as type,important.base_location as address
        from business.sl_old_population important
        <where>
            important.base_location = #{address} or important.living_location = #{address} or important.working_location = #{address}
        </where>
    </select>


</mapper>
