<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhonghe.system.mapper.SlSocialWorkerOnenetMapper">

    <resultMap type="com.zhonghe.system.domain.SlSocialWorkerOnenet" id="SlSocialWorkerOnenetResult">
        <result property="onenetWorkerId" column="onenet_worker_id"/>
        <result property="socialWorkerId" column="social_worker_id"/>
        <result property="onenetWorkLocation" column="onenet_work_location"/>
        <result property="onenetTelephone" column="onenet_telephone"/>
        <result property="onenetSex" column="onenet_sex"/>
        <result property="onenetIdNo" column="onenet_id_no"/>
        <result property="onenetIdType" column="onenet_id_type"/>
        <result property="onenetJson" column="onenet_json"/>
        <result property="delFlag" column="del_flag"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="onenetName" column="onenet_name"/>
        <result property="workerIdNumber" column="worker_id_number"/>
        <result property="workerPhone" column="worker_phone"/>
        <result property="mark" column="mark"/>
        <result property="workPosition" column="work_position"/>
        <result property="standardPosition" column="standard_position"/>
    </resultMap>


</mapper>
