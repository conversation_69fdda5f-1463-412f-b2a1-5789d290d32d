<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhonghe.system.mapper.SlGasOnenetMapper">

    <resultMap type="com.zhonghe.system.domain.SlGasOnenet" id="SlGasOnenetResult">
        <result property="onenetGasId" column="onenet_gas_id"/>
        <result property="gasId" column="gas_id"/>
        <result property="gasSubjectName" column="gas_subject_name"/>
        <result property="gasGem" column="gas_gem"/>
        <result property="fid" column="fid"/>
        <result property="onenetJson" column="onenet_json"/>
        <result property="delFlag" column="del_flag"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="sectionName" column="section_name"/>
        <result property="subjectName" column="subject_name"/>
        <result property="subjectAddress" column="subject_address"/>
        <result property="subjectManager" column="subject_manager"/>
        <result property="subjectPhone" column="subject_phone"/>
        <result property="gridManager" column="grid_manager"/>
        <result property="powerType" column="power_type"/>
        <result property="powerTypeMark" column="power_type_mark"/>
        <result property="powerStoreMark" column="power_store_mark"/>
        <result property="powerStoreLargeNum" column="power_store_large_num"/>
        <result property="powerStoreHugeNum" column="power_store_huge_num"/>
        <result property="powerStoreSmallNum" column="power_store_small_num"/>
        <result property="questionMark" column="question_mark"/>
        <result property="questionGasBottle" column="question_gas_bottle"/>
        <result property="questionGasPipeline" column="question_gas_pipeline"/>
        <result property="questionGasEnvironment" column="question_gas_environment"/>
        <result property="questionGasWarmingMachine" column="question_gas_warming_machine"/>
        <result property="questionGasStoreWeight" column="question_gas_store_weight"/>
        <result property="questionGasStoreLocation" column="question_gas_store_location"/>
        <result property="questionGasIllegal" column="question_gas_illegal"/>
        <result property="questionRectification" column="question_rectification"/>
        <result property="gatherTime" column="gather_time"/>
        <result property="standardLocation" column="standard_location"/>
    </resultMap>


</mapper>
