<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhonghe.system.mapper.SlGridStructureCharacterRelMapper">

    <resultMap type="com.zhonghe.system.domain.SlGridStructureCharacterRel" id="SlGridStructureCharacterRelResult">
        <result property="relationId" column="relation_id"/>
        <result property="gridCharacterId" column="grid_character_id"/>
        <result property="gridStructureId" column="grid_structure_id"/>
        <result property="delFlag" column="del_flag"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>


</mapper>
