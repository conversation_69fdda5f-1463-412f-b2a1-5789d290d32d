<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhonghe.system.mapper.SlSocialWorkerMapper">

    <resultMap type="com.zhonghe.system.domain.SlSocialWorker" id="SlSocialWorkerResult">
        <result property="socialWorkerId" column="social_worker_id"/>
        <result property="workerName" column="worker_name"/>
        <result property="workerAge" column="worker_age"/>
        <result property="sex" column="sex"/>
        <result property="workerIdNumber" column="worker_id_number"/>
        <result property="workerPhone" column="worker_phone"/>
        <result property="mark" column="mark"/>
        <result property="workPosition" column="work_position"/>
        <result property="standardPosition" column="standard_position"/>
        <result property="delFlag" column="del_flag"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>


</mapper>
