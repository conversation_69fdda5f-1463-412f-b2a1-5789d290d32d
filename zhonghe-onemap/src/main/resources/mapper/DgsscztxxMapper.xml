<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhonghe.system.mapper.DgsscztxxMapper">

    <resultMap type="com.zhonghe.system.domain.thirdparty.Dgsscztxx" id="DgsscztxxResult">
        <result property="xyml" column="xyml"/>
        <result property="dlzj" column="dlzj"/>
        <result property="scbz" column="scbz"/>
        <result property="etlrq" column="etlrq"/>
        <result property="cyrs" column="cyrs"/>
        <result property="zj" column="zj"/>
        <result property="fddbrxm" column="fddbrxm"/>
        <result property="xydm" column="xydm"/>
        <result property="jyzzqxz" column="jyzzqxz"/>
        <result property="scztmc" column="scztmc"/>
        <result property="cs" column="cs"/>
        <result property="jssj" column="jssj"/>
        <result property="kssj" column="kssj"/>
        <result property="scztlx" column="scztlx"/>
        <result property="zsdzbm" column="zsdzbm"/>
        <result property="tyshxydm" column="tyshxydm"/>
        <result property="ztsfdm" column="ztsfdm"/>
        <result property="gb" column="gb"/>
        <result property="hzrq" column="hzrq"/>
        <result property="djgxjg" column="djgxjg"/>
        <result property="zczb" column="zczb"/>
        <result property="jyzt" column="jyzt"/>
        <result property="gxsj" column="gxsj"/>
        <result property="jyfw" column="jyfw"/>
        <result property="jyzzqxz1" column="jyzzqxz1"/>
        <result property="zch" column="zch"/>
        <result property="clrq" column="clrq"/>
    </resultMap>
    <select id="selectSlaverCount" resultType="java.lang.Long">
        select sum(cyrs) as ct
        from business.dgsscztxx d
        where cyrs is not null
    </select>
    <select id="marketEventStatistics"
            resultType="com.zhonghe.system.domain.thirdparty.vo.SlCaseEventStatisticsVo">
        select d.SXFLMC as name, count(1) as count
        from business.dgsscztxx e
                 inner join business.sl_grid_patrol_order d
                            on e.zj = d.ztid
        group by d.SXFLMC
    </select>
    <select id="selectByZj" resultType="com.zhonghe.system.domain.thirdparty.Dgsscztxx">
        select
        zj,
        xyml,
        dlzj,
        scbz,
        etlrq,
        cyrs,
        fddbrxm,
        xydm,
        jyzzqxz,
        scztmc,
        "cs",
        jssj,
        kssj,
        scztlx,
        zsdzbm,
        tyshxydm,
        ztsfdm,
        gb,

        hzrq,
        djgxjg,
        zczb,
        jyzt,
        gxsj,
        jyfw,
        jyzzqxz1,
        zch,
        clrq
        from business.dgsscztxx
        <where>
            zj = #{zj}
        </where>
    </select>
    <select id="queryPage" resultType="com.zhonghe.system.domain.thirdparty.vo.DgsscztxxVo">

        select zj,
        xyml,
        dlzj,
        scbz,
        etlrq,
        cyrs,
        fddbrxm,
        xydm,
        jyzzqxz,
        scztmc,
        "cs",
        jssj,
        kssj,
        scztlx,
        zsdzbm,
        tyshxydm,
        ztsfdm,
        gb,
        hzrq,
        djgxjg,
        zczb,
        jyzt,
        gxsj,
        jyfw,
        jyzzqxz1,
        zch,
        clrq
        from business.dgsscztxx
        <where>
            <if test="key == null and type !=null">
                <if test="type == 1">
                    scztlx = '个体工商户'
                </if>
                <if test="type == 2">
                    scztlx != '个体工商户'
                </if>
                <if test="type == 3">

                </if>
            </if>
            <if test="key!=null">
                (
                tyshxydm like concat('%',#{key}, '%')
                or
                "cs" like concat('%',#{key}, '%')
                or
                scztmc like concat('%',#{key}, '%')
                or
                fddbrxm like concat('%',#{key}, '%')
                or
                jyfw like concat('%',#{key}, '%')
                )
                <if test="type!=null">
                    <if test="type == 1">
                        and scztlx = '个体工商户'
                    </if>
                    <if test="type == 2">
                        and scztlx != '个体工商户'
                    </if>
                    <if test="type == 3">

                    </if>
                </if>
            </if>
        </where>
        order by scztmc desc
    </select>


</mapper>
