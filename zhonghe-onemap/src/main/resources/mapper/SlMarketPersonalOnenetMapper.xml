<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhonghe.system.mapper.SlMarketPersonalOnenetMapper">

    <resultMap type="com.zhonghe.system.domain.SlMarketPersonalOnenet" id="SlMarketPersonalOnenetResult">
        <result property="marketPersonalOnenetId" column="market_personal_onenet_id"/>
        <result property="marketPersonalId" column="market_personal_id"/>
        <result property="personalCreditCode" column="personal_credit_code"/>
        <result property="personalName" column="personal_name"/>
        <result property="personalAddress" column="personal_address"/>
        <result property="marketPersonalGem" column="market_personal_gem"/>
        <result property="fid" column="fid"/>
        <result property="onenetJson" column="onenet_json"/>
        <result property="delFlag" column="del_flag"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="personalType" column="personal_type"/>
        <result property="createDate" column="create_date"/>
        <result property="operatingEndDate" column="operating_end_date"/>
        <result property="operatingScope" column="operating_scope"/>
        <result property="legalPerson" column="legal_person"/>
        <result property="idType" column="id_type"/>
        <result property="idCode" column="id_code"/>
        <result property="currencyRegister" column="currency_register"/>
    </resultMap>
    <delete id="deleteBatchIds">
        delete from business.sl_market_personal_onenet
        <where>
            market_personal_id in
            <if test="marketIds !=null and marketIds.size() > 0">
                <foreach collection="marketIds" separator="," open="(" close=")" item="id">
                    #{id}
                </foreach>
            </if>
        </where>
    </delete>


</mapper>
