<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhonghe.system.mapper.TaskOrderNewMapper">

    <resultMap type="com.zhonghe.system.domain.thirdparty.TaskOrderNew" id="TaskOrderNewResult">
        <result property="zjid" column="zjid"/>
        <result property="orderName" column="order_name"/>
        <result property="subjectName" column="subject_name"/>
        <result property="buildAddress" column="build_address"/>
        <result property="itemClassName" column="item_class_name"/>
        <result property="labelName" column="label_name"/>
        <result property="updateDate" column="update_date"/>
        <result property="createDate" column="create_date"/>
        <result property="deptName" column="dept_name"/>
        <result property="checkState" column="check_state"/>
        <result property="checkEndDate" column="check_end_date"/>
    </resultMap>
    <select id="marketEventStatistics"
            resultType="com.zhonghe.system.domain.thirdparty.vo.SlCaseEventStatisticsVo">
        select sum(check_num)    as count,
               m.item_class_name as name
        from t_task_order_new m
                 inner join zsj_entreg_info n on
            m.subject_name = n.entname
        group by item_class_name
        order by count desc
    </select>


</mapper>
