<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhonghe.system.mapper.SlWeatherStationOnenetMapper">

    <resultMap type="com.zhonghe.system.domain.SlWeatherStationOnenet" id="SlWeatherStationOnenetResult">
        <result property="stationOnenetId" column="station_onenet_id"/>
        <result property="stationName" column="station_name"/>
        <result property="stationGem" column="station_gem"/>
        <result property="fid" column="fid"/>
        <result property="delFlag" column="del_flag"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>


</mapper>
