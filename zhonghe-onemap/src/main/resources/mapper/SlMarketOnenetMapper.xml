<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhonghe.system.mapper.SlMarketOnenetMapper">

    <resultMap type="com.zhonghe.system.domain.SlMarketOnenet" id="SlMarketOnenetResult">
        <result property="marketId" column="market_id"/>
        <result property="marketDataId" column="market_data_id"/>
        <result property="marketAddress" column="market_address"/>
        <result property="fid" column="fid"/>
        <result property="delFlag" column="del_flag"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="marketCode" column="market_code"/>
        <result property="marketName" column="market_name"/>
        <result property="marketManager" column="market_manager"/>
        <result property="businessScope" column="business_scope"/>
    </resultMap>
    <delete id="deleteBatchIds">
        delete from business.sl_market_onenet
        <where>
            <if test="marketDataIds!=null and marketDataIds.size() > 0">
                market_data_id in
                <foreach collection="marketDataIds" item="dataId" close=")" open="(" separator=",">
                    #{dataId}
                </foreach>
            </if>
        </where>
    </delete>


</mapper>
