<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhonghe.system.mapper.T2031000000026000005V1Mapper">

    <resultMap type="com.zhonghe.system.domain.thirdparty.T2031000000026000005V1" id="T2031000000026000005V1Result">
        <result property="f2031000000026000005001" column="f2031000000026_000005001"/>
        <result property="f2031000000026000005002" column="f2031000000026_000005002"/>
        <result property="f2031000000026000005004" column="f2031000000026_000005004"/>
        <result property="f2031000000026000005005" column="f2031000000026_000005005"/>
        <result property="f2031000000026000005008" column="f2031000000026_000005008"/>
        <result property="f2031000000026000005009" column="f2031000000026_000005009"/>
        <result property="f2031000000026000005012" column="f2031000000026_000005012"/>
        <result property="f2031000000026000005013" column="f2031000000026_000005013"/>
        <result property="f2031000000026000005017" column="f2031000000026_000005017"/>
        <result property="f2031000000026000005019" column="f2031000000026_000005019"/>
        <result property="f2031000000026000005020" column="f2031000000026_000005020"/>
        <result property="f2031000000026000005021" column="f2031000000026_000005021"/>
        <result property="f2031000000026000005022" column="f2031000000026_000005022"/>
        <result property="f2031000000026000005023" column="f2031000000026_000005023"/>
        <result property="f2031000000026000005024" column="f2031000000026_000005024"/>
        <result property="f2031000000026000005025" column="f2031000000026_000005025"/>
        <result property="f2031000000026000005026" column="f2031000000026_000005026"/>
        <result property="f2031000000026000005027" column="f2031000000026_000005027"/>
        <result property="f2031000000026000005028" column="f2031000000026_000005028"/>
        <result property="f2031000000026000005029" column="f2031000000026_000005029"/>
        <result property="f2031000000026000005030" column="f2031000000026_000005030"/>
        <result property="f2031000000026000005031" column="f2031000000026_000005031"/>
        <result property="f2031000000026000005032" column="f2031000000026_000005032"/>
        <result property="f2031000000026000005033" column="f2031000000026_000005033"/>
        <result property="f2031000000026000005034" column="f2031000000026_000005034"/>
        <result property="cdTime" column="cd_time"/>
        <result property="dmpShareCdTimeIdatat" column="dmp_share_cd_time_idatat"/>
        <result property="dataSharingCdTimeIdatat" column="data_sharing_cd_time_idatat"/>
    </resultMap>
    <select id="queryByAddress" resultType="com.zhonghe.system.domain.thirdparty.vo.SpecialistVo">
        select city.f2031000000026_000005001 as name , city.f2031000000026_000005020 as code ,
               city.f2031000000026_000005022 as phone ,
               '低边家庭' as type,city.f2031000000026_000005021 as address
        from public.t2031000000026_000005_v1 city
        <where>
            city.f2031000000026_000005021 = #{address}
        </where>
    </select>


</mapper>
