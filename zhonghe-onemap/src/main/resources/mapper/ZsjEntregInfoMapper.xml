<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhonghe.system.mapper.ZsjEntregInfoMapper">

    <resultMap type="com.zhonghe.system.domain.thirdparty.ZsjEntregInfo" id="ZsjEntregInfoResult">
        <result property="id" column="id"/>
        <result property="pripid" column="pripid"/>
        <result property="uniscid" column="uniscid"/>
        <result property="regno" column="regno"/>
        <result property="entname" column="entname"/>
        <result property="typename" column="typename"/>
        <result property="opscope" column="opscope"/>
        <result property="aicid" column="aicid"/>
        <result property="regcap" column="regcap"/>
        <result property="country" column="country"/>
        <result property="estdate" column="estdate"/>
        <result property="apprdate" column="apprdate"/>
        <result property="opstate" column="opstate"/>
        <result property="lerepname" column="lerepname"/>
        <result property="opfrom" column="opfrom"/>
        <result property="opto" column="opto"/>
        <result property="industryphy" column="industryphy"/>
        <result property="industryco" column="industryco"/>
        <result property="dom" column="dom"/>
        <result property="faxnumber" column="faxnumber"/>
        <result property="empnum" column="empnum"/>
        <result property="datDt" column="dat_dt"/>
        <result property="etlDt" column="etl_dt"/>
        <result property="gldmStaDt" column="gldm_sta_dt"/>
        <result property="gldmEndDt" column="gldm_end_dt"/>
        <result property="gldmDelFlag" column="gldm_del_flag"/>
        <result property="dsesUuid" column="dses_uuid"/>
        <result property="industrycoVal" column="industryco_val"/>
        <result property="reservedField1" column="reserved_field1"/>
        <result property="reservedField2" column="reserved_field2"/>
        <result property="dataSharingCdTimeIdatat" column="data_sharing_cd_time_idatat"/>
    </resultMap>
    <select id="selectSlaverCount" resultType="java.lang.Long">
        select sum(cast(empnum as int)) as ct
        from public.zsj_entreg_info d
        where empnum is not null
    </select>


</mapper>
