<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhonghe.system.mapper.SlGridPatrolOrderMapper">

    <resultMap type="com.zhonghe.system.domain.thirdparty.SlGridPatrolOrder" id="SlGridPatrolOrderResult">
        <result property="fywzj" column="fywzj"/>
        <result property="zj" column="zj"/>
        <result property="ztid" column="ztid"/>
        <result property="gdmc" column="gdmc"/>
        <result property="sswgid" column="sswgid"/>
        <result property="gdlsh" column="gdlsh"/>
        <result property="xczt" column="xczt"/>
        <result property="scrq" column="scrq"/>
        <result property="xcqx" column="xcqx"/>
        <result property="cjrid" column="cjrid"/>
        <result property="cjrxm" column="cjrxm"/>
        <result property="cjsj" column="cjsj"/>
        <result property="gxrid" column="gxrid"/>
        <result property="gxrxm" column="gxrxm"/>
        <result property="gxsj" column="gxsj"/>
        <result property="bmid" column="bmid"/>
        <result property="bmmc" column="bmmc"/>
        <result property="sjrid" column="sjrid"/>
        <result property="sjrxm" column="sjrxm"/>
        <result property="zhjcrid" column="zhjcrid"/>
        <result property="zhjcrxm" column="zhjcrxm"/>
        <result property="jccs" column="jccs"/>
        <result property="scyhs" column="scyhs"/>
        <result property="sfcsyh" column="sfcsyh"/>
        <result property="kmqk" column="kmqk"/>
        <result property="txzt" column="txzt"/>
        <result property="ztlx" column="ztlx"/>
        <result property="ztmc" column="ztmc"/>
        <result property="jzid" column="jzid"/>
        <result property="jzdz" column="jzdz"/>
        <result property="sxflid" column="sxflid"/>
        <result property="sxflmc" column="sxflmc"/>
        <result property="zybqid" column="zybqid"/>
        <result property="sjsj" column="sjsj"/>
        <result property="zhjcsj" column="zhjcsj"/>
        <result property="gdjb" column="gdjb"/>
        <result property="gdpc" column="gdpc"/>
        <result property="zybqmc" column="zybqmc"/>
        <result property="clrdh" column="clrdh"/>
        <result property="cljg" column="cljg"/>
        <result property="ms" column="ms"/>
        <result property="qzwj" column="qzwj"/>
        <result property="wgmc" column="wgmc"/>
        <result property="wgid" column="wgid"/>
        <result property="xzsj" column="xzsj"/>
        <result property="zlbs" column="zlbs"/>
        <result property="zlsj" column="zlsj"/>
        <result property="pch" column="pch"/>
        <result property="scbs" column="scbs"/>
    </resultMap>
    <select id="queryStatisticByAddress"
            resultType="com.zhonghe.system.domain.thirdparty.vo.SlCaseEventStatisticsVo">
        select
        sum(m.check_num ) as count ,
        m.item_class_name as name
        from
        t_task_order_new m
        <where>
            m.build_address in
            <foreach collection="address" separator="," open="(" close=")" item="item">
                #{item}
            </foreach>
        </where>
        group by item_class_name order by count desc
    </select>


</mapper>
