<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhonghe.system.mapper.T2031000000026000003V2TbwMapper">

    <resultMap type="com.zhonghe.system.domain.thirdparty.T2031000000026000003V2Tbw" id="T2031000000026000003V2TbwResult">
        <result property="f2031000000026000003023" column="f2031000000026_000003023"/>
        <result property="f2031000000026000003004" column="f2031000000026_000003004"/>
        <result property="f2031000000026000003007" column="f2031000000026_000003007"/>
        <result property="f2031000000026000003010" column="f2031000000026_000003010"/>
        <result property="f2031000000026000003011" column="f2031000000026_000003011"/>
        <result property="f2031000000026000003015" column="f2031000000026_000003015"/>
        <result property="f2031000000026000003017" column="f2031000000026_000003017"/>
        <result property="f2031000000026000003018" column="f2031000000026_000003018"/>
        <result property="f2031000000026000003019" column="f2031000000026_000003019"/>
        <result property="f2031000000026000003020" column="f2031000000026_000003020"/>
        <result property="f2031000000026000003021" column="f2031000000026_000003021"/>
        <result property="f2031000000026000003022" column="f2031000000026_000003022"/>
        <result property="f2031000000026000003024" column="f2031000000026_000003024"/>
        <result property="f2031000000026000003025" column="f2031000000026_000003025"/>
        <result property="f2031000000026000003026" column="f2031000000026_000003026"/>
        <result property="f2031000000026000003027" column="f2031000000026_000003027"/>
        <result property="f2031000000026000003028" column="f2031000000026_000003028"/>
        <result property="f2031000000026000003029" column="f2031000000026_000003029"/>
        <result property="f2031000000026000003030" column="f2031000000026_000003030"/>
        <result property="f2031000000026000003031" column="f2031000000026_000003031"/>
        <result property="f2031000000026000003032" column="f2031000000026_000003032"/>
        <result property="f2031000000026000003033" column="f2031000000026_000003033"/>
        <result property="cdTime" column="cd_time"/>
        <result property="f2031000000026000003034" column="f2031000000026_000003034"/>
        <result property="f2031000000026000003001" column="f2031000000026_000003001"/>
        <result property="f2031000000026000003002" column="f2031000000026_000003002"/>
        <result property="f2031000000026000003003" column="f2031000000026_000003003"/>
        <result property="etlUpdateTime" column="etl_update_time"/>
        <result property="dmpShareCdTimeIdatat" column="dmp_share_cd_time_idatat"/>
        <result property="dataSharingCdTimeIdatat" column="data_sharing_cd_time_idatat"/>
    </resultMap>
    <select id="queryByAddress" resultType="com.zhonghe.system.domain.thirdparty.vo.SpecialistVo">
        select city.f2031000000026_000003017 as name , city.f2031000000026_000003018 as code , city.f2031000000026_000003021 as phone , '城市低保对象' as type,city.f2031000000026_000003026 as address
        from public.t2031000000026_000003_v2_tbw city
        <where>
            city.f2031000000026_000003026 = #{address}
        </where>
    </select>


</mapper>
