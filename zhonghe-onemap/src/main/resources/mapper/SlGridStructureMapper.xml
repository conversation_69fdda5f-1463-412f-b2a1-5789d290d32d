<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhonghe.system.mapper.SlGridStructureMapper">

    <resultMap type="com.zhonghe.system.domain.SlGridStructure" id="SlGridStructureResult">
        <result property="gridStructureId" column="grid_structure_id"/>
        <result property="gridStructureParentName" column="grid_structure_parent_name"/>
        <result property="girdStructureName" column="gird_structure_name"/>
        <result property="gridStructureParentId" column="grid_structure_parent_id"/>
        <result property="gridZoneName" column="grid_zone_name"/>
        <result property="mark" column="mark"/>
        <result property="delFlag" column="del_flag"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>


</mapper>
