package com.zhonghe.system.auto.init;

import com.zhonghe.common.utils.spring.SpringUtils;
import com.zhonghe.system.auto.AutoRequest;
import com.zhonghe.system.schedule.ScheduledTaskComponent;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.lang.reflect.Method;

/**
 * @description:
 * @author: cq
 * @date: 2023/8/29 15:20
 */
//@Component
@AllArgsConstructor
@Slf4j
public class AutoRequestRegisterRunner implements ApplicationRunner {

    private final ScheduledTaskComponent scheduledTaskComponent;


    @Override
    public void run(ApplicationArguments args) throws Exception {
        String[] beanNames = SpringUtils.getApplicationContext().getBeanNamesForAnnotation(RestController.class);
        for (String beanName : beanNames) {
            Object controller = SpringUtils.getApplicationContext().getBean(beanName);
            Class<?> controllerClass = controller.getClass();
            RequestMapping requestMapping = AnnotationUtils.findAnnotation(controllerClass, RequestMapping.class);
            // 获取标注了 @RequestMapping 注解的方法
            Method[] methods = controllerClass.getDeclaredMethods();
            for (Method method : methods) {
                AutoRequest annotation = AnnotationUtils.findAnnotation(method, AutoRequest.class);
                if (annotation != null) {
                    GetMapping getMapping = AnnotationUtils.findAnnotation(method, GetMapping.class);
                    if (getMapping != null && requestMapping != null) {
                        String[] endpoints = getMapping.value();
                        String basePath = String.join(",", requestMapping.value());
                        String path = String.join(", ", endpoints);
                        log.info("AutoRequest annotated method found: {} - Endpoints: {}", method.getName(), basePath + String.join(", ", endpoints));
                        if (!basePath.endsWith("/") && !path.startsWith("/")) {
                            basePath += "/";
                        }
                        scheduledTaskComponent.register(basePath + path, controller, method, annotation);
                    }
                }
            }
        }
    }
}

