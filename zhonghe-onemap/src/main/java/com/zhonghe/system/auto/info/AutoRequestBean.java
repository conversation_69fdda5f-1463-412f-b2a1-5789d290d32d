package com.zhonghe.system.auto.info;

import com.zhonghe.system.auto.IParamService;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.lang.reflect.Method;

/**
 * @description:
 * @author: cq
 * @date: 2023/8/29 15:31
 */

@Data
@AllArgsConstructor
public class AutoRequestBean {

    private Object bean;

    private Method method;

    private long updateTime;

    private long frequency;

    private IParamService service;
}

