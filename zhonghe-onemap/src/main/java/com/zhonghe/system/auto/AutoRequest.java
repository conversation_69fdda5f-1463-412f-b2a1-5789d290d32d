package com.zhonghe.system.auto;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * @description:
 * @author: cq
 * @date: 2023/8/29 15:18
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface AutoRequest {

    long frequency() default 60 * 60 * 1000;

    Class<? extends IParamService> clazz() default DefaultImpl.class;

    class DefaultImpl implements IParamService {
    }

}
