package com.zhonghe.system.constants;

/**
 * @description:
 * @author: cq
 * @date: 2023/7/31 11:54
 */

public interface SlOneMapConstants {
    /**
     * 未整改
     */
    String UN_RECTIFICATION = "0";

    String OK = "0";
    /**
     * 燃气类型
     */
    String GAS_TYPE = "1";

    String POWER_TYPE_GAS = "1";
    String POWER_TYPE_BOTTLE = "2";

    String POWER_TYPE_ELECTRIC = "3";

    /**
     * 黑气
     */
    String ILLEGAL_GAS = "1";

    String GAS_BOTTLE_QUESTION = "1";

    String GAS_PIPELINE_QUESTION = "1";


    String GAS_MACHINE_QUESTION = "1";
    String GAS_STORE_QUESTION = "1";
    String GAS_LOCATION_QUESTION = "1";
    String GAS_ENVIRONMENT_QUESTION = "1";
    String GAS_BOTTLE_QUESTION_CN = "气瓶隐患";
    String GAS_PIPELINE_QUESTION_CN = "管道隐患";
    String GAS_MACHINE_QUESTION_CN = "报警器隐患";
    String GAS_STORE_QUESTION_CN = "气瓶存储隐患";
    String GAS_LOCATION_QUESTION_CN = "气瓶摆放隐患";
    String GAS_ENVIRONMENT_QUESTION_CN = "用气环境隐患";


    /**
     * 服务力量类型:
     * 0:一支队伍
     * 1:社工
     * 2:自愿者
     */
    String SL_SERVICE_TYPE_ONE_TEAM = "一支队伍";
    String SL_SERVICE_TYPE_SOCIAL_WORKER = "社工";
    String SL_SERVICE_TYPE_VOLUNTEER = "志愿者";

    /**
     * 月度查询
     */
    String CASE_EVENT_MONTH_QUERY_TYPE = "month";
    String MAP_REDIS_TOKEN = "MAP_REDIS_TOKEN";


    String[] WEATHER_QUESTION = new String[]{"台风", "暴雨", "高温", "寒冷", "大雾", "灰霾天气", "道路结冰", "冰雹", "森林火险"};

    /**
     * 定时更新接口校验数据变动,以及MQTT通知发送跟新消息类型
     */
    String UPDATE_KEY = "UPDATE_REQUEST_VALUE";
    int UPDATE_MQTT_MSG_TYPE = 999;
    String MARKET_TYPE_PERSONAL = "个体工商户";
    int TOP_THREE = 3;
    String OTHER = "其它";
}
