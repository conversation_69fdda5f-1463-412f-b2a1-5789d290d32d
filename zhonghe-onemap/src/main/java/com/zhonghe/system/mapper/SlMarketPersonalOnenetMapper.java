package com.zhonghe.system.mapper;

import com.zhonghe.common.core.mapper.BaseMapperPlus;
import com.zhonghe.system.domain.SlMarketPersonalOnenet;
import com.zhonghe.system.domain.vo.SlMarketPersonalOnenetVo;
import org.apache.ibatis.annotations.Param;

import java.util.Set;

/**
 * 【请填写功能名称】Mapper接口
 *
 * <AUTHOR>
 * @date 2023-08-14
 */
public interface SlMarketPersonalOnenetMapper extends BaseMapperPlus<SlMarketPersonalOnenetMapper, SlMarketPersonalOnenet, SlMarketPersonalOnenetVo> {

    void deleteBatchIds(@Param("marketIds") Set<String> marketIds);
}
