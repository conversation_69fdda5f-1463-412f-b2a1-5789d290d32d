package com.zhonghe.system.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.zhonghe.common.core.mapper.BaseMapperPlus;
import com.zhonghe.system.domain.thirdparty.TaskOrderNew;
import com.zhonghe.system.domain.thirdparty.vo.SlCaseEventStatisticsVo;
import com.zhonghe.system.domain.thirdparty.vo.TaskOrderNewVo;

import java.util.List;

/**
 * @description:
 * @author: cq
 * @date: 2023/11/2 16:44
 */

@DS("data")
public interface TaskOrderNewMapper extends BaseMapperPlus<TaskOrderNewMapper, TaskOrderNew, TaskOrderNewVo> {

    List<SlCaseEventStatisticsVo> marketEventStatistics();
}
