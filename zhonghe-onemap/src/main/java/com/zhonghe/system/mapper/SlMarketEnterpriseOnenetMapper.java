package com.zhonghe.system.mapper;

import com.zhonghe.common.core.mapper.BaseMapperPlus;
import com.zhonghe.system.domain.SlMarketEnterpriseOnenet;
import com.zhonghe.system.domain.vo.SlMarketEnterpriseOnenetVo;
import org.apache.ibatis.annotations.Param;

import java.util.Set;

/**
 * 【请填写功能名称】Mapper接口
 *
 * <AUTHOR>
 * @date 2023-08-14
 */
public interface SlMarketEnterpriseOnenetMapper extends BaseMapperPlus<SlMarketEnterpriseOnenetMapper, SlMarketEnterpriseOnenet, SlMarketEnterpriseOnenetVo> {

    void deleteBatchIds(@Param("dataIds") Set<String> ids);
}
