package com.zhonghe.system.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.zhonghe.common.core.mapper.BaseMapperPlus;
import com.zhonghe.system.domain.thirdparty.T2031000000026000005V1;
import com.zhonghe.system.domain.thirdparty.vo.SpecialistVo;
import com.zhonghe.system.domain.thirdparty.vo.T2031000000026000005V1Vo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 【请填写功能名称】Mapper接口
 *
 * <AUTHOR>
 * @date 2023-09-14
 */
@DS("data")
public interface T2031000000026000005V1Mapper extends BaseMapperPlus<T2031000000026000005V1Mapper, T2031000000026000005V1, T2031000000026000005V1Vo> {

    List<SpecialistVo> queryByAddress(@Param("address") String address);
}
