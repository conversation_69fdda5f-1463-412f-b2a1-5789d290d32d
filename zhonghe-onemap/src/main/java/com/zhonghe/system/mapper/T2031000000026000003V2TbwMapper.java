package com.zhonghe.system.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.zhonghe.common.core.mapper.BaseMapperPlus;
import com.zhonghe.system.domain.thirdparty.T2031000000026000003V2Tbw;
import com.zhonghe.system.domain.thirdparty.vo.SpecialistVo;
import com.zhonghe.system.domain.thirdparty.vo.T2031000000026000003V2TbwVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 【请填写功能名称】Mapper接口
 *
 * <AUTHOR>
 * @date 2023-09-14
 */
@DS("data")
public interface T2031000000026000003V2TbwMapper extends BaseMapperPlus<T2031000000026000003V2TbwMapper, T2031000000026000003V2Tbw, T2031000000026000003V2TbwVo> {

    List<SpecialistVo> queryByAddress(@Param("address") String address);
}
