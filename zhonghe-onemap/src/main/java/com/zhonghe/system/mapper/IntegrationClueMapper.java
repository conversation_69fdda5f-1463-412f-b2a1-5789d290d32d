package com.zhonghe.system.mapper;

import com.zhonghe.common.core.mapper.BaseMapperPlus;
import com.zhonghe.system.domain.thirdparty.IntegrationClue;
import com.zhonghe.system.domain.thirdparty.vo.IntegrationClueVo;
import com.zhonghe.system.domain.thirdparty.vo.SlCaseEventProxyGroupStatisticsVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @description:
 * @author: cq
 * @date: 2023/11/7 10:32
 */

public interface IntegrationClueMapper extends BaseMapperPlus<IntegrationClueMapper, IntegrationClue, IntegrationClueVo> {
    Long queryOneDay(@Param("date") String date);

    List<SlCaseEventProxyGroupStatisticsVo> queryQuarterByYear(@Param("year") String year);
}
