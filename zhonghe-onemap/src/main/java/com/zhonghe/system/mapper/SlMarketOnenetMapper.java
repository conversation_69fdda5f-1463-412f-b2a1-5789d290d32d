package com.zhonghe.system.mapper;

import com.zhonghe.common.core.mapper.BaseMapperPlus;
import com.zhonghe.system.domain.SlMarketOnenet;
import com.zhonghe.system.domain.vo.SlMarketOnenetVo;
import org.apache.ibatis.annotations.Param;

import java.util.Set;

/**
 * 前置机市场地图Mapper接口
 *
 * <AUTHOR>
 * @date 2023-08-21
 */
public interface SlMarketOnenetMapper extends BaseMapperPlus<SlMarketOnenetMapper, SlMarketOnenet, SlMarketOnenetVo> {

    void deleteBatchIds(@Param("marketDataIds") Set<String> collect);
}
