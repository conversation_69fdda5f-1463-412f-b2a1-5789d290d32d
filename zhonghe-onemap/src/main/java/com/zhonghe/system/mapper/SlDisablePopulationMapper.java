package com.zhonghe.system.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.zhonghe.common.core.mapper.BaseMapperPlus;
import com.zhonghe.system.domain.SlDisablePopulation;
import com.zhonghe.system.domain.thirdparty.vo.SpecialistVo;
import com.zhonghe.system.domain.vo.SlDisablePopulationVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 残疾人Mapper接口
 *
 * <AUTHOR>
 * @date 2023-09-01
 */
public interface SlDisablePopulationMapper extends BaseMapperPlus<SlDisablePopulationMapper, SlDisablePopulation, SlDisablePopulationVo> {

    @DS("master")
    List<SpecialistVo> queryByAddress(@Param("address") String address);

}
