package com.zhonghe.system.mapper;

import com.zhonghe.common.core.mapper.BaseMapperPlus;
import com.zhonghe.system.domain.thirdparty.SlCitySubsistenceAllowanceInfo;
import com.zhonghe.system.domain.thirdparty.vo.SlCitySubsistenceAllowanceInfoVo;
import com.zhonghe.system.domain.thirdparty.vo.SpecialistVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 【请填写功能名称】Mapper接口
 *
 * <AUTHOR>
 * @date 2023-08-18
 */
public interface SlCitySubsistenceAllowanceInfoMapper extends BaseMapperPlus<SlCitySubsistenceAllowanceInfoMapper, SlCitySubsistenceAllowanceInfo, SlCitySubsistenceAllowanceInfoVo> {

    List<SpecialistVo> queryByAddress(@Param("address") String address);

    List<SpecialistVo> queryCityFamilyByAddress(@Param("address") String addres);
}
