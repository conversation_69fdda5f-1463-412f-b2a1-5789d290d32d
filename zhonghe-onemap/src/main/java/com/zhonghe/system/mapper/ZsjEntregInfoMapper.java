package com.zhonghe.system.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.zhonghe.common.core.mapper.BaseMapperPlus;
import com.zhonghe.system.domain.thirdparty.ZsjEntregInfo;
import com.zhonghe.system.domain.thirdparty.vo.ZsjEntregInfoVo;

/**
 * 【请填写功能名称】Mapper接口
 *
 * <AUTHOR>
 * @date 2023-09-11
 */
@DS("data")
public interface ZsjEntregInfoMapper extends BaseMapperPlus<ZsjEntregInfoMapper, ZsjEntregInfo, ZsjEntregInfoVo> {

    Long selectSlaverCount();
}
