package com.zhonghe.system.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhonghe.common.core.mapper.BaseMapperPlus;
import com.zhonghe.system.domain.thirdparty.Dgsscztxx;
import com.zhonghe.system.domain.thirdparty.vo.DgsscztxxVo;
import com.zhonghe.system.domain.thirdparty.vo.SlCaseEventStatisticsVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 【请填写功能名称】Mapper接口
 *
 * <AUTHOR>
 * @date 2023-08-14
 */
public interface DgsscztxxMapper extends BaseMapperPlus<DgsscztxxMapper, Dgsscztxx, DgsscztxxVo> {

    Long selectSlaverCount();

    List<SlCaseEventStatisticsVo> marketEventStatistics();

    Dgsscztxx selectByZj(@Param("zj") Long zj);

    IPage<DgsscztxxVo> queryPage(@Param("page") Page<DgsscztxxVo> build, @Param("key")String keyword,@Param("type") String queryType);
}
