package com.zhonghe.system.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.zhonghe.common.core.mapper.BaseMapperPlus;
import com.zhonghe.system.domain.SlImportantPopulation;
import com.zhonghe.system.domain.thirdparty.vo.SpecialistVo;
import com.zhonghe.system.domain.vo.SlImportantPopulationVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 重点优抚对象Mapper接口
 *
 * <AUTHOR>
 * @date 2023-09-01
 */
public interface SlImportantPopulationMapper extends BaseMapperPlus<SlImportantPopulationMapper, SlImportantPopulation, SlImportantPopulationVo> {

    @DS("master")
    List<SpecialistVo> queryByAddress(@Param("address") String address);
}
