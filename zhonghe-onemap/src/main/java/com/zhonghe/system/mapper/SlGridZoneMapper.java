package com.zhonghe.system.mapper;

import com.zhonghe.common.core.mapper.BaseMapperPlus;
import com.zhonghe.system.domain.SlGridZone;
import com.zhonghe.system.domain.vo.grid.SlGridZoneVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 网格信息Mapper接口
 *
 * <AUTHOR>
 * @date 2023-08-02
 */
public interface SlGridZoneMapper extends BaseMapperPlus<SlGridZoneMapper, SlGridZone, SlGridZoneVo> {


    List<String> grid(@Param("lng") double lng, @Param("lat") double lat);
}
