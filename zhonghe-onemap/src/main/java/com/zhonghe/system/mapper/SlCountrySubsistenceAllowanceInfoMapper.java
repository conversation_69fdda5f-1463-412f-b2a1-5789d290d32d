package com.zhonghe.system.mapper;

import com.zhonghe.common.core.mapper.BaseMapperPlus;
import com.zhonghe.system.domain.thirdparty.SlCountrySubsistenceAllowanceInfo;
import com.zhonghe.system.domain.thirdparty.vo.SlCountrySubsistenceAllowanceInfoVo;
import com.zhonghe.system.domain.thirdparty.vo.SpecialistVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 【请填写功能名称】Mapper接口
 *
 * <AUTHOR>
 * @date 2023-08-18
 */
public interface SlCountrySubsistenceAllowanceInfoMapper extends BaseMapperPlus<SlCountrySubsistenceAllowanceInfoMapper, SlCountrySubsistenceAllowanceInfo, SlCountrySubsistenceAllowanceInfoVo> {

    List<SpecialistVo> queryByAddress(@Param("address") String address);
}
