package com.zhonghe.system.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.zhonghe.common.core.mapper.BaseMapperPlus;
import com.zhonghe.system.domain.thirdparty.ZsjEntregInfoAudit;
import com.zhonghe.system.domain.thirdparty.vo.ZsjEntregInfoAuditVo;

/**
 * 【请填写功能名称】Mapper接口
 *
 * <AUTHOR>
 * @date 2023-09-13
 */
@DS("data")
public interface ZsjEntregInfoAuditMapper extends BaseMapperPlus<ZsjEntregInfoAuditMapper, ZsjEntregInfoAudit, ZsjEntregInfoAuditVo> {

}
