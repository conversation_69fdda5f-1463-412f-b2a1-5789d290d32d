package com.zhonghe.system.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.zhonghe.common.core.mapper.BaseMapperPlus;
import com.zhonghe.system.domain.thirdparty.T3070220000088000190V3Uom;
import com.zhonghe.system.domain.thirdparty.vo.SpecialistVo;
import com.zhonghe.system.domain.thirdparty.vo.T3070220000088000190V3UomVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 【请填写功能名称】Mapper接口
 *
 * <AUTHOR>
 * @date 2023-09-14
 */
@DS("data")
public interface T3070220000088000190V3UomMapper extends BaseMapperPlus<T3070220000088000190V3UomMapper, T3070220000088000190V3Uom, T3070220000088000190V3UomVo> {

    List<SpecialistVo> queryByAddress(@Param("address") String address);
}
