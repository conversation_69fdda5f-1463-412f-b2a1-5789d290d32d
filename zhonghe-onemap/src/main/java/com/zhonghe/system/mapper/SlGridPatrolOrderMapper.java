package com.zhonghe.system.mapper;

import com.zhonghe.common.core.mapper.BaseMapperPlus;
import com.zhonghe.system.domain.thirdparty.SlGridPatrolOrder;
import com.zhonghe.system.domain.thirdparty.vo.SlCaseEventStatisticsVo;
import com.zhonghe.system.domain.thirdparty.vo.SlGridPatrolOrderVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 【请填写功能名称】Mapper接口
 *
 * <AUTHOR>
 * @date 2023-08-18
 */
public interface SlGridPatrolOrderMapper extends BaseMapperPlus<SlGridPatrolOrderMapper, SlGridPatrolOrder, SlGridPatrolOrderVo> {

    List<SlCaseEventStatisticsVo> queryStatisticByAddress(@Param("address") List<String> address);
}
