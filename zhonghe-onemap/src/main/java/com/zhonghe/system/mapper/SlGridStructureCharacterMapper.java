package com.zhonghe.system.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhonghe.common.core.mapper.BaseMapperPlus;
import com.zhonghe.system.domain.SlGridStructureCharacter;
import com.zhonghe.system.domain.vo.grid.SlGridStructureCharacterVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 网格人员信息Mapper接口
 *
 * <AUTHOR>
 * @date 2023-08-02
 */
public interface SlGridStructureCharacterMapper extends BaseMapperPlus<SlGridStructureCharacterMapper, SlGridStructureCharacter, SlGridStructureCharacterVo> {

    IPage<SlGridStructureCharacterVo> queryPageCharacterDetailVo(@Param("page") Page<SlGridStructureCharacter> page, @Param(Constants.WRAPPER) Wrapper<SlGridStructureCharacter> ew);

    List<SlGridStructureCharacterVo> queryCharacterDetailVo(@Param(Constants.WRAPPER) Wrapper<SlGridStructureCharacter> ew);

    IPage<SlGridStructureCharacterVo> queryServicePowerCharacters(@Param("page") Page<SlGridStructureCharacter> page, @Param(Constants.WRAPPER) QueryWrapper<SlGridStructureCharacter> filter);


    SlGridStructureCharacterVo queryServicePowerCharactersByCharacterId(@Param("id") Long id);

    List<SlGridStructureCharacterVo> selectGridLeader(@Param("grid") String grid);
}
