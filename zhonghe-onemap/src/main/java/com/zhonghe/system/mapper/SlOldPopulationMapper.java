package com.zhonghe.system.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.zhonghe.common.core.mapper.BaseMapperPlus;
import com.zhonghe.system.domain.SlOldPopulation;
import com.zhonghe.system.domain.thirdparty.vo.SpecialistVo;
import com.zhonghe.system.domain.vo.SlOldPopulationVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 老年失能Mapper接口
 *
 * <AUTHOR>
 * @date 2023-09-01
 */
public interface SlOldPopulationMapper extends BaseMapperPlus<SlOldPopulationMapper, SlOldPopulation, SlOldPopulationVo> {

    @DS("master")
    List<SpecialistVo> queryAddress(@Param("address") String address);
}
