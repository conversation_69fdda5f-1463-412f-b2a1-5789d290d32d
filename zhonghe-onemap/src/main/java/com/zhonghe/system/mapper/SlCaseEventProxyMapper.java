package com.zhonghe.system.mapper;

import cn.hutool.core.date.DateTime;
import com.zhonghe.common.core.mapper.BaseMapperPlus;
import com.zhonghe.system.domain.thirdparty.SlCaseEventProxy;
import com.zhonghe.system.domain.thirdparty.vo.SlCaseEventMainSubjectStatisticsVo;
import com.zhonghe.system.domain.thirdparty.vo.SlCaseEventProxyGroupStatisticsVo;
import com.zhonghe.system.domain.thirdparty.vo.SlCaseEventProxyVo;
import com.zhonghe.system.domain.thirdparty.vo.SlCaseEventStatisticsVo;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 【请填写功能名称】Mapper接口
 *
 * <AUTHOR>
 * @date 2023-08-14
 */
public interface SlCaseEventProxyMapper extends BaseMapperPlus<SlCaseEventProxyMapper, SlCaseEventProxy, SlCaseEventProxyVo> {

    List<SlCaseEventProxyGroupStatisticsVo> queryDateByYear(@Param(("year")) String valueOf);

    List<SlCaseEventProxyGroupStatisticsVo> queryQuarterByYear(@Param(("year")) String valueOf);

    List<SlCaseEventMainSubjectStatisticsVo> queryGroupByType();

    List<SlCaseEventStatisticsVo> queryGroupByAddress(@Param("addresses") Set<String> addresses);

    List<SlCaseEventProxyGroupStatisticsVo> queryOneMonthOfToDay(@Param("old") DateTime offset,@Param("now")  Date now);

    long selectMarkerCaseEventCount();

    Long queryOneDay(@Param("date") String date);
}
