package com.zhonghe.system.enmu;

import cn.hutool.core.collection.CollectionUtil;
import lombok.Getter;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.Map;

/**
 * @description:
 * @author: cq
 * @date: 2023/8/8 15:36
 */

public enum OneNetConfig {

    /**
     * 人口
     * 字段名称 类型 必填 中文名称 备注
     * ZJHM varchar 是 证件号
     */
    POPULATION("C16911420030479037", new String[]{"ZJHM"}),

    /**
     * 单位
     * 字段名称 类型 必填 中文名称 备注
     * TYSHXYDM varchar 是 统一社会信用代
     */
    UNIT("C16911420062325658", new String[]{"TYSHXYDM"}),

    /**
     * 东莞市市场主体基本信息-19
     * 字段名称 类型 必填 中文名称 备注
     * SCZTMC varchar 是 市场主体名称
     * TYSHXYDM varchar 是 统一社会信用代码
     */
    MACKET("C16914804034721893", new String[]{"SCZTMC", "TYSHXYDM"}),

    /**
     * 自然灾害
     * 字段名称 类型 必填 中文名称 备注
     * YJXHLX varchar 是 预警信号类
     */
    DISASTER("C16914606067282451", new String[]{"YJXHLX"}),

    /**
     * 网格基础
     * 字段名称 类型 必填 中文名称 备注
     * XZJB varchar 是 行政级别
     */
    GRID_BASE("C16914606028570420", new String[]{"XZJB"}),


    /**
     * 东莞市市场主体基本信息
     * 字段名称 类型 必填 中文名称 备注
     * SCZTMC varchar 是 市场主体名称
     * TYSHXYDM varchar 是 统一社会信用代码
     */
    MACKET_UNIT("C16914804034721893", new String[]{"SCZTMC", "TYSHXYDM"}),


    /**
     * 东莞市二标四实标准地址信息新-9
     * 字段名称 类型 必填 中文名称 备注
     * DZMC varchar 否 地址名称
     * DZXZ varchar 否 地址详址
     * DZQC varchar 是 地址全称
     */
    ADDRESS("C16915719019920052", new String[]{"DZQC"}, new String[]{"DZMC", "DZXZ"}),


    /**
     * 东莞市二标四实实有房屋信息新-2
     * <p>
     * 字段名称 类型 必填 中文名称 备注
     * DZDM varchar 否 地址代码
     * DZMC varchar 否 地址名称
     * DZQC varchar 是 地址全称
     */
    HOUSE("C16915719049416451", new String[]{"DZQC"}, new String[]{"DZDM", "DZMC"}),

    /**
     * 东莞市国家气象站实况监测数据-2
     * 字段名称 类型 必填 中文名称 备注
     * ZM varchar 否 站名
     * JCSJ timestamp 是 监测时间
     */
    WEATHER("C16928451024164405", new String[]{"JCSJ"}, new String[]{"ZM"}),

    ;

    @Getter
    private String id;
    @Getter
    private String[] keys;

    @Getter
    private String[] optional;

    @Getter
    private DataSelector selector = (list, field) -> {
        Map<String, String> result = null;
        if (CollectionUtil.isNotEmpty(list)) {
            result = list.get(0);
            if (result.containsKey(field)) {
                try {
                    DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSXXX");

                    long updateTime = dateFormat.parse(result.get(field)).getTime();
                    for (Map<String, String> one : list) {
                        if (one.containsKey(field)) {
                            long timestamp = dateFormat.parse(one.get(field)).getTime();
                            if (timestamp > updateTime) {
                                result = one;
                                updateTime = timestamp;
                            }
                        }
                    }
                } catch (Exception e) {

                }
            }
        }
        return result;
    };

    OneNetConfig(String id, String[] keys, String[] optional) {
        this.keys = keys;
        this.id = id;
        this.optional = optional;
    }

    OneNetConfig(String id, String[] keys) {
        this.keys = keys;
        this.id = id;
    }
}
