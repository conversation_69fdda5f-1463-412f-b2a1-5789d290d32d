package com.zhonghe.system.event;

import com.zhonghe.system.domain.thirdparty.vo.SlCaseEventMainSubjectStatisticsVo;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * @description:
 * @author: cq
 * @date: 2023/8/21 10:48
 */

public class ExchangeDataBaseEventCaseEventByMarketEvent extends ApplicationEvent {

    @Getter
    private final SlCaseEventMainSubjectStatisticsVo vo;

    public ExchangeDataBaseEventCaseEventByMarketEvent(Object source, SlCaseEventMainSubjectStatisticsVo vo) {
        super(source);
        this.vo = vo;
    }
}

