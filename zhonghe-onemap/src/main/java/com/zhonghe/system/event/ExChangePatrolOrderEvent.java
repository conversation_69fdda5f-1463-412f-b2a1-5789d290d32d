package com.zhonghe.system.event;

import com.zhonghe.system.domain.vo.BaseThirdPartyVo;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * @description:
 * @author: cq
 * @date: 2023/8/15 17:40
 */

public class ExChangePatrolOrderEvent extends ApplicationEvent {

    @Getter
    private final String address;
    @Getter
    private final BaseThirdPartyVo vo;

    public ExChangePatrolOrderEvent(Object source, BaseThirdPartyVo vo, String address) {
        super(source);
        this.vo = vo;
        this.address = address;
    }
}

