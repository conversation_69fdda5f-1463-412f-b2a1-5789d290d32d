package com.zhonghe.system.event;

import com.zhonghe.system.domain.SlWeatherStationOnenet;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.util.List;

/**
 * @description:
 * @author: cq
 * @date: 2023/8/21 15:28
 */

public class ExchangeDataBaseEventQueryWeatherStation extends ApplicationEvent {

    @Getter
    private final List<String> stationNames;

    @Getter
    private final List<SlWeatherStationOnenet> geometries;

    public ExchangeDataBaseEventQueryWeatherStation(Object source,List<String> stationNames,List<SlWeatherStationOnenet> geometries) {
        super(source);
        this.stationNames = stationNames;
        this.geometries = geometries;
    }
}

