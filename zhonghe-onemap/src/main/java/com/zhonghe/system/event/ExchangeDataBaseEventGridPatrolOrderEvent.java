package com.zhonghe.system.event;

import com.zhonghe.system.domain.thirdparty.vo.SlCaseEventStatisticsVo;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.util.List;
import java.util.Set;

/**
 * @description:
 * @author: cq
 * @date: 2023/8/21 9:46
 */

public class ExchangeDataBaseEventGridPatrolOrderEvent extends ApplicationEvent {


    @Getter
    private final Set<String> address;

    @Getter
    private final List<SlCaseEventStatisticsVo> result;

    public ExchangeDataBaseEventGridPatrolOrderEvent(Object source, Set<String> address, List<SlCaseEventStatisticsVo> result) {
        super(source);
        this.address = address;
        this.result = result;
    }
}

