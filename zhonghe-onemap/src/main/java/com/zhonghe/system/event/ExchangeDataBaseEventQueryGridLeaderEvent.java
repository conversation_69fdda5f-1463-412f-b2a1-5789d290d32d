package com.zhonghe.system.event;

import com.zhonghe.system.domain.vo.grid.SlGridStructureCharacterVo;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * @description:
 * @author: cq
 * @date: 2023/8/21 12:43
 */

public class ExchangeDataBaseEventQueryGridLeaderEvent extends ApplicationEvent {

    @Getter
    private final String grid;

    @Getter
    private final SlGridStructureCharacterVo vo;

    public ExchangeDataBaseEventQueryGridLeaderEvent(Object source, String grid , SlGridStructureCharacterVo leader) {
        super(source);
        this.grid = grid;
        this.vo = leader;
    }
}

