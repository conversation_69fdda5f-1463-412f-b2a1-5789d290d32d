package com.zhonghe.system.event;

import com.zhonghe.system.domain.thirdparty.vo.SlCaseEventStatisticsVo;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.util.List;
import java.util.Set;

/**
 * @description:
 * @author: cq
 * @date: 2023/8/15 11:52
 */

public class ExchangeDataBaseEventCaseByAddressEvent extends ApplicationEvent {

    @Getter
    private final Set<String> addresses;

    @Getter
    private final List<SlCaseEventStatisticsVo> result;

    public ExchangeDataBaseEventCaseByAddressEvent(Object source, Set<String> addresses, List<SlCaseEventStatisticsVo> result) {
        super(source);
        this.addresses = addresses;
        this.result = result;
    }
}

