package com.zhonghe.system.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.zhonghe.common.annotation.Log;
import com.zhonghe.common.annotation.RepeatSubmit;
import com.zhonghe.common.core.controller.BaseController;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import com.zhonghe.common.enums.BusinessType;
import com.zhonghe.common.utils.poi.ExcelUtil;
import com.zhonghe.system.domain.bo.grid.SlGridStructureBo;
import com.zhonghe.system.domain.vo.grid.SlGridStructureVo;
import com.zhonghe.system.service.ISlGridStructureService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 网格职位架构Controller
 *
 * <AUTHOR>
 * @date 2023-08-02
 */
@Validated
@Api(value = "网格职位架构控制器", tags = {"网格职位架构管理"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/gridStructure")
public class SlGridStructureController extends BaseController {

    private final ISlGridStructureService iSlGridStructureService;

    /**
     * 查询网格职位架构列表
     */
    @ApiOperation("查询网格职位架构列表")
    @SaCheckPermission("system:gridStructure:list")
    @GetMapping("/list")
    public R<?> list() {
        return R.ok();
    }
    /**
     * 导出网格职位架构列表
     */
    @ApiOperation("导出网格职位架构列表")
    @SaCheckPermission("system:gridStructure:export")
    @Log(title = "网格职位架构", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SlGridStructureBo bo, HttpServletResponse response) {
        List<SlGridStructureVo> list = iSlGridStructureService.queryList(bo);
        ExcelUtil.exportExcel(list, "网格职位架构", SlGridStructureVo.class, response);
    }

    /**
     * 获取网格职位架构详细信息
     */
    @ApiOperation("获取网格职位架构详细信息")
    @SaCheckPermission("system:gridStructure:query")
    @GetMapping("/{gridStructureId}")
    public R<SlGridStructureVo> getInfo(@ApiParam("主键")
                                     @NotNull(message = "主键不能为空")
                                     @PathVariable("gridStructureId") Long gridStructureId) {
        return R.ok(iSlGridStructureService.queryById(gridStructureId));
    }

    /**
     * 新增网格职位架构
     */
    @ApiOperation("新增网格职位架构")
    @SaCheckPermission("system:gridStructure:add")
    @Log(title = "网格职位架构", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SlGridStructureBo bo) {
        return toAjax(iSlGridStructureService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改网格职位架构
     */
    @ApiOperation("修改网格职位架构")
    @SaCheckPermission("system:gridStructure:edit")
    @Log(title = "网格职位架构", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SlGridStructureBo bo) {
        return toAjax(iSlGridStructureService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除网格职位架构
     */
    @ApiOperation("删除网格职位架构")
    @SaCheckPermission("system:gridStructure:remove")
    @Log(title = "网格职位架构", businessType = BusinessType.DELETE)
    @DeleteMapping("/{gridStructureIds}")
    public R<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable Long[] gridStructureIds) {
        return toAjax(iSlGridStructureService.deleteWithValidByIds(Arrays.asList(gridStructureIds), true) ? 1 : 0);
    }
}
