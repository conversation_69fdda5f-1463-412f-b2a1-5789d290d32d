package com.zhonghe.system.controller;

import com.zhonghe.common.core.controller.BaseController;
import com.zhonghe.system.service.ISlVolunteerService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 志愿者Controller
 *
 * <AUTHOR>
 * @date 2023-08-03
 */
@Validated
@Api(value = "志愿者控制器", tags = {"志愿者管理"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/com/zhonghe/system/volunteer")
public class SlVolunteerController extends BaseController {

    private final ISlVolunteerService iSlVolunteerService;

    /**
     * 查询志愿者列表
     */
    /*@ApiOperation("查询志愿者列表")
    @SaCheckPermission("system:volunteer:list")
    @GetMapping("/list")
    public TableDataInfo<?> list(SlVolunteerBo bo, PageQuery pageQuery) {
        return null;
    }

    *//**
     * 导出志愿者列表
     *//*
    @ApiOperation("导出志愿者列表")
    @SaCheckPermission("system:volunteer:export")
    @Log(title = "志愿者", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SlVolunteerBo bo, HttpServletResponse response) {
        List<SlVolunteerVo> list = iSlVolunteerService.queryList(bo);
        ExcelUtil.exportExcel(list, "志愿者", SlVolunteerVo.class, response);
    }

    *//**
     * 获取志愿者详细信息
     *//*
    @ApiOperation("获取志愿者详细信息")
    @SaCheckPermission("system:volunteer:query")
    @GetMapping("/{volunteerId}")
    public R<SlVolunteerVo> getInfo(@ApiParam("主键")
                                    @NotNull(message = "主键不能为空")
                                    @PathVariable("volunteerId") Long volunteerId) {
        return R.ok(iSlVolunteerService.queryById(volunteerId));
    }

    *//**
     * 新增志愿者
     *//*
    @ApiOperation("新增志愿者")
    @SaCheckPermission("system:volunteer:add")
    @Log(title = "志愿者", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SlVolunteerBo bo) {
        return toAjax(iSlVolunteerService.insertByBo(bo) ? 1 : 0);
    }

    *//**
     * 修改志愿者
     *//*
    @ApiOperation("修改志愿者")
    @SaCheckPermission("system:volunteer:edit")
    @Log(title = "志愿者", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SlVolunteerBo bo) {
        return toAjax(iSlVolunteerService.updateByBo(bo) ? 1 : 0);
    }

    *//**
     * 删除志愿者
     *//*
    @ApiOperation("删除志愿者")
    @SaCheckPermission("system:volunteer:remove")
    @Log(title = "志愿者", businessType = BusinessType.DELETE)
    @DeleteMapping("/{volunteerIds}")
    public R<Void> remove(@ApiParam("主键串")
                          @NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] volunteerIds) {
        return toAjax(iSlVolunteerService.deleteWithValidByIds(Arrays.asList(volunteerIds), true) ? 1 : 0);
    }*/
}
