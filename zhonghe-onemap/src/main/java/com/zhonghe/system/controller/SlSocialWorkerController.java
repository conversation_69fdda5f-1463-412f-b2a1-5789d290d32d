package com.zhonghe.system.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.zhonghe.common.core.controller.BaseController;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.system.service.ISlSocialWorkerService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 石龙社工Controller
 *
 * <AUTHOR>
 * @date 2023-08-03
 */
@Validated
@Api(value = "石龙社工控制器", tags = {"石龙社工管理"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/socialWorker")
public class SlSocialWorkerController extends BaseController {

    private final ISlSocialWorkerService iSlSocialWorkerService;

    @ApiOperation("刷新石龙社工二标数据")
    @SaCheckPermission("system:socialWorker:refresh")
    @GetMapping("/refresh")
    public  R<?> refresh() {
        iSlSocialWorkerService.requestOnenetData();
        return R.ok();
    }


    /**
     * 查询石龙社工列表
     */
   /* @ApiOperation("查询石龙社工列表")
    @SaCheckPermission("system:socialWorker:list")
    @GetMapping("/list")
    public TableDataInfo list() {
        return null;
    }*/
    /**
     * 导出石龙社工列表
     */
    /*@ApiOperation("导出石龙社工列表")
    @SaCheckPermission("system:socialWorker:export")
    @Log(title = "石龙社工", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SlSocialWorkerBo bo, HttpServletResponse response) {
        List<SlSocialWorkerVo> list = iSlSocialWorkerService.queryList(bo);
        ExcelUtil.exportExcel(list, "石龙社工", SlSocialWorkerVo.class, response);
    }*/

    /**
     * 获取石龙社工详细信息
     */
    /*@ApiOperation("获取石龙社工详细信息")
    @SaCheckPermission("system:socialWorker:query")
    @GetMapping("/{socialWorkerId}")
    public R<SlSocialWorkerVo> getInfo(@ApiParam("主键")
                                     @NotNull(message = "主键不能为空")
                                     @PathVariable("socialWorkerId") Long socialWorkerId) {
        return R.ok(iSlSocialWorkerService.queryById(socialWorkerId));
    }

    *//**
     * 新增石龙社工
     *//*
    @ApiOperation("新增石龙社工")
    @SaCheckPermission("system:socialWorker:add")
    @Log(title = "石龙社工", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SlSocialWorkerBo bo) {
        return toAjax(iSlSocialWorkerService.insertByBo(bo) ? 1 : 0);
    }

    *//**
     * 修改石龙社工
     *//*
    @ApiOperation("修改石龙社工")
    @SaCheckPermission("system:socialWorker:edit")
    @Log(title = "石龙社工", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SlSocialWorkerBo bo) {
        return toAjax(iSlSocialWorkerService.updateByBo(bo) ? 1 : 0);
    }

    *//**
     * 删除石龙社工
     *//*
    @ApiOperation("删除石龙社工")
    @SaCheckPermission("system:socialWorker:remove")
    @Log(title = "石龙社工", businessType = BusinessType.DELETE)
    @DeleteMapping("/{socialWorkerIds}")
    public R<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable Long[] socialWorkerIds) {
        return toAjax(iSlSocialWorkerService.deleteWithValidByIds(Arrays.asList(socialWorkerIds), true) ? 1 : 0);
    }*/
}
