package com.zhonghe.system.controller;

import com.zhonghe.common.core.controller.BaseController;
import com.zhonghe.system.service.ISlWeatherStationDataService;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 【请填写功能名称】Controller
 *
 * <AUTHOR>
 * @date 2023-08-21
 */
@Validated
@Api(value = "【请填写功能名称】控制器", tags = {"【请填写功能名称】管理"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/weatherStationData")
public class SlWeatherStationDataController extends BaseController {

    private final ISlWeatherStationDataService iSlWeatherStationDataService;

    /**
     * 查询【请填写功能名称】列表
     */
/*    @ApiOperation("查询【请填写功能名称】列表")
    @SaCheckPermission("system:weatherStationData:list")
    @GetMapping("/list")*/

    /**
     * 导出【请填写功能名称】列表
     */
    /*@ApiOperation("导出【请填写功能名称】列表")
    @SaCheckPermission("system:weatherStationData:export")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SlWeatherStationDataBo bo, HttpServletResponse response) {
        List<SlWeatherStationDataVo> list = iSlWeatherStationDataService.queryList(bo);
        ExcelUtil.exportExcel(list, "【请填写功能名称】", SlWeatherStationDataVo.class, response);
    }

    *//**
     * 获取【请填写功能名称】详细信息
     *//*
    @ApiOperation("获取【请填写功能名称】详细信息")
    @SaCheckPermission("system:weatherStationData:query")
    @GetMapping("/{fywzjid}")
    public R<SlWeatherStationDataVo> getInfo(@ApiParam("主键")
                                     @NotNull(message = "主键不能为空")
                                     @PathVariable("fywzjid") String fywzjid) {
        return R.ok(iSlWeatherStationDataService.queryById(fywzjid));
    }

    *//**
     * 新增【请填写功能名称】
     *//*
    @ApiOperation("新增【请填写功能名称】")
    @SaCheckPermission("system:weatherStationData:add")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SlWeatherStationDataBo bo) {
        return toAjax(iSlWeatherStationDataService.insertByBo(bo) ? 1 : 0);
    }

    *//**
     * 修改【请填写功能名称】
     *//*
    @ApiOperation("修改【请填写功能名称】")
    @SaCheckPermission("system:weatherStationData:edit")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SlWeatherStationDataBo bo) {
        return toAjax(iSlWeatherStationDataService.updateByBo(bo) ? 1 : 0);
    }

    *//**
     * 删除【请填写功能名称】
     *//*
    @ApiOperation("删除【请填写功能名称】")
    @SaCheckPermission("system:weatherStationData:remove")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.DELETE)
    @DeleteMapping("/{fywzjids}")
    public R<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable String[] fywzjids) {
        return toAjax(iSlWeatherStationDataService.deleteWithValidByIds(Arrays.asList(fywzjids), true) ? 1 : 0);
    }*/
}
