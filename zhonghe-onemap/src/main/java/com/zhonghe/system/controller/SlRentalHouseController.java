package com.zhonghe.system.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.zhonghe.common.core.controller.BaseController;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.system.domain.bo.SlRentalHouseBo;
import com.zhonghe.system.domain.vo.RentalHouseDetailVo;
import com.zhonghe.system.domain.vo.SlRentalHouseVo;
import com.zhonghe.system.service.ISlRentalHouseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 出租屋信息Controller
 *
 * <AUTHOR>
 * @date 2023-08-11
 */
@Validated
@Api(value = "出租屋信息控制器", tags = {"出租屋信息管理"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/com/zhonghe/system/rentalHouse")
public class SlRentalHouseController extends BaseController {

    private final ISlRentalHouseService iSlRentalHouseService;

    /**
     * 查询出租屋信息列表
     */
    @ApiOperation("查询出租屋信息列表")
    @SaCheckPermission("system:rentalHouse:list")
    @GetMapping("/list")
    public TableDataInfo<SlRentalHouseVo> list(PageQuery pageQuery, SlRentalHouseBo bo) {
        return iSlRentalHouseService.pageSlRentalHouse(pageQuery, bo);
    }

    @ApiOperation("查询出租屋信息详细列表")
    @SaCheckPermission("system:rentalHouse:list")
    @GetMapping("/detail/{rentalHouseId}")
    public R<RentalHouseDetailVo> rentalHouseDetail(@PathVariable Long rentalHouseId) {
        return R.ok(iSlRentalHouseService.detailRentalHouse(rentalHouseId));
    }

    @ApiOperation("刷新出租屋图层数据")
    @SaCheckPermission("system:rentalHouse:refresh")
    @GetMapping("/refresh")
    public R<?> refresh() {
        return R.ok(iSlRentalHouseService.refresh());
    }

    /**
     * 导出出租屋信息列表
     */
   /* @ApiOperation("导出出租屋信息列表")
    @SaCheckPermission("system:rentalHouse:export")
    @Log(title = "出租屋信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SlRentalHouseBo bo, HttpServletResponse response) {
        List<SlRentalHouseVo> list = iSlRentalHouseService.queryList(bo);
        ExcelUtil.exportExcel(list, "出租屋信息", SlRentalHouseVo.class, response);
    }

    *//**
     * 获取出租屋信息详细信息
     *//*
    @ApiOperation("获取出租屋信息详细信息")
    @SaCheckPermission("system:rentalHouse:query")
    @GetMapping("/{rentalHouseId}")
    public R<SlRentalHouseVo> getInfo(@ApiParam("主键")
                                     @NotNull(message = "主键不能为空")
                                     @PathVariable("rentalHouseId") Long rentalHouseId) {
        return R.ok(iSlRentalHouseService.queryById(rentalHouseId));
    }

    *//**
     * 新增出租屋信息
     *//*
    @ApiOperation("新增出租屋信息")
    @SaCheckPermission("system:rentalHouse:add")
    @Log(title = "出租屋信息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SlRentalHouseBo bo) {
        return toAjax(iSlRentalHouseService.insertByBo(bo) ? 1 : 0);
    }

    *//**
     * 修改出租屋信息
     *//*
    @ApiOperation("修改出租屋信息")
    @SaCheckPermission("system:rentalHouse:edit")
    @Log(title = "出租屋信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SlRentalHouseBo bo) {
        return toAjax(iSlRentalHouseService.updateByBo(bo) ? 1 : 0);
    }

    *//**
     * 删除出租屋信息
     *//*
    @ApiOperation("删除出租屋信息")
    @SaCheckPermission("system:rentalHouse:remove")
    @Log(title = "出租屋信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{rentalHouseIds}")
    public R<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable Long[] rentalHouseIds) {
        return toAjax(iSlRentalHouseService.deleteWithValidByIds(Arrays.asList(rentalHouseIds), true) ? 1 : 0);
    }*/
}
