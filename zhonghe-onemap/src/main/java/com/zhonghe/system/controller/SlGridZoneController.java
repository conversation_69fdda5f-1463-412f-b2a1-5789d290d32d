package com.zhonghe.system.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.zhonghe.common.annotation.Log;
import com.zhonghe.common.annotation.RepeatSubmit;
import com.zhonghe.common.core.controller.BaseController;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import com.zhonghe.common.enums.BusinessType;
import com.zhonghe.common.utils.poi.ExcelUtil;
import com.zhonghe.system.domain.bo.grid.SlGridZoneBo;
import com.zhonghe.system.domain.vo.grid.SlGridZoneVo;
import com.zhonghe.system.service.ISlGridZoneService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 网格信息Controller
 *
 * <AUTHOR>
 * @date 2023-08-02
 */
@Validated
@Api(value = "网格信息控制器", tags = {"网格信息管理"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/gridZone")
public class SlGridZoneController extends BaseController {

    private final ISlGridZoneService iSlGridZoneService;

    /**
     * 查询网格信息列表
     */
    @ApiOperation("查询网格信息列表")
    @SaCheckPermission("system:gridZone:list")
    @GetMapping("/list")
    public R<?> list() {
        return R.ok();
    }
    /**
     * 导出网格信息列表
     */
    @ApiOperation("导出网格信息列表")
    @SaCheckPermission("system:gridZone:export")
    @Log(title = "网格信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SlGridZoneBo bo, HttpServletResponse response) {
        List<SlGridZoneVo> list = iSlGridZoneService.queryList(bo);
        ExcelUtil.exportExcel(list, "网格信息", SlGridZoneVo.class, response);
    }

    /**
     * 获取网格信息详细信息
     */
    @ApiOperation("获取网格信息详细信息")
    @SaCheckPermission("system:gridZone:query")
    @GetMapping("/{gridZoneId}")
    public R<SlGridZoneVo> getInfo(@ApiParam("主键")
                                     @NotNull(message = "主键不能为空")
                                     @PathVariable("gridZoneId") Long gridZoneId) {
        return R.ok(iSlGridZoneService.queryById(gridZoneId));
    }

    /**
     * 新增网格信息
     */
    @ApiOperation("新增网格信息")
    @SaCheckPermission("system:gridZone:add")
    @Log(title = "网格信息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SlGridZoneBo bo) {
        return toAjax(iSlGridZoneService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改网格信息
     */
    @ApiOperation("修改网格信息")
    @SaCheckPermission("system:gridZone:edit")
    @Log(title = "网格信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SlGridZoneBo bo) {
        return toAjax(iSlGridZoneService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除网格信息
     */
    @ApiOperation("删除网格信息")
    @SaCheckPermission("system:gridZone:remove")
    @Log(title = "网格信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{gridZoneIds}")
    public R<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable Long[] gridZoneIds) {
        return toAjax(iSlGridZoneService.deleteWithValidByIds(Arrays.asList(gridZoneIds), true) ? 1 : 0);
    }
}
