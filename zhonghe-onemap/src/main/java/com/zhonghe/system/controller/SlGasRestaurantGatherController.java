package com.zhonghe.system.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.zhonghe.common.core.controller.BaseController;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.system.auto.AutoRequest;
import com.zhonghe.system.domain.bo.gas.SlGasRestaurantGatherBo;
import com.zhonghe.system.domain.vo.gas.SlGasRestaurantGatherGroupListVo;
import com.zhonghe.system.domain.vo.gas.SlGasRestaurantGatherStatisticVo;
import com.zhonghe.system.domain.vo.gas.SlGasRestaurantGatherVo;
import com.zhonghe.system.service.ISlGasRestaurantGatherService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import java.text.ParseException;
import java.util.List;

/**
 * 燃气统计Controller
 *
 * <AUTHOR>
 * @date 2023-07-28
 */
@Validated
@Api(value = "燃气统计控制器", tags = {"燃气统计管理"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/com/zhonghe/one-map/gas")
public class SlGasRestaurantGatherController extends BaseController {

    private final ISlGasRestaurantGatherService iSlGasRestaurantGatherService;


    @ApiOperation("燃气上图刷新接口")
    @SaCheckPermission("system:gas:map:refresh")
    @GetMapping("/onenet/refresh")
    public R<?> refresh() {
        iSlGasRestaurantGatherService.refreshOnenet();
        return R.ok();
    }

    @ApiOperation("燃气上图数据统计接口")
    @SaCheckPermission("system:gas:map:statistic")
    @AutoRequest
    @GetMapping("statistic")
    public R<SlGasRestaurantGatherStatisticVo> statistic() {
        return R.ok(iSlGasRestaurantGatherService.statistic());
    }


    @ApiOperation("燃气上图数据按组分类统计接口")
    @SaCheckPermission("system:gas:map:group:list")
    @GetMapping("group/list")
    public R<List<SlGasRestaurantGatherGroupListVo>> groupList(SlGasRestaurantGatherBo bo) throws ParseException {
        return R.ok(iSlGasRestaurantGatherService.groupList(bo));
    }
    /**
     * 查询燃气统计列表
     */
    @ApiOperation("查询燃气列表")
    @SaCheckPermission("system:gasRestaurantGather:list")
    @GetMapping("/list")
    public TableDataInfo<SlGasRestaurantGatherVo> list(SlGasRestaurantGatherBo bo , PageQuery pageQuery) throws ParseException {
        return iSlGasRestaurantGatherService.queryPageList(bo, pageQuery);
    }
    /**
     * 导出燃气统计列表
     */
   /* @ApiOperation("导出燃气统计列表")
    @SaCheckPermission("system:gasRestaurantGather:export")
    @Log(title = "燃气统计", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SlGasRestaurantGatherBo bo, HttpServletResponse response) throws ParseException {
        List<SlGasRestaurantGatherVo> list = iSlGasRestaurantGatherService.queryList(bo);
        ExcelUtil.exportExcel(list, "燃气统计", SlGasRestaurantGatherVo.class, response);
    }*/

    /**
     * 获取燃气统计详细信息
     */
    @ApiOperation("获取燃气统计详细信息")
    @SaCheckPermission("system:gasRestaurantGather:query")
    @GetMapping("/{gatherGasId}")
    public R<SlGasRestaurantGatherVo> getInfo(@ApiParam("主键")
                                     @NotNull(message = "主键不能为空")
                                     @PathVariable("gatherGasId") Long gatherGasId) {
        return R.ok(iSlGasRestaurantGatherService.queryById(gatherGasId));
    }

    /**
     * 新增燃气统计
     */
   /* @ApiOperation("新增燃气统计")
    @SaCheckPermission("system:gasRestaurantGather:add")
    @Log(title = "燃气统计", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SlGasRestaurantGatherBo bo) {
        return toAjax(iSlGasRestaurantGatherService.insertByBo(bo) ? 1 : 0);
    }

    *//**
     * 修改燃气统计
     *//*
    @ApiOperation("修改燃气统计")
    @SaCheckPermission("system:gasRestaurantGather:edit")
    @Log(title = "燃气统计", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SlGasRestaurantGatherBo bo) {
        return toAjax(iSlGasRestaurantGatherService.updateByBo(bo) ? 1 : 0);
    }

    *//**
     * 删除燃气统计
     *//*
    @ApiOperation("删除燃气统计")
    @SaCheckPermission("system:gasRestaurantGather:remove")
    @Log(title = "燃气统计", businessType = BusinessType.DELETE)
    @DeleteMapping("/{gatherGasIds}")
    public R<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable Long[] gatherGasIds) {
        return toAjax(iSlGasRestaurantGatherService.deleteWithValidByIds(Arrays.asList(gatherGasIds), true) ? 1 : 0);
    }*/
}
