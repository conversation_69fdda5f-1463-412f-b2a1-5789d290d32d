package com.zhonghe.system.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.zhonghe.common.core.controller.BaseController;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.system.auto.AutoRequest;
import com.zhonghe.system.domain.bo.grid.SlGridStructureCharacterBo;
import com.zhonghe.system.domain.thirdparty.bo.SlCaseEventProxyBo;
import com.zhonghe.system.domain.thirdparty.bo.SlWeatherBo;
import com.zhonghe.system.domain.thirdparty.vo.SlCaseEventMainSubjectStatisticsVo;
import com.zhonghe.system.domain.thirdparty.vo.SlCaseEventProxyOneMapVo;
import com.zhonghe.system.domain.thirdparty.vo.SlCaseEventProxyStatisticsVo;
import com.zhonghe.system.domain.thirdparty.vo.SlCaseEventStatisticsVo;
import com.zhonghe.system.domain.thirdparty.vo.SlStationWeatherVo;
import com.zhonghe.system.domain.thirdparty.vo.SlWeatherStatisticVo;
import com.zhonghe.system.domain.vo.SlMarketSubjectStatisticsVo;
import com.zhonghe.system.domain.vo.SlOneMapServicePowerStatisticsVo;
import com.zhonghe.system.domain.vo.SlRentalHouseStatisticsVo;
import com.zhonghe.system.domain.vo.gas.SlGasOneMapStatisticsVo;
import com.zhonghe.system.domain.vo.grid.SlGridStructureCharacterVo;
import com.zhonghe.system.service.IMapService;
import com.zhonghe.system.service.ISlCaseEventProxyService;
import com.zhonghe.system.service.ISlGasRestaurantGatherService;
import com.zhonghe.system.service.ISlGridStructureCharacterService;
import com.zhonghe.system.service.ISlMarketSubjectService;
import com.zhonghe.system.service.ISlRentalHouseService;
import com.zhonghe.system.service.ISlSocialWorkerService;
import com.zhonghe.system.service.ISlVolunteerService;
import com.zhonghe.system.service.IWeatherService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.text.ParseException;
import java.util.List;

/**
 * @description:
 * @author: cq
 * @date: 2023/8/3 17:13
 */
@Validated
@Api(value = "大屏接口", tags = {"大屏接口"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/com/zhonghe/one-map")
public class SlOneMapController extends BaseController {

    private final ISlGridStructureCharacterService iSlGridStructureCharacterService;

    private final ISlGasRestaurantGatherService gasRestaurantGatherService;

    private final ISlRentalHouseService slRentalHouseService;
    private final ISlVolunteerService volunteerService;

    private final ISlSocialWorkerService socialWorkerService;

    private final ISlMarketSubjectService marketSubjectService;

    private final ISlCaseEventProxyService caseEventProxyService;

    private final IMapService mapService;

    private final IWeatherService weatherService;

    /**
     * 查询网格人员信息列表
     */
    @ApiOperation("查询服务力量人员列表")
    @SaCheckPermission("system:service:power:list")
    @GetMapping("/service/power/list")
    public TableDataInfo<SlGridStructureCharacterVo> list(SlGridStructureCharacterBo bo, PageQuery pageQuery) {
        return iSlGridStructureCharacterService.queryAllServicePowerCharactersInfo(bo, pageQuery);
    }

    @ApiOperation("查询服务力量人员详情")
    @SaCheckPermission("system:service:power:detail")
    @GetMapping("/service/power/detail/{gridCharacterId}")
    public R<SlGridStructureCharacterVo> detail(@PathVariable("gridCharacterId") Long gridCharacterId) {
        return R.ok(iSlGridStructureCharacterService.queryServicePowerByCharacterId(gridCharacterId));
    }


    @ApiOperation("查询服务力量人员统计")
    @SaCheckPermission("system:service:power:statistics")
    @GetMapping("/service/power/statistics")
    @AutoRequest
    public R<SlOneMapServicePowerStatisticsVo> servicePowerStatistics() {
        return R.ok(iSlGridStructureCharacterService.servicePowerStatistics());
    }

    @ApiOperation("燃气主题统计、气瓶统计")
    @SaCheckPermission("system:gas:statistics")
    @GetMapping("/gas/statistics")
    @AutoRequest
    public R<SlGasOneMapStatisticsVo> gasStatistics() {
        return R.ok(gasRestaurantGatherService.onemapUnitStatistics());
    }

    @ApiOperation("出租屋首页大图统计")
    @SaCheckPermission("system:rental:statistics")
    @GetMapping("/rental/statistics")
    @AutoRequest
    public R<SlRentalHouseStatisticsVo> rentalHouseStatistics() {
        return R.ok(slRentalHouseService.rentalHouseStatistics());
    }

    @ApiOperation("出租屋首页大图巡查事件统计")
    @SaCheckPermission("system:rental:statistics")
    @GetMapping("/rental/event/statistics")
    @AutoRequest
    public R<List<SlCaseEventStatisticsVo>> rentalEventStatistics() {
        return R.ok(slRentalHouseService.rentalHouseEventStatistics());
    }

    @ApiOperation("市场主体首页大图统计")
    @SaCheckPermission("system:market:statistics")
    @GetMapping("/market/statistics")
    @AutoRequest
    public R<SlMarketSubjectStatisticsVo> marketStatistics() {
        return R.ok(marketSubjectService.marketStatistics());
    }

    @ApiOperation("市场主体首页大图案事件事项统计")
    @SaCheckPermission("system:market:statistics")
    @GetMapping("/market/event/statistics")
    @AutoRequest
    public R<List<SlCaseEventStatisticsVo>> markEventStatistic() {
        return R.ok(marketSubjectService.marketEventStatistic());
    }

    @ApiOperation("案事件首页大图统计")
    @SaCheckPermission("system:case:statistics")
    @GetMapping("/case/statistics")
    @AutoRequest
    public R<SlCaseEventProxyOneMapVo> caseStatistics() {
        return R.ok(caseEventProxyService.caseStatistics());
    }

    @ApiOperation("案事件首页大图按类型统计")
    @SaCheckPermission("system:case:statistics")
    @GetMapping("/case/group")
    public R<SlCaseEventProxyStatisticsVo> caseStatistics(SlCaseEventProxyBo bo) throws ParseException {
        return R.ok(caseEventProxyService.caseGroupStatistics(bo));
    }

    @ApiOperation("案事件首页大图按主体类型统计")
    @SaCheckPermission("system:rentalHouse:list")
    @GetMapping("/casetype/group")
    @AutoRequest
    public R<List<SlCaseEventMainSubjectStatisticsVo>> caseTypeStatistics() {
        return R.ok(caseEventProxyService.caseTypeStatistics());
    }

    @ApiOperation("查询获取地图访问token")
    @SaCheckPermission("system:map:token")
    @GetMapping("token")
    public R<String> token() {
        return R.ok(mapService.token());
    }


    @ApiOperation("天气时间范围查询")
    @SaCheckPermission("system:weather:range")
    @GetMapping("weather/range")
    @AutoRequest(clazz = IWeatherService.class)
    public R<List<SlWeatherStatisticVo>> weatherRange(SlWeatherBo bo) throws ParseException {
        return R.ok(weatherService.query(bo));
    }

    @ApiOperation("天气时间范围查询具体信息")
    @SaCheckPermission("system:weather:range")
    @GetMapping("weather/station/range")
    public R<List<SlStationWeatherVo>> weatherStationRange(SlWeatherBo bo) throws ParseException {
        return R.ok(weatherService.queryStationWeather(bo));
    }


    @ApiOperation("天气时间范围查询具体时间点得天气信息")
    @SaCheckPermission("system:weather:range")
    @GetMapping("weather/station")
    @AutoRequest(clazz = IWeatherService.class)
    public R<SlStationWeatherVo> weatherStation(SlWeatherBo bo) throws ParseException {
        return R.ok(weatherService.queryWeather(bo));
    }
}

