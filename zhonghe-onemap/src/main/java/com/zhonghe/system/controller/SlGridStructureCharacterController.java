package com.zhonghe.system.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.lang.tree.Tree;
import com.zhonghe.common.core.controller.BaseController;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.system.auto.AutoRequest;
import com.zhonghe.system.domain.bo.grid.SlGridStructureCharacterBo;
import com.zhonghe.system.domain.vo.grid.SlGridStructureCharacterVo;
import com.zhonghe.system.service.ISlGridStructureCharacterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 网格人员信息Controller
 *
 * <AUTHOR>
 * @date 2023-08-02
 */
@Validated
@Api(value = "网格人员信息控制器", tags = {"网格人员信息管理"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/com/zhonghe/system/gridStructureCharacter")
public class SlGridStructureCharacterController extends BaseController {

    private final ISlGridStructureCharacterService iSlGridStructureCharacterService;

    @ApiOperation("查询树状网格职权信息")
    @SaCheckPermission("system:gridStructureCharacter:list")
    @GetMapping("grid")
    @AutoRequest
    public R<List<Tree<Long>>> grid() {
        return R.ok(iSlGridStructureCharacterService.grid());
    }

    /**
     * 查询网格人员信息列表
     */
    @ApiOperation("查询网格人员信息列表")
    @SaCheckPermission("system:gridStructureCharacter:list")
    @GetMapping("/list")
    public TableDataInfo<SlGridStructureCharacterVo> list(SlGridStructureCharacterBo bo, PageQuery pageQuery){
        return iSlGridStructureCharacterService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出网格人员信息列表
     */
/*    @ApiOperation("导出网格人员信息列表")
    @SaCheckPermission("system:gridStructureCharacter:export")
    @Log(title = "网格人员信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SlGridStructureCharacterBo bo, HttpServletResponse response) {
        List<SlGridStructureCharacterVo> list = iSlGridStructureCharacterService.queryList(bo);
        ExcelUtil.exportExcel(list, "网格人员信息", SlGridStructureCharacterVo.class, response);
    }*/

    /**
     * 获取网格人员信息详细信息
     */
    @ApiOperation("获取网格人员信息详细信息")
    @SaCheckPermission("system:gridStructureCharacter:query")
    @GetMapping("/{gridCharacterId}")
    public R<SlGridStructureCharacterVo> getInfo(@ApiParam("主键")
                                                 @NotNull(message = "主键不能为空")
                                                 @PathVariable("gridCharacterId") Long gridCharacterId) {
        return R.ok(iSlGridStructureCharacterService.queryById(gridCharacterId));
    }

    /**
     * 新增网格人员信息
     */
    /*@ApiOperation("新增网格人员信息")
    @SaCheckPermission("system:gridStructureCharacter:add")
    @Log(title = "网格人员信息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SlGridStructureCharacterBo bo) {
        return toAjax(iSlGridStructureCharacterService.insertByBo(bo) ? 1 : 0);
    }

    *//**
     * 修改网格人员信息
     *//*
    @ApiOperation("修改网格人员信息")
    @SaCheckPermission("system:gridStructureCharacter:edit")
    @Log(title = "网格人员信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SlGridStructureCharacterBo bo) {
        return toAjax(iSlGridStructureCharacterService.updateByBo(bo) ? 1 : 0);
    }

    *//**
     * 删除网格人员信息
     *//*
    @ApiOperation("删除网格人员信息")
    @SaCheckPermission("system:gridStructureCharacter:remove")
    @Log(title = "网格人员信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{gridCharacterIds}")
    public R<Void> remove(@ApiParam("主键串")
                          @NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] gridCharacterIds) {
        return toAjax(iSlGridStructureCharacterService.deleteWithValidByIds(Arrays.asList(gridCharacterIds), true) ? 1 : 0);
    }*/
}
