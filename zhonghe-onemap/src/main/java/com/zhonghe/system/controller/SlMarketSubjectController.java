package com.zhonghe.system.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.zhonghe.common.core.controller.BaseController;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.system.domain.thirdparty.bo.DgsscztxxBo;
import com.zhonghe.system.domain.thirdparty.vo.ZsjEntregInfoVo;
import com.zhonghe.system.domain.vo.MarketSubjectDetailVo;
import com.zhonghe.system.service.ISlMarketSubjectService;
import com.zhonghe.system.service.IZsjEntregInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @description:
 * @author: cq
 * @date: 2023/8/15 15:25
 */


@Api(value = "市场主体控制器", tags = {"市场主体管理"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/com/zhonghe/system/market")
public class SlMarketSubjectController extends BaseController {


    private final ISlMarketSubjectService marketSubjectService;

    private final IZsjEntregInfoService zsjEntregInfoService;


    @ApiOperation("市场主体前置机地图数据刷新")
    @SaCheckPermission("system:market:refresh")
    @GetMapping("/refresh")
    public R<Void> refresh() {
        zsjEntregInfoService.refresh();
        return R.ok();
    }

    @ApiOperation("市场主体列表")
    @SaCheckPermission("system:market:list")
    @GetMapping("/list")
    public TableDataInfo<ZsjEntregInfoVo> list(PageQuery pageQuery, DgsscztxxBo bo) {
        return marketSubjectService.page(pageQuery ,bo);
    }

    @ApiOperation("市场主体详情")
    @SaCheckPermission("system:market:detail")
    @GetMapping("/detail/{zj}")
    public R<MarketSubjectDetailVo> detail(@PathVariable String zj) {
        return R.ok(marketSubjectService.detail(zj));
    }
}

