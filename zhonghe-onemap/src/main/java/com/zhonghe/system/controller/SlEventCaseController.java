package com.zhonghe.system.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.zhonghe.common.core.controller.BaseController;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.system.domain.thirdparty.bo.SlCaseEventProxyBo;
import com.zhonghe.system.domain.thirdparty.vo.SlCaseEventProxyDetailVo;
import com.zhonghe.system.domain.thirdparty.vo.SlCaseEventProxyVo;
import com.zhonghe.system.service.ISlCaseEventProxyService;
import com.zhonghe.system.service.ISlCaseEventService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @description:
 * @author: cq
 * @date: 2023/8/15 15:25
 */


@Api(value = "案事件控制器", tags = {"案事件管理"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/com/zhonghe/system/case/event")
public class SlEventCaseController extends BaseController {


    private final ISlCaseEventProxyService caseEventProxyService;

    private final ISlCaseEventService caseEventService;

    @ApiOperation("案事件信息列表")
    @SaCheckPermission("system:case:event:list")
    @GetMapping("/list")
    public TableDataInfo<SlCaseEventProxyVo> list(PageQuery pageQuery, SlCaseEventProxyBo bo) {
        return caseEventProxyService.page(pageQuery,bo);
    }

    @ApiOperation("案事件详情")
    @SaCheckPermission("system:case:event:detail")
    @GetMapping("/detail/{asjid}")
    public R<SlCaseEventProxyDetailVo> detail(@PathVariable String asjid) {
        return R.ok(caseEventProxyService.queryDetail(asjid));
    }


    @ApiOperation("刷新图形数据")
    @SaCheckPermission("system:case:event:refresh")
    @GetMapping("/refresh")
    public R<Boolean> refresh() {
        return R.ok(caseEventService.refresh());
    }
}

