package com.zhonghe.system.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.zhonghe.common.core.controller.BaseController;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.system.auto.AutoRequest;
import com.zhonghe.system.domain.thirdparty.SlWeatherSign;
import com.zhonghe.system.domain.thirdparty.vo.SlWeatherSignVo;
import com.zhonghe.system.service.ISlWeatherSignService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import java.text.ParseException;

/**
 * 【请填写功能名称】Controller
 *
 * <AUTHOR>
 * @date 2023-08-21
 */
@Validated
@Api(value = "天气预警控制器", tags = {"天气预警管理"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/weatherSign")
public class SlWeatherSignController extends BaseController {

    private final ISlWeatherSignService iSlWeatherSignService;

    /**
     * 查询【天气预警控制器】列表
     */
    @ApiOperation("查询天气预警列表")
    @SaCheckPermission("system:weatherSign:list")
    @GetMapping("/list")
    @AutoRequest(clazz = ISlWeatherSignService.class)
    public TableDataInfo<SlWeatherSign> list(PageQuery page) throws ParseException {
        return iSlWeatherSignService.queryPage(page);
    }

    /**
     * 获取【请填写功能名称】详细信息
     */
    @ApiOperation("获取天气预警详细信息")
    @SaCheckPermission("system:weatherSign:query")
    @GetMapping("/{fywzjid}")
    public R<SlWeatherSignVo> getInfo(@ApiParam("主键")
                                      @NotNull(message = "主键不能为空")
                                      @PathVariable("fywzjid") String fywzjid) {
        return R.ok(iSlWeatherSignService.queryById(fywzjid));
    }

}
