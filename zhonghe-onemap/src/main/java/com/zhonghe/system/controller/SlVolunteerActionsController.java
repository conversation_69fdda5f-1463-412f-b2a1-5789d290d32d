package com.zhonghe.system.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.zhonghe.common.annotation.Log;
import com.zhonghe.common.annotation.RepeatSubmit;
import com.zhonghe.common.core.controller.BaseController;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import com.zhonghe.common.enums.BusinessType;
import com.zhonghe.common.utils.poi.ExcelUtil;
import com.zhonghe.system.domain.bo.SlVolunteerActionsBo;
import com.zhonghe.system.domain.vo.SlVolunteerActionsVo;
import com.zhonghe.system.service.ISlVolunteerActionsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 志愿者活动Controller
 *
 * <AUTHOR>
 * @date 2023-08-04
 */
@Validated
@Api(value = "志愿者活动控制器", tags = {"志愿者活动管理"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/com/zhonghe/system/volunteerActions")
public class SlVolunteerActionsController extends BaseController {

    private final ISlVolunteerActionsService iSlVolunteerActionsService;

    /**
     * 查询志愿者活动列表
     */
    @ApiOperation("查询志愿者活动列表")
    @SaCheckPermission("system:volunteerActions:list")
    @GetMapping("/list")
    public TableDataInfo<SlVolunteerActionsVo> list(SlVolunteerActionsBo bo , PageQuery pageQuery) {
        return iSlVolunteerActionsService.selectPage(bo, pageQuery);
    }

    /**
     * 导出志愿者活动列表
     */
    @ApiOperation("导出志愿者活动列表")
    @SaCheckPermission("system:volunteerActions:export")
    @Log(title = "志愿者活动", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SlVolunteerActionsBo bo, HttpServletResponse response) {
        List<SlVolunteerActionsVo> list = iSlVolunteerActionsService.queryList(bo);
        ExcelUtil.exportExcel(list, "志愿者活动", SlVolunteerActionsVo.class, response);
    }

    /**
     * 获取志愿者活动详细信息
     */
    @ApiOperation("获取志愿者活动详细信息")
    @SaCheckPermission("system:volunteerActions:query")
    @GetMapping("/{actionId}")
    public R<SlVolunteerActionsVo> getInfo(@ApiParam("主键")
                                           @NotNull(message = "主键不能为空")
                                           @PathVariable("actionId") Long actionId) {
        return R.ok(iSlVolunteerActionsService.queryById(actionId));
    }

    /**
     * 新增志愿者活动
     */
    @ApiOperation("新增志愿者活动")
    @SaCheckPermission("system:volunteerActions:add")
    @Log(title = "志愿者活动", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SlVolunteerActionsBo bo) {
        return toAjax(iSlVolunteerActionsService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改志愿者活动
     */
    @ApiOperation("修改志愿者活动")
    @SaCheckPermission("system:volunteerActions:edit")
    @Log(title = "志愿者活动", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SlVolunteerActionsBo bo) {
        return toAjax(iSlVolunteerActionsService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除志愿者活动
     */
    @ApiOperation("删除志愿者活动")
    @SaCheckPermission("system:volunteerActions:remove")
    @Log(title = "志愿者活动", businessType = BusinessType.DELETE)
    @DeleteMapping("/{actionIds}")
    public R<Void> remove(@ApiParam("主键串")
                          @NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] actionIds) {
        return toAjax(iSlVolunteerActionsService.deleteWithValidByIds(Arrays.asList(actionIds), true) ? 1 : 0);
    }
}
