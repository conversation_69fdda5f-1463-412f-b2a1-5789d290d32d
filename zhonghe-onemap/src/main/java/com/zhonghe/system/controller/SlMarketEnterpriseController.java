package com.zhonghe.system.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.zhonghe.common.core.controller.BaseController;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.system.service.ISlMarketEnterpriseService;
import com.zhonghe.system.service.ISlMarketPersonalService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 【请填写功能名称】Controller
 *
 * <AUTHOR>
 * @date 2023-08-14
 */
@Validated
@Api(value = "市场主体-不对外控制器", tags = {"市场主体内部管理"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/com/zhonghe/system/marketEnterprise")
public class SlMarketEnterpriseController extends BaseController {

    private final ISlMarketEnterpriseService iSlMarketEnterpriseService;

    private final ISlMarketPersonalService slMarketPersonalService;

    @ApiOperation("查询刷新地图服务")
    @SaCheckPermission("system:marketEnterprise:refresh")
    @GetMapping("/refresh")
    public R refresh() {
        iSlMarketEnterpriseService.refresh();
        return R.ok();
    }

    @ApiOperation("查询刷新个体户地图服务")
    @SaCheckPermission("system:marketEnterprise:refresh")
    @GetMapping("/personal/refresh")
    public R personalRefresh() {
        slMarketPersonalService.refresh();
        return R.ok();
    }

    /**
     * 查询【请填写功能名称】列表
     */
/*    @ApiOperation("查询【请填写功能名称】列表")
    @SaCheckPermission("system:marketEnterprise:list")
    @GetMapping("/list")*/

    /**
     * 导出【请填写功能名称】列表
     */
    /*@ApiOperation("导出【请填写功能名称】列表")
    @SaCheckPermission("system:marketEnterprise:export")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(SlMarketEnterpriseBo bo, HttpServletResponse response) {
        List<SlMarketEnterpriseVo> list = iSlMarketEnterpriseService.queryList(bo);
        ExcelUtil.exportExcel(list, "【请填写功能名称】", SlMarketEnterpriseVo.class, response);
    }

    *//**
     * 获取【请填写功能名称】详细信息
     *//*
    @ApiOperation("获取【请填写功能名称】详细信息")
    @SaCheckPermission("system:marketEnterprise:query")
    @GetMapping("/{marketEnterpriseId}")
    public R<SlMarketEnterpriseVo> getInfo(@ApiParam("主键")
                                     @NotNull(message = "主键不能为空")
                                     @PathVariable("marketEnterpriseId") Long marketEnterpriseId) {
        return R.ok(iSlMarketEnterpriseService.queryById(marketEnterpriseId));
    }

    *//**
     * 新增【请填写功能名称】
     *//*
    @ApiOperation("新增【请填写功能名称】")
    @SaCheckPermission("system:marketEnterprise:add")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody SlMarketEnterpriseBo bo) {
        return toAjax(iSlMarketEnterpriseService.insertByBo(bo) ? 1 : 0);
    }

    *//**
     * 修改【请填写功能名称】
     *//*
    @ApiOperation("修改【请填写功能名称】")
    @SaCheckPermission("system:marketEnterprise:edit")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody SlMarketEnterpriseBo bo) {
        return toAjax(iSlMarketEnterpriseService.updateByBo(bo) ? 1 : 0);
    }

    *//**
     * 删除【请填写功能名称】
     *//*
    @ApiOperation("删除【请填写功能名称】")
    @SaCheckPermission("system:marketEnterprise:remove")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.DELETE)
    @DeleteMapping("/{marketEnterpriseIds}")
    public R<Void> remove(@ApiParam("主键串")
                                       @NotEmpty(message = "主键不能为空")
                                       @PathVariable Long[] marketEnterpriseIds) {
        return toAjax(iSlMarketEnterpriseService.deleteWithValidByIds(Arrays.asList(marketEnterpriseIds), true) ? 1 : 0);
    }*/
}
