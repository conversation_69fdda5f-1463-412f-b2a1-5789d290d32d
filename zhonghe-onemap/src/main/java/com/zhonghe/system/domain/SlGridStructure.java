package com.zhonghe.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zhonghe.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 网格职位架构对象 sl_grid_structure
 *
 * <AUTHOR>
 * @date 2023-08-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("business.sl_grid_structure")
public class SlGridStructure extends BaseEntity {

    private static final long serialVersionUID= 6702752957741459012L;

    /**
     * $column.columnComment
     */
    @TableId(value = "grid_structure_id")
    private Long gridStructureId;
    /**
     * 父节点名称
     */
    private String gridStructureParentName;
    /**
     * 节点名称
     */
    private String girdStructureName;
    /**
     * 父节点ID
     */
    private Long gridStructureParentId;
    /**
     * 描述
     */
    private String mark;
    /**
     * $column.columnComment
     */
    @TableLogic
    private String delFlag;
    /**
     * $column.columnComment
     */
    private String status;

    private String gridZoneName;
}
