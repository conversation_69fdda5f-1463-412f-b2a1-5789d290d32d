package com.zhonghe.system.domain.thirdparty;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;


/**
 * 【请填写功能名称】对象 zsj_entreg_info
 *
 * <AUTHOR>
 * @date 2023-09-11
 */
@Data
@TableName("public.zsj_entreg_info")
public class ZsjEntregInfo {

    private static final long serialVersionUID=1L;

    /**
     * $column.columnComment
     */
    @TableId(value = "id")
    private Long id;
    /**
     * $column.columnComment
     */
    private String pripid;
    /**
     * $column.columnComment
     */
    private String uniscid;
    /**
     * $column.columnComment
     */
    private String regno;
    /**
     * $column.columnComment
     */
    private String entname;
    /**
     * $column.columnComment
     */
    private String typename;
    /**
     * $column.columnComment
     */
    private String opscope;
    /**
     * $column.columnComment
     */
    private String aicid;
    /**
     * $column.columnComment
     */
    private String regcap;
    /**
     * $column.columnComment
     */
    private String country;
    /**
     * $column.columnComment
     */
    private Date estdate;
    /**
     * $column.columnComment
     */
    private Date apprdate;
    /**
     * $column.columnComment
     */
    private String opstate;
    /**
     * $column.columnComment
     */
    private String lerepname;
    /**
     * $column.columnComment
     */
    private Date opfrom;
    /**
     * $column.columnComment
     */
    private Date opto;
    /**
     * $column.columnComment
     */
    private String industryphy;
    /**
     * $column.columnComment
     */
    private String industryco;
    /**
     * $column.columnComment
     */
    private String dom;
    /**
     * $column.columnComment
     */
    private String faxnumber;
    /**
     * $column.columnComment
     */
    private String empnum;
    /**
     * $column.columnComment
     */
    private String datDt;
    /**
     * $column.columnComment
     */
    private String etlDt;
    /**
     * $column.columnComment
     */
    private String gldmStaDt;
    /**
     * $column.columnComment
     */
    private String gldmEndDt;
    /**
     * $column.columnComment
     */
    private String gldmDelFlag;
    /**
     * $column.columnComment
     */
    private String dsesUuid;
    /**
     * $column.columnComment
     */
    private String industrycoVal;
    /**
     * $column.columnComment
     */
    private String reservedField1;
    /**
     * $column.columnComment
     */
    private String reservedField2;
    /**
     * $column.columnComment
     */
    private Date dataSharingCdTimeIdatat;

}
