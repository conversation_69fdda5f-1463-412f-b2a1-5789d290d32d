package com.zhonghe.system.domain.bo;

import com.zhonghe.common.core.domain.BaseEntity;
import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


/**
 * 残疾人业务对象 sl_disable_population
 *
 * <AUTHOR>
 * @date 2023-09-01
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("残疾人业务对象")
public class SlDisablePopulationBo extends BaseEntity {

    /**
     * $column.columnComment
     */
    @ApiModelProperty(value = "$column.columnComment", required = true)
    @NotNull(message = "$column.columnComment不能为空", groups = { EditGroup.class })
    private Long disableId;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名", required = true)
    @NotBlank(message = "姓名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;

    /**
     * 身份证号
     */
    @ApiModelProperty(value = "身份证号", required = true)
    @NotBlank(message = "身份证号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String idCode;

    /**
     * 性别
     */
    @ApiModelProperty(value = "性别", required = true)
    @NotBlank(message = "性别不能为空", groups = { AddGroup.class, EditGroup.class })
    private String sex;

    /**
     * 民族
     */
    @ApiModelProperty(value = "民族", required = true)
    @NotBlank(message = "民族不能为空", groups = { AddGroup.class, EditGroup.class })
    private String nation;

    /**
     * 出生日期
     */
    @ApiModelProperty(value = "出生日期", required = true)
    @NotBlank(message = "出生日期不能为空", groups = { AddGroup.class, EditGroup.class })
    private String brithDate;

    /**
     * 户籍地址
     */
    @ApiModelProperty(value = "户籍地址", required = true)
    @NotBlank(message = "户籍地址不能为空", groups = { AddGroup.class, EditGroup.class })
    private String baseLocation;

    /**
     * 类型
     */
    @ApiModelProperty(value = "类型", required = true)
    @NotBlank(message = "类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String populationType;

    /**
     * 居住地址
     */
    @ApiModelProperty(value = "居住地址", required = true)
    @NotBlank(message = "居住地址不能为空", groups = { AddGroup.class, EditGroup.class })
    private String livingLocation;

    /**
     * 工作地址
     */
    @ApiModelProperty(value = "工作地址", required = true)
    @NotBlank(message = "工作地址不能为空", groups = { AddGroup.class, EditGroup.class })
    private String workingLocation;

    /**
     * $column.columnComment
     */
    @ApiModelProperty(value = "$column.columnComment", required = true)
    @NotBlank(message = "$column.columnComment不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;


}
