package com.zhonghe.system.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zhonghe.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description:
 * @author: cq
 * @date: 2023/8/1 15:30
 */

@Data
@ApiModel("网格员职位信息")
@ExcelIgnoreUnannotated
public class SlGridPostStructureDto extends BaseEntity {

    /**
     * 父节点名称
     */
    @ExcelProperty(value = "父节点名称")
    @ApiModelProperty("父节点名称")
    private String gridStructureParentName;

    /**
     * 节点名称
     */
    @ExcelProperty(value = "节点名称")
    @ApiModelProperty("节点名称")
    private String girdStructureName;

    /**
     * 描述
     */
    @ExcelProperty(value = "描述")
    @ApiModelProperty("描述")
    private String mark;

    @ExcelProperty(value = "片区")
    @ApiModelProperty("片区")
    private String gridZoneName;
}

