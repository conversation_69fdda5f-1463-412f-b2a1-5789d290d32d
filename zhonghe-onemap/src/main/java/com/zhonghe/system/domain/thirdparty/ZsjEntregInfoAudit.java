package com.zhonghe.system.domain.thirdparty;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;


/**
 * 【请填写功能名称】对象 zsj_entreg_info_audit
 *
 * <AUTHOR>
 * @date 2023-09-13
 */
@Data
@TableName("business.zsj_entreg_info_audit")
public class ZsjEntregInfoAudit {

    private static final long serialVersionUID=1L;

    /**
     * $column.columnComment
     */
    @TableId(value = "audit_id")
    private Long auditId;
    /**
     * $column.columnComment
     */
    private Long marketId;
    /**
     * $column.columnComment
     */
    private String address;
    /**
     * $column.columnComment
     */
    @TableLogic
    private String delFlag;
    /**
     * $column.columnComment
     */
    private Date insertedAt;

    private String pripid;

}
