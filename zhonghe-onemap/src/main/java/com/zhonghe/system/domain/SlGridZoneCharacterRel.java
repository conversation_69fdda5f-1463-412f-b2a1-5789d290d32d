package com.zhonghe.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zhonghe.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 网格划分区与人物关系对象 sl_grid_zone_character_rel
 *
 * <AUTHOR>
 * @date 2023-08-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("business.sl_grid_zone_character_rel")
public class SlGridZoneCharacterRel extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * $column.columnComment
     */
    @TableId(value = "zone_character_rel_id")
    private Long zoneCharacterRelId;
    /**
     * $column.columnComment
     */
    private Long gridZoneId;
    /**
     * $column.columnComment
     */
    private Long gridCharacterId;
    /**
     * $column.columnComment
     */
    @TableLogic
    private String delFlag;
    /**
     * $column.columnComment
     */
    private String status;

}
