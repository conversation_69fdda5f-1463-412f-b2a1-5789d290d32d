package com.zhonghe.system.domain.bo.gas;

import com.zhonghe.common.core.domain.BaseEntity;
import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


/**
 * 燃气统计业务对象 sl_gas_restaurant_gather
 *
 * <AUTHOR>
 * @date 2023-07-28
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("燃气统计业务对象")
public class SlGasRestaurantGatherBo extends BaseEntity {

    /**
     * $column.columnComment
     */
    @ApiModelProperty(value = "$column.columnComment", required = true)
    @NotNull(message = "$column.columnComment不能为空", groups = { EditGroup.class })
    private Long gatherGasId;

    /**
     * 辖区
     */
    @ApiModelProperty(value = "辖区", required = true)
    @NotBlank(message = "辖区不能为空", groups = { AddGroup.class, EditGroup.class })
    private String sectionName;

    /**
     * 主体名称
     */
    @ApiModelProperty(value = "主体名称", required = true)
    @NotBlank(message = "主体名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String subjectName;

    /**
     * 地址
     */
    @ApiModelProperty(value = "地址", required = true)
    @NotBlank(message = "地址不能为空", groups = { AddGroup.class, EditGroup.class })
    private String subjectAddress;

    /**
     * 管理人姓名
     */
    @ApiModelProperty(value = "管理人姓名", required = true)
    @NotBlank(message = "管理人姓名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String subjectManager;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话", required = true)
    @NotBlank(message = "联系电话不能为空", groups = { AddGroup.class, EditGroup.class })
    private String subjectPhone;

    /**
     * 网格员姓名
     */
    @ApiModelProperty(value = "网格员姓名", required = true)
    @NotBlank(message = "网格员姓名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String gridManager;

    /**
     * 燃气类型描述：天然气、电、气瓶
     */
    @ApiModelProperty(value = "燃气类型描述：天然气、电、气瓶", required = true)
    @NotBlank(message = "燃气类型描述：天然气、电、气瓶不能为空", groups = { AddGroup.class, EditGroup.class })
    private String powerType;

    /**
     * 燃气类型具体描述
     */
    @ApiModelProperty(value = "燃气类型具体描述", required = true)
    @NotBlank(message = "燃气类型具体描述不能为空", groups = { AddGroup.class, EditGroup.class })
    private String powerTypeMark;

    /**
     * 燃气使用情况
     */
    @ApiModelProperty(value = "燃气使用情况", required = true)
    @NotBlank(message = "燃气使用情况不能为空", groups = { AddGroup.class, EditGroup.class })
    private String powerStoreMark;

    /**
     * 大气瓶数量
     */
    @ApiModelProperty(value = "大气瓶数量", required = true)
    @NotNull(message = "大气瓶数量不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long powerStoreLargeNum;

    /**
     * 小气瓶数量
     */
    @ApiModelProperty(value = "小气瓶数量", required = true)
    @NotNull(message = "小气瓶数量不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long powerStoreSmallNum;

    /**
     * 隐患描述、备注
     */
    @ApiModelProperty(value = "隐患描述、备注", required = true)
    @NotBlank(message = "隐患描述、备注不能为空", groups = { AddGroup.class, EditGroup.class })
    private String questionMark;

    /**
     * 气瓶隐患
     */
    @ApiModelProperty(value = "气瓶隐患", required = true)
    @NotBlank(message = "气瓶隐患不能为空", groups = { AddGroup.class, EditGroup.class })
    private String questionGasBottle;

    /**
     * 管道隐患
     */
    @ApiModelProperty(value = "管道隐患", required = true)
    @NotBlank(message = "管道隐患不能为空", groups = { AddGroup.class, EditGroup.class })
    private String questionGasPipeline;

    /**
     * 报警器隐患
     */
    @ApiModelProperty(value = "报警器隐患", required = true)
    @NotBlank(message = "报警器隐患不能为空", groups = { AddGroup.class, EditGroup.class })
    private String questionGasWarmingMachine;

    /**
     * 存储隐患
     */
    @ApiModelProperty(value = "存储隐患", required = true)
    @NotBlank(message = "存储隐患不能为空", groups = { AddGroup.class, EditGroup.class })
    private String questionGasStoreWeight;

    /**
     * 摆放隐患
     */
    @ApiModelProperty(value = "摆放隐患", required = true)
    @NotBlank(message = "摆放隐患不能为空", groups = { AddGroup.class, EditGroup.class })
    private String questionGasStoreLocation;

    /**
     * 黑气标识
     */
    @ApiModelProperty(value = "黑气标识", required = true)
    @NotBlank(message = "黑气标识不能为空", groups = { AddGroup.class, EditGroup.class })
    private String questionGasIllegal;

    /**
     * 是否整改
     */
    @ApiModelProperty(value = "是否整改", required = true)
    @NotBlank(message = "是否整改不能为空", groups = { AddGroup.class, EditGroup.class })
    private String questionRectification;

    /**
     * 收集时间
     */
    @ApiModelProperty(value = "收集时间", required = true)
    @NotBlank(message = "收集时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private String gatherTime;

    /**
     * $column.columnComment
     */
    @ApiModelProperty(value = "$column.columnComment", required = true)
    @NotBlank(message = "$column.columnComment不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;

    @ApiModelProperty("开始日期")
    private String startDate;

    @ApiModelProperty("结束日期")
    private String endDate;

    @ApiModelProperty("特大瓶气瓶数量")
    private Long powerStoreHugeNum;

    @ApiModelProperty("关键字")
    private String keyWord;
}
