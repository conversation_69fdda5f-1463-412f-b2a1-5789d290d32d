package com.zhonghe.system.domain.thirdparty;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;


/**
 * 【请填写功能名称】对象 sl_patrol_order
 *
 * <AUTHOR>
 * @date 2023-08-15
 */
@Data
@TableName("business.sl_patrol_order")
public class SlPatrolOrder {

    private static final long serialVersionUID = -5701120771576949489L;

    /**
     * $column.columnComment
     */
    private String gdbh;
    /**
     * $column.columnComment
     */
    private String bm;
    /**
     * $column.columnComment
     */
    private String xq;
    /**
     * $column.columnComment
     */
    private String ztlx;
    /**
     * $column.columnComment
     */
    private String ztmc;
    /**
     * $column.columnComment
     */
    private String zhdz;
    /**
     * $column.columnComment
     */
    private String scsj;
    /**
     * $column.columnComment
     */
    private String clqx;
    /**
     * $column.columnComment
     */
    private String jccs;
    /**
     * $column.columnComment
     */
    private String scyhs;
    /**
     * $column.columnComment
     */
    private String zt;

}
