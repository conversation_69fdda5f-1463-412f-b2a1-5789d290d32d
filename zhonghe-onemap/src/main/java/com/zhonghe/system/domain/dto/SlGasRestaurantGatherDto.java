package com.zhonghe.system.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zhonghe.common.annotation.ExcelDictFormat;
import com.zhonghe.common.convert.ExcelDictConvert;
import com.zhonghe.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @description:
 * @author: cq
 * @date: 2023/7/28 16:19
 */

@Data
@ApiModel("燃气统计EXCEL对象")
@ExcelIgnoreUnannotated
public class SlGasRestaurantGatherDto extends BaseEntity {
    private static final long serialVersionUID = 4772709901292532620L;

    /**
     * 辖区
     */
    @ExcelProperty(value = "辖区")
    @ApiModelProperty("辖区")
    private String sectionName;

    /**
     * 主体名称
     */
    @ExcelProperty(value = "主体名称")
    @ApiModelProperty("主体名称")
    private String subjectName;

    /**
     * 地址
     */
    @ExcelProperty(value = "地址")
    @ApiModelProperty("地址")
    private String subjectAddress;

    /**
     * 管理人姓名
     */
    @ExcelProperty(value = "管理人")
    @ApiModelProperty("管理人姓名")
    private String subjectManager;

    /**
     * 联系电话
     */
    @ExcelProperty(value = "联系电话")
    @ApiModelProperty("联系电话")
    private String subjectPhone;

    /**
     * 网格员姓名
     */
    @ExcelProperty(value = "网格员")
    @ApiModelProperty("网格员姓名")
    private String gridManager;

    /**
     * 特大气瓶数量
     */
    @ExcelProperty(value = "特大瓶数量")
    @ApiModelProperty("特大气瓶数量")
    private Long powerStoreHugeNum;

    /**
     * 大气瓶数量
     */
    @ExcelProperty(value = "大瓶数量")
    @ApiModelProperty("大气瓶数量")
    private Long powerStoreLargeNum;

    /**
     * 小气瓶数量
     */
    @ExcelProperty(value = "小瓶数量")
    @ApiModelProperty("小气瓶数量")
    private Long powerStoreSmallNum;
    /**
     * 燃气使用情况
     */
    @ExcelProperty(value = "燃气存储使用情况描述")
    @ApiModelProperty("燃气使用情况")
    private String powerStoreMark;

    /**
     * 燃气类型描述：天然气、电、气瓶
     */
    @ExcelProperty(value = "燃气类型", converter = ExcelDictConvert.class)
    @ApiModelProperty("燃气类型描述：天然气、电、气瓶")
    @ExcelDictFormat(readConverterExp = "1=存储瓶,2=天然气,3=电,4=混合,5=倒闭")
    private String powerType;


    /**
     * 燃气类型具体描述
     */
    @ExcelProperty(value = "燃气类型描述")
    private String powerTypeMark;



    /**
     * 气瓶隐患
     */
    @ExcelProperty(value = "气瓶隐患", converter = ExcelDictConvert.class)
    @ApiModelProperty("气瓶隐患")
    @ExcelDictFormat(readConverterExp = "1=存在,0=不存在")
    private String questionGasBottle;

    /**
     * 管道隐患
     */
    @ExcelProperty(value = "管道隐患", converter = ExcelDictConvert.class)
    @ApiModelProperty("管道隐患")
    @ExcelDictFormat(readConverterExp = "1=存在,0=不存在")
    private String questionGasPipeline;

    /**
     * 报警器隐患
     */
    @ExcelProperty(value = "报警器隐患", converter = ExcelDictConvert.class)
    @ApiModelProperty("报警器隐患")
    @ExcelDictFormat(readConverterExp = "1=存在,0=不存在")
    private String questionGasWarmingMachine;

    /**
     * 存储隐患
     */
    @ExcelProperty(value = "存储隐患", converter = ExcelDictConvert.class)
    @ApiModelProperty("存储隐患")
    @ExcelDictFormat(readConverterExp = "1=存在,0=不存在")
    private String questionGasStoreWeight;

    /**
     * 摆放隐患
     */
    @ExcelProperty(value = "摆放隐患", converter = ExcelDictConvert.class)
    @ApiModelProperty("摆放隐患")
    @ExcelDictFormat(readConverterExp = "1=存在,0=不存在")
    private String questionGasStoreLocation;

    /**
     * 黑气标识
     */
    @ExcelProperty(value = "黑气", converter = ExcelDictConvert.class)
    @ApiModelProperty("黑气标识")
    @ExcelDictFormat(readConverterExp = "1=存在,0=不存在")
    private String questionGasIllegal;

    /**
     * 用气环境风险
     */
    @ExcelProperty(value = "用气环境隐患", converter = ExcelDictConvert.class)
    @ApiModelProperty("用气环境隐患")
    @ExcelDictFormat(readConverterExp = "1=存在,0=不存在")
    private String questionGasEnvironment;
    /**
     * 隐患描述、备注
     */
    @ExcelProperty(value = "燃气隐患描述")
    @ApiModelProperty("隐患描述、备注")
    private String questionMark;

    /**
     * 是否整改
     */
    @ExcelProperty(value = "是否整改", converter = ExcelDictConvert.class)
    @ApiModelProperty("是否整改")
    @ExcelDictFormat(readConverterExp = "1=是,0=否")
    private String questionRectification;

    /**
     * 收集时间
     */
    @ExcelProperty(value = "收集时间")
    @ApiModelProperty("收集时间")
    private Date gatherTime;

}

