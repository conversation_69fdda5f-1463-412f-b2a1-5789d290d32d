package com.zhonghe.system.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zhonghe.common.annotation.ExcelDictFormat;
import com.zhonghe.common.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


/**
 * 志愿者视图对象 sl_volunteer
 *
 * <AUTHOR>
 * @date 2023-08-03
 */
@Data
@ApiModel("志愿者视图对象")
@ExcelIgnoreUnannotated
public class SlVolunteerVo {

    private static final long serialVersionUID = 298477829640016695L;

    /**
     * $column.columnComment
     */
    @ExcelProperty(value = "${comment}", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "$column.readConverterExp()")
    @ApiModelProperty("$column.columnComment")
    private Long volunteerId;

    /**
     * 姓名
     */
    @ExcelProperty(value = "姓名")
    @ApiModelProperty("姓名")
    private String volunteerName;

    /**
     * 性别
     */
    @ExcelProperty(value = "性别")
    @ApiModelProperty("性别")
    private String sex;

    /**
     * 登录账号
     */
    @ExcelProperty(value = "登录账号")
    @ApiModelProperty("登录账号")
    private String volunteerLoginCode;

    /**
     * 证件号码
     */
    @ExcelProperty(value = "证件号码")
    @ApiModelProperty("证件号码")
    private String volunteerIdNumber;

    /**
     * 手机号码
     */
    @ExcelProperty(value = "手机号码")
    @ApiModelProperty("手机号码")
    private String volunteerPhone;

    /**
     * 注册时间
     */
    @ExcelProperty(value = "注册时间")
    @ApiModelProperty("注册时间")
    private Date volunteerRegisterDate;

    /**
     * 校验信息
     */
    @ExcelProperty(value = "校验信息")
    @ApiModelProperty("校验信息")
    private String volunteerVerify;

    /**
     * 是否面验
     */
    @ExcelProperty(value = "是否面验")
    @ApiModelProperty("是否面验")
    private String volunteerFaceVerify;

    /**
     * 审批状态
     */
    @ExcelProperty(value = "审批状态")
    @ApiModelProperty("审批状态")
    private String volunteerPass;

    /**
     * 政治面貌
     */
    @ExcelProperty(value = "政治面貌")
    @ApiModelProperty("政治面貌")
    private String volunteerPolitical;

    /**
     * 服务时长
     */
    @ExcelProperty(value = "服务时长")
    @ApiModelProperty("服务时长")
    private String volunteerServerTime;

    /**
     * 培训时长
     */
    @ExcelProperty(value = "培训时长")
    @ApiModelProperty("培训时长")
    private String volunteerTrainTime;

    /**
     * $column.columnComment
     */
    @ExcelProperty(value = "${comment}", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "$column.readConverterExp()")
    @ApiModelProperty("$column.columnComment")
    private String status;


}
