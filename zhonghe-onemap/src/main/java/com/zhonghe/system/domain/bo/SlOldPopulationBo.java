package com.zhonghe.system.domain.bo;

import com.zhonghe.common.core.domain.BaseEntity;
import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


/**
 * 老年失能业务对象 sl_old_population
 *
 * <AUTHOR>
 * @date 2023-09-01
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("老年失能业务对象")
public class SlOldPopulationBo extends BaseEntity {

    /**
     * $column.columnComment
     */
    @ApiModelProperty(value = "$column.columnComment", required = true)
    @NotNull(message = "$column.columnComment不能为空", groups = { EditGroup.class })
    private Long oldId;

    /**
     * 社区、村
     */
    @ApiModelProperty(value = "社区、村", required = true)
    @NotBlank(message = "社区、村不能为空", groups = { AddGroup.class, EditGroup.class })
    private String gridName;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名", required = true)
    @NotBlank(message = "姓名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;

    /**
     * 性别
     */
    @ApiModelProperty(value = "性别", required = true)
    @NotBlank(message = "性别不能为空", groups = { AddGroup.class, EditGroup.class })
    private String sex;

    /**
     * 身份证号码
     */
    @ApiModelProperty(value = "身份证号码", required = true)
    @NotBlank(message = "身份证号码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String idCode;

    /**
     * 年龄
     */
    @ApiModelProperty(value = "年龄", required = true)
    @NotNull(message = "年龄不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long age;

    /**
     * 户籍地址
     */
    @ApiModelProperty(value = "户籍地址", required = true)
    @NotBlank(message = "户籍地址不能为空", groups = { AddGroup.class, EditGroup.class })
    private String baseLocation;

    /**
     * 身份属性
     */
    @ApiModelProperty(value = "身份属性", required = true)
    @NotBlank(message = "身份属性不能为空", groups = { AddGroup.class, EditGroup.class })
    private String populationType;

    /**
     * 是否入住养老机构
     */
    @ApiModelProperty(value = "是否入住养老机构", required = true)
    @NotBlank(message = "是否入住养老机构不能为空", groups = { AddGroup.class, EditGroup.class })
    private String livingNursing;

    /**
     * 评定失能程度
     */
    @ApiModelProperty(value = "评定失能程度", required = true)
    @NotBlank(message = "评定失能程度不能为空", groups = { AddGroup.class, EditGroup.class })
    private String disableLv;

    /**
     * 现居地地址
     */
    @ApiModelProperty(value = "现居地地址", required = true)
    @NotBlank(message = "现居地地址不能为空", groups = { AddGroup.class, EditGroup.class })
    private String livingLocation;

    /**
     * 就业单位地址
     */
    @ApiModelProperty(value = "就业单位地址", required = true)
    @NotBlank(message = "就业单位地址不能为空", groups = { AddGroup.class, EditGroup.class })
    private String workingLocation;

    /**
     * $column.columnComment
     */
    @ApiModelProperty(value = "$column.columnComment", required = true)
    @NotBlank(message = "$column.columnComment不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;


}
