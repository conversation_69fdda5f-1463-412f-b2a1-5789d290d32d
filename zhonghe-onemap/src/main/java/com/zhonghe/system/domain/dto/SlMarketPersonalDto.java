package com.zhonghe.system.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zhonghe.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description:
 * @author: cq
 * @date: 2023/8/14 10:52
 */
@Data
@ExcelIgnoreUnannotated
public class SlMarketPersonalDto extends BaseEntity {


    /**
     * $column.columnComment
     */
    @ExcelProperty(value = "统一社会信用代码/注册号")
    @ApiModelProperty("$column.columnComment")
    private String personalCreditCode;

    /**
     * $column.columnComment
     */
    @ExcelProperty(value = "主体名称")
    @ApiModelProperty("$column.columnComment")
    private String personalName;

    /**
     * $column.columnComment
     */
    @ExcelProperty(value = "住所/经营场所/驻在场所")
    @ApiModelProperty("$column.columnComment")
    private String personalAddress;

    /**
     * $column.columnComment
     */
    @ExcelProperty(value = "主体类型")
    @ApiModelProperty("$column.columnComment")
    private String personalType;

    /**
     * $column.columnComment
     */
    @ExcelProperty(value = "成立日期")
    @ApiModelProperty("$column.columnComment")
    private String createDate;

    /**
     * $column.columnComment
     */
    @ExcelProperty(value = "经营期限止")
    @ApiModelProperty("$column.columnComment")
    private String operatingEndDate;

    /**
     * $column.columnComment
     */
    @ExcelProperty(value = "经营范围")
    @ApiModelProperty("$column.columnComment")
    private String operatingScope;

    /**
     * $column.columnComment
     */
    @ExcelProperty(value = "法定代表人/负责人/经营者")
    @ApiModelProperty("$column.columnComment")
    private String legalPerson;

    /**
     * $column.columnComment
     */
    @ExcelProperty(value = "法定代表人/负责人/经营者证件类型")
    @ApiModelProperty("$column.columnComment")
    private String idType;

    /**
     * $column.columnComment
     */
    @ExcelProperty(value = "法定代表人/负责人/经营者证件号码")
    @ApiModelProperty("$column.columnComment")
    private String idCode;

    /**
     * $column.columnComment
     */
    @ExcelProperty(value = "注册资本币种")
    @ApiModelProperty("$column.columnComment")
    private String currencyRegister;

}

