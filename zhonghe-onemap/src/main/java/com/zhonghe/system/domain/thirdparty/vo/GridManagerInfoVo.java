package com.zhonghe.system.domain.thirdparty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description:
 * @author: cq
 * @date: 2023/8/18 11:16
 */

@Data
@ApiModel("网格长信息")
public class GridManagerInfoVo {


    @ApiModelProperty("姓名")
    private String name;

    @ApiModelProperty("家庭电话")
    private String telephone;


    @ApiModelProperty("工作电话")
    private String workTelephone;

    @ApiModelProperty("所属网格")
    private String gridZoneName;

    @ApiModelProperty("所属片区")
    private String characterZoneName;
}

