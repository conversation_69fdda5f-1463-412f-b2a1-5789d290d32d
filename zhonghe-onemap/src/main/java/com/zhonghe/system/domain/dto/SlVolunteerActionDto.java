package com.zhonghe.system.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zhonghe.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @description:
 * @author: cq
 * @date: 2023/8/4 15:25
 */

@Data
@ApiModel("志愿者活动")
@ExcelIgnoreUnannotated
public class SlVolunteerActionDto extends BaseEntity {
    /**
     * 活动编号
     */
    @ExcelProperty(value = "活动编号")
    @ApiModelProperty("活动编号")
    private String actionNumber;

    /**
     * 活动名称
     */
    @ExcelProperty(value = "活动名称")
    @ApiModelProperty("活动名称")
    private String actionName;

    /**
     * 联系人
     */
    @ExcelProperty(value = "联系人")
    @ApiModelProperty("联系人")
    private String actionContract;

    /**
     * 联系电话
     */
    @ExcelProperty(value = "联系电话")
    @ApiModelProperty("联系电话")
    private String actionContractPhone;

    /**
     * 发布组织
     */
    @ExcelProperty(value = "发布组织")
    @ApiModelProperty("发布组织")
    private String actionPublishOrg;

    /**
     * 发布时间
     */
    @ExcelProperty(value = "发布时间")
    @ApiModelProperty("发布时间")
    private Date actionPublishDate;

    /**
     * 补录状态
     */
    @ExcelProperty(value = "补录状态")
    @ApiModelProperty("补录状态")
    private String actionAdditionalStatus;

    /**
     * 活动状态
     */
    @ExcelProperty(value = "活动状态")
    @ApiModelProperty("活动状态")
    private String actionStatus;

    /**
     * 活动类型
     */
    @ExcelProperty(value = "活动类型")
    @ApiModelProperty("活动类型")
    private String actionType;

    /**
     * 活动标签
     */
    @ExcelProperty(value = "活动标签")
    @ApiModelProperty("活动标签")
    private String actionTag;

    /**
     * 活动时间-开始
     */
    @ExcelProperty(value = "活动时间-开始")
    @ApiModelProperty("活动时间-开始")
    private Date actionStartTime;

    /**
     * 活动时间-截止
     */
    @ExcelProperty(value = "活动时间-截止")
    @ApiModelProperty("活动时间-截止")
    private Date actionEndTime;

    /**
     * 已录用
     */
    @ExcelProperty(value = "已录用")
    @ApiModelProperty("已录用")
    private String actionEmploy;
}

