package com.zhonghe.system.domain.thirdparty;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.Date;

/**
 * @description:
 * @author: cq
 * @date: 2023/11/7 10:25
 */

@Data
@TableName("public.t_integration_clue")
@ApiModel("案事件线索")
public class IntegrationClue {

    private String dangerId; // 案事件id
    private String subjectId; // 主体id
    private String subjectName; // 主体名称
    private String buildAddress; // 建筑物地址
    private String lng; // 经度
    private String lat; // 纬度
    private String dangerDescribe; // 隐患描述
    private String disposalDeptNo; // 处置部门编码
    private String disposalDeptName; // 处置部门名称
    private Integer disPeriod; // 处置期限
    private String createName; // 处理人姓名
    private String soundUrl; // 语音路径
    private String imageUrl; // 图片路径
    private Date verTerm; // 处置截止日期
    private Date createDate; // 生成时间
    private String dsesUuid; // 代理主键
    private Date addTime; // 新增时间
    private String cdOperation; // 增量标识
    private Date cdTime; // 增量时间
    private String cdBatch; // 批次号
    @TableId(value = "id")
    private Long id; // 非业务主键
    private Date dmpShareCdTimeIdatat; // 运营方技术字段,非业务字段,请忽略并勿使用
    private Date dataSharingCdTimeIdatat; // 用数库自动配置的入库时间技术字段,非业务字段,请勿挂接

}

