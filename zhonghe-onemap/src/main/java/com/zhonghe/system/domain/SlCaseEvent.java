package com.zhonghe.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zhonghe.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 案时间对象 sl_case_event
 *
 * <AUTHOR>
 * @date 2023-08-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("business.sl_case_event")
public class SlCaseEvent extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * $column.columnComment
     */
    @TableId(value = "case_event_id")
    private Long caseEventId;
    /**
     * 案事件编号
     */
    private String caseEventCode;
    /**
     * 案事件来源
     */
    private String caseEventSource;
    /**
     * 镇街
     */
    private String caseEventStreet;
    /**
     * 社区
     */
    private String caseEventZone;
    /**
     * 网格
     */
    private String caseEventGrid;
    /**
     * 所属部门
     */
    private String caseEventDepartment;
    /**
     * 隐患描述
     */
    private String caseEventReason;
    /**
     * 主体名称
     */
    private String caseEventSubject;
    /**
     * 地址
     */
    private String caseEventAddress;
    /**
     * 生成时间
     */
    private String caseEventCreateTime;
    /**
     * 处理期限
     */
    private String caseEventEndDate;
    /**
     * 处置部门
     */
    private String caseEventHandlerOrg;
    /**
     * 状态
     */
    private String caseEventStatus;
    /**
     * 描述
     */
    private String caseEventMark;
    /**
     * 采集字段信息
     */
    private String caseEventGatherSource;
    /**
     * $column.columnComment
     */
    @TableLogic
    private String delFlag;
    /**
     * $column.columnComment
     */
    private String status;

}
