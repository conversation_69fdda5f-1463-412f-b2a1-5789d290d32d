package com.zhonghe.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zhonghe.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;


/**
 * 重点优抚对象对象 sl_important_population
 *
 * <AUTHOR>
 * @date 2023-09-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("business.sl_important_population")
public class SlImportantPopulation extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * $column.columnComment
     */
    @TableId(value = "population_id")
    private Long populationId;
    /**
     * 行政区划
     */
    private String gridName;
    /**
     * 姓名
     */
    private String name;
    /**
     * 性别
     */
    private String sex;
    /**
     * 出生日期
     */
    private Date brithDate;
    /**
     * 身份证号
     */
    private String idCode;
    /**
     * 户籍地
     */
    private String baseLocation;
    /**
     * 人员类别
     */
    private String populationType;
    /**
     * 家庭住址
     */
    private String familyLocation;
    /**
     * 手机号码
     */
    private String phone;
    /**
     * 二标地址
     */
    private String standardLocation;
    /**
     * 居住地址
     */
    private String livingLocation;
    /**
     * 工作地址
     */
    private String workingLocation;
    /**
     * $column.columnComment
     */
    @TableLogic
    private String delFlag;
    /**
     * $column.columnComment
     */
    private String status;

}
