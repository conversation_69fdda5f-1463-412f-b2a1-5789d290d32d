package com.zhonghe.system.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zhonghe.common.annotation.ExcelDictFormat;
import com.zhonghe.common.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 燃气共享数据坐标视图对象 sl_gas_onenet
 *
 * <AUTHOR>
 * @date 2023-08-10
 */
@Data
@ApiModel("燃气共享数据坐标视图对象")
@ExcelIgnoreUnannotated
public class SlGasOnenetVo {

    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    @ExcelProperty(value = "${comment}", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "$column.readConverterExp()")
    @ApiModelProperty("$column.columnComment")
    private Long onenetGasId;

    /**
     * 燃气表主键
     */
    @ExcelProperty(value = "燃气表主键")
    @ApiModelProperty("燃气表主键")
    private Long gasId;

    /**
     * gas表名称
     */
    @ExcelProperty(value = "gas表名称")
    @ApiModelProperty("gas表名称")
    private String gasSubjectName;

    /**
     * $column.columnComment
     */
    @ExcelProperty(value = "${comment}", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "$column.readConverterExp()")
    @ApiModelProperty("$column.columnComment")
    private String gasGem;

    /**
     * $column.columnComment
     */
    @ExcelProperty(value = "${comment}", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "$column.readConverterExp()")
    @ApiModelProperty("$column.columnComment")
    private Long fid;

    /**
     * 一网共享数据
     */
    @ExcelProperty(value = "一网共享数据")
    @ApiModelProperty("一网共享数据")
    private String onenetJson;

    /**
     * $column.columnComment
     */
    @ExcelProperty(value = "${comment}", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "$column.readConverterExp()")
    @ApiModelProperty("$column.columnComment")
    private String status;


}
