package com.zhonghe.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zhonghe.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;


/**
 * 残疾人对象 sl_disable_population
 *
 * <AUTHOR>
 * @date 2023-09-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("business.sl_disable_population")
public class SlDisablePopulation extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * $column.columnComment
     */
    @TableId(value = "disable_id")
    private Long disableId;
    /**
     * 姓名
     */
    private String name;
    /**
     * 身份证号
     */
    private String idCode;
    /**
     * 性别
     */
    private String sex;
    /**
     * 民族
     */
    private String nation;
    /**
     * 出生日期
     */
    private Date brithDate;
    /**
     * 户籍地址
     */
    private String baseLocation;
    /**
     * 类型
     */
    private String populationType;
    /**
     * 居住地址
     */
    private String livingLocation;
    /**
     * 工作地址
     */
    private String workingLocation;
    /**
     * $column.columnComment
     */
    @TableLogic
    private String delFlag;
    /**
     * $column.columnComment
     */
    private String status;

}
