package com.zhonghe.system.domain.vo.gas;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description:
 * @author: cq
 * @date: 2023/7/31 11:48
 */

@ApiModel("地图燃气统计")
@Data
public class SlGasRestaurantGatherStatisticVo {

    @ApiModelProperty("数量")
    private Long count;


    @ApiModelProperty("合规餐饮数")
    private Long illegalGasCount;


    @ApiModelProperty("非燃气用气主体数")
    private Long unRectificationCount;
}

