package com.zhonghe.system.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zhonghe.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


/**
 * 残疾人视图对象 sl_disable_population
 *
 * <AUTHOR>
 * @date 2023-09-01
 */
@Data
@ApiModel("残疾人视图对象")
@ExcelIgnoreUnannotated
public class SlDisablePopulationDto  extends BaseEntity {


    /**
     * 姓名
     */
    @ExcelProperty(value = "姓名")
    @ApiModelProperty("姓名")
    private String name;

    /**
     * 身份证号
     */
    @ExcelProperty(value = "身份证号")
    @ApiModelProperty("身份证号")
    private String idCode;

    /**
     * 性别
     */
    @ExcelProperty(value = "性别")
    @ApiModelProperty("性别")
    private String sex;

    /**
     * 民族
     */
    @ExcelProperty(value = "民族")
    @ApiModelProperty("民族")
    private String nation;

    /**
     * 出生日期
     */
    @ExcelProperty(value = "出生日期")
    @ApiModelProperty("出生日期")
    private Date brithDate;

    /**
     * 户籍地址
     */
    @ExcelProperty(value = "户籍地址")
    @ApiModelProperty("户籍地址")
    private String baseLocation;

    /**
     * 类型
     */
    @ExcelProperty(value = "类型")
    @ApiModelProperty("类型")
    private String populationType;

    /**
     * 居住地址
     */
    @ExcelProperty(value = "居住地址")
    @ApiModelProperty("居住地址")
    private String livingLocation;

    /**
     * 工作地址
     */
    @ExcelProperty(value = "工作地址")
    @ApiModelProperty("工作地址")
    private String workingLocation;



}
