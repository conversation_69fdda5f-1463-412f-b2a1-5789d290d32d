package com.zhonghe.system.domain.bo;

import com.zhonghe.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 出租屋信息业务对象 sl_rental_house
 *
 * <AUTHOR>
 * @date 2023-08-11
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("出租屋信息业务对象")
public class SlRentalHouseBo extends BaseEntity {

    /**
     * $column.columnComment
     *//*
    @ApiModelProperty(value = "$column.columnComment", required = true)
    @NotNull(message = "$column.columnComment不能为空", groups = { EditGroup.class })
    private Long rentalHouseId;

    *//**
     * 所属网格
     *//*
    @ApiModelProperty(value = "所属网格", required = true)
    @NotBlank(message = "所属网格不能为空", groups = { AddGroup.class, EditGroup.class })
    private String gridZone;

    *//**
     * 所属建筑地址
     *//*
    @ApiModelProperty(value = "所属建筑地址", required = true)
    @NotBlank(message = "所属建筑地址不能为空", groups = { AddGroup.class, EditGroup.class })
    private String buildingAddress;

    *//**
     * 房屋类型
     *//*
    @ApiModelProperty(value = "房屋类型", required = true)
    @NotBlank(message = "房屋类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String buildingType;

    *//**
     * 更新时间
     *//*
    @ApiModelProperty(value = "更新时间", required = true)
    @NotBlank(message = "更新时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private String updatedTime;

    *//**
     * 创建时间
     *//*
    @ApiModelProperty(value = "创建时间", required = true)
    @NotBlank(message = "创建时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private String createdTime;

    *//**
     * 更新人
     *//*
    @ApiModelProperty(value = "更新人", required = true)
    @NotBlank(message = "更新人不能为空", groups = { AddGroup.class, EditGroup.class })
    private String updater;

    *//**
     * 创建人
     *//*
    @ApiModelProperty(value = "创建人", required = true)
    @NotBlank(message = "创建人不能为空", groups = { AddGroup.class, EditGroup.class })
    private String creator;

    *//**
     * 出租屋类别
     *//*
    @ApiModelProperty(value = "出租屋类别", required = true)
    @NotBlank(message = "出租屋类别不能为空", groups = { AddGroup.class, EditGroup.class })
    private String rentalType;

    *//**
     * 出租屋分级
     *//*
    @ApiModelProperty(value = "出租屋分级", required = true)
    @NotBlank(message = "出租屋分级不能为空", groups = { AddGroup.class, EditGroup.class })
    private String rentalLv;

    *//**
     * 招牌名称
     *//*
    @ApiModelProperty(value = "招牌名称", required = true)
    @NotBlank(message = "招牌名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String signName;

    *//**
     * 所属建筑
     *//*
    @ApiModelProperty(value = "所属建筑", required = true)
    @NotBlank(message = "所属建筑不能为空", groups = { AddGroup.class, EditGroup.class })
    private String belongToBuilding;

    *//**
     * 主体地址
     *//*
    @ApiModelProperty(value = "主体地址", required = true)
    @NotBlank(message = "主体地址不能为空", groups = { AddGroup.class, EditGroup.class })
    private String mainSubjectAddress;

    *//**
     * 有无备案登记号
     *//*
    @ApiModelProperty(value = "有无备案登记号", required = true)
    @NotBlank(message = "有无备案登记号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String hasFillCode;

    *//**
     * 出租屋备案号
     *//*
    @ApiModelProperty(value = "出租屋备案号", required = true)
    @NotBlank(message = "出租屋备案号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String rentHouseFillCode;

    *//**
     * 核查实际地址是否与登记地址一致
     *//*
    @ApiModelProperty(value = "核查实际地址是否与登记地址一致", required = true)
    @NotBlank(message = "核查实际地址是否与登记地址一致不能为空", groups = { AddGroup.class, EditGroup.class })
    private String hasCheckedAddress;

    *//**
     * 备案登记地址
     *//*
    @ApiModelProperty(value = "备案登记地址", required = true)
    @NotBlank(message = "备案登记地址不能为空", groups = { AddGroup.class, EditGroup.class })
    private String fillAddress;

    *//**
     * 是否签订治安安全责任书
     *//*
    @ApiModelProperty(value = "是否签订治安安全责任书", required = true)
    @NotBlank(message = "是否签订治安安全责任书不能为空", groups = { AddGroup.class, EditGroup.class })
    private String hasSignSafeContract;

    *//**
     * 可供出租房间数
     *//*
    @ApiModelProperty(value = "可供出租房间数", required = true)
    @NotNull(message = "可供出租房间数不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long availableRentRoomCount;

    *//**
     * 已出租房间数
     *//*
    @ApiModelProperty(value = "已出租房间数", required = true)
    @NotNull(message = "已出租房间数不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long rentedRoomCount;

    *//**
     * 租住人数
     *//*
    @ApiModelProperty(value = "租住人数", required = true)
    @NotNull(message = "租住人数不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long renterCount;

    *//**
     * 最大楼层数
     *//*
    @ApiModelProperty(value = "最大楼层数", required = true)
    @NotNull(message = "最大楼层数不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long rentalBuildingFloorCount;

    *//**
     * 出租层数
     *//*
    @ApiModelProperty(value = "出租层数", required = true)
    @NotNull(message = "出租层数不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long rentedFloorIdx;

    *//**
     * 建筑结构
     *//*
    @ApiModelProperty(value = "建筑结构", required = true)
    @NotBlank(message = "建筑结构不能为空", groups = { AddGroup.class, EditGroup.class })
    private String buildingStruct;

    *//**
     * 房东姓名
     *//*
    @ApiModelProperty(value = "房东姓名", required = true)
    @NotBlank(message = "房东姓名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String buildingOwnerName;

    *//**
     * 房东手机号
     *//*
    @ApiModelProperty(value = "房东手机号", required = true)
    @NotBlank(message = "房东手机号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String buildingOwnerTel;

    *//**
     * 是否有二房东
     *//*
    @ApiModelProperty(value = "是否有二房东", required = true)
    @NotBlank(message = "是否有二房东不能为空", groups = { AddGroup.class, EditGroup.class })
    private String hasManager;

    *//**
     * 二手房东姓名
     *//*
    @ApiModelProperty(value = "二手房东姓名", required = true)
    @NotBlank(message = "二手房东姓名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String secondHandManager;

    *//**
     * 二手房东手机号
     *//*
    @ApiModelProperty(value = "二手房东手机号", required = true)
    @NotBlank(message = "二手房东手机号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String secondHandTel;

    *//**
     * 管理员姓名
     *//*
    @ApiModelProperty(value = "管理员姓名", required = true)
    @NotBlank(message = "管理员姓名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String managerName;

    *//**
     * 管理员手机号
     *//*
    @ApiModelProperty(value = "管理员手机号", required = true)
    @NotBlank(message = "管理员手机号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String managerTel;

    *//**
     * 备注信息
     *//*
    @ApiModelProperty(value = "备注信息", required = true)
    @NotBlank(message = "备注信息不能为空", groups = { AddGroup.class, EditGroup.class })
    private String mark;

    *//**
     * 作业标签
     *//*
    @ApiModelProperty(value = "作业标签", required = true)
    @NotBlank(message = "作业标签不能为空", groups = { AddGroup.class, EditGroup.class })
    private String workingTags;

    *//**
     * 辅助标签
     *//*
    @ApiModelProperty(value = "辅助标签", required = true)
    @NotBlank(message = "辅助标签不能为空", groups = { AddGroup.class, EditGroup.class })
    private String supplyTags;

    */
    /**
     * $column.columnComment
     *//*
    @ApiModelProperty(value = "$column.columnComment", required = true)
    @NotBlank(message = "$column.columnComment不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;*/

    @ApiModelProperty("关键字")
    private String keyWord;
}
