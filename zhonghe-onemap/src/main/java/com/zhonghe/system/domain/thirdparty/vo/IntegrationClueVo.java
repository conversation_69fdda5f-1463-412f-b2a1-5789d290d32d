package com.zhonghe.system.domain.thirdparty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @description:
 * @author: cq
 * @date: 2023/11/7 10:28
 */

@Data
@ApiModel("案事件线索")
public class IntegrationClueVo {
    @ApiModelProperty("案事件id")
    private String dangerId;

    @ApiModelProperty("主体id")
    private String subjectId;

    @ApiModelProperty("主体名称")
    private String subjectName;

    @ApiModelProperty("建筑物地址")
    private String buildAddress;

    @ApiModelProperty("经度")
    private String lng;

    @ApiModelProperty("纬度")
    private String lat;

    @ApiModelProperty("隐患描述")
    private String dangerDescribe;

    @ApiModelProperty("处置部门编码")
    private String disposalDeptNo;

    @ApiModelProperty("处置部门名称")
    private String disposalDeptName;

    @ApiModelProperty("处置期限")
    private Integer disPeriod;

    @ApiModelProperty("处理人姓名")
    private String createName;

    @ApiModelProperty("语音路径")
    private String soundUrl;

    @ApiModelProperty("图片路径")
    private String imageUrl;

    @ApiModelProperty("处置截止日期")
    private Date verTerm;

    @ApiModelProperty("生成时间")
    private Date createDate;

    @ApiModelProperty("代理主键")
    private String dsesUuid;

    @ApiModelProperty("新增时间")
    private Date addTime;

    @ApiModelProperty("增量标识")
    private String cdOperation;

    @ApiModelProperty("增量时间")
    private Date cdTime;

    @ApiModelProperty("批次号")
    private String cdBatch;

    @ApiModelProperty("非业务主键")
    private Long id;

    @ApiModelProperty("运营方技术字段,非业务字段,请忽略并勿使用")
    private Date dmpShareCdTimeIdatat;

    @ApiModelProperty("用数库自动配置的入库时间技术字段,非业务字段,请勿挂接")
    private Date dataSharingCdTimeIdatat;
}

