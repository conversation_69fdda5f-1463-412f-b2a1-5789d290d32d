package com.zhonghe.system.domain.thirdparty;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * @description:
 * @author: cq
 * @date: 2023/11/2 16:47
 */

@Data
@TableName("public.t_task_order_new")
public class TaskOrderNew {

    @TableId
    private String zjid;

    private String orderName;

    private String subjectName;

    private String buildAddress;

    private String itemClassName;

    private String labelName;

    private Date updateDate;

    private Date createDate;

    private String deptName;

    private String checkState;

    private Date checkEndDate;


    public String formatCheckState() {
        switch (this.checkState) {
            case "0":
                return "带巡查";
            case "1":
                return "待复查";
            case "2":
                return "待社区审核";
            case "3":
                return "已完成";
            case "4":
                return "已超期";
            case "5":
                return "已失效";
            default:
                return "未知";
        }
    }
}

