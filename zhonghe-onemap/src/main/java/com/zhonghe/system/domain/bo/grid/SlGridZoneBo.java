package com.zhonghe.system.domain.bo.grid;

import com.zhonghe.common.core.domain.BaseEntity;
import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


/**
 * 网格信息业务对象 sl_grid_zone
 *
 * <AUTHOR>
 * @date 2023-08-02
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("网格信息业务对象")
public class SlGridZoneBo extends BaseEntity {

    /**
     * $column.columnComment
     */
    @ApiModelProperty(value = "$column.columnComment", required = true)
    @NotNull(message = "$column.columnComment不能为空", groups = { EditGroup.class })
    private Long gridZoneId;

    /**
     * 所属区域名称
     */
    @ApiModelProperty(value = "所属区域名称", required = true)
    @NotBlank(message = "所属区域名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String gridGroupName;

    /**
     * 网格名称
     */
    @ApiModelProperty(value = "网格名称", required = true)
    @NotBlank(message = "网格名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String gridZoneName;

    /**
     * $column.columnComment
     */
    @ApiModelProperty(value = "$column.columnComment", required = true)
    @NotBlank(message = "$column.columnComment不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;


}
