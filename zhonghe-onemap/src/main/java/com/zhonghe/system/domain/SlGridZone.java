package com.zhonghe.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zhonghe.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 网格信息对象 sl_grid_zone
 *
 * <AUTHOR>
 * @date 2023-08-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("business.sl_grid_zone")
public class SlGridZone extends BaseEntity {

    private static final long serialVersionUID = 1192133047284406849L;

    /**
     * $column.columnComment
     */
    @TableId(value = "grid_zone_id")
    private Long gridZoneId;
    /**
     * 所属区域名称
     */
    private String gridGroupName;
    /**
     * 网格名称
     */
    private String gridZoneName;
    /**
     * $column.columnComment
     */
    @TableLogic
    private String delFlag;
    /**
     * $column.columnComment
     */
    private String status;

}
