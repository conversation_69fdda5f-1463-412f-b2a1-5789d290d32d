package com.zhonghe.system.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zhonghe.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 重点优抚对象视图对象 sl_important_population
 *
 * <AUTHOR>
 * @date 2023-09-01
 */
@Data
@ApiModel("重点优抚对象视图对象")
@ExcelIgnoreUnannotated
public class SlImportantPopulationDto extends BaseEntity {

    /**
     * 行政区划
     */
    @ExcelProperty(value = "行政区划")
    @ApiModelProperty("行政区划")
    private String gridName;

    /**
     * 姓名
     */
    @ExcelProperty(value = "姓名")
    @ApiModelProperty("姓名")
    private String name;

    /**
     * 性别
     */
    @ExcelProperty(value = "性别")
    @ApiModelProperty("性别")
    private String sex;

    /**
     * 出生日期
     */
    @ExcelProperty(value = "出生日期")
    @ApiModelProperty("出生日期")
    private String brithDate;

    /**
     * 身份证号
     */
    @ExcelProperty(value = "身份证号")
    @ApiModelProperty("身份证号")
    private String idCode;

    /**
     * 户籍地
     */
    @ExcelProperty(value = "户籍地")
    @ApiModelProperty("户籍地")
    private String baseLocation;

    /**
     * 人员类别
     */
    @ExcelProperty(value = "人员类别")
    @ApiModelProperty("人员类别")
    private String populationType;

    /**
     * 家庭住址
     */
    @ExcelProperty(value = "家庭住址")
    @ApiModelProperty("家庭住址")
    private String familyLocation;

    /**
     * 手机号码
     */
    @ExcelProperty(value = "手机号码")
    @ApiModelProperty("手机号码")
    private String phone;

    /**
     * 二标地址
     */
    @ExcelProperty(value = "二标地址")
    @ApiModelProperty("二标地址")
    private String standardLocation;

    /**
     * 居住地址
     */
    @ExcelProperty(value = "居住地址")
    @ApiModelProperty("居住地址")
    private String livingLocation;

    /**
     * 工作地址
     */
    @ExcelProperty(value = "工作地址")
    @ApiModelProperty("工作地址")
    private String workingLocation;


}
