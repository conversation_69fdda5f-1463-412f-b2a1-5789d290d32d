package com.zhonghe.system.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zhonghe.common.annotation.ExcelDictFormat;
import com.zhonghe.common.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 社会工作者一网共享数据视图对象 sl_social_worker_onenet
 *
 * <AUTHOR>
 * @date 2023-08-09
 */
@Data
@ApiModel("社会工作者一网共享数据视图对象")
@ExcelIgnoreUnannotated
public class SlSocialWorkerOnenetVo {

    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    @ExcelProperty(value = "${comment}", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "$column.readConverterExp()")
    @ApiModelProperty("$column.columnComment")
    private Long onenetWorkerId;

    /**
     * 社工表主键
     */
    @ExcelProperty(value = "社工表主键")
    @ApiModelProperty("社工表主键")
    private Long socialWorkerId;

    /**
     * 社工二标工作地址
     */
    @ExcelProperty(value = "社工二标工作地址")
    @ApiModelProperty("社工二标工作地址")
    private String onenetWorkLocation;

    /**
     * 电话
     */
    @ExcelProperty(value = "电话")
    @ApiModelProperty("电话")
    private String onenetTelephone;

    /**
     * 性别
     */
    @ExcelProperty(value = "性别")
    @ApiModelProperty("性别")
    private String onenetSex;

    /**
     * 证件号
     */
    @ExcelProperty(value = "证件号")
    @ApiModelProperty("证件号")
    private String onenetIdNo;

    /**
     * 证件号类型
     */
    @ExcelProperty(value = "证件号类型")
    @ApiModelProperty("证件号类型")
    private String onenetIdType;

    /**
     * 数据json
     */
    @ExcelProperty(value = "数据json")
    @ApiModelProperty("数据json")
    private String onenetJson;

    /**
     * $column.columnComment
     */
    @ExcelProperty(value = "${comment}", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "$column.readConverterExp()")
    @ApiModelProperty("$column.columnComment")
    private String status;


    private String onenetName;
}
