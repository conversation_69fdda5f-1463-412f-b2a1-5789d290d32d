package com.zhonghe.system.domain.thirdparty;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;


/**
 * 【请填写功能名称】对象 sl_country_subsistence_allowance_info
 *
 * <AUTHOR>
 * @date 2023-08-18
 */
@Data
@TableName("business.sl_country_subsistence_allowance_info")
public class SlCountrySubsistenceAllowanceInfo {

    private static final long serialVersionUID= 1269720650350434254L;

    /**
     * $column.columnComment
     */
    private String dxid;
    /**
     * $column.columnComment
     */
    private String cysr;
    /**
     * $column.columnComment
     */
    private String ryzt;
    /**
     * $column.columnComment
     */
    private String xyzk;
    /**
     * $column.columnComment
     */
    private String cjdj;
    /**
     * $column.columnComment
     */
    private String cjlb;
    /**
     * $column.columnComment
     */
    private String xb;
    /**
     * $column.columnComment
     */
    private String xm;
    /**
     * $column.columnComment
     */
    private String cysfzhm;
    /**
     * $column.columnComment
     */
    private String jzdz;
    /**
     * $column.columnComment
     */
    private String lxfs;
    /**
     * $column.columnComment
     */
    private String csrq;
    /**
     * $column.columnComment
     */
    private String jrsr;
    /**
     * $column.columnComment
     */
    private String jzje;
    /**
     * $column.columnComment
     */
    private String hjdz;
    /**
     * $column.columnComment
     */
    private String jzksny;
    /**
     * $column.columnComment
     */
    private String gxsj;
    /**
     * $column.columnComment
     */
    private String sqxzqhdm;
    /**
     * $column.columnComment
     */
    private String hzsfzhm;
    /**
     * $column.columnComment
     */
    private String spbm;
    /**
     * $column.columnComment
     */
    private String jtbm;
    /**
     * $column.columnComment
     */
    private String rybm;
    /**
     * $column.columnComment
     */
    private String jzjzny;
    /**
     * $column.columnComment
     */
    private String jtgx;
    /**
     * $column.columnComment
     */
    private String jzywlx;
    /**
     * $column.columnComment
     */
    private String hjxz;
    /**
     * $column.columnComment
     */
    private String etlgxsj;

}
