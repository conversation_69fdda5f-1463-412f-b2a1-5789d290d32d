package com.zhonghe.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zhonghe.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;


/**
 * 志愿者活动对象 sl_volunteer_actions
 *
 * <AUTHOR>
 * @date 2023-08-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("business.sl_volunteer_actions")
public class SlVolunteerActions extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * $column.columnComment
     */
    @TableId(value = "action_id")
    private Long actionId;
    /**
     * 活动编号
     */
    private String actionNumber;
    /**
     * 活动名称
     */
    private String actionName;
    /**
     * 联系人
     */
    private String actionContract;
    /**
     * 联系电话
     */
    private String actionContractPhone;
    /**
     * 发布组织
     */
    private String actionPublishOrg;
    /**
     * 发布时间
     */
    private Date actionPublishDate;
    /**
     * 补录状态
     */
    private String actionAdditionalStatus;
    /**
     * 活动状态
     */
    private String actionStatus;
    /**
     * 活动类型
     */
    private String actionType;
    /**
     * 活动标签
     */
    private String actionTag;
    /**
     * 活动时间-开始
     */
    private Date actionStartTime;
    /**
     * 活动时间-截止
     */
    private Date actionEndTime;
    /**
     * 已录用
     */
    private String actionEmploy;
    /**
     * $column.columnComment
     */
    @TableLogic
    private String delFlag;
    /**
     * $column.columnComment
     */
    private String status;

}
