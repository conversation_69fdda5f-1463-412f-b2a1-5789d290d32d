package com.zhonghe.system.domain.bo.grid;

import com.zhonghe.common.core.domain.BaseEntity;
import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


/**
 * 网格职位架构业务对象 sl_grid_structure
 *
 * <AUTHOR>
 * @date 2023-08-02
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("网格职位架构业务对象")
public class SlGridStructureBo extends BaseEntity {

    /**
     * $column.columnComment
     */
    @ApiModelProperty(value = "$column.columnComment", required = true)
    @NotNull(message = "$column.columnComment不能为空", groups = { EditGroup.class })
    private Long gridStructureId;

    /**
     * 父节点名称
     */
    @ApiModelProperty(value = "父节点名称", required = true)
    @NotBlank(message = "父节点名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String gridStructureParentName;

    /**
     * 节点名称
     */
    @ApiModelProperty(value = "节点名称", required = true)
    @NotBlank(message = "节点名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String girdStructureName;

    /**
     * 父节点ID
     */
    @ApiModelProperty(value = "父节点ID", required = true)
    @NotNull(message = "父节点ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long gridStructureParentId;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述", required = true)
    @NotBlank(message = "描述不能为空", groups = { AddGroup.class, EditGroup.class })
    private String mark;

    /**
     * $column.columnComment
     */
    @ApiModelProperty(value = "$column.columnComment", required = true)
    @NotBlank(message = "$column.columnComment不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;


}
