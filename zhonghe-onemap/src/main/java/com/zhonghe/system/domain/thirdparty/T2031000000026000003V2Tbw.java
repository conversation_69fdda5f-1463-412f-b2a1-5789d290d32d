package com.zhonghe.system.domain.thirdparty;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;


/**
 * 【请填写功能名称】对象 t2031000000026_000003_v2_tbw
 *
 * <AUTHOR>
 * @date 2023-09-14
 */
@Data
@TableName("public.t2031000000026_000003_v2_tbw")
public class T2031000000026000003V2Tbw {

    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private String f2031000000026000003023;
    /**
     * $column.columnComment
     */
    private String f2031000000026000003004;
    /**
     * $column.columnComment
     */
    private String f2031000000026000003007;
    /**
     * $column.columnComment
     */
    private String f2031000000026000003010;
    /**
     * $column.columnComment
     */
    private String f2031000000026000003011;
    /**
     * $column.columnComment
     */
    private String f2031000000026000003015;
    /**
     * $column.columnComment
     */
    private String f2031000000026000003017;
    /**
     * $column.columnComment
     */
    private String f2031000000026000003018;
    /**
     * $column.columnComment
     */
    private String f2031000000026000003019;
    /**
     * $column.columnComment
     */
    @TableId(value = "f2031000000026_000003020")
    private String f2031000000026000003020;
    /**
     * $column.columnComment
     */
    private String f2031000000026000003021;
    /**
     * $column.columnComment
     */
    private String f2031000000026000003022;
    /**
     * $column.columnComment
     */
    private String f2031000000026000003024;
    /**
     * $column.columnComment
     */
    private String f2031000000026000003025;
    /**
     * $column.columnComment
     */
    private String f2031000000026000003026;
    /**
     * $column.columnComment
     */
    private String f2031000000026000003027;
    /**
     * $column.columnComment
     */
    private String f2031000000026000003028;
    /**
     * $column.columnComment
     */
    private String f2031000000026000003029;
    /**
     * $column.columnComment
     */
    private String f2031000000026000003030;
    /**
     * $column.columnComment
     */
    private String f2031000000026000003031;
    /**
     * $column.columnComment
     */
    private String f2031000000026000003032;
    /**
     * $column.columnComment
     */
    private String f2031000000026000003033;
    /**
     * $column.columnComment
     */
    private String cdTime;
    /**
     * $column.columnComment
     */
    private String f2031000000026000003034;
    /**
     * $column.columnComment
     */
    private String f2031000000026000003001;
    /**
     * $column.columnComment
     */
    private String f2031000000026000003002;
    /**
     * $column.columnComment
     */
    private String f2031000000026000003003;
    /**
     * $column.columnComment
     */
    private String etlUpdateTime;
    /**
     * $column.columnComment
     */
    private String dmpShareCdTimeIdatat;
    /**
     * $column.columnComment
     */
    private String dataSharingCdTimeIdatat;

}
