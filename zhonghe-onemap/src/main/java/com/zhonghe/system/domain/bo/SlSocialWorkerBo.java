package com.zhonghe.system.domain.bo;

import com.zhonghe.common.core.domain.BaseEntity;
import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


/**
 * 石龙社工业务对象 sl_social_worker
 *
 * <AUTHOR>
 * @date 2023-08-03
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("石龙社工业务对象")
public class SlSocialWorkerBo extends BaseEntity {

    /**
     * $column.columnComment
     */
    @ApiModelProperty(value = "$column.columnComment", required = true)
    @NotNull(message = "$column.columnComment不能为空", groups = { EditGroup.class })
    private Long socialWorkerId;

    /**
     * 社工名称
     */
    @ApiModelProperty(value = "社工名称", required = true)
    @NotBlank(message = "社工名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String workerName;

    /**
     * 社工年龄
     */
    @ApiModelProperty(value = "社工年龄", required = true)
    @NotNull(message = "社工年龄不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long workerAge;

    /**
     * 社工性别
     */
    @ApiModelProperty(value = "社工性别", required = true)
    @NotBlank(message = "社工性别不能为空", groups = { AddGroup.class, EditGroup.class })
    private String sex;

    /**
     * 社工身份证号
     */
    @ApiModelProperty(value = "社工身份证号", required = true)
    @NotBlank(message = "社工身份证号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String workerIdNumber;

    /**
     * 社工手机
     */
    @ApiModelProperty(value = "社工手机", required = true)
    @NotBlank(message = "社工手机不能为空", groups = { AddGroup.class, EditGroup.class })
    private String workerPhone;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注", required = true)
    @NotBlank(message = "备注不能为空", groups = { AddGroup.class, EditGroup.class })
    private String mark;

    /**
     * $column.columnComment
     */
    @ApiModelProperty(value = "$column.columnComment", required = true)
    @NotBlank(message = "$column.columnComment不能为空", groups = { AddGroup.class, EditGroup.class })
    private String workPosition;

    /**
     * $column.columnComment
     */
    @ApiModelProperty(value = "$column.columnComment", required = true)
    @NotBlank(message = "$column.columnComment不能为空", groups = { AddGroup.class, EditGroup.class })
    private String standardPosition;

    /**
     * $column.columnComment
     */
    @ApiModelProperty(value = "$column.columnComment", required = true)
    @NotBlank(message = "$column.columnComment不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;


}
