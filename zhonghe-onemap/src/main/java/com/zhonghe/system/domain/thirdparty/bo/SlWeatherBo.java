package com.zhonghe.system.domain.thirdparty.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description:
 * @author: cq
 * @date: 2023/8/21 16:47
 */

@Data
@ApiModel("天气查询")
public class SlWeatherBo {


    @ApiModelProperty("中间时间点")
    private String time;

    @ApiModelProperty("时间查询范围")
    private String timeRange;

    @ApiModelProperty("具体站点")
    private String stationName;

    @ApiModelProperty("具体时间点,可为空")
    private String queryTime;
}

