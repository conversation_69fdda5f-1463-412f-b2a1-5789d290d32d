package com.zhonghe.system.domain.thirdparty;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;


/**
 * 【请填写功能名称】对象 dgsscztxx
 *
 * <AUTHOR>
 * @date 2023-08-14
 */
@Data
@TableName("business.dgsscztxx")
public class Dgsscztxx {

    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private String xyml;
    /**
     * $column.columnComment
     */
    private String dlzj;
    /**
     * $column.columnComment
     */
    private String scbz;
    /**
     * $column.columnComment
     */
    private String etlrq;
    /**
     * $column.columnComment
     */
    private String cyrs;
    /**
     * $column.columnComment
     */
    @TableId(value = "zj")
    private Long zj;
    /**
     * $column.columnComment
     */
    private String fddbrxm;
    /**
     * $column.columnComment
     */
    private String xydm;
    /**
     * $column.columnComment
     */
    private Date jyzzqxz;
    /**
     * $column.columnComment
     */
    private String scztmc;
    /**
     * $column.columnComment
     */
    private String cs;
    /**
     * $column.columnComment
     */
    private String jssj;
    /**
     * $column.columnComment
     */
    private String kssj;
    /**
     * $column.columnComment
     */
    private String scztlx;
    /**
     * $column.columnComment
     */
    private String zsdzbm;
    /**
     * $column.columnComment
     */
    private String tyshxydm;
    /**
     * $column.columnComment
     */
    private String ztsfdm;
    /**
     * $column.columnComment
     */
    private String gb;
    /**
     * $column.columnComment
     */
    private Date hzrq;
    /**
     * $column.columnComment
     */
    private String djgxjg;
    /**
     * $column.columnComment
     */
    private String zczb;
    /**
     * $column.columnComment
     */
    private String jyzt;
    /**
     * $column.columnComment
     */
    private String gxsj;
    /**
     * $column.columnComment
     */
    private String jyfw;
    /**
     * $column.columnComment
     */
    private Date jyzzqxz1;
    /**
     * $column.columnComment
     */
    private String zch;
    /**
     * $column.columnComment
     */
    private Date clrq;


}
