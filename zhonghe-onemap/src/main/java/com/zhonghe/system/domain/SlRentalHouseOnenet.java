package com.zhonghe.system.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zhonghe.common.core.domain.BaseEntity;
import com.zhonghe.system.mappedtypes.GeometryTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.postgis.jdbc.geometry.Geometry;

import java.util.Date;


/**
 * 出租屋图形对象 sl_rental_house_onenet
 *
 * <AUTHOR>
 * @date 2023-08-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("business.sl_rental_house_onenet")
public class SlRentalHouseOnenet extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    @TableId(value = "rental_house_onenet_id")
    private Long rentalHouseOnenetId;
    /**
     * $column.columnComment
     */
    private Long rentalHouseId;
    /**
     * 智网系统编号
     */
    private String smartOnenetCode;
    /**
     * $column.columnComment
     */
    @TableField(typeHandler = GeometryTypeHandler.class)
    private Geometry rentalHouseGem;
    /**
     * $column.columnComment
     */
    private Long fid;
    /**
     * 请求结果
     */
    private String onenetJson;
    /**
     * $column.columnComment
     */
    @TableLogic
    private String delFlag;
    /**
     * $column.columnComment
     */
    private String status;

    private String gridZone;
    /**
     * 所属建筑地址
     */
    private String buildingAddress;
    /**
     * 房屋类型
     */
    private String buildingType;
    /**
     * 更新时间
     */
    private Date updatedTime;
    /**
     * 创建时间
     */
    private Date createdTime;
    /**
     * 更新人
     */
    private String updater;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 出租屋类别
     */
    private String rentalType;
    /**
     * 出租屋分级
     */
    private String rentalLv;
    /**
     * 招牌名称
     */
    private String signName;
    /**
     * 所属建筑
     */
    private String belongToBuilding;
    /**
     * 主体地址
     */
    private String mainSubjectAddress;
    /**
     * 有无备案登记号
     */
    private String hasFillCode;
    /**
     * 出租屋备案号
     */
    private String rentHouseFillCode;
    /**
     * 核查实际地址是否与登记地址一致
     */
    private String hasCheckedAddress;
    /**
     * 备案登记地址
     */
    private String fillAddress;
    /**
     * 是否签订治安安全责任书
     */
    private String hasSignSafeContract;
    /**
     * 可供出租房间数
     */
    private Long availableRentRoomCount;
    /**
     * 已出租房间数
     */
    private Long rentedRoomCount;
    /**
     * 租住人数
     */
    private Long renterCount;
    /**
     * 最大楼层数
     */
    private Long rentalBuildingFloorCount;
    /**
     * 出租层数
     */
    private Long rentedFloorIdx;
    /**
     * 建筑结构
     */
    private String buildingStruct;
    /**
     * 房东姓名
     */
    private String buildingOwnerName;
    /**
     * 房东手机号
     */
    private String buildingOwnerTel;
    /**
     * 是否有二房东
     */
    private String hasManager;
    /**
     * 二手房东姓名
     */
    private String secondHandManager;
    /**
     * 二手房东手机号
     */
    private String secondHandTel;
    /**
     * 管理员姓名
     */
    private String managerName;
    /**
     * 管理员手机号
     */
    private String managerTel;
    /**
     * 备注信息
     */
    private String mark;
    /**
     * 作业标签
     */
    private String workingTags;
    /**
     * 辅助标签
     */
    private String supplyTags;

}
