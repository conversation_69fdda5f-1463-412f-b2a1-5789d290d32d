package com.zhonghe.system.domain.thirdparty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description:
 * @author: cq
 * @date: 2023/8/21 16:38
 */

@Data
@ApiModel("时间范围站点统计")
public class SlWeatherStatisticVo {


    @ApiModelProperty("站点名称")
    private String stationName;

    @ApiModelProperty("站点地址")
    private String address;

    @ApiModelProperty("下雨总量")

    private Integer rainCount;
    @ApiModelProperty("最低温度")

    private Double lowestTemp;
    @ApiModelProperty("最高温度")

    private Double highestTemp;


    @ApiModelProperty("经度")
    private double lng;

    @ApiModelProperty("纬度")
    private double lat;
}

