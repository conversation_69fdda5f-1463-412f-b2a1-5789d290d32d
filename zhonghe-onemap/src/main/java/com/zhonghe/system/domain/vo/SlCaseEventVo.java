package com.zhonghe.system.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zhonghe.common.annotation.ExcelDictFormat;
import com.zhonghe.common.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


/**
 * 案时间视图对象 sl_case_event
 *
 * <AUTHOR>
 * @date 2023-08-14
 */
@Data
@ApiModel("案时间视图对象")
@ExcelIgnoreUnannotated
public class SlCaseEventVo {

    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    @ExcelProperty(value = "${comment}", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "$column.readConverterExp()")
    @ApiModelProperty("$column.columnComment")
    private Long caseEventId;

    /**
     * 案事件编号
     */
    @ExcelProperty(value = "案事件编号")
    @ApiModelProperty("案事件编号")
    private String caseEventCode;

    /**
     * 案事件来源
     */
    @ExcelProperty(value = "案事件来源")
    @ApiModelProperty("案事件来源")
    private String caseEventSource;

    /**
     * 镇街
     */
    @ExcelProperty(value = "镇街")
    @ApiModelProperty("镇街")
    private String caseEventStreet;

    /**
     * 社区
     */
    @ExcelProperty(value = "社区")
    @ApiModelProperty("社区")
    private String caseEventZone;

    /**
     * 网格
     */
    @ExcelProperty(value = "网格")
    @ApiModelProperty("网格")
    private String caseEventGrid;

    /**
     * 所属部门
     */
    @ExcelProperty(value = "所属部门")
    @ApiModelProperty("所属部门")
    private String caseEventDepartment;

    /**
     * 隐患描述
     */
    @ExcelProperty(value = "隐患描述")
    @ApiModelProperty("隐患描述")
    private String caseEventReason;

    /**
     * 主体名称
     */
    @ExcelProperty(value = "主体名称")
    @ApiModelProperty("主体名称")
    private String caseEventSubject;

    /**
     * 地址
     */
    @ExcelProperty(value = "地址")
    @ApiModelProperty("地址")
    private String caseEventAddress;

    /**
     * 生成时间
     */
    @ExcelProperty(value = "生成时间")
    @ApiModelProperty("生成时间")
    private Date caseEventCreateTime;

    /**
     * 处理期限
     */
    @ExcelProperty(value = "处理期限")
    @ApiModelProperty("处理期限")
    private Date caseEventEndDate;

    /**
     * 处置部门
     */
    @ExcelProperty(value = "处置部门")
    @ApiModelProperty("处置部门")
    private String caseEventHandlerOrg;

    /**
     * 状态
     */
    @ExcelProperty(value = "状态")
    @ApiModelProperty("状态")
    private String caseEventStatus;

    /**
     * 描述
     */
    @ExcelProperty(value = "描述")
    @ApiModelProperty("描述")
    private String caseEventMark;

    /**
     * 采集字段信息
     */
    @ExcelProperty(value = "采集字段信息")
    @ApiModelProperty("采集字段信息")
    private String caseEventGatherSource;

    /**
     * $column.columnComment
     */
    @ExcelProperty(value = "${comment}", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "$column.readConverterExp()")
    @ApiModelProperty("$column.columnComment")
    private String status;


}
