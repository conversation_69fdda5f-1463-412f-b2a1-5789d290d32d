package com.zhonghe.system.domain.bo;

import com.zhonghe.common.core.domain.BaseEntity;
import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


/**
 * 燃气共享数据坐标业务对象 sl_gas_onenet
 *
 * <AUTHOR>
 * @date 2023-08-10
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("燃气共享数据坐标业务对象")
public class SlGasOnenetBo extends BaseEntity {

    /**
     * $column.columnComment
     */
    @ApiModelProperty(value = "$column.columnComment", required = true)
    @NotNull(message = "$column.columnComment不能为空", groups = { EditGroup.class })
    private Long onenetGasId;

    /**
     * 燃气表主键
     */
    @ApiModelProperty(value = "燃气表主键", required = true)
    @NotNull(message = "燃气表主键不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long gasId;

    /**
     * gas表名称
     */
    @ApiModelProperty(value = "gas表名称", required = true)
    @NotBlank(message = "gas表名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String gasSubjectName;

    /**
     * $column.columnComment
     */
    @ApiModelProperty(value = "$column.columnComment", required = true)
    @NotBlank(message = "$column.columnComment不能为空", groups = { AddGroup.class, EditGroup.class })
    private String gasGem;

    /**
     * $column.columnComment
     */
    @ApiModelProperty(value = "$column.columnComment", required = true)
    @NotNull(message = "$column.columnComment不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long fid;

    /**
     * 一网共享数据
     */
    @ApiModelProperty(value = "一网共享数据", required = true)
    @NotBlank(message = "一网共享数据不能为空", groups = { AddGroup.class, EditGroup.class })
    private String onenetJson;

    /**
     * $column.columnComment
     */
    @ApiModelProperty(value = "$column.columnComment", required = true)
    @NotBlank(message = "$column.columnComment不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;


}
