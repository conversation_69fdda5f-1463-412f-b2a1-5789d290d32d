package com.zhonghe.system.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zhonghe.common.core.domain.BaseEntity;
import com.zhonghe.system.mappedtypes.GeometryTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.postgis.jdbc.geometry.Geometry;


/**
 * 社会工作者一网共享数据对象 sl_social_worker_onenet
 *
 * <AUTHOR>
 * @date 2023-08-09
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("business.sl_social_worker_onenet")
public class SlSocialWorkerOnenet extends BaseEntity {

    private static final long serialVersionUID= -6666431308912464448L;

    /**
     * $column.columnComment
     */
    @TableId(value = "onenet_worker_id")
    private Long onenetWorkerId;
    /**
     * 社工表主键
     */
    private Long socialWorkerId;
    /**
     * 社工二标工作地址
     */
    private String onenetWorkLocation;
    /**
     * 电话
     */
    private String onenetTelephone;
    /**
     * 性别
     */
    private String onenetSex;
    /**
     * 证件号
     */
    private String onenetIdNo;
    /**
     * 证件号类型
     */
    private String onenetIdType;
    /**
     * 数据json
     */
    private String onenetJson;
    /**
     * $column.columnComment
     */
    @TableLogic
    private String delFlag;
    /**
     * $column.columnComment
     */
    private String status;

    private String onenetName;

    @TableField(typeHandler = GeometryTypeHandler.class)
    private Geometry socialWorkerGem;


    /**
     * 社工身份证号
     */
    private String workerIdNumber;
    /**
     * 社工手机
     */
    private String workerPhone;
    /**
     * 备注
     */
    private String mark;
    /**
     * $column.columnComment
     */
    private String workPosition;
    /**
     * $column.columnComment
     */
    private String standardPosition;
}
