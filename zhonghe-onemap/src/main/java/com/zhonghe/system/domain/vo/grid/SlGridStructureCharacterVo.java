package com.zhonghe.system.domain.vo.grid;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zhonghe.common.annotation.ExcelDictFormat;
import com.zhonghe.common.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 网格人员信息视图对象 sl_grid_structure_character
 *
 * <AUTHOR>
 * @date 2023-08-02
 */
@Data
@ApiModel("网格人员信息视图对象")
@ExcelIgnoreUnannotated
public class SlGridStructureCharacterVo {

    private static final long serialVersionUID = 7402713914701806350L;

    /**
     * $column.columnComment
     */
    @ExcelProperty(value = "${comment}", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "$column.readConverterExp()")
    @ApiModelProperty("$column.columnComment")
    private Long gridCharacterId;

    /**
     * 所属区域名称
     */
    @ExcelProperty(value = "所属区域名称")
    @ApiModelProperty("所属区域名称")
    private String characterZoneName;

    /**
     * 姓名
     */
    @ExcelProperty(value = "姓名")
    @ApiModelProperty("姓名")
    private String characterName;

    /**
     * 性别
     */
    @ExcelProperty(value = "性别")
    @ApiModelProperty("性别")
    private String characterSex;

    /**
     * 工作手机
     */
    @ExcelProperty(value = "工作手机")
    @ApiModelProperty("工作手机")
    private String characterWorkPhone;

    /**
     * 家庭手机
     */
    @ExcelProperty(value = "家庭手机")
    @ApiModelProperty("家庭手机")
    private String characterFamilyPhone;

    /**
     * 描述
     */
    @ExcelProperty(value = "描述")
    @ApiModelProperty("描述")
    private String mark;

    /**
     * $column.columnComment
     */
    @ExcelProperty(value = "${comment}", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "$column.readConverterExp()")
    @ApiModelProperty("$column.columnComment")
    private String status;

    @ApiModelProperty("职位")
    private String girdStructureName;

    @ApiModelProperty("网格名称")
    private String gridZoneName;

    @ApiModelProperty("职位父节点")
    private Long gridStructureParentId;

    @ApiModelProperty("职位节点id")
    private Long gridStructureId;

    @ApiModelProperty("服务力量类型")
    private String characterServiceType;
}
