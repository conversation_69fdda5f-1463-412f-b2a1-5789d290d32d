package com.zhonghe.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zhonghe.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 【请填写功能名称】对象 sl_market_enterprise
 *
 * <AUTHOR>
 * @date 2023-08-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("business.sl_market_enterprise")
public class SlMarketEnterprise extends BaseEntity {

    private static final long serialVersionUID= 3632577228060949861L;

    /**
     * $column.columnComment
     */
    @TableId(value = "market_enterprise_id")
    private Long marketEnterpriseId;
    /**
     * $column.columnComment
     */
    private String enterpriseCreditCode;
    /**
     * $column.columnComment
     */
    private String enterpriseName;
    /**
     * $column.columnComment
     */
    private String enterpriseAddress;
    /**
     * $column.columnComment
     */
    private String enterpriseType;
    /**
     * $column.columnComment
     */
    private String createDate;
    /**
     * $column.columnComment
     */
    private String operatingEndDate;
    /**
     * $column.columnComment
     */
    private String operatingScope;
    /**
     * $column.columnComment
     */
    private String legalPerson;
    /**
     * $column.columnComment
     */
    private String idType;
    /**
     * $column.columnComment
     */
    private String idCode;
    /**
     * $column.columnComment
     */
    private String currencyRegister;
    /**
     * $column.columnComment
     */
    @TableLogic
    private String delFlag;
    /**
     * $column.columnComment
     */
    private String status;

}
