package com.zhonghe.system.domain.vo.grid;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zhonghe.common.annotation.ExcelDictFormat;
import com.zhonghe.common.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 网格职位架构视图对象 sl_grid_structure
 *
 * <AUTHOR>
 * @date 2023-08-02
 */
@Data
@ApiModel("网格职位架构视图对象")
@ExcelIgnoreUnannotated
public class SlGridStructureVo {

    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    @ExcelProperty(value = "${comment}", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "$column.readConverterExp()")
    @ApiModelProperty("$column.columnComment")
    private Long gridStructureId;

    /**
     * 父节点名称
     */
    @ExcelProperty(value = "父节点名称")
    @ApiModelProperty("父节点名称")
    private String gridStructureParentName;

    /**
     * 节点名称
     */
    @ExcelProperty(value = "节点名称")
    @ApiModelProperty("节点名称")
    private String girdStructureName;

    /**
     * 父节点ID
     */
    @ExcelProperty(value = "父节点ID")
    @ApiModelProperty("父节点ID")
    private Long gridStructureParentId;

    /**
     * 描述
     */
    @ExcelProperty(value = "描述")
    @ApiModelProperty("描述")
    private String mark;
    @ExcelProperty(value = "片区")
    @ApiModelProperty("片区")
    private String gridZoneName;

    /**
     * $column.columnComment
     */
    @ExcelProperty(value = "${comment}", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "$column.readConverterExp()")
    @ApiModelProperty("$column.columnComment")
    private String status;


}
