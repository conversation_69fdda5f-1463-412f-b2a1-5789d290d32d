package com.zhonghe.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zhonghe.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;


/**
 * 燃气统计对象 sl_gas_restaurant_gather
 *
 * <AUTHOR>
 * @date 2023-07-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("business.sl_gas_restaurant_gather")
public class SlGasRestaurantGather extends BaseEntity {

    private static final long serialVersionUID= 4671748345165742849L;

    /**
     * $column.columnComment
     */
    @TableId(value = "gather_gas_id")
    private Long gatherGasId;
    /**
     * 辖区
     */
    private String sectionName;
    /**
     * 主体名称
     */
    private String subjectName;
    /**
     * 地址
     */
    private String subjectAddress;
    /**
     * 管理人姓名
     */
    private String subjectManager;
    /**
     * 联系电话
     */
    private String subjectPhone;
    /**
     * 网格员姓名
     */
    private String gridManager;
    /**
     * 燃气类型描述：天然气、电、气瓶
     */
    private String powerType;
    /**
     * 燃气类型具体描述
     */
    private String powerTypeMark;
    /**
     * 燃气使用情况
     */
    private String powerStoreMark;
    /**
     * 大气瓶数量
     */
    private Long powerStoreLargeNum;
    /**
     * 小气瓶数量
     */
    private Long powerStoreSmallNum;
    /**
     * 隐患描述、备注
     */
    private String questionMark;
    /**
     * 气瓶隐患
     */
    private String questionGasBottle;
    /**
     * 管道隐患
     */
    private String questionGasPipeline;
    /**
     * 报警器隐患
     */
    private String questionGasWarmingMachine;
    /**
     * 存储隐患
     */
    private String questionGasStoreWeight;
    /**
     * 摆放隐患
     */
    private String questionGasStoreLocation;
    /**
     * 黑气标识
     */
    private String questionGasIllegal;
    /**
     * 是否整改
     */
    private String questionRectification;
    /**
     * 收集时间
     */
    private Date gatherTime;
    /**
     * $column.columnComment
     */
    @TableLogic
    private String delFlag;
    /**
     * $column.columnComment
     */
    private String status;

    private Long powerStoreHugeNum;

    private String questionGasEnvironment;

    private String standardLocation;
}
