package com.zhonghe.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zhonghe.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 网格人员信息对象 sl_grid_structure_character
 *
 * <AUTHOR>
 * @date 2023-08-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("business.sl_grid_structure_character")
public class SlGridStructureCharacter extends BaseEntity {

    private static final long serialVersionUID= 7992051792921467373L;

    /**
     * $column.columnComment
     */
    @TableId(value = "grid_character_id")
    private Long gridCharacterId;
    /**
     * 所属区域名称
     */
    private String characterZoneName;
    /**
     * 姓名
     */
    private String characterName;
    /**
     * 性别
     */
    private String characterSex;
    /**
     * 工作手机
     */
    private String characterWorkPhone;
    /**
     * 家庭手机
     */
    private String characterFamilyPhone;
    /**
     * 描述
     */
    private String mark;
    /**
     * $column.columnComment
     */
    @TableLogic
    private String delFlag;
    /**
     * $column.columnComment
     */
    private String status;

    private String characterServiceType;

}
