package com.zhonghe.system.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zhonghe.common.core.domain.BaseEntity;
import com.zhonghe.system.mappedtypes.GeometryTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.postgis.jdbc.geometry.Geometry;


/**
 * 【请填写功能名称】对象 sl_case_event_onenet
 *
 * <AUTHOR>
 * @date 2023-08-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("business.sl_case_event_onenet")
public class SlCaseEventOnenet extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    @TableId(value = "case_event_onenet_id")
    private Long caseEventOnenetId;
    /**
     * $column.columnComment
     */
    private String caseEventCode;
    /**
     * $column.columnComment
     */
    private String caseEventAddress;
    /**
     * $column.columnComment
     */
    private Long caseEventId;
    /**
     * $column.columnComment
     */
    @TableField(typeHandler = GeometryTypeHandler.class)
    private Geometry caseEventGem;
    /**
     * $column.columnComment
     */
    private Long fid;
    /**
     * $column.columnComment
     */
    private String onenetJson;
    /**
     * $column.columnComment
     */
    @TableLogic
    private String delFlag;
    /**
     * $column.columnComment
     */
    private String status;

    private String asjid;

}
