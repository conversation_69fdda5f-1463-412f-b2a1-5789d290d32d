package com.zhonghe.system.domain.bo.grid;

import com.zhonghe.common.core.domain.BaseEntity;
import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


/**
 * 网格人员信息业务对象 sl_grid_structure_character
 *
 * <AUTHOR>
 * @date 2023-08-02
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("网格人员信息业务对象")
public class SlGridStructureCharacterBo extends BaseEntity {

    /**
     * $column.columnComment
     */
    @ApiModelProperty(value = "$column.columnComment", required = true)
    @NotNull(message = "$column.columnComment不能为空", groups = { EditGroup.class })
    private Long gridCharacterId;

    /**
     * 所属区域名称
     */
    @ApiModelProperty(value = "所属区域名称", required = true)
    @NotBlank(message = "所属区域名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String characterZoneName;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名", required = true)
    @NotBlank(message = "姓名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String characterName;

    /**
     * 性别
     */
    @ApiModelProperty(value = "性别", required = true)
    @NotBlank(message = "性别不能为空", groups = { AddGroup.class, EditGroup.class })
    private String characterSex;

    /**
     * 工作手机
     */
    @ApiModelProperty(value = "工作手机", required = true)
    @NotBlank(message = "工作手机不能为空", groups = { AddGroup.class, EditGroup.class })
    private String characterWorkPhone;

    /**
     * 家庭手机
     */
    @ApiModelProperty(value = "家庭手机", required = true)
    @NotBlank(message = "家庭手机不能为空", groups = { AddGroup.class, EditGroup.class })
    private String characterFamilyPhone;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述", required = true)
    @NotBlank(message = "描述不能为空", groups = { AddGroup.class, EditGroup.class })
    private String mark;

    /**
     * $column.columnComment
     */
    @ApiModelProperty(value = "$column.columnComment", required = true)
    @NotBlank(message = "$column.columnComment不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;

    @ApiModelProperty("职位节点id")
    private Long gridStructureId;

    @ApiModelProperty("关键字模糊查询")
    private String keyWord;

    @ApiModelProperty("服务力量类型，传值：社工、自愿者、志愿者、网格人员")
    private String characterServiceType;
}
