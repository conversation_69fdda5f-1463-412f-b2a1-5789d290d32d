package com.zhonghe.system.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zhonghe.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 老年失能视图对象 sl_old_population
 *
 * <AUTHOR>
 * @date 2023-09-01
 */
@Data
@ApiModel("老年失能视图对象")
@ExcelIgnoreUnannotated
public class SlOldPopulationDto extends BaseEntity {

    /**
     * 社区、村
     */
    @ExcelProperty(value = "社区、村")
    @ApiModelProperty("社区、村")
    private String gridName;

    /**
     * 姓名
     */
    @ExcelProperty(value = "姓名")
    @ApiModelProperty("姓名")
    private String name;

    /**
     * 性别
     */
    @ExcelProperty(value = "性别")
    @ApiModelProperty("性别")
    private String sex;

    /**
     * 身份证号码
     */
    @ExcelProperty(value = "身份证号码")
    @ApiModelProperty("身份证号码")
    private String idCode;

    /**
     * 年龄
     */
    @ExcelProperty(value = "年龄")
    @ApiModelProperty("年龄")
    private Long age;

    /**
     * 户籍地址
     */
    @ExcelProperty(value = "户籍地址")
    @ApiModelProperty("户籍地址")
    private String baseLocation;

    /**
     * 身份属性
     */
    @ExcelProperty(value = "身份属性")
    @ApiModelProperty("身份属性")
    private String populationType;

    /**
     * 是否入住养老机构
     */
    @ExcelProperty(value = "是否入住养老机构")
    @ApiModelProperty("是否入住养老机构")
    private String livingNursing;

    /**
     * 评定失能程度
     */
    @ExcelProperty(value = "评定失能程度")
    @ApiModelProperty("评定失能程度")
    private String disableLv;

    /**
     * 现居地地址
     */
    @ExcelProperty(value = "现居地地址")
    @ApiModelProperty("现居地地址")
    private String livingLocation;

    /**
     * 就业单位地址
     */
    @ExcelProperty(value = "就业单位地址")
    @ApiModelProperty("就业单位地址")
    private String workingLocation;




}
