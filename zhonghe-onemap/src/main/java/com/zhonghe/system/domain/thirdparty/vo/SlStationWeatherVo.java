package com.zhonghe.system.domain.thirdparty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description:
 * @author: cq
 * @date: 2023/8/28 11:09
 */

@Data
@ApiModel("站点天气列表")
public class SlStationWeatherVo {

    @ApiModelProperty("雨量")
    private int rainCount;


    @ApiModelProperty("温度")
    private Double temperature;


    @ApiModelProperty("时间")
    private String time;
}

