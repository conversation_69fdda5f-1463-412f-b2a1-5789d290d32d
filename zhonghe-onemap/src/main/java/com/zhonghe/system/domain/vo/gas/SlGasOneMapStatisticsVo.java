package com.zhonghe.system.domain.vo.gas;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @description:
 * @author: cq
 * @date: 2023/8/9 15:22
 */

@Data
@ApiModel("大屏上餐饮主题统计图")
public class SlGasOneMapStatisticsVo {


    @ApiModelProperty("餐饮主体总数")
    private Integer gasUnitCount;

    @ApiModelProperty("合规餐饮主体")
    private Long gasLegalUnitCount;

    @ApiModelProperty("非燃气用气主体数")
    private Long notGasPowerUnitCount;


    @ApiModelProperty("气瓶类型统计")
    private List<SlGasOneMapGasBottleVo> gasBottles;
}

