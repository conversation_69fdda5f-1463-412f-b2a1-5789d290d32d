package com.zhonghe.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zhonghe.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 老年失能对象 sl_special_population
 *
 * <AUTHOR>
 * @date 2023-09-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("business.sl_special_population")
public class SlSpecialPopulation extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * $column.columnComment
     */
    @TableId(value = "special_id")
    private Long specialId;
    /**
     * 所属村（社区）
     */
    private String gridName;
    /**
     * 所属网格
     */
    private String netName;
    /**
     * 姓名
     */
    private String name;
    /**
     * 性别
     */
    private String sex;
    /**
     * 年龄
     */
    private Long age;
    /**
     * 身份证
     */
    private String idCode;
    /**
     * 联系电话
     */
    private String phone;
    /**
     * 现居住地址
     */
    private String livingLocation;
    /**
     * 居住类别
     */
    private String livingType;
    /**
     * 群体
     */
    private String populationType;
    /**
     * 疫苗接种
     */
    private String vaccinum;
    /**
     * 健康类别
     */
    private String healthType;
    /**
     * 身体状况
     */
    private String bodyType;
    /**
     * 诊疗服药情况（就医频次及药品）
     */
    private String medicine;
    /**
     * 家庭医生（姓名、联系电话）
     */
    private String doctor;
    /**
     * 紧急联系人
     */
    private String helpManager;
    /**
     * 网格跟进工作人员
     */
    private String gridManager;
    /**
     * 备注
     */
    private String mark;
    /**
     * $column.columnComment
     */
    @TableLogic
    private String delFlag;
    /**
     * $column.columnComment
     */
    private String status;

}
