package com.zhonghe.system.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description:
 * @author: cq
 * @date: 2023/8/14 14:48
 */
@Data
@ApiModel("出租屋信息视图统计对象")
@ExcelIgnoreUnannotated
public class SlRentalHouseStatisticsVo {

    @ApiModelProperty("总数")
    private Long count;

    @ApiModelProperty("房间总数")
    private Long roomCount;

    @ApiModelProperty("房东总数")
    private Long ownerCount;

    @ApiModelProperty("已出租房间数")
    private Long rentedRoomCount;

    @ApiModelProperty("租客总数")
    private Long slaverCount;

    @ApiModelProperty("出租率")
    private String rentalPercent;

}

