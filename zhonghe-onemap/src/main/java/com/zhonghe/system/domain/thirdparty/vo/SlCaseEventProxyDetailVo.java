package com.zhonghe.system.domain.thirdparty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @description:
 * @author: cq
 * @date: 2023/8/18 11:15
 */

@Data
@ApiModel("详情对象")
public class SlCaseEventProxyDetailVo {

    @ApiModelProperty("按事件详情")
    private SlCaseEventProxyVo vo;

    @ApiModelProperty("网格长信息")
    private GridManagerInfoVo gridManager;


    @ApiModelProperty("特殊人群列表")
    private List<SpecialistVo> specialist;

    @ApiModelProperty("案事件记录")
    private List<IntegrationClueVo> integrationClueVos;
}

