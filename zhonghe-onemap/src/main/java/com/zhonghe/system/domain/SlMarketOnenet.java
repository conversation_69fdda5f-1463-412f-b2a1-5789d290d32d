package com.zhonghe.system.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zhonghe.common.core.domain.BaseEntity;
import com.zhonghe.system.mappedtypes.GeometryTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.postgis.jdbc.geometry.Geometry;


/**
 * 前置机市场地图对象 sl_market_onenet
 *
 * <AUTHOR>
 * @date 2023-08-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("business.sl_market_onenet")
public class SlMarketOnenet extends BaseEntity {

    private static final long serialVersionUID= -6833681756306587089L;

    /**
     * $column.columnComment
     */
    @TableId(value = "market_id")
    private Long marketId;
    /**
     * $column.columnComment
     */
    private String marketDataId;
    /**
     * $column.columnComment
     */
    @TableField(typeHandler = GeometryTypeHandler.class)
    private Geometry marketGem;
    /**
     * $column.columnComment
     */
    private String marketAddress;
    /**
     * $column.columnComment
     */
    private Long fid;
    /**
     * $column.columnComment
     */
    @TableLogic
    private String delFlag;
    /**
     * $column.columnComment
     */
    private String status;

    private String marketCode;

    private String marketName;

    private String marketManager;

    private String businessScope;

}
