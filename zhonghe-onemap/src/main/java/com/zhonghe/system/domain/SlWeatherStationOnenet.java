package com.zhonghe.system.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zhonghe.common.core.domain.BaseEntity;
import com.zhonghe.system.mappedtypes.GeometryTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.postgis.jdbc.geometry.Geometry;


/**
 * 【请填写功能名称】对象 sl_weather_station_onenet
 *
 * <AUTHOR>
 * @date 2023-08-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("business.sl_weather_station_onenet")
public class SlWeatherStationOnenet extends BaseEntity {

    private static final long serialVersionUID= 8309843099151712134L;

    /**
     * $column.columnComment
     */
    @TableId(value = "station_onenet_id")
    private Long stationOnenetId;
    /**
     * $column.columnComment
     */
    private String stationName;
    /**
     * $column.columnComment
     */
    @TableField(typeHandler = GeometryTypeHandler.class)
    private Geometry stationGem;
    /**
     * $column.columnComment
     */
    private Long fid;
    /**
     * $column.columnComment
     */
    @TableLogic
    private String delFlag;
    /**
     * $column.columnComment
     */
    private String status;

}
