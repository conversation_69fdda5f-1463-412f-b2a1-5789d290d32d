package com.zhonghe.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zhonghe.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 职位与人物关系对象 sl_grid_structure_character_rel
 *
 * <AUTHOR>
 * @date 2023-08-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("business.sl_grid_structure_character_rel")
public class SlGridStructureCharacterRel extends BaseEntity {

    private static final long serialVersionUID= 4273014999683078055L;

    /**
     * $column.columnComment
     */
    @TableId(value = "relation_id")
    private Long relationId;
    /**
     * $column.columnComment
     */
    private Long gridCharacterId;
    /**
     * $column.columnComment
     */
    private Long gridStructureId;
    /**
     * $column.columnComment
     */
    @TableLogic
    private String delFlag;
    /**
     * $column.columnComment
     */
    private String status;

}
