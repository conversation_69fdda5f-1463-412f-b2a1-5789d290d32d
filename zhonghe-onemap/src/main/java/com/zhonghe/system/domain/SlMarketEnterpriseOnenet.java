package com.zhonghe.system.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zhonghe.common.core.domain.BaseEntity;
import com.zhonghe.system.mappedtypes.GeometryTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.postgis.jdbc.geometry.Geometry;


/**
 * 【请填写功能名称】对象 sl_market_enterprise_onenet
 *
 * <AUTHOR>
 * @date 2023-08-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("business.sl_market_enterprise_onenet")
public class SlMarketEnterpriseOnenet extends BaseEntity {

    private static final long serialVersionUID= 4761623730863354638L;

    /**
     * $column.columnComment
     */
    @TableId(value = "market_enterprise_onenet_id")
    private Long marketEnterpriseOnenetId;
    /**
     * $column.columnComment
     */
    private String marketEnterpriseId;
    /**
     * $column.columnComment
     */
    private String enterpriseCreditCode;
    /**
     * $column.columnComment
     */
    private String enterpriseName;
    /**
     * $column.columnComment
     */
    private String enterpriseAddress;
    /**
     * $column.columnComment
     */
    @TableField(typeHandler = GeometryTypeHandler.class)
    private Geometry marketEnterpriseGem;
    /**
     * $column.columnComment
     */
    private Long fid;
    /**
     * $column.columnComment
     */
    private String onenetJson;
    /**
     * $column.columnComment
     */
    @TableLogic
    private String delFlag;
    /**
     * $column.columnComment
     */
    private String status;

    /**
     * $column.columnComment
     */
    private String enterpriseType;
    /**
     * $column.columnComment
     */
    private String createDate;
    /**
     * $column.columnComment
     */
    private String operatingEndDate;
    /**
     * $column.columnComment
     */
    private String operatingScope;
    /**
     * $column.columnComment
     */
    private String legalPerson;
    /**
     * $column.columnComment
     */
    private String idType;
    /**
     * $column.columnComment
     */
    private String idCode;
    /**
     * $column.columnComment
     */
    private String currencyRegister;

}
