package com.zhonghe.system.domain.bo;

import com.zhonghe.common.core.domain.BaseEntity;
import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


/**
 * 志愿者活动业务对象 sl_volunteer_actions
 *
 * <AUTHOR>
 * @date 2023-08-04
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("志愿者活动业务对象")
public class SlVolunteerActionsBo extends BaseEntity {

    /**
     * $column.columnComment
     */
    @ApiModelProperty(value = "$column.columnComment", required = true)
    @NotNull(message = "$column.columnComment不能为空", groups = { EditGroup.class })
    private Long actionId;

    /**
     * 活动编号
     */
    @ApiModelProperty(value = "活动编号", required = true)
    @NotBlank(message = "活动编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String actionNumber;

    /**
     * 活动名称
     */
    @ApiModelProperty(value = "活动名称", required = true)
    @NotBlank(message = "活动名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String actionName;

    /**
     * 联系人
     */
    @ApiModelProperty(value = "联系人", required = true)
    @NotBlank(message = "联系人不能为空", groups = { AddGroup.class, EditGroup.class })
    private String actionContract;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话", required = true)
    @NotBlank(message = "联系电话不能为空", groups = { AddGroup.class, EditGroup.class })
    private String actionContractPhone;

    /**
     * 发布组织
     */
    @ApiModelProperty(value = "发布组织", required = true)
    @NotBlank(message = "发布组织不能为空", groups = { AddGroup.class, EditGroup.class })
    private String actionPublishOrg;

    /**
     * 发布时间
     */
    @ApiModelProperty(value = "发布时间", required = true)
    @NotBlank(message = "发布时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private String actionPublishDate;

    /**
     * 补录状态
     */
    @ApiModelProperty(value = "补录状态", required = true)
    @NotBlank(message = "补录状态不能为空", groups = { AddGroup.class, EditGroup.class })
    private String actionAdditionalStatus;

    /**
     * 活动状态
     */
    @ApiModelProperty(value = "活动状态", required = true)
    @NotBlank(message = "活动状态不能为空", groups = { AddGroup.class, EditGroup.class })
    private String actionStatus;

    /**
     * 活动类型
     */
    @ApiModelProperty(value = "活动类型", required = true)
    @NotBlank(message = "活动类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String actionType;

    /**
     * 活动标签
     */
    @ApiModelProperty(value = "活动标签", required = true)
    @NotBlank(message = "活动标签不能为空", groups = { AddGroup.class, EditGroup.class })
    private String actionTag;

    /**
     * 活动时间-开始
     */
    @ApiModelProperty(value = "活动时间-开始", required = true)
    @NotBlank(message = "活动时间-开始不能为空", groups = { AddGroup.class, EditGroup.class })
    private String actionStartTime;

    /**
     * 活动时间-截止
     */
    @ApiModelProperty(value = "活动时间-截止", required = true)
    @NotBlank(message = "活动时间-截止不能为空", groups = { AddGroup.class, EditGroup.class })
    private String actionEndTime;

    /**
     * 已录用
     */
    @ApiModelProperty(value = "已录用", required = true)
    @NotBlank(message = "已录用不能为空", groups = { AddGroup.class, EditGroup.class })
    private String actionEmploy;

    /**
     * $column.columnComment
     */
    @ApiModelProperty(value = "$column.columnComment", required = true)
    @NotBlank(message = "$column.columnComment不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;


}
