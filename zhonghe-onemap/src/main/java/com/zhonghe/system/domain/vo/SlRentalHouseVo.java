package com.zhonghe.system.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zhonghe.common.annotation.ExcelDictFormat;
import com.zhonghe.common.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


/**
 * 出租屋信息视图对象 sl_rental_house
 *
 * <AUTHOR>
 * @date 2023-08-11
 */
@Data
@ApiModel("出租屋信息视图对象")
@ExcelIgnoreUnannotated
public class SlRentalHouseVo extends BaseThirdPartyVo {

    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    @ExcelProperty(value = "${comment}", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "$column.readConverterExp()")
    @ApiModelProperty("$column.columnComment")
    private Long rentalHouseId;

    /**
     * 所属网格
     */
    @ExcelProperty(value = "所属网格")
    @ApiModelProperty("所属网格")
    private String gridZone;

    /**
     * 所属建筑地址
     */
    @ExcelProperty(value = "所属建筑地址")
    @ApiModelProperty("所属建筑地址")
    private String buildingAddress;

    /**
     * 房屋类型
     */
    @ExcelProperty(value = "房屋类型")
    @ApiModelProperty("房屋类型")
    private String buildingType;

    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间")
    @ApiModelProperty("更新时间")
    private Date updatedTime;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    @ApiModelProperty("创建时间")
    private Date createdTime;

    /**
     * 更新人
     */
    @ExcelProperty(value = "更新人")
    @ApiModelProperty("更新人")
    private String updater;

    /**
     * 创建人
     */
    @ExcelProperty(value = "创建人")
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 出租屋类别
     */
    @ExcelProperty(value = "出租屋类别")
    @ApiModelProperty("出租屋类别")
    private String rentalType;

    /**
     * 出租屋分级
     */
    @ExcelProperty(value = "出租屋分级")
    @ApiModelProperty("出租屋分级")
    private String rentalLv;

    /**
     * 招牌名称
     */
    @ExcelProperty(value = "招牌名称")
    @ApiModelProperty("招牌名称")
    private String signName;

    /**
     * 所属建筑
     */
    @ExcelProperty(value = "所属建筑")
    @ApiModelProperty("所属建筑")
    private String belongToBuilding;

    /**
     * 主体地址
     */
    @ExcelProperty(value = "主体地址")
    @ApiModelProperty("主体地址")
    private String mainSubjectAddress;

    /**
     * 有无备案登记号
     */
    @ExcelProperty(value = "有无备案登记号")
    @ApiModelProperty("有无备案登记号")
    private String hasFillCode;

    /**
     * 出租屋备案号
     */
    @ExcelProperty(value = "出租屋备案号")
    @ApiModelProperty("出租屋备案号")
    private String rentHouseFillCode;

    /**
     * 核查实际地址是否与登记地址一致
     */
    @ExcelProperty(value = "核查实际地址是否与登记地址一致")
    @ApiModelProperty("核查实际地址是否与登记地址一致")
    private String hasCheckedAddress;

    /**
     * 备案登记地址
     */
    @ExcelProperty(value = "备案登记地址")
    @ApiModelProperty("备案登记地址")
    private String fillAddress;

    /**
     * 是否签订治安安全责任书
     */
    @ExcelProperty(value = "是否签订治安安全责任书")
    @ApiModelProperty("是否签订治安安全责任书")
    private String hasSignSafeContract;

    /**
     * 可供出租房间数
     */
    @ExcelProperty(value = "可供出租房间数")
    @ApiModelProperty("可供出租房间数")
    private Long availableRentRoomCount;

    /**
     * 已出租房间数
     */
    @ExcelProperty(value = "已出租房间数")
    @ApiModelProperty("已出租房间数")
    private Long rentedRoomCount;

    /**
     * 租住人数
     */
    @ExcelProperty(value = "租住人数")
    @ApiModelProperty("租住人数")
    private Long renterCount;

    /**
     * 最大楼层数
     */
    @ExcelProperty(value = "最大楼层数")
    @ApiModelProperty("最大楼层数")
    private Long rentalBuildingFloorCount;

    /**
     * 出租层数
     */
    @ExcelProperty(value = "出租层数")
    @ApiModelProperty("出租层数")
    private Long rentedFloorIdx;

    /**
     * 建筑结构
     */
    @ExcelProperty(value = "建筑结构")
    @ApiModelProperty("建筑结构")
    private String buildingStruct;

    /**
     * 房东姓名
     */
    @ExcelProperty(value = "房东姓名")
    @ApiModelProperty("房东姓名")
    private String buildingOwnerName;

    /**
     * 房东手机号
     */
    @ExcelProperty(value = "房东手机号")
    @ApiModelProperty("房东手机号")
    private String buildingOwnerTel;

    /**
     * 是否有二房东
     */
    @ExcelProperty(value = "是否有二房东")
    @ApiModelProperty("是否有二房东")
    private String hasManager;

    /**
     * 二手房东姓名
     */
    @ExcelProperty(value = "二手房东姓名")
    @ApiModelProperty("二手房东姓名")
    private String secondHandManager;

    /**
     * 二手房东手机号
     */
    @ExcelProperty(value = "二手房东手机号")
    @ApiModelProperty("二手房东手机号")
    private String secondHandTel;

    /**
     * 管理员姓名
     */
    @ExcelProperty(value = "管理员姓名")
    @ApiModelProperty("管理员姓名")
    private String managerName;

    /**
     * 管理员手机号
     */
    @ExcelProperty(value = "管理员手机号")
    @ApiModelProperty("管理员手机号")
    private String managerTel;

    /**
     * 备注信息
     */
    @ExcelProperty(value = "备注信息")
    @ApiModelProperty("备注信息")
    private String mark;

    /**
     * 作业标签
     */
    @ExcelProperty(value = "作业标签")
    @ApiModelProperty("作业标签")
    private String workingTags;

    /**
     * 辅助标签
     */
    @ExcelProperty(value = "辅助标签")
    @ApiModelProperty("辅助标签")
    private String supplyTags;

    /**
     * $column.columnComment
     */
    @ExcelProperty(value = "${comment}", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "$column.readConverterExp()")
    @ApiModelProperty("$column.columnComment")
    private String status;


}
