package com.zhonghe.system.domain.bo;

import com.zhonghe.common.core.domain.BaseEntity;
import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


/**
 * 重点优抚对象业务对象 sl_important_population
 *
 * <AUTHOR>
 * @date 2023-09-01
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("重点优抚对象业务对象")
public class SlImportantPopulationBo extends BaseEntity {

    /**
     * $column.columnComment
     */
    @ApiModelProperty(value = "$column.columnComment", required = true)
    @NotNull(message = "$column.columnComment不能为空", groups = { EditGroup.class })
    private Long populationId;

    /**
     * 行政区划
     */
    @ApiModelProperty(value = "行政区划", required = true)
    @NotBlank(message = "行政区划不能为空", groups = { AddGroup.class, EditGroup.class })
    private String gridName;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名", required = true)
    @NotBlank(message = "姓名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;

    /**
     * 性别
     */
    @ApiModelProperty(value = "性别", required = true)
    @NotBlank(message = "性别不能为空", groups = { AddGroup.class, EditGroup.class })
    private String sex;

    /**
     * 出生日期
     */
    @ApiModelProperty(value = "出生日期", required = true)
    @NotBlank(message = "出生日期不能为空", groups = { AddGroup.class, EditGroup.class })
    private String brithDate;

    /**
     * 身份证号
     */
    @ApiModelProperty(value = "身份证号", required = true)
    @NotBlank(message = "身份证号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String idCode;

    /**
     * 户籍地
     */
    @ApiModelProperty(value = "户籍地", required = true)
    @NotBlank(message = "户籍地不能为空", groups = { AddGroup.class, EditGroup.class })
    private String baseLocation;

    /**
     * 人员类别
     */
    @ApiModelProperty(value = "人员类别", required = true)
    @NotBlank(message = "人员类别不能为空", groups = { AddGroup.class, EditGroup.class })
    private String populationType;

    /**
     * 家庭住址
     */
    @ApiModelProperty(value = "家庭住址", required = true)
    @NotBlank(message = "家庭住址不能为空", groups = { AddGroup.class, EditGroup.class })
    private String familyLocation;

    /**
     * 手机号码
     */
    @ApiModelProperty(value = "手机号码", required = true)
    @NotBlank(message = "手机号码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String phone;

    /**
     * 二标地址
     */
    @ApiModelProperty(value = "二标地址", required = true)
    @NotBlank(message = "二标地址不能为空", groups = { AddGroup.class, EditGroup.class })
    private String standardLocation;

    /**
     * 居住地址
     */
    @ApiModelProperty(value = "居住地址", required = true)
    @NotBlank(message = "居住地址不能为空", groups = { AddGroup.class, EditGroup.class })
    private String livingLocation;

    /**
     * 工作地址
     */
    @ApiModelProperty(value = "工作地址", required = true)
    @NotBlank(message = "工作地址不能为空", groups = { AddGroup.class, EditGroup.class })
    private String workingLocation;

    /**
     * $column.columnComment
     */
    @ApiModelProperty(value = "$column.columnComment", required = true)
    @NotBlank(message = "$column.columnComment不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;


}
