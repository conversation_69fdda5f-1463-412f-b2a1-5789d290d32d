package com.zhonghe.system.domain.vo.gas;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * @description:
 * @author: cq
 * @date: 2023/8/9 15:28
 */

@Data
@ApiModel("气瓶类型与气瓶数量")
@AllArgsConstructor
public class SlGasOneMapGasBottleVo {

    @ApiModelProperty("气瓶类型")
    private String name;

    @ApiModelProperty("气瓶数量")
    private long count;
}

