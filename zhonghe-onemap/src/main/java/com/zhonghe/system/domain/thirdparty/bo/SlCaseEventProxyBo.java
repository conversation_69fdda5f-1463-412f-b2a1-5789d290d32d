package com.zhonghe.system.domain.thirdparty.bo;

import com.zhonghe.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 【请填写功能名称】业务对象 sl_case_event_proxy
 *
 * <AUTHOR>
 * @date 2023-08-14
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("案事件业务对象")
public class SlCaseEventProxyBo extends BaseEntity {


    @ApiModelProperty(value = "查询类型，month按月查询，quarter按季度", required = true)
    private String queryType;


    @ApiModelProperty("关键字")
    private String keyWord;
}
