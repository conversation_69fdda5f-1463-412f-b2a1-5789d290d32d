package com.zhonghe.system.domain.thirdparty;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;


/**
 * 【请填写功能名称】对象 t2031000000026_000005_v1
 *
 * <AUTHOR>
 * @date 2023-09-14
 */
@Data
@TableName("public.t2031000000026_000005_v1")
public class T2031000000026000005V1 {

    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private String f2031000000026000005001;
    /**
     * $column.columnComment
     */
    private String f2031000000026000005002;
    /**
     * $column.columnComment
     */
    private String f2031000000026000005004;
    /**
     * $column.columnComment
     */
    private String f2031000000026000005005;
    /**
     * $column.columnComment
     */
    @TableId(value = "f2031000000026_000005008")
    private String f2031000000026000005008;
    /**
     * $column.columnComment
     */
    private String f2031000000026000005009;
    /**
     * $column.columnComment
     */
    private String f2031000000026000005012;
    /**
     * $column.columnComment
     */
    private String f2031000000026000005013;
    /**
     * $column.columnComment
     */
    private String f2031000000026000005017;
    /**
     * $column.columnComment
     */
    private String f2031000000026000005019;
    /**
     * $column.columnComment
     */
    private String f2031000000026000005020;
    /**
     * $column.columnComment
     */
    private String f2031000000026000005021;
    /**
     * $column.columnComment
     */
    private String f2031000000026000005022;
    /**
     * $column.columnComment
     */
    private String f2031000000026000005023;
    /**
     * $column.columnComment
     */
    private Long f2031000000026000005024;
    /**
     * $column.columnComment
     */
    private Long f2031000000026000005025;
    /**
     * $column.columnComment
     */
    private String f2031000000026000005026;
    /**
     * $column.columnComment
     */
    private String f2031000000026000005027;
    /**
     * $column.columnComment
     */
    private String f2031000000026000005028;
    /**
     * $column.columnComment
     */
    private String f2031000000026000005029;
    /**
     * $column.columnComment
     */
    private String f2031000000026000005030;
    /**
     * $column.columnComment
     */
    private String f2031000000026000005031;
    /**
     * $column.columnComment
     */
    private String f2031000000026000005032;
    /**
     * $column.columnComment
     */
    private String f2031000000026000005033;
    /**
     * $column.columnComment
     */
    private String f2031000000026000005034;
    /**
     * $column.columnComment
     */
    private String cdTime;
    /**
     * $column.columnComment
     */
    private String dmpShareCdTimeIdatat;
    /**
     * $column.columnComment
     */
    private String dataSharingCdTimeIdatat;

}
