package com.zhonghe.system.domain.thirdparty;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


/**
 * 【请填写功能名称】对象 sl_weather_sign
 *
 * <AUTHOR>
 * @date 2023-08-21
 */
@Data
@TableName("business.sl_weather_sign")
@ApiModel("天气警报视图对象")
public class SlWeatherSign {

    private static final long serialVersionUID = 8006351837232087178L;

    /**
     * $column.columnComment
     */
    @TableId
    @ApiModelProperty("非业务主键ID")

    private String fywzjid;
    /**
     * $column.columnComment
     */
    @ApiModelProperty("新增时间")

    private Date xzsj;
    /**
     * $column.columnComment
     */
    @ApiModelProperty("增量时间")

    private Date zlsj;
    /**
     * $column.columnComment
     */
    @ApiModelProperty("增量标识")

    private String zlbs;
    /**
     * $column.columnComment
     */
    @ApiModelProperty("批次号")

    private String pch;
    /**
     * $column.columnComment
     */
    @ApiModelProperty("预警信号类型")

    private String yjxhlx;
    /**
     * $column.columnComment
     */
    @ApiModelProperty("预警信号级别")

    private String yjxhjb;
    /**
     * $column.columnComment
     */
    @ApiModelProperty("影响范围")
    private String yxfw;
    /**
     * $column.columnComment
     */
    @ApiModelProperty("发布时间")

    private Date fbsj;
    /**
     * $column.columnComment
     */
    @ApiModelProperty("预警信息内容")

    private String yjxhnr;

}
