package com.zhonghe.system.domain.vo;

import com.zhonghe.system.domain.thirdparty.SlCaseEventProxy;
import com.zhonghe.system.domain.thirdparty.SlGridPatrolOrder;
import com.zhonghe.system.domain.thirdparty.vo.SpecialistVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @description:
 * @author: cq
 * @date: 2023/8/25 10:32
 */

@Data
@ApiModel("基础信息")
public class BaseThirdPartyVo {

    @ApiModelProperty("案事件")
    private List<SlCaseEventProxy> eventCase;

    @ApiModelProperty("巡查工单")
    private List<SlGridPatrolOrder> patrolOrders;


    @ApiModelProperty("特殊人群列表")
    private List<SpecialistVo> specialist;
}
