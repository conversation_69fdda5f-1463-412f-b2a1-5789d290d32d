package com.zhonghe.system.domain.thirdparty.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description:
 * @author: cq
 * @date: 2023/8/15 11:09
 */

@Data
@ApiModel("案事件统计")
@NoArgsConstructor
@AllArgsConstructor
public class SlCaseEventStatisticsVo {

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("数量")
    private Long count;
}

