package com.zhonghe.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zhonghe.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 老年失能对象 sl_old_population
 *
 * <AUTHOR>
 * @date 2023-09-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("business.sl_old_population")
public class SlOldPopulation extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * $column.columnComment
     */
    @TableId(value = "old_id")
    private Long oldId;
    /**
     * 社区、村
     */
    private String gridName;
    /**
     * 姓名
     */
    private String name;
    /**
     * 性别
     */
    private String sex;
    /**
     * 身份证号码
     */
    private String idCode;
    /**
     * 年龄
     */
    private Long age;
    /**
     * 户籍地址
     */
    private String baseLocation;
    /**
     * 身份属性
     */
    private String populationType;
    /**
     * 是否入住养老机构
     */
    private String livingNursing;
    /**
     * 评定失能程度
     */
    private String disableLv;
    /**
     * 现居地地址
     */
    private String livingLocation;
    /**
     * 就业单位地址
     */
    private String workingLocation;
    /**
     * $column.columnComment
     */
    @TableLogic
    private String delFlag;
    /**
     * $column.columnComment
     */
    private String status;

}
