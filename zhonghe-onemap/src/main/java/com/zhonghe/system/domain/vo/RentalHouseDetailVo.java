package com.zhonghe.system.domain.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description:
 * @author: cq
 * @date: 2023/8/15 14:59
 */

@Data
@ApiModel("出租屋详细信息")
public class RentalHouseDetailVo  extends BaseThirdPartyVo {

    @ApiModelProperty("房东")
    private String master;


    @ApiModelProperty("地址")
    private String address;


    @ApiModelProperty("房号")
    private String roomNo;


    @ApiModelProperty("租客数")
    private Long slaverCount;
/*

    @ApiModelProperty("案事件")
    private List<SlCaseEventProxy> eventCase;

    @ApiModelProperty("巡查工单")
    private List<SlGridPatrolOrder> patrolOrders;


    @ApiModelProperty("特殊人群列表")
    private List<SpecialistVo> specialist;*/
}

