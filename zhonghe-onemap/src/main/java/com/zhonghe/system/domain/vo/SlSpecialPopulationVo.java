package com.zhonghe.system.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zhonghe.common.annotation.ExcelDictFormat;
import com.zhonghe.common.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 老年失能视图对象 sl_special_population
 *
 * <AUTHOR>
 * @date 2023-09-01
 */
@Data
@ApiModel("老年失能视图对象")
@ExcelIgnoreUnannotated
public class SlSpecialPopulationVo {

    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    @ExcelProperty(value = "${comment}", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "$column.readConverterExp()")
    @ApiModelProperty("$column.columnComment")
    private Long specialId;

    /**
     * 所属村（社区）
     */
    @ExcelProperty(value = "所属村", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "社=区")
    @ApiModelProperty("所属村（社区）")
    private String gridName;

    /**
     * 所属网格
     */
    @ExcelProperty(value = "所属网格")
    @ApiModelProperty("所属网格")
    private String netName;

    /**
     * 姓名
     */
    @ExcelProperty(value = "姓名")
    @ApiModelProperty("姓名")
    private String name;

    /**
     * 性别
     */
    @ExcelProperty(value = "性别")
    @ApiModelProperty("性别")
    private String sex;

    /**
     * 年龄
     */
    @ExcelProperty(value = "年龄")
    @ApiModelProperty("年龄")
    private Long age;

    /**
     * 身份证
     */
    @ExcelProperty(value = "身份证")
    @ApiModelProperty("身份证")
    private String idCode;

    /**
     * 联系电话
     */
    @ExcelProperty(value = "联系电话")
    @ApiModelProperty("联系电话")
    private String phone;

    /**
     * 现居住地址
     */
    @ExcelProperty(value = "现居住地址")
    @ApiModelProperty("现居住地址")
    private String livingLocation;

    /**
     * 居住类别
     */
    @ExcelProperty(value = "居住类别")
    @ApiModelProperty("居住类别")
    private String livingType;

    /**
     * 群体
     */
    @ExcelProperty(value = "群体")
    @ApiModelProperty("群体")
    private String populationType;

    /**
     * 疫苗接种
     */
    @ExcelProperty(value = "疫苗接种")
    @ApiModelProperty("疫苗接种")
    private String vaccinum;

    /**
     * 健康类别
     */
    @ExcelProperty(value = "健康类别")
    @ApiModelProperty("健康类别")
    private String healthType;

    /**
     * 身体状况
     */
    @ExcelProperty(value = "身体状况")
    @ApiModelProperty("身体状况")
    private String bodyType;

    /**
     * 诊疗服药情况（就医频次及药品）
     */
    @ExcelProperty(value = "诊疗服药情况", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "就=医频次及药品")
    @ApiModelProperty("诊疗服药情况（就医频次及药品）")
    private String medicine;

    /**
     * 家庭医生（姓名、联系电话）
     */
    @ExcelProperty(value = "家庭医生", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "姓=名、联系电话")
    @ApiModelProperty("家庭医生（姓名、联系电话）")
    private String doctor;

    /**
     * 紧急联系人
     */
    @ExcelProperty(value = "紧急联系人")
    @ApiModelProperty("紧急联系人")
    private String helpManager;

    /**
     * 网格跟进工作人员
     */
    @ExcelProperty(value = "网格跟进工作人员")
    @ApiModelProperty("网格跟进工作人员")
    private String gridManager;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    @ApiModelProperty("备注")
    private String mark;

    /**
     * $column.columnComment
     */
    @ExcelProperty(value = "${comment}", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "$column.readConverterExp()")
    @ApiModelProperty("$column.columnComment")
    private String status;


}
