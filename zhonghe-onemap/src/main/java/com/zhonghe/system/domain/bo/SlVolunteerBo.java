package com.zhonghe.system.domain.bo;

import com.zhonghe.common.core.domain.BaseEntity;
import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


/**
 * 志愿者业务对象 sl_volunteer
 *
 * <AUTHOR>
 * @date 2023-08-03
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("志愿者业务对象")
public class SlVolunteerBo extends BaseEntity {

    /**
     * $column.columnComment
     */
    @ApiModelProperty(value = "$column.columnComment", required = true)
    @NotNull(message = "$column.columnComment不能为空", groups = { EditGroup.class })
    private Long volunteerId;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名", required = true)
    @NotBlank(message = "姓名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String volunteerName;

    /**
     * 性别
     */
    @ApiModelProperty(value = "性别", required = true)
    @NotBlank(message = "性别不能为空", groups = { AddGroup.class, EditGroup.class })
    private String sex;

    /**
     * 登录账号
     */
    @ApiModelProperty(value = "登录账号", required = true)
    @NotBlank(message = "登录账号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String volunteerLoginCode;

    /**
     * 证件号码
     */
    @ApiModelProperty(value = "证件号码", required = true)
    @NotBlank(message = "证件号码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String volunteerIdNumber;

    /**
     * 手机号码
     */
    @ApiModelProperty(value = "手机号码", required = true)
    @NotBlank(message = "手机号码不能为空", groups = { AddGroup.class, EditGroup.class })
    private String volunteerPhone;

    /**
     * 注册时间
     */
    @ApiModelProperty(value = "注册时间", required = true)
    @NotBlank(message = "注册时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private String volunteerRegisterDate;

    /**
     * 校验信息
     */
    @ApiModelProperty(value = "校验信息", required = true)
    @NotBlank(message = "校验信息不能为空", groups = { AddGroup.class, EditGroup.class })
    private String volunteerVerify;

    /**
     * 是否面验
     */
    @ApiModelProperty(value = "是否面验", required = true)
    @NotBlank(message = "是否面验不能为空", groups = { AddGroup.class, EditGroup.class })
    private String volunteerFaceVerify;

    /**
     * 审批状态
     */
    @ApiModelProperty(value = "审批状态", required = true)
    @NotBlank(message = "审批状态不能为空", groups = { AddGroup.class, EditGroup.class })
    private String volunteerPass;

    /**
     * 政治面貌
     */
    @ApiModelProperty(value = "政治面貌", required = true)
    @NotBlank(message = "政治面貌不能为空", groups = { AddGroup.class, EditGroup.class })
    private String volunteerPolitical;

    /**
     * 服务时长
     */
    @ApiModelProperty(value = "服务时长", required = true)
    @NotBlank(message = "服务时长不能为空", groups = { AddGroup.class, EditGroup.class })
    private String volunteerServerTime;

    /**
     * 培训时长
     */
    @ApiModelProperty(value = "培训时长", required = true)
    @NotBlank(message = "培训时长不能为空", groups = { AddGroup.class, EditGroup.class })
    private String volunteerTrainTime;

    /**
     * $column.columnComment
     */
    @ApiModelProperty(value = "$column.columnComment", required = true)
    @NotBlank(message = "$column.columnComment不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;


}
