package com.zhonghe.system.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zhonghe.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 石龙社工对象 sl_social_worker
 *
 * <AUTHOR>
 * @date 2023-08-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("business.sl_social_worker")
public class SlSocialWorker extends BaseEntity {

    private static final long serialVersionUID= 5292455495846122407L;

    /**
     * $column.columnComment
     */
    @TableId(value = "social_worker_id")
    private Long socialWorkerId;
    /**
     * 社工名称
     */
    private String workerName;
    /**
     * 社工年龄
     */
    private Long workerAge;
    /**
     * 社工性别
     */
    private String sex;
    /**
     * 社工身份证号
     */
    private String workerIdNumber;
    /**
     * 社工手机
     */
    private String workerPhone;
    /**
     * 备注
     */
    private String mark;
    /**
     * $column.columnComment
     */
    private String workPosition;
    /**
     * $column.columnComment
     */
    private String standardPosition;
    /**
     * $column.columnComment
     */
    @TableLogic
    private String delFlag;
    /**
     * $column.columnComment
     */
    private String status;

    @TableField(exist = false)
    private String gridZoneName;

    private String onenetJson;
}
