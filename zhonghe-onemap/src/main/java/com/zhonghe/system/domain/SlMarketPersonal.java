package com.zhonghe.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zhonghe.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 【请填写功能名称】对象 sl_market_personal
 *
 * <AUTHOR>
 * @date 2023-08-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("business.sl_market_personal")
public class SlMarketPersonal extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * $column.columnComment
     */
    @TableId(value = "market_personal_id")
    private Long marketPersonalId;
    /**
     * $column.columnComment
     */
    private String personalCreditCode;
    /**
     * $column.columnComment
     */
    private String personalName;
    /**
     * $column.columnComment
     */
    private String personalAddress;
    /**
     * $column.columnComment
     */
    private String personalType;
    /**
     * $column.columnComment
     */
    private String createDate;
    /**
     * $column.columnComment
     */
    private String operatingEndDate;
    /**
     * $column.columnComment
     */
    private String operatingScope;
    /**
     * $column.columnComment
     */
    private String legalPerson;
    /**
     * $column.columnComment
     */
    private String idType;
    /**
     * $column.columnComment
     */
    private String idCode;
    /**
     * $column.columnComment
     */
    private String currencyRegister;
    /**
     * $column.columnComment
     */
    @TableLogic
    private String delFlag;
    /**
     * $column.columnComment
     */
    private String status;

}
