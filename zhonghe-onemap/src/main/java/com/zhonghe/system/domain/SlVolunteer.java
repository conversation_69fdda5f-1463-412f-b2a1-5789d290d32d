package com.zhonghe.system.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zhonghe.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;


/**
 * 志愿者对象 sl_volunteer
 *
 * <AUTHOR>
 * @date 2023-08-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("business.sl_volunteer")
public class SlVolunteer extends BaseEntity {

    private static final long serialVersionUID= -6201939470363196238L;

    /**
     * $column.columnComment
     */
    @TableId(value = "volunteer_id")
    private Long volunteerId;
    /**
     * 姓名
     */
    private String volunteerName;
    /**
     * 性别
     */
    private String sex;
    /**
     * 登录账号
     */
    private String volunteerLoginCode;
    /**
     * 证件号码
     */
    private String volunteerIdNumber;
    /**
     * 手机号码
     */
    private String volunteerPhone;
    /**
     * 注册时间
     */
    private Date volunteerRegisterDate;
    /**
     * 校验信息
     */
    private String volunteerVerify;
    /**
     * 是否面验
     */
    private String volunteerFaceVerify;
    /**
     * 审批状态
     */
    private String volunteerPass;
    /**
     * 政治面貌
     */
    private String volunteerPolitical;
    /**
     * 服务时长
     */
    private String volunteerServerTime;
    /**
     * 培训时长
     */
    private String volunteerTrainTime;
    /**
     * $column.columnComment
     */
    @TableLogic
    private String delFlag;
    /**
     * $column.columnComment
     */
    private String status;

    @TableField(exist = false)
    private String gridZoneName;
}
