package com.zhonghe.system.domain.bo;

import com.zhonghe.common.core.domain.BaseEntity;
import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


/**
 * 案时间业务对象 sl_case_event
 *
 * <AUTHOR>
 * @date 2023-08-14
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("案时间业务对象")
public class SlCaseEventBo extends BaseEntity {

    /**
     * $column.columnComment
     */
    @ApiModelProperty(value = "$column.columnComment", required = true)
    @NotNull(message = "$column.columnComment不能为空", groups = { EditGroup.class })
    private Long caseEventId;

    /**
     * 案事件编号
     */
    @ApiModelProperty(value = "案事件编号", required = true)
    @NotBlank(message = "案事件编号不能为空", groups = { AddGroup.class, EditGroup.class })
    private String caseEventCode;

    /**
     * 案事件来源
     */
    @ApiModelProperty(value = "案事件来源", required = true)
    @NotBlank(message = "案事件来源不能为空", groups = { AddGroup.class, EditGroup.class })
    private String caseEventSource;

    /**
     * 镇街
     */
    @ApiModelProperty(value = "镇街", required = true)
    @NotBlank(message = "镇街不能为空", groups = { AddGroup.class, EditGroup.class })
    private String caseEventStreet;

    /**
     * 社区
     */
    @ApiModelProperty(value = "社区", required = true)
    @NotBlank(message = "社区不能为空", groups = { AddGroup.class, EditGroup.class })
    private String caseEventZone;

    /**
     * 网格
     */
    @ApiModelProperty(value = "网格", required = true)
    @NotBlank(message = "网格不能为空", groups = { AddGroup.class, EditGroup.class })
    private String caseEventGrid;

    /**
     * 所属部门
     */
    @ApiModelProperty(value = "所属部门", required = true)
    @NotBlank(message = "所属部门不能为空", groups = { AddGroup.class, EditGroup.class })
    private String caseEventDepartment;

    /**
     * 隐患描述
     */
    @ApiModelProperty(value = "隐患描述", required = true)
    @NotBlank(message = "隐患描述不能为空", groups = { AddGroup.class, EditGroup.class })
    private String caseEventReason;

    /**
     * 主体名称
     */
    @ApiModelProperty(value = "主体名称", required = true)
    @NotBlank(message = "主体名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String caseEventSubject;

    /**
     * 地址
     */
    @ApiModelProperty(value = "地址", required = true)
    @NotBlank(message = "地址不能为空", groups = { AddGroup.class, EditGroup.class })
    private String caseEventAddress;

    /**
     * 生成时间
     */
    @ApiModelProperty(value = "生成时间", required = true)
    @NotBlank(message = "生成时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private String caseEventCreateTime;

    /**
     * 处理期限
     */
    @ApiModelProperty(value = "处理期限", required = true)
    @NotBlank(message = "处理期限不能为空", groups = { AddGroup.class, EditGroup.class })
    private String caseEventEndDate;

    /**
     * 处置部门
     */
    @ApiModelProperty(value = "处置部门", required = true)
    @NotBlank(message = "处置部门不能为空", groups = { AddGroup.class, EditGroup.class })
    private String caseEventHandlerOrg;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态", required = true)
    @NotBlank(message = "状态不能为空", groups = { AddGroup.class, EditGroup.class })
    private String caseEventStatus;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述", required = true)
    @NotBlank(message = "描述不能为空", groups = { AddGroup.class, EditGroup.class })
    private String caseEventMark;

    /**
     * 采集字段信息
     */
    @ApiModelProperty(value = "采集字段信息", required = true)
    @NotBlank(message = "采集字段信息不能为空", groups = { AddGroup.class, EditGroup.class })
    private String caseEventGatherSource;

    /**
     * $column.columnComment
     */
    @ApiModelProperty(value = "$column.columnComment", required = true)
    @NotBlank(message = "$column.columnComment不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;


}
