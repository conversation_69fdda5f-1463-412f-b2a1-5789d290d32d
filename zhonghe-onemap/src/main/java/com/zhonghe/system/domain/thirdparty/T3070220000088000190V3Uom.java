package com.zhonghe.system.domain.thirdparty;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;


/**
 * 【请填写功能名称】对象 t3070220000088_000190_v3_uom
 *
 * <AUTHOR>
 * @date 2023-09-14
 */
@Data
@TableName("public.t3070220000088_000190_v3_uom")
public class T3070220000088000190V3Uom {

    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private String f3070220000088000190001;
    /**
     * $column.columnComment
     */
    private String f3070220000088000190002;
    /**
     * $column.columnComment
     */
    private String f3070220000088000190003;
    /**
     * $column.columnComment
     */
    private String f3070220000088000190004;
    /**
     * $column.columnComment
     */
    private String f3070220000088000190006;
    /**
     * $column.columnComment
     */
    private String f3070220000088000190007;
    /**
     * $column.columnComment
     */
    private String f3070220000088000190008;
    /**
     * $column.columnComment
     */
    private String f3070220000088000190009;
    /**
     * $column.columnComment
     */
    private String f3070220000088000190010;
    /**
     * $column.columnComment
     */
    private String f3070220000088000190011;
    /**
     * $column.columnComment
     */
    private String f3070220000088000190012;
    /**
     * $column.columnComment
     */
    private String f3070220000088000190013;
    /**
     * $column.columnComment
     */
    private String f3070220000088000190014;
    /**
     * $column.columnComment
     */
    private String f3070220000088000190015;
    /**
     * $column.columnComment
     */
    private String f3070220000088000190016;
    /**
     * $column.columnComment
     */
    private String f3070220000088000190017;
    /**
     * $column.columnComment
     */
    private String f3070220000088000190018;
    /**
     * $column.columnComment
     */
    private String f3070220000088000190019;
    /**
     * $column.columnComment
     */
    private String f3070220000088000190020;
    /**
     * $column.columnComment
     */
    private String f3070220000088000190021;
    /**
     * $column.columnComment
     */
    private String f3070220000088000190022;
    /**
     * $column.columnComment
     */
    private String f3070220000088000190023;
    /**
     * $column.columnComment
     */
    private String f3070220000088000190024;
    /**
     * $column.columnComment
     */
    private String f3070220000088000190025;
    /**
     * $column.columnComment
     */
    private String f3070220000088000190026;
    /**
     * $column.columnComment
     */
    private String f3070220000088000190032;
    /**
     * $column.columnComment
     */
    private String f3070220000088000190038;
    /**
     * $column.columnComment
     */
    private String f3070220000088000190044;
    /**
     * $column.columnComment
     */
    private String f3070220000088000190050;
    /**
     * $column.columnComment
     */
    private String f3070220000088000190056;
    /**
     * $column.columnComment
     */
    private String f3070220000088000190062;
    /**
     * $column.columnComment
     */
    private String f3070220000088000190063;
    /**
     * $column.columnComment
     */
    @TableId(value = "id")
    private String id;
    /**
     * $column.columnComment
     */
    private String cdOperation;
    /**
     * $column.columnComment
     */
    private String cdTime;
    /**
     * $column.columnComment
     */
    private String f3070220000088000190076;
    /**
     * $column.columnComment
     */
    private String f3070220000088000190077;
    /**
     * $column.columnComment
     */
    private String dmpShareCdTimeIdatat;
    /**
     * $column.columnComment
     */
    private String dataSharingCdTimeIdatat;

}
