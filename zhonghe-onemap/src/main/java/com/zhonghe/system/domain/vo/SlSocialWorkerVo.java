package com.zhonghe.system.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zhonghe.common.annotation.ExcelDictFormat;
import com.zhonghe.common.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 石龙社工视图对象 sl_social_worker
 *
 * <AUTHOR>
 * @date 2023-08-03
 */
@Data
@ApiModel("石龙社工视图对象")
@ExcelIgnoreUnannotated
public class SlSocialWorkerVo {

    private static final long serialVersionUID = -733167681005384012L;

    /**
     * $column.columnComment
     */
    @ExcelProperty(value = "${comment}", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "$column.readConverterExp()")
    @ApiModelProperty("$column.columnComment")
    private Long socialWorkerId;

    /**
     * 社工名称
     */
    @ExcelProperty(value = "社工名称")
    @ApiModelProperty("社工名称")
    private String workerName;

    /**
     * 社工年龄
     */
    @ExcelProperty(value = "社工年龄")
    @ApiModelProperty("社工年龄")
    private Long workerAge;

    /**
     * 社工性别
     */
    @ExcelProperty(value = "社工性别")
    @ApiModelProperty("社工性别")
    private String sex;

    /**
     * 社工身份证号
     */
    @ExcelProperty(value = "社工身份证号")
    @ApiModelProperty("社工身份证号")
    private String workerIdNumber;

    /**
     * 社工手机
     */
    @ExcelProperty(value = "社工手机")
    @ApiModelProperty("社工手机")
    private String workerPhone;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    @ApiModelProperty("备注")
    private String mark;

    /**
     * $column.columnComment
     */
    @ExcelProperty(value = "工作地点", converter = ExcelDictConvert.class)
    @ApiModelProperty("工作地点")
    private String workPosition;

    /**
     * $column.columnComment
     */
    @ExcelProperty(value = "二标地点", converter = ExcelDictConvert.class)
    @ApiModelProperty("二标地点")
    private String standardPosition;

    /**
     * $column.columnComment
     */
    @ExcelProperty(value = "${comment}", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "$column.readConverterExp()")
    @ApiModelProperty("$column.columnComment")
    private String status;


}
