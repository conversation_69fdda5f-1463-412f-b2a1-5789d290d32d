package com.zhonghe.system.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zhonghe.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description:
 * @author: cq
 * @date: 2023/8/2 14:14
 */

@Data
@ApiModel("网格区域")
@ExcelIgnoreUnannotated
public class SlGridZoneDto extends BaseEntity {


    /**
     * 所属区域名称
     */
    @ExcelProperty(value = "所属片区")
    @ApiModelProperty("所属区域名称")
    private String gridGroupName;

    /**
     * 网格名称
     */
    @ExcelProperty(value = "网格名")
    @ApiModelProperty("网格名称")
    private String gridZoneName;

}

