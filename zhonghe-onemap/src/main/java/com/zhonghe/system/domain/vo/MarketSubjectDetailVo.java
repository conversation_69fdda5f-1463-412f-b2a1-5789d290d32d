package com.zhonghe.system.domain.vo;

import com.zhonghe.system.domain.thirdparty.SlCaseEventProxy;
import com.zhonghe.system.domain.thirdparty.SlGridPatrolOrder;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @description:
 * @author: cq
 * @date: 2023/8/15 15:43
 */

@Data
@ApiModel("市场主体详情")
public class MarketSubjectDetailVo {

    @ApiModelProperty("主体名称")
    private String name;

    @ApiModelProperty("统一社会信用代码")
    private String creditCode;

    @ApiModelProperty("法人代表")
    private String legalContract;

    @ApiModelProperty("主体地址")
    private String address;


    @ApiModelProperty("案事件")
    private List<SlCaseEventProxy> eventCase;


    @ApiModelProperty("巡查记录")
    private List<SlGridPatrolOrder> patrolOrders;
}

