package com.zhonghe.system.domain.thirdparty.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description:
 * @author: cq
 * @date: 2023/8/25 10:51
 */

@Data
@ApiModel("市场主体查询")
public class DgsscztxxBo {


    @ApiModelProperty("关键字")
    private String keyWord;

    @ApiModelProperty("类型查询，传值（1、2、3）定义: 1：个体户 2：企业 3：三小")
    private String queryType;
}

