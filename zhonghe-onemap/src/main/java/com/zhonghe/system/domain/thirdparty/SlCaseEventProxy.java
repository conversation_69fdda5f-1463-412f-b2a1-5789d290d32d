package com.zhonghe.system.domain.thirdparty;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;


/**
 * 【请填写功能名称】对象 sl_case_event_proxy
 *
 * <AUTHOR>
 * @date 2023-08-14
 */
@Data
@TableName("business.case_event")
public class SlCaseEventProxy {

    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private String fywzj;
    /**
     * $column.columnComment
     */
    private String asjid;
    /**
     * $column.columnComment
     */
    private String ztid;
    /**
     * $column.columnComment
     */
    private String ztmc;
    /**
     * $column.columnComment
     */
    private String jzwdz;
    /**
     * $column.columnComment
     */
    private String jd;
    /**
     * $column.columnComment
     */
    private String wd;
    /**
     * $column.columnComment
     */
    private String yhms;
    /**
     * $column.columnComment
     */
    private String czbmbm;
    /**
     * $column.columnComment
     */
    private String czbmmc;
    /**
     * $column.columnComment
     */
    private String czqx;
    /**
     * $column.columnComment
     */
    private String clrxm;
    /**
     * $column.columnComment
     */
    private String yylj;
    /**
     * $column.columnComment
     */
    private String tplj;
    /**
     * $column.columnComment
     */
    private String czjzrq;
    /**
     * $column.columnComment
     */
    private Date scsj;
    /**
     * $column.columnComment
     */
    private String dlzj;
    /**
     * $column.columnComment
     */
    private Date xzsj;
    /**
     * $column.columnComment
     */
    private String zlbs;
    /**
     * $column.columnComment
     */
    private Date zlsj;
    /**
     * $column.columnComment
     */
    private String pch;

}
