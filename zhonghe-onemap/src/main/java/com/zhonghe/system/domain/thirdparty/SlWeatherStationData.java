package com.zhonghe.system.domain.thirdparty;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;


/**
 * 【请填写功能名称】对象 sl_weather_station_data
 *
 * <AUTHOR>
 * @date 2023-08-21
 */
@Data
@TableName("business.sl_weather_station_data")
public class SlWeatherStationData {

    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private String fywzjid;
    /**
     * $column.columnComment
     */
    private String xzsj;
    /**
     * $column.columnComment
     */
    private String zlsj;
    /**
     * $column.columnComment
     */
    private String zlbs;
    /**
     * $column.columnComment
     */
    private String pch;
    /**
     * $column.columnComment
     */
    private String zm;
    /**
     * $column.columnComment
     */
    private Double zljd;
    private Double zdjd;
    /**
     * $column.columnComment
     */
    private Double zdwd;
    /**
     * $column.columnComment
     */
    private String jcsj;
    /**
     * $column.columnComment
     */
    private String yxsyl;
    /**
     * $column.columnComment
     */
    private String zdqw;

}
