package com.zhonghe.system.domain.bo;

import com.zhonghe.common.core.domain.BaseEntity;
import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


/**
 * 老年失能业务对象 sl_special_population
 *
 * <AUTHOR>
 * @date 2023-09-01
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("老年失能业务对象")
public class SlSpecialPopulationBo extends BaseEntity {

    /**
     * $column.columnComment
     */
    @ApiModelProperty(value = "$column.columnComment", required = true)
    @NotNull(message = "$column.columnComment不能为空", groups = { EditGroup.class })
    private Long specialId;

    /**
     * 所属村（社区）
     */
    @ApiModelProperty(value = "所属村（社区）", required = true)
    @NotBlank(message = "所属村（社区）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String gridName;

    /**
     * 所属网格
     */
    @ApiModelProperty(value = "所属网格", required = true)
    @NotBlank(message = "所属网格不能为空", groups = { AddGroup.class, EditGroup.class })
    private String netName;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名", required = true)
    @NotBlank(message = "姓名不能为空", groups = { AddGroup.class, EditGroup.class })
    private String name;

    /**
     * 性别
     */
    @ApiModelProperty(value = "性别", required = true)
    @NotBlank(message = "性别不能为空", groups = { AddGroup.class, EditGroup.class })
    private String sex;

    /**
     * 年龄
     */
    @ApiModelProperty(value = "年龄", required = true)
    @NotNull(message = "年龄不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long age;

    /**
     * 身份证
     */
    @ApiModelProperty(value = "身份证", required = true)
    @NotBlank(message = "身份证不能为空", groups = { AddGroup.class, EditGroup.class })
    private String idCode;

    /**
     * 联系电话
     */
    @ApiModelProperty(value = "联系电话", required = true)
    @NotBlank(message = "联系电话不能为空", groups = { AddGroup.class, EditGroup.class })
    private String phone;

    /**
     * 现居住地址
     */
    @ApiModelProperty(value = "现居住地址", required = true)
    @NotBlank(message = "现居住地址不能为空", groups = { AddGroup.class, EditGroup.class })
    private String livingLocation;

    /**
     * 居住类别
     */
    @ApiModelProperty(value = "居住类别", required = true)
    @NotBlank(message = "居住类别不能为空", groups = { AddGroup.class, EditGroup.class })
    private String livingType;

    /**
     * 群体
     */
    @ApiModelProperty(value = "群体", required = true)
    @NotBlank(message = "群体不能为空", groups = { AddGroup.class, EditGroup.class })
    private String populationType;

    /**
     * 疫苗接种
     */
    @ApiModelProperty(value = "疫苗接种", required = true)
    @NotBlank(message = "疫苗接种不能为空", groups = { AddGroup.class, EditGroup.class })
    private String vaccinum;

    /**
     * 健康类别
     */
    @ApiModelProperty(value = "健康类别", required = true)
    @NotBlank(message = "健康类别不能为空", groups = { AddGroup.class, EditGroup.class })
    private String healthType;

    /**
     * 身体状况
     */
    @ApiModelProperty(value = "身体状况", required = true)
    @NotBlank(message = "身体状况不能为空", groups = { AddGroup.class, EditGroup.class })
    private String bodyType;

    /**
     * 诊疗服药情况（就医频次及药品）
     */
    @ApiModelProperty(value = "诊疗服药情况（就医频次及药品）", required = true)
    @NotBlank(message = "诊疗服药情况（就医频次及药品）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String medicine;

    /**
     * 家庭医生（姓名、联系电话）
     */
    @ApiModelProperty(value = "家庭医生（姓名、联系电话）", required = true)
    @NotBlank(message = "家庭医生（姓名、联系电话）不能为空", groups = { AddGroup.class, EditGroup.class })
    private String doctor;

    /**
     * 紧急联系人
     */
    @ApiModelProperty(value = "紧急联系人", required = true)
    @NotBlank(message = "紧急联系人不能为空", groups = { AddGroup.class, EditGroup.class })
    private String helpManager;

    /**
     * 网格跟进工作人员
     */
    @ApiModelProperty(value = "网格跟进工作人员", required = true)
    @NotBlank(message = "网格跟进工作人员不能为空", groups = { AddGroup.class, EditGroup.class })
    private String gridManager;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注", required = true)
    @NotBlank(message = "备注不能为空", groups = { AddGroup.class, EditGroup.class })
    private String mark;

    /**
     * $column.columnComment
     */
    @ApiModelProperty(value = "$column.columnComment", required = true)
    @NotBlank(message = "$column.columnComment不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;


}
