package com.zhonghe.system.domain.vo.gas;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zhonghe.common.annotation.ExcelDictFormat;
import com.zhonghe.common.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 燃气统计视图对象 sl_gas_restaurant_gather
 *
 * <AUTHOR>
 * @date 2023-07-28
 */
@Data
@ApiModel("燃气统计视图对象")
@ExcelIgnoreUnannotated
public class SlGasRestaurantGatherVo {

    private static final long serialVersionUID = 1027767160580713321L;

    /**
     * $column.columnComment
     */
    @ExcelProperty(value = "${comment}", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "$column.readConverterExp()")
    @ApiModelProperty("$column.columnComment")
    private Long gatherGasId;

    /**
     * 辖区
     */
    @ExcelProperty(value = "辖区")
    @ApiModelProperty("辖区")
    private String sectionName;

    /**
     * 主体名称
     */
    @ExcelProperty(value = "主体名称")
    @ApiModelProperty("主体名称")
    private String subjectName;

    /**
     * 地址
     */
    @ExcelProperty(value = "地址")
    @ApiModelProperty("地址")
    private String subjectAddress;

    /**
     * 管理人姓名
     */
    @ExcelProperty(value = "管理人姓名")
    @ApiModelProperty("管理人姓名")
    private String subjectManager;

    /**
     * 联系电话
     */
    @ExcelProperty(value = "联系电话")
    @ApiModelProperty("联系电话")
    private String subjectPhone;

    /**
     * 网格员姓名
     */
    @ExcelProperty(value = "网格员姓名")
    @ApiModelProperty("网格员姓名")
    private String gridManager;

    /**
     * 燃气类型描述：天然气、电、气瓶
     */
    @ExcelProperty(value = "燃气类型描述：天然气、电、气瓶")
    @ApiModelProperty("燃气类型描述：天然气、电、气瓶")
    private String powerType;

    /**
     * 燃气类型具体描述
     */
    @ExcelProperty(value = "燃气类型具体描述")
    @ApiModelProperty("燃气类型具体描述")
    private String powerTypeMark;

    /**
     * 燃气使用情况
     */
    @ExcelProperty(value = "燃气使用情况")
    @ApiModelProperty("燃气使用情况")
    private String powerStoreMark;

    /**
     * 大气瓶数量
     */
    @ExcelProperty(value = "大气瓶数量")
    @ApiModelProperty("大气瓶数量")
    private Long powerStoreLargeNum;

    /**
     * 小气瓶数量
     */
    @ExcelProperty(value = "小气瓶数量")
    @ApiModelProperty("小气瓶数量")
    private Long powerStoreSmallNum;

    /**
     * 隐患描述、备注
     */
    @ExcelProperty(value = "隐患描述、备注")
    @ApiModelProperty("隐患描述、备注")
    private String questionMark;

    /**
     * 气瓶隐患
     */
    @ExcelProperty(value = "气瓶隐患")
    @ApiModelProperty("气瓶隐患")
    private String questionGasBottle;

    /**
     * 管道隐患
     */
    @ExcelProperty(value = "管道隐患")
    @ApiModelProperty("管道隐患")
    private String questionGasPipeline;

    /**
     * 报警器隐患
     */
    @ExcelProperty(value = "报警器隐患")
    @ApiModelProperty("报警器隐患")
    private String questionGasWarmingMachine;

    /**
     * 存储隐患
     */
    @ExcelProperty(value = "存储隐患")
    @ApiModelProperty("存储隐患")
    private String questionGasStoreWeight;

    /**
     * 摆放隐患
     */
    @ExcelProperty(value = "摆放隐患")
    @ApiModelProperty("摆放隐患")
    private String questionGasStoreLocation;

    /**
     * 黑气标识
     */
    @ExcelProperty(value = "黑气标识")
    @ApiModelProperty("黑气标识")
    private String questionGasIllegal;

    /**
     * 是否整改
     */
    @ExcelProperty(value = "是否整改")
    @ApiModelProperty("是否整改")
    private String questionRectification;

    /**
     * 收集时间
     */
    @ExcelProperty(value = "收集时间")
    @ApiModelProperty("收集时间")
    private String gatherTime;

    /**
     * $column.columnComment
     */
    @ExcelProperty(value = "${comment}", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "$column.readConverterExp()")
    @ApiModelProperty("$column.columnComment")
    private String status;

    @ApiModelProperty("特大瓶气瓶数量")
    private Long powerStoreHugeNum;

    @ApiModelProperty("用气环境隐患")
    private String questionGasEnvironment;
}
