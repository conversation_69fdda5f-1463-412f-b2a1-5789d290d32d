package com.zhonghe.system.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description:
 * @author: cq
 * @date: 2023/8/14 15:28
 */
@Data
@ApiModel("市场主体信息视图统计对象")
@ExcelIgnoreUnannotated
public class SlMarketSubjectStatisticsVo {

    @ApiModelProperty("总数")
    private Long count;

    @ApiModelProperty("三小场所")
    private Long smallPlaceCount;

    @ApiModelProperty("个体户")
    private Long personalCount;

    @ApiModelProperty("从业人数")
    private Long slaverCount;

    @ApiModelProperty("企业")
    private Long enterpriseCount;

}

