package com.zhonghe.system.domain.thirdparty;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;


/**
 * 【请填写功能名称】对象 sl_grid_patrol_order
 *
 * <AUTHOR>
 * @date 2023-08-18
 */
@Data
@TableName("business.sl_grid_patrol_order")
public class SlGridPatrolOrder  {

    private static final long serialVersionUID=1L;

    /**
     * $column.columnComment
     */
    private String fywzj;
    /**
     * $column.columnComment
     */
    private String zj;
    /**
     * $column.columnComment
     */
    private String ztid;
    /**
     * $column.columnComment
     */
    private String gdmc;
    /**
     * $column.columnComment
     */
    private String sswgid;
    /**
     * $column.columnComment
     */
    private String gdlsh;
    /**
     * $column.columnComment
     */
    private String xczt;
    /**
     * $column.columnComment
     */
    private String scrq;
    /**
     * $column.columnComment
     */
    private String xcqx;
    /**
     * $column.columnComment
     */
    private String cjrid;
    /**
     * $column.columnComment
     */
    private String cjrxm;
    /**
     * $column.columnComment
     */
    private String cjsj;
    /**
     * $column.columnComment
     */
    private String gxrid;
    /**
     * $column.columnComment
     */
    private String gxrxm;
    /**
     * $column.columnComment
     */
    private String gxsj;
    /**
     * $column.columnComment
     */
    private String bmid;
    /**
     * $column.columnComment
     */
    private String bmmc;
    /**
     * $column.columnComment
     */
    private String sjrid;
    /**
     * $column.columnComment
     */
    private String sjrxm;
    /**
     * $column.columnComment
     */
    private String zhjcrid;
    /**
     * $column.columnComment
     */
    private String zhjcrxm;
    /**
     * $column.columnComment
     */
    private String jccs;
    /**
     * $column.columnComment
     */
    private String scyhs;
    /**
     * $column.columnComment
     */
    private String sfcsyh;
    /**
     * $column.columnComment
     */
    private String kmqk;
    /**
     * $column.columnComment
     */
    private String txzt;
    /**
     * $column.columnComment
     */
    private String ztlx;
    /**
     * $column.columnComment
     */
    private String ztmc;
    /**
     * $column.columnComment
     */
    private String jzid;
    /**
     * $column.columnComment
     */
    private String jzdz;
    /**
     * $column.columnComment
     */
    private String sxflid;
    /**
     * $column.columnComment
     */
    private String sxflmc;
    /**
     * $column.columnComment
     */
    private String zybqid;
    /**
     * $column.columnComment
     */
    private String sjsj;
    /**
     * $column.columnComment
     */
    private String zhjcsj;
    /**
     * $column.columnComment
     */
    private String gdjb;
    /**
     * $column.columnComment
     */
    private String gdpc;
    /**
     * $column.columnComment
     */
    private String zybqmc;
    /**
     * $column.columnComment
     */
    private String clrdh;
    /**
     * $column.columnComment
     */
    private String cljg;
    /**
     * $column.columnComment
     */
    private String ms;
    /**
     * $column.columnComment
     */
    private String qzwj;
    /**
     * $column.columnComment
     */
    private String wgmc;
    /**
     * $column.columnComment
     */
    private String wgid;
    /**
     * $column.columnComment
     */
    private String xzsj;
    /**
     * $column.columnComment
     */
    private String zlbs;
    /**
     * $column.columnComment
     */
    private String zlsj;
    /**
     * $column.columnComment
     */
    private String pch;
    /**
     * $column.columnComment
     */
    private String scbs;

}
