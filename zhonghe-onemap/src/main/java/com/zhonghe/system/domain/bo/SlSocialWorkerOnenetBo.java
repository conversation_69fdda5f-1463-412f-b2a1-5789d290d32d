package com.zhonghe.system.domain.bo;

import com.zhonghe.common.core.domain.BaseEntity;
import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


/**
 * 社会工作者一网共享数据业务对象 sl_social_worker_onenet
 *
 * <AUTHOR>
 * @date 2023-08-09
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("社会工作者一网共享数据业务对象")
public class SlSocialWorkerOnenetBo extends BaseEntity {

    /**
     * $column.columnComment
     */
    @ApiModelProperty(value = "$column.columnComment", required = true)
    @NotNull(message = "$column.columnComment不能为空", groups = {EditGroup.class})
    private Long onenetWorkerId;

    /**
     * 社工表主键
     */
    @ApiModelProperty(value = "社工表主键", required = true)
    @NotNull(message = "社工表主键不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long socialWorkerId;

    /**
     * 社工二标工作地址
     */
    @ApiModelProperty(value = "社工二标工作地址", required = true)
    @NotBlank(message = "社工二标工作地址不能为空", groups = {AddGroup.class, EditGroup.class})
    private String onenetWorkLocation;

    /**
     * 电话
     */
    @ApiModelProperty(value = "电话", required = true)
    @NotBlank(message = "电话不能为空", groups = {AddGroup.class, EditGroup.class})
    private String onenetTelephone;

    /**
     * 性别
     */
    @ApiModelProperty(value = "性别", required = true)
    @NotBlank(message = "性别不能为空", groups = {AddGroup.class, EditGroup.class})
    private String onenetSex;

    /**
     * 证件号
     */
    @ApiModelProperty(value = "证件号", required = true)
    @NotBlank(message = "证件号不能为空", groups = {AddGroup.class, EditGroup.class})
    private String onenetIdNo;

    /**
     * 证件号类型
     */
    @ApiModelProperty(value = "证件号类型", required = true)
    @NotBlank(message = "证件号类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private String onenetIdType;

    /**
     * 数据json
     */
    @ApiModelProperty(value = "数据json", required = true)
    @NotBlank(message = "数据json不能为空", groups = {AddGroup.class, EditGroup.class})
    private String onenetJson;

    /**
     * $column.columnComment
     */
    @ApiModelProperty(value = "$column.columnComment", required = true)
    @NotBlank(message = "$column.columnComment不能为空", groups = {AddGroup.class, EditGroup.class})
    private String status;

    private String onenetName;

}
