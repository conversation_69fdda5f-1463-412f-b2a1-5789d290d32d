package com.zhonghe.system.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zhonghe.common.annotation.ExcelDictFormat;
import com.zhonghe.common.convert.ExcelDictConvert;
import com.zhonghe.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description:
 * @author: cq
 * @date: 2023/8/3 15:13
 */

@Data
@ApiModel("社区社工")
@ExcelIgnoreUnannotated
public class SlSocialWorkerDto extends BaseEntity {

    /**
     * 社工名称
     */
    @ExcelProperty(value = "姓名")
    @ApiModelProperty("社工名称")
    private String workerName;

    /**
     * 社工年龄
     */
    @ExcelProperty(value = "年龄")
    @ApiModelProperty("社工年龄")
    private Long workerAge;

    /**
     * 社工性别
     */
    @ExcelProperty(value = "性别", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=男,1=女")
    @ApiModelProperty("社工性别")
    private String sex;

    /**
     * 社工身份证号
     */
    @ExcelProperty(value = "身份证号码")
    @ApiModelProperty("社工身份证号")
    private String workerIdNumber;

    /**
     * 社工手机
     */
    @ExcelProperty(value = "联系电话")
    @ApiModelProperty("社工手机")
    private String workerPhone;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    @ApiModelProperty("备注")
    private String mark;

    /**
     * $column.columnComment
     */
    @ExcelProperty(value = "工作地址")
    private String workPosition;

    /**
     * $column.columnComment
     */
    @ExcelProperty(value = "二标地址")
    private String standardPosition;

    @ExcelProperty(value = "所属网格")
    private String gridZoneName;
}

