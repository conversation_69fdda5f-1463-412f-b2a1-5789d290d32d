package com.zhonghe.system.domain.vo.gas;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * @description:
 * @author: cq
 * @date: 2023/7/31 15:26
 */

@ApiModel("燃气分组统计")
@Data
@AllArgsConstructor
public class SlGasRestaurantGatherGroupListVo {

    @ApiModelProperty("问题类型")
    private String name;


    @ApiModelProperty("数量统计")
    private Long count;
}

