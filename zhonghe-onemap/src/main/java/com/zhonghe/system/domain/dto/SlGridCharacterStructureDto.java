package com.zhonghe.system.domain.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zhonghe.common.annotation.ExcelDictFormat;
import com.zhonghe.common.convert.ExcelDictConvert;
import com.zhonghe.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description:
 * @author: cq
 * @date: 2023/8/2 15:29
 */

@Data
@ApiModel("构建网格员信息表")
@ExcelIgnoreUnannotated
public class SlGridCharacterStructureDto extends BaseEntity {

    /**
     * 所属区域名称
     */
    @ExcelProperty(value = "片区")
    @ApiModelProperty("所属区域名称")
    private String characterZoneName;

    @ExcelProperty(value = "职位")
    private String gridStructureName;

    @ExcelProperty(value = "所属网格")
    private String gridName;

    /**
     * 姓名
     */
    @ExcelProperty(value = "姓名")
    @ApiModelProperty("姓名")
    private String characterName;

    /**
     * 性别
     */
    @ExcelProperty(value = "性别" ,converter = ExcelDictConvert.class)
    @ApiModelProperty("性别")
    @ExcelDictFormat(readConverterExp = "0=男,1=女")
    private String characterSex;

    /**
     * 工作手机
     */
    @ExcelProperty(value = "工作电话")
    @ApiModelProperty("工作电话")
    private String characterWorkPhone;

    /**
     * 家庭手机
     */
    @ExcelProperty(value = "手机号码")
    @ApiModelProperty("手机号码")
    private String characterFamilyPhone;

    /**
     * 描述
     */
    @ExcelProperty(value = "备注")
    @ApiModelProperty("备注")
    private String mark;
}

