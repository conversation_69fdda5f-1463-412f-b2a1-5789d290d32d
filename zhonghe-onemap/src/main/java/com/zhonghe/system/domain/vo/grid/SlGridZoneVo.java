package com.zhonghe.system.domain.vo.grid;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zhonghe.common.annotation.ExcelDictFormat;
import com.zhonghe.common.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 网格信息视图对象 sl_grid_zone
 *
 * <AUTHOR>
 * @date 2023-08-02
 */
@Data
@ApiModel("网格信息视图对象")
@ExcelIgnoreUnannotated
public class SlGridZoneVo {

    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    @ExcelProperty(value = "${comment}", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "$column.readConverterExp()")
    @ApiModelProperty("$column.columnComment")
    private Long gridZoneId;

    /**
     * 所属区域名称
     */
    @ExcelProperty(value = "所属区域名称")
    @ApiModelProperty("所属区域名称")
    private String gridGroupName;

    /**
     * 网格名称
     */
    @ExcelProperty(value = "网格名称")
    @ApiModelProperty("网格名称")
    private String gridZoneName;

    /**
     * $column.columnComment
     */
    @ExcelProperty(value = "${comment}", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "$column.readConverterExp()")
    @ApiModelProperty("$column.columnComment")
    private String status;


}
