package com.zhonghe.system.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zhonghe.common.core.domain.BaseEntity;
import com.zhonghe.system.mappedtypes.GeometryTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;
import net.postgis.jdbc.geometry.Geometry;

import java.util.Date;


/**
 * 燃气共享数据坐标对象 sl_gas_onenet
 *
 * <AUTHOR>
 * @date 2023-08-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("business.sl_gas_onenet")
public class SlGasOnenet extends BaseEntity {

    private static final long serialVersionUID = -6518434607170233309L;

    /**
     * $column.columnComment
     */
    @TableId(value = "onenet_gas_id")
    private Long onenetGasId;
    /**
     * 燃气表主键
     */
    private Long gasId;
    /**
     * gas表名称
     */
    private String gasSubjectName;
    /**
     * $column.columnComment
     */
    @TableField(typeHandler = GeometryTypeHandler.class)
    private Geometry gasGem;
    /**
     * $column.columnComment
     */
    private Long fid;
    /**
     * 一网共享数据
     */
    private String onenetJson;
    /**
     * $column.columnComment
     */
    @TableLogic
    private String delFlag;
    /**
     * $column.columnComment
     */
    private String status;

    /**
     * 辖区
     */
    private String sectionName;
    /**
     * 主体名称
     */
    private String subjectName;
    /**
     * 地址
     */
    private String subjectAddress;
    /**
     * 管理人姓名
     */
    private String subjectManager;
    /**
     * 联系电话
     */
    private String subjectPhone;
    /**
     * 网格员姓名
     */
    private String gridManager;
    /**
     * 燃气类型描述：天然气、电、气瓶
     */
    private String powerType;
    /**
     * 燃气类型具体描述
     */
    private String powerTypeMark;
    /**
     * 燃气使用情况
     */
    private String powerStoreMark;
    /**
     * 大气瓶数量
     */
    private Long powerStoreLargeNum;
    /**
     * 小气瓶数量
     */
    private Long powerStoreSmallNum;
    /**
     * 隐患描述、备注
     */
    private String questionMark;
    /**
     * 气瓶隐患
     */
    private String questionGasBottle;
    /**
     * 管道隐患
     */
    private String questionGasPipeline;
    /**
     * 报警器隐患
     */
    private String questionGasWarmingMachine;
    /**
     * 存储隐患
     */
    private String questionGasStoreWeight;
    /**
     * 摆放隐患
     */
    private String questionGasStoreLocation;
    /**
     * 黑气标识
     */
    private String questionGasIllegal;
    /**
     * 是否整改
     */
    private String questionRectification;
    /**
     * 收集时间
     */
    private Date gatherTime;


    private Long powerStoreHugeNum;

    private String questionGasEnvironment;

    private String standardLocation;

}
