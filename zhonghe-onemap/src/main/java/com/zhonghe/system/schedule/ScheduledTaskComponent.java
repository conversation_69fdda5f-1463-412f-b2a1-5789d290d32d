package com.zhonghe.system.schedule;

import cn.hutool.json.JSONUtil;
import com.zhonghe.common.core.domain.dto.BaseMqttMsgDTO;
import com.zhonghe.common.utils.redis.RedisUtils;
import com.zhonghe.common.utils.spring.SpringUtils;
import com.zhonghe.system.auto.AutoRequest;
import com.zhonghe.system.auto.IParamService;
import com.zhonghe.system.auto.dto.MqttAutoRequestDto;
import com.zhonghe.system.auto.info.AutoRequestBean;
import com.zhonghe.system.constants.SlOneMapConstants;
import com.zhonghe.system.service.ISlMarketOnenetService;
import com.zhonghe.system.service.ISlMarketSubjectService;
import com.zhonghe.system.service.ISlWeatherStationOnenetService;
import com.zhonghe.system.service.ISysMqttService;
import com.zhonghe.system.service.IZsjEntregInfoService;
import com.zhonghe.system.util.AlgorithmUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * @description:
 * @author: cq
 * @date: 2023/8/21 15:16
 */
@Component
@AllArgsConstructor
@Slf4j
public class ScheduledTaskComponent {

    private final static Map<String, AutoRequestBean> M = new HashMap<>();

    private final ISlWeatherStationOnenetService weatherStationOnenetService;

    private final ISlMarketSubjectService marketSubjectService;

    private final ISlMarketOnenetService marketOnenetService;

    private final ISysMqttService mqttService;

    private final IZsjEntregInfoService zsjEntregInfoService;

    @Scheduled(fixedRate = 1, timeUnit = TimeUnit.DAYS)
    public void refreshWeatherStationGis() {
        weatherStationOnenetService.updateGis();
    }

    @Scheduled(cron = "0 0 0 * * ?")
    public void refreshMarketGis() {
        zsjEntregInfoService.updateYesterdayGisInformation();
    }

    public void register(String join, Object controller, Method method, AutoRequest autoRequest) {
        if (autoRequest.clazz().equals(AutoRequest.DefaultImpl.class)) {
            M.put(join, new AutoRequestBean(controller, method, 0, autoRequest.frequency(), null));
        } else {
            IParamService paramService = SpringUtils.getBean(autoRequest.clazz());
            M.put(join, new AutoRequestBean(controller, method, 0, autoRequest.frequency(), paramService));
        }
    }

    @Scheduled(fixedRate = 20, timeUnit = TimeUnit.SECONDS)
    public void refresh() {
        long time = System.currentTimeMillis();

        List<BaseMqttMsgDTO> mqttMsg = new ArrayList<>();

        M.forEach((k, v) -> {
            if (time - v.getUpdateTime() >= v.getFrequency()) {
                v.setUpdateTime(time);
                try {
                    Object invoke = v.getMethod().invoke(v.getBean(), v.getService() == null ? null : v.getService().params(v.getMethod().getName()));
                    if (!ObjectUtils.isEmpty(invoke)) {
                        String json = JSONUtil.toJsonStr(invoke);
                        String hash = AlgorithmUtil.calculateHash(json);

                        Object cacheMapValue = RedisUtils.getCacheMapValue(SlOneMapConstants.UPDATE_KEY, k);
                        if (ObjectUtils.isEmpty(cacheMapValue) || (!ObjectUtils.isEmpty(cacheMapValue) && !hash.equals(cacheMapValue.toString()))) {
                            RedisUtils.setCacheMapValue(SlOneMapConstants.UPDATE_KEY, k, hash);
                            MqttAutoRequestDto dto = new MqttAutoRequestDto(k);
                            mqttMsg.add(dto);
                        }
                    }
                } catch (Exception e) {
                    log.error("代理方法异常信息：请求url:{},类型：{},方法名称：{}", k, v.getBean().getClass(), v.getMethod().getName());
                    throw new RuntimeException(e);
                }
            }
            if (!CollectionUtils.isEmpty(mqttMsg)) {
                mqttService.publishBatchMessage(mqttMsg);
            }
        });
    }
}

