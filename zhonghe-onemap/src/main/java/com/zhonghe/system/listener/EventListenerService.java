package com.zhonghe.system.listener;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.system.domain.SlWeatherStationOnenet;
import com.zhonghe.system.domain.thirdparty.vo.SlCaseEventStatisticsVo;
import com.zhonghe.system.domain.vo.grid.SlGridStructureCharacterVo;
import com.zhonghe.system.event.ExChangePatrolOrderEvent;
import com.zhonghe.system.event.ExChangeSpecialPopulationsEvent;
import com.zhonghe.system.event.ExchangeDataBaseEventCaseByAddressEvent;
import com.zhonghe.system.event.ExchangeDataBaseEventCaseByRentalHouseEvent;
import com.zhonghe.system.event.ExchangeDataBaseEventCaseEventByMarketEvent;
import com.zhonghe.system.event.ExchangeDataBaseEventGridPatrolOrderEvent;
import com.zhonghe.system.event.ExchangeDataBaseEventQueryGridLeaderEvent;
import com.zhonghe.system.event.ExchangeDataBaseEventQueryWeatherStation;
import com.zhonghe.system.service.IIntegrationClueService;
import com.zhonghe.system.service.ISlCaseEventProxyService;
import com.zhonghe.system.service.ISlGridStructureCharacterService;
import com.zhonghe.system.service.ISlPatrolOrderService;
import com.zhonghe.system.service.ISlWeatherStationDataService;
import com.zhonghe.system.service.ISpecialPopulationsService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: cq
 * @date: 2023/8/15 11:54
 */

@Service
@Slf4j
@AllArgsConstructor
public class EventListenerService {

    private final ISlCaseEventProxyService caseEventProxyService;

    private final ISlPatrolOrderService slPatrolOrderService;

    private final ISpecialPopulationsService specialPopulationsService;

    private final ISlGridStructureCharacterService gridStructureCharacterService;

    private final ISlWeatherStationDataService weatherStationDataService;

    private final IIntegrationClueService iIntegrationClueService;

    @EventListener
    public void handler(ExchangeDataBaseEventCaseByAddressEvent event) {
        event.getResult().addAll(caseEventProxyService.caseEventStatisticByAddresses(event.getAddresses()));
    }

    @EventListener
    public void handler(ExchangeDataBaseEventCaseByRentalHouseEvent event) {
        if (StringUtils.isNotEmpty(event.getAddress())) {
            event.getVo().setEventCase(iIntegrationClueService.queryEventCaseByAddress(event.getAddress()));
        }
    }

    @EventListener
    public void handler(ExChangePatrolOrderEvent event) {
        if (StringUtils.isNotEmpty(event.getAddress())) {
            slPatrolOrderService.queryByAddress(event.getVo(), event.getAddress());
        }
    }

    @EventListener
    public void handler(ExChangeSpecialPopulationsEvent event) {
        if (StringUtils.isNotEmpty(event.getAddress())) {
            event.getVo().setSpecialist(
                specialPopulationsService.queryByAddress(event.getAddress()));
        }
    }

    @EventListener
    public void handler(ExchangeDataBaseEventGridPatrolOrderEvent event) {
        if (CollectionUtil.isNotEmpty(event.getAddress())) {
            Set<String> conditions = event.getAddress();
            // 按批次查询
            int batchSize = 100;
            List<String> conditionList = new ArrayList<>(conditions);
            Map<String, SlCaseEventStatisticsVo> statistic = new HashMap<>();
            for (int i = 0; i < conditionList.size(); i += batchSize) {
                int fromIndex = i;
                int toIndex = Math.min(i + batchSize, conditionList.size());

                List<String> batch = conditionList.subList(fromIndex, toIndex);

                // 在这里执行查询操作，使用 batch 作为查询条件
                List<SlCaseEventStatisticsVo> slCaseEventStatisticsVos = slPatrolOrderService.queryStatisticBYAddress(batch);
                Map<String, List<SlCaseEventStatisticsVo>> collect = slCaseEventStatisticsVos.stream().collect(Collectors.groupingBy(SlCaseEventStatisticsVo::getName));

                collect.forEach((k, v) -> {
                    if (!statistic.containsKey(k)) {
                        statistic.put(k, new SlCaseEventStatisticsVo(k, 0L));
                    }
                    statistic.get(k).setCount(v.stream().mapToLong(SlCaseEventStatisticsVo::getCount).sum());
                });
            }
            List<SlCaseEventStatisticsVo> vos = new ArrayList<>(statistic.values());
            vos.sort(Comparator.comparing(SlCaseEventStatisticsVo::getCount).reversed());
            event.getResult().addAll(vos);
        }
    }

    @EventListener
    public void handler(ExchangeDataBaseEventCaseEventByMarketEvent event) {
        long count = caseEventProxyService.queryMarkerCaseEventCount();
        event.getVo().setCount(count);
    }

    @EventListener
    public void handler(ExchangeDataBaseEventQueryGridLeaderEvent event) {
        SlGridStructureCharacterVo vo = gridStructureCharacterService.queryGridLeader(event.getGrid());
        if (vo != null) {
            BeanUtil.copyProperties(vo, event.getVo());
        }
    }

    @EventListener
    public void handler(ExchangeDataBaseEventQueryWeatherStation event) {
        List<SlWeatherStationOnenet> stationOnenets = weatherStationDataService.queryNewStation(event.getStationNames());
        if (CollectionUtil.isNotEmpty(stationOnenets)) {
            event.getGeometries().addAll(stationOnenets);
        }
    }
}

