package com.zhonghe.system.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.Method;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.system.domain.dto.OneNetResponseDto;
import com.zhonghe.system.enmu.OneNetConfig;
import com.zhonghe.system.service.IOneNetHttpService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.Map;

/**
 * @description: 请求一网共享数据的接口
 * @author: cq
 * @date: 2023/8/8 14:28
 */

@Service
@Slf4j
public class OneNetHttpServiceImpl implements IOneNetHttpService {

    @Value("${onenet.passId}")
    private String passId;

    @Value("${onenet.token}")
    private String token;

    @Value("${onenet.url}")
    private String url;

    @Value("${onenet.personId}")
    private String id;
    @Value("${onenet.personName}")
    private String name;
    @Value("${onenet.fromIp}")
    private String ip;


    @Override
    public OneNetResponseDto request(OneNetConfig config, String[] values, Map<String, String> optional) {

        if (ObjectUtil.isNull(values) || values.length != config.getKeys().length) {
            throw new RuntimeException("参数长度异常！");
        }

        HttpRequest request = this.create(config.getId());

        try (HttpResponse execute = request.body(this.jsonBody(config.getKeys(), values, optional)).execute()) {
            if (execute.isOk()) {
                String body = execute.body();
                if (StringUtils.isNotEmpty(body)) {
                    try {
                        return JSONUtil.toBean(body, OneNetResponseDto.class);
                    } catch (Exception e) {
                        log.error("转换json失败：{}", body);
                    }
                }
            }
        }

        return null;
    }


    private HttpRequest create(String serviceId) {
        HttpRequest request = new HttpRequest(url);
        long time = System.currentTimeMillis() / 1000;
        String random = RandomUtil.randomStringUpper(5);
        StringBuilder sb = new StringBuilder();
        sb.append(time).append(token).append(random).append(time);

        request.header("Content-Type", "application/json")
            .header("x-tif-paasid", passId)
            .header("x-tif-serviceId", serviceId, true)
            .header("x-tif-timestamp", String.valueOf(time), true)
            .header("x-tif-nonce", random, true)
            .header("x-tif-signature", sha256(sb.toString()), true);

        request.method(Method.POST);
        return request;
    }

    private String sha256(String content) {
        MessageDigest digest = null;
        try {
            digest = MessageDigest.getInstance("SHA-256");
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
        byte[] encodedHash = digest.digest(content.getBytes(StandardCharsets.UTF_8));

        // 将哈希值转换为十六进制字符串
        StringBuilder hexString = new StringBuilder();
        for (byte b : encodedHash) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }

        return hexString.toString().toUpperCase();
    }


    public String jsonBody(String[] keys, String[] vals, Map<String, String> optional) {

        JSONObject jsonObject = new JSONObject();

        jsonObject.set("system_id", passId);
        jsonObject.set("vender_id", "91440101MA9XM0BM38");
        jsonObject.set("department_id", "11441900007331179C");
        jsonObject.set("query_timestamp", System.currentTimeMillis());
        Map<String, String> condition = new HashMap<>(2);
        for (int i = 0; i < keys.length; i++) {
            condition.put(keys[i], vals[i]);
        }
        if (CollectionUtil.isNotEmpty(optional)) {
            condition.putAll(optional);
        }
        jsonObject.set("query", condition);


        Map<String, Object> audit = new HashMap<>(16);
        audit.put("operator_id", id);
        audit.put("operator_name", name);
        audit.put("query_object_id", " 身份证号码");
        audit.put("query_object_id_type", "01");
        audit.put("item_id", "无");
        audit.put("item_code", "无");
        audit.put("item_sequence", "无");
        audit.put("terminal_info", ip);
        audit.put("query_timestamp", System.currentTimeMillis());

        jsonObject.set("audit_info", audit);

        return jsonObject.toJSONString(0);
    }

}

