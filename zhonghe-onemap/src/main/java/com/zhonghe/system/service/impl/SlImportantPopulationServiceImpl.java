package com.zhonghe.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.zhonghe.common.helper.IdGeneratorHelper;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.system.domain.SlImportantPopulation;
import com.zhonghe.system.domain.dto.SlImportantPopulationDto;
import com.zhonghe.system.excel.ExcelToDatabaseSwitch;
import com.zhonghe.system.mapper.SlImportantPopulationMapper;
import com.zhonghe.system.service.ISlImportantPopulationService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

/**
 * 重点优抚对象Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-09-01
 */
@RequiredArgsConstructor
@Service
public class SlImportantPopulationServiceImpl implements ISlImportantPopulationService {

    private final SlImportantPopulationMapper baseMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void load(List<?> result) throws ParseException {
        List<SlImportantPopulation> slImportantPopulations = new ArrayList<>(result.size());
/*        BeanUtil.copyToList(result, SlImportantPopulation.class
        , CopyOptions.create().setIgnoreProperties("brithDate"));*/
        DateFormat df = new SimpleDateFormat("yyyyMMdd");
        for (Object p : result) {
            SlImportantPopulation ip = new SlImportantPopulation();
            BeanUtil.copyProperties(p, ip, "brithDate");

            ip.setPopulationId(IdGeneratorHelper.next());
            if ("同户籍".equals(ip.getFamilyLocation())) {
                if (StringUtils.isNotEmpty(ip.getLivingLocation())) {
                    ip.setFamilyLocation(ip.getLivingLocation());
                } else {
                    ip.setFamilyLocation(ip.getBaseLocation());
                }
            }
            if (!StringUtils.isEmpty(((SlImportantPopulationDto) p).getBrithDate())) {
                ip.setBrithDate(df.parse(((SlImportantPopulationDto) p).getBrithDate()));
            }
            slImportantPopulations.add(ip);
        }

        baseMapper.insertBatch(slImportantPopulations);
    }

    @Override
    public void register() {
        ExcelToDatabaseSwitch.register("ip-pop", SlImportantPopulationDto.class, this.getClass());
    }
}
