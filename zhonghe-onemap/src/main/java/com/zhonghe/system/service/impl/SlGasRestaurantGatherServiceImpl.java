package com.zhonghe.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.helper.IdGeneratorHelper;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.system.constants.SlOneMapConstants;
import com.zhonghe.system.domain.SlGasOnenet;
import com.zhonghe.system.domain.SlGasRestaurantGather;
import com.zhonghe.system.domain.bo.gas.SlGasRestaurantGatherBo;
import com.zhonghe.system.domain.dto.OneNetResponseDto;
import com.zhonghe.system.domain.dto.SlGasRestaurantGatherDto;
import com.zhonghe.system.domain.vo.gas.SlGasOneMapGasBottleVo;
import com.zhonghe.system.domain.vo.gas.SlGasOneMapStatisticsVo;
import com.zhonghe.system.domain.vo.gas.SlGasRestaurantGatherGroupListVo;
import com.zhonghe.system.domain.vo.gas.SlGasRestaurantGatherStatisticVo;
import com.zhonghe.system.domain.vo.gas.SlGasRestaurantGatherVo;
import com.zhonghe.system.enmu.OneNetConfig;
import com.zhonghe.system.excel.ExcelToDatabaseSwitch;
import com.zhonghe.system.excel.service.ILoadExcelService;
import com.zhonghe.system.mapper.SlGasOnenetMapper;
import com.zhonghe.system.mapper.SlGasRestaurantGatherMapper;
import com.zhonghe.system.service.IOneNetHttpService;
import com.zhonghe.system.service.ISlGasRestaurantGatherService;
import lombok.RequiredArgsConstructor;
import net.postgis.jdbc.geometry.Point;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 燃气统计Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-07-28
 */
@RequiredArgsConstructor
@Service
public class SlGasRestaurantGatherServiceImpl implements ISlGasRestaurantGatherService, ILoadExcelService {

    private final SlGasRestaurantGatherMapper baseMapper;

    private final SlGasOnenetMapper gasOnenetMapper;

    private final IOneNetHttpService oneNetHttpService;

    /**
     * 查询燃气统计
     */
    @Override
    public SlGasRestaurantGatherVo queryById(Long gatherGasId) {
        return baseMapper.selectVoById(gatherGasId);
    }


    /**
     * 查询燃气统计列表
     */
    @Override
    public List<SlGasRestaurantGatherVo> queryList(SlGasRestaurantGatherBo bo) throws ParseException {
        LambdaQueryWrapper<SlGasRestaurantGather> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SlGasRestaurantGather> buildQueryWrapper(SlGasRestaurantGatherBo bo) throws ParseException {
        Map<String, Object> params = bo.getParams();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date startDate = StringUtils.isNotEmpty(bo.getStartDate()) ? simpleDateFormat.parse(bo.getStartDate() + "-01") : null;
        Date endDate = StringUtils.isNotEmpty(bo.getStartDate()) ? DateUtil.offset(simpleDateFormat.parse(bo.getEndDate() + "-01"), DateField.MONTH, 1) : null;

        LambdaQueryWrapper<SlGasRestaurantGather> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getSectionName()), SlGasRestaurantGather::getSectionName, bo.getSectionName());
        lqw.like(StringUtils.isNotBlank(bo.getSubjectName()), SlGasRestaurantGather::getSubjectName, bo.getSubjectName());
        lqw.eq(StringUtils.isNotBlank(bo.getSubjectAddress()), SlGasRestaurantGather::getSubjectAddress, bo.getSubjectAddress());
        lqw.eq(StringUtils.isNotBlank(bo.getSubjectManager()), SlGasRestaurantGather::getSubjectManager, bo.getSubjectManager());
        lqw.eq(StringUtils.isNotBlank(bo.getSubjectPhone()), SlGasRestaurantGather::getSubjectPhone, bo.getSubjectPhone());
        lqw.eq(StringUtils.isNotBlank(bo.getGridManager()), SlGasRestaurantGather::getGridManager, bo.getGridManager());
        lqw.eq(StringUtils.isNotBlank(bo.getPowerType()), SlGasRestaurantGather::getPowerType, bo.getPowerType());
        lqw.eq(StringUtils.isNotBlank(bo.getPowerTypeMark()), SlGasRestaurantGather::getPowerTypeMark, bo.getPowerTypeMark());
        lqw.eq(StringUtils.isNotBlank(bo.getPowerStoreMark()), SlGasRestaurantGather::getPowerStoreMark, bo.getPowerStoreMark());
        lqw.eq(bo.getPowerStoreLargeNum() != null, SlGasRestaurantGather::getPowerStoreLargeNum, bo.getPowerStoreLargeNum());
        lqw.eq(bo.getPowerStoreSmallNum() != null, SlGasRestaurantGather::getPowerStoreSmallNum, bo.getPowerStoreSmallNum());
        lqw.eq(StringUtils.isNotBlank(bo.getQuestionMark()), SlGasRestaurantGather::getQuestionMark, bo.getQuestionMark());
        lqw.eq(StringUtils.isNotBlank(bo.getQuestionGasBottle()), SlGasRestaurantGather::getQuestionGasBottle, bo.getQuestionGasBottle());
        lqw.eq(StringUtils.isNotBlank(bo.getQuestionGasPipeline()), SlGasRestaurantGather::getQuestionGasPipeline, bo.getQuestionGasPipeline());
        lqw.eq(StringUtils.isNotBlank(bo.getQuestionGasWarmingMachine()), SlGasRestaurantGather::getQuestionGasWarmingMachine, bo.getQuestionGasWarmingMachine());
        lqw.eq(StringUtils.isNotBlank(bo.getQuestionGasStoreWeight()), SlGasRestaurantGather::getQuestionGasStoreWeight, bo.getQuestionGasStoreWeight());
        lqw.eq(StringUtils.isNotBlank(bo.getQuestionGasStoreLocation()), SlGasRestaurantGather::getQuestionGasStoreLocation, bo.getQuestionGasStoreLocation());
        lqw.eq(StringUtils.isNotBlank(bo.getQuestionGasIllegal()), SlGasRestaurantGather::getQuestionGasIllegal, bo.getQuestionGasIllegal());
        lqw.eq(StringUtils.isNotBlank(bo.getQuestionRectification()), SlGasRestaurantGather::getQuestionRectification, bo.getQuestionRectification());
        lqw.eq(StringUtils.isNotBlank(bo.getGatherTime()), SlGasRestaurantGather::getGatherTime, bo.getGatherTime());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), SlGasRestaurantGather::getStatus, bo.getStatus());
        lqw.lt(ObjectUtil.isNotNull(endDate), SlGasRestaurantGather::getGatherTime, endDate);
        lqw.ge(ObjectUtil.isNotNull(startDate), SlGasRestaurantGather::getGatherTime, startDate);
        lqw.and(StringUtils.isNotEmpty(bo.getKeyWord()), qw -> {
            qw
                .like(SlGasRestaurantGather::getSubjectAddress, bo.getKeyWord())
                .or()
                .like(SlGasRestaurantGather::getSubjectName, bo.getKeyWord())
                /*.or()
                .like(SlGasRestaurantGather::getStandardLocation, bo.getKeyWord())
                .or()
                .like(SlGasRestaurantGather::getGridManager, bo.getKeyWord())
                .or()
                .like(SlGasRestaurantGather::getSubjectManager, bo.getKeyWord())*/
            ;
        });
        lqw.orderByDesc(SlGasRestaurantGather::getGatherGasId);
        return lqw;
    }

    /**
     * 新增燃气统计
     */
    @Override
    public Boolean insertByBo(SlGasRestaurantGatherBo bo) {
        SlGasRestaurantGather add = BeanUtil.toBean(bo, SlGasRestaurantGather.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setGatherGasId(add.getGatherGasId());
        }
        return flag;
    }

    /**
     * 修改燃气统计
     */
    @Override
    public Boolean updateByBo(SlGasRestaurantGatherBo bo) {
        SlGasRestaurantGather update = BeanUtil.toBean(bo, SlGasRestaurantGather.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SlGasRestaurantGather entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除燃气统计
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public SlGasRestaurantGatherStatisticVo statistic() {
        SlGasRestaurantGatherStatisticVo vo = new SlGasRestaurantGatherStatisticVo();

        Long count = baseMapper.selectCount(new LambdaQueryWrapper<>());
        Long illegalGasCount = baseMapper.selectCount(new LambdaQueryWrapper<SlGasRestaurantGather>()
            .eq(SlGasRestaurantGather::getQuestionGasEnvironment, SlOneMapConstants.OK)
            .eq(SlGasRestaurantGather::getQuestionGasIllegal, SlOneMapConstants.OK)
            .eq(SlGasRestaurantGather::getQuestionGasPipeline, SlOneMapConstants.OK)
            .eq(SlGasRestaurantGather::getQuestionGasStoreWeight, SlOneMapConstants.OK)
            .eq(SlGasRestaurantGather::getQuestionGasBottle, SlOneMapConstants.OK)
            .eq(SlGasRestaurantGather::getQuestionGasStoreLocation, SlOneMapConstants.OK)
            .eq(SlGasRestaurantGather::getQuestionGasWarmingMachine, SlOneMapConstants.OK)
        );
        Long unRectificationCount = baseMapper.selectCount(new LambdaQueryWrapper<SlGasRestaurantGather>()
            .eq(SlGasRestaurantGather::getPowerType, SlOneMapConstants.POWER_TYPE_ELECTRIC));

        vo.setCount(count);
        vo.setIllegalGasCount(illegalGasCount);
        vo.setUnRectificationCount(unRectificationCount);

        return vo;
    }

    @Override
    public List<SlGasRestaurantGatherGroupListVo> groupList(SlGasRestaurantGatherBo bo) throws ParseException {
        List<SlGasRestaurantGatherGroupListVo> result = new ArrayList<>();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date startDate = StringUtils.isNotEmpty(bo.getStartDate()) ? simpleDateFormat.parse(bo.getStartDate() + "-01") : null;
        Date endDate = StringUtils.isNotEmpty(bo.getStartDate()) ? DateUtil.offset(simpleDateFormat.parse(bo.getEndDate() + "-01"), DateField.MONTH, 1) : null;

        // 1.气瓶隐患（黑气）
        Long questionGasBottle = baseMapper.selectCount(new LambdaQueryWrapper<SlGasRestaurantGather>()
            .eq(SlGasRestaurantGather::getQuestionGasBottle, SlOneMapConstants.GAS_BOTTLE_QUESTION)
            .like(StringUtils.isNotEmpty(bo.getGatherTime()), SlGasRestaurantGather::getGatherTime, bo.getGatherTime())
            .lt(ObjectUtil.isNotNull(endDate), SlGasRestaurantGather::getGatherTime, endDate)
            .ge(ObjectUtil.isNotNull(startDate), SlGasRestaurantGather::getGatherTime, startDate)
        );
        result.add(new SlGasRestaurantGatherGroupListVo
            (SlOneMapConstants.GAS_BOTTLE_QUESTION_CN, questionGasBottle));

        //2.管道隐患（文字描述）
        Long questionGasPipeline = baseMapper.selectCount(new LambdaQueryWrapper<SlGasRestaurantGather>()
            .eq(SlGasRestaurantGather::getQuestionGasPipeline, SlOneMapConstants.GAS_PIPELINE_QUESTION)
            .like(StringUtils.isNotEmpty(bo.getGatherTime()), SlGasRestaurantGather::getGatherTime, bo.getGatherTime())
            .lt(ObjectUtil.isNotNull(endDate), SlGasRestaurantGather::getGatherTime, endDate)
            .ge(ObjectUtil.isNotNull(startDate), SlGasRestaurantGather::getGatherTime, startDate));
        result.add(new SlGasRestaurantGatherGroupListVo
            (SlOneMapConstants.GAS_PIPELINE_QUESTION_CN, questionGasPipeline));

        //3.报警器隐患（是否安装、可用）
        Long questionGasMachine = baseMapper.selectCount(new LambdaQueryWrapper<SlGasRestaurantGather>()
            .eq(SlGasRestaurantGather::getQuestionGasWarmingMachine, SlOneMapConstants.GAS_MACHINE_QUESTION)
            .like(StringUtils.isNotEmpty(bo.getGatherTime()), SlGasRestaurantGather::getGatherTime, bo.getGatherTime())
            .lt(ObjectUtil.isNotNull(endDate), SlGasRestaurantGather::getGatherTime, endDate)
            .ge(ObjectUtil.isNotNull(startDate), SlGasRestaurantGather::getGatherTime, startDate));
        result.add(new SlGasRestaurantGatherGroupListVo
            (SlOneMapConstants.GAS_MACHINE_QUESTION_CN, questionGasMachine));
        //4.气瓶存储隐患(总重量)
        Long questionGasStore = baseMapper.selectCount(new LambdaQueryWrapper<SlGasRestaurantGather>()
            .eq(SlGasRestaurantGather::getQuestionGasStoreWeight, SlOneMapConstants.GAS_STORE_QUESTION)
            .like(StringUtils.isNotEmpty(bo.getGatherTime()), SlGasRestaurantGather::getGatherTime, bo.getGatherTime())
            .lt(ObjectUtil.isNotNull(endDate), SlGasRestaurantGather::getGatherTime, endDate)
            .ge(ObjectUtil.isNotNull(startDate), SlGasRestaurantGather::getGatherTime, startDate));
        result.add(new SlGasRestaurantGatherGroupListVo
            (SlOneMapConstants.GAS_STORE_QUESTION_CN, questionGasStore));

        //5.摆放隐患（文字描述）
        Long questionGasLocation = baseMapper.selectCount(new LambdaQueryWrapper<SlGasRestaurantGather>()
            .eq(SlGasRestaurantGather::getQuestionGasStoreLocation, SlOneMapConstants.GAS_LOCATION_QUESTION)
            .like(StringUtils.isNotEmpty(bo.getGatherTime()), SlGasRestaurantGather::getGatherTime, bo.getGatherTime())
            .lt(ObjectUtil.isNotNull(endDate), SlGasRestaurantGather::getGatherTime, endDate)
            .ge(ObjectUtil.isNotNull(startDate), SlGasRestaurantGather::getGatherTime, startDate));
        result.add(new SlGasRestaurantGatherGroupListVo
            (SlOneMapConstants.GAS_LOCATION_QUESTION_CN, questionGasLocation));

        //6.用气环境隐患
        Long questionGasEnvironment = baseMapper.selectCount(new LambdaQueryWrapper<SlGasRestaurantGather>()
            .eq(SlGasRestaurantGather::getQuestionGasEnvironment, SlOneMapConstants.GAS_ENVIRONMENT_QUESTION)
            .like(StringUtils.isNotEmpty(bo.getGatherTime()), SlGasRestaurantGather::getGatherTime, bo.getGatherTime())
            .lt(ObjectUtil.isNotNull(endDate), SlGasRestaurantGather::getGatherTime, endDate)
            .ge(ObjectUtil.isNotNull(startDate), SlGasRestaurantGather::getGatherTime, startDate));
        result.add(new SlGasRestaurantGatherGroupListVo
            (SlOneMapConstants.GAS_ENVIRONMENT_QUESTION_CN, questionGasEnvironment));

        return result;
    }

    @Override
    public TableDataInfo<SlGasRestaurantGatherVo> queryPageList(SlGasRestaurantGatherBo bo, PageQuery pageQuery) throws ParseException {
        LambdaQueryWrapper<SlGasRestaurantGather> queryWrapper = this.buildQueryWrapper(bo);
        IPage<SlGasRestaurantGatherVo> pageData = baseMapper.selectVoPage(pageQuery.build(), queryWrapper);
        return TableDataInfo.build(pageData);
    }

    @Override
    public SlGasOneMapStatisticsVo onemapUnitStatistics() {
        List<SlGasRestaurantGather> restaurantGathers = baseMapper.selectList();
        SlGasOneMapStatisticsVo vo = new SlGasOneMapStatisticsVo();
        vo.setGasUnitCount(restaurantGathers.size());

        long legalCount = restaurantGathers.stream().filter(a -> SlOneMapConstants.OK.equals(a.getQuestionGasStoreWeight())
            &&
            SlOneMapConstants.OK.equals(a.getQuestionGasEnvironment())
            &&
            SlOneMapConstants.OK.equals(a.getQuestionGasBottle())
            &&
            SlOneMapConstants.OK.equals(a.getQuestionGasIllegal())
            &&
            SlOneMapConstants.OK.equals(a.getQuestionGasWarmingMachine())
            &&
            SlOneMapConstants.OK.equals(a.getQuestionGasPipeline())).count();

        vo.setGasLegalUnitCount(legalCount);

        long gasTypeCount = restaurantGathers.stream().filter(a -> SlOneMapConstants.POWER_TYPE_ELECTRIC.equals(a.getPowerType())).count();
        vo.setNotGasPowerUnitCount(gasTypeCount);

        List<SlGasOneMapGasBottleVo> gasBottles = new ArrayList<>(4);

        long ctHuge = 0;
        long ctBig = 0;
        long ctLitter = 0;
        for (SlGasRestaurantGather restaurantGather : restaurantGathers) {
            if (ObjectUtil.isNotNull(restaurantGather.getPowerStoreHugeNum())) {
                ctHuge += restaurantGather.getPowerStoreHugeNum();
            }
            if (ObjectUtil.isNotNull(restaurantGather.getPowerStoreLargeNum())) {
                ctBig += restaurantGather.getPowerStoreLargeNum();
            }
            if (ObjectUtil.isNotNull(restaurantGather.getPowerStoreSmallNum())) {
                ctLitter += restaurantGather.getPowerStoreSmallNum();
            }
        }
        gasBottles.add(new SlGasOneMapGasBottleVo("特大瓶", ctHuge));
        gasBottles.add(new SlGasOneMapGasBottleVo("大瓶", ctBig));
        gasBottles.add(new SlGasOneMapGasBottleVo("小瓶", ctLitter));

        vo.setGasBottles(gasBottles);

        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void refreshOnenet() {
        List<SlGasRestaurantGather> restaurantGathers = baseMapper.selectList();
        this.refresh(restaurantGathers);
        baseMapper.updateBatchById(restaurantGathers);
    }

    private void refresh(List<SlGasRestaurantGather> restaurantGathers) {

        List<SlGasOnenet> result = new ArrayList<>(restaurantGathers.size());
        List<Long> deleteIds = new ArrayList<>();
        restaurantGathers.forEach(a -> {

            if (StringUtils.isNotEmpty(a.getSubjectAddress())) {
                OneNetResponseDto request = oneNetHttpService.request(OneNetConfig.ADDRESS, new String[]{a.getSubjectAddress()}, null);

                if (ObjectUtil.isNotNull(request)) {
                    List<Map<String, String>> data = request.getData().getData();
                    SlGasOnenet gasOnenet = new SlGasOnenet();
                    BeanUtil.copyProperties(a, gasOnenet);
                    gasOnenet.setGasId(a.getGatherGasId());
                    gasOnenet.setOnenetGasId(IdGeneratorHelper.next());
                    gasOnenet.setGasSubjectName(a.getSubjectName());
                    gasOnenet.setOnenetJson(JSONUtil.toJsonStr(data));
                    if (CollectionUtil.isNotEmpty(data)) {
                        deleteIds.add(a.getGatherGasId());
                        Map<String, String> onenet = OneNetConfig.ADDRESS.getSelector().select(data, "GXSJ");
                        if (CollectionUtil.isNotEmpty(onenet)) {
                            a.setStandardLocation(onenet.getOrDefault("DZQC", null));
                            String jydwzxjd = onenet.get("ZXJD");
                            String jydwzxwd = onenet.get("ZXWD");
                            if (StringUtils.isNotEmpty(jydwzxjd) && StringUtils.isNotEmpty(jydwzxwd)) {
                                Point pt = new Point(Double.parseDouble(jydwzxjd), Double.parseDouble(jydwzxwd));
                                gasOnenet.setGasGem(pt);
                            }
                        }
                    }
                    result.add(gasOnenet);
                }
            }
        });
        if (CollectionUtil.isNotEmpty(deleteIds)) {
            gasOnenetMapper.delete(new LambdaQueryWrapper<SlGasOnenet>().in(SlGasOnenet::getGasId, deleteIds));
        }
        gasOnenetMapper.insertBatch(result);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void load(List<?> result) {
        List<SlGasRestaurantGather> restaurantGathers = BeanUtil.copyToList(result, SlGasRestaurantGather.class);
        restaurantGathers.forEach(d -> {
            d.setGatherGasId(IdGeneratorHelper.next());
            if (!ObjectUtil.equal(d.getQuestionGasStoreWeight(), SlOneMapConstants.GAS_STORE_QUESTION)) {
                long total = 0;
                total += ObjectUtil.isNotNull(d.getPowerStoreHugeNum()) ? d.getPowerStoreHugeNum() * 50 : 0;
                total += ObjectUtil.isNotNull(d.getPowerStoreLargeNum()) ? d.getPowerStoreLargeNum() * 15 : 0;
                total += ObjectUtil.isNotNull(d.getPowerStoreSmallNum()) ? d.getPowerStoreSmallNum() * 5 : 0;

                if (total > 100) {
                    d.setQuestionGasStoreWeight(SlOneMapConstants.GAS_STORE_QUESTION);
                }
            }
            switch (d.getPowerType()) {
                case "1":
                    d.setPowerTypeMark("储存瓶");
                    break;
                case "2":
                    d.setPowerTypeMark("天然气");
                    break;
                case "3":
                    d.setPowerTypeMark("电");
                    break;
                case "4":
                    d.setPowerTypeMark("混合");
                    break;
                case "5":
                    d.setPowerTypeMark("倒闭");
                    break;
                default:
                    d.setPowerTypeMark("未知");
                    break;
            }
        });
        baseMapper.insertBatch(restaurantGathers);
        this.refresh(restaurantGathers);
    }

    @Override
    public void register() {
        ExcelToDatabaseSwitch.register(new ExcelToDatabaseSwitch("gas", SlGasRestaurantGatherDto.class, SlGasRestaurantGatherServiceImpl.class));
    }
}
