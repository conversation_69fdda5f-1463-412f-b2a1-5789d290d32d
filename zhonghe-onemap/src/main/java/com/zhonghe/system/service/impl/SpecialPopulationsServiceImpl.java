package com.zhonghe.system.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.zhonghe.system.domain.thirdparty.vo.SpecialistVo;
import com.zhonghe.system.mapper.SlCountrySubsistenceAllowanceInfoMapper;
import com.zhonghe.system.mapper.SlImportantPopulationMapper;
import com.zhonghe.system.mapper.SlOldPopulationMapper;
import com.zhonghe.system.mapper.SlSpecialPopulationMapper;
import com.zhonghe.system.mapper.T2031000000026000003V2TbwMapper;
import com.zhonghe.system.mapper.T2031000000026000005V1Mapper;
import com.zhonghe.system.mapper.T3070220000088000190V3UomMapper;
import com.zhonghe.system.service.ISpecialPopulationsService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @description:
 * @author: cq
 * @date: 2023/8/18 10:03
 */

@Service
@Slf4j
@AllArgsConstructor
@DS("data")
public class SpecialPopulationsServiceImpl implements ISpecialPopulationsService {

    private final SlImportantPopulationMapper importantPopulationMapper;
    //private final SlCitySubsistenceAllowanceInfoMapper citySubsistenceAllowanceInfoMapper;

    private final SlCountrySubsistenceAllowanceInfoMapper countrySubsistenceAllowanceInfoMapper;

    private final T3070220000088000190V3UomMapper disablePopulationMapper;

    private final SlOldPopulationMapper oldPopulationMapper;

    private final SlSpecialPopulationMapper specialPopulationMapper;

    private final T2031000000026000003V2TbwMapper citySubsistenceAllowanceInfoMapper;

    private final T2031000000026000005V1Mapper queryCityFamilyMapper;

    @Override
    public List<SpecialistVo> queryByAddress(String address) {

        List<SpecialistVo> city = citySubsistenceAllowanceInfoMapper.queryByAddress(address);
        List<SpecialistVo> cityFamily = queryCityFamilyMapper.queryByAddress(address);
        List<SpecialistVo> country = countrySubsistenceAllowanceInfoMapper.queryByAddress(address);
        List<SpecialistVo> important = importantPopulationMapper.queryByAddress(address);
        List<SpecialistVo> disables = disablePopulationMapper.queryByAddress(address);
        List<SpecialistVo> oldMan = oldPopulationMapper.queryAddress(address);
        List<SpecialistVo> specials = specialPopulationMapper.queryAddress(address);
        List<SpecialistVo> result = new ArrayList<>();

        result.addAll(city);
        result.addAll(country);
        result.addAll(important);
        result.addAll(disables);
        result.addAll(oldMan);
        result.addAll(specials);
        result.addAll(cityFamily);

        return result;
    }
}

