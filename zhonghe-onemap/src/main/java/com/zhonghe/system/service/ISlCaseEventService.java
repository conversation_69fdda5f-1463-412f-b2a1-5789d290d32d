package com.zhonghe.system.service;

import com.zhonghe.system.domain.bo.SlCaseEventBo;
import com.zhonghe.system.domain.vo.SlCaseEventVo;

import java.util.Collection;
import java.util.List;

/**
 * 案时间Service接口
 *
 * <AUTHOR>
 * @date 2023-08-14
 */
public interface ISlCaseEventService {

    /**
     * 查询案时间
     */
    SlCaseEventVo queryById(Long caseEventId);


    /**
     * 查询案时间列表
     */
    List<SlCaseEventVo> queryList(SlCaseEventBo bo);

    /**
     * 修改案时间
     */
    Boolean insertByBo(SlCaseEventBo bo);

    /**
     * 修改案时间
     */
    Boolean updateByBo(SlCaseEventBo bo);

    /**
     * 校验并批量删除案时间信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    Boolean refresh();
}
