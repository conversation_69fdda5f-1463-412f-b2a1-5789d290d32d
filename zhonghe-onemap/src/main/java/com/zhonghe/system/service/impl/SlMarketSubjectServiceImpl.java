package com.zhonghe.system.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.helper.IdGeneratorHelper;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.system.constants.SlOneMapConstants;
import com.zhonghe.system.domain.SlMarketOnenet;
import com.zhonghe.system.domain.dto.OneNetResponseDto;
import com.zhonghe.system.domain.thirdparty.Dgsscztxx;
import com.zhonghe.system.domain.thirdparty.SlCaseEventProxy;
import com.zhonghe.system.domain.thirdparty.SlGridPatrolOrder;
import com.zhonghe.system.domain.thirdparty.TaskOrderNew;
import com.zhonghe.system.domain.thirdparty.ZsjEntregInfo;
import com.zhonghe.system.domain.thirdparty.bo.DgsscztxxBo;
import com.zhonghe.system.domain.thirdparty.vo.IntegrationClueVo;
import com.zhonghe.system.domain.thirdparty.vo.SlCaseEventStatisticsVo;
import com.zhonghe.system.domain.thirdparty.vo.ZsjEntregInfoVo;
import com.zhonghe.system.domain.vo.MarketSubjectDetailVo;
import com.zhonghe.system.domain.vo.SlMarketSubjectStatisticsVo;
import com.zhonghe.system.enmu.OneNetConfig;
import com.zhonghe.system.mapper.DgsscztxxMapper;
import com.zhonghe.system.mapper.TaskOrderNewMapper;
import com.zhonghe.system.mapper.ZsjEntregInfoMapper;
import com.zhonghe.system.service.IIntegrationClueService;
import com.zhonghe.system.service.IOneNetHttpService;
import com.zhonghe.system.service.ISlMarketSubjectService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.postgis.jdbc.geometry.Geometry;
import net.postgis.jdbc.geometry.Point;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @description:
 * @author: cq
 * @date: 2023/8/14 15:27
 */

@Service
@Slf4j
@AllArgsConstructor
@DS("data")
public class SlMarketSubjectServiceImpl implements ISlMarketSubjectService {

    private final DgsscztxxMapper dgsscztxxMapper;

    private final TaskOrderNewMapper taskOrderNewMapper;

    private final ZsjEntregInfoMapper zsjEntregInfoMapper;

    private final IOneNetHttpService httpService;

    private final IIntegrationClueService iIntegrationClueService;

    @Override
    public SlMarketSubjectStatisticsVo marketStatistics() {
        SlMarketSubjectStatisticsVo vo = new SlMarketSubjectStatisticsVo();

        Long count = zsjEntregInfoMapper.selectCount(new LambdaQueryWrapper<>());
        vo.setCount(count);
        // 个体户
        Long personalCount = zsjEntregInfoMapper.selectCount(new LambdaQueryWrapper<ZsjEntregInfo>().eq(ZsjEntregInfo::getTypename, "个体工商户"));
        vo.setPersonalCount(personalCount);
        // 从业人数
        Long slaverCount = zsjEntregInfoMapper.selectSlaverCount();
        vo.setSlaverCount(slaverCount);
        // 企业
        Long enterpriseCount = zsjEntregInfoMapper.selectCount(new LambdaQueryWrapper<ZsjEntregInfo>().ne(ZsjEntregInfo::getTypename, "个体工商户"));
        vo.setEnterpriseCount(enterpriseCount);

        return vo;
    }

    @Override
    public List<SlCaseEventStatisticsVo> marketEventStatistic() {

        List<SlCaseEventStatisticsVo> marketEvent = taskOrderNewMapper.marketEventStatistics();

        if (CollectionUtil.isNotEmpty(marketEvent) && marketEvent.size() > 3) {

            List<SlCaseEventStatisticsVo> result = new ArrayList<>();
            // 1.取前三 后面的按其它统计
            for (int i = 0; i < SlOneMapConstants.TOP_THREE; i++) {
                result.add(marketEvent.get(i));
            }
            // 2.其它的统计全部
            long total = 0;
            for (int i = SlOneMapConstants.TOP_THREE; i < marketEvent.size(); i++) {
                total += marketEvent.get(i).getCount();
            }
            result.add(new SlCaseEventStatisticsVo(SlOneMapConstants.OTHER, total));
            return result;
        }
        return marketEvent;
    }


    @Override
    public TableDataInfo<ZsjEntregInfoVo> page(PageQuery pageQuery, DgsscztxxBo bo) {
        LambdaQueryWrapper<ZsjEntregInfo> filter = new LambdaQueryWrapper<ZsjEntregInfo>().and(StringUtils.isNotEmpty(bo.getKeyWord()), qw -> {
            qw.like(ZsjEntregInfo::getEntname, bo.getKeyWord())
                .or()
                .like(ZsjEntregInfo::getLerepname, bo.getKeyWord())
                .or()
                .like(ZsjEntregInfo::getDom, bo.getKeyWord())
                .or()
                .like(ZsjEntregInfo::getUniscid, bo.getKeyWord())
                .or()
                .like(ZsjEntregInfo::getOpscope, bo.getKeyWord());
        });

        if ("1".equals(bo.getQueryType())) {
            filter.eq(ZsjEntregInfo::getTypename, "个体工商户");
        } else if ("2".equals(bo.getQueryType())) {
            filter.ne(ZsjEntregInfo::getTypename, "个体工商户");
        } else {

        }

        IPage<ZsjEntregInfoVo> dgsscztxxVoIPage = zsjEntregInfoMapper.selectVoPage(pageQuery.build(), filter);

        dgsscztxxVoIPage.getRecords().forEach(a -> a.setId(a.getPripid()));

        return TableDataInfo.build(dgsscztxxVoIPage);
    }

    @Override
    public MarketSubjectDetailVo detail(String zj) {
        MarketSubjectDetailVo vo = new MarketSubjectDetailVo();

        ZsjEntregInfo dgsscztxx = zsjEntregInfoMapper.selectOne(new LambdaQueryWrapper<ZsjEntregInfo>().eq(ZsjEntregInfo::getPripid, zj));

        vo.setCreditCode(dgsscztxx.getUniscid());
        vo.setName(dgsscztxx.getEntname());
        vo.setLegalContract(dgsscztxx.getLerepname());
        vo.setAddress(dgsscztxx.getDom());

        // 案事件
        if (StringUtils.isNotEmpty(dgsscztxx.getDom())) {
            List<IntegrationClueVo> integrationClueVos = iIntegrationClueService.selectListByAddress(dgsscztxx.getDom());
            List<SlCaseEventProxy> slCaseEventProxies = new ArrayList<>(integrationClueVos.size());
            DateFormat df = new SimpleDateFormat("yyyy-MM-dd 00:00:00.000");
            integrationClueVos.forEach(i -> {
                SlCaseEventProxy p = new SlCaseEventProxy();

                p.setYhms(i.getDangerDescribe());
                p.setCzbmbm(i.getDisposalDeptName());
                if (ObjectUtil.isNotNull(i.getVerTerm())) {
                    p.setCzqx(df.format(i.getVerTerm()));
                }
                p.setScsj(i.getCreateDate());
                slCaseEventProxies.add(p);
            });

            vo.setEventCase(slCaseEventProxies);
            //  修改为新版数据库的结构
            List<TaskOrderNew> orderNews = taskOrderNewMapper.selectList(new LambdaQueryWrapper<TaskOrderNew>().eq(TaskOrderNew::getBuildAddress, dgsscztxx.getDom()).orderByAsc(TaskOrderNew::getUpdateDate));

            List<SlGridPatrolOrder> ztid = new ArrayList<>(orderNews.size());
            DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            for (TaskOrderNew orderNew : orderNews) {
                SlGridPatrolOrder order = new SlGridPatrolOrder();
                // 属性填充
                if (ObjectUtil.isNotNull(orderNew.getCheckEndDate())) {
                    order.setXcqx(dateFormat.format(orderNew.getCheckEndDate()));
                }
                if (ObjectUtil.isNotNull(orderNew.getCreateDate())) {
                    order.setCjsj(dateFormat.format(orderNew.getCreateDate()));
                }
                order.setBmmc(orderNew.getDeptName());
                order.setZtmc(orderNew.formatCheckState());
                order.setSxflmc(orderNew.getItemClassName());
                order.setZtlx(orderNew.formatCheckState());
                order.setXczt(orderNew.formatCheckState());
                order.setZj(orderNew.getZjid());

                ztid.add(order);
            }
            vo.setPatrolOrders(ztid);
        }
        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<SlMarketOnenet> updateMarketGis(List<Long> markerZjs) {
        List<Dgsscztxx> dgsscztxxes = dgsscztxxMapper.selectList(new QueryWrapper<Dgsscztxx>()
            .notIn(CollectionUtil.isNotEmpty(markerZjs), "zj", markerZjs));


        List<SlMarketOnenet> geometry = new ArrayList<>(dgsscztxxes.size());

        dgsscztxxes.forEach(d -> {
            if (StringUtils.isNotEmpty(d.getCs())) {
                OneNetResponseDto request = httpService.request(OneNetConfig.ADDRESS, new String[]{d.getCs()}, null);
                if (null != request) {
                    List<Map<String, String>> data = request.getData().getData();
                    Map<String, String> gxsj = OneNetConfig.ADDRESS.getSelector().select(data, "GXSJ");
                    if (CollectionUtil.isNotEmpty(gxsj)) {
                        SlMarketOnenet onenet = new SlMarketOnenet();
                        onenet.setMarketId(IdGeneratorHelper.next());
                        onenet.setMarketDataId(String.valueOf(d.getZj()));

                        String zxjd = gxsj.get("ZXJD");
                        String zxwd = gxsj.get("ZXWD");
                        if (StringUtils.isNotEmpty(zxjd) && StringUtils.isNotEmpty(zxwd)) {
                            Geometry geometry1 = new Point(Double.parseDouble(zxjd), Double.parseDouble(zxwd));
                            onenet.setMarketGem(geometry1);
                        }
                        onenet.setCreateBy("schedule");
                        onenet.setUpdateBy("schedule");
                        geometry.add(onenet);
                    }
                }
            }
        });

        return geometry;
    }
}

