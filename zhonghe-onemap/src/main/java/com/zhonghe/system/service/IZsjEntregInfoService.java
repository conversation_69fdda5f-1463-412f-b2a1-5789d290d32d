package com.zhonghe.system.service;

import com.zhonghe.system.domain.thirdparty.bo.ZsjEntregInfoBo;
import com.zhonghe.system.domain.thirdparty.vo.ZsjEntregInfoVo;

import java.util.Collection;
import java.util.List;

/**
 * 【请填写功能名称】Service接口
 *
 * <AUTHOR>
 * @date 2023-09-11
 */
public interface IZsjEntregInfoService {

    /**
     * 查询【请填写功能名称】
     */
    ZsjEntregInfoVo queryById(Long id);


    /**
     * 查询【请填写功能名称】列表
     */
    List<ZsjEntregInfoVo> queryList(ZsjEntregInfoBo bo);

    /**
     * 修改【请填写功能名称】
     */
    Boolean insertByBo(ZsjEntregInfoBo bo);

    /**
     * 修改【请填写功能名称】
     */
    Boolean updateByBo(ZsjEntregInfoBo bo);

    /**
     * 校验并批量删除【请填写功能名称】信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    void refresh();

    /**
     * 更新每天的地理数据
     */
    void updateYesterdayGisInformation();
}
