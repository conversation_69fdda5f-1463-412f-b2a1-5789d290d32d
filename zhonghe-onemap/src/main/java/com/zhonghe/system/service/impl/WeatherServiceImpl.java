package com.zhonghe.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.system.domain.dto.OneNetResponseDto;
import com.zhonghe.system.domain.thirdparty.bo.SlWeatherBo;
import com.zhonghe.system.domain.thirdparty.vo.SlStationWeatherVo;
import com.zhonghe.system.domain.thirdparty.vo.SlWeatherStatisticVo;
import com.zhonghe.system.enmu.OneNetConfig;
import com.zhonghe.system.service.IOneNetHttpService;
import com.zhonghe.system.service.ISlWeatherStationOnenetService;
import com.zhonghe.system.service.IWeatherService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @description:
 * @author: cq
 * @date: 2023/8/21 16:44
 */

@Service
@AllArgsConstructor
@Slf4j
public class WeatherServiceImpl implements IWeatherService {

    private final IOneNetHttpService httpService;

    private final ISlWeatherStationOnenetService weatherStationOnenetService;

    @Override
    public List<SlWeatherStatisticVo> query(SlWeatherBo bo) throws ParseException {
        List<SlWeatherStatisticVo> vo = new ArrayList<>();
        // 假数据
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String[] split = bo.getTimeRange().split(",");

        Date start = dateFormat.parse(split[0]);
        Date end = dateFormat.parse(split[1]);

        Map<String, List<SlWeatherStatisticVo>> map = new HashMap<>();

        while (start.before(end)) {
            OneNetResponseDto request = httpService.request(OneNetConfig.WEATHER, new String[]{split[0]}, null);
            log.info("请求服务器,目标时间:{}:结果{}", start, request);
            if (request != null) {
                List<Map<String, String>> data = request.getData().getData();
                if (CollectionUtil.isNotEmpty(data)) {
                    data.forEach(d -> {
                        if (d.containsKey("ZM")) {
                            String zm = d.get("ZM");
                            if (!map.containsKey(zm)) {
                                map.put(zm, new ArrayList<>());
                            }
                            // 这里的对象只是做个缓存
                            SlWeatherStatisticVo v = new SlWeatherStatisticVo();
                            v.setStationName(zm);
                            v.setAddress("未知");
                            if (d.containsKey("ZDQW")) {
                                double temperature = Double.parseDouble(d.get("ZDQW"));
                                v.setHighestTemp(temperature);
                            }
                            if (d.containsKey("XSYL")) {
                                double xsyl = Double.parseDouble(d.get("XSYL"));
                                v.setRainCount((int) xsyl);
                            }

                            if (d.containsKey("ZDJD")) {
                                double lng = Double.parseDouble(d.get("ZDJD"));
                                v.setLng(lng);
                            }

                            if (d.containsKey("ZDWD")) {
                                double lat = Double.parseDouble(d.get("ZDWD"));
                                v.setLat(lat);
                            }
                            map.get(zm).add(v);
                        }
                    });
                }
            }
            start = DateUtil.offset(start, DateField.HOUR_OF_DAY, 1);
        }


        map.forEach((k, v) -> {
            SlWeatherStatisticVo one = new SlWeatherStatisticVo();
            BeanUtil.copyProperties(v.get(0), one, "rainCount", "highestTemp");
            double maxTemperature = Double.MIN_VALUE;
            double lowTemperature = Double.MAX_VALUE;
            int rainCount = 0;
            for (SlWeatherStatisticVo v1 : v) {
                rainCount += v1.getRainCount();
                maxTemperature = maxTemperature > v1.getHighestTemp() ? maxTemperature : v1.getHighestTemp();
                lowTemperature = lowTemperature < v1.getHighestTemp() ? lowTemperature : v1.getHighestTemp();
            }
            one.setRainCount(rainCount);
            one.setLowestTemp(lowTemperature);
            one.setHighestTemp(maxTemperature);
            vo.add(one);
        });

        return vo;
    }

    @Override
    public List<SlStationWeatherVo> queryStationWeather(SlWeatherBo bo) throws ParseException {
        List<SlStationWeatherVo> result = new ArrayList<>();
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String[] split = bo.getTimeRange().split(",");

        Date start = dateFormat.parse(split[0]);
        Date end = dateFormat.parse(split[1]);

        Map<String, String> optionals = new HashMap<>();
        optionals.put("ZM", bo.getStationName());
        while (start.before(end) || start.getTime() == end.getTime()) {
            OneNetResponseDto request = httpService.request(OneNetConfig.WEATHER, new String[]{dateFormat.format(start)},
                optionals);
            log.info("请求站点天气情况接口：{}", request);
            if (request != null) {
                List<Map<String, String>> data = request.getData().getData();
                if (CollectionUtil.isNotEmpty(data)) {
                    for (Map<String, String> d : data) {// 这里的对象只是做个缓存
                        SlStationWeatherVo v = new SlStationWeatherVo();
                        if (d.containsKey("ZDQW")) {
                            double temperature = Double.parseDouble(d.get("ZDQW"));
                            v.setTemperature(temperature);
                        }
                        if (d.containsKey("XSYL")) {
                            double xsyl = Double.parseDouble(d.get("XSYL"));
                            v.setRainCount((int) xsyl);
                        }
                        v.setTime(dateFormat.format(start));
                        result.add(v);

                    }
                }
            }
            start = DateUtil.offset(start, DateField.HOUR_OF_DAY, 1);
        }
        return result;
    }

    @Override
    public SlStationWeatherVo queryWeather(SlWeatherBo bo) throws ParseException {
        Date now = new Date();
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        if (StringUtils.isEmpty(bo.getQueryTime())) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(now);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            now = calendar.getTime();
        }
        Map<String, String> optionals = new HashMap<>();
        optionals.put("ZM", bo.getStationName() == null ? "东莞国家基本气象站" : bo.getStationName());
        int tryTimes = 10;

        SlStationWeatherVo v = new SlStationWeatherVo();
        while (tryTimes-- > 0) {
            OneNetResponseDto request = httpService.request(OneNetConfig.WEATHER, new String[]{dateFormat.format(now)},
                optionals);
            log.info("请求站点天气情况接口：{}", request);

            if (request != null) {
                List<Map<String, String>> data = request.getData().getData();
                if (CollectionUtil.isNotEmpty(data)) {
                    for (Map<String, String> d : data) {// 这里的对象只是做个缓存

                        if (d.containsKey("ZDQW")) {
                            double temperature = Double.parseDouble(d.get("ZDQW"));
                            v.setTemperature(temperature);
                        }
                        if (d.containsKey("XSYL")) {
                            double xsyl = Double.parseDouble(d.get("XSYL"));
                            v.setRainCount((int) xsyl);
                        }
                        v.setTime(dateFormat.format(now));
                    }
                    break;
                } else {
                    now = DateUtil.offset(now, DateField.HOUR_OF_DAY, -1);
                }
            }
        }
        return v;
    }

    @Override
    public Object[] params(String param) {

        if (param.contains("weatherRange")) {
            DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date now = new Date();
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(now);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            now = calendar.getTime();
            DateTime offset = DateUtil.offset(now, DateField.DAY_OF_MONTH, -1);
            SlWeatherBo bo = new SlWeatherBo();
            bo.setTimeRange(dateFormat.format(offset) + "," + dateFormat.format(now));
            return new Object[]{bo};
        } else {
            SlWeatherBo bo = new SlWeatherBo();
            bo.setStationName("东莞国家基本气象站");
            return new Object[]{bo};
        }
    }
}

