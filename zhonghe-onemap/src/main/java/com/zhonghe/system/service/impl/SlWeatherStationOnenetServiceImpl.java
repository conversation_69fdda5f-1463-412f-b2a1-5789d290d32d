package com.zhonghe.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.system.domain.SlWeatherStationOnenet;
import com.zhonghe.system.domain.bo.SlWeatherStationOnenetBo;
import com.zhonghe.system.domain.vo.SlWeatherStationOnenetVo;
import com.zhonghe.system.event.ExchangeDataBaseEventQueryWeatherStation;
import com.zhonghe.system.mapper.SlWeatherStationOnenetMapper;
import com.zhonghe.system.service.ISlWeatherStationDataService;
import com.zhonghe.system.service.ISlWeatherStationOnenetService;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 【请填写功能名称】Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-08-21
 */
@RequiredArgsConstructor
@Service
public class SlWeatherStationOnenetServiceImpl implements ISlWeatherStationOnenetService {

    private final SlWeatherStationOnenetMapper baseMapper;

    private final ApplicationEventPublisher applicationEventPublisher;

    private final ISlWeatherStationDataService weatherStationDataService;

    /**
     * 查询【请填写功能名称】
     */
    @Override
    public SlWeatherStationOnenetVo queryById(Long stationOnenetId) {
        return baseMapper.selectVoById(stationOnenetId);
    }


    /**
     * 查询【请填写功能名称】列表
     */
    @Override
    public List<SlWeatherStationOnenetVo> queryList(SlWeatherStationOnenetBo bo) {
        LambdaQueryWrapper<SlWeatherStationOnenet> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SlWeatherStationOnenet> buildQueryWrapper(SlWeatherStationOnenetBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SlWeatherStationOnenet> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getStationName()), SlWeatherStationOnenet::getStationName, bo.getStationName());
        lqw.eq(StringUtils.isNotBlank(bo.getStationGem()), SlWeatherStationOnenet::getStationGem, bo.getStationGem());
        lqw.eq(bo.getFid() != null, SlWeatherStationOnenet::getFid, bo.getFid());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), SlWeatherStationOnenet::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增【请填写功能名称】
     */
    @Override
    public Boolean insertByBo(SlWeatherStationOnenetBo bo) {
        SlWeatherStationOnenet add = BeanUtil.toBean(bo, SlWeatherStationOnenet.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setStationOnenetId(add.getStationOnenetId());
        }
        return flag;
    }

    /**
     * 修改【请填写功能名称】
     */
    @Override
    public Boolean updateByBo(SlWeatherStationOnenetBo bo) {
        SlWeatherStationOnenet update = BeanUtil.toBean(bo, SlWeatherStationOnenet.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SlWeatherStationOnenet entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除【请填写功能名称】
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateGis() {
        List<SlWeatherStationOnenet> oldStation = baseMapper.selectList(new LambdaQueryWrapper<SlWeatherStationOnenet>().select(SlWeatherStationOnenet::getStationName));
        List<String> oldStationNames = oldStation.stream().map(SlWeatherStationOnenet::getStationName).collect(Collectors.toList());
        List<SlWeatherStationOnenet> geometry = new ArrayList<>();
        applicationEventPublisher.publishEvent(new ExchangeDataBaseEventQueryWeatherStation(this, oldStationNames, geometry));
        if (CollectionUtil.isNotEmpty(geometry)) {
            baseMapper.insertBatch(geometry);
        }
    }

    @Override
    public List<SlWeatherStationOnenet> queryByStationName(String[] stationNames) {
        return baseMapper.selectList(new LambdaQueryWrapper<SlWeatherStationOnenet>().in(SlWeatherStationOnenet::getStationName, stationNames));
    }
}
