package com.zhonghe.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpStatus;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.system.constants.SlOneMapConstants;
import com.zhonghe.system.domain.bo.SlRentalHouseBo;
import com.zhonghe.system.domain.thirdparty.IntegrationClue;
import com.zhonghe.system.domain.thirdparty.SlCaseEventProxy;
import com.zhonghe.system.domain.thirdparty.bo.SlCaseEventProxyBo;
import com.zhonghe.system.domain.thirdparty.vo.GridManagerInfoVo;
import com.zhonghe.system.domain.thirdparty.vo.IntegrationClueVo;
import com.zhonghe.system.domain.thirdparty.vo.SlCaseEventMainSubjectStatisticsVo;
import com.zhonghe.system.domain.thirdparty.vo.SlCaseEventProxyDetailVo;
import com.zhonghe.system.domain.thirdparty.vo.SlCaseEventProxyGroupStatisticsVo;
import com.zhonghe.system.domain.thirdparty.vo.SlCaseEventProxyOneMapVo;
import com.zhonghe.system.domain.thirdparty.vo.SlCaseEventProxyStatisticsVo;
import com.zhonghe.system.domain.thirdparty.vo.SlCaseEventProxyVo;
import com.zhonghe.system.domain.thirdparty.vo.SlCaseEventStatisticsVo;
import com.zhonghe.system.domain.thirdparty.vo.SpecialistVo;
import com.zhonghe.system.domain.vo.SlRentalHouseVo;
import com.zhonghe.system.domain.vo.grid.SlGridStructureCharacterVo;
import com.zhonghe.system.event.ExchangeDataBaseEventCaseByAddressEvent;
import com.zhonghe.system.event.ExchangeDataBaseEventCaseEventByMarketEvent;
import com.zhonghe.system.event.ExchangeDataBaseEventQueryGridLeaderEvent;
import com.zhonghe.system.mapper.IntegrationClueMapper;
import com.zhonghe.system.mapper.SlCaseEventProxyMapper;
import com.zhonghe.system.service.IIntegrationClueService;
import com.zhonghe.system.service.IMapService;
import com.zhonghe.system.service.IOneNetHttpService;
import com.zhonghe.system.service.ISlCaseEventProxyService;
import com.zhonghe.system.service.ISlRentalHouseService;
import com.zhonghe.system.service.ISpecialPopulationsService;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 【请填写功能名称】Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-08-14
 */
@RequiredArgsConstructor
@Service
public class SlCaseEventProxyServiceImpl implements ISlCaseEventProxyService {

    private final SlCaseEventProxyMapper baseMapper;

    private final ISpecialPopulationsService specialPopulationsService;


    private final ISlRentalHouseService rentalHouseService;

    private final ApplicationEventPublisher applicationEventPublisher;

    private final IMapService mapService;

    private final IOneNetHttpService httpService;

    private final IIntegrationClueService integrationClueService;

    private final IntegrationClueMapper integrationClueMapper;
    /**
     * 查询【请填写功能名称】
     */
    @Override
    public SlCaseEventProxyVo queryById(String fywzj) {
        return baseMapper.selectVoById(fywzj);
    }


    /**
     * 查询【请填写功能名称】列表
     */
    @Override
    public List<SlCaseEventProxyVo> queryList(SlCaseEventProxyBo bo) {
        LambdaQueryWrapper<SlCaseEventProxy> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SlCaseEventProxy> buildQueryWrapper(SlCaseEventProxyBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SlCaseEventProxy> lqw = Wrappers.lambdaQuery();
        return lqw;
    }

    /**
     * 新增【请填写功能名称】
     */
    @Override
    public Boolean insertByBo(SlCaseEventProxyBo bo) {
        SlCaseEventProxy add = BeanUtil.toBean(bo, SlCaseEventProxy.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {

        }
        return flag;
    }

    /**
     * 修改【请填写功能名称】
     */
    @Override
    public Boolean updateByBo(SlCaseEventProxyBo bo) {
        SlCaseEventProxy update = BeanUtil.toBean(bo, SlCaseEventProxy.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SlCaseEventProxy entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除【请填写功能名称】
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    @DS("data")
    public SlCaseEventProxyOneMapVo caseStatistics() {
        SlCaseEventProxyOneMapVo vo = new SlCaseEventProxyOneMapVo();
        Long count = integrationClueMapper.selectCount(new LambdaQueryWrapper<>());
        //baseMapper.selectCount(new LambdaQueryWrapper<>());
        vo.setCount(count);

        //TODO 新增
        Long newCount = integrationClueMapper.selectCount(new LambdaQueryWrapper<IntegrationClue>()
            .gt(IntegrationClue::getVerTerm,new Date()));
            //baseMapper.selectCount(new QueryWrapper<SlCaseEventProxy>().eq("ZLBS", "待处置"));
        vo.setNewCount(newCount);

        // 已处理
        Long overCount =integrationClueMapper.selectCount(new LambdaQueryWrapper<IntegrationClue>()
            .lt(IntegrationClue::getVerTerm,new Date()));
            // baseMapper.selectCount(new QueryWrapper<SlCaseEventProxy>().isNotNull("CLRXM"));
        vo.setOverCount(overCount);

        // 百分率
        vo.setPercent(((overCount == null ? 0L : overCount) * 100 / ((count == null || count == 0) ? 1L : count)) + "%");
        return vo;
    }

    @Override
    @DS("data")
    public SlCaseEventProxyStatisticsVo caseGroupStatistics(SlCaseEventProxyBo bo) throws ParseException {
        SlCaseEventProxyStatisticsVo result = new SlCaseEventProxyStatisticsVo();
        if (SlOneMapConstants.CASE_EVENT_MONTH_QUERY_TYPE.equals(bo.getQueryType())) {
            List<SlCaseEventProxyGroupStatisticsVo> group = new ArrayList<>(6);
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            Date now = new Date();
            DateTime offset = DateUtil.offset(now, DateField.DAY_OF_MONTH, -30);
            DateTime offset1 = DateUtil.offset(now, DateField.DAY_OF_MONTH, -25);
            DateTime offset2 = DateUtil.offset(now, DateField.DAY_OF_MONTH, -20);
            DateTime offset3 = DateUtil.offset(now, DateField.DAY_OF_MONTH, -15);
            DateTime offset4 = DateUtil.offset(now, DateField.DAY_OF_MONTH, -10);
            DateTime offset5 = DateUtil.offset(now, DateField.DAY_OF_MONTH, -5);
            String[] dates = new String[]{
                simpleDateFormat.format(offset),
                simpleDateFormat.format(offset1),
                simpleDateFormat.format(offset2),
                simpleDateFormat.format(offset3),
                simpleDateFormat.format(offset4),
                simpleDateFormat.format(offset5),
                simpleDateFormat.format(now),
            };
            for (String date : dates) {
                SlCaseEventProxyGroupStatisticsVo vo = new SlCaseEventProxyGroupStatisticsVo();
                vo.setDate(date);
                //vo.setCount(baseMapper.queryOneDay(date));
                vo.setCount(integrationClueMapper.queryOneDay(date));
                group.add(vo);
            }

            result.setGroup(group);
            //result.setGroup(baseMapper.queryOneMonthOfToDay(offset, now));
        } else {
            int year = DateUtil.thisYear();
            DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
            //List<SlCaseEventProxyGroupStatisticsVo> vo = baseMapper.queryQuarterByYear(String.valueOf(year));
            List<SlCaseEventProxyGroupStatisticsVo> vo = integrationClueMapper.queryQuarterByYear(String.valueOf(year));
            result.setGroup(vo);
            for (SlCaseEventProxyGroupStatisticsVo slCaseEventProxyGroupStatisticsVo : vo) {
                Date d = dateFormat.parse(slCaseEventProxyGroupStatisticsVo.getDate());
                int quarter = DateUtil.quarter(d);
                slCaseEventProxyGroupStatisticsVo.setDate(null);
                slCaseEventProxyGroupStatisticsVo.setQuarter(String.valueOf(quarter));
            }
        }
        //result.setSubjects(baseMapper.queryGroupByType());

        return result;
    }

    @Override
    @DS("data")
    public List<SlCaseEventStatisticsVo> caseEventStatisticByAddresses(Set<String> addresses) {

        return baseMapper.queryGroupByAddress(addresses);

    }

    @Override
    @DS("data")
    public TableDataInfo<SlCaseEventProxyVo> page(PageQuery pageQuery, SlCaseEventProxyBo bo) {
        /*IPage<SlCaseEventProxyVo> slCaseEventProxyVoIPage = baseMapper.selectVoPage(pageQuery.build(),
            new LambdaQueryWrapper<SlCaseEventProxy>()
                .or(StringUtils.isNotEmpty(bo.getKeyWord()), wq -> {
                    wq.like(StringUtils.isNotEmpty(bo.getKeyWord()), SlCaseEventProxy::getAsjid, bo.getKeyWord())
                        .or()
                        .like(StringUtils.isNotEmpty(bo.getKeyWord()), SlCaseEventProxy::getJzwdz, bo.getKeyWord());
                })
        );
        return TableDataInfo.build(slCaseEventProxyVoIPage);*/
        IPage<IntegrationClueVo> pageData = integrationClueService.selectVoPageList(pageQuery.build(), new LambdaQueryWrapper<IntegrationClue>()
            .or(StringUtils.isNotEmpty(bo.getKeyWord()), wq -> {
                wq.like(StringUtils.isNotEmpty(bo.getKeyWord()), IntegrationClue::getBuildAddress, bo.getKeyWord())
                    .or()
                    .like(StringUtils.isNotEmpty(bo.getKeyWord()), IntegrationClue::getDangerDescribe, bo.getKeyWord());
            }));

        List<SlCaseEventProxyVo> records = new ArrayList<>(pageData.getRecords().size());


        pageData.getRecords().forEach(d -> {
            SlCaseEventProxyVo v = this.create(d);
            records.add(v);
        });

        TableDataInfo<SlCaseEventProxyVo> rspData = new TableDataInfo<>();
        rspData.setCode(HttpStatus.HTTP_OK);
        rspData.setMsg("查询成功");
        rspData.setTotal(pageData.getTotal());
        rspData.setRows(records);

        return rspData;
    }

    private SlCaseEventProxyVo create(IntegrationClueVo d) {
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        SlCaseEventProxyVo v = new SlCaseEventProxyVo();

        v.setZtmc(d.getSubjectName());
        v.setJzwdz(d.getBuildAddress());
        v.setYhms(d.getDangerDescribe());
        v.setAsjid(d.getDangerId());
        //v.setZlbs();
        if (ObjectUtil.isNotNull(d.getVerTerm())) {
            v.setCzqx(dateFormat.format(d.getVerTerm()));
        }
        v.setCzbmmc(d.getDisposalDeptName());
        v.setClrxm(d.getCreateName());
        v.setScsj(d.getCreateDate());

        return v;
    }

    @Override
    @DS("data")
    public List<SlCaseEventProxy> queryEventCaseByAddress(String address) {
        return baseMapper.selectList(new QueryWrapper<SlCaseEventProxy>().eq("JZWDZ", address)
            .orderByAsc("SCSJ"));
    }

    @Override
    @DS("data")
    public SlCaseEventProxyDetailVo queryDetail(String asjid) {
        SlCaseEventProxyDetailVo vo = new SlCaseEventProxyDetailVo();

        //List<SlCaseEventProxyVo> asjidEntity = baseMapper.selectVoList(new QueryWrapper<SlCaseEventProxy>().eq("asjid", asjid));
        List<IntegrationClueVo> asjIdEntities = integrationClueService.queryEventCaseByAsjid(asjid);
        if (CollectionUtils.isNotEmpty(asjIdEntities)) {
            vo.setVo(this.create(asjIdEntities.get(0)));

            if (StringUtils.isNotEmpty(vo.getVo().getJzwdz())) {
                List<SpecialistVo> specialistVos = specialPopulationsService.queryByAddress(vo.getVo().getJzwdz());
                vo.setSpecialist(specialistVos);

                //案事件记录
                List<IntegrationClueVo> integrationClueVos = integrationClueService.selectListByAddress(vo.getVo().getJzwdz());
                vo.setIntegrationClueVos(integrationClueVos);

                // 查询网格员数据
                String zxwd = asjIdEntities.get(0).getLng();
                String zxjd = asjIdEntities.get(0).getLat();
                if (StringUtils.isNotEmpty(zxwd) && StringUtils.isNotEmpty(zxjd) && !"null".equals(zxwd) && !"null".equals(zxjd)) {
                    List<String> grids = mapService.gridDB(Double.parseDouble(zxjd), Double.parseDouble(zxwd));
                    if (CollectionUtils.isNotEmpty(grids)) {
                        SlGridStructureCharacterVo gridLeader = new SlGridStructureCharacterVo();
                        applicationEventPublisher.publishEvent(new ExchangeDataBaseEventQueryGridLeaderEvent(this, grids.get(0), gridLeader));
                        GridManagerInfoVo gridManager = new GridManagerInfoVo();
                        gridManager.setName(gridLeader.getCharacterName());
                        gridManager.setWorkTelephone(gridLeader.getCharacterWorkPhone());
                        gridManager.setTelephone(gridLeader.getCharacterFamilyPhone());
                        gridManager.setGridZoneName(gridLeader.getGridZoneName());
                        gridManager.setCharacterZoneName(gridLeader.getCharacterZoneName());
                        vo.setGridManager(gridManager);
                    }
                }
                /*OneNetResponseDto request = httpService.request(OneNetConfig.ADDRESS, new String[]{vo.getVo().getJzwdz()}, null);
                if (request != null) {
                    List<Map<String, String>> data = request.getData().getData();
                    Map<String, String> gxsj = OneNetConfig.ADDRESS.getSelector().select(data, "GXSJ");
                    if (CollectionUtils.isNotEmpty(gxsj)) {
                    }
                }*/
            }
        }

        return vo;
    }

    @Override
    public List<SlCaseEventMainSubjectStatisticsVo> caseTypeStatistics() {
        List<SlCaseEventMainSubjectStatisticsVo> result = new ArrayList<>();
        // 市场主体
        SlCaseEventMainSubjectStatisticsVo vo = new SlCaseEventMainSubjectStatisticsVo("市场主体", 0L);
        applicationEventPublisher.publishEvent(new ExchangeDataBaseEventCaseEventByMarketEvent(this, vo));
        result.add(vo);
        // 出租屋主体
        List<SlRentalHouseVo> slRentalHouseVos = rentalHouseService.queryList(new SlRentalHouseBo());
        Set<String> collect = slRentalHouseVos.stream().map(SlRentalHouseVo::getBuildingAddress).collect(Collectors.toSet());
        List<SlCaseEventStatisticsVo> r = new ArrayList<>();
        applicationEventPublisher.publishEvent(new ExchangeDataBaseEventCaseByAddressEvent(this, collect, r));
        long count = 0L;
        for (SlCaseEventStatisticsVo r1 : r) {
            count += r1.getCount();
        }
        result.add(new SlCaseEventMainSubjectStatisticsVo("出租屋", count));
        // TODO 三小主体
        SlCaseEventMainSubjectStatisticsVo small = new SlCaseEventMainSubjectStatisticsVo("三小主体", 0L);
        result.add(small);
        return result;
    }

    @Override
    @DS("data")
    public long queryMarkerCaseEventCount() {
        return baseMapper.selectMarkerCaseEventCount();
    }
}
