package com.zhonghe.system.service;

import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.system.domain.bo.SlVolunteerActionsBo;
import com.zhonghe.system.domain.vo.SlVolunteerActionsVo;

import java.util.Collection;
import java.util.List;

/**
 * 志愿者活动Service接口
 *
 * <AUTHOR>
 * @date 2023-08-04
 */
public interface ISlVolunteerActionsService {

    /**
     * 查询志愿者活动
     */
    SlVolunteerActionsVo queryById(Long actionId);


    /**
     * 查询志愿者活动列表
     */
    List<SlVolunteerActionsVo> queryList(SlVolunteerActionsBo bo);

    /**
     * 修改志愿者活动
     */
    Boolean insertByBo(SlVolunteerActionsBo bo);

    /**
     * 修改志愿者活动
     */
    Boolean updateByBo(SlVolunteerActionsBo bo);

    /**
     * 校验并批量删除志愿者活动信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    TableDataInfo<SlVolunteerActionsVo> selectPage(SlVolunteerActionsBo bo, PageQuery pageQuery);
}
