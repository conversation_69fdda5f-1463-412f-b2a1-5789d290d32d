package com.zhonghe.system.service;

import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.system.domain.bo.SlRentalHouseBo;
import com.zhonghe.system.domain.thirdparty.vo.SlCaseEventStatisticsVo;
import com.zhonghe.system.domain.vo.RentalHouseDetailVo;
import com.zhonghe.system.domain.vo.SlRentalHouseStatisticsVo;
import com.zhonghe.system.domain.vo.SlRentalHouseVo;

import java.util.Collection;
import java.util.List;

/**
 * 出租屋信息Service接口
 *
 * <AUTHOR>
 * @date 2023-08-11
 */
public interface ISlRentalHouseService {

    /**
     * 查询出租屋信息
     */
    SlRentalHouseVo queryById(Long rentalHouseId);


    /**
     * 查询出租屋信息列表
     */
    List<SlRentalHouseVo> queryList(SlRentalHouseBo bo);

    /**
     * 修改出租屋信息
     */
    Boolean insertByBo(SlRentalHouseBo bo);

    /**
     * 修改出租屋信息
     */
    Boolean updateByBo(SlRentalHouseBo bo);

    /**
     * 校验并批量删除出租屋信息信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    SlRentalHouseStatisticsVo rentalHouseStatistics();

    List<SlCaseEventStatisticsVo> rentalHouseEventStatistics();

    TableDataInfo<SlRentalHouseVo> pageSlRentalHouse(PageQuery pageQuery, SlRentalHouseBo bo);

    RentalHouseDetailVo detailRentalHouse(Long rentalHouseId);

    boolean refresh();
}
