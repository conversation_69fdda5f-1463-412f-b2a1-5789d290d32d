package com.zhonghe.system.service;

import com.zhonghe.system.domain.dto.OneNetResponseDto;
import com.zhonghe.system.enmu.OneNetConfig;

import java.util.Map;

/**
 * @description:
 * @author: cq
 * @date: 2023/8/8 14:28
 */

public interface IOneNetHttpService {
    /**
     * 请求数据
     *
     * @param config
     * @param values
     * @param optional 可选参数
     * @return
     */
    OneNetResponseDto request(OneNetConfig config, String[] values, Map<String ,String> optional);

}
