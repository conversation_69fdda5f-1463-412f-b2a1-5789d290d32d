package com.zhonghe.system.service;

import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.system.auto.IParamService;
import com.zhonghe.system.domain.thirdparty.SlWeatherSign;
import com.zhonghe.system.domain.thirdparty.bo.SlWeatherSignBo;
import com.zhonghe.system.domain.thirdparty.vo.SlWeatherSignVo;

import java.text.ParseException;
import java.util.Collection;
import java.util.List;

/**
 * 【请填写功能名称】Service接口
 *
 * <AUTHOR>
 * @date 2023-08-21
 */
public interface ISlWeatherSignService extends IParamService {

    /**
     * 查询【请填写功能名称】
     */
    SlWeatherSignVo queryById(String fywzjid);


    /**
     * 查询【请填写功能名称】列表
     */
    List<SlWeatherSignVo> queryList(SlWeatherSignBo bo);

    /**
     * 修改【请填写功能名称】
     */
    Boolean insertByBo(SlWeatherSignBo bo);

    /**
     * 修改【请填写功能名称】
     */
    Boolean updateByBo(SlWeatherSignBo bo);

    /**
     * 校验并批量删除【请填写功能名称】信息
     */
    Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

    TableDataInfo<SlWeatherSign> queryPage(PageQuery page) throws ParseException;
}
