package com.zhonghe.system.service;

import com.zhonghe.system.domain.SlMarketEnterpriseOnenet;
import com.zhonghe.system.domain.bo.SlMarketEnterpriseBo;
import com.zhonghe.system.domain.vo.SlMarketEnterpriseVo;

import java.util.Collection;
import java.util.List;

/**
 * 【请填写功能名称】Service接口
 *
 * <AUTHOR>
 * @date 2023-08-14
 */
public interface ISlMarketEnterpriseService {

    /**
     * 查询【请填写功能名称】
     */
    SlMarketEnterpriseVo queryById(Long marketEnterpriseId);


    /**
     * 查询【请填写功能名称】列表
     */
    List<SlMarketEnterpriseVo> queryList(SlMarketEnterpriseBo bo);

    /**
     * 修改【请填写功能名称】
     */
    Boolean insertByBo(SlMarketEnterpriseBo bo);

    /**
     * 修改【请填写功能名称】
     */
    Boolean updateByBo(SlMarketEnterpriseBo bo);

    /**
     * 校验并批量删除【请填写功能名称】信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    void refresh();

    void insertBatch(List<SlMarketEnterpriseOnenet> enterpriseOnenets);
}
