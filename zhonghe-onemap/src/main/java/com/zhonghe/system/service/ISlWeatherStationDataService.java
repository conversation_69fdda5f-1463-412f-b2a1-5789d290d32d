package com.zhonghe.system.service;

import com.zhonghe.system.domain.SlWeatherStationOnenet;
import com.zhonghe.system.domain.thirdparty.bo.SlWeatherStationDataBo;
import com.zhonghe.system.domain.thirdparty.vo.SlWeatherStationDataVo;

import java.util.Collection;
import java.util.List;

/**
 * 【请填写功能名称】Service接口
 *
 * <AUTHOR>
 * @date 2023-08-21
 */
public interface ISlWeatherStationDataService {

    /**
     * 查询【请填写功能名称】
     */
    SlWeatherStationDataVo queryById(String fywzjid);


    /**
     * 查询【请填写功能名称】列表
     */
    List<SlWeatherStationDataVo> queryList(SlWeatherStationDataBo bo);

    /**
     * 修改【请填写功能名称】
     */
    Boolean insertByBo(SlWeatherStationDataBo bo);

    /**
     * 修改【请填写功能名称】
     */
    Boolean updateByBo(SlWeatherStationDataBo bo);

    /**
     * 校验并批量删除【请填写功能名称】信息
     */
    Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

    List<SlWeatherStationOnenet> queryNewStation(List<String> stationNames);
}
