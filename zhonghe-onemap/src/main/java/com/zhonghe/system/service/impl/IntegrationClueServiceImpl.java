package com.zhonghe.system.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhonghe.system.domain.thirdparty.IntegrationClue;
import com.zhonghe.system.domain.thirdparty.SlCaseEventProxy;
import com.zhonghe.system.domain.thirdparty.vo.IntegrationClueVo;
import com.zhonghe.system.mapper.IntegrationClueMapper;
import com.zhonghe.system.service.IIntegrationClueService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

/**
 * @description:
 * @author: cq
 * @date: 2023/11/7 10:43
 */

@Service
@Slf4j
@AllArgsConstructor
@DS("data")
public class IntegrationClueServiceImpl implements IIntegrationClueService {

    private final IntegrationClueMapper baseMapper;


    @Override
    public List<IntegrationClueVo> selectListByAddress(String address) {
        LambdaQueryWrapper<IntegrationClue> lqw = new LambdaQueryWrapper<IntegrationClue>().eq(IntegrationClue::getBuildAddress, address);
        return baseMapper.selectVoList(lqw);
    }

    @Override
    public List<IntegrationClueVo> selectListByAddresses(List<String> addresses) {
        LambdaQueryWrapper<IntegrationClue> lqw = new LambdaQueryWrapper<IntegrationClue>().in(IntegrationClue::getBuildAddress, addresses);
        return baseMapper.selectVoList(lqw);
    }

    @Override
    public List<SlCaseEventProxy> queryEventCaseByAddress(String address) {
        List<IntegrationClueVo> integrationClueVos = this.selectListByAddress(address);

        List<SlCaseEventProxy> result = new ArrayList<>(integrationClueVos.size());

        DateFormat df = new SimpleDateFormat("yyyy-MM-dd 00:00:00.000");
        integrationClueVos.forEach(i -> {
            SlCaseEventProxy p = new SlCaseEventProxy();

            p.setYhms(i.getDangerDescribe());
            p.setCzbmbm(i.getDisposalDeptName());
            if (ObjectUtil.isNotNull(i.getVerTerm())) {
                p.setCzqx(df.format(i.getVerTerm()));
            }
            p.setScsj(i.getCreateDate());
            result.add(p);
        });

        return result;
    }

    @Override
    public IPage<IntegrationClueVo> selectVoPageList(Page<IntegrationClue> build, LambdaQueryWrapper<IntegrationClue> lqw) {
        return baseMapper.selectVoPage(build, lqw);
    }

    @Override
    public List<IntegrationClueVo> queryEventCaseByAsjid(String asjid) {
        return baseMapper.selectVoList(new LambdaQueryWrapper<IntegrationClue>().like(IntegrationClue::getDangerId, asjid));
    }
}

