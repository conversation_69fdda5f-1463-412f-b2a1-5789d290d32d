package com.zhonghe.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.system.domain.SlMarketOnenet;
import com.zhonghe.system.domain.bo.SlMarketOnenetBo;
import com.zhonghe.system.domain.vo.SlMarketOnenetVo;
import com.zhonghe.system.mapper.SlMarketOnenetMapper;
import com.zhonghe.system.service.ISlMarketOnenetService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 前置机市场地图Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-08-21
 */
@RequiredArgsConstructor
@Service
@DS("master")
public class SlMarketOnenetServiceImpl implements ISlMarketOnenetService {

    private final SlMarketOnenetMapper baseMapper;

    /**
     * 查询前置机市场地图
     */
    @Override
    public SlMarketOnenetVo queryById(Long marketId) {
        return baseMapper.selectVoById(marketId);
    }


    /**
     * 查询前置机市场地图列表
     */
    @Override
    public List<SlMarketOnenetVo> queryList(SlMarketOnenetBo bo) {
        LambdaQueryWrapper<SlMarketOnenet> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SlMarketOnenet> buildQueryWrapper(SlMarketOnenetBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SlMarketOnenet> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getMarketDataId() != null, SlMarketOnenet::getMarketDataId, bo.getMarketDataId());
        lqw.eq(StringUtils.isNotBlank(bo.getMarketGem()), SlMarketOnenet::getMarketGem, bo.getMarketGem());
        lqw.eq(StringUtils.isNotBlank(bo.getMarketAddress()), SlMarketOnenet::getMarketAddress, bo.getMarketAddress());
        lqw.eq(bo.getFid() != null, SlMarketOnenet::getFid, bo.getFid());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), SlMarketOnenet::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增前置机市场地图
     */
    @Override
    public Boolean insertByBo(SlMarketOnenetBo bo) {
        SlMarketOnenet add = BeanUtil.toBean(bo, SlMarketOnenet.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setMarketId(add.getMarketId());
        }
        return flag;
    }

    /**
     * 修改前置机市场地图
     */
    @Override
    public Boolean updateByBo(SlMarketOnenetBo bo) {
        SlMarketOnenet update = BeanUtil.toBean(bo, SlMarketOnenet.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SlMarketOnenet entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除前置机市场地图
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public List<String> selectAllIds() {
        List<SlMarketOnenet> slMarketOnenets = baseMapper.selectList(new LambdaQueryWrapper<SlMarketOnenet>().select(SlMarketOnenet::getMarketDataId));

        return slMarketOnenets.stream().map(SlMarketOnenet::getMarketDataId).collect(Collectors.toList());
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void insertBatch(List<SlMarketOnenet> slMarketOnenets) {
        if (CollectionUtil.isNotEmpty(slMarketOnenets)) {
            Set<String> collect = slMarketOnenets.stream().map(SlMarketOnenet::getMarketDataId).collect(Collectors.toSet());
            baseMapper.deleteBatchIds(collect);
            baseMapper.insertBatch(slMarketOnenets);
        }
    }
}
