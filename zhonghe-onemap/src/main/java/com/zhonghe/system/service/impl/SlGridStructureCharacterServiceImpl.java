package com.zhonghe.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.helper.IdGeneratorHelper;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.common.utils.TreeBuildUtils;
import com.zhonghe.system.constants.SlOneMapConstants;
import com.zhonghe.system.domain.SlGridStructure;
import com.zhonghe.system.domain.SlGridStructureCharacter;
import com.zhonghe.system.domain.SlGridStructureCharacterRel;
import com.zhonghe.system.domain.SlGridZone;
import com.zhonghe.system.domain.SlGridZoneCharacterRel;
import com.zhonghe.system.domain.bo.grid.SlGridStructureCharacterBo;
import com.zhonghe.system.domain.dto.SlGridCharacterStructureDto;
import com.zhonghe.system.domain.vo.SlOneMapServicePowerStatisticsVo;
import com.zhonghe.system.domain.vo.grid.SlGridStructureCharacterVo;
import com.zhonghe.system.excel.ExcelToDatabaseSwitch;
import com.zhonghe.system.excel.service.ILoadExcelService;
import com.zhonghe.system.mapper.SlGridStructureCharacterMapper;
import com.zhonghe.system.mapper.SlGridStructureCharacterRelMapper;
import com.zhonghe.system.mapper.SlGridStructureMapper;
import com.zhonghe.system.mapper.SlGridZoneCharacterRelMapper;
import com.zhonghe.system.mapper.SlGridZoneMapper;
import com.zhonghe.system.mapper.SlSocialWorkerMapper;
import com.zhonghe.system.mapper.SlVolunteerMapper;
import com.zhonghe.system.service.ISlGridStructureCharacterService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 网格人员信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-08-02
 */
@RequiredArgsConstructor
@Service
@DS("master")
public class SlGridStructureCharacterServiceImpl implements ISlGridStructureCharacterService, ILoadExcelService {

    private final SlGridStructureCharacterMapper baseMapper;

    private final SlGridStructureMapper structureMapper;

    private final SlGridZoneMapper gridZoneMapper;

    private final SlGridStructureCharacterRelMapper structureCharacterRelMapper;

    private final SlGridZoneCharacterRelMapper zoneCharacterRelMapper;

    private final SlVolunteerMapper slVolunteerMapper;

    private final SlSocialWorkerMapper socialWorkerMapper;

    /**
     * 查询网格人员信息
     */
    @Override
    public SlGridStructureCharacterVo queryById(Long gridCharacterId) {
        List<SlGridStructureCharacterVo> slGridStructureCharacterVos = baseMapper.queryCharacterDetailVo(new QueryWrapper<SlGridStructureCharacter>()
            .eq("sgsc.grid_character_id", gridCharacterId));
        if (!CollectionUtil.isEmpty(slGridStructureCharacterVos)) {
            return slGridStructureCharacterVos.get(0);
        }

        return null;
    }


    /**
     * 查询网格人员信息列表
     */
    @Override
    public List<SlGridStructureCharacterVo> queryList(SlGridStructureCharacterBo bo) {
        LambdaQueryWrapper<SlGridStructureCharacter> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SlGridStructureCharacter> buildQueryWrapper(SlGridStructureCharacterBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SlGridStructureCharacter> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getCharacterZoneName()), SlGridStructureCharacter::getCharacterZoneName, bo.getCharacterZoneName());
        lqw.like(StringUtils.isNotBlank(bo.getCharacterName()), SlGridStructureCharacter::getCharacterName, bo.getCharacterName());
        lqw.eq(StringUtils.isNotBlank(bo.getCharacterSex()), SlGridStructureCharacter::getCharacterSex, bo.getCharacterSex());
        lqw.eq(StringUtils.isNotBlank(bo.getCharacterWorkPhone()), SlGridStructureCharacter::getCharacterWorkPhone, bo.getCharacterWorkPhone());
        lqw.eq(StringUtils.isNotBlank(bo.getCharacterFamilyPhone()), SlGridStructureCharacter::getCharacterFamilyPhone, bo.getCharacterFamilyPhone());
        lqw.eq(StringUtils.isNotBlank(bo.getMark()), SlGridStructureCharacter::getMark, bo.getMark());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), SlGridStructureCharacter::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增网格人员信息
     */
    @Override
    public Boolean insertByBo(SlGridStructureCharacterBo bo) {
        SlGridStructureCharacter add = BeanUtil.toBean(bo, SlGridStructureCharacter.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setGridCharacterId(add.getGridCharacterId());
        }
        return flag;
    }

    /**
     * 修改网格人员信息
     */
    @Override
    public Boolean updateByBo(SlGridStructureCharacterBo bo) {
        SlGridStructureCharacter update = BeanUtil.toBean(bo, SlGridStructureCharacter.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SlGridStructureCharacter entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除网格人员信息
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public List<Tree<Long>> grid() {
        List<SlGridStructure> structures = structureMapper.selectList();

        List<SlGridStructureCharacterVo> characters = baseMapper.queryCharacterDetailVo(new LambdaQueryWrapper<>());

        Map<Long, Set<String>> structureAndCharacters = characters.stream()
            .collect(Collectors.groupingBy(SlGridStructureCharacterVo::getGridStructureId,
                Collectors.mapping(SlGridStructureCharacterVo::getCharacterName,
                    Collectors.toSet())));
        List<Tree<Long>> trees = TreeBuildUtils.build(structures, (structure, tree) -> {
            tree.setId(structure.getGridStructureId());
            tree.setParentId(structure.getGridStructureParentId());
            tree.setName(structure.getGirdStructureName());
            tree.setWeight(structure.getGridStructureId());

            if (structureAndCharacters.containsKey(structure.getGridStructureId())) {
                tree.put("count", structureAndCharacters.get(structure.getGridStructureId()).size());
            }

        }, "gridStructureParentId");

        if (CollectionUtil.isNotEmpty(trees)) {
            trees.forEach(this::setTreeCount);
        }
        return trees;


    }

    @Override
    public TableDataInfo<SlGridStructureCharacterVo> queryPageList(SlGridStructureCharacterBo bo, PageQuery pageQuery) {
        IPage<SlGridStructureCharacterVo> page = baseMapper.queryPageCharacterDetailVo(pageQuery.build(), this.createFilter(bo));
        return TableDataInfo.build(page);
    }

    @Override
    public TableDataInfo<SlGridStructureCharacterVo> queryAllServicePowerCharactersInfo(SlGridStructureCharacterBo bo, PageQuery pageQuery) {

        QueryWrapper<SlGridStructureCharacter> filter = this.createFilter(bo);

        IPage<SlGridStructureCharacterVo> slGridStructureCharacterVoIPage = baseMapper.queryServicePowerCharacters(pageQuery.build(), filter);

        return TableDataInfo.build(slGridStructureCharacterVoIPage);
    }

    @Override
    public SlOneMapServicePowerStatisticsVo servicePowerStatistics() {
        SlOneMapServicePowerStatisticsVo vo = new SlOneMapServicePowerStatisticsVo();

        // 1.全部服务力量
        Long selectCount = baseMapper.selectCount(new LambdaQueryWrapper<SlGridStructureCharacter>()
            .eq(SlGridStructureCharacter::getCharacterServiceType, SlOneMapConstants.SL_SERVICE_TYPE_ONE_TEAM));
        vo.setOneTeamCount(selectCount);

        // 2.网格员
        List<SlGridStructureCharacterVo> characterVos = baseMapper.queryCharacterDetailVo(new QueryWrapper<SlGridStructureCharacter>()
            .in("sgs.gird_structure_name", new String[]{"网格员", "网格长"}));

        vo.setGridWorkerCount((long) characterVos.size());

        // 3.社工
        Long socialWorkerCount = baseMapper.selectCount(new LambdaQueryWrapper<SlGridStructureCharacter>()
            .eq(SlGridStructureCharacter::getCharacterServiceType, SlOneMapConstants.SL_SERVICE_TYPE_SOCIAL_WORKER));
        vo.setSocialWorkerCount(socialWorkerCount);
        // 4.志愿者
        Long volunteerCount = baseMapper.selectCount(new LambdaQueryWrapper<SlGridStructureCharacter>()
            .eq(SlGridStructureCharacter::getCharacterServiceType, SlOneMapConstants.SL_SERVICE_TYPE_VOLUNTEER));
        vo.setVolunteerCount(volunteerCount);

        return vo;
    }

    @Override
    public SlGridStructureCharacterVo queryGridLeader(String grid) {
        List<SlGridStructureCharacterVo> vo = baseMapper.selectGridLeader(grid);
        if (CollectionUtil.isNotEmpty(vo)) {
            return vo.get(0);
        }
        return null;
    }

    @Override
    public SlGridStructureCharacterVo queryServicePowerByCharacterId(Long gridCharacterId) {
        return baseMapper.queryServicePowerCharactersByCharacterId(gridCharacterId);
    }

    private Set<Long> structureNodeIdIncludeChildrenNodes(Long structureId) {
        Set<Long> result = new HashSet<>();

        List<SlGridStructure> structures = structureMapper.selectList(new LambdaQueryWrapper<SlGridStructure>()
            .eq(SlGridStructure::getGridStructureParentId, structureId));
        if (!CollectionUtil.isEmpty(structures)) {
            Set<Long> collect = structures.stream().map(SlGridStructure::getGridStructureId).collect(Collectors.toSet());
            collect.forEach(c -> result.addAll(this.structureNodeIdIncludeChildrenNodes(c)));
        }
        result.add(structureId);
        return result;
    }

    private QueryWrapper<SlGridStructureCharacter> createFilter(SlGridStructureCharacterBo bo) {
        QueryWrapper<SlGridStructureCharacter> wrapper = new QueryWrapper<SlGridStructureCharacter>()
            .in(ObjectUtil.isNotNull(bo.getGridStructureId()), "sgs.grid_structure_id", this.structureNodeIdIncludeChildrenNodes(bo.getGridStructureId()))
            .and(StringUtils.isNotEmpty(bo.getKeyWord()), qw -> qw.like(StringUtils.isNotEmpty(bo.getKeyWord()), "sgz.grid_zone_name", bo.getKeyWord())
                .or()
                .like(StringUtils.isNotEmpty(bo.getKeyWord()), "sgsc.character_name", bo.getKeyWord())
                .or()
                .like(StringUtils.isNotEmpty(bo.getKeyWord()), "sgsc.character_zone_name", bo.getKeyWord())
                .or()
                .like(StringUtils.isNotEmpty(bo.getKeyWord()), "sgsc.character_work_phone", bo.getKeyWord())
                .or()
                .like(StringUtils.isNotEmpty(bo.getKeyWord()), "sgsc.character_family_phone", bo.getKeyWord())
                .or()
                .like(StringUtils.isNotEmpty(bo.getKeyWord()), "sgsc.work_location", bo.getKeyWord())
                .or()
                .like(StringUtils.isNotEmpty(bo.getKeyWord()), "sgsc.id_code", bo.getKeyWord())
            ).orderByAsc("sgsc.grid_character_id");

        if (StringUtils.isNotEmpty(bo.getCharacterServiceType()) && "网格人员".equals(bo.getCharacterServiceType())) {
            wrapper.in("sgs.gird_structure_name", new String[]{"网格员", "网格长"});
        } else {
            wrapper.eq(StringUtils.isNotEmpty(bo.getCharacterServiceType()), "sgsc.character_service_type", bo.getCharacterServiceType());
        }
        return wrapper;
    }

    private int setTreeCount(Tree<Long> tree) {
        int count = 0;
        if (!tree.containsKey("count")) {
            List<Tree<Long>> children = tree.getChildren();
            for (Tree<Long> t : children) {
                count += this.setTreeCount(t);
            }
            tree.put("count", count);
        } else {
            count = (int) tree.get("count");
        }
        return count;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void load(List<?> result) {
        List<SlGridStructureCharacter> characters = new ArrayList<>(result.size());
        List<SlGridStructureCharacterRel> structureCharacterRels = new ArrayList<>(result.size());
        List<SlGridZoneCharacterRel> zoneCharacterRels = new ArrayList<>(result.size());

        List<SlGridCharacterStructureDto> r = (List<SlGridCharacterStructureDto>) result;
        // 判定职位关系
        List<SlGridStructure> structures = structureMapper.selectList();
        Map<String, Long> structureIdsMap = structures.stream().collect(Collectors.toMap(a -> a.getGridZoneName() + a.getGirdStructureName(),
            SlGridStructure::getGridStructureId, (newOne, oldOne) -> newOne));
        // 判定网格
        List<SlGridZone> gridZones = gridZoneMapper.selectList();
        Map<String, Long> gridZoneIds = gridZones.stream().collect(Collectors.toMap(SlGridZone::getGridZoneName, SlGridZone::getGridZoneId));

        Map<String, SlGridStructureCharacter> exists = new HashMap<>(r.size());
        r.forEach(
            d -> {
                if (!exists.containsKey(d.getCharacterName())) {
                    SlGridStructureCharacter c = new SlGridStructureCharacter();
                    BeanUtil.copyProperties(d, c);
                    c.setGridCharacterId(IdGeneratorHelper.next());
                    characters.add(c);
                    exists.put(c.getCharacterName(), c);
                }
                Long characterId = exists.get(d.getCharacterName()).getGridCharacterId();
                // 建立人与职位关系
                SlGridStructureCharacterRel scRel = new SlGridStructureCharacterRel();
                scRel.setRelationId(IdGeneratorHelper.next());
                scRel.setGridStructureId(structureIdsMap.get(d.getCharacterZoneName() + d.getGridStructureName()));
                scRel.setGridCharacterId(characterId);
                scRel.setCreateBy(d.getCreateBy());
                scRel.setUpdateBy(d.getUpdateBy());
                structureCharacterRels.add(scRel);

                if (!StringUtils.isEmpty(d.getGridName())) {
                    // 人与网格的关系
                    SlGridZoneCharacterRel zcRel = new SlGridZoneCharacterRel();
                    zcRel.setZoneCharacterRelId(IdGeneratorHelper.next());
                    zcRel.setGridZoneId(gridZoneIds.get(d.getGridName()));
                    zcRel.setGridCharacterId(characterId);
                    zcRel.setCreateBy(d.getCreateBy());
                    zcRel.setUpdateBy(d.getUpdateBy());
                    zoneCharacterRels.add(zcRel);
                }
            }
        );

        baseMapper.insertBatch(characters);
        structureCharacterRelMapper.insertBatch(structureCharacterRels);
        zoneCharacterRelMapper.insertBatch(zoneCharacterRels);

    }

    @Override
    public void register() {
        ExcelToDatabaseSwitch.register(new ExcelToDatabaseSwitch("grid-character", SlGridCharacterStructureDto.class, this.getClass()));
    }
}
