package com.zhonghe.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.helper.IdGeneratorHelper;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.system.constants.SlOneMapConstants;
import com.zhonghe.system.domain.SlMarketEnterpriseOnenet;
import com.zhonghe.system.domain.SlMarketOnenet;
import com.zhonghe.system.domain.SlMarketPersonalOnenet;
import com.zhonghe.system.domain.dto.OneNetResponseDto;
import com.zhonghe.system.domain.thirdparty.ZsjEntregInfo;
import com.zhonghe.system.domain.thirdparty.ZsjEntregInfoAudit;
import com.zhonghe.system.domain.thirdparty.bo.ZsjEntregInfoBo;
import com.zhonghe.system.domain.thirdparty.vo.ZsjEntregInfoVo;
import com.zhonghe.system.enmu.OneNetConfig;
import com.zhonghe.system.mapper.ZsjEntregInfoAuditMapper;
import com.zhonghe.system.mapper.ZsjEntregInfoMapper;
import com.zhonghe.system.service.IOneNetHttpService;
import com.zhonghe.system.service.ISlMarketEnterpriseService;
import com.zhonghe.system.service.ISlMarketOnenetService;
import com.zhonghe.system.service.ISlMarketPersonalService;
import com.zhonghe.system.service.IZsjEntregInfoService;
import lombok.RequiredArgsConstructor;
import net.postgis.jdbc.geometry.Geometry;
import net.postgis.jdbc.geometry.Point;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * 【请填写功能名称】Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-09-11
 */
@RequiredArgsConstructor
@Service
public class ZsjEntregInfoServiceImpl implements IZsjEntregInfoService {

    private final ZsjEntregInfoMapper baseMapper;

    private final IOneNetHttpService oneNetHttpService;

    private final ISlMarketOnenetService marketOnenetService;

    private final ISlMarketEnterpriseService marketEnterpriseService;

    private final ISlMarketPersonalService marketPersonalService;

    private final Executor executor;

    private final ZsjEntregInfoAuditMapper entregInfoAuditMapper;

    /**
     * 查询【请填写功能名称】
     */
    @Override
    public ZsjEntregInfoVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }


    /**
     * 查询【请填写功能名称】列表
     */
    @Override
    public List<ZsjEntregInfoVo> queryList(ZsjEntregInfoBo bo) {
        LambdaQueryWrapper<ZsjEntregInfo> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ZsjEntregInfo> buildQueryWrapper(ZsjEntregInfoBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ZsjEntregInfo> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getPripid()), ZsjEntregInfo::getPripid, bo.getPripid());
        lqw.eq(StringUtils.isNotBlank(bo.getUniscid()), ZsjEntregInfo::getUniscid, bo.getUniscid());
        lqw.eq(StringUtils.isNotBlank(bo.getRegno()), ZsjEntregInfo::getRegno, bo.getRegno());
        lqw.like(StringUtils.isNotBlank(bo.getEntname()), ZsjEntregInfo::getEntname, bo.getEntname());
        lqw.like(StringUtils.isNotBlank(bo.getTypename()), ZsjEntregInfo::getTypename, bo.getTypename());
        lqw.eq(StringUtils.isNotBlank(bo.getOpscope()), ZsjEntregInfo::getOpscope, bo.getOpscope());
        lqw.eq(StringUtils.isNotBlank(bo.getAicid()), ZsjEntregInfo::getAicid, bo.getAicid());
        lqw.eq(StringUtils.isNotBlank(bo.getRegcap()), ZsjEntregInfo::getRegcap, bo.getRegcap());
        lqw.eq(StringUtils.isNotBlank(bo.getCountry()), ZsjEntregInfo::getCountry, bo.getCountry());
        lqw.eq(StringUtils.isNotBlank(bo.getEstdate()), ZsjEntregInfo::getEstdate, bo.getEstdate());
        lqw.eq(StringUtils.isNotBlank(bo.getApprdate()), ZsjEntregInfo::getApprdate, bo.getApprdate());
        lqw.eq(StringUtils.isNotBlank(bo.getOpstate()), ZsjEntregInfo::getOpstate, bo.getOpstate());
        lqw.like(StringUtils.isNotBlank(bo.getLerepname()), ZsjEntregInfo::getLerepname, bo.getLerepname());
        lqw.eq(StringUtils.isNotBlank(bo.getOpfrom()), ZsjEntregInfo::getOpfrom, bo.getOpfrom());
        lqw.eq(StringUtils.isNotBlank(bo.getOpto()), ZsjEntregInfo::getOpto, bo.getOpto());
        lqw.eq(StringUtils.isNotBlank(bo.getIndustryphy()), ZsjEntregInfo::getIndustryphy, bo.getIndustryphy());
        lqw.eq(StringUtils.isNotBlank(bo.getIndustryco()), ZsjEntregInfo::getIndustryco, bo.getIndustryco());
        lqw.eq(StringUtils.isNotBlank(bo.getDom()), ZsjEntregInfo::getDom, bo.getDom());
        lqw.eq(StringUtils.isNotBlank(bo.getFaxnumber()), ZsjEntregInfo::getFaxnumber, bo.getFaxnumber());
        lqw.eq(StringUtils.isNotBlank(bo.getEmpnum()), ZsjEntregInfo::getEmpnum, bo.getEmpnum());
        lqw.eq(StringUtils.isNotBlank(bo.getDatDt()), ZsjEntregInfo::getDatDt, bo.getDatDt());
        lqw.eq(StringUtils.isNotBlank(bo.getEtlDt()), ZsjEntregInfo::getEtlDt, bo.getEtlDt());
        lqw.eq(StringUtils.isNotBlank(bo.getGldmStaDt()), ZsjEntregInfo::getGldmStaDt, bo.getGldmStaDt());
        lqw.eq(StringUtils.isNotBlank(bo.getGldmEndDt()), ZsjEntregInfo::getGldmEndDt, bo.getGldmEndDt());
        lqw.eq(StringUtils.isNotBlank(bo.getGldmDelFlag()), ZsjEntregInfo::getGldmDelFlag, bo.getGldmDelFlag());
        lqw.eq(StringUtils.isNotBlank(bo.getDsesUuid()), ZsjEntregInfo::getDsesUuid, bo.getDsesUuid());
        lqw.eq(StringUtils.isNotBlank(bo.getIndustrycoVal()), ZsjEntregInfo::getIndustrycoVal, bo.getIndustrycoVal());
        lqw.eq(StringUtils.isNotBlank(bo.getReservedField1()), ZsjEntregInfo::getReservedField1, bo.getReservedField1());
        lqw.eq(StringUtils.isNotBlank(bo.getReservedField2()), ZsjEntregInfo::getReservedField2, bo.getReservedField2());
        lqw.eq(StringUtils.isNotBlank(bo.getDataSharingCdTimeIdatat()), ZsjEntregInfo::getDataSharingCdTimeIdatat, bo.getDataSharingCdTimeIdatat());
        return lqw;
    }

    /**
     * 新增【请填写功能名称】
     */
    @Override
    public Boolean insertByBo(ZsjEntregInfoBo bo) {
        ZsjEntregInfo add = BeanUtil.toBean(bo, ZsjEntregInfo.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改【请填写功能名称】
     */
    @Override
    public Boolean updateByBo(ZsjEntregInfoBo bo) {
        ZsjEntregInfo update = BeanUtil.toBean(bo, ZsjEntregInfo.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ZsjEntregInfo entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除【请填写功能名称】
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }


    @Override
    public void refresh() {

        executor.execute(() -> {
            Long count = baseMapper.selectCount(new LambdaQueryWrapper<>());

            int page = 1;
            int ct = 1000;

            while ((long) ct * page <= (count + 1000)) {
                PageQuery pageQuery = new PageQuery();
                pageQuery.setPageSize(ct);
                pageQuery.setPageNum(page);
                Page<ZsjEntregInfo> selectPage = baseMapper.selectPage(pageQuery.build(), new LambdaQueryWrapper<>());
                List<ZsjEntregInfo> records = selectPage.getRecords();

                this.insertGis(records);
                page++;
            }
        });
    }

    private void insertGis(List<ZsjEntregInfo> records) {
        List<SlMarketEnterpriseOnenet> enterpriseOnenets = new ArrayList<>(records.size());
        List<SlMarketPersonalOnenet> personalOnenets = new ArrayList<>();
        List<SlMarketOnenet> marketOnenets = new ArrayList<>(records.size());

        records.forEach(z -> {

            SlMarketOnenet marketOnenet = new SlMarketOnenet();
            marketOnenet.setMarketId(IdGeneratorHelper.next());
            marketOnenet.setMarketDataId(z.getPripid());
            marketOnenet.setMarketAddress(z.getDom());
            marketOnenet.setMarketCode(z.getUniscid());
            marketOnenet.setMarketName(z.getEntname());
            marketOnenet.setBusinessScope(z.getOpscope());
            marketOnenet.setMarketManager(z.getLerepname());

            marketOnenet.setCreateBy("schedule");
            marketOnenet.setUpdateBy("schedule");

            if (StringUtils.isNotEmpty(z.getDom())) {
                OneNetResponseDto request = oneNetHttpService.request(OneNetConfig.ADDRESS, new String[]{z.getDom()}, null);
                if (ObjectUtils.isNotEmpty(request)) {
                    List<Map<String, String>> data = request.getData().getData();
                    Map<String, String> select = OneNetConfig.ADDRESS.getSelector().select(data, "GXSJ");
                    if (!CollectionUtil.isEmpty(select)) {
                        String zxjd = select.get("ZXJD");
                        String zxwd = select.get("ZXWD");
                        if (StringUtils.isNotEmpty(zxjd) && StringUtils.isNotEmpty(zxwd)) {
                            Geometry geometry = new Point(Double.parseDouble(zxjd), Double.parseDouble(zxwd));
                            marketOnenet.setMarketGem(geometry);
                        }
                    }
                }
            }

            if (SlOneMapConstants.MARKET_TYPE_PERSONAL.equals(z.getTypename())) {
                SlMarketPersonalOnenet personalOnenet = new SlMarketPersonalOnenet();
                personalOnenet.setMarketPersonalOnenetId(IdGeneratorHelper.next());
                personalOnenet.setMarketPersonalId(z.getPripid());

                personalOnenet.setPersonalName(z.getLerepname());
                personalOnenet.setPersonalAddress(z.getDom());
                personalOnenet.setPersonalType(z.getTypename());
                personalOnenet.setPersonalCreditCode(z.getUniscid());
                personalOnenet.setLegalPerson(z.getLerepname());
                personalOnenet.setOperatingScope(z.getOpscope());

                personalOnenet.setCreateBy("schedule");
                personalOnenet.setUpdateBy("schedule");
                personalOnenet.setMarketPersonalGem(marketOnenet.getMarketGem());

                personalOnenets.add(personalOnenet);
            } else {
                SlMarketEnterpriseOnenet enterpriseOnenet = new SlMarketEnterpriseOnenet();
                enterpriseOnenet.setMarketEnterpriseOnenetId(IdGeneratorHelper.next());
                enterpriseOnenet.setMarketEnterpriseId(z.getPripid());
                enterpriseOnenet.setEnterpriseType(z.getTypename());

                enterpriseOnenet.setEnterpriseName(z.getEntname());
                enterpriseOnenet.setEnterpriseAddress(z.getDom());
                enterpriseOnenet.setEnterpriseCreditCode(z.getUniscid());
                enterpriseOnenet.setLegalPerson(z.getLerepname());
                enterpriseOnenet.setOperatingScope(z.getOpscope());


                enterpriseOnenet.setCreateBy("schedule");
                enterpriseOnenet.setUpdateBy("schedule");
                enterpriseOnenet.setMarketEnterpriseGem(marketOnenet.getMarketGem());
                enterpriseOnenets.add(enterpriseOnenet);
            }

            marketOnenets.add(marketOnenet);
        });
        if (CollectionUtil.isNotEmpty(marketOnenets)) {
            marketOnenetService.insertBatch(marketOnenets);
        }
        if (CollectionUtil.isNotEmpty(personalOnenets)) {
            marketPersonalService.insertBatch(personalOnenets);
        }
        if (CollectionUtil.isNotEmpty(enterpriseOnenets)) {
            marketEnterpriseService.insertBatch(enterpriseOnenets);
        }
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    @DS("data")
    public void updateYesterdayGisInformation() {

        Long count = entregInfoAuditMapper.selectCount(new LambdaQueryWrapper<>());

        int page = 1;
        int ct = 1000;

        while ((long) ct * page <= (count + 1000)) {
            PageQuery pageQuery = new PageQuery();
            pageQuery.setPageSize(ct);
            pageQuery.setPageNum(page);
            Page<ZsjEntregInfoAudit> selectPage = entregInfoAuditMapper.selectPage(pageQuery.build(), new LambdaQueryWrapper<>());
            Set<String> propids = selectPage.getRecords().stream().map(ZsjEntregInfoAudit::getPripid).collect(Collectors.toSet());
            List<ZsjEntregInfo> filter = baseMapper.selectList(new LambdaQueryWrapper<ZsjEntregInfo>().in(ZsjEntregInfo::getPripid, propids));
            this.insertGis(filter);
            page++;

            entregInfoAuditMapper.delete(new LambdaQueryWrapper<ZsjEntregInfoAudit>().in(ZsjEntregInfoAudit::getPripid, propids));
        }
    }
}
