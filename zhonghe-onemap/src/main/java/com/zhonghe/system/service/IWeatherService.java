package com.zhonghe.system.service;

import com.zhonghe.system.auto.IParamService;
import com.zhonghe.system.domain.thirdparty.bo.SlWeatherBo;
import com.zhonghe.system.domain.thirdparty.vo.SlStationWeatherVo;
import com.zhonghe.system.domain.thirdparty.vo.SlWeatherStatisticVo;

import java.text.ParseException;
import java.util.List;

/**
 * @description:
 * @author: cq
 * @date: 2023/8/21 16:44
 */

public interface IWeatherService extends IParamService {


    List<SlWeatherStatisticVo> query(SlWeatherBo bo) throws ParseException;

    List<SlStationWeatherVo> queryStationWeather(SlWeatherBo bo) throws ParseException;

    SlStationWeatherVo queryWeather(SlWeatherBo bo) throws ParseException;
}
