package com.zhonghe.system.service;

import com.zhonghe.system.domain.bo.SlVolunteerBo;
import com.zhonghe.system.domain.vo.SlVolunteerVo;

import java.util.Collection;
import java.util.List;

/**
 * 志愿者Service接口
 *
 * <AUTHOR>
 * @date 2023-08-03
 */
public interface ISlVolunteerService {

    /**
     * 查询志愿者
     */
    SlVolunteerVo queryById(Long volunteerId);


    /**
     * 查询志愿者列表
     */
    List<SlVolunteerVo> queryList(SlVolunteerBo bo);

    /**
     * 修改志愿者
     */
    Boolean insertByBo(SlVolunteerBo bo);

    /**
     * 修改志愿者
     */
    Boolean updateByBo(SlVolunteerBo bo);

    /**
     * 校验并批量删除志愿者信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
