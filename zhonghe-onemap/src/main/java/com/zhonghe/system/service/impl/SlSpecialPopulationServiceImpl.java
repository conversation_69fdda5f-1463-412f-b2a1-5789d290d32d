package com.zhonghe.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.zhonghe.common.helper.IdGeneratorHelper;
import com.zhonghe.system.domain.SlSpecialPopulation;
import com.zhonghe.system.domain.dto.SlSpecialPopulationDto;
import com.zhonghe.system.excel.ExcelToDatabaseSwitch;
import com.zhonghe.system.mapper.SlSpecialPopulationMapper;
import com.zhonghe.system.service.ISlSpecialPopulationService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.util.List;

/**
 * 老年失能Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-09-01
 */
@RequiredArgsConstructor
@Service
public class SlSpecialPopulationServiceImpl implements ISlSpecialPopulationService {

    private final SlSpecialPopulationMapper baseMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void load(List<?> result) throws ParseException {
        List<SlSpecialPopulation> slSpecialPopulations = BeanUtil.copyToList(result, SlSpecialPopulation.class);
        slSpecialPopulations.forEach(sp->{
            sp.setSpecialId(IdGeneratorHelper.next());
        });

        baseMapper.insertBatch(slSpecialPopulations);
    }

    @Override
    public void register() {
        ExcelToDatabaseSwitch.register("special-p", SlSpecialPopulationDto.class, this.getClass());
    }
}
