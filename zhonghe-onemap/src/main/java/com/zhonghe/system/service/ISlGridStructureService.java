package com.zhonghe.system.service;

import com.zhonghe.system.domain.bo.grid.SlGridStructureBo;
import com.zhonghe.system.domain.vo.grid.SlGridStructureVo;

import java.util.Collection;
import java.util.List;

/**
 * 网格职位架构Service接口
 *
 * <AUTHOR>
 * @date 2023-08-02
 */
public interface ISlGridStructureService {

    /**
     * 查询网格职位架构
     */
    SlGridStructureVo queryById(Long gridStructureId);


    /**
     * 查询网格职位架构列表
     */
    List<SlGridStructureVo> queryList(SlGridStructureBo bo);

    /**
     * 修改网格职位架构
     */
    Boolean insertByBo(SlGridStructureBo bo);

    /**
     * 修改网格职位架构
     */
    Boolean updateByBo(SlGridStructureBo bo);

    /**
     * 校验并批量删除网格职位架构信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
