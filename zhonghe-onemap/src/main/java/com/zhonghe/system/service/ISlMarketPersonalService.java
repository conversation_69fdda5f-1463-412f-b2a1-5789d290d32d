package com.zhonghe.system.service;

import com.zhonghe.system.domain.SlMarketPersonalOnenet;
import com.zhonghe.system.domain.bo.SlMarketPersonalBo;
import com.zhonghe.system.domain.vo.SlMarketPersonalVo;

import java.util.Collection;
import java.util.List;

/**
 * 【请填写功能名称】Service接口
 *
 * <AUTHOR>
 * @date 2023-08-14
 */
public interface ISlMarketPersonalService {

    /**
     * 查询【请填写功能名称】
     */
    SlMarketPersonalVo queryById(Long marketPersonalId);


    /**
     * 查询【请填写功能名称】列表
     */
    List<SlMarketPersonalVo> queryList(SlMarketPersonalBo bo);

    /**
     * 修改【请填写功能名称】
     */
    Boolean insertByBo(SlMarketPersonalBo bo);

    /**
     * 修改【请填写功能名称】
     */
    Boolean updateByBo(SlMarketPersonalBo bo);

    /**
     * 校验并批量删除【请填写功能名称】信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    void refresh();

    void insertBatch(List<SlMarketPersonalOnenet> personalOnenets);
}
