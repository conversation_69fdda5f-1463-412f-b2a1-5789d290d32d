package com.zhonghe.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.system.constants.SlOneMapConstants;
import com.zhonghe.system.domain.dto.OneNetResponseDto;
import com.zhonghe.system.domain.thirdparty.SlWeatherSign;
import com.zhonghe.system.domain.thirdparty.bo.SlWeatherSignBo;
import com.zhonghe.system.domain.thirdparty.vo.SlWeatherSignVo;
import com.zhonghe.system.enmu.OneNetConfig;
import com.zhonghe.system.mapper.SlWeatherSignMapper;
import com.zhonghe.system.service.IOneNetHttpService;
import com.zhonghe.system.service.ISlWeatherSignService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 【请填写功能名称】Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-08-21
 */
@RequiredArgsConstructor
@Service
@DS("data")
public class SlWeatherSignServiceImpl implements ISlWeatherSignService {

    private final SlWeatherSignMapper baseMapper;

    private final IOneNetHttpService httpService;

    /**
     * 查询【请填写功能名称】
     */
    @Override
    public SlWeatherSignVo queryById(String fywzjid) {
        List<SlWeatherSignVo> fywzjid1 = baseMapper.selectVoList(new QueryWrapper<SlWeatherSign>().eq("fywzjid", fywzjid));
        if (CollectionUtil.isNotEmpty(fywzjid1)) {
            return fywzjid1.get(0);
        }
        return null;
    }


    /**
     * 查询【请填写功能名称】列表
     */
    @Override
    public List<SlWeatherSignVo> queryList(SlWeatherSignBo bo) {
        LambdaQueryWrapper<SlWeatherSign> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SlWeatherSign> buildQueryWrapper(SlWeatherSignBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SlWeatherSign> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getFywzjid()), SlWeatherSign::getFywzjid, bo.getFywzjid());
        lqw.eq(StringUtils.isNotBlank(bo.getXzsj()), SlWeatherSign::getXzsj, bo.getXzsj());
        lqw.eq(StringUtils.isNotBlank(bo.getZlsj()), SlWeatherSign::getZlsj, bo.getZlsj());
        lqw.eq(StringUtils.isNotBlank(bo.getZlbs()), SlWeatherSign::getZlbs, bo.getZlbs());
        lqw.eq(StringUtils.isNotBlank(bo.getPch()), SlWeatherSign::getPch, bo.getPch());
        return lqw;
    }

    /**
     * 新增【请填写功能名称】
     */
    @Override
    public Boolean insertByBo(SlWeatherSignBo bo) {
        SlWeatherSign add = BeanUtil.toBean(bo, SlWeatherSign.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setFywzjid(add.getFywzjid());
        }
        return flag;
    }

    /**
     * 修改【请填写功能名称】
     */
    @Override
    public Boolean updateByBo(SlWeatherSignBo bo) {
        SlWeatherSign update = BeanUtil.toBean(bo, SlWeatherSign.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SlWeatherSign entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除【请填写功能名称】
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public TableDataInfo<SlWeatherSign> queryPage(PageQuery page) throws ParseException {

        List<SlWeatherSign> slWeatherSignVos = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
        for (String type : SlOneMapConstants.WEATHER_QUESTION) {
            OneNetResponseDto request = httpService.request(OneNetConfig.DISASTER, new String[]{type}, null);
            if (request != null) {
                List<Map<String, String>> data = request.getData().getData();
                for (Map<String, String> d : data) {
                    SlWeatherSign vo = new SlWeatherSign();
                    vo.setPch(d.get("PCH"));
                    vo.setYjxhnr(d.get("YJXXNR"));
                    if (d.containsKey("FBSJ")) {
                        Date fbsj = sdf.parse(d.get("FBSJ"));
                        vo.setFbsj(fbsj);
                    }
                    vo.setYxfw(d.get("YXFW"));
                    vo.setYjxhlx(type);
                    vo.setFywzjid(d.get("FYWZJID"));
                    vo.setYjxhjb(d.get("YJXHJB"));
                    if ("东莞市".equals(vo.getYxfw())) {
                        slWeatherSignVos.add(vo);
                    }
                }
            }
        }
        Collections.sort(slWeatherSignVos, (bean1, bean2) -> -bean1.getFbsj().compareTo(bean2.getFbsj()));
        baseMapper.insertOrUpdateBatch(slWeatherSignVos);
        /*IPage<SlWeatherSignVo> jcsj = baseMapper.selectVoPage(page.build(),
            new QueryWrapper<SlWeatherSign>().orderByDesc("fbsj"));*/
        return TableDataInfo.build(slWeatherSignVos);
    }

    @Override
    public Object[] params(String param) {
        return new Object[]{new PageQuery()};
    }
}
