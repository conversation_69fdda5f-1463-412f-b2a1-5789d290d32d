package com.zhonghe.system.service;

import com.zhonghe.system.domain.bo.grid.SlGridZoneBo;
import com.zhonghe.system.domain.vo.grid.SlGridZoneVo;

import java.util.Collection;
import java.util.List;

/**
 * 网格信息Service接口
 *
 * <AUTHOR>
 * @date 2023-08-02
 */
public interface ISlGridZoneService {

    /**
     * 查询网格信息
     */
    SlGridZoneVo queryById(Long gridZoneId);


    /**
     * 查询网格信息列表
     */
    List<SlGridZoneVo> queryList(SlGridZoneBo bo);

    /**
     * 修改网格信息
     */
    Boolean insertByBo(SlGridZoneBo bo);

    /**
     * 修改网格信息
     */
    Boolean updateByBo(SlGridZoneBo bo);

    /**
     * 校验并批量删除网格信息信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
