package com.zhonghe.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.zhonghe.common.helper.IdGeneratorHelper;
import com.zhonghe.system.domain.SlOldPopulation;
import com.zhonghe.system.domain.dto.SlOldPopulationDto;
import com.zhonghe.system.excel.ExcelToDatabaseSwitch;
import com.zhonghe.system.mapper.SlOldPopulationMapper;
import com.zhonghe.system.service.ISlOldPopulationService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.util.List;

/**
 * 老年失能Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-09-01
 */
@RequiredArgsConstructor
@Service
public class SlOldPopulationServiceImpl implements ISlOldPopulationService {

    private final SlOldPopulationMapper baseMapper;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void load(List<?> result) throws ParseException {

        List<SlOldPopulation> slOldPopulations = BeanUtil.copyToList(result, SlOldPopulation.class);
        slOldPopulations.forEach(o->{
            o.setOldId(IdGeneratorHelper.next());
        });
        baseMapper.insertBatch(slOldPopulations);
    }

    @Override
    public void register() {
        ExcelToDatabaseSwitch.register("old-p", SlOldPopulationDto.class, this.getClass());
    }
}
