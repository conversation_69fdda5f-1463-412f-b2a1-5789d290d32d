package com.zhonghe.system.service;

import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.system.domain.bo.gas.SlGasRestaurantGatherBo;
import com.zhonghe.system.domain.vo.gas.SlGasOneMapStatisticsVo;
import com.zhonghe.system.domain.vo.gas.SlGasRestaurantGatherGroupListVo;
import com.zhonghe.system.domain.vo.gas.SlGasRestaurantGatherStatisticVo;
import com.zhonghe.system.domain.vo.gas.SlGasRestaurantGatherVo;

import java.text.ParseException;
import java.util.Collection;
import java.util.List;

/**
 * 燃气统计Service接口
 *
 * <AUTHOR>
 * @date 2023-07-28
 */
public interface ISlGasRestaurantGatherService {

    /**
     * 查询燃气统计
     */
    SlGasRestaurantGatherVo queryById(Long gatherGasId);


    /**
     * 查询燃气统计列表
     */
    List<SlGasRestaurantGatherVo> queryList(SlGasRestaurantGatherBo bo) throws ParseException;

    /**
     * 修改燃气统计
     */
    Boolean insertByBo(SlGasRestaurantGatherBo bo);

    /**
     * 修改燃气统计
     */
    Boolean updateByBo(SlGasRestaurantGatherBo bo);

    /**
     * 校验并批量删除燃气统计信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 地图上统计燃气数据
     * @return
     */
    SlGasRestaurantGatherStatisticVo statistic();

    /**
     * 按查询条件分组查询
     * @param bo
     * @return
     */
    List<SlGasRestaurantGatherGroupListVo> groupList(SlGasRestaurantGatherBo bo) throws ParseException;

    /**
     * 分页查询
     * @param bo
     * @param pageQuery
     * @return
     */
    TableDataInfo<SlGasRestaurantGatherVo> queryPageList(SlGasRestaurantGatherBo bo, PageQuery pageQuery) throws ParseException;

    /**
     * 气瓶首页单位统计
     * @return
     */
    SlGasOneMapStatisticsVo onemapUnitStatistics();

    /**
     * 刷新onenet地理数据
     */
    void refreshOnenet();
}
