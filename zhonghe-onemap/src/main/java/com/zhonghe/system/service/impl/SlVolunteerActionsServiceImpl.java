package com.zhonghe.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.helper.IdGeneratorHelper;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.system.domain.SlVolunteerActions;
import com.zhonghe.system.domain.bo.SlVolunteerActionsBo;
import com.zhonghe.system.domain.dto.SlVolunteerActionDto;
import com.zhonghe.system.domain.vo.SlVolunteerActionsVo;
import com.zhonghe.system.excel.ExcelToDatabaseSwitch;
import com.zhonghe.system.excel.service.ILoadExcelService;
import com.zhonghe.system.mapper.SlVolunteerActionsMapper;
import com.zhonghe.system.service.ISlVolunteerActionsService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 志愿者活动Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-08-04
 */
@RequiredArgsConstructor
@Service
public class SlVolunteerActionsServiceImpl implements ISlVolunteerActionsService, ILoadExcelService {

    private final SlVolunteerActionsMapper baseMapper;

    /**
     * 查询志愿者活动
     */
    @Override
    public SlVolunteerActionsVo queryById(Long actionId) {
        return baseMapper.selectVoById(actionId);
    }


    /**
     * 查询志愿者活动列表
     */
    @Override
    public List<SlVolunteerActionsVo> queryList(SlVolunteerActionsBo bo) {
        LambdaQueryWrapper<SlVolunteerActions> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SlVolunteerActions> buildQueryWrapper(SlVolunteerActionsBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SlVolunteerActions> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getActionNumber()), SlVolunteerActions::getActionNumber, bo.getActionNumber());
        lqw.like(StringUtils.isNotBlank(bo.getActionName()), SlVolunteerActions::getActionName, bo.getActionName());
        lqw.eq(StringUtils.isNotBlank(bo.getActionContract()), SlVolunteerActions::getActionContract, bo.getActionContract());
        lqw.eq(StringUtils.isNotBlank(bo.getActionContractPhone()), SlVolunteerActions::getActionContractPhone, bo.getActionContractPhone());
        lqw.eq(StringUtils.isNotBlank(bo.getActionPublishOrg()), SlVolunteerActions::getActionPublishOrg, bo.getActionPublishOrg());
        lqw.eq(StringUtils.isNotBlank(bo.getActionPublishDate()), SlVolunteerActions::getActionPublishDate, bo.getActionPublishDate());
        lqw.eq(StringUtils.isNotBlank(bo.getActionAdditionalStatus()), SlVolunteerActions::getActionAdditionalStatus, bo.getActionAdditionalStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getActionStatus()), SlVolunteerActions::getActionStatus, bo.getActionStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getActionType()), SlVolunteerActions::getActionType, bo.getActionType());
        lqw.eq(StringUtils.isNotBlank(bo.getActionTag()), SlVolunteerActions::getActionTag, bo.getActionTag());
        lqw.eq(StringUtils.isNotBlank(bo.getActionStartTime()), SlVolunteerActions::getActionStartTime, bo.getActionStartTime());
        lqw.eq(StringUtils.isNotBlank(bo.getActionEndTime()), SlVolunteerActions::getActionEndTime, bo.getActionEndTime());
        lqw.eq(StringUtils.isNotBlank(bo.getActionEmploy()), SlVolunteerActions::getActionEmploy, bo.getActionEmploy());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), SlVolunteerActions::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增志愿者活动
     */
    @Override
    public Boolean insertByBo(SlVolunteerActionsBo bo) {
        SlVolunteerActions add = BeanUtil.toBean(bo, SlVolunteerActions.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setActionId(add.getActionId());
        }
        return flag;
    }

    /**
     * 修改志愿者活动
     */
    @Override
    public Boolean updateByBo(SlVolunteerActionsBo bo) {
        SlVolunteerActions update = BeanUtil.toBean(bo, SlVolunteerActions.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SlVolunteerActions entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除志愿者活动
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public TableDataInfo<SlVolunteerActionsVo> selectPage(SlVolunteerActionsBo bo, PageQuery pageQuery) {
        IPage<SlVolunteerActionsVo> voIPage = baseMapper.selectVoPage(pageQuery.build(), this.buildQueryWrapper(bo));
        return TableDataInfo.build(voIPage);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void load(List<?> result) {
        List<SlVolunteerActions> slVolunteerActions = BeanUtil.copyToList(result, SlVolunteerActions.class);
        slVolunteerActions.forEach(a -> a.setActionId(IdGeneratorHelper.next()));

        baseMapper.insertBatch(slVolunteerActions);

    }

    @Override
    public void register() {
        ExcelToDatabaseSwitch.register(new ExcelToDatabaseSwitch("v-action", SlVolunteerActionDto.class, this.getClass()));
    }
}
