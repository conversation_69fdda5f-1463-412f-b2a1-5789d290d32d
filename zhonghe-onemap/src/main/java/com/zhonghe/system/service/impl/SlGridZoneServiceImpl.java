package com.zhonghe.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zhonghe.common.helper.IdGeneratorHelper;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.system.domain.SlGridZone;
import com.zhonghe.system.domain.bo.grid.SlGridZoneBo;
import com.zhonghe.system.domain.dto.SlGridZoneDto;
import com.zhonghe.system.domain.vo.grid.SlGridZoneVo;
import com.zhonghe.system.excel.ExcelToDatabaseSwitch;
import com.zhonghe.system.excel.service.ILoadExcelService;
import com.zhonghe.system.mapper.SlGridZoneMapper;
import com.zhonghe.system.service.ISlGridZoneService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 网格信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-08-02
 */
@RequiredArgsConstructor
@Service
public class SlGridZoneServiceImpl implements ISlGridZoneService, ILoadExcelService {

    private final SlGridZoneMapper baseMapper;

    /**
     * 查询网格信息
     */
    @Override
    public SlGridZoneVo queryById(Long gridZoneId) {
        return baseMapper.selectVoById(gridZoneId);
    }


    /**
     * 查询网格信息列表
     */
    @Override
    public List<SlGridZoneVo> queryList(SlGridZoneBo bo) {
        LambdaQueryWrapper<SlGridZone> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SlGridZone> buildQueryWrapper(SlGridZoneBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SlGridZone> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getGridGroupName()), SlGridZone::getGridGroupName, bo.getGridGroupName());
        lqw.like(StringUtils.isNotBlank(bo.getGridZoneName()), SlGridZone::getGridZoneName, bo.getGridZoneName());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), SlGridZone::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增网格信息
     */
    @Override
    public Boolean insertByBo(SlGridZoneBo bo) {
        SlGridZone add = BeanUtil.toBean(bo, SlGridZone.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setGridZoneId(add.getGridZoneId());
        }
        return flag;
    }

    /**
     * 修改网格信息
     */
    @Override
    public Boolean updateByBo(SlGridZoneBo bo) {
        SlGridZone update = BeanUtil.toBean(bo, SlGridZone.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SlGridZone entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除网格信息
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void load(List<?> result) {
        baseMapper.delete(new LambdaQueryWrapper<>());
        List<SlGridZone> slGridZones = BeanUtil.copyToList(result, SlGridZone.class);
        slGridZones.forEach(d -> d.setGridZoneId(IdGeneratorHelper.next()));

        baseMapper.insertBatch(slGridZones);
    }

    @Override
    public void register() {
        ExcelToDatabaseSwitch.register(new ExcelToDatabaseSwitch("gird-zone", SlGridZoneDto.class, this.getClass()));
    }
}
