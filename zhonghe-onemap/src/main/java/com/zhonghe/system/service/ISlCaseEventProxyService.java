package com.zhonghe.system.service;

import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.system.domain.thirdparty.SlCaseEventProxy;
import com.zhonghe.system.domain.thirdparty.bo.SlCaseEventProxyBo;
import com.zhonghe.system.domain.thirdparty.vo.SlCaseEventMainSubjectStatisticsVo;
import com.zhonghe.system.domain.thirdparty.vo.SlCaseEventProxyDetailVo;
import com.zhonghe.system.domain.thirdparty.vo.SlCaseEventProxyOneMapVo;
import com.zhonghe.system.domain.thirdparty.vo.SlCaseEventProxyStatisticsVo;
import com.zhonghe.system.domain.thirdparty.vo.SlCaseEventProxyVo;
import com.zhonghe.system.domain.thirdparty.vo.SlCaseEventStatisticsVo;

import java.text.ParseException;
import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * 【请填写功能名称】Service接口
 *
 * <AUTHOR>
 * @date 2023-08-14
 */
public interface ISlCaseEventProxyService {

    /**
     * 查询【请填写功能名称】
     */
    SlCaseEventProxyVo queryById(String fywzj);


    /**
     * 查询【请填写功能名称】列表
     */
    List<SlCaseEventProxyVo> queryList(SlCaseEventProxyBo bo);

    /**
     * 修改【请填写功能名称】
     */
    Boolean insertByBo(SlCaseEventProxyBo bo);

    /**
     * 修改【请填写功能名称】
     */
    Boolean updateByBo(SlCaseEventProxyBo bo);

    /**
     * 校验并批量删除【请填写功能名称】信息
     */
    Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

    SlCaseEventProxyOneMapVo caseStatistics();

    SlCaseEventProxyStatisticsVo caseGroupStatistics(SlCaseEventProxyBo bo) throws ParseException;

    List<SlCaseEventStatisticsVo> caseEventStatisticByAddresses(Set<String> addresses);

    TableDataInfo<SlCaseEventProxyVo> page(PageQuery pageQuery, SlCaseEventProxyBo bo);

    List<SlCaseEventProxy> queryEventCaseByAddress(String address);

    SlCaseEventProxyDetailVo queryDetail(String asjid);

    List<SlCaseEventMainSubjectStatisticsVo> caseTypeStatistics();

    long queryMarkerCaseEventCount();
}
