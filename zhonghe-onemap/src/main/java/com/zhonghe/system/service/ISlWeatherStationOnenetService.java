package com.zhonghe.system.service;

import com.zhonghe.system.domain.SlWeatherStationOnenet;
import com.zhonghe.system.domain.bo.SlWeatherStationOnenetBo;
import com.zhonghe.system.domain.vo.SlWeatherStationOnenetVo;

import java.util.Collection;
import java.util.List;

/**
 * 【请填写功能名称】Service接口
 *
 * <AUTHOR>
 * @date 2023-08-21
 */
public interface ISlWeatherStationOnenetService {

    /**
     * 查询【请填写功能名称】
     */
    SlWeatherStationOnenetVo queryById(Long stationOnenetId);


    /**
     * 查询【请填写功能名称】列表
     */
    List<SlWeatherStationOnenetVo> queryList(SlWeatherStationOnenetBo bo);

    /**
     * 修改【请填写功能名称】
     */
    Boolean insertByBo(SlWeatherStationOnenetBo bo);

    /**
     * 修改【请填写功能名称】
     */
    Boolean updateByBo(SlWeatherStationOnenetBo bo);

    /**
     * 校验并批量删除【请填写功能名称】信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    void updateGis();

    List<SlWeatherStationOnenet> queryByStationName(String[] stationNames);
}
