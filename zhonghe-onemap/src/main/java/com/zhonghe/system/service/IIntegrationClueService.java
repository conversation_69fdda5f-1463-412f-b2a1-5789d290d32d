package com.zhonghe.system.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhonghe.system.domain.thirdparty.IntegrationClue;
import com.zhonghe.system.domain.thirdparty.SlCaseEventProxy;
import com.zhonghe.system.domain.thirdparty.vo.IntegrationClueVo;

import java.util.List;

/**
 * @description:
 * @author: cq
 * @date: 2023/11/7 10:42
 */

public interface IIntegrationClueService {


    List<IntegrationClueVo> selectListByAddress(String address);


    List<IntegrationClueVo> selectListByAddresses(List<String> addresses);

    List<SlCaseEventProxy> queryEventCaseByAddress(String address);

    IPage<IntegrationClueVo> selectVoPageList(Page<IntegrationClue> build, LambdaQueryWrapper<IntegrationClue> lqw);

    List<IntegrationClueVo> queryEventCaseByAsjid(String asjid);
}
