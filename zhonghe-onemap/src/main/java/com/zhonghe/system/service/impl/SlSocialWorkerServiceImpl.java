package com.zhonghe.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zhonghe.common.helper.IdGeneratorHelper;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.system.constants.SlOneMapConstants;
import com.zhonghe.system.domain.SlGridStructureCharacter;
import com.zhonghe.system.domain.SlGridZone;
import com.zhonghe.system.domain.SlGridZoneCharacterRel;
import com.zhonghe.system.domain.SlSocialWorker;
import com.zhonghe.system.domain.SlSocialWorkerOnenet;
import com.zhonghe.system.domain.bo.SlSocialWorkerBo;
import com.zhonghe.system.domain.dto.OneNetResponseDto;
import com.zhonghe.system.domain.dto.SlSocialWorkerDto;
import com.zhonghe.system.domain.vo.SlSocialWorkerVo;
import com.zhonghe.system.enmu.OneNetConfig;
import com.zhonghe.system.excel.ExcelToDatabaseSwitch;
import com.zhonghe.system.excel.service.ILoadExcelService;
import com.zhonghe.system.mapper.SlGridStructureCharacterMapper;
import com.zhonghe.system.mapper.SlGridZoneCharacterRelMapper;
import com.zhonghe.system.mapper.SlGridZoneMapper;
import com.zhonghe.system.mapper.SlSocialWorkerMapper;
import com.zhonghe.system.mapper.SlSocialWorkerOnenetMapper;
import com.zhonghe.system.service.IOneNetHttpService;
import com.zhonghe.system.service.ISlSocialWorkerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.postgis.jdbc.geometry.Point;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 石龙社工Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-08-03
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class SlSocialWorkerServiceImpl implements ISlSocialWorkerService, ILoadExcelService {

    private final SlSocialWorkerMapper baseMapper;

    private final SlGridStructureCharacterMapper structureCharacterMapper;

    private final SlGridZoneMapper slGridZoneMapper;

    private final SlGridZoneCharacterRelMapper zoneCharacterRelMapper;

    private final IOneNetHttpService oneNetHttpService;

    private final SlSocialWorkerOnenetMapper socialWorkerOnenetMapper;

    /**
     * 查询石龙社工
     */
    @Override
    public SlSocialWorkerVo queryById(Long socialWorkerId) {
        return baseMapper.selectVoById(socialWorkerId);
    }


    /**
     * 查询石龙社工列表
     */
    @Override
    public List<SlSocialWorkerVo> queryList(SlSocialWorkerBo bo) {
        LambdaQueryWrapper<SlSocialWorker> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SlSocialWorker> buildQueryWrapper(SlSocialWorkerBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SlSocialWorker> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getWorkerName()), SlSocialWorker::getWorkerName, bo.getWorkerName());
        lqw.eq(bo.getWorkerAge() != null, SlSocialWorker::getWorkerAge, bo.getWorkerAge());
        lqw.eq(StringUtils.isNotBlank(bo.getSex()), SlSocialWorker::getSex, bo.getSex());
        lqw.eq(StringUtils.isNotBlank(bo.getWorkerIdNumber()), SlSocialWorker::getWorkerIdNumber, bo.getWorkerIdNumber());
        lqw.eq(StringUtils.isNotBlank(bo.getWorkerPhone()), SlSocialWorker::getWorkerPhone, bo.getWorkerPhone());
        lqw.eq(StringUtils.isNotBlank(bo.getMark()), SlSocialWorker::getMark, bo.getMark());
        lqw.eq(StringUtils.isNotBlank(bo.getWorkPosition()), SlSocialWorker::getWorkPosition, bo.getWorkPosition());
        lqw.eq(StringUtils.isNotBlank(bo.getStandardPosition()), SlSocialWorker::getStandardPosition, bo.getStandardPosition());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), SlSocialWorker::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增石龙社工
     */
    @Override
    public Boolean insertByBo(SlSocialWorkerBo bo) {
        SlSocialWorker add = BeanUtil.toBean(bo, SlSocialWorker.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setSocialWorkerId(add.getSocialWorkerId());
        }
        return flag;
    }

    /**
     * 修改石龙社工
     */
    @Override
    public Boolean updateByBo(SlSocialWorkerBo bo) {
        SlSocialWorker update = BeanUtil.toBean(bo, SlSocialWorker.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SlSocialWorker entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除石龙社工
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void requestOnenetData() {
        List<SlSocialWorker> slSocialWorkers = baseMapper.selectList();
        this.refreshOneNetData(slSocialWorkers);
        //baseMapper.updateBatchById(slSocialWorkers);
    }


    private void refreshOneNetData(List<SlSocialWorker> workers) {

        //List<Long> deleteIds = new ArrayList<>(workers.size());

        List<SlSocialWorkerOnenet> onenetWorkers = new ArrayList<>(workers.size());

        workers.forEach(w -> {
            SlSocialWorkerOnenet onenet = new SlSocialWorkerOnenet();
            BeanUtil.copyProperties(w, onenet);
            onenet.setOnenetWorkerId(IdGeneratorHelper.next());
            onenet.setSocialWorkerId(w.getSocialWorkerId());

            onenet.setOnenetSex("1".equals(w.getSex()) ? "女" : "男");
            onenet.setOnenetIdNo(w.getWorkerIdNumber());
            onenet.setOnenetIdType("身份证");
            onenet.setOnenetName(w.getWorkerName());
            onenet.setOnenetTelephone(w.getWorkerPhone());
            onenet.setOnenetWorkLocation(w.getWorkPosition());
            onenetWorkers.add(onenet);
            if (StringUtils.isNotEmpty(w.getStandardPosition())) {


                OneNetResponseDto addressRequest = oneNetHttpService.request(OneNetConfig.ADDRESS, new String[]{w.getStandardPosition()}, null);
                if (ObjectUtil.isNotNull(addressRequest)) {
                    List<Map<String, String>> addressData = addressRequest.getData().getData();

                    Map<String, String> addressSelectorData = OneNetConfig.ADDRESS.getSelector().select(addressData, "GXSJ");
                    if (CollectionUtil.isNotEmpty(addressSelectorData)) {
                        String jydwzxjd = addressSelectorData.get("ZXJD");
                        String jydwzxwd = addressSelectorData.get("ZXWD");
                        if (StringUtils.isNotEmpty(jydwzxjd) && StringUtils.isNotEmpty(jydwzxwd)) {
                            Point pt = new Point(Double.parseDouble(jydwzxjd), Double.parseDouble(jydwzxwd));
                            onenet.setSocialWorkerGem(pt);
                        }

                    }
                }
                onenet.setOnenetJson(JSONUtil.toJsonStr(addressRequest));

            }
            /*if (StringUtils.isNotEmpty(w.getWorkerIdNumber())) {
                OneNetResponseDto request = oneNetHttpService.request(OneNetConfig.POPULATION, new String[]{w.getWorkerIdNumber()}, null);
                if (ObjectUtil.isNotNull(request)) {
                    List<Map<String, String>> data = request.getData().getData();
                    // 处理数据
                    if (CollectionUtil.isNotEmpty(data)) {
                        deleteIds.add(w.getSocialWorkerId());
                        Map<String, String> personDetail = OneNetConfig.POPULATION.getSelector().select(data, "GXSJ");

                        SlSocialWorkerOnenet onenet = new SlSocialWorkerOnenet();
                        onenet.setOnenetWorkerId(IdGeneratorHelper.next());
                        onenet.setSocialWorkerId(w.getSocialWorkerId());

                        onenet.setOnenetSex(personDetail.getOrDefault("XB", null));
                        onenet.setOnenetIdNo(personDetail.getOrDefault("ZJHM", null));
                        onenet.setOnenetIdType(personDetail.getOrDefault("ZJZL", null));
                        onenet.setOnenetTelephone(personDetail.getOrDefault("LXDH", null));
                        onenet.setOnenetWorkLocation(personDetail.getOrDefault("JYDWDZ", null));

                        if (StringUtils.isNotEmpty(onenet.getOnenetWorkLocation())) {
                            OneNetResponseDto addressRequest = oneNetHttpService.request(OneNetConfig.ADDRESS, new String[]{onenet.getOnenetWorkLocation()}, null);
                            if (ObjectUtil.isNotNull(request)) {
                                List<Map<String, String>> addressData = addressRequest.getData().getData();

                                Map<String, String> addressSelectorData = OneNetConfig.ADDRESS.getSelector().select(addressData, "GXSJ");

                                String jydwzxjd = addressSelectorData.get("ZXJD");
                                String jydwzxwd = addressSelectorData.get("ZXWD");
                                if (StringUtils.isNotEmpty(jydwzxjd) && StringUtils.isNotEmpty(jydwzxwd)) {
                                    Point pt = new Point(Double.parseDouble(jydwzxjd), Double.parseDouble(jydwzxwd));
                                    onenet.setSocialWorkerGem(pt);
                                }
                            }
                        }

                        onenet.setOnenetName(w.getWorkerName());
                        w.setOnenetJson(JSONUtil.toJsonStr(data));
                        w.setWorkPosition(onenet.getOnenetWorkLocation());
                        w.setStandardPosition(onenet.getOnenetWorkLocation());

                        onenetWorkers.add(onenet);
                    } else {
                        log.error("一网共享平台没有找到对应的人物数据->名字:{},身份证：{}", w.getWorkerName(), w.getWorkerIdNumber());
                    }
                }
            }*/
        });
        /*if (CollectionUtil.isNotEmpty(deleteIds)) {
            socialWorkerOnenetMapper.delete(new LambdaQueryWrapper<SlSocialWorkerOnenet>().in(SlSocialWorkerOnenet::getSocialWorkerId, deleteIds));
        }*/
        socialWorkerOnenetMapper.insertOrUpdateBatch(onenetWorkers);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void load(List<?> result) {
        List<SlSocialWorker> slSocialWorkers = BeanUtil.copyToList(result, SlSocialWorker.class);
        List<SlGridStructureCharacter> characters = new ArrayList<>(slSocialWorkers.size());
        List<SlGridZoneCharacterRel> zoneCharacterRels = new ArrayList<>();

        List<SlGridZone> gridZones = slGridZoneMapper.selectList();
        Map<String, Long> gridZoneIds = gridZones.stream().collect(Collectors.toMap(SlGridZone::getGridZoneName, SlGridZone::getGridZoneId));

        slSocialWorkers.forEach(s -> {
            s.setSocialWorkerId(IdGeneratorHelper.next());

            SlGridStructureCharacter c = new SlGridStructureCharacter();
            c.setGridCharacterId(s.getSocialWorkerId());
            c.setCharacterName(s.getWorkerName());
            c.setCharacterSex(s.getSex());
            c.setCharacterWorkPhone(s.getWorkerPhone());
            if (StringUtils.isNotEmpty(s.getGridZoneName()) && gridZoneIds.containsKey(s.getGridZoneName())) {
                SlGridZoneCharacterRel rel = new SlGridZoneCharacterRel();
                rel.setZoneCharacterRelId(IdGeneratorHelper.next());
                rel.setGridCharacterId(s.getSocialWorkerId());
                rel.setGridZoneId(gridZoneIds.get(s.getGridZoneName()));
                rel.setCreateBy(s.getCreateBy());
                rel.setUpdateBy(s.getUpdateBy());
                zoneCharacterRels.add(rel);
            }
            c.setMark(s.getMark());
            c.setCharacterServiceType(SlOneMapConstants.SL_SERVICE_TYPE_SOCIAL_WORKER);
            c.setCreateBy(s.getCreateBy());
            c.setUpdateBy(s.getUpdateBy());
            characters.add(c);
        });
        this.refreshOneNetData(slSocialWorkers);
        baseMapper.insertBatch(slSocialWorkers);
        zoneCharacterRelMapper.insertBatch(zoneCharacterRels);
        structureCharacterMapper.insertBatch(characters);
    }

    @Override
    public void register() {
        ExcelToDatabaseSwitch.register(new ExcelToDatabaseSwitch("social-worker", SlSocialWorkerDto.class, this.getClass()));
    }
}
