package com.zhonghe.system.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zhonghe.system.domain.thirdparty.SlGridPatrolOrder;
import com.zhonghe.system.domain.thirdparty.TaskOrderNew;
import com.zhonghe.system.domain.thirdparty.vo.SlCaseEventStatisticsVo;
import com.zhonghe.system.domain.vo.BaseThirdPartyVo;
import com.zhonghe.system.mapper.SlGridPatrolOrderMapper;
import com.zhonghe.system.mapper.TaskOrderNewMapper;
import com.zhonghe.system.service.ISlPatrolOrderService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;

/**
 * @description:
 * @author: cq
 * @date: 2023/8/15 17:41
 */

@Service
@Slf4j
@DS("data")
@AllArgsConstructor
public class SlPatrolOrderServiceImpl implements ISlPatrolOrderService {


    private final SlGridPatrolOrderMapper patrolOrderMapper;

    private final TaskOrderNewMapper taskOrderNewMapper;

    @Override
    public void queryByAddress(BaseThirdPartyVo vo, String address) {
        List<TaskOrderNew> orderNews = taskOrderNewMapper.selectList(new LambdaQueryWrapper<TaskOrderNew>().eq(TaskOrderNew::getBuildAddress, address)
            .orderByDesc(TaskOrderNew::getCreateDate));

        List<SlGridPatrolOrder> slPatrolOrders = new ArrayList<>(orderNews.size());
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        for (TaskOrderNew orderNew : orderNews) {
            SlGridPatrolOrder order = new SlGridPatrolOrder();
            // 属性填充
            if (ObjectUtil.isNotNull(orderNew.getCheckEndDate())) {
                order.setXcqx(dateFormat.format(orderNew.getCheckEndDate()));
            }
            if (ObjectUtil.isNotNull(orderNew.getCreateDate())) {
                order.setCjsj(dateFormat.format(orderNew.getCreateDate()));
            }
            order.setBmmc(orderNew.getDeptName());
            order.setZtmc(orderNew.formatCheckState());
            order.setSxflmc(orderNew.getItemClassName());
            order.setZtlx(orderNew.formatCheckState());
            order.setXczt(orderNew.formatCheckState());
            order.setZj(orderNew.getZjid());
            slPatrolOrders.add(order);
        }
        vo.setPatrolOrders(slPatrolOrders);
    }

    @Override
    public List<SlCaseEventStatisticsVo> queryStatisticBYAddress(List<String> address) {
        return patrolOrderMapper.queryStatisticByAddress(address);

    }
}

