package com.zhonghe.system.service;

import cn.hutool.core.lang.tree.Tree;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.system.domain.bo.grid.SlGridStructureCharacterBo;
import com.zhonghe.system.domain.vo.SlOneMapServicePowerStatisticsVo;
import com.zhonghe.system.domain.vo.grid.SlGridStructureCharacterVo;

import java.util.Collection;
import java.util.List;

/**
 * 网格人员信息Service接口
 *
 * <AUTHOR>
 * @date 2023-08-02
 */
public interface ISlGridStructureCharacterService {

    /**
     * 查询网格人员信息
     */
    SlGridStructureCharacterVo queryById(Long gridCharacterId);


    /**
     * 查询网格人员信息列表
     */
    List<SlGridStructureCharacterVo> queryList(SlGridStructureCharacterBo bo);

    /**
     * 修改网格人员信息
     */
    Boolean insertByBo(SlGridStructureCharacterBo bo);

    /**
     * 修改网格人员信息
     */
    Boolean updateByBo(SlGridStructureCharacterBo bo);

    /**
     * 校验并批量删除网格人员信息信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);


    /**
     * 返回树结构
     * @return
     */
    List<Tree<Long>> grid();

    /**
     * 按职位返回数据
     * @param bo
     * @param pageQuery
     * @return
     */
    TableDataInfo<SlGridStructureCharacterVo> queryPageList(SlGridStructureCharacterBo bo, PageQuery pageQuery);

    /**
     * 查询所有的服务力量人员接口
     * @param bo
     * @param pageQuery
     * @return
     */
    TableDataInfo<SlGridStructureCharacterVo> queryAllServicePowerCharactersInfo(SlGridStructureCharacterBo bo, PageQuery pageQuery);

    /**
     * 按类型统计服务力量人员数量
     * @return
     */
    SlOneMapServicePowerStatisticsVo servicePowerStatistics();

    SlGridStructureCharacterVo queryGridLeader(String grid);

    SlGridStructureCharacterVo queryServicePowerByCharacterId(Long gridCharacterId);
}
