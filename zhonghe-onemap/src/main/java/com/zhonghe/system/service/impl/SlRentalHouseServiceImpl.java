package com.zhonghe.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.helper.IdGeneratorHelper;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.common.utils.spring.SpringUtils;
import com.zhonghe.system.constants.SlOneMapConstants;
import com.zhonghe.system.domain.SlRentalHouse;
import com.zhonghe.system.domain.SlRentalHouseOnenet;
import com.zhonghe.system.domain.bo.SlRentalHouseBo;
import com.zhonghe.system.domain.dto.OneNetResponseDto;
import com.zhonghe.system.domain.dto.SlRentalHouseDto;
import com.zhonghe.system.domain.thirdparty.vo.SlCaseEventStatisticsVo;
import com.zhonghe.system.domain.vo.RentalHouseDetailVo;
import com.zhonghe.system.domain.vo.SlRentalHouseStatisticsVo;
import com.zhonghe.system.domain.vo.SlRentalHouseVo;
import com.zhonghe.system.enmu.OneNetConfig;
import com.zhonghe.system.event.ExChangePatrolOrderEvent;
import com.zhonghe.system.event.ExChangeSpecialPopulationsEvent;
import com.zhonghe.system.event.ExchangeDataBaseEventCaseByRentalHouseEvent;
import com.zhonghe.system.event.ExchangeDataBaseEventGridPatrolOrderEvent;
import com.zhonghe.system.excel.ExcelToDatabaseSwitch;
import com.zhonghe.system.excel.service.ILoadExcelService;
import com.zhonghe.system.mapper.SlRentalHouseMapper;
import com.zhonghe.system.mapper.SlRentalHouseOnenetMapper;
import com.zhonghe.system.service.IOneNetHttpService;
import com.zhonghe.system.service.ISlRentalHouseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.postgis.jdbc.geometry.Geometry;
import net.postgis.jdbc.geometry.Point;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 出租屋信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-08-11
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class SlRentalHouseServiceImpl implements ISlRentalHouseService, ILoadExcelService {

    private final SlRentalHouseMapper baseMapper;

    private final SlRentalHouseOnenetMapper rentalHouseOnenetMapper;

    private final IOneNetHttpService oneNetHttpService;

    private final ApplicationEventPublisher eventPublisher;

    /**
     * 查询出租屋信息
     */
    @Override
    public SlRentalHouseVo queryById(Long rentalHouseId) {
        return baseMapper.selectVoById(rentalHouseId);
    }


    /**
     * 查询出租屋信息列表
     */
    @Override
    public List<SlRentalHouseVo> queryList(SlRentalHouseBo bo) {
        LambdaQueryWrapper<SlRentalHouse> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SlRentalHouse> buildQueryWrapper(SlRentalHouseBo bo) {
        //Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SlRentalHouse> lqw = Wrappers.lambdaQuery();
        /*lqw.eq(StringUtils.isNotBlank(bo.getGridZone()), SlRentalHouse::getGridZone, bo.getGridZone());
        lqw.eq(StringUtils.isNotBlank(bo.getBuildingAddress()), SlRentalHouse::getBuildingAddress, bo.getBuildingAddress());
        lqw.eq(StringUtils.isNotBlank(bo.getBuildingType()), SlRentalHouse::getBuildingType, bo.getBuildingType());
        lqw.eq(StringUtils.isNotBlank(bo.getUpdatedTime()), SlRentalHouse::getUpdatedTime, bo.getUpdatedTime());
        lqw.eq(StringUtils.isNotBlank(bo.getCreatedTime()), SlRentalHouse::getCreatedTime, bo.getCreatedTime());
        lqw.eq(StringUtils.isNotBlank(bo.getUpdater()), SlRentalHouse::getUpdater, bo.getUpdater());
        lqw.eq(StringUtils.isNotBlank(bo.getCreator()), SlRentalHouse::getCreator, bo.getCreator());
        lqw.eq(StringUtils.isNotBlank(bo.getRentalType()), SlRentalHouse::getRentalType, bo.getRentalType());
        lqw.eq(StringUtils.isNotBlank(bo.getRentalLv()), SlRentalHouse::getRentalLv, bo.getRentalLv());
        lqw.like(StringUtils.isNotBlank(bo.getSignName()), SlRentalHouse::getSignName, bo.getSignName());
        lqw.eq(StringUtils.isNotBlank(bo.getBelongToBuilding()), SlRentalHouse::getBelongToBuilding, bo.getBelongToBuilding());
        lqw.eq(StringUtils.isNotBlank(bo.getMainSubjectAddress()), SlRentalHouse::getMainSubjectAddress, bo.getMainSubjectAddress());
        lqw.eq(StringUtils.isNotBlank(bo.getHasFillCode()), SlRentalHouse::getHasFillCode, bo.getHasFillCode());
        lqw.eq(StringUtils.isNotBlank(bo.getRentHouseFillCode()), SlRentalHouse::getRentHouseFillCode, bo.getRentHouseFillCode());
        lqw.eq(StringUtils.isNotBlank(bo.getHasCheckedAddress()), SlRentalHouse::getHasCheckedAddress, bo.getHasCheckedAddress());
        lqw.eq(StringUtils.isNotBlank(bo.getFillAddress()), SlRentalHouse::getFillAddress, bo.getFillAddress());
        lqw.eq(StringUtils.isNotBlank(bo.getHasSignSafeContract()), SlRentalHouse::getHasSignSafeContract, bo.getHasSignSafeContract());
        lqw.eq(bo.getAvailableRentRoomCount() != null, SlRentalHouse::getAvailableRentRoomCount, bo.getAvailableRentRoomCount());
        lqw.eq(bo.getRentedRoomCount() != null, SlRentalHouse::getRentedRoomCount, bo.getRentedRoomCount());
        lqw.eq(bo.getRenterCount() != null, SlRentalHouse::getRenterCount, bo.getRenterCount());
        lqw.eq(bo.getRentalBuildingFloorCount() != null, SlRentalHouse::getRentalBuildingFloorCount, bo.getRentalBuildingFloorCount());
        lqw.eq(bo.getRentedFloorIdx() != null, SlRentalHouse::getRentedFloorIdx, bo.getRentedFloorIdx());
        lqw.eq(StringUtils.isNotBlank(bo.getBuildingStruct()), SlRentalHouse::getBuildingStruct, bo.getBuildingStruct());
        lqw.like(StringUtils.isNotBlank(bo.getBuildingOwnerName()), SlRentalHouse::getBuildingOwnerName, bo.getBuildingOwnerName());
        lqw.eq(StringUtils.isNotBlank(bo.getBuildingOwnerTel()), SlRentalHouse::getBuildingOwnerTel, bo.getBuildingOwnerTel());
        lqw.eq(StringUtils.isNotBlank(bo.getHasManager()), SlRentalHouse::getHasManager, bo.getHasManager());
        lqw.eq(StringUtils.isNotBlank(bo.getSecondHandManager()), SlRentalHouse::getSecondHandManager, bo.getSecondHandManager());
        lqw.eq(StringUtils.isNotBlank(bo.getSecondHandTel()), SlRentalHouse::getSecondHandTel, bo.getSecondHandTel());
        lqw.like(StringUtils.isNotBlank(bo.getManagerName()), SlRentalHouse::getManagerName, bo.getManagerName());
        lqw.eq(StringUtils.isNotBlank(bo.getManagerTel()), SlRentalHouse::getManagerTel, bo.getManagerTel());
        lqw.eq(StringUtils.isNotBlank(bo.getMark()), SlRentalHouse::getMark, bo.getMark());
        lqw.eq(StringUtils.isNotBlank(bo.getWorkingTags()), SlRentalHouse::getWorkingTags, bo.getWorkingTags());
        lqw.eq(StringUtils.isNotBlank(bo.getSupplyTags()), SlRentalHouse::getSupplyTags, bo.getSupplyTags());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), SlRentalHouse::getStatus, bo.getStatus());*/
        return lqw;
    }

    /**
     * 新增出租屋信息
     */
    @Override
    public Boolean insertByBo(SlRentalHouseBo bo) {
        /*SlRentalHouse add = BeanUtil.toBean(bo, SlRentalHouse.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setRentalHouseId(add.getRentalHouseId());
        }*/
        return false;
    }

    /**
     * 修改出租屋信息
     */
    @Override
    public Boolean updateByBo(SlRentalHouseBo bo) {
        SlRentalHouse update = BeanUtil.toBean(bo, SlRentalHouse.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SlRentalHouse entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除出租屋信息
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public SlRentalHouseStatisticsVo rentalHouseStatistics() {
        SlRentalHouseStatisticsVo vo = new SlRentalHouseStatisticsVo();
        List<SlRentalHouse> houses = baseMapper.selectList();

        vo.setCount((long) houses.size());

        long roomCount = houses.stream().filter(a -> ObjectUtil.isNotNull(a.getAvailableRentRoomCount())).mapToLong(SlRentalHouse::getAvailableRentRoomCount).summaryStatistics().getSum();
        vo.setRoomCount(roomCount);

        Set<String> owners = houses.stream().filter(a -> ObjectUtil
                .isNotNull(a.getBuildingOwnerName()))
            .map(SlRentalHouse::getBuildingOwnerName).collect(Collectors.toSet());

        vo.setOwnerCount((long) owners.size());


        long rentedRoomCount = houses.stream().filter(a -> ObjectUtil.isNotNull(a.getRentedRoomCount())).mapToLong(SlRentalHouse::getRentedRoomCount).summaryStatistics().getSum();
        vo.setRentedRoomCount(rentedRoomCount);

        long slaversCount = houses.stream().filter(a -> ObjectUtil.isNotNull(a.getRenterCount())).mapToLong(SlRentalHouse::getRenterCount).summaryStatistics().getSum();
        vo.setSlaverCount(slaversCount);

        vo.setRentalPercent((rentedRoomCount * 100 / roomCount) + "%");

        return vo;
    }

    @Override
    public List<SlCaseEventStatisticsVo> rentalHouseEventStatistics() {
        List<SlCaseEventStatisticsVo> result = new ArrayList<>();
        List<SlRentalHouse> houses = baseMapper.selectList(new LambdaQueryWrapper<SlRentalHouse>()
            .isNotNull(SlRentalHouse::getBuildingAddress)
            .select(SlRentalHouse::getBuildingAddress));

        Set<String> address = houses.stream().map(SlRentalHouse::getBuildingAddress).collect(Collectors.toSet());

        if (CollectionUtil.isNotEmpty(address)) {
            //SpringUtils.publishEvent(new ExchangeDataBaseEventCaseByAddressEvent(this, address, result));
            eventPublisher.publishEvent(new ExchangeDataBaseEventGridPatrolOrderEvent(this, address, result));
        }
        if (CollectionUtil.isNotEmpty(result) && result.size() > SlOneMapConstants.TOP_THREE) {
            List<SlCaseEventStatisticsVo> remark = new ArrayList<>();
            for (int i = 0; i < SlOneMapConstants.TOP_THREE; i++) {
                remark.add(result.get(i));
            }
            long total = 0;
            for (int i = 3; i < result.size(); i++) {
                total += result.get(i).getCount();
            }
            remark.add(new SlCaseEventStatisticsVo(SlOneMapConstants.OTHER, total));
            return remark;
        }


        return result;
    }

    @Override
    public TableDataInfo<SlRentalHouseVo> pageSlRentalHouse(PageQuery pageQuery, SlRentalHouseBo bo) {
        String keyword = bo.getKeyWord();
        Page<SlRentalHouseVo> housePage = baseMapper.selectVoPage(pageQuery.build(), new LambdaQueryWrapper<SlRentalHouse>()
            .or(StringUtils.isNotEmpty(keyword), qw -> {
                qw
                    .like(
                        StringUtils.isNotEmpty(keyword), SlRentalHouse::getBuildingAddress, keyword)
                    .or()
                    .like(StringUtils.isNotEmpty(keyword), SlRentalHouse::getBuildingOwnerName, keyword)
                    .or()
                    /*.like(StringUtils.isNotEmpty(keyword), SlRentalHouse::getCreator, keyword)
                    .or()*/
                    /*.like(StringUtils.isNotEmpty(keyword), SlRentalHouse::getBuildingStruct, keyword)
                    .or()*/
                    .like(StringUtils.isNotEmpty(keyword), SlRentalHouse::getBuildingOwnerTel, keyword)
                    /* .or()
                     .like(StringUtils.isNotEmpty(keyword), SlRentalHouse::getManagerName, keyword)
                     .or()
                     .like(StringUtils.isNotEmpty(keyword), SlRentalHouse::getManagerTel, keyword)*/
                    /*                   .or()
                                       .like(StringUtils.isNotEmpty(keyword), SlRentalHouse::getMainSubjectAddress, keyword)
                                       .or()
                                       .like(StringUtils.isNotEmpty(keyword), SlRentalHouse::getFillAddress, keyword)
                                       .or()
                                       .like(StringUtils.isNotEmpty(keyword), SlRentalHouse::getWorkingTags, keyword)*/
                    .or()
                    .like(StringUtils.isNotEmpty(keyword), SlRentalHouse::getGridZone, keyword)
                    /*.or()
                    .like(StringUtils.isNotEmpty(keyword), SlRentalHouse::getSupplyTags, keyword)*/
                ;
            })

        );

        housePage.getRecords().forEach(r -> {
            if (StringUtils.isNotEmpty(r.getBuildingAddress())) {
                // 案事件
                SpringUtils.publishEvent(new ExchangeDataBaseEventCaseByRentalHouseEvent(this, r, r.getBuildingAddress()));
                // 巡检记录
                SpringUtils.publishEvent(new ExChangePatrolOrderEvent(this, r, r.getBuildingAddress()));
                // 特殊人群
                SpringUtils.publishEvent(new ExChangeSpecialPopulationsEvent(this, r, r.getBuildingAddress()));
            }
        });
        return TableDataInfo.build(housePage);
    }

    @Override
    public RentalHouseDetailVo detailRentalHouse(Long rentalHouseId) {
        RentalHouseDetailVo vo = new RentalHouseDetailVo();

        SlRentalHouse slRentalHouse = baseMapper.selectById(rentalHouseId);
        if (null != slRentalHouse) {
            vo.setAddress(slRentalHouse.getBuildingAddress());
            vo.setRoomNo(slRentalHouse.getBelongToBuilding());
            if (StringUtils.isNotEmpty(slRentalHouse.getSecondHandManager())) {
                vo.setMaster(slRentalHouse.getSecondHandManager());
            } else {
                vo.setMaster(slRentalHouse.getBuildingOwnerName());
            }
            vo.setSlaverCount(slRentalHouse.getRenterCount());
            // 案事件
            eventPublisher.publishEvent(new ExchangeDataBaseEventCaseByRentalHouseEvent(this, vo, vo.getAddress()));
            // 巡检记录
            eventPublisher.publishEvent(new ExChangePatrolOrderEvent(this, vo, vo.getAddress()));
            // 特殊人群
            eventPublisher.publishEvent(new ExChangeSpecialPopulationsEvent(this, vo, vo.getAddress()));
        }

        return vo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean refresh() {
        List<SlRentalHouse> houses = baseMapper.selectList();
        rentalHouseOnenetMapper.delete(new LambdaQueryWrapper<>());
        this.refresh(houses);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void load(List<?> result) {
        List<SlRentalHouse> houses = BeanUtil.copyToList(result, SlRentalHouse.class);
        houses.forEach(h -> h.setRentalHouseId(IdGeneratorHelper.next()));
        this.refresh(houses);

        baseMapper.insertBatch(houses);
    }

    private void refresh(List<SlRentalHouse> houses) {
        List<SlRentalHouseOnenet> geometries = new ArrayList<>(houses.size());
        houses.forEach(h -> {
            SlRentalHouseOnenet one = new SlRentalHouseOnenet();
            BeanUtil.copyProperties(h, one);
            one.setRentalHouseOnenetId(IdGeneratorHelper.next());
            one.setRentalHouseId(h.getRentalHouseId());
            one.setCreateBy(h.getCreateBy());
            one.setUpdateBy(h.getUpdateBy());
            if (!StringUtils.isEmpty(h.getBuildingAddress())) {
                OneNetResponseDto request = oneNetHttpService.request(OneNetConfig.ADDRESS, new String[]{h.getBuildingAddress()}, null);
                one.setOnenetJson(JSONUtil.toJsonStr(request));
                if (!ObjectUtil.isNull(request)) {
                    List<Map<String, String>> data = request.getData().getData();
                    one.setOnenetJson(JSONUtil.toJsonStr(data));
                    Map<String, String> select = OneNetConfig.ADDRESS.getSelector().select(data, "GXSJ");
                    if (CollectionUtil.isNotEmpty(select)) {
                        String zwxtbh = select.getOrDefault("ZWXTBH", null);
                        one.setSmartOnenetCode(zwxtbh);

                        String zxjd = select.get("ZXJD");
                        String zxwd = select.get("ZXWD");
                        if (StringUtils.isNotEmpty(zxjd) && StringUtils.isNotEmpty(zxwd)) {
                            Geometry geometry = new Point(Double.parseDouble(zxjd), Double.parseDouble(zxwd));
                            one.setRentalHouseGem(geometry);
                        }
                    }

                } else {
                    log.warn("请求不到一网共享数据:{}", h.getMainSubjectAddress());
                }
            }
            geometries.add(one);
        });

        rentalHouseOnenetMapper.insertBatch(geometries);
    }

    @Override
    public void register() {
        ExcelToDatabaseSwitch.register(new ExcelToDatabaseSwitch("rental", SlRentalHouseDto.class, this.getClass()));
    }
}
