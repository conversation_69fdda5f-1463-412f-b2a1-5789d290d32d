package com.zhonghe.system.service;

import com.zhonghe.system.domain.thirdparty.vo.SlCaseEventStatisticsVo;
import com.zhonghe.system.domain.vo.BaseThirdPartyVo;

import java.util.List;

/**
 * @description:
 * @author: cq
 * @date: 2023/8/15 17:41
 */

public interface ISlPatrolOrderService {
    void queryByAddress(BaseThirdPartyVo vo, String address);

    List<SlCaseEventStatisticsVo> queryStatisticBYAddress(List<String> address);

}
