package com.zhonghe.system.service;

import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.system.domain.SlMarketOnenet;
import com.zhonghe.system.domain.thirdparty.bo.DgsscztxxBo;
import com.zhonghe.system.domain.thirdparty.vo.SlCaseEventStatisticsVo;
import com.zhonghe.system.domain.thirdparty.vo.ZsjEntregInfoVo;
import com.zhonghe.system.domain.vo.MarketSubjectDetailVo;
import com.zhonghe.system.domain.vo.SlMarketSubjectStatisticsVo;

import java.util.List;

/**
 * @description:
 * @author: cq
 * @date: 2023/8/14 15:26
 */

public interface ISlMarketSubjectService {
    SlMarketSubjectStatisticsVo marketStatistics();

    List<SlCaseEventStatisticsVo> marketEventStatistic();

    TableDataInfo<ZsjEntregInfoVo> page(PageQuery pageQuery, DgsscztxxBo bo);

    MarketSubjectDetailVo detail(String zj);

    List<SlMarketOnenet> updateMarketGis(List<Long> markerZjs);
}
