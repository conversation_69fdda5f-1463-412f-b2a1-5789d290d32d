package com.zhonghe.system.service;

import com.zhonghe.system.domain.bo.SlSocialWorkerBo;
import com.zhonghe.system.domain.vo.SlSocialWorkerVo;

import java.util.Collection;
import java.util.List;

/**
 * 石龙社工Service接口
 *
 * <AUTHOR>
 * @date 2023-08-03
 */
public interface ISlSocialWorkerService {

    /**
     * 查询石龙社工
     */
    SlSocialWorkerVo queryById(Long socialWorkerId);


    /**
     * 查询石龙社工列表
     */
    List<SlSocialWorkerVo> queryList(SlSocialWorkerBo bo);

    /**
     * 修改石龙社工
     */
    Boolean insertByBo(SlSocialWorkerBo bo);

    /**
     * 修改石龙社工
     */
    Boolean updateByBo(SlSocialWorkerBo bo);

    /**
     * 校验并批量删除石龙社工信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 批量查询二标地址与坐标,上图
     */
    void requestOnenetData();
}
