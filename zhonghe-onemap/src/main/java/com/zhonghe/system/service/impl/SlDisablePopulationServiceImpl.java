package com.zhonghe.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.zhonghe.common.helper.IdGeneratorHelper;
import com.zhonghe.system.domain.SlDisablePopulation;
import com.zhonghe.system.domain.dto.SlDisablePopulationDto;
import com.zhonghe.system.excel.ExcelToDatabaseSwitch;
import com.zhonghe.system.mapper.SlDisablePopulationMapper;
import com.zhonghe.system.service.ISlDisablePopulationService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.ParseException;
import java.util.List;

/**
 * 残疾人Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-09-01
 */
@RequiredArgsConstructor
@Service
public class SlDisablePopulationServiceImpl implements ISlDisablePopulationService {

    private final SlDisablePopulationMapper baseMapper;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void load(List<?> result) throws ParseException {
        List<SlDisablePopulation> slDisablePopulations = BeanUtil.copyToList(result, SlDisablePopulation.class);
        slDisablePopulations.forEach(dis->{
            dis.setDisableId(IdGeneratorHelper.next());
            dis.setPopulationType("残疾人");
        });
        baseMapper.insertBatch(slDisablePopulations);
    }

    @Override
    public void register() {
        ExcelToDatabaseSwitch.register("disable", SlDisablePopulationDto.class, this.getClass());
    }
}
