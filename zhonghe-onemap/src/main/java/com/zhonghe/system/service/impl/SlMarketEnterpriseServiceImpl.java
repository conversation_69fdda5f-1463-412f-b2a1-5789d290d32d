package com.zhonghe.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zhonghe.common.helper.IdGeneratorHelper;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.system.domain.SlMarketEnterprise;
import com.zhonghe.system.domain.SlMarketEnterpriseOnenet;
import com.zhonghe.system.domain.bo.SlMarketEnterpriseBo;
import com.zhonghe.system.domain.dto.OneNetResponseDto;
import com.zhonghe.system.domain.dto.SlMarketEnterpriseDto;
import com.zhonghe.system.domain.thirdparty.Dgsscztxx;
import com.zhonghe.system.domain.vo.SlMarketEnterpriseVo;
import com.zhonghe.system.enmu.OneNetConfig;
import com.zhonghe.system.excel.ExcelToDatabaseSwitch;
import com.zhonghe.system.excel.service.ILoadExcelService;
import com.zhonghe.system.mapper.DgsscztxxMapper;
import com.zhonghe.system.mapper.SlMarketEnterpriseMapper;
import com.zhonghe.system.mapper.SlMarketEnterpriseOnenetMapper;
import com.zhonghe.system.service.IOneNetHttpService;
import com.zhonghe.system.service.ISlMarketEnterpriseService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.postgis.jdbc.geometry.Geometry;
import net.postgis.jdbc.geometry.Point;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 【请填写功能名称】Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-08-14
 */
@RequiredArgsConstructor
@Service
@Slf4j
@DS("master")
public class SlMarketEnterpriseServiceImpl implements ISlMarketEnterpriseService, ILoadExcelService {

    private final SlMarketEnterpriseMapper baseMapper;

    private final DgsscztxxMapper dgsscztxxMapper;

    private final IOneNetHttpService oneNetHttpService;

    private final SlMarketEnterpriseOnenetMapper enterpriseOnenetMapper;

    /**
     * 查询【请填写功能名称】
     */
    @Override
    public SlMarketEnterpriseVo queryById(Long marketEnterpriseId) {
        return baseMapper.selectVoById(marketEnterpriseId);
    }


    /**
     * 查询【请填写功能名称】列表
     */
    @Override
    public List<SlMarketEnterpriseVo> queryList(SlMarketEnterpriseBo bo) {
        LambdaQueryWrapper<SlMarketEnterprise> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SlMarketEnterprise> buildQueryWrapper(SlMarketEnterpriseBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SlMarketEnterprise> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getEnterpriseCreditCode()), SlMarketEnterprise::getEnterpriseCreditCode, bo.getEnterpriseCreditCode());
        lqw.like(StringUtils.isNotBlank(bo.getEnterpriseName()), SlMarketEnterprise::getEnterpriseName, bo.getEnterpriseName());
        lqw.eq(StringUtils.isNotBlank(bo.getEnterpriseAddress()), SlMarketEnterprise::getEnterpriseAddress, bo.getEnterpriseAddress());
        lqw.eq(StringUtils.isNotBlank(bo.getEnterpriseType()), SlMarketEnterprise::getEnterpriseType, bo.getEnterpriseType());
        lqw.eq(StringUtils.isNotBlank(bo.getCreateDate()), SlMarketEnterprise::getCreateDate, bo.getCreateDate());
        lqw.eq(StringUtils.isNotBlank(bo.getOperatingEndDate()), SlMarketEnterprise::getOperatingEndDate, bo.getOperatingEndDate());
        lqw.eq(StringUtils.isNotBlank(bo.getOperatingScope()), SlMarketEnterprise::getOperatingScope, bo.getOperatingScope());
        lqw.eq(StringUtils.isNotBlank(bo.getLegalPerson()), SlMarketEnterprise::getLegalPerson, bo.getLegalPerson());
        lqw.eq(StringUtils.isNotBlank(bo.getIdType()), SlMarketEnterprise::getIdType, bo.getIdType());
        lqw.eq(StringUtils.isNotBlank(bo.getIdCode()), SlMarketEnterprise::getIdCode, bo.getIdCode());
        lqw.eq(StringUtils.isNotBlank(bo.getCurrencyRegister()), SlMarketEnterprise::getCurrencyRegister, bo.getCurrencyRegister());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), SlMarketEnterprise::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增【请填写功能名称】
     */
    @Override
    public Boolean insertByBo(SlMarketEnterpriseBo bo) {
        SlMarketEnterprise add = BeanUtil.toBean(bo, SlMarketEnterprise.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setMarketEnterpriseId(add.getMarketEnterpriseId());
        }
        return flag;
    }

    /**
     * 修改【请填写功能名称】
     */
    @Override
    public Boolean updateByBo(SlMarketEnterpriseBo bo) {
        SlMarketEnterprise update = BeanUtil.toBean(bo, SlMarketEnterprise.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SlMarketEnterprise entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除【请填写功能名称】
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void refresh() {
        List<SlMarketEnterprise> slMarketEnterprises = baseMapper.selectList();
        enterpriseOnenetMapper.delete(new LambdaQueryWrapper<>());
        this.refreshByExcel(slMarketEnterprises);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void insertBatch(List<SlMarketEnterpriseOnenet> enterpriseOnenets) {
        if (CollectionUtil.isNotEmpty(enterpriseOnenets)) {
            Set<String> collect = enterpriseOnenets.stream().map(SlMarketEnterpriseOnenet::getMarketEnterpriseId).collect(Collectors.toSet());
            enterpriseOnenetMapper.deleteBatchIds(collect);
            enterpriseOnenetMapper.insertBatch(enterpriseOnenets);
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void load(List<?> result) {
        List<SlMarketEnterprise> slMarketEnterprises = BeanUtil.copyToList(result, SlMarketEnterprise.class);
        slMarketEnterprises.forEach(m -> m.setMarketEnterpriseId(IdGeneratorHelper.next()));
        this.refreshByExcel(slMarketEnterprises);
        //this.registerDataToThirdParty(slMarketEnterprises);
        baseMapper.insertBatch(slMarketEnterprises);
    }


    private void registerDataToThirdParty(List<SlMarketEnterprise> slMarketEnterprises) {
        List<Dgsscztxx> dgsscztxxes = new ArrayList<>(slMarketEnterprises.size());
        List<Long> deletes = new ArrayList<>();
        slMarketEnterprises.forEach(m -> {
            OneNetResponseDto request = oneNetHttpService.request(OneNetConfig.MACKET_UNIT, new String[]{m.getEnterpriseName(), m.getIdCode()}, null);
            if (null != request) {
                List<Map<String, String>> data = request.getData().getData();

                Map<String, String> select = OneNetConfig.UNIT.getSelector().select(data, "GXSJ");

                if (CollectionUtil.isNotEmpty(select)) {
                    Dgsscztxx dgsscztxx = BeanUtil.toBean(select, Dgsscztxx.class);
                    dgsscztxxes.add(dgsscztxx);
                    deletes.add(dgsscztxx.getZj());
                } else {
                    log.error("信用代码没有查询到结果：{}:{}", m.getEnterpriseName(), m.getIdCode());
                }
            } else {
                log.error("信用代码没有查询到结果：{}", m.getIdCode());
            }
        });
        this.refresh(dgsscztxxes);
        if (CollectionUtil.isNotEmpty(deletes)) {
            dgsscztxxMapper.deleteBatchIds(deletes);
        }
        if (CollectionUtil.isNotEmpty(dgsscztxxes)) {
            dgsscztxxMapper.insertBatch(dgsscztxxes);
        }
    }

    private void refreshByExcel(List<SlMarketEnterprise> slMarketEnterprises) {
        List<SlMarketEnterpriseOnenet> onenets = new ArrayList<>(slMarketEnterprises.size());

        slMarketEnterprises.forEach(e -> {
            if (StringUtils.isNotEmpty(e.getEnterpriseCreditCode())) {
                SlMarketEnterpriseOnenet onenet = new SlMarketEnterpriseOnenet();
                BeanUtil.copyProperties(e, onenet);
                onenet.setMarketEnterpriseOnenetId(IdGeneratorHelper.next());
                onenet.setMarketEnterpriseId(String.valueOf(e.getMarketEnterpriseId()));
                onenet.setEnterpriseName(e.getEnterpriseName());
                onenet.setEnterpriseCreditCode(e.getEnterpriseCreditCode());

                OneNetResponseDto request = oneNetHttpService.request(OneNetConfig.ADDRESS, new String[]{
                    e.getEnterpriseAddress().contains("（集群注册）") ? e.getEnterpriseAddress().replaceAll("（集群注册）", "") : e.getEnterpriseAddress()
                }, null);
                if (null != request) {
                    List<Map<String, String>> data = request.getData().getData();
                    onenet.setOnenetJson(JSONUtil.toJsonStr(data));
                    onenet.setCreateBy(e.getCreateBy());
                    onenet.setUpdateBy(e.getUpdateBy());
                    Map<String, String> select = OneNetConfig.ADDRESS.getSelector().select(data, "GXSJ");
                    if (CollectionUtil.isNotEmpty(select)) {
                        String dzxz = select.get("DZXZ");
                        onenet.setEnterpriseAddress(dzxz);

                        String zxjd = select.get("ZXJD");
                        String zxwd = select.get("ZXWD");
                        if (StringUtils.isNotEmpty(zxjd) && StringUtils.isNotEmpty(zxwd)) {
                            Geometry geometry = new Point(Double.parseDouble(zxjd), Double.parseDouble(zxwd));
                            onenet.setMarketEnterpriseGem(geometry);
                        }
                    }
                }
                onenets.add(onenet);
            }
        });

        enterpriseOnenetMapper.insertBatch(onenets);
    }

    private void refresh(List<Dgsscztxx> dgsscztxxes) {
        List<SlMarketEnterpriseOnenet> onenets = new ArrayList<>(dgsscztxxes.size());

        dgsscztxxes.forEach(e -> {
            if (StringUtils.isNotEmpty(e.getTyshxydm())) {
                SlMarketEnterpriseOnenet onenet = new SlMarketEnterpriseOnenet();
                onenet.setMarketEnterpriseOnenetId(IdGeneratorHelper.next());
                onenet.setMarketEnterpriseId(String.valueOf(e.getZj()));
                onenet.setEnterpriseName(e.getScztmc());
                onenet.setEnterpriseCreditCode(e.getTyshxydm());

                OneNetResponseDto request = oneNetHttpService.request(OneNetConfig.UNIT, new String[]{e.getTyshxydm()}, null);
                if (null != request) {
                    List<Map<String, String>> data = request.getData().getData();
                    onenet.setOnenetJson(JSONUtil.toJsonStr(data));
                    Map<String, String> select = OneNetConfig.UNIT.getSelector().select(data, "GXSJ");
                    if (CollectionUtil.isNotEmpty(select)) {
                        String dwdz = select.get("DWDZ");
                        onenet.setEnterpriseAddress(dwdz);

                        String dwzxjd = select.get("DWZXJD");
                        String dwzxwd = select.get("DWZXWD");
                        if (StringUtils.isNotEmpty(dwzxjd) && StringUtils.isNotEmpty(dwzxwd)) {
                            Geometry geometry = new Point(Double.parseDouble(dwzxjd), Double.parseDouble(dwzxwd));
                            onenet.setMarketEnterpriseGem(geometry);
                        }
                    }
                }
                onenets.add(onenet);
            }
        });

        enterpriseOnenetMapper.insertBatch(onenets);
    }

    @Override
    public void register() {
        ExcelToDatabaseSwitch.register(new ExcelToDatabaseSwitch("enterprise", SlMarketEnterpriseDto.class, this.getClass()));
    }
}
