package com.zhonghe.system.service.impl;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.zhonghe.common.exception.base.BaseException;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.common.utils.redis.RedisUtils;
import com.zhonghe.system.constants.SlOneMapConstants;
import com.zhonghe.system.mapper.SlGridZoneMapper;
import com.zhonghe.system.service.IMapService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @description:
 * @author: cq
 * @date: 2023/8/21 11:13
 */

@Service
@Slf4j
public class MapServiceImpl implements IMapService {

    @Autowired
    private SlGridZoneMapper slGridZoneMapper;

    @Value("${map.client_id}")
    private String clientId;

    @Value("${map.client_secret}")
    private String secret;

    @Value("${map.url}")
    private String url;

    private String gridWfsUrl = "http://ktd.dg:18080/wgtcfw-zw/wfs?service=WFS&version=1.0.0&request=GetFeature&token=%s&outputFormat=json&typeName=ktd_zfw:zwwg_2021&filter=<Filter>\n" +
        "    <Intersects>\n" +
        "      <PropertyName>geom</PropertyName>\n" +
        "      <Point>\n" +
        "       <coordinates>%s,%s</coordinates>\n" +
        "      </Point>\n" +
        "    </Intersects>\n" +
        "</Filter>";

    @Override
    public String token() {

        Object cacheObject = RedisUtils.getCacheObject(SlOneMapConstants.MAP_REDIS_TOKEN);
        if (ObjectUtils.isEmpty(cacheObject)) {
            Map<String, Object> map = new HashMap<>();
            map.put("client_id", clientId);
            map.put("client_secret", secret);
            String s = HttpUtil.get(url, map);
            if (StringUtils.isNotEmpty(s)) {
                JSONObject jsonObject = JSONUtil.parseObj(s);
                JSONObject data = jsonObject.getJSONObject("data");
                if (data.containsKey("access_token")) {
                    String token = data.getStr("access_token");
                    RedisUtils.setCacheObject(SlOneMapConstants.MAP_REDIS_TOKEN, token, Duration.ofHours(2));
                    return token;
                }
            }
            throw new BaseException("查询失败");
        }
        return cacheObject.toString();
    }

    @Override
    public String grid(double lng, double lat) {
        String token = this.token();
        String s = HttpUtil.get(String.format(gridWfsUrl, token, lng, lat));

        if (StringUtils.isNotEmpty(s)) {
            JSONObject json = JSONUtil.parseObj(s);
            Integer totalFeatures = json.getInt("totalFeatures");
            if (totalFeatures > 0) {
                JSONArray features = json.getJSONArray("features");
                JSONObject jsonObject = features.getJSONObject(0);
                if (jsonObject.containsKey("properties")) {
                    JSONObject properties = jsonObject.getJSONObject("properties");
                    return properties.getStr("gridname", null);
                }
            }

        }
        return null;
    }

    @Override
    @DS("master")
    public List<String> gridDB(double lng, double lat) {
        return slGridZoneMapper.grid(lng, lat);
    }
}

