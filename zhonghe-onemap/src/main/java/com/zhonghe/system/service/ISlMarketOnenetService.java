package com.zhonghe.system.service;

import com.zhonghe.system.domain.SlMarketOnenet;
import com.zhonghe.system.domain.bo.SlMarketOnenetBo;
import com.zhonghe.system.domain.vo.SlMarketOnenetVo;

import java.util.Collection;
import java.util.List;

/**
 * 前置机市场地图Service接口
 *
 * <AUTHOR>
 * @date 2023-08-21
 */
public interface ISlMarketOnenetService {

    /**
     * 查询前置机市场地图
     */
    SlMarketOnenetVo queryById(Long marketId);


    /**
     * 查询前置机市场地图列表
     */
    List<SlMarketOnenetVo> queryList(SlMarketOnenetBo bo);

    /**
     * 修改前置机市场地图
     */
    Boolean insertByBo(SlMarketOnenetBo bo);

    /**
     * 修改前置机市场地图
     */
    Boolean updateByBo(SlMarketOnenetBo bo);

    /**
     * 校验并批量删除前置机市场地图信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    List<String> selectAllIds();

    void insertBatch(List<SlMarketOnenet> slMarketOnenets);
}
