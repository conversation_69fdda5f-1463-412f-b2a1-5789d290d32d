package com.zhonghe.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zhonghe.common.helper.IdGeneratorHelper;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.system.constants.SlOneMapConstants;
import com.zhonghe.system.domain.SlGridStructureCharacter;
import com.zhonghe.system.domain.SlGridZone;
import com.zhonghe.system.domain.SlGridZoneCharacterRel;
import com.zhonghe.system.domain.SlVolunteer;
import com.zhonghe.system.domain.bo.SlVolunteerBo;
import com.zhonghe.system.domain.dto.SlVolunteerDto;
import com.zhonghe.system.domain.vo.SlVolunteerVo;
import com.zhonghe.system.excel.ExcelToDatabaseSwitch;
import com.zhonghe.system.excel.service.ILoadExcelService;
import com.zhonghe.system.mapper.SlGridStructureCharacterMapper;
import com.zhonghe.system.mapper.SlGridZoneCharacterRelMapper;
import com.zhonghe.system.mapper.SlGridZoneMapper;
import com.zhonghe.system.mapper.SlVolunteerMapper;
import com.zhonghe.system.service.ISlVolunteerService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 志愿者Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-08-03
 */
@RequiredArgsConstructor
@Service
public class SlVolunteerServiceImpl implements ISlVolunteerService, ILoadExcelService {

    private final SlVolunteerMapper baseMapper;

    private final SlGridStructureCharacterMapper structureCharacterMapper;

    private final SlGridZoneMapper slGridZoneMapper;

    private final SlGridZoneCharacterRelMapper zoneCharacterRelMapper;

    /**
     * 查询志愿者
     */
    @Override
    public SlVolunteerVo queryById(Long volunteerId) {
        return baseMapper.selectVoById(volunteerId);
    }


    /**
     * 查询志愿者列表
     */
    @Override
    public List<SlVolunteerVo> queryList(SlVolunteerBo bo) {
        LambdaQueryWrapper<SlVolunteer> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SlVolunteer> buildQueryWrapper(SlVolunteerBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SlVolunteer> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getVolunteerName()), SlVolunteer::getVolunteerName, bo.getVolunteerName());
        lqw.eq(StringUtils.isNotBlank(bo.getSex()), SlVolunteer::getSex, bo.getSex());
        lqw.eq(StringUtils.isNotBlank(bo.getVolunteerLoginCode()), SlVolunteer::getVolunteerLoginCode, bo.getVolunteerLoginCode());
        lqw.eq(StringUtils.isNotBlank(bo.getVolunteerIdNumber()), SlVolunteer::getVolunteerIdNumber, bo.getVolunteerIdNumber());
        lqw.eq(StringUtils.isNotBlank(bo.getVolunteerPhone()), SlVolunteer::getVolunteerPhone, bo.getVolunteerPhone());
        lqw.eq(StringUtils.isNotBlank(bo.getVolunteerRegisterDate()), SlVolunteer::getVolunteerRegisterDate, bo.getVolunteerRegisterDate());
        lqw.eq(StringUtils.isNotBlank(bo.getVolunteerVerify()), SlVolunteer::getVolunteerVerify, bo.getVolunteerVerify());
        lqw.eq(StringUtils.isNotBlank(bo.getVolunteerFaceVerify()), SlVolunteer::getVolunteerFaceVerify, bo.getVolunteerFaceVerify());
        lqw.eq(StringUtils.isNotBlank(bo.getVolunteerPass()), SlVolunteer::getVolunteerPass, bo.getVolunteerPass());
        lqw.eq(StringUtils.isNotBlank(bo.getVolunteerPolitical()), SlVolunteer::getVolunteerPolitical, bo.getVolunteerPolitical());
        lqw.eq(StringUtils.isNotBlank(bo.getVolunteerServerTime()), SlVolunteer::getVolunteerServerTime, bo.getVolunteerServerTime());
        lqw.eq(StringUtils.isNotBlank(bo.getVolunteerTrainTime()), SlVolunteer::getVolunteerTrainTime, bo.getVolunteerTrainTime());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), SlVolunteer::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增志愿者
     */
    @Override
    public Boolean insertByBo(SlVolunteerBo bo) {
        SlVolunteer add = BeanUtil.toBean(bo, SlVolunteer.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setVolunteerId(add.getVolunteerId());
        }
        return flag;
    }

    /**
     * 修改志愿者
     */
    @Override
    public Boolean updateByBo(SlVolunteerBo bo) {
        SlVolunteer update = BeanUtil.toBean(bo, SlVolunteer.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SlVolunteer entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除志愿者
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void load(List<?> result) {
        List<SlVolunteer> slVolunteers = BeanUtil.copyToList(result, SlVolunteer.class);
        List<SlGridStructureCharacter> characters = new ArrayList<>(slVolunteers.size());
        List<SlGridZoneCharacterRel> zoneCharacterRels = new ArrayList<>();

        List<SlGridZone> gridZones = slGridZoneMapper.selectList();
        Map<String, Long> gridZoneIds = gridZones.stream().collect(Collectors.toMap(SlGridZone::getGridZoneName, SlGridZone::getGridZoneId));

        slVolunteers.forEach(a -> {
            a.setVolunteerId(IdGeneratorHelper.next());
            SlGridStructureCharacter c = new SlGridStructureCharacter();
            c.setGridCharacterId(a.getVolunteerId());
            c.setCharacterSex(a.getSex());
            c.setCharacterServiceType(SlOneMapConstants.SL_SERVICE_TYPE_VOLUNTEER);
            c.setCharacterName(a.getVolunteerName());
            c.setCharacterWorkPhone(a.getVolunteerPhone());
            c.setCreateBy(a.getCreateBy());
            c.setUpdateBy(a.getUpdateBy());
            characters.add(c);
            if (StringUtils.isNotEmpty(a.getGridZoneName()) && gridZoneIds.containsKey(a.getGridZoneName())) {
                SlGridZoneCharacterRel rel = new SlGridZoneCharacterRel();
                rel.setZoneCharacterRelId(IdGeneratorHelper.next());
                rel.setGridCharacterId(c.getGridCharacterId());
                rel.setGridZoneId(gridZoneIds.get(a.getGridZoneName()));
                rel.setCreateBy(a.getCreateBy());
                rel.setUpdateBy(a.getUpdateBy());
                zoneCharacterRels.add(rel);
            }

        });
        baseMapper.insertBatch(slVolunteers);
        zoneCharacterRelMapper.insertBatch(zoneCharacterRels);
        structureCharacterMapper.insertBatch(characters);
    }

    @Override
    public void register() {
        ExcelToDatabaseSwitch.register(new ExcelToDatabaseSwitch("volunteer", SlVolunteerDto.class, this.getClass()));
    }
}
