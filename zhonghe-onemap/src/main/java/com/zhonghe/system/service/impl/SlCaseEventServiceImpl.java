package com.zhonghe.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhonghe.common.helper.IdGeneratorHelper;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.system.domain.SlCaseEvent;
import com.zhonghe.system.domain.SlCaseEventOnenet;
import com.zhonghe.system.domain.bo.SlCaseEventBo;
import com.zhonghe.system.domain.dto.OneNetResponseDto;
import com.zhonghe.system.domain.dto.SlCaseEventDto;
import com.zhonghe.system.domain.thirdparty.IntegrationClue;
import com.zhonghe.system.domain.thirdparty.vo.IntegrationClueVo;
import com.zhonghe.system.domain.vo.SlCaseEventVo;
import com.zhonghe.system.enmu.OneNetConfig;
import com.zhonghe.system.excel.ExcelToDatabaseSwitch;
import com.zhonghe.system.excel.service.ILoadExcelService;
import com.zhonghe.system.mapper.SlCaseEventMapper;
import com.zhonghe.system.mapper.SlCaseEventOnenetMapper;
import com.zhonghe.system.service.IIntegrationClueService;
import com.zhonghe.system.service.IOneNetHttpService;
import com.zhonghe.system.service.ISlCaseEventService;
import lombok.RequiredArgsConstructor;
import net.postgis.jdbc.geometry.Geometry;
import net.postgis.jdbc.geometry.Point;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 案时间Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-08-14
 */
@RequiredArgsConstructor
@Service
public class SlCaseEventServiceImpl implements ISlCaseEventService, ILoadExcelService {

    private final SlCaseEventMapper baseMapper;

    private final SlCaseEventOnenetMapper slCaseEventOnenetMapper;

    private final IOneNetHttpService httpService;

    private final IIntegrationClueService integrationClueService;

    /**
     * 查询案时间
     */
    @Override
    public SlCaseEventVo queryById(Long caseEventId) {
        return baseMapper.selectVoById(caseEventId);
    }


    /**
     * 查询案时间列表
     */
    @Override
    public List<SlCaseEventVo> queryList(SlCaseEventBo bo) {
        LambdaQueryWrapper<SlCaseEvent> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SlCaseEvent> buildQueryWrapper(SlCaseEventBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SlCaseEvent> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getCaseEventCode()), SlCaseEvent::getCaseEventCode, bo.getCaseEventCode());
        lqw.eq(StringUtils.isNotBlank(bo.getCaseEventSource()), SlCaseEvent::getCaseEventSource, bo.getCaseEventSource());
        lqw.eq(StringUtils.isNotBlank(bo.getCaseEventStreet()), SlCaseEvent::getCaseEventStreet, bo.getCaseEventStreet());
        lqw.eq(StringUtils.isNotBlank(bo.getCaseEventZone()), SlCaseEvent::getCaseEventZone, bo.getCaseEventZone());
        lqw.eq(StringUtils.isNotBlank(bo.getCaseEventGrid()), SlCaseEvent::getCaseEventGrid, bo.getCaseEventGrid());
        lqw.eq(StringUtils.isNotBlank(bo.getCaseEventDepartment()), SlCaseEvent::getCaseEventDepartment, bo.getCaseEventDepartment());
        lqw.eq(StringUtils.isNotBlank(bo.getCaseEventReason()), SlCaseEvent::getCaseEventReason, bo.getCaseEventReason());
        lqw.eq(StringUtils.isNotBlank(bo.getCaseEventSubject()), SlCaseEvent::getCaseEventSubject, bo.getCaseEventSubject());
        lqw.eq(StringUtils.isNotBlank(bo.getCaseEventAddress()), SlCaseEvent::getCaseEventAddress, bo.getCaseEventAddress());
        lqw.eq(StringUtils.isNotBlank(bo.getCaseEventCreateTime()), SlCaseEvent::getCaseEventCreateTime, bo.getCaseEventCreateTime());
        lqw.eq(StringUtils.isNotBlank(bo.getCaseEventEndDate()), SlCaseEvent::getCaseEventEndDate, bo.getCaseEventEndDate());
        lqw.eq(StringUtils.isNotBlank(bo.getCaseEventHandlerOrg()), SlCaseEvent::getCaseEventHandlerOrg, bo.getCaseEventHandlerOrg());
        lqw.eq(StringUtils.isNotBlank(bo.getCaseEventStatus()), SlCaseEvent::getCaseEventStatus, bo.getCaseEventStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getCaseEventMark()), SlCaseEvent::getCaseEventMark, bo.getCaseEventMark());
        lqw.eq(StringUtils.isNotBlank(bo.getCaseEventGatherSource()), SlCaseEvent::getCaseEventGatherSource, bo.getCaseEventGatherSource());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), SlCaseEvent::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增案时间
     */
    @Override
    public Boolean insertByBo(SlCaseEventBo bo) {
        SlCaseEvent add = BeanUtil.toBean(bo, SlCaseEvent.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setCaseEventId(add.getCaseEventId());
        }
        return flag;
    }

    /**
     * 修改案时间
     */
    @Override
    public Boolean updateByBo(SlCaseEventBo bo) {
        SlCaseEvent update = BeanUtil.toBean(bo, SlCaseEvent.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SlCaseEvent entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除案时间
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public Boolean refresh() {
        int page = 1;
        IPage<IntegrationClueVo> integrationClueVoIPage;
        do {
            Page<IntegrationClue> p = new Page<>();
            p.setCurrent(page);
            p.setSize(1000);
            integrationClueVoIPage = integrationClueService.selectVoPageList(p, new LambdaQueryWrapper<>());
            if (integrationClueVoIPage != null && CollectionUtil.isNotEmpty(integrationClueVoIPage.getRecords())) {
                ++page;
                // 刷新地图操作
                List<SlCaseEventOnenet> formats = new ArrayList<>(integrationClueVoIPage.getRecords().size());

                integrationClueVoIPage.getRecords().forEach(r -> {
                    SlCaseEventOnenet tmp = new SlCaseEventOnenet();
                    tmp.setCaseEventId(r.getId());
                    tmp.setCaseEventOnenetId(IdGeneratorHelper.next());
                    if (r.getLat() != null && r.getLng() != null && !"null".equals(r.getLng()) && !"null".equals(r.getLat())) {
                        Geometry geometry = new Point(Double.parseDouble(r.getLat()), Double.parseDouble(r.getLng()));
                        tmp.setCaseEventGem(geometry);
                    }
                    tmp.setCaseEventAddress(r.getBuildAddress());
                    tmp.setCaseEventCode(String.valueOf(r.getId()));
                    tmp.setCreateBy("system");
                    tmp.setUpdateBy("system");
                    tmp.setUpdateTime(new Date());
                    tmp.setCreateTime(r.getCreateDate());
                    tmp.setAsjid(r.getDangerId());
                    formats.add(tmp);

                });
                slCaseEventOnenetMapper.insertBatch(formats);
            }


        } while (integrationClueVoIPage != null && CollectionUtil.isNotEmpty(integrationClueVoIPage.getRecords()) && integrationClueVoIPage.getRecords().size() == 1000);

        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void load(List<?> result) {
        List<SlCaseEvent> slCaseEvents = BeanUtil.copyToList(result, SlCaseEvent.class);
        this.refresh(slCaseEvents);
        baseMapper.insertBatch(slCaseEvents);
    }

    private void refresh(List<SlCaseEvent> slCaseEvents) {

        List<SlCaseEventOnenet> onenets = new ArrayList<>(slCaseEvents.size());
        slCaseEvents.forEach(c -> {
            c.setCaseEventId(IdGeneratorHelper.next());
            SlCaseEventOnenet onenet = new SlCaseEventOnenet();

            onenet.setCaseEventOnenetId(IdGeneratorHelper.next());
            onenet.setCaseEventId(c.getCaseEventId());
            onenet.setCaseEventCode(c.getCaseEventCode());
            onenet.setCaseEventAddress(c.getCaseEventAddress());
            onenet.setCreateBy(c.getCreateBy());
            onenet.setUpdateBy(c.getUpdateBy());
            if (StringUtils.isNotEmpty(c.getCaseEventAddress())) {
                OneNetResponseDto request = httpService.request(OneNetConfig.ADDRESS, new String[]{c.getCaseEventAddress()}, null);
                if (null != request) {
                    onenet.setOnenetJson(JSONUtil.toJsonStr(request));

                    List<Map<String, String>> data = request.getData().getData();
                    Map<String, String> select = OneNetConfig.ADDRESS.getSelector().select(data, "GXSJ");
                    if (CollectionUtil.isNotEmpty(select)) {

                        String zxjd = select.get("ZXJD");
                        String zxwd = select.get("ZXWD");
                        if (StringUtils.isNotEmpty(zxjd) && StringUtils.isNotEmpty(zxwd)) {
                            Geometry geometry = new Point(Double.parseDouble(zxjd), Double.parseDouble(zxwd));
                            onenet.setCaseEventGem(geometry);
                        }
                    }
                }
            }
            onenets.add(onenet);

        });
        slCaseEventOnenetMapper.insertBatch(onenets);
    }

    @Override
    public void register() {
        ExcelToDatabaseSwitch.register("case", SlCaseEventDto.class, this.getClass());
    }
}
