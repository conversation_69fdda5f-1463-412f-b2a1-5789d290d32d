package com.zhonghe.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zhonghe.common.helper.IdGeneratorHelper;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.system.domain.SlMarketPersonal;
import com.zhonghe.system.domain.SlMarketPersonalOnenet;
import com.zhonghe.system.domain.bo.SlMarketPersonalBo;
import com.zhonghe.system.domain.dto.OneNetResponseDto;
import com.zhonghe.system.domain.dto.SlMarketPersonalDto;
import com.zhonghe.system.domain.vo.SlMarketPersonalVo;
import com.zhonghe.system.enmu.OneNetConfig;
import com.zhonghe.system.excel.ExcelToDatabaseSwitch;
import com.zhonghe.system.excel.service.ILoadExcelService;
import com.zhonghe.system.mapper.DgsscztxxMapper;
import com.zhonghe.system.mapper.SlMarketPersonalMapper;
import com.zhonghe.system.mapper.SlMarketPersonalOnenetMapper;
import com.zhonghe.system.service.IOneNetHttpService;
import com.zhonghe.system.service.ISlMarketPersonalService;
import lombok.RequiredArgsConstructor;
import net.postgis.jdbc.geometry.Geometry;
import net.postgis.jdbc.geometry.Point;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 【请填写功能名称】Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-08-14
 */
@RequiredArgsConstructor
@Service
@DS("master")
public class SlMarketPersonalServiceImpl implements ISlMarketPersonalService, ILoadExcelService {

    private final SlMarketPersonalMapper baseMapper;

    private final DgsscztxxMapper dgsscztxxMapper;

    private final IOneNetHttpService oneNetHttpService;

    private final SlMarketPersonalOnenetMapper marketPersonalOnenetMapper;

    /**
     * 查询【请填写功能名称】
     */
    @Override
    public SlMarketPersonalVo queryById(Long marketPersonalId) {
        return baseMapper.selectVoById(marketPersonalId);
    }


    /**
     * 查询【请填写功能名称】列表
     */
    @Override
    public List<SlMarketPersonalVo> queryList(SlMarketPersonalBo bo) {
        LambdaQueryWrapper<SlMarketPersonal> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SlMarketPersonal> buildQueryWrapper(SlMarketPersonalBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SlMarketPersonal> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getPersonalCreditCode()), SlMarketPersonal::getPersonalCreditCode, bo.getPersonalCreditCode());
        lqw.like(StringUtils.isNotBlank(bo.getPersonalName()), SlMarketPersonal::getPersonalName, bo.getPersonalName());
        lqw.eq(StringUtils.isNotBlank(bo.getPersonalAddress()), SlMarketPersonal::getPersonalAddress, bo.getPersonalAddress());
        lqw.eq(StringUtils.isNotBlank(bo.getPersonalType()), SlMarketPersonal::getPersonalType, bo.getPersonalType());
        lqw.eq(StringUtils.isNotBlank(bo.getCreateDate()), SlMarketPersonal::getCreateDate, bo.getCreateDate());
        lqw.eq(StringUtils.isNotBlank(bo.getOperatingEndDate()), SlMarketPersonal::getOperatingEndDate, bo.getOperatingEndDate());
        lqw.eq(StringUtils.isNotBlank(bo.getOperatingScope()), SlMarketPersonal::getOperatingScope, bo.getOperatingScope());
        lqw.eq(StringUtils.isNotBlank(bo.getLegalPerson()), SlMarketPersonal::getLegalPerson, bo.getLegalPerson());
        lqw.eq(StringUtils.isNotBlank(bo.getIdType()), SlMarketPersonal::getIdType, bo.getIdType());
        lqw.eq(StringUtils.isNotBlank(bo.getIdCode()), SlMarketPersonal::getIdCode, bo.getIdCode());
        lqw.eq(StringUtils.isNotBlank(bo.getCurrencyRegister()), SlMarketPersonal::getCurrencyRegister, bo.getCurrencyRegister());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), SlMarketPersonal::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增【请填写功能名称】
     */
    @Override
    public Boolean insertByBo(SlMarketPersonalBo bo) {
        SlMarketPersonal add = BeanUtil.toBean(bo, SlMarketPersonal.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setMarketPersonalId(add.getMarketPersonalId());
        }
        return flag;
    }

    /**
     * 修改【请填写功能名称】
     */
    @Override
    public Boolean updateByBo(SlMarketPersonalBo bo) {
        SlMarketPersonal update = BeanUtil.toBean(bo, SlMarketPersonal.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SlMarketPersonal entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除【请填写功能名称】
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void refresh() {
        List<SlMarketPersonal> slMarketPersonals = baseMapper.selectList();
        marketPersonalOnenetMapper.delete(new LambdaQueryWrapper<>());
        this.refreshByExcel(slMarketPersonals);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public void insertBatch(List<SlMarketPersonalOnenet> personalOnenets) {
        if (CollectionUtil.isNotEmpty(personalOnenets)) {
            Set<String> marketPersonalIds = personalOnenets.stream().map(SlMarketPersonalOnenet::getMarketPersonalId).collect(Collectors.toSet());
            marketPersonalOnenetMapper.deleteBatchIds(marketPersonalIds);
            marketPersonalOnenetMapper.insertBatch(personalOnenets);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void load(List<?> result) {
        List<SlMarketPersonal> slMarketPersonals = BeanUtil.copyToList(result, SlMarketPersonal.class);
        this.refreshByExcel(slMarketPersonals);
        baseMapper.insertBatch(slMarketPersonals);
    }


    private void refreshByExcel(List<SlMarketPersonal> slMarketPersonals) {
        List<SlMarketPersonalOnenet> onenets = new ArrayList<>(slMarketPersonals.size());

        slMarketPersonals.forEach(e -> {
            if (StringUtils.isNotEmpty(e.getPersonalAddress())) {
                SlMarketPersonalOnenet onenet = new SlMarketPersonalOnenet();
                BeanUtil.copyProperties(e, onenet);
                onenet.setMarketPersonalOnenetId(IdGeneratorHelper.next());
                onenet.setMarketPersonalId(String.valueOf(e.getMarketPersonalId()));
                onenet.setPersonalName(e.getPersonalName());
                onenet.setPersonalCreditCode(e.getIdCode());
                //e.setMarketPersonalId(onenet.getMarketPersonalId());
                OneNetResponseDto request = oneNetHttpService.request(OneNetConfig.ADDRESS, new String[]{e.getPersonalAddress()}, null);
                if (null != request) {
                    List<Map<String, String>> data = request.getData().getData();
                    onenet.setOnenetJson(JSONUtil.toJsonStr(data));
                    onenet.setCreateBy(e.getCreateBy());
                    onenet.setUpdateBy(e.getUpdateBy());
                    Map<String, String> select = OneNetConfig.ADDRESS.getSelector().select(data, "GXSJ");
                    if (CollectionUtil.isNotEmpty(select)) {
                        String dzxz = select.get("DZXZ");
                        onenet.setPersonalAddress(dzxz);

                        String zxjd = select.get("ZXJD");
                        String zxwd = select.get("ZXWD");
                        if (StringUtils.isNotEmpty(zxjd) && StringUtils.isNotEmpty(zxwd)) {
                            Geometry geometry = new Point(Double.parseDouble(zxjd), Double.parseDouble(zxwd));
                            onenet.setMarketPersonalGem(geometry);
                        }
                    }
                }
                onenets.add(onenet);
            }
        });

        marketPersonalOnenetMapper.insertBatch(onenets);
    }

    @Override
    public void register() {
        ExcelToDatabaseSwitch.register("personal", SlMarketPersonalDto.class, this.getClass());
    }
}
