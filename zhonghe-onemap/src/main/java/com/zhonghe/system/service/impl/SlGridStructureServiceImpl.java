package com.zhonghe.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zhonghe.common.helper.IdGeneratorHelper;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.system.domain.SlGridStructure;
import com.zhonghe.system.domain.bo.grid.SlGridStructureBo;
import com.zhonghe.system.domain.dto.SlGridPostStructureDto;
import com.zhonghe.system.domain.vo.grid.SlGridStructureVo;
import com.zhonghe.system.excel.ExcelToDatabaseSwitch;
import com.zhonghe.system.excel.service.ILoadExcelService;
import com.zhonghe.system.mapper.SlGridStructureMapper;
import com.zhonghe.system.service.ISlGridStructureService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 网格职位架构Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-08-02
 */
@RequiredArgsConstructor
@Service
public class SlGridStructureServiceImpl implements ISlGridStructureService, ILoadExcelService {

    private final SlGridStructureMapper baseMapper;

    /**
     * 查询网格职位架构
     */
    @Override
    public SlGridStructureVo queryById(Long gridStructureId) {
        return baseMapper.selectVoById(gridStructureId);
    }


    /**
     * 查询网格职位架构列表
     */
    @Override
    public List<SlGridStructureVo> queryList(SlGridStructureBo bo) {
        LambdaQueryWrapper<SlGridStructure> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SlGridStructure> buildQueryWrapper(SlGridStructureBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SlGridStructure> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getGridStructureParentName()), SlGridStructure::getGridStructureParentName, bo.getGridStructureParentName());
        lqw.like(StringUtils.isNotBlank(bo.getGirdStructureName()), SlGridStructure::getGirdStructureName, bo.getGirdStructureName());
        lqw.eq(bo.getGridStructureParentId() != null, SlGridStructure::getGridStructureParentId, bo.getGridStructureParentId());
        lqw.eq(StringUtils.isNotBlank(bo.getMark()), SlGridStructure::getMark, bo.getMark());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), SlGridStructure::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增网格职位架构
     */
    @Override
    public Boolean insertByBo(SlGridStructureBo bo) {
        SlGridStructure add = BeanUtil.toBean(bo, SlGridStructure.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setGridStructureId(add.getGridStructureId());
        }
        return flag;
    }

    /**
     * 修改网格职位架构
     */
    @Override
    public Boolean updateByBo(SlGridStructureBo bo) {
        SlGridStructure update = BeanUtil.toBean(bo, SlGridStructure.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SlGridStructure entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除网格职位架构
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void load(List<?> result) {
        List<SlGridStructure> structures = BeanUtil.copyToList(result, SlGridStructure.class);
        Map<String, Long> map = new HashMap<>(structures.size());
        Long masterId = null;
        for (SlGridStructure structure : structures) {
            structure.setGridStructureId(IdGeneratorHelper.next());
            map.put(structure.getGridZoneName() + structure.getGirdStructureName(), structure.getGridStructureId());
            if (StringUtils.isEmpty(structure.getGridZoneName())) {
                masterId = structure.getGridStructureId();
            }
        }

        for (SlGridStructure d : structures) {
            if (StringUtils.isNotEmpty(d.getGridStructureParentName())) {
                d.setGridStructureParentId(map.getOrDefault(d.getGridZoneName() + d.getGridStructureParentName(), masterId));
            }
        }
        baseMapper.insertBatch(structures);
    }

    @Override
    public void register() {
        ExcelToDatabaseSwitch.register(new ExcelToDatabaseSwitch("grid-structure", SlGridPostStructureDto.class, this.getClass()));
    }
}
