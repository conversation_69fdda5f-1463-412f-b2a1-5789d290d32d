package com.zhonghe.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zhonghe.common.helper.IdGeneratorHelper;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.system.domain.SlWeatherStationOnenet;
import com.zhonghe.system.domain.thirdparty.SlWeatherStationData;
import com.zhonghe.system.domain.thirdparty.bo.SlWeatherStationDataBo;
import com.zhonghe.system.domain.thirdparty.vo.SlWeatherStationDataVo;
import com.zhonghe.system.mapper.SlWeatherStationDataMapper;
import com.zhonghe.system.service.ISlWeatherStationDataService;
import lombok.RequiredArgsConstructor;
import net.postgis.jdbc.geometry.Geometry;
import net.postgis.jdbc.geometry.Point;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 【请填写功能名称】Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-08-21
 */
@RequiredArgsConstructor
@Service
@DS("data")
public class SlWeatherStationDataServiceImpl implements ISlWeatherStationDataService {

    private final SlWeatherStationDataMapper baseMapper;

    /**
     * 查询【请填写功能名称】
     */
    @Override
    public SlWeatherStationDataVo queryById(String fywzjid) {
        return baseMapper.selectVoById(fywzjid);
    }


    /**
     * 查询【请填写功能名称】列表
     */
    @Override
    public List<SlWeatherStationDataVo> queryList(SlWeatherStationDataBo bo) {
        LambdaQueryWrapper<SlWeatherStationData> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SlWeatherStationData> buildQueryWrapper(SlWeatherStationDataBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SlWeatherStationData> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getFywzjid()), SlWeatherStationData::getFywzjid, bo.getFywzjid());
        lqw.eq(StringUtils.isNotBlank(bo.getXzsj()), SlWeatherStationData::getXzsj, bo.getXzsj());
        lqw.eq(StringUtils.isNotBlank(bo.getZlsj()), SlWeatherStationData::getZlsj, bo.getZlsj());
        lqw.eq(StringUtils.isNotBlank(bo.getZlbs()), SlWeatherStationData::getZlbs, bo.getZlbs());
        lqw.eq(StringUtils.isNotBlank(bo.getPch()), SlWeatherStationData::getPch, bo.getPch());
        lqw.eq(StringUtils.isNotBlank(bo.getZm()), SlWeatherStationData::getZm, bo.getZm());
        lqw.eq(StringUtils.isNotBlank(bo.getZljd()), SlWeatherStationData::getZljd, bo.getZljd());
        lqw.eq(StringUtils.isNotBlank(bo.getZdwd()), SlWeatherStationData::getZdwd, bo.getZdwd());
        lqw.eq(StringUtils.isNotBlank(bo.getJcsj()), SlWeatherStationData::getJcsj, bo.getJcsj());
        lqw.eq(StringUtils.isNotBlank(bo.getYxsyl()), SlWeatherStationData::getYxsyl, bo.getYxsyl());
        lqw.eq(StringUtils.isNotBlank(bo.getZdqw()), SlWeatherStationData::getZdqw, bo.getZdqw());
        return lqw;
    }

    /**
     * 新增【请填写功能名称】
     */
    @Override
    public Boolean insertByBo(SlWeatherStationDataBo bo) {
        SlWeatherStationData add = BeanUtil.toBean(bo, SlWeatherStationData.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setFywzjid(add.getFywzjid());
        }
        return flag;
    }

    /**
     * 修改【请填写功能名称】
     */
    @Override
    public Boolean updateByBo(SlWeatherStationDataBo bo) {
        SlWeatherStationData update = BeanUtil.toBean(bo, SlWeatherStationData.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SlWeatherStationData entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除【请填写功能名称】
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public List<SlWeatherStationOnenet> queryNewStation(List<String> stationNames) {

        List<SlWeatherStationData> stationData = baseMapper.
            selectList(
                new LambdaQueryWrapper<SlWeatherStationData>()
                    .select(SlWeatherStationData::getZm, SlWeatherStationData::getZdjd, SlWeatherStationData::getZdwd)
                    .notIn(CollectionUtils.isNotEmpty(stationNames), SlWeatherStationData::getZm, stationNames)
                    .groupBy(SlWeatherStationData::getZm, SlWeatherStationData::getZdjd, SlWeatherStationData::getZdwd));

        List<SlWeatherStationOnenet> r = new ArrayList<>(stationData.size());

        stationData.forEach(s -> {
            SlWeatherStationOnenet onenet = new SlWeatherStationOnenet();
            onenet.setStationOnenetId(IdGeneratorHelper.next());
            onenet.setStationName(s.getZm());
            if (ObjectUtils.isNotEmpty(s.getZdwd()) && ObjectUtils.isNotEmpty(s.getZdjd())) {
                Geometry geometry = new Point(s.getZdjd(), s.getZdwd());
                onenet.setStationGem(geometry);
                onenet.setUpdateBy("schedule");
                onenet.setCreateBy("schedule");
            }
            r.add(onenet);
        });

        return r;
    }
}
