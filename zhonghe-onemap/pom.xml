<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.zhonghe</groupId>
        <artifactId>zhonghe-vue-plus</artifactId>
        <version>2.0</version>
    </parent>
    <artifactId>zhonghe-onemap</artifactId>
    <name>zhonghe-onemap</name>
    <url>http://maven.apache.org</url>


    <description>
        石龙一张图工程
    </description>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.zhonghe</groupId>
            <artifactId>zhonghe-system</artifactId>
        </dependency>
        <!-- GIS模块功能 -->
<!--        <dependency>
            <groupId>org.locationtech.jts</groupId>
            <artifactId>jts-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.geotools</groupId>
            <artifactId>gt-main</artifactId>
        </dependency>
        <dependency>
            <groupId>org.geotools</groupId>
            <artifactId>gt-geojson</artifactId>
        </dependency>-->
    </dependencies>
</project>
