-- 企业风险分析AI训练数据视图
-- 用于训练AI模型识别企业风险信号和预警分析
CREATE OR REPLACE VIEW cop.v_cop_risk_ai_training
AS
WITH risk_history AS (
    -- 企业历史风险记录统计
    SELECT cop_id,
           COUNT(*)                                           as total_risk_count,
           COUNT(CASE WHEN level >= 3 THEN 1 END)             as high_risk_count,
           COUNT(CASE WHEN level = 2 THEN 1 END)              as medium_risk_count,
           COUNT(CASE WHEN level = 1 THEN 1 END)              as low_risk_count,
           MAX(rep_date)                                      as last_risk_date,
           AVG(CASE WHEN level > 0 THEN level END)            as avg_risk_level,
           -- 欠薪风险指标
           MAX(COALESCE(propaymonth, 0))                      as max_unpaid_salary_months,
           MAX(COALESCE(propaymoney, 0))                      as max_unpaid_salary_amount,
           MAX(COALESCE(propaypopulation, 0))                 as max_affected_employees,
           -- 欠税风险指标
           MAX(COALESCE(protaxmonth, 0))                      as max_unpaid_tax_months,
           MAX(COALESCE(protaxmoney, 0))                      as max_unpaid_tax_amount,
           -- 欠社保风险指标
           MAX(COALESCE(proinsuremonth, 0))                   as max_unpaid_insurance_months,
           MAX(COALESCE(proinsuremoney, 0))                   as max_unpaid_insurance_amount,
           -- 欠水电费风险指标
           MAX(COALESCE(prowatermonth, 0))                    as max_unpaid_water_months,
           MAX(COALESCE(prowatermoney, 0))                    as max_unpaid_water_amount,
           MAX(COALESCE(proenergymonth, 0))                   as max_unpaid_energy_months,
           MAX(COALESCE(proenergymoney, 0))                   as max_unpaid_energy_amount,
           -- 欠租金风险指标
           MAX(COALESCE(prorentmonth, 0))                     as max_unpaid_rent_months,
           MAX(COALESCE(prorentmoney, 0))                     as max_unpaid_rent_amount,
           -- 企业异常状态统计
           COUNT(CASE WHEN copabnormal = '1' THEN 1 END)      as abnormal_count,
           COUNT(CASE WHEN copmajorabnormal = '1' THEN 1 END) as major_abnormal_count,
           -- 处理状态统计
           COUNT(CASE WHEN despacho = 0 THEN 1 END)           as unhandled_count,
           COUNT(CASE WHEN auditing = 0 THEN 1 END)           as unaudited_count
    FROM cop.t_rep_report
    WHERE del_flag = '0'
    GROUP BY cop_id),
     latest_risk AS (
         -- 最新风险状态
         SELECT DISTINCT trr.cop_id,
                         FIRST_VALUE(trr.level)
                         OVER (PARTITION BY trr.cop_id ORDER BY trr.rep_date DESC) as current_risk_level,
                         FIRST_VALUE(trr.despacho)
                         OVER (PARTITION BY trr.cop_id ORDER BY trr.rep_date DESC) as current_handle_status,
                         FIRST_VALUE(trr.auditing)
                         OVER (PARTITION BY trr.cop_id ORDER BY trr.rep_date DESC) as current_audit_status,
                         FIRST_VALUE(trr.note)
                         OVER (PARTITION BY trr.cop_id ORDER BY trr.rep_date DESC) as latest_risk_note,
                         FIRST_VALUE(sd.dept_name)
                         OVER (PARTITION BY trr.cop_id ORDER BY trr.rep_date DESC) as reporting_dept
         FROM cop.t_rep_report trr
                  LEFT JOIN public.sys_dept sd ON sd.dept_id = trr.dept_id
         WHERE trr.del_flag = '0'),
     dept_risk_stats AS (
         -- 部门风险统计
         SELECT trr.dept_id,
                COUNT(DISTINCT trr.cop_id) as dept_risk_enterprise_count,
                AVG(trr.level)             as dept_avg_risk_level
         FROM cop.t_rep_report trr
         WHERE trr.del_flag = '0'
           AND trr.level > 0
         GROUP BY trr.dept_id)
SELECT
    -- 企业基本信息
    tci.cop_id                                                                       as cop_id,
    tci.cop_name                                                                     as cop_name,
    tci.cop_address                                                                  as cop_address,
    tci.cop_owner                                                                    as cop_owner,
    tci.cop_contacts                                                                 as cop_contacts,
    tci.cop_phone                                                                    as contact_phone,
    tci.cop_population                                                               as cop_population,
    tci.scope_of_business                                                            as scope_of_business,
    tci.business_licence                                                             as business_license,
    -- 当前风险状态
    COALESCE(lr.current_risk_level, 0)                                               as current_risk_level,
    CASE
        WHEN lr.current_risk_level IS NULL THEN '正常'
        WHEN lr.current_risk_level = 0 THEN '正常'
        WHEN lr.current_risk_level = 1 THEN '问题'
        WHEN lr.current_risk_level = 2 THEN '风险'
        WHEN lr.current_risk_level = 3 THEN '危险'
        WHEN lr.current_risk_level = 4 THEN '高危'
        ELSE '未知'
        END                                                                          as current_risk_label,

    -- 历史风险统计
    COALESCE(rh.total_risk_count, 0)                                                 as total_risk_incidents,
    COALESCE(rh.high_risk_count, 0)                                                  as high_risk_incidents,
    COALESCE(rh.medium_risk_count, 0)                                                as medium_risk_incidents,
    COALESCE(rh.low_risk_count, 0)                                                   as low_risk_incidents,
    COALESCE(rh.avg_risk_level, 0)                                                   as avg_historical_risk_level,
    rh.last_risk_date,

    -- 财务风险指标
    COALESCE(rh.max_unpaid_salary_months, 0)                                         as max_unpaid_salary_months,
    COALESCE(rh.max_unpaid_salary_amount, 0)                                         as max_unpaid_salary_amount,
    COALESCE(rh.max_affected_employees, 0)                                           as max_affected_employees,
    COALESCE(rh.max_unpaid_tax_months, 0)                                            as max_unpaid_tax_months,
    COALESCE(rh.max_unpaid_tax_amount, 0)                                            as max_unpaid_tax_amount,
    COALESCE(rh.max_unpaid_insurance_months, 0)                                      as max_unpaid_insurance_months,
    COALESCE(rh.max_unpaid_insurance_amount, 0)                                      as max_unpaid_insurance_amount,
    COALESCE(rh.max_unpaid_water_months, 0)                                          as max_unpaid_water_months,
    COALESCE(rh.max_unpaid_water_amount, 0)                                          as max_unpaid_water_amount,
    COALESCE(rh.max_unpaid_energy_months, 0)                                         as max_unpaid_energy_months,
    COALESCE(rh.max_unpaid_energy_amount, 0)                                         as max_unpaid_energy_amount,
    COALESCE(rh.max_unpaid_rent_months, 0)                                           as max_unpaid_rent_months,
    COALESCE(rh.max_unpaid_rent_amount, 0)                                           as max_unpaid_rent_amount,

    -- 异常状态指标
    COALESCE(rh.abnormal_count, 0)                                                   as abnormal_incidents,
    COALESCE(rh.major_abnormal_count, 0)                                             as major_abnormal_incidents,
    COALESCE(rh.unhandled_count, 0)                                                  as unhandled_incidents,
    COALESCE(rh.unaudited_count, 0)                                                  as unaudited_incidents,

    -- 处理状态
    COALESCE(lr.current_handle_status, 0)                                            as current_handle_status,
    COALESCE(lr.current_audit_status, 0)                                             as current_audit_status,
    lr.latest_risk_note,
    lr.reporting_dept,

    -- 部门风险环境
    COALESCE(drs.dept_risk_enterprise_count, 0)                                      as dept_total_risk_enterprises,
    COALESCE(drs.dept_avg_risk_level, 0)                                             as dept_avg_risk_level,

    -- 风险评分计算 (用于AI训练标签)
    CASE
        WHEN COALESCE(lr.current_risk_level, 0) >= 4 THEN 100
        WHEN COALESCE(lr.current_risk_level, 0) = 3 THEN 80
        WHEN COALESCE(lr.current_risk_level, 0) = 2 THEN 60
        WHEN COALESCE(lr.current_risk_level, 0) = 1 THEN 40
        ELSE 20
        END +
    CASE
        WHEN COALESCE(rh.max_unpaid_salary_months, 0) >= 6 THEN 20
        WHEN COALESCE(rh.max_unpaid_salary_months, 0) >= 3 THEN 10
        WHEN COALESCE(rh.max_unpaid_salary_months, 0) >= 1 THEN 5
        ELSE 0
        END +
    CASE
        WHEN COALESCE(rh.max_unpaid_tax_months, 0) >= 6 THEN 15
        WHEN COALESCE(rh.max_unpaid_tax_months, 0) >= 3 THEN 8
        WHEN COALESCE(rh.max_unpaid_tax_months, 0) >= 1 THEN 3
        ELSE 0
        END +
    CASE
        WHEN COALESCE(rh.total_risk_count, 0) >= 10 THEN 15
        WHEN COALESCE(rh.total_risk_count, 0) >= 5 THEN 10
        WHEN COALESCE(rh.total_risk_count, 0) >= 1 THEN 5
        ELSE 0
        END                                                                          as comprehensive_risk_score,

    -- AI训练标签
    CASE
        WHEN COALESCE(lr.current_risk_level, 0) >= 4 OR
             COALESCE(rh.max_unpaid_salary_months, 0) >= 6 OR
             COALESCE(rh.max_unpaid_tax_months, 0) >= 6 THEN 'HIGH_RISK'
        WHEN COALESCE(lr.current_risk_level, 0) >= 3 OR
             COALESCE(rh.max_unpaid_salary_months, 0) >= 3 OR
             COALESCE(rh.total_risk_count, 0) >= 5 THEN 'MEDIUM_RISK'
        WHEN COALESCE(lr.current_risk_level, 0) >= 2 OR
             COALESCE(rh.total_risk_count, 0) >= 1 THEN 'LOW_RISK'
        ELSE 'NORMAL'
        END                                                                          as ai_training_label,

    -- 预警类型分类 (基于您提到的预警信息)
    CASE
        WHEN COALESCE(rh.max_unpaid_salary_months, 0) > 0 THEN '欠薪预警'
        WHEN COALESCE(rh.max_unpaid_tax_months, 0) > 0 THEN '欠税预警'
        WHEN COALESCE(rh.max_unpaid_insurance_months, 0) > 0 THEN '欠社保预警'
        WHEN COALESCE(rh.max_unpaid_water_months, 0) > 0 THEN '欠水费预警'
        WHEN COALESCE(rh.max_unpaid_energy_months, 0) > 0 THEN '欠电费预警'
        WHEN COALESCE(rh.max_unpaid_rent_months, 0) > 0 THEN '欠租金预警'
        ELSE '正常'
        END                                                                          as warning_type,

    -- 时间特征
    EXTRACT(YEAR FROM CURRENT_DATE) - EXTRACT(YEAR FROM tci.create_time)             as enterprise_record_years,
    EXTRACT(DAYS FROM (CURRENT_DATE - COALESCE(rh.last_risk_date, tci.create_time))) as days_since_last_risk,

    -- 数据更新时间
    CURRENT_TIMESTAMP                                                                as data_update_time

FROM cop.t_cop_information tci
         LEFT JOIN risk_history rh ON tci.cop_id = rh.cop_id
         LEFT JOIN latest_risk lr ON tci.cop_id = lr.cop_id
         LEFT JOIN dept_risk_stats drs
                   ON lr.reporting_dept = (SELECT dept_name FROM public.sys_dept WHERE dept_id = drs.dept_id)
WHERE tci.del_flag = '0'
  and is_cancel = '0'
ORDER BY comprehensive_risk_score DESC, tci.cop_name;


-- 添加视图和字段注释
COMMENT ON VIEW cop.v_cop_risk_ai_training IS '企业风险分析AI训练数据视图，包含企业基本信息、历史风险统计、财务风险指标等用于AI模型训练的综合数据';

-- 企业基本信息字段注释
COMMENT ON COLUMN cop.v_cop_risk_ai_training.cop_id IS '企业ID，主键标识';
COMMENT ON COLUMN cop.v_cop_risk_ai_training.cop_name IS '企业名称';
COMMENT ON COLUMN cop.v_cop_risk_ai_training.cop_address IS '企业地址';
COMMENT ON COLUMN cop.v_cop_risk_ai_training.cop_owner IS '企业法人代表';
COMMENT ON COLUMN cop.v_cop_risk_ai_training.cop_contacts IS '企业联系人';
COMMENT ON COLUMN cop.v_cop_risk_ai_training.contact_phone IS '企业联系电话';
COMMENT ON COLUMN cop.v_cop_risk_ai_training.cop_population IS '企业员工人数';
COMMENT ON COLUMN cop.v_cop_risk_ai_training.scope_of_business IS '企业经营范围';
COMMENT ON COLUMN cop.v_cop_risk_ai_training.business_license IS '营业执照注册号';

-- 风险状态字段注释
COMMENT ON COLUMN cop.v_cop_risk_ai_training.current_risk_level IS '当前风险等级，0-正常，1-问题，2-风险，3-危险，4-高危';
COMMENT ON COLUMN cop.v_cop_risk_ai_training.current_risk_label IS '当前风险等级标签，中文描述';

-- 历史风险统计字段注释
COMMENT ON COLUMN cop.v_cop_risk_ai_training.total_risk_incidents IS '历史风险事件总数';
COMMENT ON COLUMN cop.v_cop_risk_ai_training.high_risk_incidents IS '高危风险事件数量（等级>=3）';
COMMENT ON COLUMN cop.v_cop_risk_ai_training.medium_risk_incidents IS '中等风险事件数量（等级=2）';
COMMENT ON COLUMN cop.v_cop_risk_ai_training.low_risk_incidents IS '低风险事件数量（等级=1）';
COMMENT ON COLUMN cop.v_cop_risk_ai_training.avg_historical_risk_level IS '历史平均风险等级';
COMMENT ON COLUMN cop.v_cop_risk_ai_training.last_risk_date IS '最近风险事件日期';

-- 财务风险指标字段注释
COMMENT ON COLUMN cop.v_cop_risk_ai_training.max_unpaid_salary_months IS '最大欠薪月数';
COMMENT ON COLUMN cop.v_cop_risk_ai_training.max_unpaid_salary_amount IS '最大欠薪金额（元）';
COMMENT ON COLUMN cop.v_cop_risk_ai_training.max_affected_employees IS '最大受影响员工数';
COMMENT ON COLUMN cop.v_cop_risk_ai_training.max_unpaid_tax_months IS '最大欠税月数';
COMMENT ON COLUMN cop.v_cop_risk_ai_training.max_unpaid_tax_amount IS '最大欠税金额（元）';
COMMENT ON COLUMN cop.v_cop_risk_ai_training.max_unpaid_insurance_months IS '最大欠社保月数';
COMMENT ON COLUMN cop.v_cop_risk_ai_training.max_unpaid_insurance_amount IS '最大欠社保金额（元）';
COMMENT ON COLUMN cop.v_cop_risk_ai_training.max_unpaid_water_months IS '最大欠水费月数';
COMMENT ON COLUMN cop.v_cop_risk_ai_training.max_unpaid_water_amount IS '最大欠水费金额（元）';
COMMENT ON COLUMN cop.v_cop_risk_ai_training.max_unpaid_energy_months IS '最大欠电费月数';
COMMENT ON COLUMN cop.v_cop_risk_ai_training.max_unpaid_energy_amount IS '最大欠电费金额（元）';
COMMENT ON COLUMN cop.v_cop_risk_ai_training.max_unpaid_rent_months IS '最大欠租金月数';
COMMENT ON COLUMN cop.v_cop_risk_ai_training.max_unpaid_rent_amount IS '最大欠租金金额（元）';

-- 异常状态指标字段注释
COMMENT ON COLUMN cop.v_cop_risk_ai_training.abnormal_incidents IS '企业异常状态事件数量';
COMMENT ON COLUMN cop.v_cop_risk_ai_training.major_abnormal_incidents IS '企业重大异常状态事件数量';
COMMENT ON COLUMN cop.v_cop_risk_ai_training.unhandled_incidents IS '未处理事件数量';
COMMENT ON COLUMN cop.v_cop_risk_ai_training.unaudited_incidents IS '未审核事件数量';

-- 处理状态字段注释
COMMENT ON COLUMN cop.v_cop_risk_ai_training.current_handle_status IS '当前处理状态，0-未处理，1-已处理';
COMMENT ON COLUMN cop.v_cop_risk_ai_training.current_audit_status IS '当前审核状态，0-未审核，1-已审核，2-退回';
COMMENT ON COLUMN cop.v_cop_risk_ai_training.latest_risk_note IS '最新风险事件备注信息';
COMMENT ON COLUMN cop.v_cop_risk_ai_training.reporting_dept IS '上报部门名称';

-- 部门风险环境字段注释
COMMENT ON COLUMN cop.v_cop_risk_ai_training.dept_total_risk_enterprises IS '部门管辖的风险企业总数';
COMMENT ON COLUMN cop.v_cop_risk_ai_training.dept_avg_risk_level IS '部门管辖企业平均风险等级';

-- AI训练相关字段注释
COMMENT ON COLUMN cop.v_cop_risk_ai_training.comprehensive_risk_score IS '综合风险评分，基于多维度风险指标计算';
COMMENT ON COLUMN cop.v_cop_risk_ai_training.ai_training_label IS 'AI训练标签，HIGH_RISK-高风险，MEDIUM_RISK-中风险，LOW_RISK-低风险，NORMAL-正常';
COMMENT ON COLUMN cop.v_cop_risk_ai_training.warning_type IS '预警类型分类，如欠薪预警、欠税预警等';

-- 时间特征字段注释
COMMENT ON COLUMN cop.v_cop_risk_ai_training.enterprise_record_years IS '企业记录存在年限，基于创建时间计算';
COMMENT ON COLUMN cop.v_cop_risk_ai_training.days_since_last_risk IS '距离最近风险事件的天数';
COMMENT ON COLUMN cop.v_cop_risk_ai_training.data_update_time IS '数据更新时间戳';

