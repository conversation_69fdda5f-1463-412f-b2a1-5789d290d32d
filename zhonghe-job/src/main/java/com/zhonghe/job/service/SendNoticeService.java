package com.zhonghe.job.service;

import com.zhonghe.job.JobHandlerConstants;
import com.zhonghe.job.config.XXlJobUtil;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.executor.XxlJobExecutor;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;


/**
 * @Author: lpg
 * @Date: 2023/03/27/10:41
 * @Description:
 */
@Slf4j
//@Component
public class SendNoticeService {

    @Resource
    private XXlJobUtil xxlJobUtil;

    @XxlJob(value = JobHandlerConstants.sendNoticeBean)
    public void sendNoticeJobHandler() {
        XxlJobHelper.log("XXL-JOB, SendNoticeJobHandler is start.");
        log.info("任务参数：" + XxlJobHelper.getJobParam());
        //TODO 发送到服务器

        XxlJobExecutor.removeJobThread((int)XxlJobHelper.getJobId(),"定时任务已处理完成.");
        xxlJobUtil.remove((int)XxlJobHelper.getJobId());
    }
}
