package com.zhonghe.job.config;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zhonghe.common.utils.JsonUtils;
import com.zhonghe.job.config.properties.XxlJobProperties;
import com.zhonghe.job.entity.XxlJobInfo;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.net.HttpCookie;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * @Author: lpg
 * @Date: 2023/03/27/14:18
 * @Description:
 */
@Slf4j
//@Component
public class XXlJobUtil {

    @Resource
    private XxlJobProperties xxlJobProperties;

    private final Map<String,String> loginCookie = new HashMap<>();

    public  void login() {
        String url= xxlJobProperties.getAdminAddresses() + "/login";
        HttpResponse response = HttpRequest.post(url)
            .form("userName","admin")
            .form("password","123456")
            .execute();
        List<HttpCookie> cookies = response.getCookies();
        Optional<HttpCookie> cookieOpt = cookies.stream()
            .filter(cookie -> cookie.getName().equals("XXL_JOB_LOGIN_IDENTITY")).findFirst();
        if (!cookieOpt.isPresent())
            throw new RuntimeException("get xxl-job cookie error!");

        String value = cookieOpt.get().getValue();
        loginCookie.put("XXL_JOB_LOGIN_IDENTITY",value);
    }
    public  String getCookie() {
        for (int i = 0; i < 3; i++) {
            String cookieStr = loginCookie.get("XXL_JOB_LOGIN_IDENTITY");
            if (cookieStr !=null) {
                return "XXL_JOB_LOGIN_IDENTITY="+cookieStr;
            }
            login();
        }
        throw new RuntimeException("get xxl-job cookie error!");
    }
    /**
     * 添加并启动任务
     * @param xxlJobInfo
     * @return
     */
    public void addAndStart(XxlJobInfo xxlJobInfo){
        String cookie = getCookie();
        String body = HttpRequest.post(xxlJobProperties.getAdminAddresses() + "/jobinfo/add")
            .contentType("application/json")
            .body(JsonUtils.toJsonString(xxlJobInfo))
            .cookie(cookie)
            .execute().body();

        JSONObject jsonObject = JSON.parseObject(body);
        HttpRequest.post(xxlJobProperties.getAdminAddresses() + "/jobinfo/start")
            .form("id",jsonObject.get("data"))
            .cookie(cookie)
            .execute();
    }

    /**
     * 移除任务
     * @param id
     * @return
     */
    public String remove(int id){
        HttpResponse response = HttpRequest.post(xxlJobProperties.getAdminAddresses() + "/jobinfo/remove")
            .form("id",id)
            .cookie(getCookie())
            .execute();
        log.info("XXL-JOB, SendNoticeJobHandler is remove.");
        return response.body();
    }
}
