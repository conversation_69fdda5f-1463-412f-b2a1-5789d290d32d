<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>zhonghe-vue-plus</artifactId>
        <groupId>com.zhonghe</groupId>
        <version>2.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>zhonghe-flowable</artifactId>

    <description>
        flowable工作流
    </description>

    <dependencies>
        <dependency>
            <groupId>com.zhonghe</groupId>
            <artifactId>zhonghe-common</artifactId>
        </dependency>
        <dependency>
            <groupId>org.flowable</groupId>
            <artifactId>flowable-spring-boot-starter-process</artifactId>
        </dependency>

        <dependency>
            <groupId>com.googlecode.aviator</groupId>
            <artifactId>aviator</artifactId>
            <version>5.3.1</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

    </dependencies>
</project>
