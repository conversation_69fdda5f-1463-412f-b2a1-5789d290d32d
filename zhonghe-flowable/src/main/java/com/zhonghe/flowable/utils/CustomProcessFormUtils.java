package com.zhonghe.flowable.utils;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import com.zhonghe.flowable.core.CustomFormConf;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: lpg
 * @Date: 2023/06/27/14:39
 * @Description:
 */
public class CustomProcessFormUtils {

    private static final String FIELD = "field";
    private static final String CONFIG = "__config__";
    private static final String MODEL = "__vModel__";
    /**
     * 填充表单项内容
     *
     * @param formConf 表单配置信息
     * @param data     表单内容
     */
    public static void fillFormData(CustomFormConf formConf, Map<String, Object> data) {
        recursiveFillField(formConf, data);
    }

    @SuppressWarnings("unchecked")
    private static void recursiveFillField(CustomFormConf formConf, final Map<String, Object> data) {
        List<Map<String, Object>> fillFields = new ArrayList<>();

        if (ObjectUtil.isNull(formConf.getRules())) {
            return;
        }
        for (Map<String, Object> rule : formConf.getRules()) {
            String modelKey = Convert.toStr(rule.get(FIELD));
            Object value = data.get(modelKey);
            if (value != null) {
                Map<String, Object> map = new HashMap<>();
                map.put(FIELD, modelKey);
                map.put("defaultValue", value);
                fillFields.add(map);
            }
        }
        formConf.setValue(fillFields);
    }
}
