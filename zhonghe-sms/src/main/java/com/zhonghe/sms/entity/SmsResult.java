package com.zhonghe.sms.entity;

import lombok.Data;

/**
 * 上传返回体
 *
 * <AUTHOR> Li
 */
@Data
public class SmsResult {

    /**
     * 是否成功
     */
    private boolean isSuccess;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 实际响应体
     */
    private Object response;

    SmsResult(boolean isSuccess, String message, Object response) {
        this.isSuccess = isSuccess;
        this.message = message;
        this.response = response;
    }

    public static SmsResultBuilder builder() {
        return new SmsResultBuilder();
    }

    public static class SmsResultBuilder {
        private boolean isSuccess;
        private String message;
        private Object response;

        SmsResultBuilder() {
        }

        public SmsResultBuilder isSuccess(boolean isSuccess) {
            this.isSuccess = isSuccess;
            return this;
        }

        public SmsResultBuilder message(String message) {
            this.message = message;
            return this;
        }

        public SmsResultBuilder response(Object response) {
            this.response = response;
            return this;
        }

        public SmsResult build() {
            return new SmsResult(isSuccess, message, response);
        }

        @Override
        public String toString() {
            return "SmsResult.SmsResultBuilder(isSuccess=" + isSuccess + ", message=" + message + ", response=" + response + ")";
        }
    }
}
