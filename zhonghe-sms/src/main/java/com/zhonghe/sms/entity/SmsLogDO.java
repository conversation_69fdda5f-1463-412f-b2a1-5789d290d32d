package com.zhonghe.sms.entity;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zhonghe.common.annotation.ExcelDictFormat;
import com.zhonghe.common.convert.ExcelDictConvert;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.Map;

/**
 * 短信日志 DO
 *
 * <AUTHOR>
 * @since 2021-01-25
 */
@TableName(value = "system_sms_log", autoResultMap = true)
@KeySequence("system_sms_log_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@ToString(callSuper = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SmsLogDO implements Serializable {

    /**
     * 自增编号
     */
    @ExcelProperty(value = "自增编号")
    private Long id;

    // ========= 渠道相关字段 =========

    /**
     * 短信渠道编号
     *
     */
    @ExcelProperty(value = "短信渠道编号")
    private Long channelId;
    /**
     * 短信渠道编码
     *
     */
    @ExcelProperty(value = "短信渠道编码")
    private String channelCode;

    // ========= 模板相关字段 =========
    /**
     * 模板编号
     *
     */
    @ExcelProperty(value = "模板编号")
    private String templateId;
    /**
     * 模板编号
     *
     */
    @ExcelProperty(value = "模板编号")
    private String templateCode;
    /**
     * 短信类型
     * 0通知    1验证
     */
    @ExcelProperty(value = "短信类型 (0通知   1验证)", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=通知,1=验证")
    private String templateType;
    /**
     */
    @ExcelProperty(value = "模板内容")
    private String templateContent;
    /**
     */
    @ExcelProperty(value = "模板属性")
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Map<String, Object> templateParams;
    /**
     * 短信 API 的模板编号
     *
     */
    @ExcelProperty(value = "短信 API 的模板编号")
    private String apiTemplateId;

    // ========= 手机相关字段 =========

    /**
     * 手机号
     */
    @ExcelProperty(value = "手机号")
    private String mobile;
    /**
     * 用户编号
     */
    @ExcelProperty(value = "用户编号")
    private Long userId;
    /**
     * 用户类型
     *
     */
    @ExcelProperty(value = "用户类型")
    private Integer userType;

    // ========= 发送相关字段 =========

    /**
     * 发送状态 0：初始化  1：发送成功  2：发送失败   3：不发送
     *
     */
    @ExcelProperty(value = "发送状态 0：初始化  1：发送成功  2：发送失败   3：不发送",converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=初始化,1=发送成功,2=发送失败,3=不发送")
    private Integer sendStatus;
    /**
     * 发送时间
     */
    @ExcelProperty(value = "发送时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date sendTime;
    /**
     * 发送结果的编码
     *
     */
    @ExcelProperty(value = "发送结果的编码")
    private Integer sendCode;
    /**
     * 发送结果的提示
     *
     * 异常情况下，通过格式化 Exception 的提示存储
     */
    @ExcelProperty(value = "发送结果的提示")
    private String sendMsg;
    /**
     * 短信 API 发送结果的编码
     *
     * 由于第三方的错误码可能是字符串，所以使用 String 类型
     */
    @ExcelProperty(value = "短信 API 发送结果的编码")
    private String apiSendCode;
    /**
     * 短信 API 发送失败的提示
     */
    @ExcelProperty(value = "短信 API 发送失败的提示")
    private String apiSendMsg;
    /**
     * 短信 API 发送返回的唯一请求 ID
     *
     * 用于和短信 API 进行定位于排错
     */
    @ExcelProperty(value = "短信 API 发送返回的唯一请求 ID")
    private String apiRequestId;
    /**
     * 短信 API 发送返回的序号
     *
     * 用于和短信 API 平台的发送记录关联
     */
    @ExcelProperty(value = "短信 API 发送返回的序号")
    private String apiSerialNo;

    // ========= 接收相关字段 =========
    /**
     * 接收状态  0：等待接口  1：接收成功  2：接收失败
     *
     */
    @ExcelProperty(value = "接收状态  0：等待接口  1：接收成功  2：接收失败" ,converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=等待接口,1=接收成功,2=接收失败")
    private Integer receiveStatus;
    /**
     * 接收时间
     */
    @ExcelProperty(value = "接收时间")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date receiveTime;
    /**
     * 短信 API 接收结果的编码
     */
    @ExcelProperty(value = "短信 API 接收结果的编码")
    private String apiReceiveCode;
    /**
     * 短信 API 接收结果的提示
     */
    @ExcelProperty(value = "短信 API 接收结果的提示")
    private String apiReceiveMsg;

    /**
     * 搜索开始时间
     */
    @ExcelProperty(value = "搜索开始时间")
    @TableField(exist = false)
    private String beginSendTime;

    /**
     * 搜索结束时间
     */
    @ExcelProperty(value = "搜索结束时间")
    @TableField(exist = false)
    private String endSendTime;

    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date beginReceiveTime;

    @TableField(exist = false)
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date endReceiveTime;

    @TableField(exist = false)
    private Map<String,Object> params;
}
