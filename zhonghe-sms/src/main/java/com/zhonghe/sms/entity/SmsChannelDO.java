package com.zhonghe.sms.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.util.Date;

/**
 * 短信渠道 DO
 *
 * <AUTHOR>
 * @since 2021-01-25
 */
@TableName(value = "system_sms_channel", autoResultMap = true)
@Data
@ToString(callSuper = true)
@ApiModel("短信渠道对象")
public class SmsChannelDO implements Serializable {

    /**
     * 渠道编号
     */
    @ApiModelProperty("渠道编号")
    private Long id;
    /**
     * 短信签名
     */
    @ApiModelProperty("短信签名")
    private String signature;
    /**
     * 渠道编码
     *
     */
    @ApiModelProperty("渠道编码")
    private String code;
    /**
     * 启用状态
     *
     */
    @ApiModelProperty("启用状态")
    private Integer status;
    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;
    /**
     * 短信 API 的账号
     */
    @ApiModelProperty("短信 API 的账号")
    private String apiKey;
    /**
     * 短信 API 的密钥
     */
    @ApiModelProperty("短信 API 的密钥")
    private String apiSecret;
    /**
     * 短信发送回调 URL
     */
    @ApiModelProperty("短信发送回调 URL")
    private String callbackUrl;
    /**
     * 开始时间
     */
    @ApiModelProperty("开始时间")
    private Date startTime;
    /**
     *  结束时间
     */
    @ApiModelProperty("结束时间")
    private Date endTime;
}
