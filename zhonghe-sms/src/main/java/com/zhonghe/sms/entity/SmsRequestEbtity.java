package com.zhonghe.sms.entity;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.aliyun.tea.NameInMap;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.zhonghe.common.annotation.ExcelDictFormat;
import com.zhonghe.common.convert.ExcelDictConvert;
import com.zhonghe.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
//@Builder 使用这个注解之后就不能使用swagger扫描实体
@TableName(value = "sms_request_ebtity")
@ExcelIgnoreUnannotated
@ApiModel("短信模板对象")
public class SmsRequestEbtity extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @TableId(value = "out_id")
    @JsonProperty(value = "outId", required = true)
    @ExcelProperty(value = "模板编码")
    @ApiModelProperty("模板编码")
    private String outId;

    @NameInMap("OwnerId")
    @ExcelProperty(value = "代号")
    @ApiModelProperty("代号")
    private Long ownerId;

    @NameInMap("PhoneNumbers")
    @ExcelProperty(value = "电话号码")
    @ApiModelProperty("电话号码")
    private String phoneNumbers;

    @NameInMap("ResourceOwnerAccount")
    @ExcelProperty(value = "来往记录")
    @ApiModelProperty("来往记录")
    private String resourceOwnerAccount;

    @NameInMap("ResourceOwnerId")
    @ApiModelProperty("记录id")
    @ExcelProperty(value = "记录id")
    private Long resourceOwnerId;

    @NameInMap("SignName")
    @ExcelProperty(value = "署名")
    @ApiModelProperty("署名")
    private String signName;

    @NameInMap("SmsUpExtendCode")
    @ExcelProperty(value = "发送代码")
    @ApiModelProperty("发送代码")
    private String smsUpExtendCode;

    @NameInMap("TemplateCode")
    @ApiModelProperty("短信api模板号")
    @ExcelProperty(value = "短信api模板号")
    private String templateCode;

    @NameInMap("templateContent")
    @ExcelProperty(value = "模板内容")
    @ApiModelProperty("模板内容")
    private String templateContent;

    @NameInMap("smsType")
    @ExcelProperty(value = "短信类型",converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "1=验证码,2=通知,3=营销")
    @ApiModelProperty("短信类型")
    private String smsType;

    @NameInMap("idOpen")
    @ExcelProperty(value = "开启状态" ,converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=开启,1=关闭")
    @ApiModelProperty("开启状态 0：开启 1：关闭")
    @JsonProperty(value = "idOpen", required = true)
    private Integer idOpen;

    @NameInMap("node")
    @ExcelProperty(value = "备注")
    @ApiModelProperty("备注")
    private String node;

    @NameInMap("TemplateParam")
    @ExcelProperty(value = "模板名称")
    @ApiModelProperty("模板名称")
    private String templateParam;

    @NameInMap("testChannel")
    @ExcelProperty(value = "测试渠道")
    @ApiModelProperty("测试渠道")
    private String testChannel;
}
