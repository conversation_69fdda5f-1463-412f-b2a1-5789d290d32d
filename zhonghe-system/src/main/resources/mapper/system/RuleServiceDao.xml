<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhonghe.system.mapper.RuleServiceMapper">


    <!-- 批量插入 -->
    <insert id="insertBatch" keyProperty="serviceId" useGeneratedKeys="true">
        insert into public.gis_service(service_name, graph_name, service_type, Load_order, Load_status, gateway_proxy,
        create_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.serviceName}, #{entity.graphName}, #{entity.serviceType}, #{entity.loadOrder},
            #{entity.loadStatus}, #{entity.gatewayProxy}, #{entity.createTime})
        </foreach>
    </insert>
    <!-- 批量插入或按主键更新 -->
    <insert id="insertOrUpdateBatch" keyProperty="serviceId" useGeneratedKeys="true">
        insert into public.gis_service(service_name, graph_name, service_type, Load_order, Load_status, gateway_proxy, create_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.serviceName}, #{entity.graphName}, #{entity.serviceType}, #{entity.loadOrder}, #{entity.loadStatus}, #{entity.gatewayProxy}, #{entity.createTime})
        </foreach>
        on duplicate key update
         service_name = values(service_name) , graph_name = values(graph_name) , service_type = values(service_type) , Load_order = values(Load_order) , Load_status = values(Load_status) , gateway_proxy = values(gateway_proxy) , create_time = values(create_time)     </insert>

    <update id="updateRolueById" parameterType="java.lang.Long">
        update rule_service set server_power = #{authorization} WHERE rule_id = #{authorization}
    </update>

</mapper>

