<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhonghe.system.mapper.SysOssBucketMapper">

    <resultMap type="com.zhonghe.system.domain.SysOssBucket" id="SysOssBucketResult">
        <result property="id" column="id"/>
        <result property="nickName" column="nick_name"/>
        <result property="bucketType" column="bucket_type"/>
        <result property="originalName" column="original_name"/>
        <result property="fileSuffix" column="file_suffix"/>
        <result property="parentId" column="parent_id"/>
        <result property="bucketId" column="bucket_id"/>
        <result property="expiredTime" column="expired_time"/>
        <result property="tags" column="tags"/>
        <result property="ownerId" column="owner_id"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="maxCapacity" column="max_capacity"/>
        <result property="personal" column="personal"/>
    </resultMap>

    <sql id="selectJoinDept">
        select distinct m.bucket_id
        from sys_oss_bucket m
                 left join sys_oss_bucket_dept n on m.id = n.bucket_id
    </sql>

    <select id="selectBucketIdsByDeptId" parameterType="java.lang.Long" resultType="java.lang.Long">
        select distinct m.bucket_id
        from sys_oss_bucket m
        left join sys_oss_bucket_dept n on m.id = n.bucket_id
        <where>
            n.dept_id = #{deptId}
        </where>
    </select>
</mapper>
