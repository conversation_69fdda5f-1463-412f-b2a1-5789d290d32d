<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhonghe.system.mapper.SysRobotMsgMapper">

    <resultMap type="com.zhonghe.system.domain.SysRobotMsg" id="SysRobotMsgResult">
        <result property="id" column="id"/>
        <result property="msgType" column="msg_type"/>
        <result property="msgContent" column="msg_content"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="sendMsg" column="send_msg"/>
        <result property="sendResultCode" column="send_result_code"/>
        <result property="msgTitle" column="msg_title"/>
    </resultMap>


</mapper>
