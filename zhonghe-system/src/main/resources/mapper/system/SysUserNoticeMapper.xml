<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhonghe.system.mapper.SysUserNoticeMapper">

    <resultMap type="SysUserNotice" id="SysUserNoticeResult">
        <result property="userNoticeId" column="user_notice_id"/>
        <result property="noticeId" column="notice_id"/>
        <result property="noticeTitle" column="notice_title"/>
        <result property="noticeType" column="notice_type"/>
        <result property="noticeContent" column="notice_content"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="sendTime" column="send_time"/>
    </resultMap>

    <sql id="selectUserNoticeVo">
        select distinct un.user_notice_id
                      , un.notice_title
                      , un.notice_content
                      , un.notice_type
                      , un.status
                      , un.send_time
                      , un.level
                      , un.nick_name
        from sys_user_notice un
                 left join sys_notice_dept d on un.notice_id = d.notice_id
    </sql>

    <sql id="selectUserNotice">
        select distinct sun.user_notice_id
             , sun.notice_title
             , sun.notice_content
             , sun.notice_type
             , sun.status
             , r.stuts
             , sun.send_time
             , sun.level
             , sun.nick_name
        from sys_user_notice sun
                 left join sys_notice_dept d on d.notice_id = sun.notice_id
                 left join read_the_information r on sun.user_notice_id = r.noted_id
    </sql>

    <select id="selectPage" resultMap="SysUserNoticeResult">
        <include refid="selectUserNoticeVo"/>
        ${ew.getCustomSqlSegment}
    </select>

    <select id="selectAllUnReadUserNotices" parameterType="java.lang.Long"
            resultType="com.zhonghe.system.domain.SysUserNotice">
        <include refid="selectUserNotice"/>
        <where>
            r.information_id is null
        </where>
        order by sun.send_time desc , sun.user_notice_id desc
    </select>

    <select id="selectUserNoticeIdAndDeptId" parameterType="java.util.List"
            resultType="com.zhonghe.system.domain.dto.SysUserNoticeDto">
        select n.* , d.dept_id from sys_user_notice n left join sys_notice_dept d on n.notice_id = d.notice_id
        <where>
            1=1 AND n.user_notice_id IN
            <foreach collection="items" close=")" separator="," open="(" item="id">
                #{id}
            </foreach>
        </where>
        order by n.send_time desc, n.user_notice_id desc
    </select>

</mapper>
