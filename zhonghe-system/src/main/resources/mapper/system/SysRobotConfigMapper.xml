<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhonghe.system.mapper.SysRobotConfigMapper">

    <resultMap type="com.zhonghe.system.domain.SysRobotConfig" id="SysRobotConfigResult">
        <result property="id" column="id"/>
        <result property="robotType" column="robot_type"/>
        <result property="webhook" column="webhook"/>
        <result property="remark" column="remark"/>
        <result property="accessToken" column="access_token"/>
        <result property="secret" column="secret"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="name" column="name"/>
    </resultMap>


</mapper>
