<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhonghe.system.mapper.SysNoticeDeptMapper">

    <resultMap type="com.zhonghe.system.domain.SysNoticeDept" id="SysNoticeDeptResult">
        <result property="noticeId" column="notice_id"/>
        <result property="deptId" column="dept_id"/>
    </resultMap>

    <select id="selectDeptsName" parameterType="List" resultType="com.zhonghe.system.domain.vo.SysNoticeDeptVo">
        select ndp.* , d.dept_name as deptName from sys_notice_dept ndp left join sys_dept d on ndp.dept_id = d.dept_id
        <where>
            1 = 1
            <if test="noticeIds.size()>0">
                AND ndp.notice_id IN
                <foreach collection="noticeIds" separator="," open="(" close=")" item="id">
                    #{id}
                </foreach>
            </if>
        </where>
    </select>
</mapper>
