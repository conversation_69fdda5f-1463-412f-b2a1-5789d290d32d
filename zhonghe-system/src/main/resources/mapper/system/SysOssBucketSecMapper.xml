<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhonghe.system.mapper.SysOssBucketSecMapper">
    <resultMap type="com.zhonghe.system.domain.SysOssBucketSec" id="SysOssBucketSecResult">
        <result property="id" column="id"/>
        <result property="bucketId" column="bucket_id"/>
        <result property="bucketName" column="bucket_name"/>
        <result property="bucketType" column="bucket_type"/>
        <result property="minioPath" column="minio_path"/>
        <result property="currentCapacity" column="current_capacity"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>
    <select id="getOssBucketSecById" resultType="com.zhonghe.system.domain.SysOss">
        select sobs.id           as file_id,
               sobs.bucket_id    as bucket_id,
               sob.bucket_name   as file_name,
               sob.bucket_name   as bucket_name,
               sob.original_name as original_name,
               sob.file_suffix   as file_suffix,
               sobs.minio_path   as url
        from sys_oss_bucket_sec sobs
                 left join sys_oss_bucket sob on sobs.bucket_id = sob.id
        where sob.del_flag = '0'
          and sobs.id = #{id}
    </select>
</mapper>
