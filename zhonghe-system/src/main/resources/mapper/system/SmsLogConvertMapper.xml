<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhonghe.system.mapper.SmsLogConvert">


    <update id="convertList" parameterType="com.zhonghe.sms.entity.SmsLogDO">

    </update>

    <select id="convertPage" resultType="com.zhonghe.sms.entity.SmsRequestEbtity"
            parameterType="com.zhonghe.sms.entity.SmsLogDO">

    </select>

    <select id="convertList02" resultType="com.zhonghe.sms.entity.SmsRequestEbtity"
            parameterType="com.zhonghe.sms.entity.SmsLogDO">

    </select>

    <select id="convert" resultType="com.zhonghe.sms.entity.SmsRequestEbtity"
            parameterType="com.zhonghe.sms.entity.SmsLogDO">

    </select>
</mapper>
