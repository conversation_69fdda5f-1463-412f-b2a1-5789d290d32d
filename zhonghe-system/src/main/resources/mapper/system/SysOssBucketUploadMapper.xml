<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhonghe.system.mapper.SysOssBucketUploadMapper">

    <resultMap type="com.zhonghe.system.domain.SysOssBucketUpload" id="SysOssBucketUploadResult">
        <result property="bucketUploadId" column="bucket_upload_id"/>
        <result property="bucketId" column="bucket_id"/>
        <result property="hashcode" column="hashcode"/>
        <result property="totalCount" column="total_count"/>
        <result property="finish" column="finish"/>
        <result property="uploadId" column="upload_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
        <result property="minioKey" column="minio_key"/>
        <result property="minioBucket" column="minio_bucket"/>
        <result property="fileName" column="file_name"/>
    </resultMap>


</mapper>
