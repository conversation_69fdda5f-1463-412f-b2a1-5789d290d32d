<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhonghe.system.mapper.SysOssBucketDeptMapper">

    <resultMap type="com.zhonghe.system.domain.SysOssBucketDept" id="SysOssBucketDeptResult">
        <result property="id" column="id"/>
        <result property="deptId" column="dept_id"/>
        <result property="bucketId" column="bucket_id"/>
    </resultMap>

    <select id="selectPowerByBucketIds" resultType="com.zhonghe.system.domain.vo.SysOssBucketPowerVo">
        select bd.bucket_id ,opd.power,bd.dept_id
        from sys_oss_bucket_dept bd
        left join sys_oss_power_relation sopr
        on bd.id = sopr.bucket_dept_id
        left join sys_oss_power_define opd on opd.id = sopr.power_define_id
        <where>
            opd.power is not null
            <if test="deptId!= null">
                and bd.dept_id = #{deptId}
            </if>
            AND bd.bucket_id in
            <foreach collection="items" item="id" close=")" open="(" separator=",">
                #{id}
            </foreach>
        </where>
    </select>
</mapper>
