<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhonghe.system.mapper.SysMenuMapper">

    <resultMap type="com.zhonghe.common.core.domain.entity.SysMenu" id="SysMenuResult">
        <id property="menuId" column="menu_id"/>
        <result property="menuName" column="menu_name"/>
        <result property="parentName" column="parent_name"/>
        <result property="parentId" column="parent_id"/>
        <result property="orderNum" column="order_num"/>
        <result property="path" column="path"/>
        <result property="component" column="component"/>
        <result property="queryParam" column="query_param"/>
        <result property="isFrame" column="is_frame"/>
        <result property="isCache" column="is_cache"/>
        <result property="menuType" column="menu_type"/>
        <result property="visible" column="visible"/>
        <result property="status" column="status"/>
        <result property="perms" column="perms"/>
        <result property="icon" column="icon"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="remark" column="remark"/>
        <result property="isApp" column="is_app"/>
        <result property="app" column="app"/>
        <result property="appUrl" column="app_url"/>
        <result property="isHome" column="is_home"/>
    </resultMap>

    <sql id="selectColumn">
        m.menu_id,
                        m.parent_id,
                        m.menu_name,
                        m.path,
                        m.component,
                        m.query_param,
                        m.visible,
                        m.status,
                        m.perms,
                        m.page_name,
                        m.is_frame,
                        m.is_cache,
                        m.menu_type,
                        m.icon,
                        m.order_num,
                        m.create_time,
                        m.is_app,
                        m.app,
                        m.app_url,
                        m.is_home
    </sql>

    <select id="insertSysRoleMenuId" resultType="com.zhonghe.common.core.domain.entity.SysMenu">
        SELECT *
        from sys_menu
        WHERE create_time > '2023-03-14'
          and menu_type = 'F'
          and menu_name not in ('新增按钮', '修改按钮')
    </select>

    <select id="selectMenuListByUserId" parameterType="com.zhonghe.common.core.domain.entity.SysMenu"
            resultMap="SysMenuResult">
        select distinct m.menu_id,
                        m.parent_id,
                        m.menu_name,
                        m.component,
                        m.query_param,
                        m.visible,
                        m.status,
                        m.perms,
                        m.is_frame,
                        m.is_cache,
                        m.menu_type,
                        m.icon,
                        m.order_num,
                        m.create_time,
                        m.page_name,
                        m.is_app,
                        m.app_url,
                        m.path
        from sys_menu m
                 left join sys_role_menu rm on m.menu_id = rm.menu_id
                 left join sys_user_role sur on rm.role_id = sur.role_id
                 left join sys_role ro on sur.role_id = ro.role_id
            ${ew.getCustomSqlSegment}
    </select>

    <select id="selectMenuTreeByUserId" parameterType="Long" resultMap="SysMenuResult">
        select distinct m.menu_id,
                        m.parent_id,
                        m.menu_name,
                        m.path,
                        m.component,
                        m.query_param,
                        m.visible,
                        m.status,
                        m.perms,
                        m.page_name,
                        m.is_frame,
                        m.is_cache,
                        m.menu_type,
                        m.icon,
                        m.order_num,
                        m.create_time
        from sys_menu m
                 left join public.sys_role_menu rm on m.menu_id = rm.menu_id
                 left join public.sys_user_role sur on rm.role_id = sur.role_id
                 left join public.sys_role ro on sur.role_id = ro.role_id
                 left join public.sys_user u on sur.user_id = u.user_id
        where u.user_id = #{userId}
          and m.menu_type in ('M', 'C')
          and m.status = '0'
          and ro.status = '0'
        order by m.parent_id, m.order_num
    </select>

    <select id="selectMenuListByRoleId" resultType="Long">
        select m.menu_id
        from sys_menu m
        left join sys_role_menu rm on m.menu_id = rm.menu_id
        where rm.role_id = #{roleId}
        <if test="menuCheckStrictly">
            and m.menu_id not in (select m.parent_id from sys_menu m inner join sys_role_menu rm on m.menu_id =
            rm.menu_id and rm.role_id = #{roleId})
        </if>
        order by m.parent_id, m.order_num
    </select>

    <select id="selectSysMenuByRoleId" resultType="SysMenu">
        select distinct m.menu_id,
                        m.parent_id,
                        m.menu_name,
                        m.path,
                        m.is_home,
                        m.component,
                        m.query_param,
                        m.visible,
                        m.status,
                        m.perms,
                        m.page_name,
                        m.is_frame,
                        m.is_cache,
                        m.menu_type,
                        m.icon,
                        m.order_num,
                        m.create_time
        from sys_menu m
                 left join sys_role_menu rm on m.menu_id = rm.menu_id
        where rm.role_id = #{roleId}
        order by m.parent_id, m.order_num
    </select>

    <select id="selectMenuPerms" resultType="String">
        select distinct m.perms
        from sys_menu m
                 left join sys_role_menu rm on m.menu_id = rm.menu_id
                 left join sys_user_role sur on rm.role_id = sur.role_id
    </select>

    <select id="selectMenuPermsByUserId" parameterType="long" resultType="java.lang.String">
        select distinct m.perms
        from sys_menu m
                 left join sys_role_menu rm on m.menu_id = rm.menu_id
                 left join sys_user_role sur on rm.role_id = sur.role_id
                 left join sys_role r on r.role_id = sur.role_id
        where m.status = '0'
          and r.status = '0'
          and sur.user_id = #{userId}
    </select>

    <select id="selectFastId" resultType="java.lang.Long">
        SELECT menu_id
        from sys_menu
        order by create_time DESC
        LIMIT 1
    </select>

    <select id="selectOneMenuByIsHome" resultMap="SysMenuResult">
        SELECT
        <include refid="com.zhonghe.system.mapper.SysMenuMapper.selectColumn"></include>
        from sys_menu m where m.status = '0' and m.is_home = #{isHome} and m.menu_type != 'F'
    </select>

</mapper>
