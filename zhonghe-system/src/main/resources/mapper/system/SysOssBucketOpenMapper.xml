<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhonghe.system.mapper.SysOssBucketOpenMapper">

    <resultMap type="com.zhonghe.system.domain.SysOssBucketOpen" id="SysOssBucketOpenResult">
        <result property="id" column="id"/>
        <result property="bucketId" column="bucket_id"/>
        <result property="expiredTime" column="expired_time"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>

    <select id="selectBucketOpen" resultType="com.zhonghe.system.domain.vo.SysOssBucketSecVo" parameterType="long">
        select sobs.*,sb.nick_name,sobo.expired_time from sys_oss_bucket_sec sobs
        inner join sys_oss_bucket_open sobo on sobs.bucket_id = sobo.bucket_id
        inner join sys_oss_bucket sb on sobs.bucket_id = sb.bucket_id
        <where>
            sb.bucket_id = #{bucketId}
        </where>
    </select>


</mapper>
