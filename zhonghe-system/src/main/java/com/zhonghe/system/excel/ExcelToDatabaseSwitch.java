package com.zhonghe.system.excel;

import com.zhonghe.common.core.domain.BaseEntity;
import com.zhonghe.system.excel.service.ILoadExcelService;
import lombok.Getter;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @description:
 * @author: cq
 * @date: 2023/6/14 14:52
 */

public class ExcelToDatabaseSwitch {

    public static Map<String, ExcelToDatabaseSwitch> switchers = new ConcurrentHashMap<>();
    /**
     *
     */
    @Getter
    private Class<? extends ILoadExcelService> loadExcelService;

    @Getter
    private Class<?> clazz;

    @Getter
    private String name;

    public ExcelToDatabaseSwitch(String name, Class<? extends BaseEntity> clazz, Class<? extends ILoadExcelService> loadExcelService) {
        this.name = name;
        this.clazz = clazz;
        this.loadExcelService = loadExcelService;
    }

    public static void register(ExcelToDatabaseSwitch switcher) {
        if (!switchers.containsKey(switcher.getName())) {
            switchers.put(switcher.getName(), switcher);
        }
    }

    public static void register(String path, Class<? extends BaseEntity> clazz, Class<? extends ILoadExcelService> loadExcelService) {
        if (!switchers.containsKey(path)) {
            switchers.put(path, new ExcelToDatabaseSwitch(path, clazz, loadExcelService));
        }
    }

    public static ExcelToDatabaseSwitch get(String type) {
        return switchers.get(type);
    }
}
