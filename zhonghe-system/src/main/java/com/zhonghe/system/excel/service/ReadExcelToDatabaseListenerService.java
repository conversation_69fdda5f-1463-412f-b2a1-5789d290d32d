package com.zhonghe.system.excel.service;

import cn.hutool.core.util.ObjectUtil;
import com.zhonghe.common.utils.spring.SpringUtils;
import com.zhonghe.system.excel.event.ReadExcelDataEvent;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.util.List;
import java.util.Map;

/**
 * @description:
 * @author: cq
 * @date: 2023/7/28 9:51
 */

@Component
@Slf4j
@AllArgsConstructor
public class ReadExcelToDatabaseListenerService {

    /**
     * 处理数据的逻辑
     *
     * @param event ，包含excel数据对象列表
     */
    @EventListener
    @Async
    public void handler(ReadExcelDataEvent event) throws ParseException {
        List<?> result = event.getResult();
        Class<? extends ILoadExcelService> clazz = event.getType().getLoadExcelService();
        ILoadExcelService excelService = SpringUtils.getBean(clazz);
        if (ObjectUtil.isNotNull(excelService)) {
            excelService.load(result);
        } else {
            log.error("没有找到对应的bean：{}", event.getType());
        }
    }

    public void init() {
        Map<String, ILoadExcelService> beans = SpringUtils.getBeansOfType(ILoadExcelService.class);
        beans.values().forEach(ILoadExcelService::register);
    }
}

