package com.zhonghe.system.excel.event;

import com.zhonghe.system.excel.ExcelToDatabaseSwitch;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.util.List;

/**
 * @description:
 * @author: cq
 * @date: 2023/6/14 15:49
 */

public class ReadExcelDataEvent extends ApplicationEvent {

    @Getter
    private final List<?> result;

    @Getter
    private final ExcelToDatabaseSwitch type;

    public ReadExcelDataEvent(Object source, ExcelToDatabaseSwitch type, List<?> result) {
        super(source);
        this.result = result;
        this.type = type;
    }
}

