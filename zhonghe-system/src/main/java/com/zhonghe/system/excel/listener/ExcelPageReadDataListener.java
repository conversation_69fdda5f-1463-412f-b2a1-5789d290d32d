package com.zhonghe.system.excel.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.zhonghe.common.core.domain.BaseEntity;
import com.zhonghe.common.helper.LoginHelper;
import com.zhonghe.common.utils.spring.SpringUtils;
import com.zhonghe.system.excel.ExcelToDatabaseSwitch;
import com.zhonghe.system.excel.event.ReadExcelDataEvent;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * @description:
 * @author: cq
 * @date: 2023/6/14 15:40
 */

@Slf4j
public class ExcelPageReadDataListener<T extends BaseEntity> implements ReadListener<T> {

    private final List<T> result = new ArrayList<>();

    private final ExcelToDatabaseSwitch type;

    public ExcelPageReadDataListener(ExcelToDatabaseSwitch type) {
        this.type = type;
    }

    @Override
    public void invoke(T data, AnalysisContext context) {
        data.setCreateBy(LoginHelper.getUsername());
        data.setUpdateBy(LoginHelper.getUsername());
        result.add(data);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        SpringUtils.publishEvent(new ReadExcelDataEvent(this, type, result));
    }
}

