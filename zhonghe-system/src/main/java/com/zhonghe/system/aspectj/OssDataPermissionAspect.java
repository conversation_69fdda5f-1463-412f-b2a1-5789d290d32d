package com.zhonghe.system.aspectj;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zhonghe.common.annotation.OssDataPermission;
import com.zhonghe.common.exception.oss.SysOssException;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.oss.constant.OssConstant;
import com.zhonghe.system.domain.SysOssBucketSec;
import com.zhonghe.system.domain.bo.SysOssBucketBo;
import com.zhonghe.system.domain.vo.SysOssBucketSecVo;
import com.zhonghe.system.mapper.SysOssBucketSecMapper;
import com.zhonghe.system.service.ISysOssBucketService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

/**
 * @description: OSS服务类的自定义权限判定
 * @author: cq
 * @date: 2023/4/4 10:18
 */

@Slf4j
@RequiredArgsConstructor
@Aspect
@Component
public class OssDataPermissionAspect {

    private final SysOssBucketSecMapper bucketSecMapper;
    private final ISysOssBucketService sysOssBucketService;


    @Around("@annotation(oss)")
    public Object around(ProceedingJoinPoint joinPoint, OssDataPermission oss) throws Throwable {

        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        String methodName = signature.getMethod().getName();
        Object[] args = joinPoint.getArgs();
        Set<Long> bucketIds = new HashSet<>();
        switch (methodName) {
            case "add":
                SysOssBucketBo bo = (SysOssBucketBo) args[0];
                if (bo != null) {
                    if (OssConstant.BUCKET_TYPE_FOLD == bo.getBucketType()) {
                        bucketIds.add(bo.getParentId());
                    }
                }
                break;
            case "nextTree":
            case "upload":
            case "share":
            case "listNodePowers":
                Long id = (Long) args[0];
                if (id != null) {
                    bucketIds.add(id);
                }
                break;
            case "remove":
                Long[] ids = (Long[]) args[0];
                if (ObjectUtil.isNotNull(ids)) {
                    bucketIds.addAll(Arrays.asList(ids));
                }
                break;
            case "edit":
            case "power":
                SysOssBucketBo modify = (SysOssBucketBo) args[0];
                if (modify != null) {
                    bucketIds.add(modify.getId());
                }
                break;
            case "preViewFileFromHome":
                String param = (String) args[0];
                SysOssBucketSecVo bucketOpenVo = bucketSecMapper.selectVoOne(Wrappers.<SysOssBucketSec>lambdaQuery().eq(SysOssBucketSec::getId, Long.parseLong(StringUtils.decrypt(param))));
                if (bucketOpenVo != null) {
                    bucketIds.add(bucketOpenVo.getBucketId());
                }
                break;
        }
        if (CollectionUtils.isNotEmpty(bucketIds) && !sysOssBucketService.checkPermission(bucketIds, oss.has())) {
            throw new SysOssException("sys.oss.power.error", "");
        }

        return joinPoint.proceed();
    }
}

