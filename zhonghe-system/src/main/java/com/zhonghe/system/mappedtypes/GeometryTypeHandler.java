package com.zhonghe.system.mappedtypes;

import net.postgis.jdbc.PGgeometry;
import net.postgis.jdbc.geometry.Geometry;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedTypes;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * @description:
 * @author: cq
 * @date: 2023/8/9 11:40
 */

@MappedTypes(Geometry.class)
public class GeometryTypeHandler extends BaseTypeHandler<Geometry> {

    @Override
    public void setNonNullParameter(PreparedStatement preparedStatement, int i, Geometry geometry, JdbcType jdbcType) throws SQLException {
        PGgeometry pgGeometry = new PGgeometry(geometry);
        preparedStatement.setObject(i, pgGeometry);
    }

    @Override
    public Geometry getNullableResult(ResultSet resultSet, String s) throws SQLException {
        PGgeometry pgGeometry = (PGgeometry) resultSet.getObject(s);
        if (pgGeometry == null) {
            return null;
        }
        return pgGeometry.getGeometry();
    }

    @Override
    public Geometry getNullableResult(ResultSet resultSet, int i) throws SQLException {
        PGgeometry pgGeometry = (PGgeometry) resultSet.getObject(i);
        if (pgGeometry == null) {
            return null;
        }
        return pgGeometry.getGeometry();
    }

    @Override
    public Geometry getNullableResult(CallableStatement callableStatement, int i) throws SQLException {
        PGgeometry pgGeometry = (PGgeometry) callableStatement.getObject(i);
        if (pgGeometry == null) {
            return null;
        }
        return pgGeometry.getGeometry();
    }
}

