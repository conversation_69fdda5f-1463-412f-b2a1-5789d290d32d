package com.zhonghe.system.runner;

import com.zhonghe.common.config.RuoYiConfig;
import com.zhonghe.system.excel.service.ReadExcelToDatabaseListenerService;
import com.zhonghe.system.service.ISysConfigService;
import com.zhonghe.system.service.ISysDictTypeService;
import com.zhonghe.system.service.ISysMqttService;
import com.zhonghe.system.service.ISysOssConfigService;
import com.zhonghe.system.service.ISysOssPowerDefineService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

/**
 * 初始化 system 模块对应业务数据
 *
 * <AUTHOR> Li
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class SystemApplicationRunner implements ApplicationRunner {

    private final RuoYiConfig ruoyiConfig;
    private final ISysConfigService configService;
    private final ISysDictTypeService dictTypeService;
    private final ISysOssConfigService ossConfigService;
    private final ISysOssPowerDefineService ossPowerDefineService;
    private final ISysMqttService mqttService;
    private final ReadExcelToDatabaseListenerService excelToDatabaseListenerService;
    @Override
    public void run(ApplicationArguments args) throws Exception {
        ossConfigService.init();
        log.info("初始化OSS配置成功");
        if (ruoyiConfig.isCacheLazy()) {
            return;
        }
        configService.loadingConfigCache();
        log.info("加载参数缓存数据成功");
        dictTypeService.loadingDictCache();
        log.info("加载字典缓存数据成功");
        mqttService.initMqtt();
        log.info("链接mqtt服务器成功");
        ossPowerDefineService.init();
        log.info("加载OSS权限配置成功");
        excelToDatabaseListenerService.init();
        log.info("加载Excel导入功能");
    }
}
