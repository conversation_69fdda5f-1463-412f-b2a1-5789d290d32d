package com.zhonghe.system.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.zhonghe.common.annotation.DataColumn;
import com.zhonghe.common.annotation.DataPermission;
import com.zhonghe.common.core.mapper.BaseMapperPlus;
import com.zhonghe.system.domain.SysUserNotice;
import com.zhonghe.system.domain.dto.SysUserNoticeDto;
import com.zhonghe.system.domain.vo.SysUserNoticeVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 通知公告表 数据层
 *
 * <AUTHOR> Li
 */
public interface SysUserNoticeMapper extends BaseMapperPlus<SysUserNoticeMapper, SysUserNotice, SysUserNoticeVo> {

    IPage<SysUserNotice> selectAllList(IPage<SysUserNotice>  page);


    @Override
    @DataPermission({
        @DataColumn(key = "deptName", value = "d.dept_id")
    })
    <P extends IPage<SysUserNotice>> P selectPage(P page, @Param(Constants.WRAPPER) Wrapper<SysUserNotice> queryWrapper);

    @DataPermission({
        @DataColumn(key = "deptName", value = "d.dept_id")
    })
    List<SysUserNotice> selectAllUnReadUserNotices();

    List<SysUserNoticeDto> selectUserNoticeIdAndDeptId(@Param("items") List<Long> userNoticeIds);
}
