package com.zhonghe.system.mapper;

import com.zhonghe.common.core.mapper.BaseMapperPlus;
import com.zhonghe.system.domain.SysOssBucket;
import com.zhonghe.system.domain.vo.SysOssBucketVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 对象存储桶Mapper接口
 *
 * <AUTHOR>
 * @date 2023-03-31
 */
public interface SysOssBucketMapper extends BaseMapperPlus<SysOssBucketMapper, SysOssBucket, SysOssBucketVo> {

    List<Long> selectBucketIdsByDeptId(@Param("deptId") Long deptId);
}
