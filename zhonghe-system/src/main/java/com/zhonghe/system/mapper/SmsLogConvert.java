package com.zhonghe.system.mapper;

import cn.hutool.db.PageResult;
import com.zhonghe.common.core.mapper.BaseMapperPlus;
import com.zhonghe.sms.entity.SmsLogDO;
import com.zhonghe.sms.entity.SmsRequestEbtity;

import java.util.List;

/**
 * 短信日志 Convert
 *
 * <AUTHOR>
 */

public interface SmsLogConvert  extends BaseMapperPlus<SmsLogConvert, SmsLogDO, SmsLogDO> {


    SmsRequestEbtity convert(SmsLogDO bean);

    List<SmsRequestEbtity> convertList(List<SmsLogDO> list);

    PageResult<SmsRequestEbtity> convertPage(PageResult<SmsLogDO> page);

    List<SmsRequestEbtity> convertList02(List<SmsLogDO> list);

}
