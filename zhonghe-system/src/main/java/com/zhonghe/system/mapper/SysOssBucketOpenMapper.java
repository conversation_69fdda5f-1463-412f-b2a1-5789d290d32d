package com.zhonghe.system.mapper;


import com.zhonghe.common.core.mapper.BaseMapperPlus;
import com.zhonghe.system.domain.SysOssBucketOpen;
import com.zhonghe.system.domain.vo.SysOssBucketOpenVo;
import com.zhonghe.system.domain.vo.SysOssBucketSecVo;

/**
 * 存储文件开放表Mapper接口
 *
 * <AUTHOR>
 * @date 2023-04-03
 */
public interface SysOssBucketOpenMapper extends BaseMapperPlus<SysOssBucketOpenMapper, SysOssBucketOpen, SysOssBucketOpenVo> {

    SysOssBucketSecVo selectBucketOpen(Long bucketId);
}
