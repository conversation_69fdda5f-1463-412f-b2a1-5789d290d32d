package com.zhonghe.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zhonghe.common.core.domain.entity.GisServiceRule;
import com.zhonghe.common.core.domain.entity.ServiceRelevGraph;

import java.util.List;


public interface CoordinteMapper extends BaseMapper {


    List<String> getCoordinate();

    Long selectFastid();

    List<GisServiceRule> serviceRuleLise(Long ruleId);

    void ftests();

    void ftest();

    void updateRolueById(Long authorityId);

    void updateButhParamScope(List<GisServiceRule> gisService1);

    void updateParamsById(Long ruleId);

    List<ServiceRelevGraph> selectGraphById(Long ruleId);

    void deleteBatchRelevIds(List<Long> collect);

    void saveButhRelevEntity(List<ServiceRelevGraph> paramsList);

}
