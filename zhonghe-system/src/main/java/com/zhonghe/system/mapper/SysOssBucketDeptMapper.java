package com.zhonghe.system.mapper;

import com.zhonghe.common.core.mapper.BaseMapperPlus;
import com.zhonghe.system.domain.SysOssBucketDept;
import com.zhonghe.system.domain.vo.SysOssBucketDeptVo;
import com.zhonghe.system.domain.vo.SysOssBucketPowerVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * 部门存储桶Mapper接口
 *
 * <AUTHOR>
 * @date 2023-03-31
 */
public interface SysOssBucketDeptMapper extends BaseMapperPlus<SysOssBucketDeptMapper, SysOssBucketDept, SysOssBucketDeptVo> {

    List<SysOssBucketPowerVo> selectPowerByBucketIds(@Param("items") Set<Long> ids, @Param("deptId") Long deptId);
}
