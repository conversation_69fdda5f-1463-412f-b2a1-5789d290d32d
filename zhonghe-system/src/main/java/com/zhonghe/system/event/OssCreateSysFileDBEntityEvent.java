package com.zhonghe.system.event;

import com.zhonghe.system.domain.SysOss;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * @description:
 * @author: cq
 * @date: 2023/4/3 15:47
 */

public class OssCreateSysFileDBEntityEvent extends ApplicationEvent {

    @Getter
    private final Long parentId;

    @Getter
    private final SysOss oss;
    public OssCreateSysFileDBEntityEvent(Object source, Long parentId , SysOss oss) {
        super(source);
        this.parentId = parentId;
        this.oss = oss;
    }
}

