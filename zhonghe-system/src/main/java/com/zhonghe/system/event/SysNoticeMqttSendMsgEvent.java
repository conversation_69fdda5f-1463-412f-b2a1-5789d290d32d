package com.zhonghe.system.event;

import com.zhonghe.common.core.domain.dto.BaseMqttMsgDTO;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.util.List;

/**
 * @description:
 * @author: cq
 * @date: 2023/3/29 17:46
 */

public class SysNoticeMqttSendMsgEvent extends ApplicationEvent {

    @Getter
    private final List<BaseMqttMsgDTO> msgs;

    public SysNoticeMqttSendMsgEvent(Object source ,  List<BaseMqttMsgDTO> msgs) {
        super(source);
        this.msgs = msgs;
    }
}

