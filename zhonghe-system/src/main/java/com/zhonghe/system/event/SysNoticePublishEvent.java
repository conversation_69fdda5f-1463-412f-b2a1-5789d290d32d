package com.zhonghe.system.event;

import com.zhonghe.system.domain.SysNotice;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.util.List;

/**
 * @description: 发布消息需要分开处理，有一部分是立即发送，有一部分是延迟发送
 * @author: cq
 * @date: 2023/3/23 11:23
 */

public class SysNoticePublishEvent extends ApplicationEvent {

    @Getter
    private final List<SysNotice> notices;

    public SysNoticePublishEvent(Object source, List<SysNotice> notices) {
        super(source);
        this.notices = notices;
    }
}

