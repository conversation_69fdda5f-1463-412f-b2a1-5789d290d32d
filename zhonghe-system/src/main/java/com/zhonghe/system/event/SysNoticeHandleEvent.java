package com.zhonghe.system.event;
import lombok.Getter;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.springframework.context.ApplicationEvent;

import java.util.List;

/**
 * @Author: lpg
 * @Date: 2023/03/27/18:42
 * @Description:
 */
public class SysNoticeHandleEvent extends ApplicationEvent {

    @Getter
    private MqttMessage message;

    @Getter
    private String topic;

    public SysNoticeHandleEvent(Object source, String topic,MqttMessage message) {
        super(source);
        this.topic = topic;
        this.message = message;
    }
}
