package com.zhonghe.system.event;

import com.zhonghe.system.domain.SysNotice;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.util.List;


/**
 * @description:
 * @author: shao
 * @date: 2023/4/4
 */

public class SysRobotSendEvent extends ApplicationEvent {

    @Getter
    List<SysNotice> sysNotice;
    @Getter
    Long deptId;

    public SysRobotSendEvent(Object source,Long deptId, List<SysNotice> sysNotice) {
        super(source);
        this.deptId = deptId;
        this.sysNotice = sysNotice;
    }
}
