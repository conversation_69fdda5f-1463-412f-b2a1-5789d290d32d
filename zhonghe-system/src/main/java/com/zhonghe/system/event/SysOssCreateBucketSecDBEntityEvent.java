package com.zhonghe.system.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * @description:
 * @author: cq
 * @date: 2023/4/3 15:58
 */

public class SysOssCreateBucketSecDBEntityEvent extends ApplicationEvent {
    @Getter
    private final Long bucketId;

    @Getter
    private final String bucketName;

    @Getter
    private final String url;

    @Getter
    private final boolean create;

    public SysOssCreateBucketSecDBEntityEvent(Object source, Long bucketId, String bucketName , String url, boolean createBucket) {
        super(source);
        this.bucketId = bucketId;
        this.bucketName = bucketName;
        this.url = url;
        this.create = createBucket;
    }
}

