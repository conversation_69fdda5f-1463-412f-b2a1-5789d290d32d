package com.zhonghe.system.event;

import lombok.Getter;

/**
 * @description: 用户机构更新也需要更新权限
 * @author: cq
 * @date: 2023/3/20 11:29
 */

public class SysUserInformationUpdateEvent extends AbstractSysPermissionEvent {

    @Getter
    private final Long userId;
    @Getter
    private final Long deptId;
    public SysUserInformationUpdateEvent(Object source, Long userId , Long deptId) {
        super(source);
        this.userId = userId;
        this.deptId = deptId;
    }
}

