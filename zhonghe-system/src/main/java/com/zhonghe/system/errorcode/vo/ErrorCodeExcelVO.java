package com.zhonghe.system.errorcode.vo;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zhonghe.common.annotation.ExcelDictFormat;
import com.zhonghe.common.convert.ExcelDictConvert;
import lombok.Data;

import java.util.Date;

//import cn.iocoder.yudao.adminserver.modules.infra.enums.InfDictTypeConstants;

/**
 * 错误码 Excel VO
 *
 * <AUTHOR>
 */
@Data
public class ErrorCodeExcelVO {

    @ExcelIgnore//不将该字段转换成Excel
    private Long id;


//    @ExcelDictFormat("sys_normal_disable")
    @ExcelDictFormat(readConverterExp = "1=自动生成,2=手动编辑")
    @ExcelProperty(value = "错误码类型", converter = ExcelDictConvert.class)
    private Integer type;

    @ExcelProperty("应用名")
    private String applicationName;

    @ExcelProperty("错误码编码")
    private Integer code;

    @ExcelProperty("错误码错误提示")
    private String message;

    @ExcelProperty("备注")
    private String memo;

    @ExcelProperty("创建时间")
    private Date createTime;

}
