package com.zhonghe.system.errorcode;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaMode;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zhonghe.common.annotation.Log;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.common.enums.BusinessType;
import com.zhonghe.common.utils.poi.ExcelUtil;
import com.zhonghe.system.errorcode.dto.ErrorCodeDO;
import com.zhonghe.system.errorcode.vo.ErrorCodeCreateReqVO;
import com.zhonghe.system.errorcode.vo.ErrorCodeExcelVO;
import com.zhonghe.system.errorcode.vo.ErrorCodeExportReqVO;
import com.zhonghe.system.errorcode.vo.ErrorCodePageReqVO;
import com.zhonghe.system.errorcode.vo.ErrorCodeRespVO;
import com.zhonghe.system.errorcode.vo.ErrorCodeUpdateReqVO;
import com.zhonghe.system.service.ErrorCodeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;


@Api(tags = "管理后台 - 错误码")
@RestController
@RequestMapping("/com/zhonghe/system/error-code")
@Validated
public class ErrorCodeController {

    @Resource
    private ErrorCodeService errorCodeService;

    @PostMapping("/create")
    @ApiOperation("创建错误码")
    @SaCheckPermission("system:monitor:errorcode:index:add")
    public R<Long> createErrorCode(@Valid @RequestBody ErrorCodeCreateReqVO createReqVO) {
        return R.ok(errorCodeService.createErrorCode(createReqVO));
    }

    @PutMapping("/update")
    @ApiOperation("更新错误码")
    @SaCheckPermission("system:monitor:errorcode:index:update")
    public R<Boolean> updateErrorCode(@Valid @RequestBody ErrorCodeUpdateReqVO updateReqVO) {
        errorCodeService.updateErrorCode(updateReqVO);
        return R.ok(true);
    }

    @DeleteMapping("/delete")
    @ApiOperation("删除错误码")
    @SaCheckPermission("system:monitor:errorcode:index:delete")
    @ApiImplicitParam(name = "id", value = "编号", required = true, dataTypeClass = Long.class)
    public R<Boolean> deleteErrorCode(@RequestParam("ids") Long[] ids) {
        errorCodeService.deleteErrorCode(ids);
        return R.ok(true);
    }

    @GetMapping("/get")
    @SaCheckPermission( value = {"system:monitor:errorcode:index:query","system:monitor:errorcode:index:list"},mode = SaMode.OR)
    @ApiOperation("获得错误码")
    @ApiImplicitParam(name = "id", value = "编号", required = true, example = "1024", dataTypeClass = Long.class)
    public R<ErrorCodeRespVO> getErrorCode(@RequestParam("id") Long id) {
        ErrorCodeDO errorCode = errorCodeService.getErrorCode(id);
        ErrorCodeRespVO r = new ErrorCodeRespVO();
        r.setCode(errorCode.getCode()+"");
        r.setType(errorCode.getType());
        r.setApplicationName(errorCode.getApplicationName());
        r.setMessage(errorCode.getMessage());
        r.setMemo(errorCode.getMemo());
        return R.ok(r);
    }

    @GetMapping("/page")
    @ApiOperation("获得错误码分页")
    @SaCheckPermission( value = {"system:monitor:errorcode:index:query","system:monitor:errorcode:index:list"},mode = SaMode.OR)
    public R<IPage<ErrorCodeRespVO>> getErrorCodePage(@Valid ErrorCodePageReqVO pageVO) {
//        if(StringUtils.isNotBlank(code)) return R.fail("错误码只能为数字");

        IPage<ErrorCodeRespVO> errorCodePage = errorCodeService.getErrorCodePage(pageVO);
        return R.ok(errorCodePage);
    }

    @PostMapping("/export-excel")
    @ApiOperation("导出错误码 Excel")
    @Log(title = "导出错误码", businessType = BusinessType.EXPORT)
    @SaCheckPermission("system:monitor:errorcode:index:export")
    public void exportErrorCodeExcel(@Valid ErrorCodeExportReqVO exportReqVO,
              HttpServletResponse response) throws IOException {
        List<ErrorCodeDO> list = errorCodeService.getErrorCodeList(exportReqVO);
        // 导出 Excel
        List<ErrorCodeExcelVO> datas = new ArrayList<>();

        list.stream().forEach( f -> {
            ErrorCodeExcelVO r = new ErrorCodeExcelVO();
            r.setCode(f.getCode());
            r.setType(f.getType());
            r.setApplicationName(f.getApplicationName());
            r.setMessage(f.getMessage());
            r.setMemo(f.getMemo());
            r.setCreateTime(f.getCreateTime());
            datas.add(r);
        });
        ExcelUtil.exportExcel(datas, "导出错误码", ErrorCodeExcelVO.class, response);
    }

}
