package com.zhonghe.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.system.domain.SysNotice;
import com.zhonghe.system.domain.SysUserNotice;

import java.util.List;

/**
 * 公告 服务层
 *
 * <AUTHOR> Li
 */
public interface ISysUserNoticeService {


    TableDataInfo<SysUserNotice> selectPageNoticeList(SysUserNotice notice, PageQuery pageQuery);

    IPage<SysUserNotice> selectList(SysUserNotice notice, PageQuery pageQuery);

    /**
     * 根据消息模板新增用户消息
     * @param notices
     */
    void createAndNotifyUserNotice(List<SysNotice> notices);

    List<SysUserNotice> selectAllUnRead();

    /**
     * 读消息
     * @param userNoticeIds
     * @return
     */
    int readNotices(List<Long> userNoticeIds);

    void insertBatch(List<SysUserNotice> sysUserNotices);

    SysUserNotice selectByNoticeId(Long id);

    SysUserNotice selectByUserNoticeId(Long userNoticeId);

    Long selectAllUnReadCount();
}
