package com.zhonghe.system.service;

import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.system.domain.SysRobotRelevanceDepart;
import com.zhonghe.system.domain.bo.SysRobotRelevanceDepartBo;
import com.zhonghe.system.domain.vo.SysRobotRelevanceDepartVo;

import java.util.Collection;
import java.util.List;

/**
 * 机器人配置关联部门Service接口
 *
 * <AUTHOR>
 * @date 2023-04-04
 */
public interface ISysRobotRelevanceDepartService {

    /**
     * 查询机器人配置关联部门
     */
    SysRobotRelevanceDepartVo queryById(Long id);


    /**
     * 查询机器人配置关联部门列表
     */
    List<SysRobotRelevanceDepartVo> queryList(SysRobotRelevanceDepartBo bo);

    /**
     * 修改机器人配置关联部门
     */
    Boolean insertByBo(SysRobotRelevanceDepartBo bo);

    /**
     * 修改机器人配置关联部门
     */
    Boolean updateByBo(SysRobotRelevanceDepartBo bo);

    /**
     * 校验并批量删除机器人配置关联部门信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    TableDataInfo<SysRobotRelevanceDepart> selectPageRoleList(SysRobotRelevanceDepart robotRelevanceDepart, PageQuery pageQuery);

}
