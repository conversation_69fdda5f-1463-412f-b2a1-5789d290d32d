package com.zhonghe.system.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.zhonghe.common.exception.ServiceException;
import com.zhonghe.system.excel.ExcelToDatabaseSwitch;
import com.zhonghe.system.excel.listener.ExcelPageReadDataListener;
import com.zhonghe.system.service.IUploadExcelToDataService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;

/**
 * @description:
 * @author: cq
 * @date: 2023/7/26 10:49
 */

@Service
@Slf4j
@RequiredArgsConstructor
public class UploadExcelToDataServiceImpl implements IUploadExcelToDataService {

    @Override
    public void upload(String type, MultipartFile file) {
        ExcelToDatabaseSwitch switcher = ExcelToDatabaseSwitch.get(type);
        if (ObjectUtil.isNotNull(switcher)) {
            try (InputStream inputStream = file.getInputStream()) {
                EasyExcel.read(inputStream, switcher.getClazz(), new ExcelPageReadDataListener<>(switcher)).sheet().doRead();
            } catch (Exception e) {
                log.error("读取文件异常：{}", e.getMessage());
                throw new ServiceException(e.getMessage());
            }
        }
    }

}

