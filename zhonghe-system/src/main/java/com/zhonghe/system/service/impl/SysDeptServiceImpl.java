package com.zhonghe.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zhonghe.common.constant.UserConstants;
import com.zhonghe.common.core.domain.entity.SysDept;
import com.zhonghe.common.core.domain.entity.SysRole;
import com.zhonghe.common.core.domain.entity.SysUser;
import com.zhonghe.common.exception.ServiceException;
import com.zhonghe.common.helper.DataBaseHelper;
import com.zhonghe.common.helper.LoginHelper;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.common.utils.TreeBuildUtils;
import com.zhonghe.system.domain.SysNoticeDept;
import com.zhonghe.system.domain.SysOssBucketDept;
import com.zhonghe.system.domain.SysOssPowerRelation;
import com.zhonghe.system.domain.SysRobotRelevanceDepart;
import com.zhonghe.system.domain.vo.SysOssBucketDeptVo;
import com.zhonghe.system.mapper.SysDeptMapper;
import com.zhonghe.system.mapper.SysNoticeDeptMapper;
import com.zhonghe.system.mapper.SysOssBucketDeptMapper;
import com.zhonghe.system.mapper.SysOssPowerRelationMapper;
import com.zhonghe.system.mapper.SysRobotRelevanceDepartMapper;
import com.zhonghe.system.mapper.SysRoleMapper;
import com.zhonghe.system.mapper.SysUserMapper;
import com.zhonghe.system.service.ISysDeptService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 部门管理 服务实现
 *
 * <AUTHOR> Li
 */
@RequiredArgsConstructor
@Service
public class SysDeptServiceImpl implements ISysDeptService {

    private final SysDeptMapper baseMapper;
    private final SysRoleMapper roleMapper;
    private final SysUserMapper userMapper;
    private final SysNoticeDeptMapper sysNoticeDeptMapper;
    private final SysRobotRelevanceDepartMapper sysRobotRelevanceDepartMapper;
    private final SysOssBucketDeptMapper sysOssBucketDeptMapper;
    private final SysOssPowerRelationMapper sysOssPowerRelationMapper;

    /**
     * 查询部门管理数据
     *
     * @param dept 部门信息
     * @return 部门信息集合
     */
    @Override
    public List<SysDept> selectDeptList(SysDept dept) {
        LambdaQueryWrapper<SysDept> lqw = new LambdaQueryWrapper<>();
        lqw.eq(SysDept::getDelFlag, "0")
            .eq(ObjectUtil.isNotNull(dept.getDeptId()), SysDept::getDeptId, dept.getDeptId())
            .eq(ObjectUtil.isNotNull(dept.getParentId()), SysDept::getParentId, dept.getParentId())
            .like(StringUtils.isNotBlank(dept.getDeptName()), SysDept::getDeptName, dept.getDeptName())
            .eq(StringUtils.isNotBlank(dept.getStatus()), SysDept::getStatus, dept.getStatus())
            .orderByAsc(SysDept::getParentId)
            .orderByAsc(SysDept::getOrderNum);
        return baseMapper.selectDeptList(lqw);
    }

    /**
     * 构建前端所需要下拉树结构
     *
     * @param depts 部门列表
     * @return 下拉树结构列表
     */
    @Override
    public List<Tree<Long>> buildDeptTreeSelect(List<SysDept> depts) {
        if (CollUtil.isEmpty(depts)) {
            return CollUtil.newArrayList();
        }
        return TreeBuildUtils.build(depts, (dept, tree) ->
            tree.setId(dept.getDeptId())
                .setParentId(dept.getParentId())
                .setName(dept.getDeptName())
                .setWeight(dept.getOrderNum()));
    }

    /**
     * 根据角色ID查询部门树信息
     *
     * @param roleId 角色ID
     * @return 选中部门列表
     */
    @Override
    public List<Long> selectDeptListByRoleId(Long roleId) {
        SysRole role = roleMapper.selectById(roleId);
        return baseMapper.selectDeptListByRoleId(roleId, role.getDeptCheckStrictly());
    }

    /**
     * 根据部门ID查询信息
     *
     * @param deptId 部门ID
     * @return 部门信息
     */
    @Override
    public SysDept selectDeptById(Long deptId) {
        return baseMapper.selectById(deptId);
    }

    /**
     * 根据ID查询所有子部门数（正常状态）
     *
     * @param deptId 部门ID
     * @return 子部门数
     */
    @Override
    public long selectNormalChildrenDeptById(Long deptId) {
        return baseMapper.selectCount(new LambdaQueryWrapper<SysDept>()
            .eq(SysDept::getStatus, UserConstants.DEPT_NORMAL)
            .apply(DataBaseHelper.findInSet(deptId, "ancestors")));
    }

    /**
     * 是否存在子节点
     *
     * @param deptId 部门ID
     * @return 结果
     */
    @Override
    public boolean hasChildByDeptId(Long deptId) {
        return baseMapper.exists(new LambdaQueryWrapper<SysDept>()
            .eq(SysDept::getParentId, deptId));
    }

    /**
     * 查询部门是否存在用户
     *
     * @param deptId 部门ID
     * @return 结果 true 存在 false 不存在
     */
    @Override
    public boolean checkDeptExistUser(Long deptId) {
        return userMapper.exists(new LambdaQueryWrapper<SysUser>()
            .eq(SysUser::getDeptId, deptId));
    }

    /**
     * 校验部门名称是否唯一
     *
     * @param dept 部门信息
     * @return 结果
     */
    @Override
    public String checkDeptNameUnique(SysDept dept) {
        boolean exist = baseMapper.exists(new LambdaQueryWrapper<SysDept>()
            .eq(SysDept::getDeptName, dept.getDeptName())
            .eq(SysDept::getParentId, dept.getParentId())
            .ne(ObjectUtil.isNotNull(dept.getDeptId()), SysDept::getDeptId, dept.getDeptId()));
        if (exist) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验部门是否有数据权限
     *
     * @param deptId 部门id
     */
    @Override
    public void checkDeptDataScope(Long deptId) {
        if (!LoginHelper.isAdmin()) {
            SysDept dept = new SysDept();
            dept.setDeptId(deptId);
            List<SysDept> depts = selectDeptList(dept);
            if (CollUtil.isEmpty(depts)) {
                throw new ServiceException("没有权限访问部门数据！");
            }
        }
    }

    /**
     * 新增保存部门信息
     *
     * @param dept 部门信息
     * @return 结果
     */
    @Override
    public int insertDept(SysDept dept) {
        SysDept info = baseMapper.selectById(dept.getParentId());
        // 如果父节点不为正常状态,则不允许新增子节点
        if (ObjectUtil.isNotNull(info) && !UserConstants.DEPT_NORMAL.equals(info.getStatus()) && dept.getParentId() != 0L) {
            throw new ServiceException("部门停用，不允许新增");
        }
        if (ObjectUtil.isNotNull(info) && StringUtils.isNotEmpty(info.getAncestors())) {
            if (info.getAncestors().split(",").length >= 6) {
                throw new ServiceException("子部门层级超过最大限度");
            }
        }
        return baseMapper.insert(dept);
    }

    /**
     * 修改保存部门信息
     *
     * @param dept 部门信息
     * @return 结果
     */
    @Override
    public int updateDept(SysDept dept) {
        SysDept newParentDept = baseMapper.selectById(dept.getParentId());
        SysDept oldDept = baseMapper.selectById(dept.getDeptId());
        if (ObjectUtil.isNotNull(newParentDept) && ObjectUtil.isNotNull(oldDept)) {
            String newAncestors = newParentDept.getAncestors() + "," + newParentDept.getDeptId();
            String oldAncestors = oldDept.getAncestors();
            dept.setAncestors(newAncestors);
            updateDeptChildren(dept.getDeptId(), newAncestors, oldAncestors);
        }
        int result = baseMapper.updateById(dept);
        if (UserConstants.DEPT_NORMAL.equals(dept.getStatus()) && StringUtils.isNotEmpty(dept.getAncestors())
            && !StringUtils.equals(UserConstants.DEPT_NORMAL, dept.getAncestors())) {
            // 如果该部门是启用状态，则启用该部门的所有上级部门
            updateParentDeptStatusNormal(dept);
        }
        return result;
    }

    /**
     * 修改该部门的父级部门状态
     *
     * @param dept 当前部门
     */
    private void updateParentDeptStatusNormal(SysDept dept) {
        String ancestors = dept.getAncestors();
        Long[] deptIds = Convert.toLongArray(ancestors);
        baseMapper.update(null, new LambdaUpdateWrapper<SysDept>()
            .set(SysDept::getStatus, UserConstants.DEPT_NORMAL)
            .in(SysDept::getDeptId, Arrays.asList(deptIds)));
    }

    /**
     * 修改子元素关系
     *
     * @param deptId       被修改的部门ID
     * @param newAncestors 新的父ID集合
     * @param oldAncestors 旧的父ID集合
     */
    public void updateDeptChildren(Long deptId, String newAncestors, String oldAncestors) {
        List<SysDept> children = baseMapper.selectList(new LambdaQueryWrapper<SysDept>()
            .apply(DataBaseHelper.findInSet(deptId, "ancestors")));
        List<SysDept> list = new ArrayList<>();
        for (SysDept child : children) {
            SysDept dept = new SysDept();
            dept.setDeptId(child.getDeptId());
            dept.setAncestors(child.getAncestors().replaceFirst(oldAncestors, newAncestors));
            list.add(dept);
        }
        if (list.size() > 0) {
            baseMapper.updateBatchById(list);
        }
    }

    /**
     * 删除部门管理信息
     *
     * @param deptId 部门ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteDeptById(Long deptId) {
        // 删除部门管理信息
        sysNoticeDeptMapper.delete(Wrappers.<SysNoticeDept>lambdaQuery().eq(SysNoticeDept::getDeptId, deptId));
        List<SysOssBucketDeptVo> sysOssBucketDeptVos = sysOssBucketDeptMapper.selectVoList(Wrappers.<SysOssBucketDept>lambdaQuery().eq(SysOssBucketDept::getDeptId, deptId));
        for (SysOssBucketDeptVo sysOssBucketDeptVo : sysOssBucketDeptVos) {
            sysOssBucketDeptMapper.deleteById(sysOssBucketDeptVo.getId());
            sysOssPowerRelationMapper.delete(Wrappers.<SysOssPowerRelation>lambdaQuery().eq(SysOssPowerRelation::getBucketDeptId, sysOssBucketDeptVo.getId()));
        }
        sysRobotRelevanceDepartMapper.delete(Wrappers.<SysRobotRelevanceDepart>lambdaQuery().eq(SysRobotRelevanceDepart::getDepartId, deptId));
        return baseMapper.deleteById(deptId);
    }

    @Override
    public boolean checkNoticeDeptPowerScope(Set<Long> deptIds) {
        if (LoginHelper.isAdmin()) {
            return true;
        }
        if (!CollectionUtils.isEmpty(deptIds)) {
            List<SysDept> depts = baseMapper.selectDeptList(new LambdaQueryWrapper<SysDept>().select(SysDept::getDeptId).in(
                SysDept::getDeptId, deptIds));
            return depts.size() == deptIds.size();
        }
        return true;
    }

    @Override
    public Set<Long> selectAllDeptIdsWithOutDataScope() {
        List<SysDept> sysDepts = baseMapper.selectList(new LambdaQueryWrapper<SysDept>()
            .select(SysDept::getDeptId));
        return sysDepts.stream().map(SysDept::getDeptId).collect(Collectors.toSet());
    }

    @Override
    public List<SysDept> selectDeptByIds(Set<Long> deptIds) {
        return baseMapper.selectList(new LambdaQueryWrapper<SysDept>()
            .in(SysDept::getDeptId, deptIds));
    }

    @Override
    public List<Tree<Long>> selectDeptTreeList(SysDept dept) {
        throw new ServiceException("not implement");
    }

}
