package com.zhonghe.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.domain.entity.*;
import com.zhonghe.common.core.service.GisServicesParamsService;
import com.zhonghe.system.mapper.CoordinteMapper;
import com.zhonghe.system.mapper.GisFieldsMapper;
import com.zhonghe.system.mapper.GisServicesParamsMapper;
import com.zhonghe.system.mapper.GraphServiceMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
public class GisServicesParamsServiceImpl implements GisServicesParamsService {

    private final GisServicesParamsMapper gisServicesParamsMapper;

    private final GraphServiceMapper graphServiceMapper;

    private final GisFieldsMapper gisFieldsMapper;
    private final CoordinteMapper coordinteDao;

    @Override
    public IPage<GisServicesParams> selectAll(String serviceName, PageQuery pageQuery) {
        IPage iPage = gisServicesParamsMapper.selectVoPage(pageQuery.build(), new QueryWrapper<>());
        return iPage;
    }

    @Override
    public GisServicesParams selectById(Long serviceId) {
        return gisServicesParamsMapper.selectById(serviceId);
    }

    @Override
    public int updateEntity(GisServicesParams gisService) {
        return gisServicesParamsMapper.updateById(gisService);
    }

    @Override
    public int saveEntity(GisServicesParams gisService) {
        return gisServicesParamsMapper.insert(gisService);
    }

    @Override
    public int deleteEntity(Long serviceId) {
        return gisServicesParamsMapper.deleteById(serviceId);
    }

    /**
     * 参数列表
     * */
    @Override
    public List<GisService> selectAll(List<GisService> gisServicesList) {
        if (!gisServicesList.isEmpty()){
            for (GisService gisService : gisServicesList) {
                List<GisServicesParams> gisServicesParams = gisServicesParamsMapper.selectVoList(new LambdaQueryWrapper<GisServicesParams>()
                    .eq(GisServicesParams::getServiceId, gisService.getServiceId()));
                gisService.setServicesParamsList(gisServicesParams);
            }
        }
        return gisServicesList;
    }

    /**
     * 使用保存服务参数集合
     * */
    @Override
    public void saveButhEntity(List<GisServicesParams> serviceParams) {
        gisServicesParamsMapper.insertBatch(serviceParams);
    }

    /**
     * 使用规则id获取所有服务和参数
     * */
    @Override
    public void selectByIdList(List<GisService> serviceList) {
        if (!serviceList.isEmpty()){
            for (GisService gisService : serviceList) {
                List<GisServicesParams> gisServicesParams = gisServicesParamsMapper.selectVoList(new LambdaQueryWrapper<GisServicesParams>()
                    .eq(GisServicesParams::getServiceId, gisService.getServiceId()));

                List<GraphService> graphServices = graphServiceMapper.selectVoList(new LambdaQueryWrapper<GraphService>()
                    .eq(GraphService::getGeographical, gisService.getServiceId()));

//                List<GisFields> gisFields = gisFieldsMapper.selectVoList(new LambdaQueryWrapper<GisFields>()
//                    .eq(GisFields::getServiceId, gisService.getServiceId()));

                gisService.setServicesParamsList(gisServicesParams);
                gisService.setServiceGraphList(graphServices);
//                gisService.setServiceFieldsList(gisFields);
            }
        }
    }

    /**
     * 修改参数范围
     * */
    @Override
    public void updateButhParamScope(List<GisServiceRule> gisService1) {
        coordinteDao.updateButhParamScope(gisService1);

    }
    /**
     * 更新规则的参数配置
    * */
    @Override
    public void updateButhParam(RuleService gisService) {
        coordinteDao.updateParamsById(gisService.getRuleId());
    }
    /**
     * 删除临时参数
     * */
    @Override
    public void deleteButhEntity(List<GisService> gisServiceList) {
        List<Long> collect = gisServiceList.stream().map(GisService::getServiceId).collect(Collectors.toList());
        gisServicesParamsMapper.delete(new LambdaQueryWrapper<GisServicesParams>()
        .in(collect!=null,GisServicesParams::getServiceId,collect));
    }
}
