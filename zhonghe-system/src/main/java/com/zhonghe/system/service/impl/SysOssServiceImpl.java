package com.zhonghe.system.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.exception.ServiceException;
import com.zhonghe.common.exception.base.BaseException;
import com.zhonghe.common.helper.LoginHelper;
import com.zhonghe.common.utils.JsonUtils;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.common.utils.redis.RedisUtils;
import com.zhonghe.oss.constant.OssConstant;
import com.zhonghe.oss.core.OssClient;
import com.zhonghe.oss.entity.UploadResult;
import com.zhonghe.oss.factory.OssFactory;
import com.zhonghe.oss.properties.OssProperties;
import com.zhonghe.system.domain.SysOss;
import com.zhonghe.system.domain.SysOssBucket;
import com.zhonghe.system.domain.SysOssBucketSec;
import com.zhonghe.system.domain.SysOssConfig;
import com.zhonghe.system.domain.bo.SysOssBo;
import com.zhonghe.system.domain.dto.BucketFileHashDto;
import com.zhonghe.system.domain.vo.OSSInfoVo;
import com.zhonghe.system.domain.vo.SysOssBucketSecVo;
import com.zhonghe.system.domain.vo.SysOssBucketVo;
import com.zhonghe.system.domain.vo.SysOssVo;
import com.zhonghe.system.event.OssCalculatedBucketCapacityEvent;
import com.zhonghe.system.event.OssCreateSysFileDBEntityEvent;
import com.zhonghe.system.event.SysOssBucketHugeFileMergeEvent;
import com.zhonghe.system.mapper.SysOssBucketDeptMapper;
import com.zhonghe.system.mapper.SysOssBucketMapper;
import com.zhonghe.system.mapper.SysOssBucketOpenMapper;
import com.zhonghe.system.mapper.SysOssBucketSecMapper;
import com.zhonghe.system.mapper.SysOssMapper;
import com.zhonghe.system.mapper.SysOssPowerRelationMapper;
import com.zhonghe.system.service.ISysOssService;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 文件上传 服务层实现
 *
 * <AUTHOR> Li
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class SysOssServiceImpl implements ISysOssService {

    private final SysOssMapper baseMapper;
    private final SysOssBucketOpenMapper bucketOpenMapper;
    private final SysOssBucketSecMapper bucketSecMapper;
    private final SysOssBucketMapper bucketMapper;
    private final SysOssBucketDeptMapper bucketDeptMapper;
    private final SysOssPowerRelationMapper powerRelationMapper;
    private final SysOssBucketDeptMapper ossBucketDeptMapper;
    private final ApplicationEventPublisher applicationEventPublisher;
    private SysOssConfig sysOssConfig;

    @Override
    public TableDataInfo<SysOssVo> queryPageList(SysOssBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SysOss> lqw = buildQueryWrapper(bo);
        Page<SysOssVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    @Override
    public List<SysOssVo> listByIds(Collection<Long> ossIds) {
        List<SysOssVo> list = new ArrayList<>();
        for (Long id : ossIds) {
            String key = OssConstant.SYS_OSS_KEY + id;
            SysOssVo vo = RedisUtils.getCacheObject(key);
            if (ObjectUtil.isNull(vo)) {
                vo = baseMapper.selectVoById(id);
                RedisUtils.setCacheObject(key, vo, Duration.ofDays(30));
            }
            list.add(vo);
        }
        return list;
    }

    @SneakyThrows
    private LambdaQueryWrapper<SysOss> buildQueryWrapper(SysOssBo bo) {
        Map<String, Object> params = bo.getParams();
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        LambdaQueryWrapper<SysOss> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getFileName()), SysOss::getFileName, bo.getFileName());
        lqw.like(StringUtils.isNotBlank(bo.getOriginalName()), SysOss::getOriginalName, bo.getOriginalName());
        lqw.eq(StringUtils.isNotBlank(bo.getFileSuffix()), SysOss::getFileSuffix, bo.getFileSuffix());
        lqw.eq(StringUtils.isNotBlank(bo.getUrl()), SysOss::getUrl, bo.getUrl());
        lqw.orderByDesc(SysOss::getCreateTime);
        if (params.get("beginCreateTime") != null && params.get("endCreateTime") != null) {
            if (StringUtils.isNotBlank(params.get("beginCreateTime").toString()) &&
                StringUtils.isNotBlank(params.get("endCreateTime").toString())) {
                Date beginCreateTime = simpleDateFormat.parse(params.get("beginCreateTime").toString());
                Date endCreateTime = simpleDateFormat.parse(params.get("endCreateTime").toString());
                lqw.between(true, SysOss::getCreateTime, beginCreateTime, DateUtil.offsetDay(endCreateTime, 1));
            }
        }

        lqw.like(StringUtils.isNotBlank(bo.getCreateBy()), SysOss::getCreateBy, bo.getCreateBy());
        lqw.like(StringUtils.isNotBlank(bo.getService()), SysOss::getService, bo.getService());
        return lqw;
    }

    @Override
    public SysOss getById(Long ossId) {
        return baseMapper.selectById(ossId);
    }

    @Override
    public SysOss uploadFile(MultipartFile file) {
        String originalfileName = file.getOriginalFilename();
        if (!originalfileName.contains(".")) {
            originalfileName = originalfileName + ".jpeg";
        }
        String suffix = StringUtils.substring(originalfileName, originalfileName.lastIndexOf("."), originalfileName.length());
        OssClient storage = OssFactory.instance();
        UploadResult uploadResult;
        String configKey = RedisUtils.getCacheObject(OssConstant.CACHE_CONFIG_KEY);
        Object json = RedisUtils.getCacheObject(OssConstant.SYS_OSS_KEY + configKey);
        OssProperties properties = JsonUtils.parseObject(json.toString(), OssProperties.class);
        try {
            //TODO 上传文件
            uploadResult = storage.uploadSuffix(file.getBytes(), properties.getBucketName(), suffix, file.getContentType());
        } catch (IOException e) {
            throw new ServiceException(e.getMessage());
        }
        // 保存文件信息
        SysOss oss = new SysOss();
        oss.setUrl(uploadResult.getUrl());
        oss.setFileSuffix(suffix);
        oss.setFileName(originalfileName);
        oss.setOriginalName(originalfileName);
        oss.setService(storage.getConfigKey());
        baseMapper.insert(oss);
        return oss;
    }

    @Override
    public SysOss upload(MultipartFile file) {

        String originalfileName = file.getOriginalFilename();
        if (!originalfileName.contains(".")) {
            originalfileName = originalfileName + ".jpeg";
        }
        String suffix = StringUtils.substring(originalfileName, originalfileName.lastIndexOf("."), originalfileName.length());
        OssClient storage = OssFactory.instance();
        UploadResult uploadResult;
        String configKey = RedisUtils.getCacheObject(OssConstant.CACHE_CONFIG_KEY);
        Object json = RedisUtils.getCacheObject(OssConstant.SYS_OSS_KEY + configKey);
        OssProperties properties = JsonUtils.parseObject(json.toString(), OssProperties.class);
        try {
            //TODO 上传文件
            uploadResult = storage.uploadSuffix(file.getBytes(), properties.getBucketName(), suffix, file.getContentType());
        } catch (IOException e) {
            throw new ServiceException(e.getMessage());
        }
        SysOssBucket sysOssBucket = new SysOssBucket();
        sysOssBucket.setOwnerId(LoginHelper.getUserId());
        sysOssBucket.setNickName(uploadResult.getFilename().substring(0, uploadResult.getFilename().lastIndexOf(".")));
        sysOssBucket.setOriginalName(uploadResult.getFilename());
        sysOssBucket.setFileSuffix(suffix);
        bucketMapper.insert(sysOssBucket);
        SysOssBucketSec sysOssBucketSec = new SysOssBucketSec();
        sysOssBucketSec.setBucketId(sysOssBucket.getId());
        sysOssBucketSec.setBucketName(properties.getBucketName());
        sysOssBucketSec.setMinioPath(uploadResult.getUrl());
        bucketSecMapper.insert(sysOssBucketSec);
        // 保存文件信息
        SysOss oss = new SysOss();
        oss.setUrl(OssConstant.FILE_PREVIEW + StringUtils.encrypt(String.valueOf(sysOssBucketSec.getId())));
        oss.setFileSuffix(suffix);
        oss.setFileName(originalfileName);
        oss.setOriginalName(originalfileName);
        oss.setService(storage.getConfigKey());
        oss.setBucketId(sysOssBucket.getId());
        baseMapper.insert(oss);

        return oss;
    }

    @SneakyThrows
    @Override
    public String buildUrl(Long id) {
        SysOssBucketSecVo bucketOpenVo = bucketSecMapper.selectVoOne(Wrappers.<SysOssBucketSec>lambdaQuery().eq(SysOssBucketSec::getBucketId, id));
        if (ObjectUtil.isEmpty(bucketOpenVo)) {
            throw new BaseException("文件未开放");
        }
        return StringUtils.encrypt(id.toString());
    }

    @Override
    @SneakyThrows
    public void preViewFile(String param, HttpServletResponse response) {
        SysOssBucketSecVo bucketOpenVo = bucketOpenMapper.selectBucketOpen(Long.parseLong(StringUtils.decrypt(param)));
        if (ObjectUtil.isEmpty(bucketOpenVo)) {
            throw new BaseException("文件未开放");
        }
        if (DateUtil.compare(bucketOpenVo.getExpiredTime(), new Date()) < 0) {
            throw new BaseException("文件已过期");
        }
        if (StringUtils.isEmpty(bucketOpenVo.getMinioPath())) {
            throw new BaseException("获取文件地址失败");
        }
        // 根据文件链接转输出流
        responseFile(response, bucketOpenVo);

    }

    @SneakyThrows
    @Override
    public void preViewFileFromHome(String param, HttpServletResponse response) {
        SysOssBucketSecVo bucketOpenVo = bucketSecMapper.selectVoOne(Wrappers.<SysOssBucketSec>lambdaQuery().eq(SysOssBucketSec::getId, Long.parseLong(StringUtils.decrypt(param))));
        if (ObjectUtil.isEmpty(bucketOpenVo)) {
            throw new BaseException("文件不存在");
        }
        if (StringUtils.isEmpty(bucketOpenVo.getMinioPath())) {
            throw new BaseException("获取文件地址失败");
        }
        // 根据文件链接转输出流
        responseFile(response, bucketOpenVo);
    }

    /**
     * 响应文件
     *
     * @param response
     * @param bucketSecVo
     * @throws IOException
     */
    private void responseFile(HttpServletResponse response, SysOssBucketSecVo bucketSecVo) throws IOException {
        SysOssBucketVo sysOssBucket = bucketMapper.selectVoOne(Wrappers.<SysOssBucket>lambdaQuery().eq(SysOssBucket::getId, bucketSecVo.getBucketId()));
        // 根据文件链接转输出流
        //InputStream in = new URL(bucketOpenVo.getMinioPath()).openStream();
        InputStream in = OssFactory.instance().getInputStreamFromS3(bucketSecVo.getBucketName(), bucketSecVo.getMinioPath());
        if (in == null) {
            throw new BaseException("获取文件流失败");
        }
        ServletOutputStream out = null;
        String fileName = sysOssBucket.getNickName() + sysOssBucket.getFileSuffix();

        String[] array = fileName.split("[.]");
        String fileType = array[array.length - 1].toLowerCase();
        //设置文件ContentType类型
        if ("jpg,jpeg,gif,png".contains(fileType)) {
            response.setContentType("image/" + fileType);
        } else if ("pdf".contains(fileType)) {
            response.setContentType("application/pdf");
        } else {//自动判断下载文件类型
            response.setContentType("multipart/form-data");
        }
        out = response.getOutputStream();
        // 读取文件流
        byte[] b = new byte[1024];
        int len;
        while ((len = in.read(b)) != -1) {
            out.write(b, 0, len);
        }
        out.flush();
        out.close();
        in.close();
    }

    @Override
    @SneakyThrows
    public void downloadFile(String url, String fileName, HttpServletResponse response) {
        String configKey = RedisUtils.getCacheObject(OssConstant.CACHE_CONFIG_KEY);
        Object json = RedisUtils.getCacheObject(OssConstant.SYS_OSS_KEY + configKey);
        OssProperties properties = JsonUtils.parseObject(json.toString(), OssProperties.class);
        Assert.notNull(properties, "请先配置文件上传");
        //文件名进行编码
        fileName = URLEncoder.encode(fileName, "UTF-8");
        OssFactory.instance().downFromS3(properties.getBucketName(), url, fileName, response);
    }

    @Override
    @SneakyThrows
    public void downloadFromOss(Long sysOssId, HttpServletRequest request, HttpServletResponse response) {
        SysOss sysOss = baseMapper.selectById(sysOssId);
        SysOssBucketVo sysOssBucketVo = bucketMapper.selectVoOne(Wrappers.<SysOssBucket>lambdaQuery().eq(SysOssBucket::getId, sysOss.getBucketId()));

        SysOssBucketSecVo sysOssBucketSecVo = bucketSecMapper.selectVoOne(Wrappers.<SysOssBucketSec>lambdaQuery().eq(SysOssBucketSec::getBucketId, sysOssBucketVo.getId()));
        // 根据文件链接转输出流
        //InputStream in = new URL(sysOssBucketSecVo.getMinioPath()).openStream();
        InputStream in = OssFactory.instance().getInputStreamFromS3(sysOssBucketSecVo.getBucketName(), sysOssBucketSecVo.getMinioPath());

        if (ObjectUtil.isEmpty(sysOssBucketSecVo)) {
            throw new BaseException("文件不存在");
        }
        response.reset();
//        response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE + "; charset=UTF-8");
        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Credentials", "true");
        response.setHeader("Access-Control-Allow-Methods", "*");
        response.setHeader("Access-Control-Allow-Headers", "*");
        // 设置response的Header
        response.setCharacterEncoding("UTF-8");
        //下载操作
        response.setContentType("multipart/form-data");
        String fileName = sysOssBucketVo.getNickName() + sysOssBucketVo.getFileSuffix();

        String userAgent = request.getHeader("User-Agent");
        String formFileName = fileName;
        // 针对IE或者以IE为内核的浏览器：
        if (userAgent.contains("MSIE") || userAgent.contains("Trident")) {
            formFileName = java.net.URLEncoder.encode(formFileName, "UTF-8");
        } else {
            // 非IE浏览器的处理：
            formFileName = new String(formFileName.getBytes("UTF-8"), "ISO-8859-1");
        }
        response.addHeader("Content-Disposition", "attachment; filename=" + new String(formFileName.getBytes(), "UTF-8"));
        OutputStream out = response.getOutputStream();

        byte[] b = new byte[1024];
        int len;
        while ((len = in.read(b)) != -1) {
            out.write(b, 0, len);
        }
        out.flush();
        out.close();
        in.close();
    }

    @Override
    @SneakyThrows
    public void downloadFromBucket(Long id, HttpServletRequest request, HttpServletResponse response) {
        SysOssBucketVo sysOssBucketVo = bucketMapper.selectVoOne(Wrappers.<SysOssBucket>lambdaQuery().eq(SysOssBucket::getId, id));

        SysOssBucketSecVo sysOssBucketSecVo = bucketSecMapper.selectVoOne(Wrappers.<SysOssBucketSec>lambdaQuery().eq(SysOssBucketSec::getBucketId, sysOssBucketVo.getId()));
        // 根据文件链接转输出流
        //InputStream in = new URL(sysOssBucketSecVo.getMinioPath()).openStream();
        InputStream in = OssFactory.instance().getInputStreamFromS3(sysOssBucketSecVo.getBucketName(), sysOssBucketSecVo.getMinioPath());

        if (ObjectUtil.isEmpty(sysOssBucketSecVo)) {
            throw new BaseException("文件不存在");
        }
        response.reset();
        // 设置response的Header
        response.setCharacterEncoding("UTF-8");
        //下载操作
        response.setContentType("multipart/form-data");
        String fileName = sysOssBucketVo.getNickName() + sysOssBucketVo.getFileSuffix();

        String userAgent = request.getHeader("User-Agent");
        String formFileName = fileName;
        // 针对IE或者以IE为内核的浏览器：
        if (userAgent.contains("MSIE") || userAgent.contains("Trident")) {
            formFileName = java.net.URLEncoder.encode(formFileName, "UTF-8");
        } else {
            // 非IE浏览器的处理：
            formFileName = new String(formFileName.getBytes("UTF-8"), "ISO-8859-1");
        }
        response.addHeader("Content-Disposition", "attachment; filename=" + new String(formFileName.getBytes(), "UTF-8"));
        OutputStream out = response.getOutputStream();

        byte[] b = new byte[1024];
        int len;
        while ((len = in.read(b)) != -1) {
            out.write(b, 0, len);
        }
        out.flush();
        out.close();
        in.close();
    }

    @Override
    public OSSInfoVo getOssInfo(String bucketName) {
        OSSInfoVo ossInfoVo = new OSSInfoVo();
        if (StringUtils.isEmpty(bucketName)) {
            double bucketSize = OssFactory.instance().getAllBucketSize() / (1024 * 1024);
            double objectNum = OssFactory.instance().getAllBucketObjectsNum();
            double bucketsNum = OssFactory.instance().getBucketsNum();
            ossInfoVo.setBucketSize(String.format("%.2f", bucketSize));
            ossInfoVo.setObjectNum(objectNum);
            ossInfoVo.setBucketNum(bucketsNum);
        } else {
            double bucketSize = OssFactory.instance().getOneBucketSize(bucketName) / (1024 * 1024);
            double objectNum = OssFactory.instance().getOneBucketObjectsNum(bucketName);
            double bucketsNum = OssFactory.instance().getBucketsNum();
            ossInfoVo.setBucketSize(String.format("%.2f", bucketSize));
            ossInfoVo.setObjectNum(objectNum);
            ossInfoVo.setBucketNum(bucketsNum);
        }
        return ossInfoVo;
    }

    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 做一些业务上的校验,判断是否需要校验
        }
        List<SysOss> list = baseMapper.selectBatchIds(ids);
        for (SysOss sysOss : list) {
            OssClient storage = OssFactory.instance(sysOss.getService());
            storage.delete(sysOss.getUrl());
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public void createBucket(String bucketName) {
        OssFactory.instance().createBucket(bucketName);
    }

    @Override
    public SysOss uploadAndCreateEntity(Long id, MultipartFile file) {
        Long bucketId = 0L;
        //文件夹或者盘
        SysOssBucket sysOssBucket = bucketMapper.selectById(id);
        if (sysOssBucket.getBucketType() == OssConstant.BUCKET_TYPE_FOLD) {
            //查询盘
            SysOssBucketVo bucketVos = bucketMapper.selectVoOne(Wrappers.<SysOssBucket>lambdaQuery().eq(SysOssBucket::getBucketId, sysOssBucket.getBucketId()).eq(SysOssBucket::getParentId, 0));
            bucketId = bucketVos.getId();
        } else {
            bucketId = sysOssBucket.getId();
        }
        SysOssBucketSec bucketSec = bucketSecMapper.selectOne(new LambdaQueryWrapper<SysOssBucketSec>()
            .eq(SysOssBucketSec::getBucketId, bucketId));
        if ((ObjectUtil.isNotNull(bucketSec.getCurrentCapacity()) ? bucketSec.getCurrentCapacity() : 0) + file.getSize() > sysOssBucket.getMaxCapacity()) {
            throw new ServiceException(sysOssBucket.getNickName() + "容量不足");
        }
        String originalfileName = file.getOriginalFilename();
        String suffix = StringUtils.substring(originalfileName, originalfileName.lastIndexOf("."), originalfileName.length());
        OssClient storage = OssFactory.instance();
        UploadResult uploadResult;
        try {
            uploadResult = storage.uploadSuffix(file.getBytes(), bucketSec.getBucketName(), suffix, file.getContentType());
        } catch (IOException e) {
            throw new ServiceException(e.getMessage());
        }
        // 保存文件信息
        SysOss oss = new SysOss();
        oss.setUrl(uploadResult.getUrl());
        oss.setBucketName(bucketSec.getBucketName());
        oss.setFileSuffix(suffix);
        oss.setFileName(uploadResult.getFilename());
        oss.setOriginalName(originalfileName);
        oss.setService(storage.getConfigKey());
        oss.setFileSize(file.getSize());
        baseMapper.insert(oss);
        // 先建立一个sys_oss_bucket对象记录文件信息,同时巨鹿sec表内容
        applicationEventPublisher.publishEvent(new OssCreateSysFileDBEntityEvent(this, id, oss));


        //计算当前桶容量大小
        applicationEventPublisher.publishEvent(new OssCalculatedBucketCapacityEvent(this, bucketId));
        return oss;
    }

    @Override
    public boolean setObjectDelayDeleteTag(Long id) {
        // 查询到bucketName 与 对象的 key
        SysOssBucketSec bucketSec = bucketSecMapper.selectById(id);
        if (ObjectUtil.isNotNull(bucketSec)) {
            return OssFactory.instance().setObjectDeleteTag(bucketSec.getBucketName(), bucketSec.getMinioPath());
        }
        return false;
    }

    @Override
    public void setBucketDelayTag(Long bucketId) {
        SysOssBucketSec bucketSec = bucketSecMapper.selectOne(new LambdaQueryWrapper<SysOssBucketSec>().eq(SysOssBucketSec::getBucketId, bucketId));
        if (ObjectUtil.isNotNull(bucketSec) && bucketSec.getBucketType() == OssConstant.BUCKET_TYPE_BUCKET) {
            OssFactory.instance().setBucketContainObjectsDeleteTag(bucketSec.getBucketName());
        }
    }

    @Override
    public void deleteByKeys(List<String> keys) {
        for (String key : keys) {
            OssFactory.instance().delete(key);
        }
    }

    @Override
    public InputStream getInputStreamById(Long ossId) {
        SysOss sysOss = baseMapper.selectById(ossId);
        String id = sysOss.getUrl().substring(sysOss.getUrl().lastIndexOf("/") + 1);
        SysOss fileInfo = bucketSecMapper.getOssBucketSecById(Long.valueOf(StringUtils.decrypt(id)));
        String configKey = RedisUtils.getCacheObject(OssConstant.CACHE_CONFIG_KEY);
        Object json = RedisUtils.getCacheObject(OssConstant.SYS_OSS_KEY + configKey);
        OssProperties properties = JsonUtils.parseObject(json.toString(), OssProperties.class);
        return OssFactory.instance().getInputStreamFromS3(properties.getBucketName(), fileInfo.getUrl());
    }

    @Override
    public byte[] getBytesById(Long ossId) {
        SysOss sysOss = baseMapper.selectById(ossId);
        String id = sysOss.getUrl().substring(sysOss.getUrl().lastIndexOf("/") + 1);
        SysOss fileInfo = bucketSecMapper.getOssBucketSecById(Long.valueOf(StringUtils.decrypt(id)));
        String configKey = RedisUtils.getCacheObject(OssConstant.CACHE_CONFIG_KEY);
        Object json = RedisUtils.getCacheObject(OssConstant.SYS_OSS_KEY + configKey);
        OssProperties properties = JsonUtils.parseObject(json.toString(), OssProperties.class);
        InputStream inputStream = OssFactory.instance().getInputStreamFromS3(properties.getBucketName(), fileInfo.getUrl());
        return IoUtil.readBytes(inputStream);
    }

    @Override
    public void uploadChunkFileAndCreateEntity(BucketFileHashDto dto, Integer chunk, MultipartFile file) {

        if (chunk > dto.getChunkCount()) {
            throw new ServiceException("数据异常");
        }

        try (InputStream in = file.getInputStream()) {
            OssFactory.instance().uploadChunkFile(in, dto.getUploadId(), dto.getBucketName(), dto.getObjectKey(), chunk, file.getSize());
        } catch (Exception e) {
            throw new ServiceException(e.getMessage());
        }
        applicationEventPublisher.publishEvent(new SysOssBucketHugeFileMergeEvent(this, dto));
    }
}
