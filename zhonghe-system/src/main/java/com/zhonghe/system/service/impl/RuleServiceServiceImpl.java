package com.zhonghe.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.domain.entity.RuleService;
import com.zhonghe.common.core.service.RuleServiceService;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.system.mapper.RuleServiceMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@RequiredArgsConstructor
@Service
public class RuleServiceServiceImpl implements RuleServiceService {

    private final RuleServiceMapper ruleServiceMapper;

    @Override
    public IPage<RuleService> selectAll(String serviceName, PageQuery pageQuery) {
        IPage iPage = ruleServiceMapper.selectVoPage(pageQuery.build(), new LambdaQueryWrapper<RuleService>()
        .like(StringUtils.isNotBlank(serviceName),RuleService::getRuleName , serviceName));
        return iPage;
    }

    @Override
    public RuleService selectById(Long serviceId) {
        return ruleServiceMapper.selectById(serviceId);
    }

    @Override
    public int updateEntity(RuleService ruleService) {
        return ruleServiceMapper.updateById(ruleService);
    }

    @Override
    public int saveEntity(RuleService ruleService) {
        return ruleServiceMapper.insert(ruleService);
    }

    @Override
    public int deleteEntity(Long serviceId) {
        return ruleServiceMapper.deleteById(serviceId);
    }

    @Override
    public List<Long> selectListById(Long ruleId) {
        ruleServiceMapper.selectVoList(new LambdaQueryWrapper<RuleService>());

        return null;
    }
}
