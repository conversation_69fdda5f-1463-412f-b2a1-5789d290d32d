package com.zhonghe.system.service;

import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.system.domain.bo.SysRobotConfigBo;
import com.zhonghe.system.domain.vo.SysRobotConfigVo;

import java.util.Collection;
import java.util.List;

/**
 * 机器人配置Service接口
 *
 * <AUTHOR>
 * @date 2023-03-29
 */
public interface ISysRobotConfigService {

    /**
     * 查询机器人配置
     */
    SysRobotConfigVo queryById(Long id);

    /**
     * 查询机器人配置列表
     */
    TableDataInfo<SysRobotConfigVo> queryPageList(SysRobotConfigBo bo, PageQuery pageQuery);

    /**
     * 查询机器人配置列表
     */
    List<SysRobotConfigVo> queryList(SysRobotConfigBo bo);

    /**
     * 修改机器人配置
     */
    Boolean insertByBo(SysRobotConfigBo bo);

    /**
     * 修改机器人配置
     */
    Boolean updateByBo(SysRobotConfigBo bo);

    /**
     * 校验并批量删除机器人配置信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
