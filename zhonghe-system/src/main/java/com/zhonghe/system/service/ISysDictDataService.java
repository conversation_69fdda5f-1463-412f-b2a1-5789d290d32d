package com.zhonghe.system.service;

import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.domain.entity.SysDictData;
import com.zhonghe.common.core.page.TableDataInfo;

import java.util.List;
import java.util.Map;

/**
 * 字典 业务层
 *
 * <AUTHOR> Li
 */
public interface ISysDictDataService {


    TableDataInfo<SysDictData> selectPageDictDataList(SysDictData dictData, PageQuery pageQuery);

    /**
     * 根据字典类型查询字典数据
     *
     * @param dictCode
     * @return
     */
    List<SysDictData> selectListByDictCode(String dictCode);

    /**
     * 根据条件分页查询字典数据
     *
     * @param dictData 字典数据信息
     * @return 字典数据集合信息
     */
    List<SysDictData> selectDictDataList(SysDictData dictData);

    /**
     * 根据字典类型和字典键值查询字典数据信息
     *
     * @param dictType  字典类型
     * @param dictValue 字典键值
     * @return 字典标签
     */
    String selectDictLabel(String dictType, String dictValue);

    /**
     * 根据字典数据ID查询信息
     *
     * @param dictCode 字典数据ID
     * @return 字典数据
     */
    SysDictData selectDictDataById(Long dictCode);

    /**
     * 批量删除字典数据信息
     *
     * @param dictCodes 需要删除的字典数据ID
     */
    void deleteDictDataByIds(Long[] dictCodes);

    /**
     * 新增保存字典数据信息
     *
     * @param dictData 字典数据信息
     * @return 结果
     */
    int insertDictData(SysDictData dictData);

    /**
     * 修改保存字典数据信息
     *
     * @param dictData 字典数据信息
     * @return 结果
     */
    int updateDictData(SysDictData dictData);

    /**
     * 通过dictType查询字典列表
     *
     * @param dictType
     * @return
     */
    List<Map<String, Object>> selectDictDataListByType(String dictType);
}
