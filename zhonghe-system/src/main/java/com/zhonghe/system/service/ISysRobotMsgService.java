package com.zhonghe.system.service;

import com.zhonghe.system.domain.vo.SysRobotMsgVo;
import com.zhonghe.system.domain.bo.SysRobotMsgBo;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 机器人消息Service接口
 *
 * <AUTHOR>
 * @date 2023-03-29
 */
public interface ISysRobotMsgService {

    /**
     * 查询机器人消息
     */
    SysRobotMsgVo queryById(Long id);

    /**
     * 查询机器人消息列表
     */
    TableDataInfo<SysRobotMsgVo> queryPageList(SysRobotMsgBo bo, PageQuery pageQuery);

    /**
     * 查询机器人消息列表
     */
    List<SysRobotMsgVo> queryList(SysRobotMsgBo bo);

    /**
     * 修改机器人消息
     */
    Boolean insertByBo(SysRobotMsgBo bo);

    /**
     * 修改机器人消息
     */
    Boolean updateByBo(SysRobotMsgBo bo);

    /**
     * 校验并批量删除机器人消息信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
