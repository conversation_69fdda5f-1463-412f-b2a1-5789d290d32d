package com.zhonghe.system.service;

import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.system.domain.bo.SysOssBucketOpenBo;
import com.zhonghe.system.domain.vo.SysOssBucketOpenVo;
import com.zhonghe.system.domain.vo.SysOssBucketSecVo;

import java.util.Collection;
import java.util.List;

/**
 * 存储文件开放表Service接口
 *
 * <AUTHOR>
 * @date 2023-04-03
 */
public interface ISysOssBucketOpenService {

    /**
     * 查询存储文件开放表
     */
    SysOssBucketOpenVo queryById(Long id);

    /**
     * 根据桶节点id查询存储文件开放表
     */
    SysOssBucketSecVo queryByBucketId(Long bucketId);

    /**
     * 查询存储文件开放表列表
     */
    TableDataInfo<SysOssBucketOpenVo> queryPageList(SysOssBucketOpenBo bo, PageQuery pageQuery);

    /**
     * 查询存储文件开放表列表
     */
    List<SysOssBucketOpenVo> queryList(SysOssBucketOpenBo bo);

    /**
     * 修改存储文件开放表
     */
    Boolean insertByBo(SysOssBucketOpenBo bo);

    /**
     * 修改存储文件开放表
     */
    Boolean updateByBo(SysOssBucketOpenBo bo);

    /**
     * 校验并批量删除存储文件开放表信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
