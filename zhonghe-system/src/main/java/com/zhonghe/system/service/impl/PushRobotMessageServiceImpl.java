package com.zhonghe.system.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.http.HttpUtil;
import com.zhonghe.common.constant.Constants;
import com.zhonghe.common.constant.UserConstants;
import com.zhonghe.common.enums.WebHookType;
import com.zhonghe.common.helper.WebHookRobotHelper;
import com.zhonghe.common.utils.JsonUtils;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.system.domain.SysRobotConfig;
import com.zhonghe.system.domain.SysRobotMsg;
import com.zhonghe.system.domain.dto.WebHookContentDto;
import com.zhonghe.system.domain.dto.WebHookDto;
import com.zhonghe.system.mapper.SysRobotConfigMapper;
import com.zhonghe.system.mapper.SysRobotMsgMapper;
import com.zhonghe.system.service.IPushRobotMessageService;
import lombok.AllArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.UnsupportedEncodingException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: cq
 * @date: 2023/3/29 10:56
 */

@AllArgsConstructor
@Service
public class PushRobotMessageServiceImpl implements IPushRobotMessageService {

    private final SysRobotConfigMapper configMapper;

    private final SysRobotMsgMapper robotMsgMapper;



    @Override
    @Transactional
    public boolean sendMessage(List<Long> robotIds, SysRobotMsg template) throws UnsupportedEncodingException, NoSuchAlgorithmException, InvalidKeyException {
        List<SysRobotConfig> configs = configMapper.selectBatchIds(robotIds);
        configs = configs.stream().filter( f -> UserConstants.ROBOT_NO.equals(f.getRobotState())).collect(Collectors.toList());

        List<SysRobotMsg> sendResult = new ArrayList<>(configs.size());
        for (SysRobotConfig config : configs) {
            WebHookType type = WebHookType.getType(config.getRobotType());
            // 从Helper中创建各个对象，并发送消息
            String sign = WebHookRobotHelper.Sign(config.getWebhook(), config.getSecret(), type);
            // 发送成功
            SysRobotMsg r = new SysRobotMsg();
            BeanUtils.copyProperties(template, r);
            r.setStatus(Constants.STATUS);
            sendResult.add(r);

            try {
                WebHookContentDto content = new WebHookContentDto(type, template.getMsgType(), template.getMsgContent());
                String result = HttpUtil.post(sign, content.toString());
                if (StringUtils.isNotEmpty(result)) {
                    WebHookDto webHookDto = JsonUtils.parseObject(result, WebHookDto.class);
                    if (ObjectUtil.isNotEmpty(webHookDto)) {
                        r.setSendResultCode(type == WebHookType.DINGDING ? webHookDto.getErrorcode() : webHookDto.getErrcode());
                        r.setSendMsg(webHookDto.getErrmsg());
                    }
                } else {
                    r.setSendResultCode(404);
                    r.setSendMsg("HTTP请求失败");
                }
            } catch (Exception e) {
                r.setSendResultCode(404);
                r.setSendMsg(e.getMessage());
            }
        }
        robotMsgMapper.insertBatch(sendResult);
        return true;
    }
}

