package com.zhonghe.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zhonghe.system.domain.SysRobotMsg;
import com.zhonghe.system.mapper.SysRobotMsgMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.zhonghe.system.domain.bo.SysRobotMsgBo;
import com.zhonghe.system.domain.vo.SysRobotMsgVo;
import com.zhonghe.system.service.ISysRobotMsgService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 机器人消息Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-03-29
 */
@RequiredArgsConstructor
@Service
public class SysRobotMsgServiceImpl implements ISysRobotMsgService {

    private final SysRobotMsgMapper baseMapper;

    /**
     * 查询机器人消息
     */
    @Override
    public SysRobotMsgVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询机器人消息列表
     */
    @Override
    public TableDataInfo<SysRobotMsgVo> queryPageList(SysRobotMsgBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SysRobotMsg> lqw = buildQueryWrapper(bo);
        Page<SysRobotMsgVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询机器人消息列表
     */
    @Override
    public List<SysRobotMsgVo> queryList(SysRobotMsgBo bo) {
        LambdaQueryWrapper<SysRobotMsg> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SysRobotMsg> buildQueryWrapper(SysRobotMsgBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SysRobotMsg> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getMsgType()), SysRobotMsg::getMsgType, bo.getMsgType());
        lqw.eq(StringUtils.isNotBlank(bo.getMsgContent()), SysRobotMsg::getMsgContent, bo.getMsgContent());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), SysRobotMsg::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增机器人消息
     */
    @Override
    public Boolean insertByBo(SysRobotMsgBo bo) {
        SysRobotMsg add = BeanUtil.toBean(bo, SysRobotMsg.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改机器人消息
     */
    @Override
    public Boolean updateByBo(SysRobotMsgBo bo) {
        SysRobotMsg update = BeanUtil.toBean(bo, SysRobotMsg.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SysRobotMsg entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除机器人消息
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
