package com.zhonghe.system.service;

import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.system.domain.SysOss;
import com.zhonghe.system.domain.bo.SysOssBo;
import com.zhonghe.system.domain.dto.BucketFileHashDto;
import com.zhonghe.system.domain.vo.OSSInfoVo;
import com.zhonghe.system.domain.vo.SysOssVo;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.Collection;
import java.util.List;

/**
 * 文件上传 服务层
 *
 * <AUTHOR> Li
 */
public interface ISysOssService {

    TableDataInfo<SysOssVo> queryPageList(SysOssBo sysOss, PageQuery pageQuery);

    List<SysOssVo> listByIds(Collection<Long> ossIds);

    SysOss getById(Long ossId);

    SysOss upload(MultipartFile file);

    /**
     * 上传文件(返回文件ID)
     *
     * @param file
     * @return
     */
    SysOss uploadFile(MultipartFile file);

    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    OSSInfoVo getOssInfo(String bucketName);

    /**
     * 预览文件
     *
     * @param param
     * @param response
     */
    void preViewFile(String param, HttpServletResponse response);

    /**
     * 生成链接地址
     *
     * @param id
     * @return
     */
    String buildUrl(Long id);

    SysOss uploadAndCreateEntity(Long id, MultipartFile file);

    void createBucket(String bucketName);

    void downloadFromOss(Long sysOssId, HttpServletRequest request, HttpServletResponse response);

    void downloadFromBucket(Long id, HttpServletRequest request, HttpServletResponse response);

    /**
     * 下载文件
     *
     * @param fileName
     * @param response
     */
    void downloadFile(String url, String fileName, HttpServletResponse response);

    /**
     * 对内预览文件
     *
     * @param param
     * @param response
     */
    void preViewFileFromHome(String param, HttpServletResponse response);

    /**
     * 给文件对象打延迟删除标签
     *
     * @return
     */
    boolean setObjectDelayDeleteTag(Long bucketSecObjectId);

    /**
     * 对桶内文件进行全过期操作
     */
    void setBucketDelayTag(Long bucketId);

    void uploadChunkFileAndCreateEntity(BucketFileHashDto uploadId, Integer chunk, MultipartFile file);

    /**
     * 根据文件ID删除文件
     *
     * @param keys
     */
    void deleteByKeys(List<String> keys);

    InputStream getInputStreamById(Long ossId);

    byte[] getBytesById(Long ossId);
}
