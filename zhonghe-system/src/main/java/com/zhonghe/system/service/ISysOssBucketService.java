package com.zhonghe.system.service;

import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.enums.OssDataScopeType;
import com.zhonghe.system.domain.SysOss;
import com.zhonghe.system.domain.SysOssBucket;
import com.zhonghe.system.domain.bo.SysOssBucketBo;
import com.zhonghe.system.domain.vo.SysOssBucketVo;
import com.zhonghe.system.domain.vo.SysOssMenuTreeAndPowerVo;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 对象存储桶Service接口
 *
 * <AUTHOR>
 * @date 2023-03-31
 */
public interface ISysOssBucketService {

    /**
     * 查询对象存储桶
     */
    SysOssBucketVo queryById(Long id);

    /**
     * 查询对象存储桶列表
     */
    TableDataInfo<SysOssBucketVo> queryPageList(SysOssBucketBo bo, PageQuery pageQuery);

    /**
     * 查询对象存储桶列表
     */
    List<SysOssBucketVo> queryList(SysOssBucketBo bo);

    /**
     * 修改对象存储桶
     */
    Boolean insertByBo(SysOssBucketBo bo);

    /**
     * 更新子级权限
     *
     * @param
     */
    void updateChildPowers(SysOssBucketBo ossBucket);

    /**
     * 添加父级权限
     *
     * @param add
     */
    void addParentPowers(SysOssBucket add);


    /**
     * 修改对象存储桶
     */
    Boolean updateByBo(SysOssBucketBo bo);

    /**
     * 校验并批量删除对象存储桶信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    Map<String ,Object> queryBucketList();

    /**
     * 查询对象存储桶内部文件列表
     *
     * @param parentId
     * @param isAsc
     * @return
     */
    Object queryNextTree(Long parentId, String nickName, String isAsc);

    boolean updateBucketPower(SysOssBucketBo bo);

    void createEntityFromOssFile(Long parentId, SysOss oss);

    /**
     * 校验是否有相关权限
     * @param bucketIds 桶id集合
     * @param has 数组以or条件判定
     * @return 通过 true ，不通过false
     */
    boolean checkPermission(Set<Long> bucketIds, OssDataScopeType[] has);

    /**
     * 计算桶容量
     *
     * @param bucketId
     */
    void calculatedBucketCapacity(Long bucketId);

    SysOssMenuTreeAndPowerVo queryManagerDeptPowers(Long id);

    String share(Long id);

    SysOssBucketVo queryBucketInfo(Long id);
}
