package com.zhonghe.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zhonghe.common.core.domain.entity.SysDept;
import com.zhonghe.common.helper.DataBaseHelper;
import com.zhonghe.system.domain.SysRoleDept;
import com.zhonghe.system.domain.SysUserRole;
import com.zhonghe.system.mapper.SysDeptMapper;
import com.zhonghe.system.mapper.SysRoleDeptMapper;
import com.zhonghe.system.mapper.SysUserRoleMapper;
import com.zhonghe.system.service.ISysDataScopeService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 数据权限 实现
 * <p>
 * 注意: 此Service内不允许调用标注`数据权限`注解的方法
 * 例如: deptMapper.selectList 此 selectList 方法标注了`数据权限`注解 会出现循环解析的问题
 *
 * <AUTHOR> Li
 */
@RequiredArgsConstructor
@Service("sdss")
public class SysDataScopeServiceImpl implements ISysDataScopeService {

    private final SysRoleDeptMapper roleDeptMapper;
    private final SysDeptMapper deptMapper;

    private final SysUserRoleMapper userRoleMapper;

    /**
     * 传入用户id获取到所有的角色，再通过角色获取所有的部门
     *
     * @param userId 角色id
     * @return
     */
    @Override
    public String getRoleCustom(Long userId) {
        List<SysUserRole> sysUserRoles = userRoleMapper.selectList(new LambdaQueryWrapper<SysUserRole>()
            .select(SysUserRole::getRoleId)
            .eq(SysUserRole::getUserId, userId));
        List<SysRoleDept> list = roleDeptMapper.selectList(
            new LambdaQueryWrapper<SysRoleDept>()
                .select(SysRoleDept::getDeptId)
                .in(SysRoleDept::getRoleId, sysUserRoles.stream().map(SysUserRole::getRoleId).collect(Collectors.toList())));
        if (CollUtil.isNotEmpty(list)) {
            return list.stream().map(rd -> Convert.toStr(rd.getDeptId())).collect(Collectors.joining(","));
        }
        return null;
    }

    @Override
    public String getDeptAndChild(Long deptId) {
        List<SysDept> deptList = deptMapper.selectList(new LambdaQueryWrapper<SysDept>()
            .select(SysDept::getDeptId)
            .apply(DataBaseHelper.findInSet(deptId, "ancestors")));
        List<Long> ids = deptList.stream().map(SysDept::getDeptId).collect(Collectors.toList());
        ids.add(deptId);
        List<SysDept> list = deptMapper.selectList(new LambdaQueryWrapper<SysDept>()
            .select(SysDept::getDeptId)
            .in(SysDept::getDeptId, ids));
        if (CollUtil.isNotEmpty(list)) {
            return list.stream().map(d -> Convert.toStr(d.getDeptId())).collect(Collectors.joining(","));
        }
        return null;
    }

}
