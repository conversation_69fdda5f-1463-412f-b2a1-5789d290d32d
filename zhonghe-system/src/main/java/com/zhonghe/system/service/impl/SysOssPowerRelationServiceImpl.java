package com.zhonghe.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zhonghe.system.domain.SysOssPowerRelation;
import com.zhonghe.system.mapper.SysOssPowerRelationMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.zhonghe.system.domain.bo.SysOssPowerRelationBo;
import com.zhonghe.system.domain.vo.SysOssPowerRelationVo;
import com.zhonghe.system.service.ISysOssPowerRelationService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 存储桶部门关系Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-03-31
 */
@RequiredArgsConstructor
@Service
public class SysOssPowerRelationServiceImpl implements ISysOssPowerRelationService {

    private final SysOssPowerRelationMapper baseMapper;

    /**
     * 查询存储桶部门关系
     */
    @Override
    public SysOssPowerRelationVo queryById(Long bucketDeptId){
        return baseMapper.selectVoById(bucketDeptId);
    }

    /**
     * 查询存储桶部门关系列表
     */
    @Override
    public TableDataInfo<SysOssPowerRelationVo> queryPageList(SysOssPowerRelationBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SysOssPowerRelation> lqw = buildQueryWrapper(bo);
        Page<SysOssPowerRelationVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询存储桶部门关系列表
     */
    @Override
    public List<SysOssPowerRelationVo> queryList(SysOssPowerRelationBo bo) {
        LambdaQueryWrapper<SysOssPowerRelation> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SysOssPowerRelation> buildQueryWrapper(SysOssPowerRelationBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SysOssPowerRelation> lqw = Wrappers.lambdaQuery();
        return lqw;
    }

    /**
     * 新增存储桶部门关系
     */
    @Override
    public Boolean insertByBo(SysOssPowerRelationBo bo) {
        SysOssPowerRelation add = BeanUtil.toBean(bo, SysOssPowerRelation.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setBucketDeptId(add.getBucketDeptId());
        }
        return flag;
    }

    /**
     * 修改存储桶部门关系
     */
    @Override
    public Boolean updateByBo(SysOssPowerRelationBo bo) {
        SysOssPowerRelation update = BeanUtil.toBean(bo, SysOssPowerRelation.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SysOssPowerRelation entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除存储桶部门关系
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
