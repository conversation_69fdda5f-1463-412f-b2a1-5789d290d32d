package com.zhonghe.system.service;


import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.system.domain.bo.SysNoticeTemplateBo;
import com.zhonghe.system.domain.vo.SysNoticeTemplateVo;

import java.util.Collection;
import java.util.List;

/**
 * 1Service接口
 *
 * <AUTHOR>
 * @date 2023-03-22
 */
public interface ISysNoticeTemplateService {

    /**
     * 查询1
     */
    SysNoticeTemplateVo queryById(Long templateId);

    /**
     * 查询1列表
     */
    TableDataInfo<SysNoticeTemplateVo> queryPageList(SysNoticeTemplateBo bo, PageQuery pageQuery);

    /**
     * 查询1列表
     */
    List<SysNoticeTemplateVo> queryList(SysNoticeTemplateBo bo);

    /**
     * 修改1
     */
    Boolean insertByBo(SysNoticeTemplateBo bo) ;

    /**
     * 修改1
     */
    Boolean updateByBo(SysNoticeTemplateBo bo);

    /**
     * 校验并批量删除1信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
