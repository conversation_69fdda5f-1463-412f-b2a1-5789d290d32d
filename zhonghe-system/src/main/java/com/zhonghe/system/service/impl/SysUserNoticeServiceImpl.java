package com.zhonghe.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhonghe.common.constant.ServiceConstants;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.domain.dto.BaseMqttMsgDTO;
import com.zhonghe.common.core.domain.dto.UserNoticeMsgDTO;
import com.zhonghe.common.core.domain.entity.ReadTheInformation;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.helper.LoginHelper;
import com.zhonghe.common.utils.DateUtils;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.system.domain.SysNotice;
import com.zhonghe.system.domain.SysUserNotice;
import com.zhonghe.system.domain.dto.SysNoticeDto;
import com.zhonghe.system.domain.dto.SysUserNoticeDto;
import com.zhonghe.system.domain.vo.SysUserNoticeVo;
import com.zhonghe.system.event.SysNoticeMqttSendMsgEvent;
import com.zhonghe.system.event.SysRobotSendEvent;
import com.zhonghe.system.mapper.ReadTheInformationMapper;
import com.zhonghe.system.mapper.SysUserNoticeMapper;
import com.zhonghe.system.service.ISysMqttService;
import com.zhonghe.system.service.ISysUserNoticeService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 公告 服务层实现
 *
 * <AUTHOR> Li
 */
@RequiredArgsConstructor
@Service
public class SysUserNoticeServiceImpl implements ISysUserNoticeService {

    private final ISysMqttService mqttService;
    private final SysUserNoticeMapper baseMapper;
    private final ReadTheInformationMapper readTheInformationMapper;

    private final ApplicationEventPublisher applicationEventPublisher;

    @Override
    public IPage<SysUserNotice> selectList(SysUserNotice notice, PageQuery pageQuery) {

        Page<SysUserNotice> build = pageQuery.build();
        return baseMapper.selectAllList(build);
    }

    @Override
    @Transactional
    public void createAndNotifyUserNotice(List<SysNotice> notices) {
        // 首先进行分组，延迟消息发送到队列，等队列响应了再
        List<SysUserNotice> normalNotices = new ArrayList<>();
        Map<Integer, List<SysNotice>> collect = notices.stream().collect(Collectors.groupingBy(SysNotice::getType));
        if (collect.containsKey(ServiceConstants.NOTICE_SEND_TYPE_NORMAL)) {
            // 非延时发送
            collect.get(ServiceConstants.NOTICE_SEND_TYPE_NORMAL).forEach(a -> {
                SysUserNotice user = new SysUserNotice();
                BeanUtils.copyProperties(a, user);
                normalNotices.add(user);
            });
            if (baseMapper.insertBatch(normalNotices)) {
                // 发送用户消息
                List<Long> userNoticeIds = normalNotices.stream().map(SysUserNotice::getUserNoticeId).collect(Collectors.toList());
                List<SysUserNoticeDto> sysUserNoticeDtos = baseMapper.selectUserNoticeIdAndDeptId(userNoticeIds);

                List<BaseMqttMsgDTO> mqttMsg = new ArrayList<>();
                sysUserNoticeDtos.forEach(a -> {
                    if (!ObjectUtils.isEmpty(a.getDeptId())) {
                        UserNoticeMsgDTO dto = new UserNoticeMsgDTO("dept/" + Long.toHexString(a.getDeptId()));
                        dto.setLevel(a.getLevel());
                        dto.setUserNoticeId(a.getUserNoticeId());
                        mqttMsg.add(dto);

                        // TODO: 2023/4/4  在这里抛出机器人消息
                        applicationEventPublisher.publishEvent(new SysRobotSendEvent(this,a.getDeptId(),  collect.get(ServiceConstants.NOTICE_SEND_TYPE_NORMAL)));
                    }
                });
                applicationEventPublisher.publishEvent(new SysNoticeMqttSendMsgEvent(this, mqttMsg));
            }
        }

        if (collect.containsKey(ServiceConstants.NOTICE_SEND_TYPE_DELAY)) {
            // 发送用户消息
            List<SysNotice> delayNotices = collect.get(ServiceConstants.NOTICE_SEND_TYPE_DELAY);
            delayNotices.forEach(a -> {
                if (a instanceof SysNoticeDto) {
                    SysNoticeDto dto = (SysNoticeDto) a;
                    if (!CollectionUtils.isEmpty(dto.getDeptIds())) {
                        long diffSecond = DateUtils.differentDaysBySecond(new Date(), a.getSendTime());
                        if (diffSecond >= 0) {
                            for (Long deptId : dto.getDeptIds()) {
                                mqttService.publishMessage("dept/" + Long.toHexString(deptId), a.getNoticeId().toString(), diffSecond);

                            }
                        }
                    }
                }
            });
        }
    }

    @Override
    public List<SysUserNotice> selectAllUnRead() {
        return baseMapper.selectAllUnReadUserNotices();
    }

    @Override
    public SysUserNotice selectByNoticeId(Long noticeId) {
        return baseMapper.selectOne(new QueryWrapper<SysUserNotice>().eq("notice_id", noticeId));
    }

    @Override
    public Long selectAllUnReadCount() {
        return baseMapper.selectAllUnReadUserNotices().stream().count();
    }

    @Override
    public SysUserNotice selectByUserNoticeId(Long userNoticeId) {
        return baseMapper.selectById(userNoticeId);
    }

    @Override
    public void insertBatch(List<SysUserNotice> sysUserNotices) {
        baseMapper.insertBatch(sysUserNotices);
    }

    @Override
    @Transactional
    public int readNotices(List<Long> userNoticeIds) {
        LambdaQueryWrapper<ReadTheInformation> lqw = new LambdaQueryWrapper<ReadTheInformation>()
            .in(ReadTheInformation::getNotedId, userNoticeIds)
            .eq(ReadTheInformation::getUserId, LoginHelper.getUserId());
        List<ReadTheInformation> readTheInformations = readTheInformationMapper.selectList(lqw);
        Set<Long> collect = readTheInformations.stream().map(ReadTheInformation::getNotedId).collect(Collectors.toSet());
        List<ReadTheInformation> insertRead = new ArrayList<>();
        userNoticeIds.forEach(u -> {
            if (!collect.contains(u)) {
                ReadTheInformation r = new ReadTheInformation();
                r.setStuts("2");
                r.setUserId(LoginHelper.getUserId());
                r.setNotedId(u);
                insertRead.add(r);
            }
        });
        if (readTheInformationMapper.insertBatch(insertRead)) {
            return insertRead.size();
        }
        return 0;
    }

    @Override
    public TableDataInfo<SysUserNotice> selectPageNoticeList(SysUserNotice notice, PageQuery pageQuery) {
        LambdaQueryWrapper<SysUserNotice> lqw = new LambdaQueryWrapper<SysUserNotice>()
            .like(StringUtils.isNotEmpty(notice.getNoticeTitle()), SysUserNotice::getNoticeTitle, notice.getNoticeTitle())
            .orderByDesc(SysUserNotice::getSendTime)
            .orderByDesc(SysUserNotice::getUserNoticeId)
            .or(StringUtils.isNotEmpty(notice.getNoticeContent()), (wrapper) -> {
                wrapper.like(StringUtils.isNotEmpty(notice.getNoticeContent()), SysUserNotice::getNoticeContent, notice.getNoticeContent());
            });
        Page<SysUserNotice> page = baseMapper.selectPage(pageQuery.build(), lqw);
        // 进行已读未读判定
        List<Long> userNoticeIds = page.getRecords().stream().map(SysUserNotice::getUserNoticeId).collect(Collectors.toList());
        // 已读消息
        List<ReadTheInformation> readedNotices = readTheInformationMapper.selectList(new LambdaQueryWrapper<ReadTheInformation>()
            .eq(ReadTheInformation::getUserId, LoginHelper.getUserId())
            .in(CollectionUtils.isNotEmpty(userNoticeIds), ReadTheInformation::getNotedId, userNoticeIds));
        List<SysUserNotice> sysUserNoticeVos = new ArrayList<>(page.getRecords().size());
        Set<Long> readCollections = readedNotices.stream().map(ReadTheInformation::getNotedId).collect(Collectors.toSet());
        for (SysUserNotice n : page.getRecords()) {
            SysUserNoticeVo v = new SysUserNoticeVo();
            BeanUtils.copyProperties(n, v);
            if (readCollections.contains(n.getUserNoticeId())) {
                v.setStuts(ServiceConstants.USER_NOTICE_READED);
            }
            sysUserNoticeVos.add(v);
        }
        page.setRecords(sysUserNoticeVos);

        return TableDataInfo.build(page);
    }
}
