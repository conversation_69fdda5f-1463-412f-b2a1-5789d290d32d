package com.zhonghe.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.oss.constant.OssConstant;
import com.zhonghe.system.domain.SysOssBucketSec;
import com.zhonghe.system.domain.bo.SysOssBucketSecBo;
import com.zhonghe.system.domain.vo.SysOssBucketSecVo;
import com.zhonghe.system.mapper.SysOssBucketSecMapper;
import com.zhonghe.system.service.ISysOssBucketSecService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 存储桶描述Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-04-03
 */
@RequiredArgsConstructor
@Service
public class SysOssBucketSecServiceImpl implements ISysOssBucketSecService {

    private final SysOssBucketSecMapper baseMapper;

    /**
     * 查询存储桶描述
     */
    @Override
    public SysOssBucketSecVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询存储桶描述列表
     */
    @Override
    public TableDataInfo<SysOssBucketSecVo> queryPageList(SysOssBucketSecBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SysOssBucketSec> lqw = buildQueryWrapper(bo);
        Page<SysOssBucketSecVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询存储桶描述列表
     */
    @Override
    public List<SysOssBucketSecVo> queryList(SysOssBucketSecBo bo) {
        LambdaQueryWrapper<SysOssBucketSec> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SysOssBucketSec> buildQueryWrapper(SysOssBucketSecBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SysOssBucketSec> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getBucketId() != null, SysOssBucketSec::getBucketId, bo.getBucketId());
        lqw.like(StringUtils.isNotBlank(bo.getBucketName()), SysOssBucketSec::getBucketName, bo.getBucketName());
        lqw.eq(bo.getBucketType() != null, SysOssBucketSec::getBucketType, bo.getBucketType());
        lqw.eq(StringUtils.isNotBlank(bo.getMinioPath()), SysOssBucketSec::getMinioPath, bo.getMinioPath());
        return lqw;
    }

    /**
     * 新增存储桶描述
     */
    @Override
    public Boolean insertByBo(SysOssBucketSecBo bo) {
        SysOssBucketSec add = BeanUtil.toBean(bo, SysOssBucketSec.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改存储桶描述
     */
    @Override
    public Boolean updateByBo(SysOssBucketSecBo bo) {
        SysOssBucketSec update = BeanUtil.toBean(bo, SysOssBucketSec.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SysOssBucketSec entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除存储桶描述
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    @Transactional
    public void createEntityFromOssFile(Long bucketId, String bucketName, String url, boolean create) {
        SysOssBucketSec bucketSec = new SysOssBucketSec();
        bucketSec.setBucketId(bucketId);
        bucketSec.setBucketName(bucketName);
        bucketSec.setBucketType(create ? OssConstant.BUCKET_TYPE_BUCKET : OssConstant.BUCKET_TYPE_FILE);
        bucketSec.setMinioPath(url);
        baseMapper.insert(bucketSec);
    }
}
