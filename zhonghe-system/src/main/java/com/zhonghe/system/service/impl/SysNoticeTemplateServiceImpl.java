package com.zhonghe.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zhonghe.system.domain.SysNoticeTemplate;
import com.zhonghe.system.domain.bo.SysNoticeTemplateBo;
import com.zhonghe.system.domain.vo.SysNoticeTemplateVo;
import com.zhonghe.system.mapper.SysNoticeTemplateMapper;
import com.zhonghe.system.service.ISysNoticeTemplateService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;


import java.util.*;

/**
 * 1Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-03-22
 */
@RequiredArgsConstructor
@Service
public class SysNoticeTemplateServiceImpl implements ISysNoticeTemplateService {

    private final SysNoticeTemplateMapper baseMapper;

    /**
     * 通过ID查询
     */
    @Override
    public SysNoticeTemplateVo queryById(Long templateId){
        return baseMapper.selectVoById(templateId);
    }

    /**
     * 查询1列表
     */
    @Override
    public TableDataInfo<SysNoticeTemplateVo> queryPageList(SysNoticeTemplateBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SysNoticeTemplate> lqw = buildQueryWrapper(bo);
        Page<SysNoticeTemplateVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询1列表
     */
    @Override
    public List<SysNoticeTemplateVo> queryList(SysNoticeTemplateBo bo) {
        LambdaQueryWrapper<SysNoticeTemplate> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SysNoticeTemplate> buildQueryWrapper(SysNoticeTemplateBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SysNoticeTemplate> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), SysNoticeTemplate::getStatus, bo.getStatus());
        //lqw.eq(StringUtils.isNotBlank(String.valueOf(bo.getTemplateId())), SysNoticeTemplate::getTemplateId, bo.getTemplateId());
        lqw.like(StringUtils.isNotBlank(bo.getTemplateTitle()), SysNoticeTemplate::getTemplateTitle, bo.getTemplateTitle());
        return lqw;
    }

    /**
     * 新增模版
     */
    @Override
    public Boolean insertByBo(SysNoticeTemplateBo bo)  {
        SysNoticeTemplate add = BeanUtil.toBean(bo, SysNoticeTemplate.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setTemplateId(add.getTemplateId());
        }
        return flag;
    }

    /**
     * 修改1
     */
    @Override
    public Boolean updateByBo(SysNoticeTemplateBo bo) {
        SysNoticeTemplate update = BeanUtil.toBean(bo, SysNoticeTemplate.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SysNoticeTemplate entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除1
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
