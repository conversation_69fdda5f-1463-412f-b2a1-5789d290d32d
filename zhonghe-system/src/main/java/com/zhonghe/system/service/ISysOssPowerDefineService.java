package com.zhonghe.system.service;

import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.system.domain.bo.SysOssPowerDefineBo;
import com.zhonghe.system.domain.vo.SysOssPowerDefineVo;

import java.util.Collection;
import java.util.List;

/**
 * 存储桶权限Service接口
 *
 * <AUTHOR>
 * @date 2023-03-31
 */
public interface ISysOssPowerDefineService {

    /**
     * 查询存储桶权限
     */
    SysOssPowerDefineVo queryById(String power);

    /**
     * 查询存储桶权限列表
     */
    TableDataInfo<SysOssPowerDefineVo> queryPageList(SysOssPowerDefineBo bo, PageQuery pageQuery);

    /**
     * 查询存储桶权限列表
     */
    List<SysOssPowerDefineVo> queryList(SysOssPowerDefineBo bo);

    /**
     * 修改存储桶权限
     */
    Boolean insertByBo(SysOssPowerDefineBo bo);

    /**
     * 修改存储桶权限
     */
    Boolean updateByBo(SysOssPowerDefineBo bo);

    /**
     * 校验并批量删除存储桶权限信息
     */
    Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid);

    void init();

    Long getIdByPower(String power);

    /**
     * 获取所有权限字符串列表
     * @return
     */
    Collection<String> getAllPowerList();
}
