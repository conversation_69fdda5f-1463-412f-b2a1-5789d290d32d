package com.zhonghe.system.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhonghe.common.annotation.SpecifyDataSource;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.domain.entity.*;
import com.zhonghe.common.core.service.GisServiceService;
import com.zhonghe.common.enums.DataSourceTypeEnum;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.system.mapper.*;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
public class GisServiceServiceImpl implements GisServiceService {

    private final GisServiceMapper gisServiceMapper;

    private final CoordinteMapper coordinteDao;

    private final GisServiceExtendMapper gisServiceExtendMapper;
    private final GraphServiceMapper graphServiceMapper;
    private final GisServiceRuleMapper gisServiceRuleMapper;
    private final GisServicesParamsMapper gisServicesParams;

    @Override
    public IPage<GisService> selectAll(String serviceName, String serviceType, PageQuery pageQuery) {
        if (ObjectUtil.isNull(pageQuery)) {
            List<GisService> gisServices = gisServiceMapper.selectVoList(new LambdaQueryWrapper<GisService>()
                .like(StringUtils.isNotBlank(serviceName), GisService::getServiceName, serviceName)
                .eq(StringUtils.isNotBlank(serviceType), GisService::getServiceType, serviceType)
                .orderByDesc(GisService::getCreateTime));
            IPage iPage = new Page();
            iPage.setRecords(gisServices);
            iPage.setTotal(gisServices.size());
            return iPage;
        }

        IPage iPage = gisServiceMapper.selectVoPage(pageQuery.build(), new LambdaQueryWrapper<GisService>()
        .like(StringUtils.isNotBlank(serviceName),GisService::getServiceName , serviceName)
            .eq(StringUtils.isNotBlank(serviceType),GisService::getServiceType,serviceType));
        return iPage;
    }

    @Override
    public GisService selectById(Long serviceId) {
        return gisServiceMapper.selectById(serviceId);
    }

    @Override
    public int updateEntity(GisService gisService) {
        return gisServiceMapper.updateById(gisService);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveEntity(GisService gisService) {
//            int i=gisServiceMapper.insertGenerated(gisService);
        gisServiceMapper.insert(gisService);
        return coordinteDao.selectFastid();
    }

    @Override
    public int deleteEntity(Long serviceId) {
        return gisServiceMapper.deleteById(serviceId);
    }

    @Override
    @SpecifyDataSource(value = DataSourceTypeEnum.PLATFORM)
    public List<String> getCoordinate() {
        return coordinteDao.getCoordinate();
    }

    @Override
    public boolean exist(Long i) {
        boolean exists = gisServiceExtendMapper.exists(new LambdaQueryWrapper<GisServiceExtend>()
            .eq(i != null, GisServiceExtend::getServiceId, i));
        return exists;
    }

    @Override
    public List<GisService> selectAllList() {
        return gisServiceMapper.selectList();
    }

    @Override
    public void saveBathe(List<GisServiceExtend> list) {
        gisServiceExtendMapper.insertBatch(list);
    }

    @Override
    public List<GisServiceExtend> selectExtend(Long serviceId) {
        List<GisServiceExtend> gisServiceExtends = gisServiceExtendMapper.selectVoList(new LambdaQueryWrapper<GisServiceExtend>()
            .eq(serviceId != null, GisServiceExtend::getServiceId, serviceId));
        return gisServiceExtends;
    }

    @Override
    public void authoService(List<GisServiceRule> gisService) {
        gisService.stream().forEach( m->{
            List<String> ruleList = m.getRuleList();
            for (String s : ruleList) {
                switch(s){
                    case ("添加"):
                        m.setIsSave(true);
                        break;
                    case ("修改"):
                        m.setIsUpdate(true);
                        break;
                    case ("删除"):
                        m.setIsDelete(true);
                        break;
                    case ("查看"):
                        m.setIsCheck(true);
                        break;
                }
            }
        });
        gisServiceRuleMapper.insertOrUpdateBatch(gisService);
        Long authorityId = gisService.get(0).getAuthorityId();
        if(authorityId!=null)coordinteDao.updateRolueById(authorityId);

    }

    @Override
    public void deleteAutho(Long serviceId) {
        gisServiceRuleMapper.deleteById(serviceId);
    }

    @Override
    public void deleteBathe(Long serviceId) {
        gisServiceExtendMapper.delete(new LambdaQueryWrapper<GisServiceExtend>()
        .eq(serviceId!=null,GisServiceExtend ::getServiceId,serviceId));
    }

    @Override
    public List<GisService> selectByIdList(List<Long> idList) {
        List<GisService> gisServices = gisServiceMapper.selectVoList(new LambdaQueryWrapper<GisService>()
            .in(!idList.isEmpty(), GisService::getServiceId, idList));
        return gisServices;
    }

    @Override
    public List<GisServiceRule> serviceRuleLise(Long ruleId) {
        return coordinteDao.serviceRuleLise(ruleId);
    }

    @Override
    public List<GisServiceRule> selectListById(Long ruleId) {
        List<GisServiceRule> gisServiceRuleList = gisServiceRuleMapper.selectVoList(new LambdaQueryWrapper<GisServiceRule>()
            .eq(GisServiceRule::getAuthorityId, ruleId));
        return gisServiceRuleList;
    }

    @Override
    public void selectParamsById(List<GisService> gisService, Long ruleId) {
        List<GisServicesParams> gisServicesParams = this.gisServicesParams.selectVoList(new LambdaQueryWrapper<GisServicesParams>()
            .eq(GisServicesParams::getRuleId, ruleId));
        //将规则的所有参数分配到每个服务里面
        for (GisService service : gisService) {
            List<GisServicesParams> servicesParamsList = new ArrayList<>();
            gisServicesParams.stream().forEach( m -> {
                if(m.getServiceId() == service.getServiceId()) servicesParamsList.add(m);
            });
            service.setServicesParamsList(servicesParamsList);
        }
    }

    @Override
    public List<ServiceRelevGraph> testGraphList(Long ruleId) {
        List<ServiceRelevGraph>  graphServiceList= coordinteDao.selectGraphById(ruleId);
        return graphServiceList;
    }

    @Override
    public Map<String, Object> pitchServer(List<Long> list) {
        return null;
    }

    @Override
    public void selectGraphById(List<GisService> gisService, Long ruleId) {

        List<ServiceRelevGraph>  graphServiceList= coordinteDao.selectGraphById(ruleId);
        List<Long> collect = graphServiceList.stream().map(ServiceRelevGraph::getGraphId).collect(Collectors.toList());
        if (graphServiceList==null) return;
        //规则里面所有图层
        List<GraphService> graphServiceList2 = graphServiceMapper.selectVoList(new LambdaQueryWrapper<GraphService>()
            .in(GraphService::getGraphId, collect));
        //将关系表循环写入实体
        graphServiceList.stream().forEach( m ->{
            Long serviceId = m.getServiceId();
            for (GraphService graphService : graphServiceList2) {
                Long graphId = m.getGraphId();
                Long graphId1 = graphService.getGraphId();
                // 不能使用 == 比较 要使用eq比较graphId.equals(graphId1)
                if(graphId > graphId1 || graphId< graphId1) {

                }else {
                    graphService.setGeographical(serviceId);
                    String authorityList = m.getAuthorityList();
                    List<String> strings = authorityList != null ?
                        Arrays.asList(authorityList.split(",")) : new ArrayList<>();
                    graphService.setRuleList(strings);
                }
            }
        });
        //分子父图层
        List<GraphService> Parent = graphServiceList2.stream().filter(m -> {
            if (m.getGraphParent() == null) {
                return true;
            } else {
                return false;
            }
        }).collect(Collectors.toList());
        List<GraphService> children = graphServiceList2.stream().filter(m -> {
            if (m.getGraphParent() != null) {
                return true;
            } else {
                return false;
            }
        }).collect(Collectors.toList());

        Parent.stream().forEach( m ->{
            List<GraphService> graphServices = new ArrayList<>();
            for (GraphService child : children) {
                if(m.getGraphId().equals(child.getGraphParent())) graphServices.add(child);
            }
            m.setChildren(graphServices);
        });

        //将图层循环注入
        for (GisService service : gisService) {
            List<GraphService> servicesParamsList = new ArrayList<>();
            Parent.stream().forEach( m -> {
                if(m.getGeographical() == service.getServiceId()) servicesParamsList.add(m);
            });
            service.setServiceGraphList(servicesParamsList);
        }

    }
}
