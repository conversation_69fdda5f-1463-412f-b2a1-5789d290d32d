package com.zhonghe.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zhonghe.system.domain.SysNoticeDept;
import com.zhonghe.system.domain.bo.SysNoticeDeptBo;
import com.zhonghe.system.domain.vo.SysNoticeDeptVo;
import com.zhonghe.system.mapper.SysNoticeDeptMapper;
import com.zhonghe.system.service.ISysNoticeDeptService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 1Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-03-22
 */
@RequiredArgsConstructor
@Service
public class SysNoticeDeptServiceImpl implements ISysNoticeDeptService {

    private final SysNoticeDeptMapper baseMapper;

    /**
     * 查询1
     */
    @Override
    public SysNoticeDeptVo queryById(Long noticeId){
        return baseMapper.selectVoById(noticeId);
    }

    /**
     * 查询1列表
     */
    @Override
    public TableDataInfo<SysNoticeDeptVo> queryPageList(SysNoticeDeptBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SysNoticeDept> lqw = buildQueryWrapper(bo);
        Page<SysNoticeDeptVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询1列表
     */
    @Override
    public List<SysNoticeDeptVo> queryList(SysNoticeDeptBo bo) {
        LambdaQueryWrapper<SysNoticeDept> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SysNoticeDept> buildQueryWrapper(SysNoticeDeptBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SysNoticeDept> lqw = Wrappers.lambdaQuery();
        return lqw;
    }

    /**
     * 新增1
     */
    @Override
    public Boolean insertByBo(SysNoticeDeptBo bo) {
        SysNoticeDept add = BeanUtil.toBean(bo, SysNoticeDept.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setNoticeId(add.getNoticeId());
        }
        return flag;
    }

    /**
     * 修改1
     */
    @Override
    public Boolean updateByBo(SysNoticeDeptBo bo) {
        SysNoticeDept update = BeanUtil.toBean(bo, SysNoticeDept.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SysNoticeDept entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除1
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
