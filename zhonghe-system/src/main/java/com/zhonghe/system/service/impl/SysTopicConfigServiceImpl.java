package com.zhonghe.system.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhonghe.common.constant.Constants;
import com.zhonghe.common.utils.redis.RedisUtils;
import com.zhonghe.system.domain.SysConfig;
import com.zhonghe.system.domain.SysTopicConfig;
import com.zhonghe.system.mapper.SysTopicConfigMapper;
import com.zhonghe.system.service.ISysConfigService;
import com.zhonghe.system.service.ISysTopicConfigService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * @Author: lpg
 * @Date: 2023/03/09/11:59
 * @Description:
 */
@RequiredArgsConstructor
@Service
public class SysTopicConfigServiceImpl extends ServiceImpl<SysTopicConfigMapper, SysTopicConfig> implements ISysTopicConfigService {



    private final ISysConfigService sysConfigService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean save(SysTopicConfig entity) {
        //处理验证码
        SysConfig sysConfig = new SysConfig();
        sysConfig.setConfigKey("sys.account.captchaOnOff");
        SysConfig retConfig = sysConfigService.getOne(sysConfig);
        boolean b = entity.getIsVerify() == 0 ? true : false;
        retConfig.setConfigValue(String.valueOf(b));
        sysConfigService.updateConfig(retConfig);
        RedisUtils.setCacheObject(getCacheKey("sys.account.captchaOnOff"), String.valueOf(b));

        return super.saveOrUpdate(entity,null);
    }

    /**
     * 设置cache key
     *
     * @param configKey 参数键
     * @return 缓存键key
     */
    private String getCacheKey(String configKey) {
        return Constants.SYS_CONFIG_KEY + configKey;
    }
}
