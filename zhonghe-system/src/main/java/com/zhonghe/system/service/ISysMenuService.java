package com.zhonghe.system.service;

import cn.hutool.core.lang.tree.Tree;
import com.zhonghe.common.core.domain.entity.SysMenu;
import com.zhonghe.system.domain.bo.SysMenuPermissionBo;
import com.zhonghe.system.domain.vo.RouterVo;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 菜单 业务层
 *
 * <AUTHOR> Li
 */
public interface ISysMenuService {

    /**
     * 根据用户查询系统菜单列表
     *
     * @param userId 用户ID
     * @return 菜单列表
     */
    List<SysMenu> selectMenuList(Long userId);

    /**
     * 根据用户查询系统菜单列表
     *
     * @param menu   菜单信息
     * @param userId 用户ID
     * @return 菜单列表
     */
    List<SysMenu> selectMenuList(SysMenu menu, Long userId);

    /**
     * 根据用户ID查询权限
     *
     * @param userId 用户ID
     * @return 权限列表
     */
    Set<String> selectMenuPermsByUserId(Long userId);

    /**
     * 根据用户ID查询菜单树信息
     *
     * @param userId 用户ID
     * @return 菜单列表
     */
    List<SysMenu> selectMenuTreeByUserId(Long userId);

    /**
     * 根据角色ID查询菜单树信息
     *
     * @param roleId 角色ID
     * @return 选中菜单列表
     */
    List<Long> selectMenuListByRoleId(Long roleId);

    /**
     * 构建前端路由所需要的菜单
     *
     * @param menus 菜单列表
     * @return 路由列表
     */
    List<RouterVo> buildMenus(List<SysMenu> menus);

    /**
     * 构建前端所需要下拉树结构
     *
     * @param menus 菜单列表
     * @return 下拉树结构列表
     */
    List<Tree<Long>> buildMenuTreeSelect(List<SysMenu> menus);

    /**
     * 根据菜单ID查询信息
     *
     * @param menuId 菜单ID
     * @return 菜单信息
     */
    SysMenu selectMenuById(Long menuId);

    /**
     * 是否存在菜单子节点
     *
     * @param menuId 菜单ID
     * @return 结果 true 存在 false 不存在
     */
    boolean hasChildByMenuId(Long menuId);

    /**
     * 查询菜单是否存在角色
     *
     * @param menuId 菜单ID
     * @return 结果 true 存在 false 不存在
     */
    boolean checkMenuExistRole(Long menuId);

    /**
     * 查询菜单标识是否存在角色
     *
     * @return 结果 true 存在 false 不存在
     */
    String checkMenuIdentifier(SysMenu sysMenu);

    /**
     * 查询菜单是否为首页
     *
     * @param sysMenu
     * @return
     */
    String checkMenuIsHome(SysMenu sysMenu);

    /**
     * 查询菜单状态
     *
     * @param sysMenu
     * @return
     */
    Boolean checkMenuStatus(SysMenu sysMenu);

    /**
     * 新增保存菜单信息
     *
     * @param menu 菜单信息
     * @return 结果
     */
    Long insertMenu(SysMenu menu);

    /**
     * 修改保存菜单信息
     *
     * @param menu 菜单信息
     * @return 结果
     */
    int updateMenu(SysMenu menu);

    /**
     * 删除菜单管理信息
     *
     * @param menuId 菜单ID
     * @return 结果
     */
    int deleteMenuById(Long menuId);

    /**
     * 校验菜单名称是否唯一
     *
     * @param menu 菜单信息
     * @return 结果
     */
    String checkMenuNameUnique(SysMenu menu);

    /**
     * 查询门户首页的菜单
     * @param isHome
     * @return
     */
    SysMenu selectOneMenuByIsHome(Integer isHome);

    String checkMenuPathUnique(SysMenu menu);

    void testBath();

    List<SysMenu> permissionList(SysMenu menu);

    Integer addPermissionList(List<SysMenu> menu);

    Integer bingPermissionRole(List<SysMenu> menu, Long role);

    /**
     * 根据角色查询菜单列表
     *
     * @param roleId
     * @return
     */
    Set<Long> selectMenuListExcludeParentId(Long roleId);

    /**
     * 根据上级菜单查询下级菜单
     *
     * @param parentId
     * @return
     */
    Map<String, Object> selectMenuByParentId(Long parentId, Long roleId);

    List<Tree<Long>> selectRoleMenuTreeByRoleId(Long roleId);

    boolean saveMenuPermission(SysMenuPermissionBo menuPermissionBo);
}
