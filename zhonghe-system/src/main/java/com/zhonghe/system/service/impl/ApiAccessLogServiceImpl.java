package com.zhonghe.system.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhonghe.common.apiassace.ApiAccessLogService;
import com.zhonghe.common.apiassace.PageParam;
import com.zhonghe.common.apiassace.apiaccesslog.ApiAccessLogExportReqVO;
import com.zhonghe.common.apiassace.apiaccesslog.ApiAccessLogPageReqVO;
import com.zhonghe.common.apiassace.apierrorlog.ApiAccessLogCreateReqDTO;
import com.zhonghe.common.apiassace.apierrorlog.ApiAccessLogDO;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.system.mapper.ApiAccessLogMapper;
import com.zhonghe.system.mapper.CoordinteMapper;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;

/**
 * API 访问日志 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ApiAccessLogServiceImpl implements ApiAccessLogService {

    @Resource
    private ApiAccessLogMapper apiAccessLogMapper;

    @Resource
    private CoordinteMapper coordinteMapper;

    @Override
    public void createApiAccessLog(ApiAccessLogCreateReqDTO createDTO) {
//        ApiAccessLogDO apiAccessLog =new ApiAccessLogDO();
        ApiAccessLogDO apiAccessLog = ApiAccessLogDO.builder().applicationName(createDTO.getApplicationName()).duration(createDTO.getDuration())
            .requestUrl(createDTO.getRequestUrl()).beginTime(createDTO.getBeginTime())
            .endTime(createDTO.getEndTime()).requestParams(createDTO.getRequestParams())
            .resultMsg(createDTO.getResultMsg()).traceId(createDTO.getTraceId()).userAgent(createDTO.getUserAgent())
            .userIp(createDTO.getUserIp()).userId(createDTO.getUserId()).requestMethod(createDTO.getRequestMethod())
            .resultCode(createDTO.getResultCode()).userType(createDTO.getUserType())
            .build();
        apiAccessLog.setCreateTime(createDTO.getBeginTime());
        apiAccessLog.setUpdateTime(createDTO.getEndTime());

        apiAccessLogMapper.insert(apiAccessLog);
    }

    private Long isNumeric2(String str) {
        try {
            if (StringUtils.isBlank(str)) {
                return null;
            }
            Long.parseLong(str);
            return Long.parseLong(str);
        } catch (Exception e) {
            return 0L;
        }
    }

    private Integer isNumeric1(String str) {
        try {
            if (StringUtils.isBlank(str)) {
                return null;
            }
            Integer.parseInt(str);
            return Integer.parseInt(str);
        } catch (Exception e) {
            return -2;
        }
    }

    @Override
    public IPage getApiAccessLogPage(ApiAccessLogPageReqVO pageReqVO) {
        Page page = selectPage(pageReqVO);
        ApiAccessLogDO build = ApiAccessLogDO.builder().userId(isNumeric2(pageReqVO.getUserId())).endTime(pageReqVO.getBeginBeginTime() != null ? pageReqVO.getBeginBeginTime() : null)
            .userType(pageReqVO.getUserType()).applicationName(pageReqVO.getApplicationName()).requestUrl(pageReqVO.getRequestUrl())
            .beginTime(pageReqVO.getEndBeginTime() != null ? pageReqVO.getEndBeginTime() : null).duration(isNumeric1(pageReqVO.getDuration())).resultCode(isNumeric1(pageReqVO.getResultCode()))
            .isAsc(pageReqVO.getIsAsc())
            .build();
        pageReqVO.setIsAsc(null);
        LambdaQueryWrapper<ApiAccessLogDO> apiAccessLogDOLambdaQueryWrapper = buildQueryWrapper(build);
        IPage iPage = apiAccessLogMapper.selectVoPage(page, apiAccessLogDOLambdaQueryWrapper);
        return iPage;
    }

    @Override
    public List<ApiAccessLogDO> getApiAccessLogList(ApiAccessLogExportReqVO exportReqVO) {
        ApiAccessLogDO build = ApiAccessLogDO.builder().userId(exportReqVO.getUserId()).endTime(exportReqVO.getBeginBeginTime() != null ? exportReqVO.getBeginBeginTime() : null)
            .userType(exportReqVO.getUserType()).applicationName(exportReqVO.getApplicationName()).requestUrl(exportReqVO.getRequestUrl())
            .beginTime(exportReqVO.getEndBeginTime() != null ? exportReqVO.getEndBeginTime() : null).duration(exportReqVO.getDuration()).resultCode(exportReqVO.getResultCode())
            .build();

        LambdaQueryWrapper<ApiAccessLogDO> apiAccessLogDOLambdaQueryWrapper = buildQueryWrapper(build);

        return apiAccessLogMapper.selectList(apiAccessLogDOLambdaQueryWrapper);
    }

    Page selectPage(PageParam pageReqVO) {
        Page objectPage = new Page<>();
//        objectPage.setPages(pageReqVO.getPageNum());
        objectPage.setCurrent(pageReqVO.getPageNum());
        objectPage.setSize(pageReqVO.getPageSize());
        return objectPage;
    }

    private LambdaQueryWrapper<ApiAccessLogDO> buildQueryWrapper(ApiAccessLogDO bo) {

        LambdaQueryWrapper<ApiAccessLogDO> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getUserId()!=null, ApiAccessLogDO::getUserId, bo.getUserId());
        lqw.eq(bo.getUserType()!=null, ApiAccessLogDO::getUserType, bo.getUserType());
        lqw.ge(bo.getBeginTime()!=null, ApiAccessLogDO::getBeginTime, bo.getBeginTime());
        lqw.le(bo.getEndTime()!=null, ApiAccessLogDO::getEndTime, DateUtil.offsetDay(bo.getEndTime(),1) );
        lqw.like(StringUtils.isNotBlank(bo.getApplicationName()), ApiAccessLogDO::getApplicationName, bo.getApplicationName());
        lqw.like(StringUtils.isNotBlank(bo.getRequestUrl()), ApiAccessLogDO::getRequestUrl, bo.getRequestUrl());
        lqw.eq(bo.getDuration()!=null, ApiAccessLogDO::getDuration, bo.getDuration());
        lqw.eq(bo.getResultCode()!=null, ApiAccessLogDO::getResultCode, bo.getResultCode());
        return lqw;
    }

    @Async
    @Override
    public void ftests() {
        coordinteMapper.ftests();
    }
}
