package com.zhonghe.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhonghe.common.annotation.SpecifyDataSource;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.domain.entity.GisFields;
import com.zhonghe.common.core.domain.entity.GisFieldsGroup;
import com.zhonghe.common.core.domain.entity.RuleService;
import com.zhonghe.common.core.service.GisFieldsService;
import com.zhonghe.common.enums.DataSourceTypeEnum;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.system.mapper.GisFieldsGroupMapper;
import com.zhonghe.system.mapper.GisFieldsMapper;
import com.zhonghe.system.mapper.RuleServiceMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@RequiredArgsConstructor
@Service
public class GisFiledsServiceImpl implements GisFieldsService {

    private final GisFieldsGroupMapper gisFieldsGroupMapper;

    private final GisFieldsMapper gisFieldsMapper;

    @Override
    public IPage<GisFieldsGroup> selectAll(String serviceName, PageQuery pageQuery) {
        IPage iPage = gisFieldsGroupMapper.selectVoPage(pageQuery.build(), new LambdaQueryWrapper<GisFieldsGroup>()
            .like(StringUtils.isNotBlank(serviceName),GisFieldsGroup::getGroupName , serviceName));
        return iPage;
    }

    @Override
    public GisFieldsGroup selectById(Long serviceId) {
        return gisFieldsGroupMapper.selectById(serviceId);
    }

    @Override
    public int updateEntity(GisFieldsGroup gisFieldsGroup) {
        return gisFieldsGroupMapper.updateById(gisFieldsGroup);
    }

    @Override
    public int saveEntity(GisFieldsGroup gisFieldsGroup) {
        return gisFieldsGroupMapper.insert(gisFieldsGroup);
    }

    @Override
    public int deleteEntity(Long serviceId) {
        return gisFieldsGroupMapper.deleteById(serviceId);
    }

    @Override
    public List<GisFields> getFieldsList(List<Long> collect) {
        return gisFieldsMapper.selectVoList(new LambdaQueryWrapper<GisFields>()
        .in(!collect.isEmpty(),GisFields::getFieldId,collect));
    }

    @Override
    public void saveBathEntity(List<GisFields> gisFieldsList) {

        gisFieldsMapper.insertBatch(gisFieldsList);
    }

    @Override
    public IPage<GisFields> selectFieldById(Long graphId, String fieldName, PageQuery pageQuery,Long ruleId) {
        Page<GisFields> gisFieldsPage = gisFieldsMapper.selectPage(pageQuery.build(),
            new LambdaQueryWrapper<GisFields>().eq(graphId !=null,GisFields:: getGraphId,graphId)
                .like(StringUtils.isNotBlank(fieldName),GisFields::getFieldName,fieldName)
                .eq(ruleId !=null,GisFields::getServiceId,ruleId));

        return gisFieldsPage;
    }

    private final RuleServiceMapper ruleServiceMapper;

    @Override
    public void saveFields(List<GisFields> gisService,Long ruleId) {

        gisFieldsMapper.updateBatchById(gisService);

        if(!gisService.isEmpty()){
            RuleService ruleService = new RuleService();
            ruleService.setRuleId(ruleId);
            ruleService.setFieldPower(ruleId);
            ruleServiceMapper.updateById(ruleService);
        }
    }

    //切换数据源，在Service层定义
    @SpecifyDataSource(value = DataSourceTypeEnum.PLATFORM)
    public int sler() {
        return gisFieldsGroupMapper.sler();
    }
}
