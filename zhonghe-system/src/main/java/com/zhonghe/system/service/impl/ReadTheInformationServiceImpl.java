package com.zhonghe.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zhonghe.common.core.domain.entity.ReadTheInformation;
import com.zhonghe.system.mapper.ReadTheInformationMapper;
import com.zhonghe.system.service.ReadTheInformationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.List;

@RequiredArgsConstructor//可以使用final定义和注入
@Service
@Validated
@Slf4j
public class ReadTheInformationServiceImpl implements ReadTheInformationService {

    private final ReadTheInformationMapper readTheInformationMapper;

    @Override
    public List<Long> selectByUserIds(Long userId) {
        return readTheInformationMapper.selectByUserIds(userId);
    }

    @Override
    public boolean extist(Long noticeID) {
        LambdaQueryWrapper<ReadTheInformation> lqw = new LambdaQueryWrapper<ReadTheInformation>()
            .eq(ReadTheInformation::getNotedId, noticeID);

        return readTheInformationMapper.exists(lqw);
    }

    @Override
    public void seave(ReadTheInformation readTheInformation) {
        readTheInformationMapper.insert(readTheInformation);
    }

    @Override
    public void saveBath(List<ReadTheInformation> list) {
        readTheInformationMapper.insertBatch(list);
    }
}
