package com.zhonghe.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fhs.trans.service.impl.DictionaryTransService;
import com.zhonghe.common.constant.Constants;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.domain.entity.SysDictData;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.helper.IdGeneratorHelper;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.common.utils.redis.RedisUtils;
import com.zhonghe.system.mapper.SysDictDataMapper;
import com.zhonghe.system.service.ISysDictDataService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 字典 业务层处理
 *
 * <AUTHOR> Li
 */
@RequiredArgsConstructor
@Service
public class SysDictDataServiceImpl implements ISysDictDataService {

    private final SysDictDataMapper baseMapper;
    private final DictionaryTransService dictionaryTransService;


    @Override
    public List<SysDictData> selectListByDictCode(String dictCode) {
        return baseMapper.selectDictDataByType(dictCode);
    }

    @Override
    public TableDataInfo<SysDictData> selectPageDictDataList(SysDictData dictData, PageQuery pageQuery) {
        LambdaQueryWrapper<SysDictData> lqw = new LambdaQueryWrapper<SysDictData>()
            .eq(StringUtils.isNotBlank(dictData.getDictType()), SysDictData::getDictType, dictData.getDictType())
            .like(StringUtils.isNotBlank(dictData.getDictLabel()), SysDictData::getDictLabel, dictData.getDictLabel())
            .eq(StringUtils.isNotBlank(dictData.getStatus()), SysDictData::getStatus, dictData.getStatus())
            .orderByAsc(SysDictData::getDictSort);
        Page<SysDictData> page = baseMapper.selectPage(pageQuery.build(), lqw);
        return TableDataInfo.build(page);
    }

    /**
     * 根据条件分页查询字典数据
     *
     * @param dictData 字典数据信息
     * @return 字典数据集合信息
     */
    @Override
    public List<SysDictData> selectDictDataList(SysDictData dictData) {
        return baseMapper.selectList(new LambdaQueryWrapper<SysDictData>()
            .eq(StringUtils.isNotBlank(dictData.getDictType()), SysDictData::getDictType, dictData.getDictType())
            .like(StringUtils.isNotBlank(dictData.getDictLabel()), SysDictData::getDictLabel, dictData.getDictLabel())
            .eq(StringUtils.isNotBlank(dictData.getStatus()), SysDictData::getStatus, dictData.getStatus())
            .orderByAsc(SysDictData::getDictSort));
    }

    /**
     * 根据字典类型和字典键值查询字典数据信息
     *
     * @param dictType  字典类型
     * @param dictValue 字典键值
     * @return 字典标签
     */
    @Override
    public String selectDictLabel(String dictType, String dictValue) {
        return baseMapper.selectOne(new LambdaQueryWrapper<SysDictData>()
                .select(SysDictData::getDictLabel)
                .eq(SysDictData::getDictType, dictType)
                .eq(SysDictData::getDictValue, dictValue))
            .getDictLabel();
    }

    /**
     * 根据字典数据ID查询信息
     *
     * @param dictCode 字典数据ID
     * @return 字典数据
     */
    @Override
    public SysDictData selectDictDataById(Long dictCode) {
        return baseMapper.selectById(dictCode);
    }

    /**
     * 批量删除字典数据信息
     *
     * @param dictCodes 需要删除的字典数据ID
     */
    @Override
    public void deleteDictDataByIds(Long[] dictCodes) {
        for (Long dictCode : dictCodes) {
            SysDictData data = selectDictDataById(dictCode);
            baseMapper.deleteById(dictCode);
            List<SysDictData> dictDatas = baseMapper.selectDictDataByType(data.getDictType());
            RedisUtils.setCacheObject(getCacheKey(data.getDictType()), dictDatas);
        }
    }

    /**
     * 新增保存字典数据信息
     *
     * @param data 字典数据信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertDictData(SysDictData data) {
        data.setDictCode(IdGeneratorHelper.next());
        //数据标签不能重复
        saveBefore(data);
        int row = baseMapper.insert(data);
        if (row > 0) {
            List<SysDictData> dictDatas = baseMapper.selectDictDataByType(data.getDictType());
            RedisUtils.setCacheObject(getCacheKey(data.getDictType()), dictDatas);
        }
        //将字典数据转换为字典类型数据
        Map<String, String> dictMaps = baseMapper.selectList(new LambdaQueryWrapper<SysDictData>()
                .eq(SysDictData::getDictType, data.getDictType()))
            .stream().collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel));
        dictionaryTransService.refreshCache(data.getDictType(), dictMaps);
        return row;
    }

    /**
     * 修改保存字典数据信息
     *
     * @param data 字典数据信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateDictData(SysDictData data) {
        saveBefore(data);
        int row = baseMapper.updateById(data);
        if (row > 0) {
            List<SysDictData> dictDatas = baseMapper.selectDictDataByType(data.getDictType());
            RedisUtils.setCacheObject(getCacheKey(data.getDictType()), dictDatas);
        }
        //将字典数据转换为字典类型数据
        Map<String, String> dictMaps = baseMapper.selectList(new LambdaQueryWrapper<SysDictData>()
                .eq(SysDictData::getDictType, data.getDictType()))
            .stream().collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel));
        dictionaryTransService.refreshCache(data.getDictType(), dictMaps);
        return row;
    }

    private void saveBefore(SysDictData data) {
        //数据标签不能重复
        if (baseMapper.selectCount(new LambdaQueryWrapper<SysDictData>()
            .eq(SysDictData::getDictType, data.getDictType())
            .eq(SysDictData::getDictLabel, data.getDictLabel())
            .ne(SysDictData::getDictCode, data.getDictCode())) > 0) {
            throw new RuntimeException("字典标签已存在");
        }
        //值不能重复
        if (baseMapper.selectCount(new LambdaQueryWrapper<SysDictData>()
            .eq(SysDictData::getDictType, data.getDictType())
            .eq(SysDictData::getDictValue, data.getDictValue())
            .ne(SysDictData::getDictCode, data.getDictCode())) > 0) {
            throw new RuntimeException("字典键值已存在");
        }
    }

    @Override
    public List<Map<String, Object>> selectDictDataListByType(String dictType) {
        return baseMapper.selectMaps(new LambdaQueryWrapper<SysDictData>()
            .select(SysDictData::getDictLabel, SysDictData::getDictValue, SysDictData::getIsDefault)
            .eq(SysDictData::getDictType, dictType));
    }

    /**
     * 设置cache key
     *
     * @param configKey 参数键
     * @return 缓存键key
     */
    String getCacheKey(String configKey) {
        return Constants.SYS_DICT_KEY + configKey;
    }
}
