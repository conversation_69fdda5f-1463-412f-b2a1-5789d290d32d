package com.zhonghe.system.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhonghe.common.apiassace.ApiErrorLogProcessStatusEnum;
import com.zhonghe.common.apiassace.ApiErrorLogService;
import com.zhonghe.common.apiassace.PageParam;
import com.zhonghe.common.apiassace.apiaccesslog.ApiErrorLogCreateReqDTO;
import com.zhonghe.common.apiassace.apierrorlog.ApiErrorLogDO;
import com.zhonghe.common.apiassace.apierrorlog.ApiErrorLogExportReqVO;
import com.zhonghe.common.apiassace.apierrorlog.ApiErrorLogPageReqVO;
import com.zhonghe.common.core.domain.entity.SysUser;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.common.utils.redis.RedisUtils;
import com.zhonghe.system.errorcode.ErrorCodeConstants;
import com.zhonghe.system.errorcode.ServiceExceptionUtil;
import com.zhonghe.system.mapper.ApiErrorLogMapper;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * API 错误日志 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class ApiErrorLogServiceImpl implements ApiErrorLogService {

    @Resource
    private ApiErrorLogMapper apiErrorLogMapper;

    ApiErrorLogDO convert(ApiErrorLogCreateReqDTO f){
        Map<String,Object> ajax = RedisUtils.getCacheObject("ajax");
        SysUser user = (SysUser)ajax.get("user");
        ApiErrorLogDO apiAccessLogRespVO = new ApiErrorLogDO();
        apiAccessLogRespVO.setCreateTime(new Date());
        apiAccessLogRespVO.setCreator(user.getUserName());
        apiAccessLogRespVO.setUpdater(user.getUserName());
        apiAccessLogRespVO.setUserId(user.getUserId());
        apiAccessLogRespVO.setApplicationName(f.getApplicationName());
        apiAccessLogRespVO.setRequestMethod(f.getRequestMethod());
        apiAccessLogRespVO.setRequestParams(f.getRequestParams());
        apiAccessLogRespVO.setRequestUrl(f.getRequestUrl());
        apiAccessLogRespVO.setTraceId(f.getTraceId());
        apiAccessLogRespVO.setUserAgent(f.getUserAgent());
        apiAccessLogRespVO.setUserType(1);
        apiAccessLogRespVO.setUserIp(f.getUserIp());
        apiAccessLogRespVO.setExceptionFileName(f.getExceptionFileName());
        apiAccessLogRespVO.setProcessTime(new Date());
        apiAccessLogRespVO.setUpdateTime(f.getExceptionTime());
        apiAccessLogRespVO.setProcessUserId(f.getUserId());
        apiAccessLogRespVO.setExceptionClassName(f.getExceptionClassName());
        apiAccessLogRespVO.setExceptionLineNumber(f.getExceptionLineNumber());
        apiAccessLogRespVO.setExceptionMessage(f.getExceptionMessage());
        apiAccessLogRespVO.setExceptionMethodName(f.getExceptionMethodName());
        apiAccessLogRespVO.setExceptionRootCauseMessage(f.getExceptionRootCauseMessage());
        apiAccessLogRespVO.setExceptionStackTrace(f.getExceptionStackTrace());
        apiAccessLogRespVO.setExceptionName(f.getExceptionName());
        apiAccessLogRespVO.setExceptionTime(f.getExceptionTime());

        return apiAccessLogRespVO;
    }

    @Override
    public void createApiErrorLog(ApiErrorLogCreateReqDTO createDTO) {
//        ApiErrorLogDO apiErrorLog = ApiErrorLogConvert.INSTANCE.convert(createDTO);
        ApiErrorLogDO apiErrorLog = convert(createDTO);
        apiErrorLog.setProcessStatus(ApiErrorLogProcessStatusEnum.INIT.getStatus());

        apiErrorLogMapper.insert(apiErrorLog);
    }

    @Override
    public IPage getApiErrorLogPage(ApiErrorLogPageReqVO pageReqVO) {
        Page page = selectPage(pageReqVO);
        LambdaQueryWrapper<ApiErrorLogDO> apiErrorLogDOLambdaQueryWrapper = buildQueryWrapper(pageReqVO);

        return apiErrorLogMapper.selectVoPage(page,apiErrorLogDOLambdaQueryWrapper);
    }

    @Override
    public List<ApiErrorLogDO> getApiErrorLogList(ApiErrorLogExportReqVO bo) {
        LambdaQueryWrapper<ApiErrorLogDO> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getUserId()!=null, ApiErrorLogDO::getUserId, bo.getUserId());
        lqw.eq(bo.getUserType()!=null, ApiErrorLogDO::getUserType, bo.getUserType());
        lqw.like(StringUtils.isNotBlank(bo.getApplicationName()), ApiErrorLogDO::getApplicationName, bo.getApplicationName());
        lqw.like(StringUtils.isNotBlank(bo.getRequestUrl()), ApiErrorLogDO::getRequestUrl, bo.getRequestUrl());

        if(bo.getBeginExceptionTime()!=null && bo.getEndExceptionTime()!=null ){
            lqw.ge( ApiErrorLogDO::getCreateTime, bo.getBeginExceptionTime());
            lqw.le( ApiErrorLogDO::getCreateTime, DateUtil.offsetDay(bo.getEndExceptionTime(),1) );
        }
        lqw.eq(bo.getProcessStatus()!=null, ApiErrorLogDO::getProcessStatus, bo.getProcessStatus());

        return apiErrorLogMapper.selectList(lqw);
    }

    @Override
    public void updateApiErrorLogProcess(Long id, Integer processStatus, Long processUserId) {
        ApiErrorLogDO errorLog = apiErrorLogMapper.selectById(id);
        if (errorLog == null) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.API_ERROR_LOG_NOT_FOUND);
        }
        if (!ApiErrorLogProcessStatusEnum.INIT.getStatus().equals(errorLog.getProcessStatus())) {
            throw ServiceExceptionUtil.exception(ErrorCodeConstants.API_ERROR_LOG_PROCESSED);
        }
        // 标记处理
        ApiErrorLogDO build = ApiErrorLogDO.builder().id(id).processStatus(processStatus)
            .processUserId(processUserId).processTime(new Date()).build();
        build.setUpdateTime(new Date());

        apiErrorLogMapper.updateById(build);
    }

    private Long isNumeric1(String str) {
        try {
            Long.parseLong(str);
            return Long.parseLong(str);
        } catch(Exception e){
            return 0L;
        }
    }

    private LambdaQueryWrapper<ApiErrorLogDO> buildQueryWrapper(ApiErrorLogPageReqVO bo) {
        LambdaQueryWrapper<ApiErrorLogDO> lqw = Wrappers.lambdaQuery();

        if(StringUtils.isNotBlank(bo.getUserId())){
            lqw.eq(ApiErrorLogDO::getUserId,isNumeric1(bo.getUserId()));
        }
//        lqw.eq(StringUtils.isNotBlank(bo.getUserId()), ApiErrorLogDO::getUserId, bo.getUserId());
        lqw.eq(bo.getUserType()!=null, ApiErrorLogDO::getUserType, bo.getUserType());
        lqw.like(StringUtils.isNotBlank(bo.getApplicationName()), ApiErrorLogDO::getApplicationName, bo.getApplicationName());
        lqw.like(StringUtils.isNotBlank(bo.getRequestUrl()), ApiErrorLogDO::getRequestUrl, bo.getRequestUrl());
        if(bo.getBeginExceptionTime()!=null && bo.getEndExceptionTime()!=null ){
            lqw.ge( ApiErrorLogDO::getCreateTime, bo.getBeginExceptionTime());
            lqw.le( ApiErrorLogDO::getCreateTime, DateUtil.offsetDay(bo.getEndExceptionTime(),1) );
        }
        lqw.eq(bo.getProcessStatus()!=null, ApiErrorLogDO::getProcessStatus, bo.getProcessStatus());
        return lqw;
    }

    Page selectPage(PageParam pageReqVO){
        Page objectPage = new Page<>();
//        objectPage.setPages(pageReqVO.getPageNum());
        objectPage.setCurrent(pageReqVO.getPageNum());
        objectPage.setSize(pageReqVO.getPageSize());
        return objectPage;
    }
}
