package com.zhonghe.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zhonghe.system.domain.SysOssBucketDept;
import com.zhonghe.system.mapper.SysOssBucketDeptMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import com.zhonghe.system.domain.bo.SysOssBucketDeptBo;
import com.zhonghe.system.domain.vo.SysOssBucketDeptVo;
import com.zhonghe.system.service.ISysOssBucketDeptService;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 部门存储桶Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-03-31
 */
@RequiredArgsConstructor
@Service
public class SysOssBucketDeptServiceImpl implements ISysOssBucketDeptService {

    private final SysOssBucketDeptMapper baseMapper;

    /**
     * 查询部门存储桶
     */
    @Override
    public SysOssBucketDeptVo queryById(Long deptId){
        return baseMapper.selectVoById(deptId);
    }

    /**
     * 查询部门存储桶列表
     */
    @Override
    public TableDataInfo<SysOssBucketDeptVo> queryPageList(SysOssBucketDeptBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SysOssBucketDept> lqw = buildQueryWrapper(bo);
        Page<SysOssBucketDeptVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询部门存储桶列表
     */
    @Override
    public List<SysOssBucketDeptVo> queryList(SysOssBucketDeptBo bo) {
        LambdaQueryWrapper<SysOssBucketDept> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SysOssBucketDept> buildQueryWrapper(SysOssBucketDeptBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SysOssBucketDept> lqw = Wrappers.lambdaQuery();
        return lqw;
    }

    /**
     * 新增部门存储桶
     */
    @Override
    public Boolean insertByBo(SysOssBucketDeptBo bo) {
        SysOssBucketDept add = BeanUtil.toBean(bo, SysOssBucketDept.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setDeptId(add.getDeptId());
        }
        return flag;
    }

    /**
     * 修改部门存储桶
     */
    @Override
    public Boolean updateByBo(SysOssBucketDeptBo bo) {
        SysOssBucketDept update = BeanUtil.toBean(bo, SysOssBucketDept.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SysOssBucketDept entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除部门存储桶
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
