package com.zhonghe.system.service.impl;

import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.domain.entity.GisService;
import com.zhonghe.common.core.domain.entity.GraphService;
import com.zhonghe.common.core.domain.entity.RuleService;
import com.zhonghe.common.core.domain.entity.ServiceRelevGraph;
import com.zhonghe.common.core.service.GraphServiceService;
import com.zhonghe.system.mapper.CoordinteMapper;
import com.zhonghe.system.mapper.GraphServiceMapper;
import com.zhonghe.system.mapper.RuleServiceMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
public class GraphServiceServiceImpl implements GraphServiceService {

    private final GraphServiceMapper graphServiceMapper;

    private final RuleServiceMapper ruleServiceMapper;

    @Override
    public Boolean syncGraph(GisService gisService) {
        return null;
    }

    @Override
    public List<GraphService> selectAll(String serviceName, PageQuery pageQuery) {

        return graphServiceMapper.selectList();
    }

    @Override
    public GraphService selectById(Long serviceId) {
        return graphServiceMapper.selectById(serviceId);
    }

    @Override
    public int updateEntity(GraphService gisService) {
        return graphServiceMapper.updateById(gisService);
    }

    @Override
    public int saveEntity(GraphService gisService) {
        return graphServiceMapper.insert(gisService);
    }

    @Override
    public int deleteEntity(Long serviceId) {
        return graphServiceMapper.deleteById(serviceId);
    }

    @Override
    public void insertBathEntity(List<GraphService> graphServiceList) {
        graphServiceMapper.insertBatch(graphServiceList);
    }

    private final CoordinteMapper coordinteDao;

    @Override
    public void deleteButhEntity(List<GisService> gisServiceList) {
        List<GraphService> graphList =new ArrayList<>();
        for (GisService gisService : gisServiceList) {
            List<GraphService> serviceGraphList = gisService.getServiceGraphList();
            graphList.addAll(serviceGraphList);
            if(!serviceGraphList.isEmpty()){
                for (GraphService graphService : serviceGraphList) {
                    List<GraphService> children = graphService.getChildren();
                    graphList.addAll(children);
                }
            }

        }
        List<Long> collect = graphList.stream().map(GraphService::getGraphId).collect(Collectors.toList());
        coordinteDao.deleteBatchRelevIds(collect);
    }

    @Override
    public void updateButhGraph(Long ruleId) {
        RuleService ruleService = new RuleService();
        ruleService.setRuleId(ruleId);
        ruleService.setGraphPower(ruleId);
        ruleServiceMapper.updateById(ruleService);
    }

    @Override
    public void saveButhEntity(List<ServiceRelevGraph> paramsList) {
        coordinteDao.saveButhRelevEntity(paramsList);
    }
}
