package com.zhonghe.system.service;

import com.zhonghe.system.domain.vo.SysOssPowerRelationVo;
import com.zhonghe.system.domain.bo.SysOssPowerRelationBo;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 存储桶部门关系Service接口
 *
 * <AUTHOR>
 * @date 2023-03-31
 */
public interface ISysOssPowerRelationService {

    /**
     * 查询存储桶部门关系
     */
    SysOssPowerRelationVo queryById(Long bucketDeptId);

    /**
     * 查询存储桶部门关系列表
     */
    TableDataInfo<SysOssPowerRelationVo> queryPageList(SysOssPowerRelationBo bo, PageQuery pageQuery);

    /**
     * 查询存储桶部门关系列表
     */
    List<SysOssPowerRelationVo> queryList(SysOssPowerRelationBo bo);

    /**
     * 修改存储桶部门关系
     */
    Boolean insertByBo(SysOssPowerRelationBo bo);

    /**
     * 修改存储桶部门关系
     */
    Boolean updateByBo(SysOssPowerRelationBo bo);

    /**
     * 校验并批量删除存储桶部门关系信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
