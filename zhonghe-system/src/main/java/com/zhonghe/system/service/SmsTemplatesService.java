package com.zhonghe.system.service;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.sms.entity.SmsRequestEbtity;
import com.zhonghe.system.mapper.SmsTemplatesMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@RequiredArgsConstructor
@Slf4j
@Service
public class SmsTemplatesService {

    private final SmsTemplatesMapper baseMapper;

    public List<SmsRequestEbtity> getSmsList(SmsRequestEbtity smsRequestEbtity){
        LambdaQueryWrapper<SmsRequestEbtity> lqw = buildQueryWrapper(smsRequestEbtity);
        return baseMapper.selectVoList(lqw);
    }

    public Page<SmsRequestEbtity> getAll(SmsRequestEbtity smsRequestEbtity, PageQuery pageQuery){
        LambdaQueryWrapper<SmsRequestEbtity> lqw = buildQueryWrapper(smsRequestEbtity);
        Page<SmsRequestEbtity> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return result;
    }


    private LambdaQueryWrapper<SmsRequestEbtity> buildQueryWrapper(SmsRequestEbtity bo) {
        LambdaQueryWrapper<SmsRequestEbtity> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getOutId()), SmsRequestEbtity::getOutId, bo.getOutId());
        lqw.like(StringUtils.isNotBlank(bo.getTemplateContent()), SmsRequestEbtity::getTemplateContent, bo.getTemplateContent());
        lqw.eq(bo.getIdOpen()!=null, SmsRequestEbtity::getIdOpen, bo.getIdOpen());
        lqw.like(StringUtils.isNotBlank(bo.getSmsType()), SmsRequestEbtity::getSmsType, bo.getSmsType());
        lqw.like(StringUtils.isNotBlank(bo.getTemplateCode()), SmsRequestEbtity::getTemplateCode, bo.getTemplateCode());
        lqw.like(StringUtils.isNotBlank(bo.getTestChannel()), SmsRequestEbtity::getTestChannel, bo.getTestChannel());
        lqw.eq(bo.getCreateTime() != null, SmsRequestEbtity::getCreateTime, bo.getCreateTime());
        lqw.like(StringUtils.isNotBlank(bo.getResourceOwnerAccount()), SmsRequestEbtity::getResourceOwnerAccount, bo.getResourceOwnerAccount());
        Map<String, Object> params = bo.getParams();
        if(params ==null)return lqw;
        if(params.get("beginTime")!=null && params.get("endTime")!=null){
            lqw.between(SmsRequestEbtity::getCreateTime, DateUtil.parse(params.get("beginTime").toString()),
                DateUtil.offsetDay(DateUtil.parse(params.get("endTime").toString()), 1));
        }
        return lqw;
    }

    public SmsRequestEbtity getById(String id){
        return baseMapper.selectById(id);
    }

    public int UpdateSmsRequestEbtity(SmsRequestEbtity smsRequestEbtity){
        return baseMapper.updateById(smsRequestEbtity);
    }

    public int SaveSms(SmsRequestEbtity smsRequestEbtity){
        return baseMapper.insert(smsRequestEbtity);
    }

    public int DeleteSms(String id){
        return baseMapper.deleteById(id);
    }


}
