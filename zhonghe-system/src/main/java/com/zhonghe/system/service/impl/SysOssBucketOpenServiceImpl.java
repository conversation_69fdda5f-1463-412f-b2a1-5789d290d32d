package com.zhonghe.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zhonghe.system.domain.SysOssBucketOpen;
import com.zhonghe.system.domain.bo.SysOssBucketOpenBo;
import com.zhonghe.system.domain.vo.SysOssBucketOpenVo;
import com.zhonghe.system.domain.vo.SysOssBucketSecVo;
import com.zhonghe.system.mapper.SysOssBucketOpenMapper;
import com.zhonghe.system.service.ISysOssBucketOpenService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;


import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 存储文件开放表Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-04-03
 */
@RequiredArgsConstructor
@Service
public class SysOssBucketOpenServiceImpl implements ISysOssBucketOpenService {

    private final SysOssBucketOpenMapper baseMapper;

    @Override
    public SysOssBucketSecVo queryByBucketId(Long bucketId) {
        return baseMapper.selectBucketOpen(bucketId);
    }

    /**
     * 查询存储文件开放表
     */
    @Override
    public SysOssBucketOpenVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询存储文件开放表列表
     */
    @Override
    public TableDataInfo<SysOssBucketOpenVo> queryPageList(SysOssBucketOpenBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SysOssBucketOpen> lqw = buildQueryWrapper(bo);
        Page<SysOssBucketOpenVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询存储文件开放表列表
     */
    @Override
    public List<SysOssBucketOpenVo> queryList(SysOssBucketOpenBo bo) {
        LambdaQueryWrapper<SysOssBucketOpen> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SysOssBucketOpen> buildQueryWrapper(SysOssBucketOpenBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SysOssBucketOpen> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getBucketId() != null, SysOssBucketOpen::getBucketId, bo.getBucketId());
        lqw.eq(StringUtils.isNotBlank(bo.getExpiredTime()), SysOssBucketOpen::getExpiredTime, bo.getExpiredTime());
        return lqw;
    }

    /**
     * 新增存储文件开放表
     */
    @Override
    public Boolean insertByBo(SysOssBucketOpenBo bo) {
        SysOssBucketOpen add = BeanUtil.toBean(bo, SysOssBucketOpen.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改存储文件开放表
     */
    @Override
    public Boolean updateByBo(SysOssBucketOpenBo bo) {
        SysOssBucketOpen update = BeanUtil.toBean(bo, SysOssBucketOpen.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SysOssBucketOpen entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除存储文件开放表
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
