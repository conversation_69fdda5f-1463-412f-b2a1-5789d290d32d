package com.zhonghe.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhonghe.common.constant.Constants;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.domain.entity.SysDept;
import com.zhonghe.common.core.domain.entity.SysUser;
import com.zhonghe.common.core.domain.vo.SysDeptTreeVo;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.enums.OssDataScopeType;
import com.zhonghe.common.exception.oss.SysOssException;
import com.zhonghe.common.helper.LoginHelper;
import com.zhonghe.common.utils.BeanCopyUtils;
import com.zhonghe.common.utils.FileSizeUtil;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.common.utils.TreeBuildUtils;
import com.zhonghe.common.utils.file.FileUtils;
import com.zhonghe.oss.constant.OssConstant;
import com.zhonghe.system.domain.SysOss;
import com.zhonghe.system.domain.SysOssBucket;
import com.zhonghe.system.domain.SysOssBucketDept;
import com.zhonghe.system.domain.SysOssBucketOpen;
import com.zhonghe.system.domain.SysOssBucketSec;
import com.zhonghe.system.domain.SysOssPowerRelation;
import com.zhonghe.system.domain.bo.SysOssBucketBo;
import com.zhonghe.system.domain.bo.SysOssBucketPowersBo;
import com.zhonghe.system.domain.dto.BucketTreeDto;
import com.zhonghe.system.domain.dto.SysOssBucketTreeDTO;
import com.zhonghe.system.domain.vo.SysOssBucketDeptVo;
import com.zhonghe.system.domain.vo.SysOssBucketPowerVo;
import com.zhonghe.system.domain.vo.SysOssBucketSecVo;
import com.zhonghe.system.domain.vo.SysOssBucketVo;
import com.zhonghe.system.domain.vo.SysOssMenuTreeAndPowerVo;
import com.zhonghe.system.domain.vo.SysOssPowerRelationVo;
import com.zhonghe.system.event.OssCalculatedBucketCapacityEvent;
import com.zhonghe.system.event.SysOssBucketDeleteBucketEvent;
import com.zhonghe.system.event.SysOssBucketDeleteEvent;
import com.zhonghe.system.event.SysOssCreateBucketSecDBEntityEvent;
import com.zhonghe.system.mapper.SysDeptMapper;
import com.zhonghe.system.mapper.SysOssBucketDeptMapper;
import com.zhonghe.system.mapper.SysOssBucketMapper;
import com.zhonghe.system.mapper.SysOssBucketOpenMapper;
import com.zhonghe.system.mapper.SysOssBucketSecMapper;
import com.zhonghe.system.mapper.SysOssPowerRelationMapper;
import com.zhonghe.system.mapper.SysUserMapper;
import com.zhonghe.system.service.ISysDeptService;
import com.zhonghe.system.service.ISysOssBucketService;
import com.zhonghe.system.service.ISysOssPowerDefineService;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 对象存储桶Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-03-31
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class SysOssBucketServiceImpl implements ISysOssBucketService {

    private final SysOssBucketMapper baseMapper;
    private final ISysDeptService sysDeptService;
    private final SysOssBucketDeptMapper ossBucketDeptMapper;
    private final SysOssBucketSecMapper ossBucketSecMapper;
    private final SysOssPowerRelationMapper powerRelationMapper;
    private final ISysOssPowerDefineService defineService;
    private final SysOssBucketOpenMapper ossBucketOpenMapper;

    private final SysDeptMapper sysDeptMapper;

    private final SysUserMapper sysUserMapper;
    private final ApplicationEventPublisher applicationEventPublisher;

    /**
     * 查询对象存储桶
     */
    @Override
    public SysOssBucketVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询对象存储桶列表
     */
    @Override
    public TableDataInfo<SysOssBucketVo> queryPageList(SysOssBucketBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SysOssBucket> lqw = buildQueryWrapper(bo);
        Page<SysOssBucketVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询对象存储桶列表
     */
    @Override
    public List<SysOssBucketVo> queryList(SysOssBucketBo bo) {
        LambdaQueryWrapper<SysOssBucket> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SysOssBucket> buildQueryWrapper(SysOssBucketBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SysOssBucket> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getNickName()), SysOssBucket::getNickName, bo.getNickName());
        lqw.eq(bo.getBucketType() != null, SysOssBucket::getBucketType, bo.getBucketType());
        lqw.like(StringUtils.isNotBlank(bo.getOriginalName()), SysOssBucket::getOriginalName, bo.getOriginalName());
        lqw.eq(StringUtils.isNotBlank(bo.getFileSuffix()), SysOssBucket::getFileSuffix, bo.getFileSuffix());
        lqw.eq(bo.getParentId() != null, SysOssBucket::getParentId, bo.getParentId());
        lqw.eq(bo.getBucketId() != null, SysOssBucket::getBucketId, bo.getBucketId());
        lqw.eq(ObjectUtil.isNotNull(bo.getExpiredTime()), SysOssBucket::getExpiredTime, bo.getExpiredTime());
        lqw.eq(StringUtils.isNotBlank(bo.getTags()), SysOssBucket::getTags, bo.getTags());
        lqw.eq(bo.getOwnerId() != null, SysOssBucket::getOwnerId, bo.getOwnerId());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), SysOssBucket::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增对象存储桶
     */
    @Override
    @Transactional
    public Boolean insertByBo(SysOssBucketBo bo) {
        SysOssBucket add = BeanUtil.toBean(bo, SysOssBucket.class);
        add.setOwnerId(0L);
        add.setStatus(Constants.STATUS);
        boolean flag = baseMapper.insert(add) > 0;
        bo.setId(add.getId());
        if (flag) {
            if (bo.getBucketType() == OssConstant.BUCKET_TYPE_BUCKET) {
                add.setParentId(0L);
                add.setBucketId(add.getId());
                add.setOwnerId(LoginHelper.getUserId());
                double size = FileUtils.convertFileSize(bo.getCapacityType(), bo.getMaxCapacity());
                //查询云盘已分配的量
                double sum = baseMapper.selectList().stream().filter(a -> ObjectUtil.isNotNull(a.getMaxCapacity())).mapToDouble(SysOssBucket::getMaxCapacity).sum();
                if (sum + size > OssConstant.CLOUD_DISK_TOTAL_CAPACITY) {
                    throw new SysOssException("sys.oss.bucket.undercapacity");
                }
                add.setMaxCapacity(size);
                applicationEventPublisher.publishEvent(new SysOssCreateBucketSecDBEntityEvent(this, add.getId(), Long.toHexString(System.currentTimeMillis()), null, true));
            } else {
                SysOssBucket sysOssBucket = baseMapper.selectOne(new LambdaQueryWrapper<SysOssBucket>().eq(SysOssBucket::getId, bo.getParentId()));
                SysOssBucketVo parentBucket = baseMapper.selectVoOne(Wrappers.<SysOssBucket>lambdaQuery().eq(SysOssBucket::getBucketId, sysOssBucket.getBucketId()).eq(SysOssBucket::getParentId, 0));
                add.setMaxCapacity(parentBucket.getMaxCapacity());
                if (!ObjectUtil.isNull(parentBucket)) {
                    add.setBucketId(parentBucket.getBucketId());
                    add.setOwnerId(parentBucket.getOwnerId());
                } else {
                    throw new SysOssException("sys.oss.bucket.parent.error", bo.getNickName());
                }
                applicationEventPublisher.publishEvent(new SysOssCreateBucketSecDBEntityEvent(this, add.getId(), Long.toHexString(System.currentTimeMillis()), null, false));
            }
            flag = baseMapper.update(null, new LambdaUpdateWrapper<SysOssBucket>()
                .set(bo.getBucketType() == OssConstant.BUCKET_TYPE_BUCKET, SysOssBucket::getParentId, add.getParentId())
                .set(SysOssBucket::getMaxCapacity, add.getMaxCapacity())
                .set(SysOssBucket::getOwnerId, add.getOwnerId())
                .set(SysOssBucket::getBucketId, add.getBucketId()).eq(SysOssBucket::getId, add.getId())) == 1;
            // 只有桶节点才会创建时刻配置权限
            if (add.getBucketType() == OssConstant.BUCKET_TYPE_BUCKET && Objects.equals(bo.getPersonal(), OssConstant.BUCKET_PERSONAL_NO)) {
                if (OssConstant.BUCKET_ALL_READ.equals(bo.getAll())) {
                    this.createAllDeptPowers(bo);
                }
                if (!CollectionUtils.isEmpty(bo.getPowers())) {
                    this.createPowers(bo);
                }
            }
            if (bo.getBucketType() != OssConstant.BUCKET_TYPE_BUCKET) {
                //创建文件夹自动赋予上级文件夹的权限
                this.addParentPowers(add);
            }

        }

        return flag;
    }

    @Override
    @Async
    @Transactional
    public void updateChildPowers(SysOssBucketBo ossBucket) {
        //查询父级
        SysOssBucket parentBucket = baseMapper.selectById(ossBucket.getId());
        if (parentBucket.getBucketType() == OssConstant.BUCKET_TYPE_BUCKET) {
            return;
        }
        //查询子级列表
        List<SysOssBucketTreeDTO> allBuckets = baseMapper.selectList(Wrappers.<SysOssBucket>lambdaQuery().select(SysOssBucket::getId, SysOssBucket::getParentId, SysOssBucket::getNickName)).stream().map(a -> {
            SysOssBucketTreeDTO tree = new SysOssBucketTreeDTO();
            tree.setId(a.getId());
            tree.setParentId(a.getParentId());
            tree.setBucketName(a.getNickName());
            return tree;
        }).collect(Collectors.toList());

        List<SysOssBucketTreeDTO> childrenBuckets = getChildDepartments(allBuckets, ossBucket.getId());
        for (SysOssBucketTreeDTO children : childrenBuckets) {
            List<Long> bucketDeptIds = ossBucketDeptMapper.selectList(Wrappers.<SysOssBucketDept>lambdaQuery().eq(SysOssBucketDept::getBucketId, children.getId())).stream().map(SysOssBucketDept::getId).collect(Collectors.toList());
            //移除权限相关
            powerRelationMapper.delete(Wrappers.<SysOssPowerRelation>lambdaQuery().in(CollectionUtils.isNotEmpty(bucketDeptIds), SysOssPowerRelation::getBucketDeptId, bucketDeptIds));
            //移除部门权限相关
            ossBucketDeptMapper.delete(Wrappers.<SysOssBucketDept>lambdaQuery().eq(SysOssBucketDept::getBucketId, children.getId()));
            //创建权限
            ossBucket.setId(children.getId());
            this.createPowers(ossBucket);
        }
    }

    public static List<SysOssBucketTreeDTO> getChildDepartments(List<SysOssBucketTreeDTO> departments, Long parentId) {
        List<SysOssBucketTreeDTO> childDepartments = new ArrayList<>();
        for (SysOssBucketTreeDTO department : departments) {
            if (department.getParentId().equals(parentId)) {
                childDepartments.add(department);
                childDepartments.addAll(getChildDepartments(departments, department.getId()));
            }
        }
        return childDepartments;
    }

    /**
     * 添加父级权限
     *
     * @param add
     */
    @Override
    public void addParentPowers(SysOssBucket add) {
        Map<Long, List<Long>> folderPowers = new HashMap<>();
        SysOssBucketVo parentBucket = baseMapper.selectVoOne(Wrappers.<SysOssBucket>lambdaQuery().eq(SysOssBucket::getId, add.getParentId()));
        List<SysOssBucketDeptVo> sysOssBucketDeptVos = ossBucketDeptMapper.selectVoList(Wrappers.<SysOssBucketDept>lambdaQuery().eq(SysOssBucketDept::getBucketId, parentBucket.getId()));
        sysOssBucketDeptVos.forEach(s -> {
            //查询权限
            List<Long> powers = powerRelationMapper.selectVoList(Wrappers.<SysOssPowerRelation>lambdaQuery()
                    .eq(SysOssPowerRelation::getBucketDeptId, s.getId()))
                .stream().map(SysOssPowerRelationVo::getId).collect(Collectors.toList());
            folderPowers.put(s.getDeptId(), powers);
        });
        folderPowers.forEach((k, v) -> {
            synchronized (this) {
                SysOssBucketDept sysOssBucketDept = new SysOssBucketDept();
                sysOssBucketDept.setBucketId(add.getId());
                sysOssBucketDept.setDeptId(k);
                ossBucketDeptMapper.insert(sysOssBucketDept);
                for (Long id : v) {
                    SysOssPowerRelation sysOssPowerRelation = new SysOssPowerRelation();
                    sysOssPowerRelation.setBucketDeptId(sysOssBucketDept.getId());

                    SysOssPowerRelation ossPowerRelation = powerRelationMapper.selectOne(Wrappers.<SysOssPowerRelation>lambdaQuery().eq(SysOssPowerRelation::getId, id));
                    sysOssPowerRelation.setPowerDefineId(ossPowerRelation.getPowerDefineId());
                    powerRelationMapper.insert(sysOssPowerRelation);
                }
            }
        });
    }

    private void createAllDeptPowers(SysOssBucketBo bo) {
        Set<Long> deptIds = sysDeptService.selectAllDeptIdsWithOutDataScope();
        SysOssBucketPowersBo power = new SysOssBucketPowersBo();
        power.setPower(OssDataScopeType.READ.getCode());
        power.setDeptIds(deptIds);
        bo.setPowers(Collections.singletonList(power));
    }

    private void createPowers(SysOssBucketBo bo) {
        // 校验是否有部门的校验权限
        Set<Long> deptIds = new HashSet<>();
        Map<Long, List<String>> powerMap = new HashMap<>();
        List<SysOssBucketDept> bucketDepts = new ArrayList<>();
        bo.getPowers().forEach(p -> {
            //deptIds.add(0L);//管理员特殊处理
            List<SysDept> sysDepts = sysDeptMapper.selectList();
            p.getDeptIds().forEach(d -> {
                SysDept sysDept = sysDeptService.selectDeptById(d);
                deptIds.addAll(TreeBuildUtils.getDeptUpList(sysDepts, sysDept));
            });
            deptIds.addAll(p.getDeptIds());
            if (CollectionUtils.isNotEmpty(p.getDeptIds())) {
                deptIds.forEach(pd -> {
                    if (!powerMap.containsKey(pd)) {
                        powerMap.put(pd, new ArrayList<>());
                    }
                    powerMap.get(pd).add(p.getPower());
                });
            }
        });
        // 不检查部门权限范围
        /*if (!sysDeptService.checkNoticeDeptPowerScope(deptIds)) {
            throw new SysOssException("dept.power.error", "");
        }*/
        deptIds.forEach(d -> {
            SysOssBucketDept ossBucketDept = new SysOssBucketDept();
            ossBucketDept.setDeptId(d);
            ossBucketDept.setBucketId(bo.getId());

            bucketDepts.add(ossBucketDept);
        });
        ossBucketDeptMapper.insertBatch(bucketDepts);
        Map<Long, Long> deptIdRelations = bucketDepts.stream().collect(Collectors.toMap(SysOssBucketDept::getDeptId, SysOssBucketDept::getId, (v1, v2) -> v1));
        // 生成具体的权限关系
        List<SysOssPowerRelation> relations = new ArrayList<>();
        deptIdRelations.forEach((key, value) -> {
            if (powerMap.containsKey(key)) {
                for (String power : powerMap.get(key)) {
                    Long powerId = defineService.getIdByPower(power);
                    SysOssPowerRelation r = new SysOssPowerRelation();
                    r.setPowerDefineId(powerId);
                    r.setBucketDeptId(value);

                    relations.add(r);
                }
            }
        });
        powerRelationMapper.insertBatch(relations);
    }

    /**
     * 修改对象存储桶
     */
    @Override
    public Boolean updateByBo(SysOssBucketBo bo) {
        LambdaUpdateWrapper<SysOssBucket> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(SysOssBucket::getId, bo.getId());
        SysOssBucket oldData = baseMapper.selectById(bo.getId());
        // 重复名称查询
        Long ct = baseMapper.selectCount(new LambdaQueryWrapper<SysOssBucket>()
            .eq(SysOssBucket::getParentId, oldData.getParentId())
            .eq(SysOssBucket::getNickName, bo.getNickName())
        );
        if (ct == 0) {
            updateWrapper.set(SysOssBucket::getNickName, bo.getNickName());
        } else {
            throw new SysOssException("sys.oss.repeated.name", bo.getNickName());
        }

        updateWrapper.set(ObjectUtil.isNotNull(bo.getStatus()), SysOssBucket::getStatus, bo.getStatus());
        updateWrapper.set(ObjectUtil.isNotNull(bo.getTags()), SysOssBucket::getTags, bo.getTags());
        updateWrapper.set(!ObjectUtil.isEmpty(bo.getExpiredTime()), SysOssBucket::getExpiredTime, bo.getExpiredTime());

        if (bo.getExpiredTime() != null) {
            // TODO 发送minio配置过期时间
            /*if (oldData.getBucketType() == OssConstant.BUCKET_TYPE_BUCKET) {
                applicationEventPublisher.publishEvent(new SysOssBucketDeleteBucketEvent(this, Collections.singleton(oldData.getId())));
            } else if (oldData.getBucketType() == OssConstant.BUCKET_TYPE_FOLD) {

            } else if (oldData.getBucketType() == OssConstant.BUCKET_TYPE_FILE) {

            }*/
        }
        return baseMapper.update(null, updateWrapper) > 0;
    }

    /**
     * 批量删除对象存储桶
     */
    @Override
    @Transactional
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        Set<Long> deleteIds = new HashSet<>();
        List<SysOssBucket> sysOssFiles = new ArrayList<>();

        Set<Long> deleteBucketIds = new HashSet<>();
        if (isValid) {
            List<SysOssBucket> ossBuckets = baseMapper.selectList(new LambdaQueryWrapper<SysOssBucket>()
                .in(SysOssBucket::getId, ids));

            ossBuckets.forEach(ob -> {
                if (ob.getBucketType() == OssConstant.BUCKET_TYPE_BUCKET) {
                    baseMapper.delete(new LambdaQueryWrapper<SysOssBucket>()
                        .eq(SysOssBucket::getBucketId, ob.getId()));
                    deleteBucketIds.add(ob.getId());
                } else if (ob.getBucketType() == OssConstant.BUCKET_TYPE_FOLD) {
                    this.searchDeepDeleteIds(ob.getId(), deleteIds);
                    deleteIds.add(ob.getId());
                } else {
                    sysOssFiles.add(ob);
                    deleteIds.add(ob.getId());
                }
            });
        }
        //删除桶
        if (CollectionUtils.isNotEmpty(deleteBucketIds)) {
            ossBucketSecMapper.delete(Wrappers.<SysOssBucketSec>lambdaQuery().in(SysOssBucketSec::getBucketId, deleteBucketIds));
            applicationEventPublisher.publishEvent(new SysOssBucketDeleteBucketEvent(this, deleteBucketIds));
        }
        //删除文件
        if (deleteIds.size() > 0) {
            List<SysOssBucketDept> deleteBucketDept = ossBucketDeptMapper.selectList(new LambdaQueryWrapper<SysOssBucketDept>()
                .select(SysOssBucketDept::getId)
                .in(SysOssBucketDept::getBucketId, deleteIds));
            List<Long> relationDelete = deleteBucketDept.stream().map(SysOssBucketDept::getId).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(relationDelete)) {
                powerRelationMapper.deleteBatchIds(relationDelete);
            }
            if (CollectionUtils.isNotEmpty(deleteIds)) {
                ossBucketDeptMapper.delete(new LambdaQueryWrapper<SysOssBucketDept>().in(SysOssBucketDept::getBucketId, deleteIds));
            }
            //计算容量大小
            if (CollectionUtils.isNotEmpty(deleteIds)) {
                if (CollectionUtils.isNotEmpty(ids)) {
                    SysOssBucket ossBucket = baseMapper.selectById(deleteIds.iterator().next());
                    SysOssBucket sysOssBucket = baseMapper.selectOne(Wrappers.<SysOssBucket>lambdaQuery().eq(SysOssBucket::getBucketId, ossBucket.getBucketId()).eq(SysOssBucket::getParentId, 0));
                    baseMapper.deleteBatchIds(deleteIds);
                    ossBucketSecMapper.delete(Wrappers.<SysOssBucketSec>lambdaQuery().in(SysOssBucketSec::getBucketId, deleteIds));
                    applicationEventPublisher.publishEvent(new OssCalculatedBucketCapacityEvent(this, sysOssBucket.getId()));
                }
            }

            // 全桶删除
            if (CollectionUtils.isNotEmpty(deleteBucketDept)) {
                applicationEventPublisher.publishEvent(new SysOssBucketDeleteBucketEvent(this, deleteBucketIds));
            }
            // 删除操作记录，文件延迟删除
            List<Long> fileIds = sysOssFiles.stream().filter(ob -> ob.getBucketType() == OssConstant.BUCKET_TYPE_FILE).map(SysOssBucket::getId).collect(Collectors.toList());
            applicationEventPublisher.publishEvent(new SysOssBucketDeleteEvent(this, fileIds));
        }
        return true;
    }


    private void searchDeepDeleteIds(Long parentId, Set<Long> need2Del) {
        List<SysOssBucket> ossBuckets = baseMapper.selectList(new LambdaQueryWrapper<SysOssBucket>()
            .select(SysOssBucket::getId, SysOssBucket::getBucketType, SysOssBucket::getPersonal)
            .in(SysOssBucket::getParentId, parentId));

        need2Del.add(parentId);
        for (SysOssBucket ob : ossBuckets) {
            if (ob.getBucketType() == OssConstant.BUCKET_TYPE_FOLD) {
                this.searchDeepDeleteIds(ob.getId(), need2Del);
            } else {
                need2Del.add(ob.getId());
            }
        }

    }

    @SneakyThrows
    @Override
    public Map<String, Object> queryBucketList() {
        Map<String, Object> result = new HashMap<>();
        //查询自己的云盘
        LambdaQueryWrapper<SysOssBucket> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(SysOssBucket::getNickName, SysOssBucket::getOwnerId, SysOssBucket::getId, SysOssBucket::getBucketType, SysOssBucket::getBucketId, SysOssBucket::getPersonal, SysOssBucket::getMaxCapacity);
        queryWrapper.eq(SysOssBucket::getBucketType, OssConstant.BUCKET_TYPE_BUCKET);
        if (!LoginHelper.isAdmin()) {
            queryWrapper.eq(SysOssBucket::getOwnerId, LoginHelper.getUserId());
        }
        queryWrapper.and(qw -> {
            qw.isNull(SysOssBucket::getExpiredTime).or().gt(SysOssBucket::getExpiredTime, new Date());
        });
        queryWrapper.orderByAsc(SysOssBucket::getBucketType).orderByDesc(SysOssBucket::getCreateTime);
        List<SysOssBucket> sysOssBuckets = baseMapper.selectList(queryWrapper);
        //没有云盘
        Map<String, Object> map = new HashMap<>();
        //查询全部云盘
        int bucketSize = baseMapper.selectList(Wrappers.<SysOssBucket>lambdaQuery().eq(SysOssBucket::getBucketType, OssConstant.BUCKET_TYPE_BUCKET)).size();
        //查询文件数量
        int bucketFileSize = baseMapper.selectList(Wrappers.<SysOssBucket>lambdaQuery().eq(SysOssBucket::getBucketType, OssConstant.BUCKET_TYPE_FILE)).size();
        //云盘已分配容量
        double bucketAllotedSize = baseMapper.selectList(Wrappers.<SysOssBucket>lambdaQuery().eq(SysOssBucket::getBucketType, OssConstant.BUCKET_TYPE_BUCKET)).stream().filter(a -> ObjectUtil.isNotNull(a.getMaxCapacity())).mapToDouble(SysOssBucket::getMaxCapacity).sum();
        //云盘实际已使用容量
        double bucketUsedSize = ossBucketSecMapper.selectList().stream().filter(a -> ObjectUtil.isNotNull(a.getCurrentCapacity())).mapToDouble(SysOssBucketSec::getCurrentCapacity).sum();
        //文件数量
        AtomicReference<Integer> fileSizeAll = new AtomicReference<>(0);
        if (sysOssBuckets.size() > 0) {
            List<SysOssBucketSecVo> sysOssBucketSecVos = ossBucketSecMapper.selectVoList(Wrappers.<SysOssBucketSec>lambdaQuery()
                .in(SysOssBucketSec::getBucketId, sysOssBuckets.stream().map(SysOssBucket::getId).collect(Collectors.toList())));
            sysOssBuckets.forEach(sob -> {
                long fileSize = baseMapper.selectVoList(Wrappers.<SysOssBucket>lambdaQuery()
                    .select(SysOssBucket::getId)
                    .eq(SysOssBucket::getBucketId, sob.getBucketId())
                    .eq(SysOssBucket::getBucketType, OssConstant.BUCKET_TYPE_FILE)).stream().count();
                fileSizeAll.updateAndGet(v -> v + (int) fileSize);
                for (SysOssBucketSecVo sysOssBucketSecVo : sysOssBucketSecVos) {
                    if (sob.getId().equals(sysOssBucketSecVo.getBucketId()) && StrUtil.isNotBlank(String.valueOf(sysOssBucketSecVo.getCurrentCapacity()))) {
                        BeanCopyUtils.copy(sob, sysOssBucketSecVo);
                        sysOssBucketSecVo.setFileSize((int) fileSize);
                        sysOssBucketSecVo.setUsedCapacity(FileSizeUtil.formatFileSize(sysOssBucketSecVo.getCurrentCapacity()));
                        sysOssBucketSecVo.setTotalCapacity(FileSizeUtil.formatFileSize(sob.getMaxCapacity()));
                        String ownerName = Optional.ofNullable(sysUserMapper.selectUserById(sob.getOwnerId()))
                            .map(SysUser::getNickName)
                            .orElse(null);
                        sysOssBucketSecVo.setOwnerName(ownerName);
                    }
                }
            });
            result.put(OssConstant.BUCKET_NAME_MINE, sysOssBucketSecVos);
            map.put(OssConstant.BUCKET_USED_SIZE, FileSizeUtil.formatFileSize(bucketUsedSize));
            map.put(OssConstant.BUCKET_BASE_USED_SIZE, bucketUsedSize);
            map.put(OssConstant.BUCKET_FILE_COUNT, fileSizeAll.get());
            map.put(OssConstant.BUCKET_COUNT, sysOssBuckets.size());
        } else {
            result.put(OssConstant.BUCKET_NAME_MINE, new ArrayList<>());
            map.put(OssConstant.BUCKET_USED_SIZE, FileSizeUtil.formatFileSize(bucketUsedSize));
            map.put(OssConstant.BUCKET_BASE_USED_SIZE, bucketUsedSize);
            map.put(OssConstant.BUCKET_FILE_COUNT, 0);
            map.put(OssConstant.BUCKET_COUNT, 0);
        }
        map.put(OssConstant.BUCKET_TOTAL_SIZE, FileSizeUtil.formatFileSize(Double.valueOf(OssConstant.CLOUD_DISK_TOTAL_CAPACITY)));
        map.put(OssConstant.BUCKET_REMAIN_SIZE, FileSizeUtil.formatFileSize(OssConstant.CLOUD_DISK_TOTAL_CAPACITY - bucketAllotedSize));
        map.put(OssConstant.BUCKET_REMAIN_Max_SIZE, OssConstant.CLOUD_DISK_TOTAL_CAPACITY - bucketAllotedSize);
        result.putAll(map);
        // 可访问的桶
        List<Long> ids = baseMapper.selectBucketIdsByDeptId(LoginHelper.getDeptId());
        if (CollectionUtils.isNotEmpty(ids)) {
            List<SysOssBucket> others = baseMapper.selectList(new LambdaQueryWrapper<SysOssBucket>()
                .select(SysOssBucket::getId, SysOssBucket::getNickName, SysOssBucket::getBucketId, SysOssBucket::getOwnerId)
                .in(SysOssBucket::getId, ids)
                .eq(SysOssBucket::getPersonal, OssConstant.NO_PERSONAL)
                .ne(SysOssBucket::getOwnerId, LoginHelper.getUserId())
                .and(qw -> {
                    qw.isNull(SysOssBucket::getExpiredTime).or().gt(SysOssBucket::getExpiredTime, new Date());
                }).orderByAsc(SysOssBucket::getBucketType).orderByDesc(SysOssBucket::getCreateTime));
            ;
            others.forEach(a -> {
                String ownerName = Optional.ofNullable(sysUserMapper.selectUserById(a.getOwnerId()))
                    .map(SysUser::getNickName)
                    .orElse(null);
                a.setOwnerName(ownerName);
            });
            result.put(OssConstant.BUCKET_NAME_VIEW, others);
        } else {
            result.put(OssConstant.BUCKET_NAME_VIEW, new ArrayList<>());
        }
        return result;
    }

    @Override
    public Object queryNextTree(Long parentId, String nickName, String isAsc) {

        BucketTreeDto bucketTreeDto = new BucketTreeDto();

        LambdaQueryWrapper<SysOssBucket> queryWrapper = new LambdaQueryWrapper<SysOssBucket>()
            .select(SysOssBucket::getId,
                SysOssBucket::getPersonal,
                SysOssBucket::getBucketType,
                SysOssBucket::getNickName,
                SysOssBucket::getTags,
                SysOssBucket::getExpiredTime,
                SysOssBucket::getUpdateTime,
                SysOssBucket::getFileSize,
                SysOssBucket::getFileSuffix
            )
            .eq(SysOssBucket::getParentId, parentId)
            .and(qw -> qw.isNull(SysOssBucket::getExpiredTime).or().gt(SysOssBucket::getExpiredTime, new Date()));
/*        if (ProcessQueryConstants.ASC.equals(isAsc)) {
            queryWrapper.orderByAsc(SysOssBucket::getUpdateTime);
        } else {
            queryWrapper.orderByDesc(SysOssBucket::getUpdateTime);
        }*/
        if (StrUtil.isNotBlank(nickName)) {
            queryWrapper.like(SysOssBucket::getNickName, nickName);
        }
        SysOssBucketVo ossBucket = baseMapper.selectVoOne(Wrappers.<SysOssBucket>lambdaQuery()
            .eq(SysOssBucket::getId, parentId));

        SysOssBucketVo bucketVo = baseMapper.selectVoOne(Wrappers.<SysOssBucket>lambdaQuery()
            .eq(SysOssBucket::getBucketId, ossBucket.getBucketId())
            .eq(SysOssBucket::getParentId, 0));
        if (bucketVo.getBucketType() == OssConstant.BUCKET_TYPE_BUCKET) {
            // 查询桶描述信息
            SysOssBucketSecVo sysOssBucketSecVo = ossBucketSecMapper.selectVoOne(Wrappers.<SysOssBucketSec>lambdaQuery()
                .eq(SysOssBucketSec::getBucketId, bucketVo.getId()));

            long fileCount = baseMapper.selectVoList(Wrappers.<SysOssBucket>lambdaQuery()
                .select(SysOssBucket::getId)
                .eq(SysOssBucket::getBucketId, sysOssBucketSecVo.getBucketId())
                .eq(SysOssBucket::getBucketType, OssConstant.BUCKET_TYPE_FILE)).stream().count();
            bucketTreeDto.getBucketInfo().put(OssConstant.BUCKET_NAME, bucketVo.getNickName());
            bucketTreeDto.getBucketInfo().put(OssConstant.PERSONAL, bucketVo.getPersonal());
            bucketTreeDto.getBucketInfo().put(OssConstant.BUCKET_USED_SIZE, FileSizeUtil.formatFileSize(sysOssBucketSecVo.getCurrentCapacity()));
            bucketTreeDto.getBucketInfo().put(OssConstant.BUCKET_FILE_COUNT, fileCount);
            bucketTreeDto.getBucketInfo().put(OssConstant.BUCKET_TOTAL_SIZE, FileSizeUtil.formatFileSize(bucketVo.getMaxCapacity()));
        }


        //查询子节点
        List<SysOssBucketVo> childrenNodes = baseMapper.selectVoList(queryWrapper);

        if (CollectionUtils.isNotEmpty(childrenNodes)) {
            childrenNodes.forEach(c -> {
                c.setPowers(this.queryPowerById(c.getId()));
                if (ObjectUtil.isNotNull(c.getFileSize())) {
                    c.setFileSize(FileSizeUtil.formatFileSize(Double.valueOf(String.valueOf(c.getFileSize()))));
                }
                if (c.getBucketType() == OssConstant.BUCKET_TYPE_FILE) {
                    // 查询文件路径信息
                    SysOssBucketSecVo sysOssBucketSecVo = ossBucketSecMapper.selectVoOne(Wrappers.<SysOssBucketSec>lambdaQuery()
                        .eq(SysOssBucketSec::getBucketId, c.getId()));
                    c.setUrl(OssConstant.FILE_PREVIEW + StringUtils.encrypt(String.valueOf(sysOssBucketSecVo.getId())));
                }
            });
        }
        bucketTreeDto.setBuckets(childrenNodes);
        bucketTreeDto.setPowers(this.queryPowerById(parentId));
        return bucketTreeDto;
    }

    @Override
    @Transactional
    public boolean updateBucketPower(SysOssBucketBo bo) {

        SysOssBucket sysOssBucket = new SysOssBucket();
        BeanCopyUtils.copy(bo, sysOssBucket);
        if (ObjectUtil.isNotNull(bo.getCapacityType())) {
            double size = FileUtils.convertFileSize(bo.getCapacityType(), bo.getMaxCapacity());
            sysOssBucket.setMaxCapacity(size);
        }
        baseMapper.updateById(sysOssBucket);
        SysOssBucket bucket = baseMapper.selectById(bo.getId());
        List<SysOssBucketDept> bucketDepts = ossBucketDeptMapper.selectList(new LambdaQueryWrapper<SysOssBucketDept>()
            .eq(SysOssBucketDept::getBucketId, bo.getId()));
        if (!CollectionUtils.isEmpty(bucketDepts) || Objects.equals(bo.getAll(), OssConstant.BUCKET_ALL_READ)) {
            // 清空权限
            List<Long> powers = bucketDepts.stream().map(SysOssBucketDept::getId).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(powers)) {
                powerRelationMapper.delete(new LambdaQueryWrapper<SysOssPowerRelation>().in(SysOssPowerRelation::getBucketDeptId, powers));
                ossBucketDeptMapper.deleteBatchIds(powers);
            }

        }
        if (ObjectUtil.isNotNull(bo.getAll()) && OssConstant.BUCKET_ALL_READ.equals(bo.getAll())) {
            this.createAllDeptPowers(bo);
        }
        // 建立新权限
        if (CollectionUtils.isNotEmpty(bo.getPowers())) {
            this.createPowers(bo);
            if (bucket.getBucketType() != OssConstant.BUCKET_TYPE_BUCKET) {
                this.updateChildPowers(bo);
            }
        }

        return true;
    }

    @Override
    @Transactional
    public void calculatedBucketCapacity(Long bucketId) {
        SysOssBucketSecVo sysOssBucketSecVo = ossBucketSecMapper.selectVoOne(Wrappers.<SysOssBucketSec>lambdaQuery()
            .eq(SysOssBucketSec::getBucketId, bucketId));
        //查询桶下所有文件大小
        double bucketSize = baseMapper.selectList(Wrappers.<SysOssBucket>lambdaQuery()
                .eq(SysOssBucket::getBucketId, bucketId).eq(SysOssBucket::getBucketType, OssConstant.BUCKET_TYPE_FILE))
            .stream().mapToDouble(SysOssBucket::getFileSize).sum();
        //double bucketSize = OssFactory.instance().getOneBucketSize(sysOssBucketSecVo.getBucketName());
        sysOssBucketSecVo.setCurrentCapacity(bucketSize);

        SysOssBucketSec sysOssBucketSec = new SysOssBucketSec();
        BeanCopyUtils.copy(sysOssBucketSecVo, sysOssBucketSec);

        ossBucketSecMapper.updateById(sysOssBucketSec);
    }

    /**
     * 根据父节点获取子
     *
     * @param allDepartments
     * @param parentId
     * @return
     */
    public List<SysDept> getAllChildDepartments(List<SysDept> allDepartments, Long parentId) {
        List<SysDept> childDepartments = new ArrayList<>();
        for (SysDept department : allDepartments) {
            if (department.getParentId().equals(parentId)) {
                childDepartments.add(department);
                childDepartments.addAll(getAllChildDepartments(allDepartments, department.getDeptId()));
            }
        }
        return childDepartments;
    }

    @Override
    public SysOssMenuTreeAndPowerVo queryManagerDeptPowers(Long id) {

        SysOssMenuTreeAndPowerVo vo = new SysOssMenuTreeAndPowerVo();
        List<SysOssBucketPowersBo> result = new ArrayList<>();
        Set<Long> deptIds = new HashSet<>();
        List<SysOssBucketPowerVo> sysOssBucketPowerVos = this.queryAllDeptPowersOfId(id);
        //查询返回的部门列表
        List<SysDept> deptList = sysDeptMapper.selectList(Wrappers.<SysDept>lambdaQuery().in(CollectionUtils.isNotEmpty(sysOssBucketPowerVos), SysDept::getDeptId, sysOssBucketPowerVos.stream().map(SysOssBucketPowerVo::getDeptId).collect(Collectors.toList())));
        //显示数据排除父节点
        deptList.stream().forEach(a -> a.setId(a.getDeptId()));
        List<Long> leafDeptIds = TreeBuildUtils.getLeafNodeEntity(deptList).stream().map(SysDept::getDeptId).collect(Collectors.toList());
        //保留子节点部门
        sysOssBucketPowerVos.removeIf(a -> !leafDeptIds.contains(a.getDeptId()));

        if (CollectionUtils.isNotEmpty(sysOssBucketPowerVos)) {
            Map<String, Set<Long>> group = sysOssBucketPowerVos.stream().collect(Collectors.groupingBy(SysOssBucketPowerVo::getPower,
                Collectors.mapping(SysOssBucketPowerVo::getDeptId, Collectors.toSet())));

            group.forEach((k, v) -> {
                SysOssBucketPowersBo bo = new SysOssBucketPowersBo();
                bo.setPower(k);
                bo.setDeptIds(v);
                deptIds.addAll(v);
                result.add(bo);
            });
        }
        //查询云盘
        SysOssBucketVo sysOssBucketVo = baseMapper.selectVoOne(Wrappers.<SysOssBucket>lambdaQuery().eq(SysOssBucket::getId, id));
        if (ObjectUtil.isNull(sysOssBucketVo)) {
            throw new SysOssException("sys.oss.bucket.null");
        }
        //设置云盘信息
        if (ObjectUtil.isNotNull(sysOssBucketVo) && sysOssBucketVo.getBucketType() == OssConstant.BUCKET_TYPE_BUCKET) {
            SysOssBucketSecVo sysOssBucketSecVo = ossBucketSecMapper.selectVoOne(Wrappers.<SysOssBucketSec>lambdaQuery().eq(SysOssBucketSec::getBucketId, id));
            sysOssBucketVo.setUsedCapacity(sysOssBucketSecVo.getCurrentCapacity());
            sysOssBucketVo.setTotalCapacity(sysOssBucketVo.getMaxCapacity());
            Set<Long> alldeptIds = sysDeptService.selectAllDeptIdsWithOutDataScope();
            if (deptIds.size() == alldeptIds.size()) {
                sysOssBucketVo.setAll(1);
            } else {
                sysOssBucketVo.setAll(0);
            }
            vo.setBucketInfo(sysOssBucketVo);
        }

        Set<Long> sysOssBucketDepts = ossBucketDeptMapper.selectList(Wrappers.<SysOssBucketDept>lambdaQuery().eq(SysOssBucketDept::getBucketId, sysOssBucketVo.getBucketId())).stream().map(SysOssBucketDept::getDeptId).collect(Collectors.toSet());
        if (sysOssBucketDepts.size() == 0) {
            vo.setPowers(new ArrayList<>());
        }
        if (CollectionUtils.isNotEmpty(sysOssBucketDepts)) {
            List<SysDept> sysDepts = sysDeptMapper.selectList(Wrappers.<SysDept>lambdaQuery().in(SysDept::getDeptId, sysOssBucketDepts));
            List<SysDeptTreeVo> depts = sysDepts.stream()
                .map(dept -> {
                    SysDeptTreeVo deptTreeVo = new SysDeptTreeVo();
                    deptTreeVo.setId(dept.getDeptId());
                    deptTreeVo.setParentId(dept.getParentId());
                    deptTreeVo.setLabel(dept.getDeptName());
                    return deptTreeVo;
                })
                .collect(Collectors.toList());
            List<SysDeptTreeVo> trees = TreeBuildUtils.create(depts);

            vo.setDepts(trees);
            vo.setPowers(result);
        }
        return vo;
    }

    @Override
    public SysOssBucketVo queryBucketInfo(Long id) {
        SysOssBucketVo sysOssBucket = baseMapper.selectVoOne(Wrappers.<SysOssBucket>lambdaQuery().eq(SysOssBucket::getId, id));
        if (ObjectUtil.isNotNull(sysOssBucket) && sysOssBucket.getBucketType() == OssConstant.BUCKET_TYPE_BUCKET) {
            SysOssBucketSecVo sysOssBucketSecVo = ossBucketSecMapper.selectVoOne(Wrappers.<SysOssBucketSec>lambdaQuery().eq(SysOssBucketSec::getBucketId, id));
            sysOssBucket.setUsedCapacity(FileSizeUtil.formatFileSize(sysOssBucketSecVo.getCurrentCapacity()));
            sysOssBucket.setTotalCapacity(FileSizeUtil.formatFileSize(sysOssBucket.getMaxCapacity()));
        } else {
            throw new SysOssException("sys.oss.bucket.null");
        }
        return sysOssBucket;
    }

    @Override
    @Transactional
    public String share(Long id) {
        SysOssBucket bucket = baseMapper.selectById(id);
        if (bucket == null || bucket.getBucketType() != OssConstant.BUCKET_TYPE_FILE) {
            throw new SysOssException("sys.oss.file.not.exist", "");
        }
        // 先检查是否已经创建过分享，并且没有过期
        SysOssBucketOpen sysOssBucketOpen = ossBucketOpenMapper.selectOne(new LambdaQueryWrapper<SysOssBucketOpen>()
            .eq(SysOssBucketOpen::getBucketId, id)
            .eq(SysOssBucketOpen::getCreateBy, LoginHelper.getUsername())
            .and(qw -> qw.isNull(SysOssBucketOpen::getExpiredTime).or().gt(SysOssBucketOpen::getExpiredTime, new Date())));

        //  链接加密
        if (sysOssBucketOpen == null) {
            // 创建链接
            sysOssBucketOpen = new SysOssBucketOpen();
            sysOssBucketOpen.setBucketId(id);
            ossBucketOpenMapper.insert(sysOssBucketOpen);
        }
        return "/s/" + StringUtils.encrypt(String.valueOf(sysOssBucketOpen.getId()));
    }

    @Override
    @Transactional
    public void createEntityFromOssFile(Long parentId, SysOss oss) {

        SysOssBucket parent = baseMapper.selectById(parentId);

        SysOssBucket bucket = new SysOssBucket();
        bucket.setParentId(parentId);
        bucket.setStatus(Constants.STATUS);
        bucket.setBucketType(OssConstant.BUCKET_TYPE_FILE);
        bucket.setBucketId(parent.getBucketId());
        bucket.setOwnerId(parent.getOwnerId());
        String fileName = oss.getOriginalName().lastIndexOf(".") > 0 ? oss.getOriginalName().substring(0, oss.getOriginalName().lastIndexOf(".")) : oss.getOriginalName();
        bucket.setNickName(fileName);
        bucket.setOriginalName(oss.getOriginalName());
        bucket.setFileSuffix(oss.getFileSuffix());
        bucket.setFileSize(oss.getFileSize());
        baseMapper.insert(bucket);
        applicationEventPublisher.publishEvent(new SysOssCreateBucketSecDBEntityEvent(this, bucket.getId(), oss.getBucketName(), oss.getUrl(), false));
        //添加上级权限
        this.addParentPowers(bucket);
    }

    private void extracted(Long parentId, SysOssBucket parent, SysOssBucket bucket) {
        //创建文件夹自动赋予上机文件夹的权限
        Map<Long, List<Long>> folderPowers = new HashMap<>();
        if (parent.getBucketType() == OssConstant.BUCKET_TYPE_FOLD) {
            SysOssBucketVo parentBucket = baseMapper.selectVoOne(Wrappers.<SysOssBucket>lambdaQuery().eq(SysOssBucket::getId, parentId));
            List<SysOssBucketDeptVo> sysOssBucketDeptVos = ossBucketDeptMapper.selectVoList(Wrappers.<SysOssBucketDept>lambdaQuery().eq(SysOssBucketDept::getBucketId, parentBucket.getId()));
            sysOssBucketDeptVos.forEach(s -> {
                //查询权限
                List<Long> powers = powerRelationMapper.selectVoList(Wrappers.<SysOssPowerRelation>lambdaQuery()
                        .eq(SysOssPowerRelation::getBucketDeptId, s.getId()))
                    .stream().map(SysOssPowerRelationVo::getId).collect(Collectors.toList());
                folderPowers.put(s.getDeptId(), powers);
            });
            folderPowers.forEach((k, v) -> {
                synchronized (this) {
                    SysOssBucketDept sysOssBucketDept = new SysOssBucketDept();
                    sysOssBucketDept.setBucketId(bucket.getId());
                    sysOssBucketDept.setDeptId(k);
                    ossBucketDeptMapper.insert(sysOssBucketDept);
                    for (Long powerId : v) {
                        SysOssPowerRelation sysOssPowerRelation = new SysOssPowerRelation();
                        sysOssPowerRelation.setBucketDeptId(sysOssBucketDept.getId());

                        SysOssPowerRelation ossPowerRelation = powerRelationMapper.selectOne(Wrappers.<SysOssPowerRelation>lambdaQuery().eq(SysOssPowerRelation::getId, powerId));
                        sysOssPowerRelation.setPowerDefineId(ossPowerRelation.getPowerDefineId());
                        powerRelationMapper.insert(sysOssPowerRelation);
                    }
                }
            });
        }
    }

    @Override
    public boolean checkPermission(Set<Long> bucketIds, OssDataScopeType[] has) {

        if (has.length == 0 || LoginHelper.isAdmin()) {
            return true;
        }

        for (OssDataScopeType p : has) {
            LambdaQueryWrapper<SysOssBucket> queryWrapper = new LambdaQueryWrapper<>();
            if (p == OssDataScopeType.OWNER) {
                queryWrapper.select(SysOssBucket::getId)
                    .in(SysOssBucket::getId, bucketIds).eq(SysOssBucket::getOwnerId, LoginHelper.getUserId());

                List<SysOssBucket> buckets = baseMapper.selectList(queryWrapper);
                Set<Long> move = buckets.stream().map(SysOssBucket::getId).collect(Collectors.toSet());
                bucketIds.removeAll(move);
            } else {
                Iterator<Long> iterator = bucketIds.iterator();
                while (iterator.hasNext()) {
                    Long next = iterator.next();
                    Set<String> keys = this.queryPowerById(next);
                    if (keys.remove(p.getCode())) {
                        iterator.remove();
                    }
                }
            }
            if (CollectionUtils.isEmpty(bucketIds)) {
                return true;
            }
        }

        return bucketIds.isEmpty();
    }


    /**
     * 获取某个节点的权限
     */
    private Set<String> queryPowerById(Long id) {
        Set<String> result = new HashSet<>();

        SysOssBucket isOwner = baseMapper.selectById(id);
        if (Objects.equals(isOwner.getOwnerId(), LoginHelper.getUserId())) {
            result.addAll(defineService.getAllPowerList());
        } else {
            Set<Long> targets = new HashSet<>();
            targets.add(id);
            do {
                List<SysOssBucketPowerVo> sysOssBucketPowerVos = ossBucketDeptMapper.selectPowerByBucketIds(targets, LoginHelper.getDeptId());
                targets.clear();

                if (CollectionUtils.isEmpty(sysOssBucketPowerVos)) {
                    for (SysOssBucketPowerVo sysOssBucketPowerVo : sysOssBucketPowerVos) {
                        SysOssBucket sysOssBucket = baseMapper.selectById(sysOssBucketPowerVo.getBucketId());
                        if (sysOssBucket.getParentId() == 0L) {
                            break;
                        }
                        targets.add(sysOssBucket.getParentId());
                    }
                } else {
                    // 配置出权限
                    sysOssBucketPowerVos.forEach(a -> result.add(a.getPower()));
                }

            } while (CollectionUtils.isNotEmpty(targets));
        }
        return result;
    }

    /**
     * 编辑权限的时候获取所有部门的id与权限列表
     */
    private List<SysOssBucketPowerVo> queryAllDeptPowersOfId(Long id) {
        Set<Long> targets = new HashSet<>();
        targets.add(id);
        do {
            List<SysOssBucketPowerVo> sysOssBucketPowerVos = ossBucketDeptMapper.selectPowerByBucketIds(targets, null);
            targets.clear();

            if (CollectionUtils.isEmpty(sysOssBucketPowerVos)) {
                for (SysOssBucketPowerVo sysOssBucketPowerVo : sysOssBucketPowerVos) {
                    SysOssBucket sysOssBucket = baseMapper.selectById(sysOssBucketPowerVo.getBucketId());
                    if (sysOssBucket.getParentId() == 0L) {
                        break;
                    }
                    targets.add(sysOssBucket.getParentId());
                }

            } else {
                // 配置出权限
                return sysOssBucketPowerVos;
            }

        } while (CollectionUtils.isNotEmpty(targets));

        return new ArrayList<>();
    }
}
