package com.zhonghe.system.service;

import com.zhonghe.common.core.domain.entity.ReadTheInformation;
import org.springframework.scheduling.annotation.Async;

import java.util.List;

public interface ReadTheInformationService {

    List<Long> selectByUserIds(Long userId);

    boolean extist(Long noticeID);

    @Async
    void seave(ReadTheInformation readTheInformation);

    void saveBath(List<ReadTheInformation> list);
}
