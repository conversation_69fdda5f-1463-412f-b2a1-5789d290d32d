package com.zhonghe.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.core.domain.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zhonghe.system.domain.SysRobotConfig;
import com.zhonghe.system.domain.SysRobotRelevanceDepart;
import com.zhonghe.system.domain.bo.SysRobotConfigBo;
import com.zhonghe.system.domain.vo.SysRobotConfigVo;
import com.zhonghe.system.mapper.SysRobotConfigMapper;
import com.zhonghe.system.mapper.SysRobotRelevanceDepartMapper;
import com.zhonghe.system.service.ISysRobotConfigService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Collection;
import java.util.stream.Collectors;

/**
 * 机器人配置Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-03-29
 */
@RequiredArgsConstructor
@Service
public class SysRobotConfigServiceImpl implements ISysRobotConfigService {

    private final SysRobotConfigMapper baseMapper;

    private final SysRobotRelevanceDepartMapper robotRelevanceDepartMapper;

    /**
     * 查询机器人配置
     */
    @Override
    public SysRobotConfigVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询机器人配置列表
     */
    @Override
    public TableDataInfo<SysRobotConfigVo> queryPageList(SysRobotConfigBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SysRobotConfig> lqw = buildQueryWrapper(bo);
        Page<SysRobotConfigVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        List<SysRobotConfigVo> records = result.getRecords();

        for (SysRobotConfigVo record : records) {
            List<SysRobotRelevanceDepart> sysRobotRelevanceDeparts = robotRelevanceDepartMapper.selectList(new LambdaQueryWrapper<SysRobotRelevanceDepart>()
                .eq(SysRobotRelevanceDepart::getRobotId, record.getId()));

            List<Long> collect = sysRobotRelevanceDeparts.stream().map(SysRobotRelevanceDepart::getDepartId)
                .collect(Collectors.toList());

            record.setDepartId(collect.toArray(new Long[0]));
        }

        return TableDataInfo.build(result);
    }

    /**
     * 查询机器人配置列表
     */
    @Override
    public List<SysRobotConfigVo> queryList(SysRobotConfigBo bo) {
        LambdaQueryWrapper<SysRobotConfig> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SysRobotConfig> buildQueryWrapper(SysRobotConfigBo bo) {
        LambdaQueryWrapper<SysRobotConfig> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getName()),SysRobotConfig::getName,bo.getName());
        lqw.eq(StringUtils.isNotBlank(bo.getRobotState()),SysRobotConfig::getRobotState,bo.getRobotState());
        lqw.eq(StringUtils.isNotBlank(bo.getRobotType()), SysRobotConfig::getRobotType, bo.getRobotType());
        lqw.eq(StringUtils.isNotBlank(bo.getWebhook()), SysRobotConfig::getWebhook, bo.getWebhook());
        lqw.eq(StringUtils.isNotBlank(bo.getAccessToken()), SysRobotConfig::getAccessToken, bo.getAccessToken());
        lqw.eq(StringUtils.isNotBlank(bo.getSecret()), SysRobotConfig::getSecret, bo.getSecret());
        lqw.orderByDesc(SysRobotConfig ::getCreateTime);
        return lqw;
    }

    /**
     * 新增机器人配置
     */
    @Transactional
    @Override
    public Boolean insertByBo(SysRobotConfigBo bo) {
        SysRobotConfig add = BeanUtil.toBean(bo, SysRobotConfig.class);
        validEntityBeforeSave(add);
        int Id = baseMapper.insert(add);
        boolean flag = Id > 0;
        if (flag) {
            bo.setId(add.getId());
        }

        //机器人与部门关联
        Long[] departlist = bo.getDepartId();
        ArrayList<SysRobotRelevanceDepart> sysRobotRelevanceDeparts = CollUtil.newArrayList(new SysRobotRelevanceDepart());
        for (Long aLong : departlist) {
            SysRobotRelevanceDepart sysRobotRelevanceDepart = new SysRobotRelevanceDepart();
            sysRobotRelevanceDepart.setRobotId(add.getId());
            sysRobotRelevanceDepart.setDepartId(aLong);
            sysRobotRelevanceDeparts.add(sysRobotRelevanceDepart);
        }
        robotRelevanceDepartMapper.insertBatch(sysRobotRelevanceDeparts);


        return flag;
    }

    /**
     * 修改机器人配置
     */
    @Override
    public Boolean updateByBo(SysRobotConfigBo bo) {
        SysRobotConfig update = BeanUtil.toBean(bo, SysRobotConfig.class);
        validEntityBeforeSave(update);

        robotRelevanceDepartMapper.delete(new LambdaQueryWrapper<SysRobotRelevanceDepart>()
            .eq(SysRobotRelevanceDepart::getRobotId,bo.getId()));
        ArrayList<SysRobotRelevanceDepart> sysRobotRelevanceDeparts = CollUtil.newArrayList(new SysRobotRelevanceDepart());
        for (Long aLong : bo.getDepartId()) {
            SysRobotRelevanceDepart sysRobotRelevanceDepart = new SysRobotRelevanceDepart();
            sysRobotRelevanceDepart.setRobotId(bo.getId());
            sysRobotRelevanceDepart.setDepartId(aLong);
            sysRobotRelevanceDeparts.add(sysRobotRelevanceDepart);
        }
        robotRelevanceDepartMapper.insertBatch(sysRobotRelevanceDeparts);

        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SysRobotConfig entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除机器人配置
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
