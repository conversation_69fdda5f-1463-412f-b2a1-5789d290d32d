package com.zhonghe.system.service;

import com.zhonghe.common.core.domain.dto.BaseMqttMsgDTO;
import com.zhonghe.system.domain.dto.SysMqttLoginDto;
import org.eclipse.paho.client.mqttv3.MqttException;

import java.security.NoSuchAlgorithmException;
import java.util.List;

/**
 * @description:
 * @author: cq
 * @date: 2023/3/27 14:03
 */

public interface ISysMqttService {

    void initMqtt() throws NoSuchAlgorithmException, MqttException;

    void publishMessage(String topic, String msg);

    void publishMessage(String topic, String msg,long second);

    /**
     * 创建用户，用户只有订阅协议
     *
     * @return
     */
    SysMqttLoginDto registerCustomerUser() throws NoSuchAlgorithmException;

    void publishBatchMessage(List<BaseMqttMsgDTO> mqttMsg);
    void publishMessage(BaseMqttMsgDTO mqttMsg,long second);
    void publishMessage(BaseMqttMsgDTO mqttMsg);
}
