package com.zhonghe.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.annotations.VisibleForTesting;
import com.zhonghe.common.core.domain.entity.SysUser;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.common.utils.redis.RedisUtils;
import com.zhonghe.system.errorcode.dto.ErrorCodeAutoGenerateReqDTO;
import com.zhonghe.system.errorcode.dto.ErrorCodeDO;
import com.zhonghe.system.errorcode.dto.ErrorCodeRespDTO;
import com.zhonghe.system.errorcode.vo.ErrorCodeCreateReqVO;
import com.zhonghe.system.errorcode.vo.ErrorCodeExportReqVO;
import com.zhonghe.system.errorcode.vo.ErrorCodePageReqVO;
import com.zhonghe.system.errorcode.vo.ErrorCodeRespVO;
import com.zhonghe.system.errorcode.vo.ErrorCodeTypeEnum;
import com.zhonghe.system.errorcode.vo.ErrorCodeUpdateReqVO;
import com.zhonghe.system.mapper.ErrorCodeMapper;
import com.zhonghe.system.service.ErrorCodeService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.zhonghe.system.errorcode.ErrorCodeConstants.ERROR_CODE_DUPLICATE;
import static com.zhonghe.system.errorcode.ErrorCodeConstants.ERROR_CODE_NOT_EXISTS;
import static com.zhonghe.system.errorcode.ServiceExceptionUtil.exception;


/**
 * 错误码 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class ErrorCodeServiceImpl implements ErrorCodeService {

    @Resource
    private ErrorCodeMapper errorCodeMapper;

    //插入
    ErrorCodeDO convert(ErrorCodeCreateReqVO bean) {
        ErrorCodeDO errorCodeDO = new ErrorCodeDO();
        errorCodeDO.setMessage(bean.getMessage());
        errorCodeDO.setType(2);
        errorCodeDO.setApplicationName(bean.getApplicationName());
        errorCodeDO.setCode(Integer.parseInt(bean.getCode()));
        errorCodeDO.setMemo(bean.getMemo());
        errorCodeDO.setCreateTime(new Date());
        errorCodeDO.setUpdateTime(new Date());

        if (RedisUtils.getCacheObject("ajax") != null) {
            Map<String, Object> ajax = RedisUtils.getCacheObject("ajax");
            SysUser user = (SysUser) ajax.get("user");
            errorCodeDO.setCreator(user.getUserName());
            errorCodeDO.setUpdater(user.getUserName());
        } else {
            errorCodeDO.setCreator("admin");
            errorCodeDO.setUpdater("admin");
        }
        return errorCodeDO;
    }

    //修改
    ErrorCodeDO convert(ErrorCodeUpdateReqVO bean) {
        ErrorCodeDO errorCodeDO = new ErrorCodeDO();
        errorCodeDO.setId(bean.getId());
        errorCodeDO.setMessage(bean.getMessage());
        errorCodeDO.setApplicationName(bean.getApplicationName());
        errorCodeDO.setType(2);
        errorCodeDO.setUpdateTime(new Date());
        errorCodeDO.setCode(Integer.parseInt(bean.getCode()));
        errorCodeDO.setMemo(bean.getMemo());
        errorCodeDO.setDeleted(false);
        return errorCodeDO;
    }

    Page selectPage(ErrorCodePageReqVO pageReqVO) {
        Page objectPage = new Page<>();
//        objectPage.setPages(pageReqVO.getPageNum());
        objectPage.setCurrent(pageReqVO.getPageNum());
        objectPage.setSize(pageReqVO.getPageSize());
        return objectPage;
    }

    @Override
    public Long createErrorCode(ErrorCodeCreateReqVO createReqVO) {
        // 校验 code 重复
        validateCodeDuplicate(Integer.parseInt(createReqVO.getCode()), null);

        // 插入
//        ErrorCodeDO errorCode = ErrorCodeConvert.INSTANCE.convert(createReqVO)
//                .setType(ErrorCodeTypeEnum.MANUAL_OPERATION.getType());
        ErrorCodeDO errorCode = convert(createReqVO);
        errorCodeMapper.insert(errorCode);
        // 返回
        return errorCode.getId();
    }


    @Override
    public void updateErrorCode(ErrorCodeUpdateReqVO updateReqVO) {
        // 校验存在
        this.validateErrorCodeExists(updateReqVO.getId());
        // 校验 code 重复
        validateCodeDuplicate(Integer.parseInt(updateReqVO.getCode()), updateReqVO.getId());

        // 更新
//        ErrorCodeDO updateObj = ErrorCodeConvert.INSTANCE.convert(updateReqVO)
//                .setType(ErrorCodeTypeEnum.MANUAL_OPERATION.getType());
        ErrorCodeDO convert = convert(updateReqVO);
        errorCodeMapper.updateById(convert);
    }

    @Override
    public void deleteErrorCode(Long[] ids) {
        for (Long id : ids) {
            // 校验存在
            this.validateErrorCodeExists(id);
        }
        // 删除
        errorCodeMapper.deleteBatchIds(Arrays.asList(ids));
    }

    /**
     * 校验错误码的唯一字段是否重复
     * <p>
     * 是否存在相同编码的错误码
     *
     * @param code 错误码编码
     * @param id   错误码编号
     */
    @VisibleForTesting
    public void validateCodeDuplicate(Integer code, Long id) {
//        ErrorCodeDO errorCodeDO = errorCodeMapper.selectByCode(code);
        ErrorCodeDO errorCodeDO = errorCodeMapper.selectVoOne(new QueryWrapper<ErrorCodeDO>().lambda()
            .eq(code != null, ErrorCodeDO::getCode, code));
        if (errorCodeDO == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的错误码
        if (id == null) {
            throw exception(ERROR_CODE_DUPLICATE);
        }
        if (!errorCodeDO.getId().equals(id)) {
            throw exception(ERROR_CODE_DUPLICATE);
        }
    }

    @VisibleForTesting
    public void validateErrorCodeExists(Long id) {
        if (errorCodeMapper.selectById(id) == null) {
            throw exception(ERROR_CODE_NOT_EXISTS);
        }
    }

    @Override
    public ErrorCodeDO getErrorCode(Long id) {
        return errorCodeMapper.selectById(id);
    }

    @Override
    public IPage<ErrorCodeRespVO> getErrorCodePage(ErrorCodePageReqVO pageReqVO) {
        Page page = selectPage(pageReqVO);
        LambdaQueryWrapper<ErrorCodeDO> lqw = buildQueryWrapper(pageReqVO);
        IPage iPage = errorCodeMapper.selectVoPage(page, lqw);

        List<ErrorCodeDO> records = iPage.getRecords();
        List<ErrorCodeRespVO> list = new ArrayList<>();
        records.stream().forEach(f -> {
            ErrorCodeRespVO r = new ErrorCodeRespVO();
            r.setCode(f.getCode() + "");
            r.setId(f.getId());
            r.setType(f.getType());
            r.setApplicationName(f.getApplicationName());
            r.setMessage(f.getMessage());
            r.setMemo(f.getMemo());
            r.setCreateTime(f.getCreateTime());
            list.add(r);
        });
        iPage.setRecords(list);
        return iPage;
    }

    @SneakyThrows
    private LambdaQueryWrapper<ErrorCodeDO> buildQueryWrapper(ErrorCodePageReqVO bo) {
//        Map params = bo.getParams();
        LambdaQueryWrapper<ErrorCodeDO> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getType() != null, ErrorCodeDO::getType, bo.getType());
        lqw.like(StringUtils.isNotBlank(bo.getApplicationName()), ErrorCodeDO::getApplicationName, bo.getApplicationName());
        lqw.eq(bo.getCode() != null, ErrorCodeDO::getCode, bo.getCode());
        lqw.like(StringUtils.isNotBlank(bo.getMessage()), ErrorCodeDO::getMessage, bo.getMessage());
        lqw.between(bo.getBeginTime() != null && bo.getEndTime() != null,
            ErrorCodeDO::getCreateTime, bo.getBeginTime(), DateUtil.offsetDay(bo.getEndTime(), 1));
        return lqw;
    }

    @Override
    public List<ErrorCodeDO> getErrorCodeList(ErrorCodeExportReqVO exportReqVO) {
        LambdaQueryWrapper<ErrorCodeDO> lqw = new QueryWrapper<ErrorCodeDO>().lambda()
            .eq(exportReqVO.getCode() != null, ErrorCodeDO::getCode, exportReqVO.getCode());
        errorCodeMapper.selectVoList(lqw);
        return errorCodeMapper.selectList(lqw);
    }

    @Override
    @Transactional
    public void autoGenerateErrorCodes(List<ErrorCodeAutoGenerateReqDTO> autoGenerateDTOs) {
        if (CollUtil.isEmpty(autoGenerateDTOs)) {
            return;
        }
        // 获得错误码
//        List<ErrorCodeDO> errorCodeDOs = errorCodeMapper.selectListByCodes(
//                convertSet(autoGenerateDTOs, ErrorCodeAutoGenerateReqDTO::getCode));
        List<@NotNull(message = "错误码编码不能为空") Integer> collect = autoGenerateDTOs.stream().map(m -> m.getCode()).collect(Collectors.toList());
        List<ErrorCodeDO> errorCodeDOS = errorCodeMapper.selectVoList(new QueryWrapper<ErrorCodeDO>().lambda()
            .in(ErrorCodeDO::getCode, collect));


        // 遍历 autoGenerateBOs 数组，逐个插入或更新。考虑到每次量级不大，就不走批量了
        autoGenerateDTOs.forEach(autoGenerateDTO -> {
            ErrorCodeDO errorCodeDO = errorCodeDOS.stream().filter(f ->
                f.getCode().compareTo(autoGenerateDTO.getCode()) == 0 ? true : false
            ).findAny().get();
            // 不存在，则进行新增
            if (errorCodeDO == null) {
                errorCodeDO = new ErrorCodeDO();
                errorCodeDO.setCode(autoGenerateDTO.getCode());
                errorCodeDO.setApplicationName(autoGenerateDTO.getApplicationName());
                errorCodeDO.setMessage(autoGenerateDTO.getMessage());
                errorCodeDO.setType(1);
                errorCodeMapper.insert(errorCodeDO);
                return;
            }
            // 存在，则进行更新。更新有三个前置条件：
            // 条件 1. 只更新自动生成的错误码，即 Type 为 ErrorCodeTypeEnum.AUTO_GENERATION
            if (!ErrorCodeTypeEnum.AUTO_GENERATION.getType().equals(errorCodeDO.getType())) {
                return;
            }
            // 条件 2. 分组 applicationName 必须匹配，避免存在错误码冲突的情况
            if (!autoGenerateDTO.getApplicationName().equals(errorCodeDO.getApplicationName())) {
                log.error("[autoGenerateErrorCodes][自动创建({}/{}) 错误码失败，数据库中已经存在({}/{})]",
                    autoGenerateDTO.getCode(), autoGenerateDTO.getApplicationName(),
                    errorCodeDO.getCode(), errorCodeDO.getApplicationName());
                return;
            }
            // 条件 3. 错误提示语存在差异
            if (autoGenerateDTO.getMessage().equals(errorCodeDO.getMessage())) {
                return;
            }
            // 最终匹配，进行更新
            ErrorCodeDO errorCodeDO1 = new ErrorCodeDO();
            errorCodeDO1.setId(errorCodeDO.getId());
            errorCodeDO1.setMessage(autoGenerateDTO.getMessage());
            errorCodeMapper.updateById(errorCodeDO1);
        });
    }

    @Override
    public List<ErrorCodeRespDTO> getErrorCodeList(String applicationName, Date minUpdateTime) {
        LambdaQueryWrapper<ErrorCodeDO> lqw = new QueryWrapper<ErrorCodeDO>().lambda()
            .eq(StringUtils.isNotEmpty(applicationName), ErrorCodeDO::getCode, applicationName);
        List<ErrorCodeDO> errorCodeDOs = errorCodeMapper.selectVoList(lqw);

        List<ErrorCodeRespDTO> errorCodeRespDTOSList = null;
        errorCodeDOs.stream().forEach(f -> {
            ErrorCodeRespDTO errorCodeRespDTO = new ErrorCodeRespDTO();
            errorCodeRespDTO.setCode(f.getCode());
            errorCodeRespDTO.setMessage(f.getMessage());
            errorCodeRespDTO.setUpdateTime(minUpdateTime);
            errorCodeRespDTOSList.add(errorCodeRespDTO);
        });
        return errorCodeRespDTOSList;
    }

}

