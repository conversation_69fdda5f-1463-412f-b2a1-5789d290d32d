package com.zhonghe.system.service;

import com.zhonghe.system.domain.vo.SysOssBucketDeptVo;
import com.zhonghe.system.domain.bo.SysOssBucketDeptBo;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 部门存储桶Service接口
 *
 * <AUTHOR>
 * @date 2023-03-31
 */
public interface ISysOssBucketDeptService {

    /**
     * 查询部门存储桶
     */
    SysOssBucketDeptVo queryById(Long deptId);

    /**
     * 查询部门存储桶列表
     */
    TableDataInfo<SysOssBucketDeptVo> queryPageList(SysOssBucketDeptBo bo, PageQuery pageQuery);

    /**
     * 查询部门存储桶列表
     */
    List<SysOssBucketDeptVo> queryList(SysOssBucketDeptBo bo);

    /**
     * 修改部门存储桶
     */
    Boolean insertByBo(SysOssBucketDeptBo bo);

    /**
     * 修改部门存储桶
     */
    Boolean updateByBo(SysOssBucketDeptBo bo);

    /**
     * 校验并批量删除部门存储桶信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
