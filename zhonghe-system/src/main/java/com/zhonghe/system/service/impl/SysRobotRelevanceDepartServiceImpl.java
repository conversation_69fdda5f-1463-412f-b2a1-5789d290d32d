package com.zhonghe.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zhonghe.system.domain.SysRobotRelevanceDepart;
import com.zhonghe.system.domain.bo.SysRobotRelevanceDepartBo;
import com.zhonghe.system.domain.vo.SysRobotRelevanceDepartVo;
import com.zhonghe.system.mapper.SysRobotRelevanceDepartMapper;
import com.zhonghe.system.service.ISysRobotRelevanceDepartService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Collection;

/**
 * 机器人配置关联部门Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-04-04
 */
@RequiredArgsConstructor
@Service
public class SysRobotRelevanceDepartServiceImpl implements ISysRobotRelevanceDepartService {

    private final SysRobotRelevanceDepartMapper baseMapper;

    /**
     * 查询机器人配置关联部门
     */
    @Override
    public SysRobotRelevanceDepartVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }


    /**
     * 查询机器人配置关联部门列表
     */
    @Override
    public List<SysRobotRelevanceDepartVo> queryList(SysRobotRelevanceDepartBo bo) {
        LambdaQueryWrapper<SysRobotRelevanceDepart> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SysRobotRelevanceDepart> buildQueryWrapper(SysRobotRelevanceDepartBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SysRobotRelevanceDepart> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getRobotId() != null, SysRobotRelevanceDepart::getRobotId, bo.getRobotId());
        lqw.eq(bo.getDepartId() != null, SysRobotRelevanceDepart::getDepartId, bo.getDepartId());
        lqw.eq(StringUtils.isNotBlank(bo.getExtend()), SysRobotRelevanceDepart::getExtend, bo.getExtend());
        return lqw;
    }

    /**
     * 新增机器人配置关联部门
     */
    @Override
    public Boolean insertByBo(SysRobotRelevanceDepartBo bo) {
        SysRobotRelevanceDepart add = BeanUtil.toBean(bo, SysRobotRelevanceDepart.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改机器人配置关联部门
     */
    @Override
    public Boolean updateByBo(SysRobotRelevanceDepartBo bo) {
        SysRobotRelevanceDepart update = BeanUtil.toBean(bo, SysRobotRelevanceDepart.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SysRobotRelevanceDepart entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除机器人配置关联部门
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public TableDataInfo<SysRobotRelevanceDepart> selectPageRoleList(SysRobotRelevanceDepart robotRelevanceDepart, PageQuery pageQuery) {
        SysRobotRelevanceDepartBo sysRobotRelevanceDepartBo = BeanUtil.toBean(robotRelevanceDepart, SysRobotRelevanceDepartBo.class);
        Page<SysRobotRelevanceDepart> page = baseMapper.selectPage(pageQuery.build(), this.buildQueryWrapper(sysRobotRelevanceDepartBo));

        return TableDataInfo.build(page);
    }
}
