package com.zhonghe.system.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhonghe.common.constant.Constants;
import com.zhonghe.common.constant.ServiceConstants;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.domain.entity.SysDept;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.exception.notice.NoticeException;
import com.zhonghe.common.helper.LoginHelper;
import com.zhonghe.common.helper.NoticePlaceholderHelper;
import com.zhonghe.common.utils.JsonUtils;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.system.domain.SysNotice;
import com.zhonghe.system.domain.SysNoticeDept;
import com.zhonghe.system.domain.SysNoticeTemplate;
import com.zhonghe.system.domain.dto.SysNoticeDto;
import com.zhonghe.system.domain.vo.SysNoticeDeptVo;
import com.zhonghe.system.event.SysNoticePublishEvent;
import com.zhonghe.system.mapper.SysDeptMapper;
import com.zhonghe.system.mapper.SysNoticeDeptMapper;
import com.zhonghe.system.mapper.SysNoticeMapper;
import com.zhonghe.system.mapper.SysNoticeTemplateMapper;
import com.zhonghe.system.service.ISysNoticeService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 公告 服务层实现
 *
 * <AUTHOR> Li
 */
@RequiredArgsConstructor
@Service
public class SysNoticeServiceImpl implements ISysNoticeService {

    private final ApplicationEventPublisher applicationEventPublisher;

    private final SysNoticeMapper baseMapper;

    private final SysNoticeDeptMapper noticeDeptMapper;

    private final SysDeptMapper sysDeptMapper;

    private final SysNoticeTemplateMapper noticeTemplateMapper;

    @Override
    public IPage<SysNotice> selectList(PageQuery pageQuery) {
        Page<SysNotice> build = pageQuery.build();
        return baseMapper.selectAllList(build);
    }

    @Override
    @Transactional
    public int publishNotices(Long[] noticeIds) {
        // 不允许过期或已发送的数据再次发送
        List<SysNotice> sysNotices = baseMapper.selectList(new LambdaQueryWrapper<SysNotice>()
            .in(SysNotice::getNoticeId, noticeIds));
        sysNotices.forEach(a -> {
            if (a.getSendStatus() != ServiceConstants.NOTICE_SEND_STATUS_WAITING) {
                if (a.getType() == ServiceConstants.NOTICE_SEND_TYPE_NORMAL && a.getSendStatus() == ServiceConstants.NOTICE_SEND_STATUS_OK) {
                    throw new NoticeException("notice.publish.error", new Object[]{a.getNoticeTitle()});
                }
                if (a.getSendStatus() == ServiceConstants.NOTICE_SEND_STATUS_CANCEL
                    && new Date().after(a.getSendTime()) && a.getType() == ServiceConstants.NOTICE_SEND_TYPE_DELAY) {
                    throw new NoticeException("notice.publish.error", new Object[]{a.getNoticeTitle()});
                }
            }

            if (a.getType() == ServiceConstants.NOTICE_SEND_TYPE_NORMAL) {
                // 非延时消息，更新发送时间
                a.setSendTime(new Date());
                a.setSendStatus(ServiceConstants.NOTICE_SEND_STATUS_OK);
            } else {
                // 如果延时消息，就把它修改为等待发送消息
                a.setSendStatus(ServiceConstants.NOTICE_SEND_STATUS_WAITING);
            }
            // 发送完成后需要把所有的状态修改 停用：status = 1
            a.setStatus(Constants.FAIL);
        });
        if (baseMapper.insertOrUpdateBatch(sysNotices)) {
            applicationEventPublisher.publishEvent(new SysNoticePublishEvent(this, sysNotices));
        }

        return noticeIds.length;
    }

    @Override
    @Transactional
    public int update(SysNotice sysNotice) {
        return baseMapper.update(null, new LambdaUpdateWrapper<SysNotice>().set(SysNotice::getSendStatus, ServiceConstants.NOTICE_SEND_STATUS_OK)
            .set(SysNotice::getStatus, sysNotice.getStatus())
            .eq(SysNotice::getNoticeId, sysNotice.getNoticeId()));
    }

    @Override
    public List<SysNotice> selectLists(List<Long> sysNotice, boolean idRead) {
        LambdaQueryWrapper<SysNotice> lqw = null;
        if (idRead) {
            if (sysNotice.isEmpty()) return new ArrayList<>();

            lqw = new LambdaQueryWrapper<SysNotice>().in(SysNotice::getNoticeId, sysNotice);
        } else {
            lqw = new LambdaQueryWrapper<SysNotice>()
                .notIn(!sysNotice.isEmpty(), SysNotice::getNoticeId, sysNotice);
        }

        return baseMapper.selectVoList(lqw);
    }

    @Override
    public TableDataInfo<SysNotice> selectPageNoticeList(SysNotice notice, PageQuery pageQuery) {
        LambdaQueryWrapper<SysNotice> lqw = new LambdaQueryWrapper<SysNotice>()
            .eq(ObjectUtil.isNotNull(notice.getSendStatus()), SysNotice::getSendStatus, notice.getSendStatus())
            .eq(ObjectUtil.isNotNull(notice.getType()), SysNotice::getType, notice.getType())
            .like(StringUtils.isNotBlank(notice.getNoticeTitle()), SysNotice::getNoticeTitle, notice.getNoticeTitle())
            .eq(StringUtils.isNotBlank(notice.getNoticeType()), SysNotice::getNoticeType, notice.getNoticeType())
            .like(StringUtils.isNotBlank(notice.getCreateBy()), SysNotice::getCreateBy, notice.getCreateBy())
            .orderByDesc(SysNotice::getCreateTime);
        Map<String, Object> params = notice.getParams();
        if (!CollectionUtils.isNotEmpty(params)) {
            if (params.containsKey("startSendTime") && params.containsKey("endSendTime")) {
                lqw.between(SysNotice::getSendTime, params.get("startSendTime"), params.containsKey("endSendTime"));
            }
            if (params.containsKey("startCreateTime") && params.containsKey("endCreateTime")) {
                lqw.between(SysNotice::getCreateTime, params.get("startCreateTime"), params.containsKey("endCreateTime"));
            }
        }
        Page<SysNotice> page = baseMapper.selectPage(pageQuery.build(), lqw);
        List<SysNotice> records = page.getRecords();
        List<Long> noticeIds = records.stream().map(SysNotice::getNoticeId).collect(Collectors.toList());

        List<SysNoticeDeptVo> sysNoticeDeptVos = noticeDeptMapper.selectDeptsName(noticeIds);
        Map<Long, List<SysNoticeDeptVo>> noticeIdDeptNameMap = sysNoticeDeptVos.stream().collect(Collectors.groupingBy(SysNoticeDeptVo::getNoticeId));

        // 包装额外的转换参数
        List<SysNotice> dto = new ArrayList<>(page.getRecords().size());
        records.forEach(a -> {
            SysNoticeDto d = new SysNoticeDto();
            BeanUtils.copyProperties(a, d, "templateValues");
            if (noticeIdDeptNameMap.containsKey(a.getNoticeId())) {
                d.setDeptIds(noticeIdDeptNameMap.get(a.getNoticeId()).stream().map(SysNoticeDeptVo::getDeptId).collect(Collectors.toList()));
                d.setDeptNames(noticeIdDeptNameMap.get(a.getNoticeId()).stream().map(SysNoticeDeptVo::getDeptName).collect(Collectors.toList()));
            }
            if (StringUtils.isNotEmpty(a.getTemplateValues())) {
                // 反序列化为 map对象
                d.setTemplateWords(JsonUtils.parseObject(a.getTemplateValues(), Map.class));
            }
            dto.add(d);
        });
        page.setRecords(dto);

        return TableDataInfo.build(page);
    }

    /**
     * 查询公告信息
     *
     * @param noticeId 公告ID
     * @return 公告信息
     */
    @Override
    public SysNotice selectNoticeById(Long noticeId) {
        return baseMapper.selectById(noticeId);
    }

    /**
     * 查询公告列表
     *
     * @param notice 公告信息
     * @return 公告集合
     */
    @Override
    public List<SysNotice> selectNoticeList(SysNotice notice) {
        return baseMapper.selectList(new LambdaQueryWrapper<SysNotice>()
            .like(StringUtils.isNotBlank(notice.getNoticeTitle()), SysNotice::getNoticeTitle, notice.getNoticeTitle())
            .eq(StringUtils.isNotBlank(notice.getNoticeType()), SysNotice::getNoticeType, notice.getNoticeType())
            .like(StringUtils.isNotBlank(notice.getCreateBy()), SysNotice::getCreateBy, notice.getCreateBy()));
    }

    /**
     * 新增公告
     *
     * @param notice 公告信息
     * @return 结果
     */
    @Override
    @Transactional
    public int insertNotice(SysNoticeDto notice) {

        //如果是公告插入所有的部门，否则按传值进行部门控制
        if (ServiceConstants.NOTICE_TYPE_ANNOUNCE.equals(notice.getNoticeType()) && CollectionUtils.isNotEmpty(notice.getDeptIds())) {
            this.checkNoticeDeptPowerScope(notice.getDeptIds());
        }
        notice.setStatus(Constants.STATUS);
        notice.setSendStatus(ServiceConstants.NOTICE_SEND_STATUS_WAITING);
        // 处理模板的关键字
        if (!CollectionUtils.isEmpty(notice.getTemplateWords()) && notice.getTemplateId() != null) {
            this.handlerTemplateInfo(notice);
        }
        if (ObjectUtil.isNotNull(notice.getType()) && notice.getType().equals(ServiceConstants.NOTICE_SEND_TYPE_DELAY) && ObjectUtil.isNull(notice.getSendTime())) {
            throw new NoticeException("notice.update.sendTime.null.error", new Object[]{notice.getNoticeTitle()});
        }
        if (notice.getType() == ServiceConstants.NOTICE_SEND_TYPE_DELAY && new Date().after(notice.getSendTime())) {
            // 延时消息，并且发送时间比当前时间早
            throw new NoticeException("notice.update.error", new Object[]{notice.getNoticeTitle()});
        }

        notice.setNickName(LoginHelper.getLoginUser().getNickName());
        int r = baseMapper.insert(notice);
        // 先把notice与部门关系绑定，发布的时候需要更新noticeId
        if (!CollectionUtils.isEmpty(notice.getDeptIds())) {
            this.insertNoticeDept(notice.getNoticeId(), notice.getDeptIds());
        }
        if (notice.getType() == ServiceConstants.NOTICE_SEND_TYPE_DELAY) {
            // 发送延迟发送事件通知
            applicationEventPublisher.publishEvent(new SysNoticePublishEvent(this, Arrays.asList(new SysNotice[]{notice})));
        }
        return r;
    }

    /**
     * 处理模板json化数据
     *
     * @param notice
     * @return
     */
    private SysNotice handlerTemplateInfo(SysNoticeDto notice) {
        SysNoticeTemplate sysNoticeTemplate = noticeTemplateMapper.selectById(notice.getTemplateId());
        if (ObjectUtil.isNotNull(sysNoticeTemplate)) {
            notice.setNoticeContent(NoticePlaceholderHelper.getInstance().replacePlaceholders(sysNoticeTemplate.getTemplateContent(),
                notice.getTemplateWords()));
            String keyValues = JsonUtils.toJsonString(notice.getTemplateWords());
            notice.setTemplateValues(keyValues);
        }
        return notice;
    }

    /**
     * 校验用户是否有指定部门的权限
     *
     * @param deptIds
     */
    private void checkNoticeDeptPowerScope(List<Long> deptIds) {
        List<SysDept> sysDepts = sysDeptMapper.selectDeptList(new LambdaQueryWrapper<SysDept>().select(SysDept::getDeptId).in(
            SysDept::getDeptId, deptIds));
        if (sysDepts.size() != deptIds.size()) {
            throw new NoticeException("notice.power.error", null);
        }
    }

    /**
     * 修改公告
     *
     * @param notice 公告信息
     * @return 结果
     */
    @Override
    @Transactional
    public int updateNotice(SysNoticeDto notice) {
        // 消息是否可以被修改
        SysNotice sysNotice = baseMapper.selectById(notice.getNoticeId());
        if (sysNotice.getSendStatus() == ServiceConstants.NOTICE_SEND_STATUS_OK) {
            throw new NoticeException("notice.update.error", new Object[]{sysNotice.getNoticeTitle()});
        }
        if (Constants.FAIL.equals(notice.getStatus())) {
            // 停用接口
            return baseMapper.update(null, new LambdaUpdateWrapper<SysNotice>()
                .set(SysNotice::getSendStatus, ServiceConstants.NOTICE_SEND_STATUS_CANCEL)
                .eq(SysNotice::getNoticeId, notice.getNoticeId())
                .set(SysNotice::getStatus, notice.getStatus()));
        }

        if (Constants.SUCCESS.equals(notice.getStatus())) {
            // 启用接口
            return baseMapper.update(null, new LambdaUpdateWrapper<SysNotice>()
                .set(SysNotice::getSendStatus, ServiceConstants.NOTICE_SEND_STATUS_WAITING)
                .eq(SysNotice::getNoticeId, notice.getNoticeId())
                .set(SysNotice::getStatus, notice.getStatus()));
        }
        if (ObjectUtil.isNotNull(notice.getType()) && notice.getType().equals(ServiceConstants.NOTICE_SEND_TYPE_DELAY) && ObjectUtil.isNull(notice.getSendTime())) {
            throw new NoticeException("notice.update.sendTime.null.error", new Object[]{notice.getNoticeTitle()});
        }
        if (sysNotice.getType() == ServiceConstants.NOTICE_SEND_TYPE_DELAY
            && ServiceConstants.NOTICE_SEND_STATUS_START.equals(notice.getStatus()) && new Date().after(sysNotice.getSendTime())) {
            // 延时消息，并且发送时间比当前时间早
            throw new NoticeException("notice.update.sendTime.error", new Object[]{sysNotice.getNoticeTitle()});
        }


        // 更新部门的范围
        if (!CollectionUtils.isEmpty(notice.getDeptIds())) {
            // 删除所有的关系消息与部门
            noticeDeptMapper.delete(new LambdaQueryWrapper<SysNoticeDept>().eq(SysNoticeDept::getNoticeId, notice.getNoticeId()));
            // 更新部门发送消息的范围
            this.checkNoticeDeptPowerScope(notice.getDeptIds());
            this.insertNoticeDept(notice.getNoticeId(), notice.getDeptIds());
        }

        if (ObjectUtil.isNotNull(notice.getTemplateId())) {
            this.handlerTemplateInfo(notice);
        }
        // 不允许修改发送状态,只允许修改为取消发送状态
        if (ObjectUtil.isNull(notice.getSendStatus())) {
            notice.setSendStatus(sysNotice.getSendStatus());
        }
        //notice.setStatus(sysNotice.getStatus());

        return baseMapper.updateById(notice);
    }

    private void insertNoticeDept(Long noticeId, List<Long> deptIds) {
        List<SysNoticeDept> depts = new ArrayList<>(deptIds.size());
        for (Long deptId : deptIds) {
            SysNoticeDept d = new SysNoticeDept();
            d.setNoticeId(noticeId);
            d.setDeptId(deptId);

            depts.add(d);
        }
        noticeDeptMapper.insertBatch(depts);
    }

    /**
     * 删除公告对象
     *
     * @param noticeId 公告ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteNoticeById(Long noticeId) {
        SysNotice sysNotice = baseMapper.selectById(noticeId);
        if (sysNotice.getSendStatus() == ServiceConstants.NOTICE_SEND_STATUS_OK) {
            throw new NoticeException("notice.delete.error", new Object[]{sysNotice.getNoticeTitle()});
        }
        return baseMapper.deleteById(noticeId);
    }

    /**
     * 批量删除公告信息
     *
     * @param noticeIds 需要删除的公告ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteNoticeByIds(Long[] noticeIds) {
        List<SysNotice> sysNotices = baseMapper.selectList(new LambdaQueryWrapper<SysNotice>().in(SysNotice::getNoticeId, noticeIds));
        List<Long> noticeDeptIds = new ArrayList<>();
        sysNotices.forEach(n -> {
            if (n.getSendStatus() != ServiceConstants.NOTICE_SEND_STATUS_OK) {
                // 处理掉关联关系
                noticeDeptIds.add(n.getNoticeId());
            }
        });
        if (!CollectionUtils.isEmpty(noticeDeptIds)) {
            noticeDeptMapper.delete(new LambdaQueryWrapper<SysNoticeDept>().in(SysNoticeDept::getNoticeId, noticeDeptIds));
        }
        return baseMapper.deleteBatchIds(Arrays.asList(noticeIds));
    }
}
