package com.zhonghe.system.service;

import com.zhonghe.system.domain.SysRobotMsg;

import java.io.UnsupportedEncodingException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.List;

/**
 * @description: 发送机器人消息
 * @author: cq
 * @date: 2023/3/29 10:56
 */

public interface IPushRobotMessageService {

    boolean sendMessage(List<Long> robotIds, SysRobotMsg template) throws UnsupportedEncodingException, NoSuchAlgorithmException, InvalidKeyException;
}
