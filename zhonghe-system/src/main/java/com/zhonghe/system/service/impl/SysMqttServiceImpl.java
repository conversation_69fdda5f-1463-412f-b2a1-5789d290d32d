package com.zhonghe.system.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.zhonghe.common.config.MqttProperties;
import com.zhonghe.common.constant.Constants;
import com.zhonghe.common.core.domain.dto.BaseMqttMsgDTO;
import com.zhonghe.common.enums.UserType;
import com.zhonghe.common.exception.base.BaseException;
import com.zhonghe.common.helper.LoginHelper;
import com.zhonghe.common.helper.MqttSecurityHelper;
import com.zhonghe.system.domain.dto.SysMqttLoginDto;
import com.zhonghe.system.event.SysNoticeHandleEvent;
import com.zhonghe.system.service.ISysMqttService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.IMqttDeliveryToken;
import org.eclipse.paho.client.mqttv3.MqttCallback;
import org.eclipse.paho.client.mqttv3.MqttClient;
import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;

import java.nio.charset.Charset;
import java.security.NoSuchAlgorithmException;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;

/**
 * @description:
 * @author: cq
 * @date: 2023/3/27 14:04
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class SysMqttServiceImpl implements ISysMqttService {

    // 这个是管理员的对象，直接生成
    private final SysMqttLoginDto admin = new SysMqttLoginDto();
    private final MqttProperties mqttProperties;
    private final ApplicationEventPublisher applicationEventPublisher;
    private MqttClient mqttClient;

    @Override
    public void initMqtt() throws NoSuchAlgorithmException, MqttException {
        if (mqttProperties.isEnable()) {
            MqttSecurityHelper.registerAdminMqttUser(mqttProperties.getUsername(), mqttProperties.getPassword(), mqttProperties.getSalt());
            if (mqttClient == null) {
                synchronized (this) {
                    mqttClient = new MqttClient("tcp://" + mqttProperties.getHost() + ":" + mqttProperties.getTcp(),
                        "publisher" + MqttSecurityHelper.GetRandomString(4), new MemoryPersistence());
                    MqttConnectOptions connOpts = new MqttConnectOptions();
                    connOpts.setCleanSession(true);
                    connOpts.setAutomaticReconnect(true);
                    connOpts.setUserName(mqttProperties.getUsername());
                    connOpts.setPassword(mqttProperties.getPassword().toCharArray());

                    admin.setPwd(mqttProperties.getPassword());
                    admin.setUsername(mqttProperties.getUsername());
                    admin.setPort(mqttProperties.getPort());
                    admin.setRemote(mqttProperties.getRemote());
                    Set<String> topics = new HashSet<>();
                    topics.add("dept/#");
                    admin.setTopics(topics);

                    mqttClient.connect(connOpts);
                    mqttClient.subscribe("#");
                    mqttClient.setCallback(new MqttCallback() {

                        @Override
                        public void messageArrived(String topic, MqttMessage message) {

                            log.info("Received a message: {} ,Topic:{}", new String(message.getPayload()), topic);
                            if (topic.startsWith(Constants.SEND_DELAY_NOTICE)) {
                                applicationEventPublisher.publishEvent(new SysNoticeHandleEvent(this, topic, message));
                            }

                        }

                        @Override
                        public void deliveryComplete(IMqttDeliveryToken token) {

                        }

                        @Override
                        public void connectionLost(Throwable cause) {
                            cause.printStackTrace();
                        }
                    });
                }
            }
        }
    }


    @Override
    public void publishMessage(String topic, String msg, long second) {
        Objects.requireNonNull(mqttClient, "mqtt服务器异常");
        synchronized (mqttClient) {
            try {
                MqttMessage mqtMsg = new MqttMessage(msg.getBytes(Charset.defaultCharset()));
                mqtMsg.setQos(2);
                mqtMsg.setRetained(false);
                String delayedTopic = String.format("$delayed/%d/%s%s", second, Constants.SEND_DELAY_NOTICE, topic);
                mqttClient.publish(delayedTopic, mqtMsg);
            } catch (MqttException e) {
                throw new BaseException("notice.publish.error", new Object[]{topic});
            }
        }
    }

    @Override
    public void publishMessage(String topic, String msg) {
        Objects.requireNonNull(mqttClient, "mqtt服务器异常");
        synchronized (mqttClient) {
            try {
                MqttMessage mqtMsg = new MqttMessage(msg.getBytes(Charset.defaultCharset()));
                mqtMsg.setQos(2);
                mqtMsg.setRetained(false);
                mqttClient.publish(topic, mqtMsg);
            } catch (MqttException e) {
                throw new BaseException("notice.publish.error", new Object[]{topic});
            }
        }
    }

    @Override
    public SysMqttLoginDto registerCustomerUser() throws NoSuchAlgorithmException {
        if (LoginHelper.isAdmin() && mqttClient != null) {
            synchronized (this) {
                // 创建随机id，避免同账号登录掉线
                admin.setClientId("admin-" + MqttSecurityHelper.GetRandomString(4));
                admin.getTopics().clear();
                admin.getTopics().add("dept/" + Long.toHexString(LoginHelper.getDeptId()));
            }
            return admin;
        }
        Long loginTime = LoginHelper.getLoginUser().getLoginTime();
        String username = LoginHelper.getUsername() + "[" + Long.toHexString(loginTime) + "]";
        Long userId = LoginHelper.getUserId();
        Set<String> topics = Collections.singleton("dept/" + Long.toHexString(LoginHelper.getLoginUser().getDeptId()));
        String loginPwd = MqttSecurityHelper.registerNormalUserMqttUser(username, String.valueOf(userId), mqttProperties.getSalt(), topics, mqttProperties.getTimeout());

        SysMqttLoginDto d = new SysMqttLoginDto();
        d.setUsername(username);
        d.setPwd(loginPwd);
        d.setPort(mqttProperties.getPort());
        d.setRemote(mqttProperties.getRemote());


        UserType userType = LoginHelper.getUserType();
        StringBuilder sb = new StringBuilder();
        if (ObjectUtil.isNotNull(userType.getUserType())) {
            sb.append(userType.getUserType());
        } else {
            sb.append("UNKNOWN");
        }
        sb.append("-").append(username).append("-").append(MqttSecurityHelper.GetRandomString(3));
        d.setClientId(sb.toString());
        d.setTopics(topics);
        return d;
    }

    /**
     * 批量发送
     *
     * @param mqttMsg
     */
    @Override
    public void publishBatchMessage(List<BaseMqttMsgDTO> mqttMsg) {
        synchronized (this) {
            mqttMsg.forEach(a -> {
                publishMessage(a.getTopic(), a.serialize());
            });
        }
    }

    @Override
    public void publishMessage(BaseMqttMsgDTO mqttMsg) {
        synchronized (this) {
            publishMessage(mqttMsg.getTopic(), mqttMsg.serialize());
        }
    }

    /**
     * 发送
     *
     * @param mqttMsg
     */
    @Override
    public void publishMessage(BaseMqttMsgDTO mqttMsg, long second) {
        synchronized (this) {
            publishMessage(mqttMsg.getTopic(), mqttMsg.serialize(), second);
        }
    }
}

