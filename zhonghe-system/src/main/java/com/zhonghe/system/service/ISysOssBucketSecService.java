package com.zhonghe.system.service;

import com.zhonghe.system.domain.vo.SysOssBucketSecVo;
import com.zhonghe.system.domain.bo.SysOssBucketSecBo;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.core.domain.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 存储桶描述Service接口
 *
 * <AUTHOR>
 * @date 2023-04-03
 */
public interface ISysOssBucketSecService {

    /**
     * 查询存储桶描述
     */
    SysOssBucketSecVo queryById(Long id);

    /**
     * 查询存储桶描述列表
     */
    TableDataInfo<SysOssBucketSecVo> queryPageList(SysOssBucketSecBo bo, PageQuery pageQuery);

    /**
     * 查询存储桶描述列表
     */
    List<SysOssBucketSecVo> queryList(SysOssBucketSecBo bo);

    /**
     * 修改存储桶描述
     */
    Boolean insertByBo(SysOssBucketSecBo bo);

    /**
     * 修改存储桶描述
     */
    Boolean updateByBo(SysOssBucketSecBo bo);

    /**
     * 校验并批量删除存储桶描述信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    void createEntityFromOssFile(Long bucketId, String bucketName, String url, boolean create);
}
