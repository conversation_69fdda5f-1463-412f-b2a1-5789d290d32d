package com.zhonghe.system.service;

import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.system.domain.bo.SysOssBucketUploadBo;
import com.zhonghe.system.domain.bo.SysOssBucketUploadCheckBo;
import com.zhonghe.system.domain.bo.SysOssBucketUploadCheckResultBo;
import com.zhonghe.system.domain.dto.BucketFileHashDto;
import com.zhonghe.system.domain.vo.SysOssBucketUploadVo;

import java.util.Collection;
import java.util.List;

/**
 * 桶文件上传Service接口
 *
 * <AUTHOR>
 * @date 2023-04-07
 */
public interface ISysOssBucketUploadService {

    /**
     * 查询桶文件上传
     */
    SysOssBucketUploadVo queryById(Long bucketUploadId);

    /**
     * 查询桶文件上传列表
     */
    TableDataInfo<SysOssBucketUploadVo> queryPageList(SysOssBucketUploadBo bo, PageQuery pageQuery);

    /**
     * 查询桶文件上传列表
     */
    List<SysOssBucketUploadVo> queryList(SysOssBucketUploadBo bo);

    /**
     * 修改桶文件上传
     */
    Boolean insertByBo(SysOssBucketUploadBo bo);

    /**
     * 修改桶文件上传
     */
    Boolean updateByBo(SysOssBucketUploadBo bo);

    /**
     * 校验并批量删除桶文件上传信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    SysOssBucketUploadCheckResultBo checkHugeFileExist(SysOssBucketUploadCheckBo bo);

    void checkAndMergeHugeFile(BucketFileHashDto dto);
}
