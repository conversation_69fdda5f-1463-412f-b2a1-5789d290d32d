package com.zhonghe.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.amazonaws.services.s3.model.PartSummary;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhonghe.common.constant.Constants;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.exception.oss.SysOssException;
import com.zhonghe.common.helper.IdGeneratorHelper;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.common.utils.redis.RedisUtils;
import com.zhonghe.oss.constant.OssConstant;
import com.zhonghe.oss.factory.OssFactory;
import com.zhonghe.system.domain.SysOssBucketSec;
import com.zhonghe.system.domain.bo.SysOssBucketUploadBo;
import com.zhonghe.system.domain.bo.SysOssBucketUploadCheckBo;
import com.zhonghe.system.domain.bo.SysOssBucketUploadCheckResultBo;
import com.zhonghe.system.domain.dto.BucketFileHashDto;
import com.zhonghe.system.domain.vo.SysOssBucketUploadVo;
import com.zhonghe.system.domain.vo.SysOssBucketVo;
import com.zhonghe.system.event.OssCalculatedBucketCapacityEvent;
import com.zhonghe.system.mapper.*;
import com.zhonghe.system.service.ISysOssBucketService;
import com.zhonghe.system.service.ISysOssBucketUploadService;
import com.zhonghe.system.domain.SysOssBucket;
import com.zhonghe.system.domain.SysOssBucketUpload;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 桶文件上传Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-04-07
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class SysOssBucketUploadServiceImpl implements ISysOssBucketUploadService {

    private final SysOssBucketUploadMapper baseMapper;

    private final SysOssBucketMapper ossBucketMapper;

    private final SysOssBucketSecMapper bucketSecMapper;

    private final ApplicationEventPublisher applicationEventPublisher;

    private final SysOssPowerRelationMapper powerRelationMapper;

    private final SysOssBucketDeptMapper ossBucketDeptMapper;

    private final ISysOssBucketService ossBucketService;

    /**
     * 查询桶文件上传
     */
    @Override
    public SysOssBucketUploadVo queryById(Long bucketUploadId) {
        return baseMapper.selectVoById(bucketUploadId);
    }

    /**
     * 查询桶文件上传列表
     */
    @Override
    public TableDataInfo<SysOssBucketUploadVo> queryPageList(SysOssBucketUploadBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SysOssBucketUpload> lqw = buildQueryWrapper(bo);
        Page<SysOssBucketUploadVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询桶文件上传列表
     */
    @Override
    public List<SysOssBucketUploadVo> queryList(SysOssBucketUploadBo bo) {
        LambdaQueryWrapper<SysOssBucketUpload> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SysOssBucketUpload> buildQueryWrapper(SysOssBucketUploadBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SysOssBucketUpload> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getBucketId() != null, SysOssBucketUpload::getBucketId, bo.getBucketId());
        lqw.eq(StringUtils.isNotBlank(bo.getHashcode()), SysOssBucketUpload::getHashcode, bo.getHashcode());
        lqw.eq(bo.getTotalCount() != null, SysOssBucketUpload::getTotalCount, bo.getTotalCount());
        lqw.eq(StringUtils.isNotBlank(bo.getFinish()), SysOssBucketUpload::getFinish, bo.getFinish());
        lqw.eq(bo.getUploadId() != null, SysOssBucketUpload::getUploadId, bo.getUploadId());
        return lqw;
    }

    /**
     * 新增桶文件上传
     */
    @Override
    public Boolean insertByBo(SysOssBucketUploadBo bo) {
        SysOssBucketUpload add = BeanUtil.toBean(bo, SysOssBucketUpload.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setBucketUploadId(add.getBucketUploadId());
        }
        return flag;
    }

    /**
     * 修改桶文件上传
     */
    @Override
    public Boolean updateByBo(SysOssBucketUploadBo bo) {
        SysOssBucketUpload update = BeanUtil.toBean(bo, SysOssBucketUpload.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SysOssBucketUpload entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除桶文件上传
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    @Transactional
    public SysOssBucketUploadCheckResultBo checkHugeFileExist(SysOssBucketUploadCheckBo bo) {
        //查询路径
        SysOssBucket dirBucket = ossBucketMapper.selectById(bo.getBucketId());
        //查询父盘
        SysOssBucketVo parentBucket = ossBucketMapper.selectVoOne(Wrappers.<SysOssBucket>lambdaQuery()
            .eq(SysOssBucket::getBucketId, dirBucket.getBucketId()).eq(SysOssBucket::getParentId, 0));
        //查询父盘容量
        SysOssBucketSec currentCapacity = bucketSecMapper.selectOne(Wrappers.<SysOssBucketSec>lambdaQuery().select(SysOssBucketSec::getCurrentCapacity).eq(SysOssBucketSec::getBucketId, parentBucket.getId()));
        //判断父盘容量是否足够
        if (ObjectUtil.isNotNull(bo.getFileSize()) && ObjectUtil.isNotNull(currentCapacity.getCurrentCapacity()) && (bo.getFileSize() + currentCapacity.getCurrentCapacity()) > parentBucket.getMaxCapacity()) {
            throw new SysOssException("sys.oss.bucket.upload.undercapacity", parentBucket.getNickName());
        }
        SysOssBucketUploadCheckResultBo r = new SysOssBucketUploadCheckResultBo();
        r.setFinish(false);
        String suffix = StringUtils.substring(bo.getFileName(), bo.getFileName().lastIndexOf("."), bo.getFileName().length());
        SysOssBucketUpload history = baseMapper.selectOne(new LambdaQueryWrapper<SysOssBucketUpload>()
            .eq(SysOssBucketUpload::getHashcode, bo.getHashCode())
            .eq(SysOssBucketUpload::getBucketId, bo.getBucketId())
            .eq(SysOssBucketUpload::getFileName, bo.getFileName())
            .eq(SysOssBucketUpload::getFinish, !OssConstant.BUCKET_UPLOAD_FINISH));
        if (ObjectUtil.isNotNull(history)) {
            if (Objects.equals(history.getFinish(), OssConstant.BUCKET_UPLOAD_FINISH)) {
                // 去查minio文件记录
                SysOssBucket bucket = ossBucketMapper.selectById(bo.getBucketId());
                if (ObjectUtil.isNotNull(bucket)) {
                    Long bucketId = bucket.getBucketId();
                    SysOssBucketSec bucketSec = bucketSecMapper.selectOne(new LambdaQueryWrapper<SysOssBucketSec>().eq(SysOssBucketSec::getBucketId, bucketId));
                    if (ObjectUtil.isNotNull(bucketSec)) {
                      /*  String objectKey = OssFactory.instance().copyHugeFileToDestination(history.getMinioBucket(), history.getMinioBucket(),
                            bucketSec.getBucketName(), suffix);
                        // 成功，创建对象
                        SysOssBucket newOne = new SysOssBucket();
                        newOne.setId(IdGeneratorHelper.next());
                        newOne.setStatus(Constants.STATUS);
                        newOne.setBucketId(bucketId);
                        newOne.setBucketType(OssConstant.BUCKET_TYPE_FILE);
                        newOne.setOwnerId(bucket.getOwnerId());
                        newOne.setNickName(bo.getFileName());
                        newOne.setOriginalName(bo.getFileName());
                        newOne.setFileSuffix(suffix);
                        newOne.setParentId(bo.getBucketId());
                        newOne.setMaxCapacity(bucket.getMaxCapacity());

                        //ossBucketMapper.insert(newOne);

                        SysOssBucketSec sec = new SysOssBucketSec();
                        sec.setId(IdGeneratorHelper.next());
                        sec.setBucketId(newOne.getId());
                        sec.setBucketName(bucketSec.getBucketName());
                        sec.setBucketType(OssConstant.BUCKET_TYPE_FILE);
                        sec.setMinioPath(objectKey);
                        sec.setCurrentCapacity(bucketSec.getCurrentCapacity() + bucketSec.getCurrentCapacity());
                        //bucketSecMapper.insert(sec);
*/
                        r.setFinish(true);
                        r.setUrl("/home/" + StringUtils.encrypt(String.valueOf(bucketSec.getId())));
                    }
                }
            } else {
                if (ObjectUtil.isNotNull(history.getUploadId())) {
                    // 去查相minio关已经完成的chunk
                    List<Integer> collect = OssFactory.instance().listUploadChunk(history.getUploadId(), history.getMinioBucket(), history.getMinioKey()).stream()
                        .map(PartSummary::getPartNumber).collect(Collectors.toList());
                    r.setChunkList(collect);
                }
            }
        } else {
            // 去查minio文件,并创建记录
            SysOssBucket bucket = ossBucketMapper.selectById(bo.getBucketId());
            if (ObjectUtil.isNotNull(bucket)) {
                SysOssBucketSec bucketSec = bucketSecMapper.selectOne(new LambdaQueryWrapper<SysOssBucketSec>().eq(SysOssBucketSec::getBucketId, bucket.getBucketId()));
                if (ObjectUtil.isNotNull(bucketSec)) {
                    String objectKey = OssFactory.instance().createObjectKey(suffix);
                    String uploadId = OssFactory.instance().initiateMultipartUpload(bucketSec.getBucketName(), objectKey);

                    history = new SysOssBucketUpload();
                    history.setBucketUploadId(IdGeneratorHelper.next());
                    history.setBucketId(bo.getBucketId());
                    history.setFinish(OssConstant.BUCKET_UPLOAD_FINISH_NOT);
                    history.setMinioBucket(bucketSec.getBucketName());
                    history.setHashcode(bo.getHashCode());
                    history.setTotalCount(bo.getChunkCount());
                    history.setFileName(bo.getFileName());
                    history.setUploadId(uploadId);
                    history.setMinioKey(objectKey);
                    baseMapper.insert(history);

                    BucketFileHashDto dto = new BucketFileHashDto();
                    dto.setHashCode(bo.getHashCode());
                    dto.setBucketName(history.getMinioBucket());
                    dto.setObjectKey(objectKey);
                    dto.setChunkCount(bo.getChunkCount());
                    dto.setUploadId(uploadId);


                    RedisUtils.setCacheMapValue(OssConstant.HUGE_FILE_UPLOAD, bo.getHashCode(), dto);
                }
            }
            // 先放redis里面快速查询
            r.setFinish(false);
            r.setChunkList(Collections.EMPTY_LIST);
        }
        return r;
    }

    @Override
    @Transactional
    public void checkAndMergeHugeFile(BucketFileHashDto dto) {
        List<PartSummary> partSummaries = OssFactory.instance().listUploadChunk(dto.getUploadId(), dto.getBucketName(), dto.getObjectKey());
        if (partSummaries.size() == dto.getChunkCount()) {
            log.info("开始合并分片:{}", dto.getObjectKey());
            long size = OssFactory.instance().mergeUploadFile(dto.getBucketName(), dto.getObjectKey(), dto.getUploadId(), partSummaries);

            SysOssBucketUpload uploadInfo = baseMapper.selectOne(new LambdaQueryWrapper<SysOssBucketUpload>()
                .eq(SysOssBucketUpload::getHashcode, dto.getHashCode())
                .eq(SysOssBucketUpload::getUploadId, dto.getUploadId()));
            String suffix = StringUtils.substring(uploadInfo.getFileName(), uploadInfo.getFileName().lastIndexOf("."), uploadInfo.getFileName().length());

            SysOssBucket bucket = ossBucketMapper.selectById(uploadInfo.getBucketId());

            SysOssBucket newOne = new SysOssBucket();
            newOne.setId(IdGeneratorHelper.next());
            newOne.setStatus(Constants.STATUS);
            newOne.setBucketId(bucket.getBucketId());
            newOne.setBucketType(OssConstant.BUCKET_TYPE_FILE);
            newOne.setOwnerId(bucket.getOwnerId());
            //检测重名文件
            long files = baseMapper.selectList(new LambdaQueryWrapper<SysOssBucketUpload>()
                .eq(SysOssBucketUpload::getHashcode, dto.getHashCode()).eq(SysOssBucketUpload::getBucketId, uploadInfo.getBucketId())).stream().count();
            String finalFileName = uploadInfo.getFileName().substring(0, uploadInfo.getFileName().lastIndexOf("."));
            finalFileName += (files > 1 ? "(" + files + ")" : "");
            newOne.setNickName(finalFileName);
            newOne.setOriginalName(uploadInfo.getFileName());
            newOne.setFileSuffix(suffix);
            newOne.setFileSize(size);
            newOne.setParentId(uploadInfo.getBucketId());
            newOne.setMaxCapacity(bucket.getMaxCapacity());
            ossBucketMapper.insert(newOne);

            SysOssBucketSec sec = new SysOssBucketSec();
            sec.setId(IdGeneratorHelper.next());
            sec.setBucketId(newOne.getId());
            sec.setBucketName(dto.getBucketName());
            sec.setBucketType(OssConstant.BUCKET_TYPE_FILE);
            sec.setMinioPath(dto.getObjectKey());
            bucketSecMapper.insert(sec);


            baseMapper.update(null, new LambdaUpdateWrapper<SysOssBucketUpload>().set(SysOssBucketUpload::getFinish, OssConstant.BUCKET_UPLOAD_FINISH)
                .set(SysOssBucketUpload::getFileName, finalFileName + suffix)
                .eq(SysOssBucketUpload::getBucketUploadId, uploadInfo.getBucketUploadId()));

            /*//创建文件夹自动赋予上机文件夹的权限
            Map<Long, List<Long>> folderPowers = new HashMap<>();
            if (bucket.getBucketType() == OssConstant.BUCKET_TYPE_FOLD) {
                SysOssBucketVo parentBucket = ossBucketMapper.selectVoOne(Wrappers.<SysOssBucket>lambdaQuery().eq(SysOssBucket::getId, bucket.getId()));
                List<SysOssBucketDeptVo> sysOssBucketDeptVos = ossBucketDeptMapper.selectVoList(Wrappers.<SysOssBucketDept>lambdaQuery().eq(SysOssBucketDept::getBucketId, parentBucket.getId()));
                sysOssBucketDeptVos.forEach(s -> {
                    //查询权限
                    List<Long> powers = powerRelationMapper.selectVoList(Wrappers.<SysOssPowerRelation>lambdaQuery()
                            .eq(SysOssPowerRelation::getBucketDeptId, s.getId()))
                        .stream().map(SysOssPowerRelationVo::getId).collect(Collectors.toList());
                    folderPowers.put(s.getDeptId(), powers);
                });
                folderPowers.forEach((k, v) -> {
                    synchronized (this) {
                        SysOssBucketDept sysOssBucketDept = new SysOssBucketDept();
                        sysOssBucketDept.setBucketId(newOne.getId());
                        sysOssBucketDept.setDeptId(k);
                        ossBucketDeptMapper.insert(sysOssBucketDept);
                        for (Long id : v) {
                            SysOssPowerRelation sysOssPowerRelation = new SysOssPowerRelation();
                            sysOssPowerRelation.setBucketDeptId(sysOssBucketDept.getId());

                            SysOssPowerRelation ossPowerRelation = powerRelationMapper.selectOne(Wrappers.<SysOssPowerRelation>lambdaQuery().eq(SysOssPowerRelation::getId, id));
                            sysOssPowerRelation.setPowerDefineId(ossPowerRelation.getPowerDefineId());
                            powerRelationMapper.insert(sysOssPowerRelation);
                        }
                    }
                });*/
            //}
            //添加上级权限
            ossBucketService.addParentPowers(newOne);
            //计算使用容量
            applicationEventPublisher.publishEvent(new OssCalculatedBucketCapacityEvent(this, bucket.getBucketId()));
            RedisUtils.delCacheMapValue(OssConstant.HUGE_FILE_UPLOAD, dto.getHashCode());
        }
    }
}
