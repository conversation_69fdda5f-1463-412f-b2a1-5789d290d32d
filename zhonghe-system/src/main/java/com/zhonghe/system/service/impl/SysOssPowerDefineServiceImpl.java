package com.zhonghe.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.exception.base.BaseException;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.system.domain.SysOssPowerDefine;
import com.zhonghe.system.domain.bo.SysOssPowerDefineBo;
import com.zhonghe.system.domain.vo.SysOssPowerDefineVo;
import com.zhonghe.system.mapper.SysOssPowerDefineMapper;
import com.zhonghe.system.service.ISysOssPowerDefineService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 存储桶权限Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-03-31
 */
@RequiredArgsConstructor
@Service
public class SysOssPowerDefineServiceImpl implements ISysOssPowerDefineService {

    private final Map<String, SysOssPowerDefine> define = new ConcurrentHashMap<>();
    private final SysOssPowerDefineMapper baseMapper;

    /**
     * 查询存储桶权限
     */
    @Override
    public SysOssPowerDefineVo queryById(String power) {
        return baseMapper.selectVoById(power);
    }

    /**
     * 查询存储桶权限列表
     */
    @Override
    public TableDataInfo<SysOssPowerDefineVo> queryPageList(SysOssPowerDefineBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<SysOssPowerDefine> lqw = buildQueryWrapper(bo);
        Page<SysOssPowerDefineVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询存储桶权限列表
     */
    @Override
    public List<SysOssPowerDefineVo> queryList(SysOssPowerDefineBo bo) {
        LambdaQueryWrapper<SysOssPowerDefine> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<SysOssPowerDefine> buildQueryWrapper(SysOssPowerDefineBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<SysOssPowerDefine> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), SysOssPowerDefine::getStatus, bo.getStatus());
        return lqw;
    }

    /**
     * 新增存储桶权限
     */
    @Override
    public Boolean insertByBo(SysOssPowerDefineBo bo) {
        SysOssPowerDefine add = BeanUtil.toBean(bo, SysOssPowerDefine.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setPower(add.getPower());
        }
        return flag;
    }

    /**
     * 修改存储桶权限
     */
    @Override
    public Boolean updateByBo(SysOssPowerDefineBo bo) {
        SysOssPowerDefine update = BeanUtil.toBean(bo, SysOssPowerDefine.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(SysOssPowerDefine entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除存储桶权限
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<String> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public void init() {
        List<SysOssPowerDefine> powerDefines = baseMapper.selectList();
        define.clear();
        powerDefines.forEach(p -> define.put(p.getPower(), p));
    }

    @Override
    public Long getIdByPower(String power) {
        if (define.containsKey(power)) {
            return define.get(power).getId();
        }
        throw new BaseException("不存在权限");
    }

    @Override
    public Collection<String> getAllPowerList() {
        return define.keySet();
    }
}
