package com.zhonghe.system.service;

import cn.dev33.satoken.session.SaSession;
import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zhonghe.common.constant.Constants;
import com.zhonghe.common.constant.UserConstants;
import com.zhonghe.common.core.domain.dto.RoleDTO;
import com.zhonghe.common.core.domain.entity.SysMenu;
import com.zhonghe.common.core.domain.entity.SysUser;
import com.zhonghe.common.core.domain.model.LoginUser;
import com.zhonghe.common.helper.LoginHelper;
import com.zhonghe.common.utils.redis.RedisUtils;
import com.zhonghe.system.domain.SysUserRole;
import com.zhonghe.system.domain.dto.MicroAppDto;
import com.zhonghe.system.mapper.SysUserRoleMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 用户权限处理
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
@Service
public class SysPermissionService {

    private final ISysRoleService roleService;
    private final ISysMenuService menuService;
    private final ISysUserService userService;

    private final SysUserRoleMapper sysUserRoleMapper;

    /**
     * 获取角色数据权限
     *
     * @param user 用户信息
     * @return 角色权限信息
     */
    public Set<String> getRolePermission(SysUser user) {
        Set<String> roles = new HashSet<>();
        // 管理员拥有所有权限
        if (user.isAdmin()) {
            roles.add("admin");
        } else {
            roles.addAll(roleService.selectRolePermissionByUserId(user.getUserId()));
        }
        return roles;
    }

    /**
     * 获取菜单数据权限
     *
     * @param user 用户信息
     * @return 菜单权限信息
     */
    public Set<String> getMenuPermission(SysUser user) {
        Set<String> perms = new HashSet<>();
        // 管理员拥有所有权限
        if (user.isAdmin()) {
            perms.add("*:*:*");
        } else {
            if (RedisUtils.hasKey(UserConstants.SYS_POWER_SWITCH) && !Constants.SYS_POWER_SWITCH_ON.equals(RedisUtils.getCacheObject(UserConstants.SYS_POWER_SWITCH))) {
                // 只拥有查询权限
                perms.add("*:*:*:query");
                perms.add("*:*:*:export");
            } else {
                perms.addAll(menuService.selectMenuPermsByUserId(user.getUserId()));
            }
            //perms.add("*:*:*");
        }
        return perms;
    }

    /**
     * 获取微应用菜单数据权限
     *
     * @param user 用户信息
     * @return 微应用菜单权限信息
     */
    public Map<String, Object> getAppMenusPermission(SysUser user) {
        Map<String, Object> apps = new HashMap<>();
        List<SysMenu> sysMenus = menuService.selectMenuList(user.getUserId());
        sysMenus.stream()
            .filter(a -> !StrUtil.isEmptyIfStr(a.getIsApp()) && a.getIsApp() == 0).forEach(a -> {
                MicroAppDto microAppDto = new MicroAppDto();
                microAppDto.setTitle(a.getMenuName());
                microAppDto.setApp(a.getApp());
                microAppDto.setUrl(a.getAppUrl());
                microAppDto.setName(a.getPageName());
                apps.put(DigestUtil.md5Hex(a.getAppUrl()), microAppDto);
            });
        return apps;
    }

    /**
     * 全局权限开关
     * 0：正常开启
     * 1：关闭
     *
     * @return
     */
    public String getPowerSwitchStatus() {
        if (RedisUtils.hasKey(UserConstants.SYS_POWER_SWITCH)) {
            return RedisUtils.getCacheObject(UserConstants.SYS_POWER_SWITCH);
        }
        return Constants.SYS_POWER_SWITCH_ON;
    }

    /**
     * 更新角色相关用户的权限信息
     *
     * @param roleId
     */
    public void updateRolePermission(Long roleId) {
        List<SysUserRole> sysUserRoles = sysUserRoleMapper.selectList(new LambdaQueryWrapper<SysUserRole>()
            .select(SysUserRole::getUserId)
            .eq(SysUserRole::getRoleId, roleId));
        Set<Long> userIds = sysUserRoles.stream().map(SysUserRole::getUserId).collect(Collectors.toSet());

        List<String> keys = StpUtil.searchTokenValue("", -1, 0);
        for (String key : keys) {
            String token = key.replace(Constants.LOGIN_TOKEN_KEY, "");
            if (StpUtil.stpLogic.getTokenActivityTimeoutByToken(token) > 0) {
                SaSession session = StpUtil.getTokenSessionByToken(token);
                if (ObjectUtil.isNotNull(session)) {
                    Object o = session.get(LoginHelper.LOGIN_USER_KEY);
                    // 不更新管理员权限
                    if (o instanceof LoginUser && !LoginHelper.isAdmin(((LoginUser) o).getUserId()) && userIds.contains(((LoginUser) o).getUserId())) {
                        LoginUser loginUser = (LoginUser) o;
                        SysUser user = userService.selectUserByUserName(loginUser.getUsername());
                        loginUser.setDeptId(user.getDeptId());
                        loginUser.setUsername(user.getUserName());
                        loginUser.setUserType(user.getUserType());
                        loginUser.setNickName(user.getNickName());
                        // 更新role菜单权限
                        loginUser.setMenuPermission(this.getMenuPermission(user));
                        // 更新role角色权限
                        loginUser.setRolePermission(this.getRolePermission(user));
                        loginUser.setDeptName(ObjectUtil.isNull(user.getDept()) ? "" : user.getDept().getDeptName());
                        List<RoleDTO> roles = BeanUtil.copyToList(user.getRoles(), RoleDTO.class);
                        loginUser.setRoles(roles);
                        session.update();
                    }
                }
            }
        }
    }

    /**
     * 只更新用户的机构信息
     *
     * @param userId
     * @param deptId
     */
    public void updateUserPermission(Long userId, Long deptId) {
        List<String> keys = StpUtil.searchTokenValue("", -1, 0);
        for (String key : keys) {
            String token = key.replace(Constants.LOGIN_TOKEN_KEY, "");
            if (StpUtil.stpLogic.getTokenActivityTimeoutByToken(token) > 0) {
                SaSession session = StpUtil.getTokenSessionByToken(token);
                Object o = session.get(LoginHelper.LOGIN_USER_KEY);
                // 不更新管理员权限
                if (o instanceof LoginUser && !LoginHelper.isAdmin(((LoginUser) o).getUserId()) && Objects.equals(userId, ((LoginUser) o).getUserId())) {
                    LoginUser loginUser = (LoginUser) o;
                    loginUser.setDeptId(deptId);
                    session.update();
                }
            }
        }
    }


}
