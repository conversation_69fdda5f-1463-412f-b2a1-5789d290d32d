package com.zhonghe.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zhonghe.common.constant.Constants;
import com.zhonghe.common.constant.UserConstants;
import com.zhonghe.common.core.domain.entity.SysMenu;
import com.zhonghe.common.core.domain.entity.SysRole;
import com.zhonghe.common.exception.ServiceException;
import com.zhonghe.common.helper.LoginHelper;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.common.utils.TreeBuildUtils;
import com.zhonghe.system.domain.SysRoleMenu;
import com.zhonghe.system.domain.bo.SysMenuPermissionBo;
import com.zhonghe.system.domain.vo.MetaVo;
import com.zhonghe.system.domain.vo.RouterVo;
import com.zhonghe.system.event.SysRolePermissionUpdateEvent;
import com.zhonghe.system.mapper.SysMenuMapper;
import com.zhonghe.system.mapper.SysRoleMapper;
import com.zhonghe.system.mapper.SysRoleMenuMapper;
import com.zhonghe.system.service.ISysMenuService;
import lombok.RequiredArgsConstructor;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 菜单 业务层处理
 *
 * <AUTHOR> Li
 */
@RequiredArgsConstructor
@Service
public class SysMenuServiceImpl implements ISysMenuService {

    private final SysMenuMapper baseMapper;
    private final SysRoleMapper roleMapper;
    private final SysRoleMenuMapper roleMenuMapper;

    private final ApplicationEventPublisher applicationEventPublisher;
    private final SysRoleMenuMapper sysRoleMenuMapper;

    public static void main(String[] args) {
        System.out.println("/application/" + DigestUtil.md5Hex("http://19.241.3.16:22026/map"));
    }

    /**
     * 根据用户查询系统菜单列表
     *
     * @param userId 用户ID
     * @return 菜单列表
     */
    @Override
    public List<SysMenu> selectMenuList(Long userId) {
        return selectMenuList(new SysMenu(), userId);
    }

    /**
     * 查询系统菜单列表
     *
     * @param menu 菜单信息
     * @return 菜单列表
     */
    @Override
    public List<SysMenu> selectMenuList(SysMenu menu, Long userId) {
        List<SysMenu> menuList = null;

        QueryWrapper<SysMenu> wrapper = Wrappers.query();
        LambdaQueryWrapper<SysMenu> lambdaQueryWrapper = wrapper.lambda();
        lambdaQueryWrapper.like(StringUtils.isNotBlank(menu.getMenuName()), SysMenu::getMenuName, menu.getMenuName())
            .eq(StringUtils.isNotBlank(menu.getVisible()), SysMenu::getVisible, menu.getVisible())
            .orderByAsc(SysMenu::getParentId)
            .orderByAsc(SysMenu::getOrderNum);

        if (ObjectUtil.isNotNull(menu.getMenuType_NOT())) {
            lambdaQueryWrapper.notIn(SysMenu::getMenuType, menu.getMenuType_NOT().split(","));
        }
        // 管理员显示所有菜单信息
        if (LoginHelper.isAdmin(userId)) {
            lambdaQueryWrapper.eq(StringUtils.isNotBlank(menu.getStatus()), SysMenu::getStatus, menu.getStatus());
            menuList = baseMapper.selectList(lambdaQueryWrapper);
        } else {
            wrapper.eq("sur.user_id", userId);
            wrapper.eq(StringUtils.isNotBlank(menu.getStatus()), "m.status", menu.getStatus());
            menuList = baseMapper.selectMenuListByUserId(lambdaQueryWrapper);
        }
        return menuList;
    }

    /**
     * 根据用户ID查询权限
     *
     * @param userId 用户ID
     * @return 权限列表
     */
    @Override
    public Set<String> selectMenuPermsByUserId(Long userId) {
        List<String> perms = baseMapper.selectMenuPermsByUserId(userId);
        Set<String> permsSet = new HashSet<>();
        for (String perm : perms) {
            if (StringUtils.isNotEmpty(perm)) {
                permsSet.addAll(Arrays.asList(perm.trim().split(",")));
            }
        }
        return permsSet;
    }

    /**
     * 根据用户ID查询菜单
     *
     * @param userId 用户名称
     * @return 菜单列表
     */
    @Override
    public List<SysMenu> selectMenuTreeByUserId(Long userId) {
        List<SysMenu> menus = null;
        if (LoginHelper.isAdmin(userId)) {
            menus = baseMapper.selectMenuTreeAll();

        } else {
            menus = baseMapper.selectMenuTreeByUserId(userId);
//            return getChildPerms(menus,2000);
        }
        return getChildPerms(menus, 0);
    }

    /**
     * 根据角色ID查询菜单树信息
     *
     * @param roleId 角色ID
     * @return 选中菜单列表
     */
    @Override
    public List<Long> selectMenuListByRoleId(Long roleId) {
        SysRole role = roleMapper.selectById(roleId);
        return baseMapper.selectMenuListByRoleId(roleId, role.getMenuCheckStrictly());
    }

    /**
     * 构建前端路由所需要的菜单
     *
     * @param menus 菜单列表
     * @return 路由列表
     */
    @Override
    public List<RouterVo> buildMenus(List<SysMenu> menus) {
        List<RouterVo> routers = new LinkedList<RouterVo>();
        for (SysMenu menu : menus) {
            RouterVo router = new RouterVo();
            router.setHidden("1".equals(menu.getVisible()));
//            router.setName(getRouteName(menu));
            router.setName(menu.getPageName());
//            router.setPath(getRouterPath(menu));
            router.setPath(menu.getPath());
            router.setComponent(getComponent(menu));
            router.setQuery(menu.getQueryParam());
            router.setMeta(new MetaVo(menu.getMenuName(), menu.getIcon(), StringUtils.equals("1", menu.getIsCache().toString()), menu.getPath()));
            List<SysMenu> cMenus = menu.getChildren();
            if (!cMenus.isEmpty() && UserConstants.TYPE_DIR.equals(menu.getMenuType())) {
                router.setAlwaysShow(true);
                router.setRedirect("noRedirect");
                router.setChildren(buildMenus(cMenus));
            } else if (isMenuFrame(menu)) {
                router.setMeta(null);
                List<RouterVo> childrenList = new ArrayList<RouterVo>();
                RouterVo children = new RouterVo();
                children.setPath(menu.getPath());
                children.setComponent(menu.getComponent());
                children.setName(StringUtils.capitalize(menu.getPath()));
                children.setMeta(new MetaVo(menu.getMenuName(), menu.getIcon(), StringUtils.equals("1", menu.getIsCache().toString()), menu.getPath()));
                children.setQuery(menu.getQueryParam());
                childrenList.add(children);
                router.setChildren(childrenList);
            } else if (menu.getParentId().intValue() == 0 && isInnerLink(menu)) {
                router.setMeta(new MetaVo(menu.getMenuName(), menu.getIcon()));
                router.setPath("/");
                List<RouterVo> childrenList = new ArrayList<RouterVo>();
                RouterVo children = new RouterVo();
                String routerPath = innerLinkReplaceEach(menu.getPath());
                children.setPath(routerPath);
                children.setComponent(UserConstants.INNER_LINK);
                children.setName(StringUtils.capitalize(routerPath));
                children.setMeta(new MetaVo(menu.getMenuName(), menu.getIcon(), menu.getPath()));
                childrenList.add(children);
                router.setChildren(childrenList);
            }
            routers.add(router);
        }
        return routers;
    }

    /**
     * 构建前端所需要下拉树结构
     *
     * @param menus 菜单列表
     * @return 下拉树结构列表
     */
    @Override
    public List<Tree<Long>> buildMenuTreeSelect(List<SysMenu> menus) {
        if (CollUtil.isEmpty(menus)) {
            return CollUtil.newArrayList();
        }
        return TreeBuildUtils.build(menus, (menu, tree) ->
            tree.setId(menu.getMenuId())
                .setParentId(menu.getParentId())
                .setName(menu.getMenuName())
                .setWeight(menu.getOrderNum()));
    }

    /**
     * 根据菜单ID查询信息
     *
     * @param menuId 菜单ID
     * @return 菜单信息
     */
    @Override
    public SysMenu selectMenuById(Long menuId) {
        return baseMapper.selectById(menuId);
    }

    /**
     * 是否存在菜单子节点
     *
     * @param menuId 菜单ID
     * @return 结果
     */
    @Override
    public boolean hasChildByMenuId(Long menuId) {
        return baseMapper.exists(new LambdaQueryWrapper<SysMenu>().eq(SysMenu::getParentId, menuId));
    }

    @Override
    public Boolean checkMenuStatus(SysMenu sysMenu) {
        SysMenu menu = baseMapper.selectById(sysMenu.getMenuId());
        if (ObjectUtil.isNotNull(menu.getStatus()) && menu.getStatus().equals(Constants.FAIL)) {
            return false;
        }
        return true;
    }

    @Override
    public String checkMenuIsHome(SysMenu sysMenu) {
        SysMenu menu = baseMapper.selectById(sysMenu.getMenuId());
        if (ObjectUtil.isNotNull(menu.getIsHome()) && String.valueOf(menu.getIsHome()).equals(Constants.STATUS) && sysMenu.getStatus().equals(Constants.FAIL)) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    @Override
    public String checkMenuIdentifier(SysMenu sysMenu) {
        boolean exist = baseMapper.exists(Wrappers.<SysMenu>lambdaQuery().eq(ObjectUtil.isNotNull(sysMenu.getPageName()), SysMenu::getPageName, sysMenu.getPageName()));
        if (exist) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 查询菜单使用数量
     *
     * @param menuId 菜单ID
     * @return 结果
     */
    @Override
    public boolean checkMenuExistRole(Long menuId) {
        return roleMenuMapper.exists(new LambdaQueryWrapper<SysRoleMenu>().eq(SysRoleMenu::getMenuId, menuId));
    }

    /**
     * 新增保存菜单信息
     *
     * @param menu 菜单信息
     * @return 结果
     */
    @Override
    public Long insertMenu(SysMenu menu) {
        //设置微应用URL
        genAppUrl(menu);

        baseMapper.insert(menu);
        return baseMapper.selectFastId();
    }

    /**
     * 修改保存菜单信息
     *
     * @param menu 菜单信息
     * @return 结果
     */
    @Override
    public int updateMenu(SysMenu menu) {
        //设置微应用URL
        genAppUrl(menu);

        return baseMapper.updateById(menu);
    }

    public String genAppUrl(SysMenu menu) {
        if (StrUtil.isNotBlank(menu.getAppUrl())) {
            menu.setPath("/application/" + DigestUtil.md5Hex(menu.getAppUrl()));
            menu.setAppUrl(menu.getAppUrl());
        }
        return menu.getAppUrl();
    }

    /**
     * 删除菜单管理信息
     *
     * @param menuId 菜单ID
     * @return 结果
     */
    @Override
    public int deleteMenuById(Long menuId) {
        return baseMapper.deleteById(menuId);
    }

    @Override
    public SysMenu selectOneMenuByIsHome(Integer isHome) {
        return baseMapper.selectOneMenuByIsHome(isHome);
    }

    @Override
    public void testBath() {
        List<SysMenu> sysMenus = baseMapper.insertSysRoleMenuId();
        List<Long> collect = sysMenus.stream().map(SysMenu::getMenuId).collect(Collectors.toList());
        List<SysRoleMenu> sysRoleMenus = new ArrayList<>();
        for (Long aLong : collect) {
            SysRoleMenu sysRoleMenu = new SysRoleMenu();
            sysRoleMenu.setMenuId(aLong);
            sysRoleMenu.setRoleId(1635937018736340993L);
            sysRoleMenus.add(sysRoleMenu);
        }
        sysRoleMenuMapper.insertBatch(sysRoleMenus);


        System.out.println("结束插入数据");
    }

    @Override
    public List<SysMenu> permissionList(SysMenu menu) {
        if (ObjectUtil.isNull(menu)) {
            return new ArrayList<>();
        }
        List<SysMenu> sysMenus = baseMapper.selectList(new LambdaQueryWrapper<SysMenu>()
            .eq(SysMenu::getParentId, menu.getMenuId()));
        return sysMenus;
    }

    /**
     * 自定义菜单权限
     *
     * @param menu
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer addPermissionList(List<SysMenu> menu) {
        List<Long> saveItems = menu.stream().map(SysMenu::getMenuId)
            .filter(ObjectUtil::isNotNull).collect(Collectors.toList());

        baseMapper.delete(Wrappers.<SysMenu>lambdaQuery()
            .eq(SysMenu::getParentId, menu.get(0).getParentId())
            .notIn(CollectionUtils.isNotEmpty(saveItems), SysMenu::getMenuId, saveItems)
            .eq(SysMenu::getMenuType, "F"));

        menu.forEach(m -> {
            m.setMenuType("F");
            m.setStatus(Constants.STATUS);
        });
        boolean b = baseMapper.insertOrUpdateBatch(menu);
        return b ? 1 : 0;
    }

    /**
     * 菜单权限跟角色绑定
     *
     * @param menu
     * @param roleId
     * @return
     */
    @Override
    public Integer bingPermissionRole(List<SysMenu> menu, Long roleId) {
        List<SysRoleMenu> sysRoleMenus = new ArrayList<>();
        for (SysMenu sysMenu : menu) {
            SysRoleMenu sysRoleMenu = new SysRoleMenu();
            sysRoleMenu.setRoleId(roleId);
            sysRoleMenu.setMenuId(sysMenu.getMenuId());
            sysRoleMenus.add(sysRoleMenu);
        }
        boolean b = sysRoleMenuMapper.insertBatch(sysRoleMenus);
        return b ? 1 : 0;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean saveMenuPermission(SysMenuPermissionBo menuPermissionBo) {
        boolean pass = true;
        //查询角色菜单权限列表
        List<Long> menuIds = sysRoleMenuMapper.selectList(Wrappers.<SysRoleMenu>lambdaQuery()
                .eq(SysRoleMenu::getRoleId, menuPermissionBo.getRoleId()))
            .stream().map(SysRoleMenu::getMenuId)
            .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(menuIds)) {
            throw new ServiceException("角色菜单权限为空");
        }

        //查询页面权限
        if (CollectionUtils.isNotEmpty(menuIds)) {
            List<Long> buttonPermission = baseMapper.selectList(Wrappers.<SysMenu>lambdaQuery()
                .in(SysMenu::getMenuId, menuIds)
                .eq(SysMenu::getParentId, menuPermissionBo.getMenuId())
                .eq(SysMenu::getMenuType, UserConstants.MENU_BUTTON)).stream().map(SysMenu::getMenuId).collect(Collectors.toList());
            //删除原有的页面权限
            if (CollectionUtils.isNotEmpty(buttonPermission)) {
                sysRoleMenuMapper.delete(Wrappers.<SysRoleMenu>lambdaQuery()
                    .eq(SysRoleMenu::getRoleId, menuPermissionBo.getRoleId())
                    .in(SysRoleMenu::getMenuId, buttonPermission));
            }
        }
        //保存页面权限
        List<SysRoleMenu> sysRoleMenus = new ArrayList<>();
        for (Long menuId : menuPermissionBo.getPermissionId()) {
            SysRoleMenu sysRoleMenu = new SysRoleMenu();
            sysRoleMenu.setRoleId(menuPermissionBo.getRoleId());
            sysRoleMenu.setMenuId(menuId);
            sysRoleMenus.add(sysRoleMenu);
        }

        if (CollectionUtils.isNotEmpty(sysRoleMenus)) {
            pass = sysRoleMenuMapper.insertBatch(sysRoleMenus);
        }
        applicationEventPublisher.publishEvent(new SysRolePermissionUpdateEvent(this, menuPermissionBo.getRoleId()));

        return pass;
    }

    @Override
    public List<Tree<Long>> selectRoleMenuTreeByRoleId(Long roleId) {
        List<SysMenu> roleMenus = baseMapper.selectSysMenuByRoleId(roleId).stream()
            .filter(
                a -> UserConstants.MENU_NORMAL.equals(a.getStatus())
                    && !a.getMenuType().equals(UserConstants.MENU_BUTTON)
                    && !UserConstants.MENU_VISIBLE_HIDDEN.equals(a.getVisible())
            ).collect(Collectors.toList());
        return buildMenuTreeSelect(roleMenus);
    }

    @Override
    public Map<String, Object> selectMenuByParentId(Long parentId, Long roleId) {
        Map<String, Object> res = new HashMap<>();
        List<SysMenu> sysMenus = baseMapper.selectList(Wrappers.<SysMenu>lambdaQuery().eq(SysMenu::getParentId, parentId));
        List<Long> checkMenuIds = sysRoleMenuMapper.selectVoList(Wrappers.<SysRoleMenu>lambdaQuery().eq(SysRoleMenu::getRoleId, roleId)).stream().map(SysRoleMenu::getMenuId).collect(Collectors.toList());
        res.put("menus", sysMenus);
        res.put("checkedKeys", sysMenus.stream().filter(a -> checkMenuIds.contains(a.getMenuId())).map(SysMenu::getMenuId).collect(Collectors.toList()));
        return res;
    }

    @Override
    public Set<Long> selectMenuListExcludeParentId(Long roleId) {
        List<SysMenu> sysMenus = baseMapper.selectSysMenuByRoleId(roleId).stream().filter(a -> !UserConstants.MENU_BUTTON.equals(a.getMenuType())).collect(Collectors.toList());

        //显示数据只返回叶子节点
        sysMenus.stream().forEach(a -> a.setId(a.getMenuId()));
        Set<Long> menuIds = TreeBuildUtils.getLeafNodeEntity(sysMenus).stream().map(SysMenu::getMenuId).collect(Collectors.toSet());
        return menuIds;
    }

    @Override
    public String checkMenuPathUnique(SysMenu menu) {
        boolean exist = baseMapper.exists(new LambdaQueryWrapper<SysMenu>()
            .eq(SysMenu::getPath, menu.getPath())
            .eq(SysMenu::getParentId, menu.getParentId())
            .ne(ObjectUtil.isNotNull(menu.getMenuId()), SysMenu::getMenuId, menu.getMenuId()));
        if (exist) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 校验菜单名称是否唯一
     *
     * @param menu 菜单信息
     * @return 结果
     */
    @Override
    public String checkMenuNameUnique(SysMenu menu) {
        boolean exist = baseMapper.exists(new LambdaQueryWrapper<SysMenu>()
            .eq(SysMenu::getMenuName, menu.getMenuName())
            .eq(SysMenu::getParentId, menu.getParentId())
            .ne(ObjectUtil.isNotNull(menu.getMenuId()), SysMenu::getMenuId, menu.getMenuId()));
        if (exist) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 获取路由名称
     *
     * @param menu 菜单信息
     * @return 路由名称
     */
    public String getRouteName(SysMenu menu) {
        String routerName = StringUtils.capitalize(menu.getPath());
        // 非外链并且是一级目录（类型为目录）
        if (isMenuFrame(menu)) {
            routerName = StringUtils.EMPTY;
        }
        return routerName;
    }

    /**
     * 获取路由地址
     *
     * @param menu 菜单信息
     * @return 路由地址
     */
    public String getRouterPath(SysMenu menu) {
        String routerPath = menu.getPath();
        // 内链打开外网方式
        if (menu.getParentId().intValue() != 0 && isInnerLink(menu)) {
            routerPath = innerLinkReplaceEach(routerPath);
        }
        // 非外链并且是一级目录（类型为目录）
        if (0 == menu.getParentId().intValue() && UserConstants.TYPE_DIR.equals(menu.getMenuType())
            && UserConstants.NO_FRAME.equals(menu.getIsFrame())) {
            routerPath = "/" + menu.getPath();
        }
        // 非外链并且是一级目录（类型为菜单）
        else if (isMenuFrame(menu)) {
            routerPath = "/";
        }
        return routerPath;
    }

    /**
     * 获取组件信息
     *
     * @param menu 菜单信息
     * @return 组件信息
     */
    public String getComponent(SysMenu menu) {
        String component = UserConstants.LAYOUT;
        if (StringUtils.isNotEmpty(menu.getComponent()) && !isMenuFrame(menu)) {
            component = menu.getComponent();
        } else if (StringUtils.isEmpty(menu.getComponent()) && menu.getParentId().intValue() != 0 && isInnerLink(menu)) {
            component = UserConstants.INNER_LINK;
        } else if (StringUtils.isEmpty(menu.getComponent()) && isParentView(menu)) {
            component = UserConstants.PARENT_VIEW;
        }
        return component;
    }

    /**
     * 是否为菜单内部跳转
     *
     * @param menu 菜单信息
     * @return 结果
     */
    public boolean isMenuFrame(SysMenu menu) {
        return menu.getParentId().intValue() == 0 && UserConstants.TYPE_MENU.equals(menu.getMenuType())
            && menu.getIsFrame().equals(UserConstants.NO_FRAME);
    }

    /**
     * 是否为内链组件
     *
     * @param menu 菜单信息
     * @return 结果
     */
    public boolean isInnerLink(SysMenu menu) {
        return menu.getIsFrame().equals(UserConstants.NO_FRAME) && StringUtils.ishttp(menu.getPath());
    }

    /**
     * 是否为parent_view组件
     *
     * @param menu 菜单信息
     * @return 结果
     */
    public boolean isParentView(SysMenu menu) {
        return menu.getParentId().intValue() != 0 && UserConstants.TYPE_DIR.equals(menu.getMenuType());
    }

    /**
     * 根据父节点的ID获取所有子节点
     *
     * @param list     分类表
     * @param parentId 传入的父节点ID
     * @return String
     */
    public List<SysMenu> getChildPerms(List<SysMenu> list, int parentId) {
        List<SysMenu> returnList = new ArrayList<SysMenu>();
        for (SysMenu t : list) {
            // 一、根据传入的某个父节点ID,遍历该父节点的所有子节点
            if (t.getParentId() == parentId) {
                recursionFn(list, t);
                returnList.add(t);
            }
        }
        return returnList;
    }

    public List<SysMenu> getChildPerms(List<SysMenu> list) {
        List<SysMenu> returnList = new ArrayList<SysMenu>();
        for (SysMenu t : list) {
            // 一、根据传入的某个父节点ID,遍历该父节点的所有子节点
            recursionFn(list, t);
            returnList.add(t);
        }
        return returnList;
    }

    /**
     * 递归列表
     *
     * @param list
     * @param t
     */
    private void recursionFn(List<SysMenu> list, SysMenu t) {
        // 得到子节点列表
        List<SysMenu> childList = getChildList(list, t);
        t.setChildren(childList);
        for (SysMenu tChild : childList) {
            if (hasChild(list, tChild)) {
                recursionFn(list, tChild);
            }
        }
    }

    /**
     * 得到子节点列表
     */
    private List<SysMenu> getChildList(List<SysMenu> list, SysMenu t) {
        List<SysMenu> tlist = new ArrayList<SysMenu>();
        for (SysMenu n : list) {
            if (n.getParentId().longValue() == t.getMenuId().longValue()) {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<SysMenu> list, SysMenu t) {
        return getChildList(list, t).size() > 0;
    }

    /**
     * 内链域名特殊字符替换
     *
     * @return
     */
    public String innerLinkReplaceEach(String path) {
        return StringUtils.replaceEach(path, new String[]{Constants.HTTP, Constants.HTTPS},
            new String[]{"", ""});
    }
}
