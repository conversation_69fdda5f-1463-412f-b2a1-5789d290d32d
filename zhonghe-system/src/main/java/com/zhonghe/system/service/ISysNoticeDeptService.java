package com.zhonghe.system.service;

import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.system.domain.bo.SysNoticeDeptBo;
import com.zhonghe.system.domain.vo.SysNoticeDeptVo;

import java.util.Collection;
import java.util.List;

/**
 * 1Service接口
 *
 * <AUTHOR>
 * @date 2023-03-22
 */
public interface ISysNoticeDeptService {

    /**
     * 查询1
     */
    SysNoticeDeptVo queryById(Long noticeId);

    /**
     * 查询1列表
     */
    TableDataInfo<SysNoticeDeptVo> queryPageList(SysNoticeDeptBo bo, PageQuery pageQuery);

    /**
     * 查询1列表
     */
    List<SysNoticeDeptVo> queryList(SysNoticeDeptBo bo);

    /**
     * 修改1
     */
    Boolean insertByBo(SysNoticeDeptBo bo);

    /**
     * 修改1
     */
    Boolean updateByBo(SysNoticeDeptBo bo);

    /**
     * 校验并批量删除1信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
