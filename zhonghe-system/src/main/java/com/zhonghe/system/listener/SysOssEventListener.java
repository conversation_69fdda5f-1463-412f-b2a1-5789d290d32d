package com.zhonghe.system.listener;

import com.zhonghe.system.event.OssCalculatedBucketCapacityEvent;
import com.zhonghe.system.event.OssCreateSysFileDBEntityEvent;
import com.zhonghe.system.event.SysOssBucketDeleteBucketEvent;
import com.zhonghe.system.event.SysOssBucketDeleteEvent;
import com.zhonghe.system.event.SysOssBucketHugeFileMergeEvent;
import com.zhonghe.system.event.SysOssCreateBucketSecDBEntityEvent;
import com.zhonghe.system.service.ISysOssBucketSecService;
import com.zhonghe.system.service.ISysOssBucketService;
import com.zhonghe.system.service.ISysOssBucketUploadService;
import com.zhonghe.system.service.ISysOssService;
import lombok.RequiredArgsConstructor;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * @description:
 * @author: cq
 * @date: 2023/4/3 15:49
 */

@RequiredArgsConstructor
@Component
public class SysOssEventListener {


    private final ISysOssBucketSecService sysOssBucketSecService;
    private final ISysOssBucketService sysOssBucketService;
    private final ISysOssService sysOssService;

    private final ISysOssBucketUploadService ossBucketUploadService;

    @EventListener
    public void recordFileInfo(OssCreateSysFileDBEntityEvent event) {
        sysOssBucketService.createEntityFromOssFile(event.getParentId(), event.getOss());
    }


    @EventListener
    public void recordBucketSecInfo(SysOssCreateBucketSecDBEntityEvent event) {
        if (event.isCreate()) {
            sysOssService.createBucket(event.getBucketName());
        }
        sysOssBucketSecService.createEntityFromOssFile(event.getBucketId(), event.getBucketName(), event.getUrl(),event.isCreate());
    }

    @EventListener
    public void recordBucketSecInfo(OssCalculatedBucketCapacityEvent event) {
        sysOssBucketService.calculatedBucketCapacity(event.getBucketId());
    }
    @EventListener
    public void calculatedBucketCapacity(OssCalculatedBucketCapacityEvent event) {
        sysOssBucketService.calculatedBucketCapacity(event.getBucketId());
    }
    @EventListener
    public void SysOssBucketDeleteEvent(SysOssBucketDeleteEvent event) {
        event.getIds().stream().forEach(id -> sysOssService.setObjectDelayDeleteTag(id));
    }

    @EventListener
    @Async
    public void deleteBucketEvent(SysOssBucketDeleteBucketEvent event) {
        event.getBucketIds().forEach(sysOssService::setBucketDelayTag);
    }

    @EventListener
    @Async
    public void mergeHugeFileAndCreateDbEntity(SysOssBucketHugeFileMergeEvent event) {
        ossBucketUploadService.checkAndMergeHugeFile(event.getDto());
    }
}

