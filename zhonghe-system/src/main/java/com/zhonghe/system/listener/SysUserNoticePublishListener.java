package com.zhonghe.system.listener;

import com.zhonghe.system.domain.SysNotice;
import com.zhonghe.system.event.SysNoticeMqttSendMsgEvent;
import com.zhonghe.system.event.SysNoticePublishEvent;
import com.zhonghe.system.service.ISysMqttService;
import com.zhonghe.system.service.ISysUserNoticeService;
import lombok.AllArgsConstructor;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @description:
 * @author: cq
 * @date: 2023/3/23 17:43
 */

@Component
@AllArgsConstructor
public class SysUserNoticePublishListener {

    private final ISysUserNoticeService userNoticeService;

    private final ISysMqttService mqttService;

    @EventListener
    @Async
    public void handlerUserNoticeMessage(SysNoticePublishEvent event) {
        // 消息原型，其中包含延时消息和即时消息
        List<SysNotice> notices = event.getNotices();
        // 依据消息中心模板发送用户消息
        userNoticeService.createAndNotifyUserNotice(notices);
    }

    @EventListener
    @Async
    public void handlerMqttPublishMessage(SysNoticeMqttSendMsgEvent event) {
        mqttService.publishBatchMessage(event.getMsgs());
    }
}

