package com.zhonghe.system.listener;

import com.zhonghe.system.event.AbstractSysPermissionEvent;
import com.zhonghe.system.event.SysRolePermissionUpdateEvent;
import com.zhonghe.system.event.SysUserInformationUpdateEvent;
import com.zhonghe.system.service.SysPermissionService;
import lombok.RequiredArgsConstructor;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * @description:
 * @author: cq
 * @date: 2023/3/20 11:52
 */

@RequiredArgsConstructor
@Component
public class SysPermissionEventListener {

    private final SysPermissionService permissionService;
    @EventListener
    public void onApplicationEvent(AbstractSysPermissionEvent event) {
        if (event instanceof SysRolePermissionUpdateEvent) {
            permissionService.updateRolePermission(((SysRolePermissionUpdateEvent) event).getRoleId());
        } else if (event instanceof SysUserInformationUpdateEvent) {
            permissionService.updateUserPermission(((SysUserInformationUpdateEvent) event).getUserId(), ((SysUserInformationUpdateEvent) event).getDeptId());
        }
    }
}

