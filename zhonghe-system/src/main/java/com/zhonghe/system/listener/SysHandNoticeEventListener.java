package com.zhonghe.system.listener;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zhonghe.common.constant.Constants;
import com.zhonghe.common.constant.ServiceConstants;
import com.zhonghe.common.core.domain.dto.UserNoticeMsgDTO;
import com.zhonghe.system.domain.SysNotice;
import com.zhonghe.system.domain.SysNoticeDept;
import com.zhonghe.system.domain.SysUserNotice;
import com.zhonghe.system.domain.vo.SysNoticeDeptVo;
import com.zhonghe.system.event.SysNoticeHandleEvent;
import com.zhonghe.system.event.SysNoticeMqttSendMsgEvent;
import com.zhonghe.system.event.SysRobotSendEvent;
import com.zhonghe.system.mapper.SysNoticeDeptMapper;
import com.zhonghe.system.service.ISysNoticeService;
import com.zhonghe.system.service.ISysUserNoticeService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: lpg
 * @Date: 2023/03/27/18:41
 * @Description:
 */
@Slf4j
@Component
@AllArgsConstructor
public class SysHandNoticeEventListener {

    private final ISysNoticeService sysNoticeService;
    private final ISysUserNoticeService sysUserNoticeService;

    private final ApplicationEventPublisher applicationEventPublisher;

    @EventListener
    @Async
    @Transactional
    public void handleUserNoticeMessage(SysNoticeHandleEvent event) {
        //原始消息
        SysNotice sysNotice = sysNoticeService.selectNoticeById(Long.valueOf(event.getMessage().toString()));
        if (ObjectUtil.isNotNull(sysNotice) && sysNotice.getSendStatus() == ServiceConstants.NOTICE_SEND_TYPE_NORMAL && sysNotice.getStatus().equals(ServiceConstants.NOTICE_SEND_STATUS_START)) {
            log.info("时间误差:{}", System.currentTimeMillis() / 1000 - (ObjectUtil.isNotEmpty(sysNotice.getSendTime()) ? sysNotice.getSendTime().getTime() / 1000 : 0L));
            if (ObjectUtil.isNotEmpty(sysNotice.getSendTime()) && System.currentTimeMillis() / 1000 <= 60 + sysNotice.getSendTime().getTime() / 1000) {
                SysUserNotice user = new SysUserNotice();
                BeanUtils.copyProperties(sysNotice, user);
                sysUserNoticeService.insertBatch(Collections.singletonList(user));

                sysNotice.setSendStatus(ServiceConstants.NOTICE_SEND_STATUS_OK);
                sysNotice.setStatus(Constants.FAIL);
                sysNoticeService.update(sysNotice);

                UserNoticeMsgDTO userNoticeMsgDTO = new UserNoticeMsgDTO(event.getTopic().replace(Constants.SEND_DELAY_NOTICE, ""));
                userNoticeMsgDTO.setLevel(sysNotice.getLevel());
                userNoticeMsgDTO.setUserNoticeId(user.getUserNoticeId());
                //发送MQTT消息
                applicationEventPublisher.publishEvent(new SysNoticeMqttSendMsgEvent(this, Collections.singletonList(userNoticeMsgDTO)));
                //发送机器人消息
                String deptId = event.getTopic().replace(Constants.SEND_DELAY_NOTICE + "dept/", "");
                applicationEventPublisher.publishEvent(new SysRobotSendEvent(this, Long.parseLong(deptId, 16), Collections.singletonList(sysNotice)));
            }
        }
    }



}
