package com.zhonghe.system.listener;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zhonghe.common.constant.UserConstants;
import com.zhonghe.system.domain.SysNotice;
import com.zhonghe.system.domain.SysRobotMsg;
import com.zhonghe.system.domain.SysRobotRelevanceDepart;
import com.zhonghe.system.mapper.SysRobotRelevanceDepartMapper;
import com.zhonghe.system.service.IPushRobotMessageService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: lpg
 * @Date: 2023/03/27/18:41
 * @Description:
 */
@Slf4j
@Component
@AllArgsConstructor
public class SysRobotSendEvent {

    @Autowired
    private SysRobotRelevanceDepartMapper robotRelevanceDepartMapper;


    @Autowired
    private IPushRobotMessageService pushRobotMessageService;

    @EventListener
    @Async
    @Transactional
    public void onApplicationEvent(com.zhonghe.system.event.SysRobotSendEvent event) {
        //处理事件
        List<SysRobotRelevanceDepart> sysRobotRelevanceDeparts = robotRelevanceDepartMapper.selectList(new LambdaQueryWrapper<SysRobotRelevanceDepart>()
            .eq(event.getDeptId() != null, SysRobotRelevanceDepart::getDepartId, event.getDeptId()));

        List<Long> collect = sysRobotRelevanceDeparts.stream().map(SysRobotRelevanceDepart::getRobotId).collect(Collectors.toList());

        for (SysNotice notice : event.getSysNotice()) {
            try {
                // TODO: 2023/4/4 发送消息
                SysRobotMsg template = new SysRobotMsg();
                template.setStatus(notice.getStatus());
                template.setSendMsg(notice.getNoticeContent());
                template.setDelFlag(notice.getDelFlag());
                template.setMsgContent(notice.getNoticeContent());
                template.setMsgTitle(notice.getNoticeTitle());
                template.setMsgType(UserConstants.ROBOT_TEXT);

                pushRobotMessageService.sendMessage(collect,template);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }

        }

    }
}
