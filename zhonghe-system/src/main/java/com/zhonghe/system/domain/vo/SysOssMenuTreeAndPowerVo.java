package com.zhonghe.system.domain.vo;

import com.zhonghe.common.core.domain.vo.SysDeptTreeVo;
import com.zhonghe.system.domain.bo.SysOssBucketPowersBo;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @description:
 * @author: cq
 * @date: 2023/4/6 17:10
 */
@Data
@ApiModel("桶对象权限与菜单权限")
public class SysOssMenuTreeAndPowerVo implements Serializable {

    private SysOssBucketVo bucketInfo;

    private List<SysDeptTreeVo> depts;


    private List<SysOssBucketPowersBo> powers;
}

