package com.zhonghe.system.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zhonghe.common.annotation.ExcelDictFormat;
import com.zhonghe.common.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;



/**
 * 存储文件开放表视图对象 sys_oss_bucket_open
 *
 * <AUTHOR>
 * @date 2023-04-03
 */
@Data
@ApiModel("存储文件开放表视图对象")
@ExcelIgnoreUnannotated
public class SysOssBucketOpenVo {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "$column.readConverterExp()")
    @ApiModelProperty("id")
    private Long id;

    /**
     * 所属桶节点
     */
    @ExcelProperty(value = "所属桶节点")
    @ApiModelProperty("所属桶节点")
    private Long bucketId;

    /**
     * 过期时间
     */
    @ExcelProperty(value = "过期时间")
    @ApiModelProperty("过期时间")
    private String expiredTime;


}
