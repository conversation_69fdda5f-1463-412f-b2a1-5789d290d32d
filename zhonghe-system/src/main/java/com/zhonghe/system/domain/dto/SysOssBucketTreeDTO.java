package com.zhonghe.system.domain.dto;

import com.zhonghe.common.core.domain.vo.SysDeptTreeVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: lpg
 * @Date: 2023/04/23/16:16
 * @Description:
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SysOssBucketTreeDTO {

    private Long id;

    private Long parentId;

    private String bucketName;

    private List<SysOssBucketTreeDTO> children = new ArrayList<>();

    public SysOssBucketTreeDTO(Long id, Long parentId, String bucketName) {
        this.id = id;
        this.parentId = parentId;
        this.bucketName = bucketName;
    }
}
