package com.zhonghe.system.domain.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @description:
 * @author: cq
 * @date: 2023/4/7 14:46
 */

@Data
@ApiModel("桶文件上传业务结果对象")
public class SysOssBucketUploadCheckResultBo {

    @ApiModelProperty("是否存在")
    private Boolean finish;

    @ApiModelProperty("文件路径")
    private String url;

    @ApiModelProperty("已上传的分块的列表")
    private List<Integer> chunkList;

}

