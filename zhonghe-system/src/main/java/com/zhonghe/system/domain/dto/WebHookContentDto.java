package com.zhonghe.system.domain.dto;

import com.zhonghe.common.enums.WebHookType;
import com.zhonghe.common.utils.JsonUtils;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;

/**
 * @description:
 * @author: cq
 * @date: 2023/3/29 15:03
 */

@Data
@AllArgsConstructor
public class WebHookContentDto {

    private WebHookType type;

    private String msgtype;

    private String content;


    /**
     * vx格式化蠢的要死
     *
     * @return
     */
    @Override
    public String toString() {
        String key = "text";
        StringBuilder sb = new StringBuilder();
        sb.append("{");
        switch (msgtype) {
            case "text":
                sb.append("\"text\":");
                Map<String, String> text = new HashMap<>();
                text.put("content", this.content);
                sb.append(JsonUtils.toJsonString(text)).append(",");
                break;
            case "markdown":
                key = "markdown";
                sb.append("\"markdown\":");
                Map<String, String> markdown = new HashMap<>();
                markdown.put("content", this.content);
                sb.append(JsonUtils.toJsonString(markdown)).append(",");
                break;
        }
        sb.append("\"msgtype\":\"").append(key).append("\"}");
        return sb.toString();
    }
}

