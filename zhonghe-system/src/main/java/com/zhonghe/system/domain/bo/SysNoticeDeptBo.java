package com.zhonghe.system.domain.bo;

import com.zhonghe.common.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import javax.validation.constraints.*;

import com.zhonghe.common.core.domain.BaseEntity;

/**
 * 1业务对象 sys_notice_dept
 *
 * <AUTHOR>
 * @date 2023-03-22
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("1业务对象")
public class SysNoticeDeptBo extends BaseEntity {

    /**
     * $column.columnComment
     */
    @ApiModelProperty(value = "$column.columnComment", required = true)
    @NotNull(message = "$column.columnComment不能为空", groups = { EditGroup.class })
    private Long noticeId;

    /**
     * $column.columnComment
     */
    @ApiModelProperty(value = "$column.columnComment", required = true)
    @NotNull(message = "$column.columnComment不能为空", groups = { EditGroup.class })
    private Long deptId;


}
