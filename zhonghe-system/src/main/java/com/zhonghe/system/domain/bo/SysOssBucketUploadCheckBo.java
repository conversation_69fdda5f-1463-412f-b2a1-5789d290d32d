package com.zhonghe.system.domain.bo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NonNull;

import javax.validation.constraints.Max;

/**
 * @description:
 * @author: cq
 * @date: 2023/4/7 14:37
 */

@Data
@ApiModel("桶文件上传业务预检对象")
public class SysOssBucketUploadCheckBo {

    @ApiModelProperty("文件hashcode")
    private String hashCode;

    @ApiModelProperty("文件名称")
    private String fileName;


    @ApiModelProperty("文件分块总数")
    private Integer chunkCount;

    @ApiModelProperty("归属桶")
    private Long bucketId;

    @ApiModelProperty("文件大小")
    @Max(value = 1024 * 1024 * 100, message = "文件大小不能超过100M")
    private Long fileSize;

}

