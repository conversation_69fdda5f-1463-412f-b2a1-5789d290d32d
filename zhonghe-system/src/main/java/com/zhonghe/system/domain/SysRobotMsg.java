package com.zhonghe.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zhonghe.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 机器人消息对象 sys_robot_msg
 *
 * <AUTHOR>
 * @date 2023-03-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_robot_msg")
public class SysRobotMsg extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * ID
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 消息标题
     */
    private String msgTitle;
    /**
     * 消息内容
     */
    private String msgType;
    /**
     * 消息内容
     */
    private String msgContent;
    /**
     * 状态
     */
    private String status;
    /**
     * $column.columnComment
     */
    @TableLogic
    private String delFlag;

    private Integer sendResultCode;

    private String sendMsg;

}
