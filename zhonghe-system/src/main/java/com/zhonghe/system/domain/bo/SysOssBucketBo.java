package com.zhonghe.system.domain.bo;

import com.zhonghe.common.core.domain.BaseEntity;
import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 对象存储桶业务对象 sys_oss_bucket
 *
 * <AUTHOR>
 * @date 2023-03-31
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("对象存储桶业务对象")
public class SysOssBucketBo extends BaseEntity {

    /**
     * id
     */
    @ApiModelProperty(value = "id", required = true)
    @NotNull(message = "id不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 系统桶名
     */
    @ApiModelProperty(value = "系统桶名", required = true)
    @NotBlank(message = "系统桶名不能为空", groups = {AddGroup.class, EditGroup.class})
    private String nickName;


    /**
     * 类型1:minio桶 2：minio文件夹 3：minio文件
     */
    @ApiModelProperty(value = "类型1:minio桶 2：minio文件夹 3：minio文件", required = true)
    private Long bucketType;

    /**
     * 原始名称
     */
    @ApiModelProperty(value = "原始名称", required = true)
    private String originalName;

    /**
     * 文件后缀名
     */
    @ApiModelProperty(value = "文件后缀名", required = true)
    private String fileSuffix;

    /**
     * 父节点
     */
    @ApiModelProperty(value = "父节点", required = true)
    private Long parentId;

    /**
     * 所属桶节点
     */
    @ApiModelProperty(value = "所属桶节点", required = true)
    private Long bucketId;

    /**
     * 过期时间
     */
    @ApiModelProperty(value = "过期时间", required = true)
    private Date expiredTime;

    /**
     * 标签
     */
    @ApiModelProperty(value = "标签", required = true)
    private String tags;

    /**
     * e
     * 所有者
     */
    @ApiModelProperty(value = "所有者", required = true)
    private Long ownerId;

    /**
     * 容量类型
     */
    @ApiModelProperty(value = "容量类型 （K,KB,MB,GB,TB）", required = true)
    @NotNull(message = "容量类型", groups = {EditGroup.class})
    private String capacityType;

    /**
     * 最大容量
     */
    @ApiModelProperty("最大容量")
    @NotNull(message = "最大容量", groups = {EditGroup.class})
    private Double maxCapacity;

    /**
     * 状态
     */
    @ApiModelProperty(value = "状态", required = true)
    private String status;


    @ApiModelProperty("桶部门定义权限集合")
    private List<SysOssBucketPowersBo> powers;

    @ApiModelProperty("是否是私有盘0 不是 1 私有")
    private Integer personal;

    @ApiModelProperty("1：全部门全读权限，0：不处理，优先级高于powers配置")
    private Integer all;
}
