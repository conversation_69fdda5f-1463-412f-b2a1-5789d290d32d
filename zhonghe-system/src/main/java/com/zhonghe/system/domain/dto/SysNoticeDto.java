package com.zhonghe.system.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.zhonghe.system.domain.SysNotice;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.annotation.Transient;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @description:
 * @author: cq
 * @date: 2023/3/23 14:37
 */

@Data
public class SysNoticeDto extends SysNotice implements Serializable {


    @Transient
    private static final long serialVersionUID = -4715601657394007519L;


    @ApiModelProperty(value = "请求部门的id")
    @TableField(exist = false)
    private List<Long> deptIds;


    @ApiModelProperty(value = "请求模板的占位符参数")
    @TableField(exist = false)
    private Map<String, String> templateWords;


    @ApiModelProperty(value = "请求部门的名称")
    @TableField(exist = false)
    private List<String> deptNames;
}

