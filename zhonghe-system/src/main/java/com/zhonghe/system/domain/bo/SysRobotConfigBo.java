package com.zhonghe.system.domain.bo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;

import com.zhonghe.common.core.domain.BaseEntity;

/**
 * 机器人配置业务对象 sys_robot_config
 *
 * <AUTHOR>
 * @date 2023-03-29
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("机器人配置业务对象")
public class SysRobotConfigBo extends BaseEntity {

    /**
     * ID
     */
    @ApiModelProperty(value = "ID", required = true)
    @NotNull(message = "ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 机器人类型
     */
    @ApiModelProperty(value = "机器人类型 1钉钉机器人 0微信机器人 3部门机器人", required = true)
    @NotBlank(message = "机器人类型不能为空", groups = { AddGroup.class, EditGroup.class })
    private String robotType;

    /**
     * 地址
     */
    @ApiModelProperty(value = "地址", required = true)
    @NotBlank(message = "地址不能为空", groups = { AddGroup.class, EditGroup.class })
    private String webhook;


    @ApiModelProperty(value = "名称", required = true)
    @NotBlank(message = "名称不能为空", groups = { AddGroup.class, EditGroup.class })
    @Size(min = 0, max = 30, message = "名称大小不能大于30")
    private String name;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注", required = true)
//    @NotBlank(message = "备注不能为空", groups = { AddGroup.class, EditGroup.class })
    private String remark;

    /**
     * $column.columnComment
     */
    @ApiModelProperty(value = "$column.columnComment", required = true)
//    @NotBlank(message = "$column.columnComment不能为空", groups = { AddGroup.class, EditGroup.class })
    private String accessToken;

    /**
     * 密钥
     */
    @ApiModelProperty(value = "密钥", required = true)
//    @NotBlank(message = "密钥不能为空", groups = { AddGroup.class, EditGroup.class })
    @Size(min = 0, max = 200, message = "密钥大小不能大于200")
    private String secret;


    @ApiModelProperty(value = "部门ID", required = true)
    @NotBlank(message = "部门ID", groups = { AddGroup.class, EditGroup.class })
    @TableField(exist = false)
    private Long[] departId;

    @ExcelProperty(value = "机器人状态 1启用 0禁用")
    @ApiModelProperty("机器人状态 1启用 0禁用")
    private String robotState;

}
