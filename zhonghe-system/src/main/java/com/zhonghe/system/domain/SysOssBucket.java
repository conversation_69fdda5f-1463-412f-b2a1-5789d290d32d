package com.zhonghe.system.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zhonghe.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 对象存储桶对象 sys_oss_bucket
 *
 * <AUTHOR>
 * @date 2023-03-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_oss_bucket")
public class SysOssBucket extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * id
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 系统桶名
     */
    private String nickName;

    /**
     * 类型1:minio桶 2：minio文件夹 3：minio文件
     */
    private Integer bucketType;

    /**
     * 原始名称
     */
    private String originalName;
    /**
     * 文件后缀名
     */
    private String fileSuffix;
    /**
     * 父节点
     */
    private Long parentId;
    /**
     * 所属桶节点
     */
    private Long bucketId;
    /**
     * 过期时间
     */
    private Date expiredTime;
    /**
     * 标签
     */
    private String tags;
    /**
     * 所有者
     */
    private Long ownerId;
    /**
     * 状态
     */
    private String status;
    /**
     * 是否删除
     */
    @TableLogic
    private String delFlag;

    /**
     * 最大容量
     */
    private Double maxCapacity;
    /**
     * 是否是私有盘 0 不是 1 私有
     */
    private Integer personal;

    /**
     * 已使用容量
     */
    @TableField(exist = false)
    private String usedCapacity;
    /**
     * 总容量
     */
    @TableField(exist = false)
    private String totalCapacity;
    /**
     * 文件数
     */
    @TableField(exist = false)
    private Long fileCount;

    /**
     * 文件大小
     */
    private Long fileSize;

    /**
     * 文件路径
     */
    @TableField(exist = false)
    private String ownerName;

}
