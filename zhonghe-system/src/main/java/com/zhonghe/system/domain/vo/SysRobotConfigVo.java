package com.zhonghe.system.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.zhonghe.common.annotation.ExcelDictFormat;
import com.zhonghe.common.convert.ExcelDictConvert;
import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;


/**
 * 机器人配置视图对象 sys_robot_config
 *
 * <AUTHOR>
 * @date 2023-03-29
 */
@Data
@ApiModel("机器人配置视图对象")
@ExcelIgnoreUnannotated
public class SysRobotConfigVo {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @ExcelProperty(value = "ID")
    @ApiModelProperty("ID")
    private Long id;

    /**
     * 机器人类型
     */
    @ExcelProperty(value = "机器人类型")
    @ApiModelProperty(value = "机器人类型 1钉钉机器人 0微信机器人 3部门机器人", required = true)
    private String robotType;

    /**
     * 地址
     */
    @ExcelProperty(value = "地址")
    @ApiModelProperty("地址")
    private String webhook;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    @ApiModelProperty("备注")
    private String remark;

    /**
     * $column.columnComment
     */
    @ExcelProperty(value = "${comment}", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "$column.readConverterExp()")
    @ApiModelProperty("$column.columnComment")
    private String accessToken;

    /**
     * 密匙
     */
    @ExcelProperty(value = "密匙")
    @ApiModelProperty("密匙")
    private String secret;

    @ExcelProperty(value = "名称")
    @ApiModelProperty("名称")
    private String name;

    @ExcelProperty(value = "机器人状态 1启用 0禁用")
    @ApiModelProperty("机器人状态 1启用 0禁用")
    private String robotState;

    @ApiModelProperty(value = "部门ID", required = true)
    @NotBlank(message = "部门ID", groups = { AddGroup.class, EditGroup.class })
    @TableField(exist = false)
    private Long[] departId;
}
