package com.zhonghe.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zhonghe.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 桶文件上传对象 sys_oss_bucket_upload
 *
 * <AUTHOR>
 * @date 2023-04-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_oss_bucket_upload")
public class SysOssBucketUpload extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * $column.columnComment
     */
    @TableId(value = "bucket_upload_id")
    private Long bucketUploadId;
    /**
     * $column.columnComment
     */
    private Long bucketId;
    /**
     * $column.columnComment
     */
    private String hashcode;
    /**
     * $column.columnComment
     */
    private Integer totalCount;
    /**
     * $column.columnComment
     */
    private Boolean finish;
    /**
     * $column.columnComment
     */
    private String uploadId;

    /**
     * minio的key
     */
    private String minioKey;

    @TableLogic
    private String delFlag;

    private String minioBucket;

    private String fileName;
}
