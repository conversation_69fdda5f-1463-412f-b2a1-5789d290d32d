package com.zhonghe.system.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.zhonghe.system.domain.SysUserNotice;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @description:
 * @author: cq
 * @date: 2023/3/24 10:03
 */

@Data
public class SysUserNoticeDto extends SysUserNotice implements Serializable {


    @ApiModelProperty("是否已读未读")
    @TableField(exist = false)
    private String stuts;

    @ApiModelProperty("机构id")
    @TableField(exist = false)
    private Long deptId;
}

