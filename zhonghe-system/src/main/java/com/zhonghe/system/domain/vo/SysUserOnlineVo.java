package com.zhonghe.system.domain.vo;

import com.zhonghe.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.Date;

/**
 * 在线用户视图对象
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysUserOnlineVo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 会话编号 */
    private String tokenId;

    /** 用户名称 */
    private String userName;

    /** 用户昵称 */
    private String nickName;

    /** 部门名称 */
    private String deptName;

    /** 登录IP地址 */
    private String ipaddr;

    /** 登录地点 */
    private String loginLocation;

    /** 浏览器类型 */
    private String browser;

    /** 操作系统 */
    private String os;

    /** 登录时间 */
    private Date loginTime;
} 