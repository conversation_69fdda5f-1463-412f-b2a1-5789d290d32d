package com.zhonghe.system.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zhonghe.common.annotation.ExcelDictFormat;
import com.zhonghe.common.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.Set;


/**
 * 对象存储桶视图对象 sys_oss_bucket
 *
 * <AUTHOR>
 * @date 2023-03-31
 */
@Data
@ApiModel("对象存储桶视图对象")
@ExcelIgnoreUnannotated
public class SysOssBucketVo {

    private static final long serialVersionUID = 1L;

    /**
     * Id
     */
    @ExcelProperty(value = "${comment}", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "$column.readConverterExp()")
    @ApiModelProperty("$column.columnComment")
    private Long id;

    /**
     * 系统桶名
     */
    @ExcelProperty(value = "系统桶名")
    @ApiModelProperty("系统桶名")
    private String nickName;


    /**
     * 所属桶节点
     */
    private Long bucketId;


    /**
     * 类型1:minio桶 2：minio文件夹 3：minio文件
     */
    @ExcelProperty(value = "类型1:minio桶 2：minio文件夹 3：minio文件")
    @ApiModelProperty("类型1:minio桶 2：minio文件夹 3：minio文件")
    private Integer bucketType;


    /**
     * 文件后缀名
     */
    @ExcelProperty(value = "文件后缀名")
    @ApiModelProperty("文件后缀名")
    private String fileSuffix;



    /**
     * 过期时间
     */
    @ExcelProperty(value = "过期时间")
    @ApiModelProperty("过期时间")
    private Date expiredTime;

    /**
     * 标签
     */
    @ExcelProperty(value = "标签")
    @ApiModelProperty("标签")
    private String tags;

    /**
     * 所有者id
     */
    @ExcelProperty(value = "所有者id", converter = ExcelDictConvert.class)
    @ApiModelProperty("所有者id")
    private Long ownerId;

    /**
     * 状态
     */
    @ExcelProperty(value = "状态", converter = ExcelDictConvert.class)
    @ApiModelProperty("状态")
    private String status;

    /**
     * 最大容量
     */
    @ApiModelProperty("最大容量")
    private Double maxCapacity;


    /**
     * 文件地址
     * */
    @ApiModelProperty("文件地址")
    private String url;


    /**
     * 是否是私有盘 0 不是 1 私有
     */
    @ApiModelProperty("是否是私有盘0 不是 1 私有")
    private Integer personal;
    /**
     * 该用户的权限
     */
    private Set<String> powers;

    /**
     * 是否全部部门
     */
    @ApiModelProperty("是否全部部门0 不是 1 是")
    private Integer all;

    /**
     * 已使用容量
     */
    @ApiModelProperty("已使用容量")
    private Object usedCapacity;
    /**
     * 总容量
     */
    @ApiModelProperty("总容量")
    private Object totalCapacity;
    /**
     * 文件数
     */
    @ApiModelProperty("文件数")
    private Long fileCount;

    /**
     * 文件大小
     */
    private Object fileSize;

    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    private Date updateTime;


}
