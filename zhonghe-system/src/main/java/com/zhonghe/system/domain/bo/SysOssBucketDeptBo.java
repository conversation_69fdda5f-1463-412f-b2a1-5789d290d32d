package com.zhonghe.system.domain.bo;

import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;

import com.zhonghe.common.core.domain.BaseEntity;

/**
 * 部门存储桶业务对象 sys_oss_bucket_dept
 *
 * <AUTHOR>
 * @date 2023-03-31
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("部门存储桶业务对象")
public class SysOssBucketDeptBo extends BaseEntity {

    /**
     * ID
     */
    @ApiModelProperty(value = "ID", required = true)
    @NotNull(message = "ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long id;

    /**
     * 部门id
     */
    @ApiModelProperty(value = "部门id", required = true)
    @NotNull(message = "部门id不能为空", groups = { EditGroup.class })
    private Long deptId;

    /**
     * 所属桶节点
     */
    @ApiModelProperty(value = "所属桶节点", required = true)
    @NotNull(message = "所属桶节点不能为空", groups = { EditGroup.class })
    private Long bucketId;


}
