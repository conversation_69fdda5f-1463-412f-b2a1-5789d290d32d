package com.zhonghe.system.domain.bo;

import com.zhonghe.common.core.domain.BaseEntity;
import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;


/**
 * 机器人配置关联部门业务对象 sys_robot_relevance_depart
 *
 * <AUTHOR>
 * @date 2023-04-04
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("机器人配置关联部门业务对象")
public class SysRobotRelevanceDepartBo extends BaseEntity {

    /**
     * 主键
     */
    @ApiModelProperty(value = "主键", required = true)
    @NotNull(message = "主键不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 机器人id
     */
    @ApiModelProperty(value = "机器人id", required = true)
    @NotNull(message = "机器人id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long robotId;

    /**
     * 部门id
     */
    @ApiModelProperty(value = "部门id", required = true)
    @NotNull(message = "部门id不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long departId;

    /**
     * $column.columnComment
     */
    @ApiModelProperty(value = "$column.columnComment", required = true)
    @NotBlank(message = "$column.columnComment不能为空", groups = { AddGroup.class, EditGroup.class })
    private String extend;


}
