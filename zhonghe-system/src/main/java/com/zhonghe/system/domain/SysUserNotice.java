package com.zhonghe.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zhonghe.common.xss.Xss;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.Date;


/**
 * 用户的通知公告表 sys_user_notice
 *
 * <AUTHOR>
 */
@Data
@TableName("sys_user_notice")
public class SysUserNotice {

    /**
     * 用户公告的id
     */
    @TableId(value = "user_notice_id")
    private Long userNoticeId;

    /**
     * 公告ID
     */
    @ApiModelProperty(value = "公告ID")
    private Long noticeId;


    /**
     * 公告标题
     */
    @Xss(message = "公告标题不能包含脚本字符")
    @ApiModelProperty(value = "公告标题")
    @NotBlank(message = "公告标题不能为空")
    @Size(min = 0, max = 50, message = "公告标题不能超过50个字符")
    private String noticeTitle;

    /**
     * 公告类型（1通知 2公告）
     */
    @ApiModelProperty(value = "公告类型（1通知 2公告）")
    private String noticeType;

    /**
     * 公告内容
     */
    @ApiModelProperty(value = "公告内容")
    private String noticeContent;

    /**
     * 公告状态（0正常 1关闭）
     */
    @ApiModelProperty(value = "公告状态（0正常 1关闭）")
    private String status;

    @ApiModelProperty(value = "发送时间")
    private Date sendTime;

    @ApiModelProperty(value = "消息等级")
    private Integer level;

    @ApiModelProperty(value = "创建者的nickname")
    private String nickName;

}
