package com.zhonghe.system.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.zhonghe.common.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


/**
 * 存储桶描述视图对象 sys_oss_bucket_sec
 *
 * <AUTHOR>
 * @date 2023-04-03
 */
@Data
@ApiModel("存储桶描述视图对象")
@ExcelIgnoreUnannotated
public class SysOssBucketSecVo {

    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ExcelProperty(value = "id", converter = ExcelDictConvert.class)
    @ApiModelProperty("id")
    private Long id;

    /**
     * 所属桶ID
     */
    @ExcelProperty(value = "所属桶ID")
    @ApiModelProperty("所属桶ID")
    private Long bucketId;

    /**
     * 名称
     */
    @TableField(exist = false)
    private String nickName;

    /**
     * 过期时间
     */
    @TableField(exist = false)
    private Date expiredTime;

    /**
     * minio桶真实名称
     */
    @ExcelProperty(value = "minio桶真实名称")
    @ApiModelProperty("minio桶真实名称")
    private String bucketName;

    /**
     * 类型1:minio桶 2：minio文件夹 3：minio文件
     */
    @ExcelProperty(value = "类型1:minio桶 2：minio文件夹 3：minio文件")
    @ApiModelProperty("类型1:minio桶 2：minio文件夹 3：minio文件")
    private Integer bucketType;

    /**
     * minio路径
     */
    @ExcelProperty(value = "minio路径")
    @ApiModelProperty("minio路径")
    private String minioPath;

    /**
     * 当前容量
     */
    @ApiModelProperty("当前容量")
    private Double currentCapacity;

    /**
     * 已使用容量
     */
    private String usedCapacity;
    /**
     * 总容量
     */
    private String totalCapacity;


    /**
     * 文件数量
     */
    private Integer fileSize;

    private Integer personal;

    /**
     * 创建者
     */
    private String ownerName;

}
