package com.zhonghe.system.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zhonghe.common.annotation.ExcelDictFormat;
import com.zhonghe.common.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 机器人配置关联部门视图对象 sys_robot_relevance_depart
 *
 * <AUTHOR>
 * @date 2023-04-04
 */
@Data
@ApiModel("机器人配置关联部门视图对象")
@ExcelIgnoreUnannotated
public class SysRobotRelevanceDepartVo {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @ExcelProperty(value = "主键")
    @ApiModelProperty("主键")
    private Long id;

    /**
     * 机器人id
     */
    @ExcelProperty(value = "机器人id")
    @ApiModelProperty("机器人id")
    private Long robotId;

    /**
     * 部门id
     */
    @ExcelProperty(value = "部门id")
    @ApiModelProperty("部门id")
    private Long departId;

    /**
     * $column.columnComment
     */
    @ExcelProperty(value = "${comment}", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "$column.readConverterExp()")
    @ApiModelProperty("$column.columnComment")
    private String extend;


}
