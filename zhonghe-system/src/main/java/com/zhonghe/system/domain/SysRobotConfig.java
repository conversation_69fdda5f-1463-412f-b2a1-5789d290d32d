package com.zhonghe.system.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zhonghe.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 机器人配置对象 sys_robot_config
 *
 * <AUTHOR>
 * @date 2023-03-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_robot_config")
public class SysRobotConfig extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 机器人类型
     */
    private String robotType;
    /**
     * 地址
     */
    private String webhook;
    /**
     * 备注
     */
    private String remark;
    /**
     * $column.columnComment
     */
    private String accessToken;
    /**
     * 密匙
     */
    private String secret;

    /**
     * 机器人名称
     */
    private String name;

    /**
     * 机器人名称(1:启用 0:停用)
     */
    private String robotState;

    @TableField(exist = false)
    private Long[] departId;

}
