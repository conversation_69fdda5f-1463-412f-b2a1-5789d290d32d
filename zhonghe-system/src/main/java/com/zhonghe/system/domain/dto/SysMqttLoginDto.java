package com.zhonghe.system.domain.dto;

import lombok.Data;

import java.util.Collections;
import java.util.Set;

/**
 * @description:
 * @author: cq
 * @date: 2023/3/27 15:58
 */

@Data
public class SysMqttLoginDto {

    /**
     * 登录账号
     */
    private String username;
    /**
     * 登录密码
     */
    private String pwd;

    /**
     * 登录ip
     */
    private String remote;

    /**
     * 登录端口
     */
    private Integer port;

    /**
     * 登录clientId,
     * 命名规范：user_type-user_name-random字符
     */
    private String clientId;

    private Set<String> topics = Collections.emptySet();
}

