package com.zhonghe.system.domain.bo;

import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;

import com.zhonghe.common.core.domain.BaseEntity;

/**
 * 存储桶描述业务对象 sys_oss_bucket_sec
 *
 * <AUTHOR>
 * @date 2023-04-03
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("存储桶描述业务对象")
public class SysOssBucketSecBo extends BaseEntity {

    /**
     * $column.columnComment
     */
    @ApiModelProperty(value = "$column.columnComment", required = true)
    @NotNull(message = "$column.columnComment不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 所属桶ID
     */
    @ApiModelProperty(value = "所属桶ID", required = true)
    @NotNull(message = "所属桶ID不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long bucketId;

    /**
     * minio桶真实名称
     */
    @ApiModelProperty(value = "minio桶真实名称", required = true)
    @NotBlank(message = "minio桶真实名称不能为空", groups = { AddGroup.class, EditGroup.class })
    private String bucketName;

    /**
     * 类型1:minio桶 2：minio文件夹 3：minio文件
     */
    @ApiModelProperty(value = "类型1:minio桶 2：minio文件夹 3：minio文件", required = true)
    @NotNull(message = "类型1:minio桶 2：minio文件夹 3：minio文件不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long bucketType;

    /**
     * minio路径
     */
    @ApiModelProperty(value = "minio路径", required = true)
    @NotBlank(message = "minio路径不能为空", groups = { AddGroup.class, EditGroup.class })
    private String minioPath;


}
