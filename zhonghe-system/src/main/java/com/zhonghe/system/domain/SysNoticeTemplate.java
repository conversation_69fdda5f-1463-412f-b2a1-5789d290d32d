package com.zhonghe.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;
import lombok.EqualsAndHashCode;

import com.zhonghe.common.core.domain.BaseEntity;

/**
 * 1对象 sys_notice_template
 *
 * <AUTHOR>
 * @date 2023-03-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "sys_notice_template",autoResultMap = true)
public class SysNoticeTemplate extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 模版ID
     */
    @TableId(value = "template_id")
    private Long templateId;

    /**
     * 模版标题
     */
    private String templateTitle;

    /**
     * 模板内容
     */
    private String templateContent;
    /**
     * 模板关键词
     */
    private String templateKeywords;

    /**
     * 模板参数
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private Object paramsContent;

    /**
     * 状态 0-启用 1-关闭
     */
    private String status;

}
