package com.zhonghe.system.domain.bo;

import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;

import com.zhonghe.common.core.domain.BaseEntity;

/**
 * 存储文件开放表业务对象 sys_oss_bucket_open
 *
 * <AUTHOR>
 * @date 2023-04-03
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("存储文件开放表业务对象")
public class SysOssBucketOpenBo extends BaseEntity {

    /**
     * ID
     */
    @ApiModelProperty(value = "ID", required = true)
    @NotNull(message = "ID不能为空", groups = { EditGroup.class })
    private Long id;

    /**
     * 所属桶节点
     */
    @ApiModelProperty(value = "所属桶节点", required = true)
    @NotNull(message = "所属桶节点不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long bucketId;

    /**
     * 过期时间
     */
    @ApiModelProperty(value = "过期时间", required = true)
    @NotBlank(message = "过期时间不能为空", groups = { AddGroup.class, EditGroup.class })
    private String expiredTime;


}
