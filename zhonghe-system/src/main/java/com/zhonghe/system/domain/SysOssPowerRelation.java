package com.zhonghe.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import com.zhonghe.common.core.domain.BaseEntity;

/**
 * 存储桶部门关系对象 sys_oss_power_relation
 *
 * <AUTHOR>
 * @date 2023-03-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_oss_power_relation")
public class SysOssPowerRelation extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 编号
     */
    @TableId("id")
    private Long id;
    /**
     * $column.columnComment
     */
    private Long bucketDeptId;
    /**
     * $column.columnComment
     */
    private Long powerDefineId;

}
