package com.zhonghe.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zhonghe.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 存储文件开放表对象 sys_oss_bucket_open
 *
 * <AUTHOR>
 * @date 2023-04-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_oss_bucket_open")
public class SysOssBucketOpen extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * ID
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 所属桶节点
     */
    private Long bucketId;
    /**
     * 过期时间
     */
    private String expiredTime;

    /**
     * 删除标志
     */
    @TableLogic
    private Long delFlag;

}
