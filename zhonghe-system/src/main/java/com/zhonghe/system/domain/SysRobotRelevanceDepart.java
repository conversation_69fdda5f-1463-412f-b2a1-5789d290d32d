package com.zhonghe.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import com.zhonghe.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 机器人配置关联部门对象 sys_robot_relevance_depart
 *
 * <AUTHOR>
 * @date 2023-04-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_robot_relevance_depart")
public class SysRobotRelevanceDepart extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 机器人id
     */
    private Long robotId;
    /**
     * 部门id
     */
    private Long departId;
    /**
     * $column.columnComment
     */
    private String extend;

}
