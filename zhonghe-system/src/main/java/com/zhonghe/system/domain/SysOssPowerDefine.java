package com.zhonghe.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import com.zhonghe.common.core.domain.BaseEntity;

/**
 * 存储桶权限对象 sys_oss_power_define
 *
 * <AUTHOR>
 * @date 2023-03-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_oss_power_define")
public class SysOssPowerDefine extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * $column.columnComment
     */
    private Long id;
    /**
     * 权限类别, read、write、share、delete
     */
    @TableId(value = "power")
    private String power;
    /**
     * $column.columnComment
     */
    private String status;

}
