package com.zhonghe.system.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zhonghe.common.annotation.ExcelDictFormat;
import com.zhonghe.common.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 部门存储桶视图对象 sys_oss_bucket_dept
 *
 * <AUTHOR>
 * @date 2023-03-31
 */
@Data
@ApiModel("部门存储桶视图对象")
@ExcelIgnoreUnannotated
public class SysOssBucketDeptVo {

    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    @ExcelProperty(value = "${comment}", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "$column.readConverterExp()")
    @ApiModelProperty("$column.columnComment")
    private Long id;

    /**
     * 部门id
     */
    @ExcelProperty(value = "部门id")
    @ApiModelProperty("部门id")
    private Long deptId;

    /**
     * 所属桶节点
     */
    @ExcelProperty(value = "所属桶节点")
    @ApiModelProperty("所属桶节点")
    private Long bucketId;


}
