package com.zhonghe.system.domain.bo;

import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;

import com.zhonghe.common.core.domain.BaseEntity;
import lombok.NoArgsConstructor;

/**
 * 1业务对象 sys_notice_template
 *
 * <AUTHOR>
 * @date 2023-03-22
 */

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@ApiModel("1业务对象")
public class SysNoticeTemplateBo extends BaseEntity {

    /**
     * 模版ID
     */
    @ApiModelProperty(value = "模版ID", required = true)
    @NotNull(message = "模版ID", groups = { EditGroup.class })
    private Long templateId;


    /**
     * 模版标题
     */
    @ApiModelProperty(value = "模版标题", required = true)
    @NotNull(message = "模版标题", groups = { EditGroup.class })
    private String templateTitle;

    /**
     * 模板内容
     */
    @ApiModelProperty(value = "模板内容", required = true)
    @NotBlank(message = "模板内容不能为空", groups = { AddGroup.class, EditGroup.class })
    private String templateContent;

    /**
     * 模板关键词
     */
    @ApiModelProperty(value = "模板关键词", required = true)
    private String templateKeywords;

    /**
     * 模板参数
     */
    @ApiModelProperty(value = "模板参数")
    private Object paramsContent;


    /**
     * 状态 0-启用 1-关闭
     */
    @ApiModelProperty(value = "模板状态", required = true)
    @NotBlank(message = "模板状态", groups = { AddGroup.class, EditGroup.class })
    private String status;
}
