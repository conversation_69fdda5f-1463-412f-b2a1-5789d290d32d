package com.zhonghe.system.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zhonghe.common.convert.ExcelDictConvert;
import com.zhonghe.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 1视图对象 sys_notice_template
 *
 * <AUTHOR>
 * @date 2023-03-22
 */
@Data
@ApiModel("消息模版视图对象")
@ExcelIgnoreUnannotated
public class SysNoticeTemplateVo extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 模版ID
     */
    @ExcelProperty(value = "模版ID", converter = ExcelDictConvert.class)
    @ApiModelProperty("模版ID")
    private Long templateId;

    /**
     * 模板内容
     */
    @ExcelProperty(value = "模板内容")
    @ApiModelProperty("模板内容")
    private String templateContent;

    /**
     * 模板标题
     */
    @ExcelProperty(value = "模板标题")
    @ApiModelProperty("模板标题")
    private String templateTitle;
    /**
     * 模板类型关键词
     */
    @ExcelProperty(value = "模板类型关键词")
    @ApiModelProperty("模板类型关键词")
    private String templateKeywords;

    /**
     * 模板参数
     */
    @ApiModelProperty(value = "模板参数")
    private Object paramsContent;

    /**
     * 状态 0-启用 1-关闭
     */
    @ExcelProperty(value = "模板状态")
    @ApiModelProperty("模板状态")
    private String status;
}
