package com.zhonghe.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zhonghe.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 存储桶描述对象 sys_oss_bucket_sec
 *
 * <AUTHOR>
 * @date 2023-04-03
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_oss_bucket_sec")
public class SysOssBucketSec extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * id
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 所属桶ID
     */
    private Long bucketId;
    /**
     * minio桶真实名称
     */
    private String bucketName;

    /**
     * 类型1:minio桶 2：minio文件夹 3：minio文件
     */
    private Integer bucketType;
    /**
     * minio路径
     */
    private String minioPath;

    /**
     * 当前容量
     */
    private Double currentCapacity;
}
