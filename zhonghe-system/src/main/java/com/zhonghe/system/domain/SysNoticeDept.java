package com.zhonghe.system.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * 部门消息对象 sys_notice_dept
 *
 * <AUTHOR>
 * @date 2023-03-22
 */
@Data
@TableName("sys_notice_dept")
@ApiModel("消息和部门关联")
public class SysNoticeDept {

    /**
     * 消息ID
     */
    @TableId(value = "notice_id")
    private Long noticeId;
    /**
     * 部门ID
     */
    //@TableId(value = "dept_id")
    private Long deptId;

}
