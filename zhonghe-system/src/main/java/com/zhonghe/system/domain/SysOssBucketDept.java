package com.zhonghe.system.domain;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import com.zhonghe.common.core.domain.BaseEntity;

/**
 * 部门存储桶对象 sys_oss_bucket_dept
 *
 * <AUTHOR>
 * @date 2023-03-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_oss_bucket_dept")
public class SysOssBucketDept extends BaseEntity {

    private static final long serialVersionUID=1L;

    /**
     * $column.columnComment
     */
    @TableId(value = "id")
    private Long id;
    /**
     * 部门id
     */
    private Long deptId;
    /**
     * 所属桶节点
     */
    private Long bucketId;

}
