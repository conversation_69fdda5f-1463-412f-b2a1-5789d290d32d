package com.zhonghe.system.domain.bo;

import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;

import com.zhonghe.common.core.domain.BaseEntity;

/**
 * 存储桶权限业务对象 sys_oss_power_define
 *
 * <AUTHOR>
 * @date 2023-03-31
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("存储桶权限业务对象")
public class SysOssPowerDefineBo extends BaseEntity {

    /**
     * $column.columnComment
     */
    @ApiModelProperty(value = "$column.columnComment", required = true)
    @NotNull(message = "$column.columnComment不能为空", groups = { AddGroup.class, EditGroup.class })
    private Long id;

    /**
     * 权限类别, read、write、share、delete
     */
    @ApiModelProperty(value = "权限类别, read、write、share、delete", required = true)
    @NotBlank(message = "权限类别, read、write、share、delete不能为空", groups = { EditGroup.class })
    private String power;

    /**
     * $column.columnComment
     */
    @ApiModelProperty(value = "$column.columnComment", required = true)
    @NotBlank(message = "$column.columnComment不能为空", groups = { AddGroup.class, EditGroup.class })
    private String status;


}
