package com.zhonghe.system.domain;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: lpg
 * @Date: 2023/03/09/11:03
 * @Description:
 */
@Data
@TableName("sys_topic_config")
@ApiModel("主题配置")
public class SysTopicConfig {
    /**
     * 主题配置序号
     */
    @TableId(value = "sys_topic_config_id")
    @ApiModelProperty(value = "主题配置ID")
    private Long sysTopicConfigId;

    /**
     * 系统名称
     */
    @ApiModelProperty(value = "系统名称")
    private String sysName;
    /**
     * 版本
     */
    @ApiModelProperty(value = "版本")
    private String version;

    /**
     * 是否开启校验（0-开启 1-关闭）
     */
    @ApiModelProperty(value = "是否开启校验（0-开启 1-关闭）")
    private Integer isVerify;

    /**
     * 系统标题图标
     */
    @ApiModelProperty(value = "系统标题图标")
    private String sysTitleIcon;

    /**
     * 站点图标
     */
    @ApiModelProperty(value = "站点图标")
    private String siteIcon;

    /**
     * 系统菜单图标
     */
    @ApiModelProperty(value = "系统菜单图标")
    private String sysMenubarIcon;

    /**
     * 页面背景
     */
    @ApiModelProperty(value = "页面背景")
    private String pageBackground;

    /**
     * 登陆框左侧图
     */
    @ApiModelProperty(value = "登陆框左侧图")
    private String leftLoginPic;
}
