//import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
//import com.zhonghe.ZhongHeApplication;
//import com.zhonghe.system.domain.SysTopicConfig;
//import com.zhonghe.system.service.ISysTopicConfigService;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import javax.annotation.Resource;
//
///**package com.zhonghe.system.test;
//
//import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
//import com.zhonghe.ZhongHeApplication;
//import com.zhonghe.system.domain.SysTopicConfig;
//import com.zhonghe.system.service.ISysTopicConfigService;
//import com.zhonghe.system.service.impl.SysTopicConfigServiceImpl;
//import org.junit.Test;
//import org.junit.runner.RunWith;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.SpringBootConfiguration;
//import or
// g.springframework.boot.test.context.SpringBootTest;
//import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
//import org.springframework.test.context.junit4.SpringRunner;
//
//import javax.annotation.Resource;
//import java.util.List;
//
///**
// * @Author: lpg
// * @Date: 2023/03/09/14:06
// * @Description:
// */
//@RunWith(SpringRunner.class)
//@SpringBootTest(classes = ZhongHeApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
//public class SysTopicConfigHandle {
//
//
//    @Resource
//    private ISysTopicConfigService sysTopicConfigService;
//
//    @Test
//    public void list(){
//        SysTopicConfig one = sysTopicConfigService.getOne(new QueryWrapper<SysTopicConfig>().lambda());
//        System.out.println(one);
//    }
//
//    @Test
//    public void add(){
//        SysTopicConfig sysTopicConfig = new SysTopicConfig();
//        sysTopicConfig.setSysName("中合开发团队");
//        sysTopicConfig.setIsVerify(0);
//        System.out.println(sysTopicConfigService.save(sysTopicConfig));
//    }
//}

