package ${packageName}.service;

import ${packageName}.domain.${ClassName};
import ${packageName}.domain.vo.${ClassName}Vo;
import ${packageName}.domain.bo.${ClassName}Bo;
#if($table.crud || $table.sub)
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.core.domain.PageQuery;
#end

import java.util.Collection;
import java.util.List;

/**
 * ${functionName}Service接口
 *
 * <AUTHOR>
 * @date ${datetime}
 */
public interface I${ClassName}Service {

    /**
     * 查询${functionName}
     */
    ${ClassName}Vo queryById(${pkColumn.javaType} ${pkColumn.javaField});

#if($table.crud || $table.sub)
    /**
     * 查询${functionName}列表
     */
    TableDataInfo<${ClassName}Vo> queryPageList(${ClassName}Bo bo, PageQuery pageQuery);
#end

    /**
     * 查询${functionName}列表
     */
    List<${ClassName}Vo> queryList(${ClassName}Bo bo);

    /**
     * 修改${functionName}
     */
    Boolean insertByBo(${ClassName}Bo bo);

    /**
     * 修改${functionName}
     */
    Boolean updateByBo(${ClassName}Bo bo);

    /**
     * 校验并批量删除${functionName}信息
     */
    Boolean deleteWithValidByIds(Collection<${pkColumn.javaType}> ids, Boolean isValid);
}
