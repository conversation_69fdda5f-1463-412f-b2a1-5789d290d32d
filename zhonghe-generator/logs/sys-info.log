2023-03-22 15:18:04 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.zhonghe.generator.test.TestDemo], using SpringBootContextLoader
2023-03-22 15:18:04 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.zhonghe.generator.test.TestDemo]: no resource found for suffixes {-context.xml, Context.groovy}.
2023-03-22 15:18:04 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2023-03-22 15:18:04 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@5f031ebd, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@4ee37ca3, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@45c8d09f, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@53812a9b, org.springframework.test.context.support.DependencyInjectionTestExecutionListener@14b030a0, org.springframework.test.context.support.DirtiesContextTestExecutionListener@41e350f1, org.springframework.test.context.transaction.TransactionalTestExecutionListener@422c3c7a, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@18230356, org.springframework.test.context.event.EventPublishingTestExecutionListener@d8305c2, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@56bca85b]
2023-03-22 15:18:04 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2023-03-22 15:18:04 [main] INFO  com.zhonghe.generator.test.TestDemo - Starting TestDemo using Java 11.0.18 on lpgMacBook-Pro.lan with PID 5925 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator)
2023-03-22 15:18:04 [main] INFO  com.zhonghe.generator.test.TestDemo - The following 1 profile is active: "dev"
2023-03-22 15:18:05 [main] INFO  c.r.framework.config.JacksonConfig - 初始化 jackson 配置
2023-03-22 15:18:06 [main] INFO  c.ruoyi.framework.config.RedisConfig - 初始化 redis 配置
2023-03-22 15:18:06 [main] INFO  org.redisson.Version - Redisson 3.17.4
2023-03-22 15:18:06 [redisson-netty-2-6] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2023-03-22 15:18:06 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2023-03-22 15:18:07 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2023-03-22 15:18:07 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-03-22 15:18:07 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2023-03-22 15:18:07 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-03-22 15:18:07 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2023-03-22 15:18:08 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2023-03-22 15:18:08 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2023-03-22 15:18:08 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2023-03-22 15:18:11 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2023-03-22 15:18:13 [main] INFO  com.zhonghe.generator.test.TestDemo - Started TestDemo in 9.114 seconds (JVM running for 9.72)
2023-03-22 15:18:13 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qiniu
2023-03-22 15:18:13 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => image
2023-03-22 15:18:13 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => minio
2023-03-22 15:18:13 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qcloud
2023-03-22 15:18:13 [main] INFO  factory.com.zhonghe.oss.OssFactory - 初始化OSS工厂
2023-03-22 15:18:13 [main] INFO  c.r.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2023-03-22 15:18:13 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载参数缓存数据成功
2023-03-22 15:18:13 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载字典缓存数据成功
2023-03-22 15:18:15 [SpringApplicationShutdownHook] INFO  c.r.f.manager.ShutdownManager - ====关闭后台任务任务线程池====
2023-03-22 15:18:15 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2023-03-22 15:18:15 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2023-03-22 15:18:15 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-03-22 15:18:15 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2023-03-22 15:27:53 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.zhonghe.generator.test.TestDemo], using SpringBootContextLoader
2023-03-22 15:27:53 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.zhonghe.generator.test.TestDemo]: no resource found for suffixes {-context.xml, Context.groovy}.
2023-03-22 15:27:53 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2023-03-22 15:27:53 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@ed3068a, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@7c2b6087, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@3fffff43, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@a8e6492, org.springframework.test.context.support.DependencyInjectionTestExecutionListener@1c7fd41f, org.springframework.test.context.support.DirtiesContextTestExecutionListener@3b77a04f, org.springframework.test.context.transaction.TransactionalTestExecutionListener@7b324585, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@2e11485, org.springframework.test.context.event.EventPublishingTestExecutionListener@60dce7ea, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@662f5666]
2023-03-22 15:27:53 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2023-03-22 15:27:53 [main] INFO  com.zhonghe.generator.test.TestDemo - Starting TestDemo using Java 11.0.18 on lpgMacBook-Pro.lan with PID 6013 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator)
2023-03-22 15:27:53 [main] INFO  com.zhonghe.generator.test.TestDemo - The following 1 profile is active: "dev"
2023-03-22 15:27:55 [main] INFO  c.r.framework.config.JacksonConfig - 初始化 jackson 配置
2023-03-22 15:27:55 [main] INFO  c.ruoyi.framework.config.RedisConfig - 初始化 redis 配置
2023-03-22 15:27:55 [main] INFO  org.redisson.Version - Redisson 3.17.4
2023-03-22 15:27:55 [redisson-netty-2-3] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2023-03-22 15:27:55 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2023-03-22 15:27:56 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2023-03-22 15:27:56 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-03-22 15:27:56 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2023-03-22 15:27:56 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-03-22 15:27:56 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2023-03-22 15:27:57 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2023-03-22 15:27:57 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2023-03-22 15:27:57 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2023-03-22 15:28:00 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2023-03-22 15:28:02 [main] INFO  com.zhonghe.generator.test.TestDemo - Started TestDemo in 8.885 seconds (JVM running for 9.405)
2023-03-22 15:28:02 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qiniu
2023-03-22 15:28:02 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => image
2023-03-22 15:28:02 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => minio
2023-03-22 15:28:02 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qcloud
2023-03-22 15:28:02 [main] INFO  factory.com.zhonghe.oss.OssFactory - 初始化OSS工厂
2023-03-22 15:28:02 [main] INFO  c.r.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2023-03-22 15:28:02 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载参数缓存数据成功
2023-03-22 15:28:02 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载字典缓存数据成功
2023-03-22 15:28:04 [SpringApplicationShutdownHook] INFO  c.r.f.manager.ShutdownManager - ====关闭后台任务任务线程池====
2023-03-22 15:28:04 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2023-03-22 15:28:04 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2023-03-22 15:28:04 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-03-22 15:28:04 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2023-03-22 15:28:56 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.zhonghe.generator.test.TestDemo], using SpringBootContextLoader
2023-03-22 15:28:56 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.zhonghe.generator.test.TestDemo]: no resource found for suffixes {-context.xml, Context.groovy}.
2023-03-22 15:28:56 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2023-03-22 15:28:56 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@609640d5, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@79da1ec0, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@19fb8826, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@192d74fb, org.springframework.test.context.support.DependencyInjectionTestExecutionListener@4bef0fe3, org.springframework.test.context.support.DirtiesContextTestExecutionListener@62ea3440, org.springframework.test.context.transaction.TransactionalTestExecutionListener@27953a83, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@556d0826, org.springframework.test.context.event.EventPublishingTestExecutionListener@66ce957f, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@55b5f5d2]
2023-03-22 15:28:57 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2023-03-22 15:28:57 [main] INFO  com.zhonghe.generator.test.TestDemo - Starting TestDemo using Java 11.0.18 on lpgMacBook-Pro.lan with PID 6031 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator)
2023-03-22 15:28:57 [main] INFO  com.zhonghe.generator.test.TestDemo - The following 1 profile is active: "dev"
2023-03-22 15:28:58 [main] INFO  c.r.framework.config.JacksonConfig - 初始化 jackson 配置
2023-03-22 15:28:59 [main] INFO  c.ruoyi.framework.config.RedisConfig - 初始化 redis 配置
2023-03-22 15:28:59 [main] INFO  org.redisson.Version - Redisson 3.17.4
2023-03-22 15:28:59 [redisson-netty-2-3] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2023-03-22 15:28:59 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2023-03-22 15:29:00 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2023-03-22 15:29:00 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-03-22 15:29:00 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2023-03-22 15:29:00 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-03-22 15:29:00 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2023-03-22 15:29:02 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2023-03-22 15:29:02 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2023-03-22 15:29:02 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2023-03-22 15:29:05 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2023-03-22 15:29:06 [main] INFO  com.zhonghe.generator.test.TestDemo - Started TestDemo in 9.886 seconds (JVM running for 10.99)
2023-03-22 15:29:06 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qiniu
2023-03-22 15:29:06 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => image
2023-03-22 15:29:06 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => minio
2023-03-22 15:29:07 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qcloud
2023-03-22 15:29:07 [main] INFO  factory.com.zhonghe.oss.OssFactory - 初始化OSS工厂
2023-03-22 15:29:07 [main] INFO  c.r.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2023-03-22 15:29:07 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载参数缓存数据成功
2023-03-22 15:29:07 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载字典缓存数据成功
2023-03-22 15:29:08 [SpringApplicationShutdownHook] INFO  c.r.f.manager.ShutdownManager - ====关闭后台任务任务线程池====
2023-03-22 15:29:08 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2023-03-22 15:29:08 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2023-03-22 15:29:08 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-03-22 15:29:08 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2023-03-22 15:29:50 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.zhonghe.generator.test.TestDemo], using SpringBootContextLoader
2023-03-22 15:29:50 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.zhonghe.generator.test.TestDemo]: no resource found for suffixes {-context.xml, Context.groovy}.
2023-03-22 15:29:50 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2023-03-22 15:29:50 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@609640d5, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@79da1ec0, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@19fb8826, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@192d74fb, org.springframework.test.context.support.DependencyInjectionTestExecutionListener@4bef0fe3, org.springframework.test.context.support.DirtiesContextTestExecutionListener@62ea3440, org.springframework.test.context.transaction.TransactionalTestExecutionListener@27953a83, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@556d0826, org.springframework.test.context.event.EventPublishingTestExecutionListener@66ce957f, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@55b5f5d2]
2023-03-22 15:29:50 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2023-03-22 15:29:50 [main] INFO  com.zhonghe.generator.test.TestDemo - Starting TestDemo using Java 11.0.18 on lpgMacBook-Pro.lan with PID 6053 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator)
2023-03-22 15:29:50 [main] INFO  com.zhonghe.generator.test.TestDemo - The following 1 profile is active: "dev"
2023-03-22 15:29:52 [main] INFO  c.r.framework.config.JacksonConfig - 初始化 jackson 配置
2023-03-22 15:29:52 [main] INFO  c.ruoyi.framework.config.RedisConfig - 初始化 redis 配置
2023-03-22 15:29:52 [main] INFO  org.redisson.Version - Redisson 3.17.4
2023-03-22 15:29:53 [redisson-netty-2-8] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2023-03-22 15:29:53 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2023-03-22 15:29:53 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2023-03-22 15:29:53 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-03-22 15:29:53 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2023-03-22 15:29:53 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-03-22 15:29:54 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2023-03-22 15:29:55 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2023-03-22 15:29:55 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2023-03-22 15:29:55 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2023-03-22 15:29:58 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2023-03-22 15:29:59 [main] INFO  com.zhonghe.generator.test.TestDemo - Started TestDemo in 9.515 seconds (JVM running for 10.358)
2023-03-22 15:30:00 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qiniu
2023-03-22 15:30:00 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => image
2023-03-22 15:30:00 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => minio
2023-03-22 15:30:00 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qcloud
2023-03-22 15:30:00 [main] INFO  factory.com.zhonghe.oss.OssFactory - 初始化OSS工厂
2023-03-22 15:30:00 [main] INFO  c.r.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2023-03-22 15:30:00 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载参数缓存数据成功
2023-03-22 15:30:00 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载字典缓存数据成功
2023-03-22 15:30:01 [SpringApplicationShutdownHook] INFO  c.r.f.manager.ShutdownManager - ====关闭后台任务任务线程池====
2023-03-22 15:30:01 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2023-03-22 15:30:02 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2023-03-22 15:30:02 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-03-22 15:30:02 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2023-03-22 15:31:38 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.zhonghe.generator.test.TestDemo], using SpringBootContextLoader
2023-03-22 15:31:38 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.zhonghe.generator.test.TestDemo]: no resource found for suffixes {-context.xml, Context.groovy}.
2023-03-22 15:31:38 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2023-03-22 15:31:38 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@ed3068a, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@7c2b6087, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@3fffff43, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@a8e6492, org.springframework.test.context.support.DependencyInjectionTestExecutionListener@1c7fd41f, org.springframework.test.context.support.DirtiesContextTestExecutionListener@3b77a04f, org.springframework.test.context.transaction.TransactionalTestExecutionListener@7b324585, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@2e11485, org.springframework.test.context.event.EventPublishingTestExecutionListener@60dce7ea, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@662f5666]
2023-03-22 15:31:38 [main] INFO  com.zhonghe.generator.test.TestDemo - Starting TestDemo using Java 11.0.18 on lpgMacBook-Pro.lan with PID 6092 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator)
2023-03-22 15:31:38 [main] INFO  com.zhonghe.generator.test.TestDemo - The following 1 profile is active: "dev"
2023-03-22 15:31:38 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2023-03-22 15:31:40 [main] INFO  c.r.framework.config.JacksonConfig - 初始化 jackson 配置
2023-03-22 15:31:40 [main] INFO  c.ruoyi.framework.config.RedisConfig - 初始化 redis 配置
2023-03-22 15:31:40 [main] INFO  org.redisson.Version - Redisson 3.17.4
2023-03-22 15:31:41 [redisson-netty-2-5] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2023-03-22 15:31:41 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2023-03-22 15:31:41 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2023-03-22 15:31:41 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-03-22 15:31:41 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2023-03-22 15:31:41 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-03-22 15:31:41 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2023-03-22 15:31:43 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2023-03-22 15:31:43 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2023-03-22 15:31:43 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2023-03-22 15:31:46 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2023-03-22 15:31:47 [main] INFO  com.zhonghe.generator.test.TestDemo - Started TestDemo in 8.801 seconds (JVM running for 9.428)
2023-03-22 15:31:47 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qiniu
2023-03-22 15:31:47 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => image
2023-03-22 15:31:47 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => minio
2023-03-22 15:31:47 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qcloud
2023-03-22 15:31:47 [main] INFO  factory.com.zhonghe.oss.OssFactory - 初始化OSS工厂
2023-03-22 15:31:47 [main] INFO  c.r.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2023-03-22 15:31:47 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载参数缓存数据成功
2023-03-22 15:31:48 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载字典缓存数据成功
2023-03-22 15:31:49 [SpringApplicationShutdownHook] INFO  c.r.f.manager.ShutdownManager - ====关闭后台任务任务线程池====
2023-03-22 15:31:49 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2023-03-22 15:31:49 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2023-03-22 15:31:49 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-03-22 15:31:49 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2023-03-22 15:33:41 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.zhonghe.generator.test.TestDemo], using SpringBootContextLoader
2023-03-22 15:33:41 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.zhonghe.generator.test.TestDemo]: no resource found for suffixes {-context.xml, Context.groovy}.
2023-03-22 15:33:41 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2023-03-22 15:33:41 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@ed3068a, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@7c2b6087, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@3fffff43, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@a8e6492, org.springframework.test.context.support.DependencyInjectionTestExecutionListener@1c7fd41f, org.springframework.test.context.support.DirtiesContextTestExecutionListener@3b77a04f, org.springframework.test.context.transaction.TransactionalTestExecutionListener@7b324585, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@2e11485, org.springframework.test.context.event.EventPublishingTestExecutionListener@60dce7ea, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@662f5666]
2023-03-22 15:33:42 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2023-03-22 15:33:42 [main] INFO  com.zhonghe.generator.test.TestDemo - Starting TestDemo using Java 11.0.18 on lpgMacBook-Pro.lan with PID 6124 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator)
2023-03-22 15:33:42 [main] INFO  com.zhonghe.generator.test.TestDemo - The following 1 profile is active: "dev"
2023-03-22 15:33:43 [main] INFO  c.r.framework.config.JacksonConfig - 初始化 jackson 配置
2023-03-22 15:33:43 [main] INFO  c.ruoyi.framework.config.RedisConfig - 初始化 redis 配置
2023-03-22 15:33:43 [main] INFO  org.redisson.Version - Redisson 3.17.4
2023-03-22 15:33:44 [redisson-netty-2-5] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2023-03-22 15:33:44 [redisson-netty-2-2] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2023-03-22 15:33:44 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2023-03-22 15:33:44 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-03-22 15:33:44 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2023-03-22 15:33:44 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-03-22 15:33:45 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2023-03-22 15:33:46 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2023-03-22 15:33:46 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2023-03-22 15:33:46 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2023-03-22 15:33:49 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2023-03-22 15:33:50 [main] INFO  com.zhonghe.generator.test.TestDemo - Started TestDemo in 8.823 seconds (JVM running for 9.395)
2023-03-22 15:33:50 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qiniu
2023-03-22 15:33:51 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => image
2023-03-22 15:33:51 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => minio
2023-03-22 15:33:51 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qcloud
2023-03-22 15:33:51 [main] INFO  factory.com.zhonghe.oss.OssFactory - 初始化OSS工厂
2023-03-22 15:33:51 [main] INFO  c.r.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2023-03-22 15:33:51 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载参数缓存数据成功
2023-03-22 15:33:52 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载字典缓存数据成功
2023-03-22 15:33:53 [SpringApplicationShutdownHook] INFO  c.r.f.manager.ShutdownManager - ====关闭后台任务任务线程池====
2023-03-22 15:33:53 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2023-03-22 15:33:53 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2023-03-22 15:33:53 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-03-22 15:33:53 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2023-03-22 15:36:34 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.zhonghe.generator.test.TestDemo], using SpringBootContextLoader
2023-03-22 15:36:34 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.zhonghe.generator.test.TestDemo]: no resource found for suffixes {-context.xml, Context.groovy}.
2023-03-22 15:36:34 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2023-03-22 15:36:34 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@609640d5, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@79da1ec0, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@19fb8826, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@192d74fb, org.springframework.test.context.support.DependencyInjectionTestExecutionListener@4bef0fe3, org.springframework.test.context.support.DirtiesContextTestExecutionListener@62ea3440, org.springframework.test.context.transaction.TransactionalTestExecutionListener@27953a83, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@556d0826, org.springframework.test.context.event.EventPublishingTestExecutionListener@66ce957f, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@55b5f5d2]
2023-03-22 15:36:35 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2023-03-22 15:36:35 [main] INFO  com.zhonghe.generator.test.TestDemo - Starting TestDemo using Java 11.0.18 on lpgMacBook-Pro.lan with PID 6161 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator)
2023-03-22 15:36:35 [main] INFO  com.zhonghe.generator.test.TestDemo - The following 1 profile is active: "dev"
2023-03-22 15:36:36 [main] INFO  c.r.framework.config.JacksonConfig - 初始化 jackson 配置
2023-03-22 15:36:36 [main] INFO  c.ruoyi.framework.config.RedisConfig - 初始化 redis 配置
2023-03-22 15:36:36 [main] INFO  org.redisson.Version - Redisson 3.17.4
2023-03-22 15:36:37 [redisson-netty-2-8] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2023-03-22 15:36:37 [redisson-netty-2-2] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2023-03-22 15:36:37 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2023-03-22 15:36:37 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-03-22 15:36:37 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2023-03-22 15:36:37 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-03-22 15:36:38 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2023-03-22 15:36:39 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2023-03-22 15:36:39 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2023-03-22 15:36:39 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2023-03-22 15:36:43 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2023-03-22 15:36:44 [main] INFO  com.zhonghe.generator.test.TestDemo - Started TestDemo in 9.82 seconds (JVM running for 10.675)
2023-03-22 15:36:44 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qiniu
2023-03-22 15:36:44 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => image
2023-03-22 15:36:44 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => minio
2023-03-22 15:36:44 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qcloud
2023-03-22 15:36:44 [main] INFO  factory.com.zhonghe.oss.OssFactory - 初始化OSS工厂
2023-03-22 15:36:45 [main] INFO  c.r.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2023-03-22 15:36:45 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载参数缓存数据成功
2023-03-22 15:36:45 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载字典缓存数据成功
2023-03-22 15:36:52 [SpringApplicationShutdownHook] INFO  c.r.f.manager.ShutdownManager - ====关闭后台任务任务线程池====
2023-03-22 15:36:52 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2023-03-22 15:36:52 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2023-03-22 15:36:52 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-03-22 15:36:52 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2023-03-22 15:37:31 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.zhonghe.generator.test.TestDemo], using SpringBootContextLoader
2023-03-22 15:37:31 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.zhonghe.generator.test.TestDemo]: no resource found for suffixes {-context.xml, Context.groovy}.
2023-03-22 15:37:31 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2023-03-22 15:37:31 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@609640d5, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@79da1ec0, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@19fb8826, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@192d74fb, org.springframework.test.context.support.DependencyInjectionTestExecutionListener@4bef0fe3, org.springframework.test.context.support.DirtiesContextTestExecutionListener@62ea3440, org.springframework.test.context.transaction.TransactionalTestExecutionListener@27953a83, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@556d0826, org.springframework.test.context.event.EventPublishingTestExecutionListener@66ce957f, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@55b5f5d2]
2023-03-22 15:37:31 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2023-03-22 15:37:31 [main] INFO  com.zhonghe.generator.test.TestDemo - Starting TestDemo using Java 11.0.18 on lpgMacBook-Pro.lan with PID 6175 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator)
2023-03-22 15:37:31 [main] INFO  com.zhonghe.generator.test.TestDemo - The following 1 profile is active: "dev"
2023-03-22 15:37:33 [main] INFO  c.r.framework.config.JacksonConfig - 初始化 jackson 配置
2023-03-22 15:37:33 [main] INFO  c.ruoyi.framework.config.RedisConfig - 初始化 redis 配置
2023-03-22 15:37:33 [main] INFO  org.redisson.Version - Redisson 3.17.4
2023-03-22 15:37:34 [redisson-netty-2-4] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2023-03-22 15:37:34 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2023-03-22 15:37:34 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2023-03-22 15:37:34 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-03-22 15:37:34 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2023-03-22 15:37:35 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-03-22 15:37:35 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2023-03-22 15:37:36 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2023-03-22 15:37:36 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2023-03-22 15:37:36 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2023-03-22 15:37:39 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2023-03-22 15:37:41 [main] INFO  com.zhonghe.generator.test.TestDemo - Started TestDemo in 9.557 seconds (JVM running for 10.354)
2023-03-22 15:37:41 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qiniu
2023-03-22 15:37:41 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => image
2023-03-22 15:37:41 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => minio
2023-03-22 15:37:41 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qcloud
2023-03-22 15:37:41 [main] INFO  factory.com.zhonghe.oss.OssFactory - 初始化OSS工厂
2023-03-22 15:37:41 [main] INFO  c.r.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2023-03-22 15:37:41 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载参数缓存数据成功
2023-03-22 15:37:41 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载字典缓存数据成功
2023-03-22 15:37:56 [SpringApplicationShutdownHook] INFO  c.r.f.manager.ShutdownManager - ====关闭后台任务任务线程池====
2023-03-22 15:37:56 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2023-03-22 15:37:56 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2023-03-22 15:37:56 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-03-22 15:37:56 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2023-03-22 15:37:59 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.zhonghe.generator.test.TestDemo], using SpringBootContextLoader
2023-03-22 15:37:59 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.zhonghe.generator.test.TestDemo]: no resource found for suffixes {-context.xml, Context.groovy}.
2023-03-22 15:37:59 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2023-03-22 15:37:59 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@609640d5, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@79da1ec0, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@19fb8826, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@192d74fb, org.springframework.test.context.support.DependencyInjectionTestExecutionListener@4bef0fe3, org.springframework.test.context.support.DirtiesContextTestExecutionListener@62ea3440, org.springframework.test.context.transaction.TransactionalTestExecutionListener@27953a83, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@556d0826, org.springframework.test.context.event.EventPublishingTestExecutionListener@66ce957f, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@55b5f5d2]
2023-03-22 15:38:00 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2023-03-22 15:38:00 [main] INFO  com.zhonghe.generator.test.TestDemo - Starting TestDemo using Java 11.0.18 on lpgMacBook-Pro.lan with PID 6181 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator)
2023-03-22 15:38:00 [main] INFO  com.zhonghe.generator.test.TestDemo - The following 1 profile is active: "dev"
2023-03-22 15:38:01 [main] INFO  c.r.framework.config.JacksonConfig - 初始化 jackson 配置
2023-03-22 15:38:01 [main] INFO  c.ruoyi.framework.config.RedisConfig - 初始化 redis 配置
2023-03-22 15:38:01 [main] INFO  org.redisson.Version - Redisson 3.17.4
2023-03-22 15:38:02 [redisson-netty-2-5] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2023-03-22 15:38:02 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2023-03-22 15:38:02 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2023-03-22 15:38:02 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-03-22 15:38:02 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2023-03-22 15:38:02 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-03-22 15:38:03 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2023-03-22 15:38:04 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2023-03-22 15:38:04 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2023-03-22 15:38:04 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2023-03-22 15:38:08 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2023-03-22 15:38:09 [main] INFO  com.zhonghe.generator.test.TestDemo - Started TestDemo in 10.185 seconds (JVM running for 10.946)
2023-03-22 15:38:10 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qiniu
2023-03-22 15:38:10 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => image
2023-03-22 15:38:10 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => minio
2023-03-22 15:38:10 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qcloud
2023-03-22 15:38:10 [main] INFO  factory.com.zhonghe.oss.OssFactory - 初始化OSS工厂
2023-03-22 15:38:10 [main] INFO  c.r.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2023-03-22 15:38:10 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载参数缓存数据成功
2023-03-22 15:38:10 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载字典缓存数据成功
2023-03-22 15:38:33 [SpringApplicationShutdownHook] INFO  c.r.f.manager.ShutdownManager - ====关闭后台任务任务线程池====
2023-03-22 15:38:33 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2023-03-22 15:38:33 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2023-03-22 15:38:33 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-03-22 15:38:33 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2023-03-22 15:38:41 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.zhonghe.generator.test.TestDemo], using SpringBootContextLoader
2023-03-22 15:38:41 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.zhonghe.generator.test.TestDemo]: no resource found for suffixes {-context.xml, Context.groovy}.
2023-03-22 15:38:42 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2023-03-22 15:38:42 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@609640d5, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@79da1ec0, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@19fb8826, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@192d74fb, org.springframework.test.context.support.DependencyInjectionTestExecutionListener@4bef0fe3, org.springframework.test.context.support.DirtiesContextTestExecutionListener@62ea3440, org.springframework.test.context.transaction.TransactionalTestExecutionListener@27953a83, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@556d0826, org.springframework.test.context.event.EventPublishingTestExecutionListener@66ce957f, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@55b5f5d2]
2023-03-22 15:38:42 [main] INFO  com.zhonghe.generator.test.TestDemo - Starting TestDemo using Java 11.0.18 on lpgMacBook-Pro.lan with PID 6193 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator)
2023-03-22 15:38:42 [main] INFO  com.zhonghe.generator.test.TestDemo - The following 1 profile is active: "dev"
2023-03-22 15:38:42 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2023-03-22 15:38:44 [main] INFO  c.r.framework.config.JacksonConfig - 初始化 jackson 配置
2023-03-22 15:38:44 [main] INFO  c.ruoyi.framework.config.RedisConfig - 初始化 redis 配置
2023-03-22 15:38:44 [main] INFO  org.redisson.Version - Redisson 3.17.4
2023-03-22 15:38:44 [redisson-netty-2-5] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2023-03-22 15:38:44 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2023-03-22 15:38:45 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2023-03-22 15:38:45 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-03-22 15:38:45 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2023-03-22 15:38:45 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-03-22 15:38:45 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2023-03-22 15:38:46 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2023-03-22 15:38:46 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2023-03-22 15:38:46 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2023-03-22 15:38:50 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2023-03-22 15:38:51 [main] INFO  com.zhonghe.generator.test.TestDemo - Started TestDemo in 9.298 seconds (JVM running for 10.008)
2023-03-22 15:38:51 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qiniu
2023-03-22 15:38:51 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => image
2023-03-22 15:38:51 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => minio
2023-03-22 15:38:51 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qcloud
2023-03-22 15:38:51 [main] INFO  factory.com.zhonghe.oss.OssFactory - 初始化OSS工厂
2023-03-22 15:38:51 [main] INFO  c.r.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2023-03-22 15:38:51 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载参数缓存数据成功
2023-03-22 15:38:52 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载字典缓存数据成功
2023-03-22 15:39:20 [SpringApplicationShutdownHook] INFO  c.r.f.manager.ShutdownManager - ====关闭后台任务任务线程池====
2023-03-22 15:39:20 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2023-03-22 15:39:20 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2023-03-22 15:39:20 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-03-22 15:39:20 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2023-03-22 15:39:23 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.zhonghe.generator.test.TestDemo], using SpringBootContextLoader
2023-03-22 15:39:23 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.zhonghe.generator.test.TestDemo]: no resource found for suffixes {-context.xml, Context.groovy}.
2023-03-22 15:39:23 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2023-03-22 15:39:23 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@609640d5, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@79da1ec0, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@19fb8826, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@192d74fb, org.springframework.test.context.support.DependencyInjectionTestExecutionListener@4bef0fe3, org.springframework.test.context.support.DirtiesContextTestExecutionListener@62ea3440, org.springframework.test.context.transaction.TransactionalTestExecutionListener@27953a83, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@556d0826, org.springframework.test.context.event.EventPublishingTestExecutionListener@66ce957f, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@55b5f5d2]
2023-03-22 15:39:24 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2023-03-22 15:39:24 [main] INFO  com.zhonghe.generator.test.TestDemo - Starting TestDemo using Java 11.0.18 on lpgMacBook-Pro.lan with PID 6206 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator)
2023-03-22 15:39:24 [main] INFO  com.zhonghe.generator.test.TestDemo - The following 1 profile is active: "dev"
2023-03-22 15:39:25 [main] INFO  c.r.framework.config.JacksonConfig - 初始化 jackson 配置
2023-03-22 15:39:26 [main] INFO  c.ruoyi.framework.config.RedisConfig - 初始化 redis 配置
2023-03-22 15:39:26 [main] INFO  org.redisson.Version - Redisson 3.17.4
2023-03-22 15:39:26 [redisson-netty-2-5] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2023-03-22 15:39:26 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2023-03-22 15:39:27 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2023-03-22 15:39:27 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-03-22 15:39:27 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2023-03-22 15:39:27 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-03-22 15:39:27 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2023-03-22 15:39:28 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2023-03-22 15:39:28 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2023-03-22 15:39:28 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2023-03-22 15:39:32 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2023-03-22 15:39:36 [main] INFO  com.zhonghe.generator.test.TestDemo - Started TestDemo in 12.176 seconds (JVM running for 13.204)
2023-03-22 15:39:37 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qiniu
2023-03-22 15:39:37 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => image
2023-03-22 15:39:37 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => minio
2023-03-22 15:39:37 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qcloud
2023-03-22 15:39:37 [main] INFO  factory.com.zhonghe.oss.OssFactory - 初始化OSS工厂
2023-03-22 15:39:37 [main] INFO  c.r.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2023-03-22 15:39:37 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载参数缓存数据成功
2023-03-22 15:39:37 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载字典缓存数据成功
2023-03-22 15:40:28 [SpringApplicationShutdownHook] INFO  c.r.f.manager.ShutdownManager - ====关闭后台任务任务线程池====
2023-03-22 15:40:28 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2023-03-22 15:40:28 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2023-03-22 15:40:28 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-03-22 15:40:28 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2023-03-22 15:40:31 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.zhonghe.generator.test.TestDemo], using SpringBootContextLoader
2023-03-22 15:40:31 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.zhonghe.generator.test.TestDemo]: no resource found for suffixes {-context.xml, Context.groovy}.
2023-03-22 15:40:32 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2023-03-22 15:40:32 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@609640d5, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@79da1ec0, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@19fb8826, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@192d74fb, org.springframework.test.context.support.DependencyInjectionTestExecutionListener@4bef0fe3, org.springframework.test.context.support.DirtiesContextTestExecutionListener@62ea3440, org.springframework.test.context.transaction.TransactionalTestExecutionListener@27953a83, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@556d0826, org.springframework.test.context.event.EventPublishingTestExecutionListener@66ce957f, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@55b5f5d2]
2023-03-22 15:40:32 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2023-03-22 15:40:32 [main] INFO  com.zhonghe.generator.test.TestDemo - Starting TestDemo using Java 11.0.18 on lpgMacBook-Pro.lan with PID 6234 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator)
2023-03-22 15:40:32 [main] INFO  com.zhonghe.generator.test.TestDemo - The following 1 profile is active: "dev"
2023-03-22 15:40:40 [main] INFO  c.r.framework.config.JacksonConfig - 初始化 jackson 配置
2023-03-22 15:40:41 [main] INFO  c.ruoyi.framework.config.RedisConfig - 初始化 redis 配置
2023-03-22 15:40:41 [main] INFO  org.redisson.Version - Redisson 3.17.4
2023-03-22 15:40:41 [redisson-netty-2-6] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2023-03-22 15:40:42 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2023-03-22 15:40:43 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2023-03-22 15:40:43 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-03-22 15:40:43 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2023-03-22 15:40:43 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-03-22 15:40:44 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2023-03-22 15:40:46 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2023-03-22 15:40:46 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2023-03-22 15:40:46 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2023-03-22 15:40:53 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2023-03-22 15:40:58 [main] INFO  com.zhonghe.generator.test.TestDemo - Started TestDemo in 26.849 seconds (JVM running for 27.857)
2023-03-22 15:40:59 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qiniu
2023-03-22 15:40:59 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => image
2023-03-22 15:40:59 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => minio
2023-03-22 15:40:59 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qcloud
2023-03-22 15:40:59 [main] INFO  factory.com.zhonghe.oss.OssFactory - 初始化OSS工厂
2023-03-22 15:40:59 [main] INFO  c.r.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2023-03-22 15:40:59 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载参数缓存数据成功
2023-03-22 15:40:59 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载字典缓存数据成功
2023-03-22 15:41:15 [SpringApplicationShutdownHook] INFO  c.r.f.manager.ShutdownManager - ====关闭后台任务任务线程池====
2023-03-22 15:41:15 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2023-03-22 15:41:15 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2023-03-22 15:41:15 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-03-22 15:41:15 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2023-03-22 15:46:51 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.zhonghe.generator.test.TestDemo], using SpringBootContextLoader
2023-03-22 15:46:51 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.zhonghe.generator.test.TestDemo]: no resource found for suffixes {-context.xml, Context.groovy}.
2023-03-22 15:46:51 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2023-03-22 15:46:51 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@ed3068a, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@7c2b6087, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@3fffff43, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@a8e6492, org.springframework.test.context.support.DependencyInjectionTestExecutionListener@1c7fd41f, org.springframework.test.context.support.DirtiesContextTestExecutionListener@3b77a04f, org.springframework.test.context.transaction.TransactionalTestExecutionListener@7b324585, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@2e11485, org.springframework.test.context.event.EventPublishingTestExecutionListener@60dce7ea, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@662f5666]
2023-03-22 15:46:51 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2023-03-22 15:46:51 [main] INFO  com.zhonghe.generator.test.TestDemo - Starting TestDemo using Java 11.0.18 on lpgMacBook-Pro.lan with PID 6300 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator)
2023-03-22 15:46:51 [main] INFO  com.zhonghe.generator.test.TestDemo - The following 1 profile is active: "dev"
2023-03-22 15:46:53 [main] INFO  c.r.framework.config.JacksonConfig - 初始化 jackson 配置
2023-03-22 15:46:53 [main] INFO  c.ruoyi.framework.config.RedisConfig - 初始化 redis 配置
2023-03-22 15:46:53 [main] INFO  org.redisson.Version - Redisson 3.17.4
2023-03-22 15:46:54 [redisson-netty-2-5] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2023-03-22 15:46:54 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2023-03-22 15:46:54 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2023-03-22 15:46:54 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-03-22 15:46:54 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2023-03-22 15:46:54 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-03-22 15:46:55 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2023-03-22 15:46:57 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2023-03-22 15:46:57 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2023-03-22 15:46:57 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2023-03-22 15:48:44 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2023-03-22 15:48:55 [main] INFO  com.zhonghe.generator.test.TestDemo - Started TestDemo in 69.943 seconds (JVM running for 70.486)
2023-03-22 15:48:56 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qiniu
2023-03-22 15:48:56 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => image
2023-03-22 15:48:56 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => minio
2023-03-22 15:48:56 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qcloud
2023-03-22 15:48:56 [main] INFO  factory.com.zhonghe.oss.OssFactory - 初始化OSS工厂
2023-03-22 15:48:56 [main] INFO  c.r.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2023-03-22 15:48:56 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载参数缓存数据成功
2023-03-22 15:48:56 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载字典缓存数据成功
2023-03-22 15:48:58 [SpringApplicationShutdownHook] INFO  c.r.f.manager.ShutdownManager - ====关闭后台任务任务线程池====
2023-03-22 15:48:59 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2023-03-22 15:48:59 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2023-03-22 15:48:59 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-03-22 15:48:59 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2023-03-22 15:49:54 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.zhonghe.generator.test.TestDemo], using SpringBootContextLoader
2023-03-22 15:49:54 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.zhonghe.generator.test.TestDemo]: no resource found for suffixes {-context.xml, Context.groovy}.
2023-03-22 15:49:54 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2023-03-22 15:49:54 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@609640d5, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@79da1ec0, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@19fb8826, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@192d74fb, org.springframework.test.context.support.DependencyInjectionTestExecutionListener@4bef0fe3, org.springframework.test.context.support.DirtiesContextTestExecutionListener@62ea3440, org.springframework.test.context.transaction.TransactionalTestExecutionListener@27953a83, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@556d0826, org.springframework.test.context.event.EventPublishingTestExecutionListener@66ce957f, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@55b5f5d2]
2023-03-22 15:49:55 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2023-03-22 15:49:55 [main] INFO  com.zhonghe.generator.test.TestDemo - Starting TestDemo using Java 11.0.18 on lpgMacBook-Pro.lan with PID 6379 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator)
2023-03-22 15:49:55 [main] INFO  com.zhonghe.generator.test.TestDemo - The following 1 profile is active: "dev"
2023-03-22 15:49:57 [main] INFO  c.r.framework.config.JacksonConfig - 初始化 jackson 配置
2023-03-22 15:49:58 [main] INFO  c.ruoyi.framework.config.RedisConfig - 初始化 redis 配置
2023-03-22 15:49:58 [main] INFO  org.redisson.Version - Redisson 3.17.4
2023-03-22 15:49:59 [redisson-netty-2-8] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2023-03-22 15:49:59 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2023-03-22 15:50:02 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2023-03-22 15:50:02 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-03-22 15:50:02 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2023-03-22 15:50:02 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-03-22 15:50:04 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2023-03-22 15:50:05 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2023-03-22 15:50:05 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2023-03-22 15:50:05 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2023-03-22 15:50:13 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2023-03-22 15:50:21 [main] INFO  com.zhonghe.generator.test.TestDemo - Started TestDemo in 26.213 seconds (JVM running for 27.392)
2023-03-22 15:50:21 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qiniu
2023-03-22 15:50:21 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => image
2023-03-22 15:50:21 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => minio
2023-03-22 15:50:21 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qcloud
2023-03-22 15:50:21 [main] INFO  factory.com.zhonghe.oss.OssFactory - 初始化OSS工厂
2023-03-22 15:50:21 [main] INFO  c.r.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2023-03-22 15:50:21 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载参数缓存数据成功
2023-03-22 15:50:22 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载字典缓存数据成功
2023-03-22 15:50:43 [SpringApplicationShutdownHook] INFO  c.r.f.manager.ShutdownManager - ====关闭后台任务任务线程池====
2023-03-22 15:50:43 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2023-03-22 15:50:43 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2023-03-22 15:50:43 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-03-22 15:50:43 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2023-03-22 15:52:14 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.zhonghe.generator.test.TestDemo], using SpringBootContextLoader
2023-03-22 15:52:14 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.zhonghe.generator.test.TestDemo]: no resource found for suffixes {-context.xml, Context.groovy}.
2023-03-22 15:52:14 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2023-03-22 15:52:14 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@609640d5, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@79da1ec0, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@19fb8826, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@192d74fb, org.springframework.test.context.support.DependencyInjectionTestExecutionListener@4bef0fe3, org.springframework.test.context.support.DirtiesContextTestExecutionListener@62ea3440, org.springframework.test.context.transaction.TransactionalTestExecutionListener@27953a83, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@556d0826, org.springframework.test.context.event.EventPublishingTestExecutionListener@66ce957f, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@55b5f5d2]
2023-03-22 15:52:14 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2023-03-22 15:52:14 [main] INFO  com.zhonghe.generator.test.TestDemo - Starting TestDemo using Java 11.0.18 on lpgMacBook-Pro.lan with PID 6403 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator)
2023-03-22 15:52:14 [main] INFO  com.zhonghe.generator.test.TestDemo - The following 1 profile is active: "dev"
2023-03-22 15:52:16 [main] INFO  c.r.framework.config.JacksonConfig - 初始化 jackson 配置
2023-03-22 15:52:16 [main] INFO  c.ruoyi.framework.config.RedisConfig - 初始化 redis 配置
2023-03-22 15:52:16 [main] INFO  org.redisson.Version - Redisson 3.17.4
2023-03-22 15:52:16 [redisson-netty-2-5] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2023-03-22 15:52:16 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2023-03-22 15:52:17 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2023-03-22 15:52:17 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-03-22 15:52:17 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2023-03-22 15:52:17 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-03-22 15:52:18 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2023-03-22 15:52:19 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2023-03-22 15:52:19 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2023-03-22 15:52:19 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2023-03-22 15:52:22 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2023-03-22 15:52:23 [main] INFO  com.zhonghe.generator.test.TestDemo - Started TestDemo in 9.295 seconds (JVM running for 10.079)
2023-03-22 15:52:24 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qiniu
2023-03-22 15:52:24 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => image
2023-03-22 15:52:24 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => minio
2023-03-22 15:52:24 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qcloud
2023-03-22 15:52:24 [main] INFO  factory.com.zhonghe.oss.OssFactory - 初始化OSS工厂
2023-03-22 15:52:24 [main] INFO  c.r.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2023-03-22 15:52:24 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载参数缓存数据成功
2023-03-22 15:52:24 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载字典缓存数据成功
2023-03-22 16:02:11 [SpringApplicationShutdownHook] INFO  c.r.f.manager.ShutdownManager - ====关闭后台任务任务线程池====
2023-03-22 16:02:11 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2023-03-22 16:02:11 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2023-03-22 16:02:11 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-03-22 16:02:11 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2023-03-22 16:15:44 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.zhonghe.generator.test.TestDemo], using SpringBootContextLoader
2023-03-22 16:15:44 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.zhonghe.generator.test.TestDemo]: no resource found for suffixes {-context.xml, Context.groovy}.
2023-03-22 16:15:44 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2023-03-22 16:15:44 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@ed3068a, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@7c2b6087, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@3fffff43, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@a8e6492, org.springframework.test.context.support.DependencyInjectionTestExecutionListener@1c7fd41f, org.springframework.test.context.support.DirtiesContextTestExecutionListener@3b77a04f, org.springframework.test.context.transaction.TransactionalTestExecutionListener@7b324585, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@2e11485, org.springframework.test.context.event.EventPublishingTestExecutionListener@60dce7ea, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@662f5666]
2023-03-22 16:15:44 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2023-03-22 16:15:44 [main] INFO  com.zhonghe.generator.test.TestDemo - Starting TestDemo using Java 11.0.18 on lpgMacBook-Pro.lan with PID 6963 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator)
2023-03-22 16:15:44 [main] INFO  com.zhonghe.generator.test.TestDemo - The following 1 profile is active: "dev"
2023-03-22 16:15:46 [main] INFO  c.r.framework.config.JacksonConfig - 初始化 jackson 配置
2023-03-22 16:15:46 [main] INFO  c.ruoyi.framework.config.RedisConfig - 初始化 redis 配置
2023-03-22 16:15:46 [main] INFO  org.redisson.Version - Redisson 3.17.4
2023-03-22 16:15:46 [redisson-netty-2-6] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2023-03-22 16:15:46 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2023-03-22 16:15:47 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2023-03-22 16:15:47 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-03-22 16:15:47 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2023-03-22 16:15:47 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-03-22 16:15:47 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2023-03-22 16:15:49 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2023-03-22 16:15:49 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2023-03-22 16:15:49 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2023-03-22 16:15:51 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2023-03-22 16:15:53 [main] INFO  com.zhonghe.generator.test.TestDemo - Started TestDemo in 8.562 seconds (JVM running for 9.124)
2023-03-22 16:15:53 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qiniu
2023-03-22 16:15:53 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => image
2023-03-22 16:15:53 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => minio
2023-03-22 16:15:53 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qcloud
2023-03-22 16:15:53 [main] INFO  factory.com.zhonghe.oss.OssFactory - 初始化OSS工厂
2023-03-22 16:15:53 [main] INFO  c.r.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2023-03-22 16:15:53 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载参数缓存数据成功
2023-03-22 16:15:53 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载字典缓存数据成功
2023-03-22 16:15:55 [SpringApplicationShutdownHook] INFO  c.r.f.manager.ShutdownManager - ====关闭后台任务任务线程池====
2023-03-22 16:15:55 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2023-03-22 16:15:55 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2023-03-22 16:15:55 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-03-22 16:15:55 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2023-03-22 16:17:33 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.zhonghe.generator.test.TestDemo], using SpringBootContextLoader
2023-03-22 16:17:33 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.zhonghe.generator.test.TestDemo]: no resource found for suffixes {-context.xml, Context.groovy}.
2023-03-22 16:17:33 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2023-03-22 16:17:33 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@ed3068a, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@7c2b6087, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@3fffff43, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@a8e6492, org.springframework.test.context.support.DependencyInjectionTestExecutionListener@1c7fd41f, org.springframework.test.context.support.DirtiesContextTestExecutionListener@3b77a04f, org.springframework.test.context.transaction.TransactionalTestExecutionListener@7b324585, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@2e11485, org.springframework.test.context.event.EventPublishingTestExecutionListener@60dce7ea, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@662f5666]
2023-03-22 16:17:33 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2023-03-22 16:17:33 [main] INFO  com.zhonghe.generator.test.TestDemo - Starting TestDemo using Java 11.0.18 on lpgMacBook-Pro.lan with PID 6996 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator)
2023-03-22 16:17:33 [main] INFO  com.zhonghe.generator.test.TestDemo - The following 1 profile is active: "dev"
2023-03-22 16:17:34 [main] INFO  c.r.framework.config.JacksonConfig - 初始化 jackson 配置
2023-03-22 16:17:35 [main] INFO  c.ruoyi.framework.config.RedisConfig - 初始化 redis 配置
2023-03-22 16:17:35 [main] INFO  org.redisson.Version - Redisson 3.17.4
2023-03-22 16:17:35 [redisson-netty-2-6] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2023-03-22 16:17:35 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2023-03-22 16:17:36 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2023-03-22 16:17:36 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-03-22 16:17:36 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2023-03-22 16:17:36 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-03-22 16:17:36 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2023-03-22 16:17:37 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2023-03-22 16:17:37 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2023-03-22 16:17:37 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2023-03-22 16:17:40 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2023-03-22 16:17:41 [main] INFO  com.zhonghe.generator.test.TestDemo - Started TestDemo in 8.56 seconds (JVM running for 9.265)
2023-03-22 16:17:42 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qiniu
2023-03-22 16:17:42 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => image
2023-03-22 16:17:42 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => minio
2023-03-22 16:17:42 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qcloud
2023-03-22 16:17:42 [main] INFO  factory.com.zhonghe.oss.OssFactory - 初始化OSS工厂
2023-03-22 16:17:42 [main] INFO  c.r.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2023-03-22 16:17:42 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载参数缓存数据成功
2023-03-22 16:17:42 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载字典缓存数据成功
2023-03-22 16:17:44 [SpringApplicationShutdownHook] INFO  c.r.f.manager.ShutdownManager - ====关闭后台任务任务线程池====
2023-03-22 16:17:44 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2023-03-22 16:17:44 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2023-03-22 16:17:44 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-03-22 16:17:44 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2023-03-22 16:25:13 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.zhonghe.generator.test.TestDemo], using SpringBootContextLoader
2023-03-22 16:25:13 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.zhonghe.generator.test.TestDemo]: no resource found for suffixes {-context.xml, Context.groovy}.
2023-03-22 16:25:14 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2023-03-22 16:25:14 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@ed3068a, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@7c2b6087, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@3fffff43, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@a8e6492, org.springframework.test.context.support.DependencyInjectionTestExecutionListener@1c7fd41f, org.springframework.test.context.support.DirtiesContextTestExecutionListener@3b77a04f, org.springframework.test.context.transaction.TransactionalTestExecutionListener@7b324585, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@2e11485, org.springframework.test.context.event.EventPublishingTestExecutionListener@60dce7ea, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@662f5666]
2023-03-22 16:25:14 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2023-03-22 16:25:14 [main] INFO  com.zhonghe.generator.test.TestDemo - Starting TestDemo using Java 11.0.18 on lpgMacBook-Pro.lan with PID 7121 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator)
2023-03-22 16:25:14 [main] INFO  com.zhonghe.generator.test.TestDemo - The following 1 profile is active: "dev"
2023-03-22 16:25:15 [main] INFO  c.r.framework.config.JacksonConfig - 初始化 jackson 配置
2023-03-22 16:25:16 [main] INFO  c.ruoyi.framework.config.RedisConfig - 初始化 redis 配置
2023-03-22 16:25:16 [main] INFO  org.redisson.Version - Redisson 3.17.4
2023-03-22 16:25:16 [redisson-netty-2-5] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2023-03-22 16:25:16 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2023-03-22 16:25:17 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2023-03-22 16:25:17 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-03-22 16:25:17 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2023-03-22 16:25:17 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-03-22 16:25:17 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2023-03-22 16:25:18 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2023-03-22 16:25:18 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2023-03-22 16:25:18 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2023-03-22 16:25:21 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2023-03-22 16:25:23 [main] INFO  com.zhonghe.generator.test.TestDemo - Started TestDemo in 8.981 seconds (JVM running for 9.604)
2023-03-22 16:25:23 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qiniu
2023-03-22 16:25:23 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => image
2023-03-22 16:25:23 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => minio
2023-03-22 16:25:23 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qcloud
2023-03-22 16:25:23 [main] INFO  factory.com.zhonghe.oss.OssFactory - 初始化OSS工厂
2023-03-22 16:25:23 [main] INFO  c.r.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2023-03-22 16:25:23 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载参数缓存数据成功
2023-03-22 16:25:23 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载字典缓存数据成功
2023-03-22 16:25:25 [SpringApplicationShutdownHook] INFO  c.r.f.manager.ShutdownManager - ====关闭后台任务任务线程池====
2023-03-22 16:25:25 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2023-03-22 16:25:25 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2023-03-22 16:25:25 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-03-22 16:25:25 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2023-03-22 16:27:29 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.zhonghe.generator.test.TestDemo], using SpringBootContextLoader
2023-03-22 16:27:29 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.zhonghe.generator.test.TestDemo]: no resource found for suffixes {-context.xml, Context.groovy}.
2023-03-22 16:27:29 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2023-03-22 16:27:29 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@ed3068a, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@7c2b6087, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@3fffff43, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@a8e6492, org.springframework.test.context.support.DependencyInjectionTestExecutionListener@1c7fd41f, org.springframework.test.context.support.DirtiesContextTestExecutionListener@3b77a04f, org.springframework.test.context.transaction.TransactionalTestExecutionListener@7b324585, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@2e11485, org.springframework.test.context.event.EventPublishingTestExecutionListener@60dce7ea, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@662f5666]
2023-03-22 16:27:29 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2023-03-22 16:27:29 [main] INFO  com.zhonghe.generator.test.TestDemo - Starting TestDemo using Java 11.0.18 on lpgMacBook-Pro.lan with PID 7148 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator)
2023-03-22 16:27:29 [main] INFO  com.zhonghe.generator.test.TestDemo - The following 1 profile is active: "dev"
2023-03-22 16:27:30 [main] INFO  c.r.framework.config.JacksonConfig - 初始化 jackson 配置
2023-03-22 16:27:31 [main] INFO  c.ruoyi.framework.config.RedisConfig - 初始化 redis 配置
2023-03-22 16:27:31 [main] INFO  org.redisson.Version - Redisson 3.17.4
2023-03-22 16:27:31 [redisson-netty-2-6] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2023-03-22 16:27:31 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2023-03-22 16:27:32 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2023-03-22 16:27:32 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-03-22 16:27:32 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2023-03-22 16:27:32 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-03-22 16:27:32 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2023-03-22 16:27:33 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2023-03-22 16:27:33 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2023-03-22 16:27:33 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2023-03-22 16:27:36 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2023-03-22 16:27:37 [main] INFO  com.zhonghe.generator.test.TestDemo - Started TestDemo in 8.609 seconds (JVM running for 9.191)
2023-03-22 16:27:38 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qiniu
2023-03-22 16:27:38 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => image
2023-03-22 16:27:38 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => minio
2023-03-22 16:27:38 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qcloud
2023-03-22 16:27:38 [main] INFO  factory.com.zhonghe.oss.OssFactory - 初始化OSS工厂
2023-03-22 16:27:38 [main] INFO  c.r.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2023-03-22 16:27:38 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载参数缓存数据成功
2023-03-22 16:27:38 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载字典缓存数据成功
2023-03-22 16:27:39 [SpringApplicationShutdownHook] INFO  c.r.f.manager.ShutdownManager - ====关闭后台任务任务线程池====
2023-03-22 16:27:39 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2023-03-22 16:27:39 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2023-03-22 16:27:39 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-03-22 16:27:39 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2023-03-22 16:57:51 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.zhonghe.generator.test.TestDemo], using SpringBootContextLoader
2023-03-22 16:57:51 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.zhonghe.generator.test.TestDemo]: no resource found for suffixes {-context.xml, Context.groovy}.
2023-03-22 16:57:51 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2023-03-22 16:57:51 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@ed3068a, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@7c2b6087, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@3fffff43, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@a8e6492, org.springframework.test.context.support.DependencyInjectionTestExecutionListener@1c7fd41f, org.springframework.test.context.support.DirtiesContextTestExecutionListener@3b77a04f, org.springframework.test.context.transaction.TransactionalTestExecutionListener@7b324585, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@2e11485, org.springframework.test.context.event.EventPublishingTestExecutionListener@60dce7ea, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@662f5666]
2023-03-22 16:57:51 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2023-03-22 16:57:51 [main] INFO  com.zhonghe.generator.test.TestDemo - Starting TestDemo using Java 11.0.18 on lpgMacBook-Pro.lan with PID 7492 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator)
2023-03-22 16:57:51 [main] INFO  com.zhonghe.generator.test.TestDemo - The following 1 profile is active: "dev"
2023-03-22 16:57:53 [main] INFO  c.r.framework.config.JacksonConfig - 初始化 jackson 配置
2023-03-22 16:57:53 [main] INFO  c.ruoyi.framework.config.RedisConfig - 初始化 redis 配置
2023-03-22 16:57:53 [main] INFO  org.redisson.Version - Redisson 3.17.4
2023-03-22 16:57:54 [redisson-netty-2-5] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2023-03-22 16:57:54 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2023-03-22 16:57:54 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2023-03-22 16:57:54 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-03-22 16:57:54 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2023-03-22 16:57:54 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-03-22 16:57:55 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2023-03-22 16:57:56 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2023-03-22 16:57:56 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2023-03-22 16:57:56 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2023-03-22 16:57:59 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2023-03-22 16:58:00 [main] INFO  com.zhonghe.generator.test.TestDemo - Started TestDemo in 8.773 seconds (JVM running for 9.473)
2023-03-22 16:58:00 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => minio
2023-03-22 16:58:00 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qiniu
2023-03-22 16:58:00 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => image
2023-03-22 16:58:00 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qcloud
2023-03-22 16:58:00 [main] INFO  factory.com.zhonghe.oss.OssFactory - 初始化OSS工厂
2023-03-22 16:58:00 [main] INFO  c.r.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2023-03-22 16:58:00 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载参数缓存数据成功
2023-03-22 16:58:01 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载字典缓存数据成功
2023-03-22 16:58:02 [SpringApplicationShutdownHook] INFO  c.r.f.manager.ShutdownManager - ====关闭后台任务任务线程池====
2023-03-22 16:58:02 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2023-03-22 16:58:02 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2023-03-22 16:58:02 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-03-22 16:58:02 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2023-03-22 17:01:51 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.zhonghe.generator.test.TestDemo], using SpringBootContextLoader
2023-03-22 17:01:51 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.zhonghe.generator.test.TestDemo]: no resource found for suffixes {-context.xml, Context.groovy}.
2023-03-22 17:01:51 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2023-03-22 17:01:51 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@ed3068a, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@7c2b6087, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@3fffff43, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@a8e6492, org.springframework.test.context.support.DependencyInjectionTestExecutionListener@1c7fd41f, org.springframework.test.context.support.DirtiesContextTestExecutionListener@3b77a04f, org.springframework.test.context.transaction.TransactionalTestExecutionListener@7b324585, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@2e11485, org.springframework.test.context.event.EventPublishingTestExecutionListener@60dce7ea, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@662f5666]
2023-03-22 17:01:52 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2023-03-22 17:01:52 [main] INFO  com.zhonghe.generator.test.TestDemo - Starting TestDemo using Java 11.0.18 on lpgMacBook-Pro.lan with PID 7529 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator)
2023-03-22 17:01:52 [main] INFO  com.zhonghe.generator.test.TestDemo - The following 1 profile is active: "dev"
2023-03-22 17:01:53 [main] INFO  c.r.framework.config.JacksonConfig - 初始化 jackson 配置
2023-03-22 17:01:53 [main] INFO  c.ruoyi.framework.config.RedisConfig - 初始化 redis 配置
2023-03-22 17:01:53 [main] INFO  org.redisson.Version - Redisson 3.17.4
2023-03-22 17:01:54 [redisson-netty-2-5] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2023-03-22 17:01:54 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2023-03-22 17:01:55 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2023-03-22 17:01:55 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-03-22 17:01:55 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2023-03-22 17:01:55 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-03-22 17:01:55 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2023-03-22 17:01:56 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2023-03-22 17:01:56 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2023-03-22 17:01:56 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2023-03-22 17:01:59 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2023-03-22 17:02:00 [main] INFO  com.zhonghe.generator.test.TestDemo - Started TestDemo in 9.145 seconds (JVM running for 9.758)
2023-03-22 17:02:01 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => minio
2023-03-22 17:02:01 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qiniu
2023-03-22 17:02:01 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => image
2023-03-22 17:02:01 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qcloud
2023-03-22 17:02:01 [main] INFO  factory.com.zhonghe.oss.OssFactory - 初始化OSS工厂
2023-03-22 17:02:01 [main] INFO  c.r.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2023-03-22 17:02:01 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载参数缓存数据成功
2023-03-22 17:02:01 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载字典缓存数据成功
2023-03-22 17:02:02 [SpringApplicationShutdownHook] INFO  c.r.f.manager.ShutdownManager - ====关闭后台任务任务线程池====
2023-03-22 17:02:02 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2023-03-22 17:02:02 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2023-03-22 17:02:02 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-03-22 17:02:02 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2023-03-22 17:08:39 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.zhonghe.generator.test.TestDemo], using SpringBootContextLoader
2023-03-22 17:08:39 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.zhonghe.generator.test.TestDemo]: no resource found for suffixes {-context.xml, Context.groovy}.
2023-03-22 17:08:39 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2023-03-22 17:08:39 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@609640d5, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@79da1ec0, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@19fb8826, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@192d74fb, org.springframework.test.context.support.DependencyInjectionTestExecutionListener@4bef0fe3, org.springframework.test.context.support.DirtiesContextTestExecutionListener@62ea3440, org.springframework.test.context.transaction.TransactionalTestExecutionListener@27953a83, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@556d0826, org.springframework.test.context.event.EventPublishingTestExecutionListener@66ce957f, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@55b5f5d2]
2023-03-22 17:08:39 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2023-03-22 17:08:39 [main] INFO  com.zhonghe.generator.test.TestDemo - Starting TestDemo using Java 11.0.18 on lpgMacBook-Pro.lan with PID 7732 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator)
2023-03-22 17:08:39 [main] INFO  com.zhonghe.generator.test.TestDemo - The following 1 profile is active: "dev"
2023-03-22 17:08:41 [main] INFO  c.r.framework.config.JacksonConfig - 初始化 jackson 配置
2023-03-22 17:08:41 [main] INFO  c.ruoyi.framework.config.RedisConfig - 初始化 redis 配置
2023-03-22 17:08:41 [main] INFO  org.redisson.Version - Redisson 3.17.4
2023-03-22 17:08:42 [redisson-netty-2-3] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2023-03-22 17:08:42 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2023-03-22 17:08:42 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2023-03-22 17:08:42 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-03-22 17:08:42 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2023-03-22 17:08:42 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-03-22 17:08:43 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2023-03-22 17:08:44 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2023-03-22 17:08:44 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2023-03-22 17:08:44 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2023-03-22 17:08:47 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2023-03-22 17:08:49 [main] INFO  com.zhonghe.generator.test.TestDemo - Started TestDemo in 9.372 seconds (JVM running for 10.268)
2023-03-22 17:08:49 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => minio
2023-03-22 17:08:49 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qiniu
2023-03-22 17:08:49 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => image
2023-03-22 17:08:49 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qcloud
2023-03-22 17:08:49 [main] INFO  factory.com.zhonghe.oss.OssFactory - 初始化OSS工厂
2023-03-22 17:08:49 [main] INFO  c.r.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2023-03-22 17:08:49 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载参数缓存数据成功
2023-03-22 17:08:49 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载字典缓存数据成功
2023-03-22 17:08:54 [SpringApplicationShutdownHook] INFO  c.r.f.manager.ShutdownManager - ====关闭后台任务任务线程池====
2023-03-22 17:08:54 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2023-03-22 17:08:54 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2023-03-22 17:08:54 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-03-22 17:08:54 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2023-03-22 17:11:15 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.zhonghe.generator.test.TestDemo], using SpringBootContextLoader
2023-03-22 17:11:15 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.zhonghe.generator.test.TestDemo]: no resource found for suffixes {-context.xml, Context.groovy}.
2023-03-22 17:11:15 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2023-03-22 17:11:15 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@ed3068a, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@7c2b6087, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@3fffff43, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@a8e6492, org.springframework.test.context.support.DependencyInjectionTestExecutionListener@1c7fd41f, org.springframework.test.context.support.DirtiesContextTestExecutionListener@3b77a04f, org.springframework.test.context.transaction.TransactionalTestExecutionListener@7b324585, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@2e11485, org.springframework.test.context.event.EventPublishingTestExecutionListener@60dce7ea, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@662f5666]
2023-03-22 17:11:15 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2023-03-22 17:11:15 [main] INFO  com.zhonghe.generator.test.TestDemo - Starting TestDemo using Java 11.0.18 on lpgMacBook-Pro.lan with PID 7793 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator)
2023-03-22 17:11:15 [main] INFO  com.zhonghe.generator.test.TestDemo - The following 1 profile is active: "dev"
2023-03-22 17:11:17 [main] INFO  c.r.framework.config.JacksonConfig - 初始化 jackson 配置
2023-03-22 17:11:17 [main] INFO  c.ruoyi.framework.config.RedisConfig - 初始化 redis 配置
2023-03-22 17:11:17 [main] INFO  org.redisson.Version - Redisson 3.17.4
2023-03-22 17:11:17 [redisson-netty-2-5] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2023-03-22 17:11:18 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2023-03-22 17:11:18 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2023-03-22 17:11:18 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-03-22 17:11:18 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2023-03-22 17:11:18 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-03-22 17:11:18 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2023-03-22 17:11:20 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2023-03-22 17:11:20 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2023-03-22 17:11:20 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2023-03-22 17:11:22 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2023-03-22 17:11:24 [main] INFO  com.zhonghe.generator.test.TestDemo - Started TestDemo in 8.777 seconds (JVM running for 9.346)
2023-03-22 17:11:24 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => minio
2023-03-22 17:11:24 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qiniu
2023-03-22 17:11:24 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => image
2023-03-22 17:11:24 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qcloud
2023-03-22 17:11:24 [main] INFO  factory.com.zhonghe.oss.OssFactory - 初始化OSS工厂
2023-03-22 17:11:24 [main] INFO  c.r.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2023-03-22 17:11:24 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载参数缓存数据成功
2023-03-22 17:11:24 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载字典缓存数据成功
2023-03-22 17:11:26 [SpringApplicationShutdownHook] INFO  c.r.f.manager.ShutdownManager - ====关闭后台任务任务线程池====
2023-03-22 17:11:26 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2023-03-22 17:11:26 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2023-03-22 17:11:26 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-03-22 17:11:26 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2023-03-22 17:16:01 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.zhonghe.generator.test.TestDemo], using SpringBootContextLoader
2023-03-22 17:16:01 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.zhonghe.generator.test.TestDemo]: no resource found for suffixes {-context.xml, Context.groovy}.
2023-03-22 17:16:01 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2023-03-22 17:16:01 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@ed3068a, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@7c2b6087, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@3fffff43, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@a8e6492, org.springframework.test.context.support.DependencyInjectionTestExecutionListener@1c7fd41f, org.springframework.test.context.support.DirtiesContextTestExecutionListener@3b77a04f, org.springframework.test.context.transaction.TransactionalTestExecutionListener@7b324585, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@2e11485, org.springframework.test.context.event.EventPublishingTestExecutionListener@60dce7ea, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@662f5666]
2023-03-22 17:16:02 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2023-03-22 17:16:02 [main] INFO  com.zhonghe.generator.test.TestDemo - Starting TestDemo using Java 11.0.18 on lpgMacBook-Pro.lan with PID 7844 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator)
2023-03-22 17:16:02 [main] INFO  com.zhonghe.generator.test.TestDemo - The following 1 profile is active: "dev"
2023-03-22 17:16:03 [main] INFO  c.r.framework.config.JacksonConfig - 初始化 jackson 配置
2023-03-22 17:16:03 [main] INFO  c.ruoyi.framework.config.RedisConfig - 初始化 redis 配置
2023-03-22 17:16:03 [main] INFO  org.redisson.Version - Redisson 3.17.4
2023-03-22 17:16:04 [redisson-netty-2-5] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2023-03-22 17:16:04 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2023-03-22 17:16:04 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2023-03-22 17:16:04 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-03-22 17:16:04 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2023-03-22 17:16:04 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-03-22 17:16:05 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2023-03-22 17:16:06 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2023-03-22 17:16:06 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2023-03-22 17:16:06 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2023-03-22 17:16:09 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2023-03-22 17:16:10 [main] INFO  com.zhonghe.generator.test.TestDemo - Started TestDemo in 8.602 seconds (JVM running for 9.154)
2023-03-22 17:16:10 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => minio
2023-03-22 17:16:10 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qiniu
2023-03-22 17:16:10 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => image
2023-03-22 17:16:10 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qcloud
2023-03-22 17:16:10 [main] INFO  factory.com.zhonghe.oss.OssFactory - 初始化OSS工厂
2023-03-22 17:16:10 [main] INFO  c.r.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2023-03-22 17:16:10 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载参数缓存数据成功
2023-03-22 17:16:11 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载字典缓存数据成功
2023-03-22 17:16:12 [SpringApplicationShutdownHook] INFO  c.r.f.manager.ShutdownManager - ====关闭后台任务任务线程池====
2023-03-22 17:16:12 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2023-03-22 17:16:12 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2023-03-22 17:16:12 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-03-22 17:16:12 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2023-03-22 17:23:27 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.zhonghe.generator.test.TestDemo], using SpringBootContextLoader
2023-03-22 17:23:27 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.zhonghe.generator.test.TestDemo]: no resource found for suffixes {-context.xml, Context.groovy}.
2023-03-22 17:23:27 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2023-03-22 17:23:27 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@ed3068a, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@7c2b6087, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@3fffff43, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@a8e6492, org.springframework.test.context.support.DependencyInjectionTestExecutionListener@1c7fd41f, org.springframework.test.context.support.DirtiesContextTestExecutionListener@3b77a04f, org.springframework.test.context.transaction.TransactionalTestExecutionListener@7b324585, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@2e11485, org.springframework.test.context.event.EventPublishingTestExecutionListener@60dce7ea, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@662f5666]
2023-03-22 17:23:27 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2023-03-22 17:23:27 [main] INFO  com.zhonghe.generator.test.TestDemo - Starting TestDemo using Java 11.0.18 on lpgMacBook-Pro.lan with PID 8022 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator)
2023-03-22 17:23:27 [main] INFO  com.zhonghe.generator.test.TestDemo - The following 1 profile is active: "dev"
2023-03-22 17:23:28 [main] INFO  c.r.framework.config.JacksonConfig - 初始化 jackson 配置
2023-03-22 17:23:28 [main] INFO  c.ruoyi.framework.config.RedisConfig - 初始化 redis 配置
2023-03-22 17:23:29 [main] INFO  org.redisson.Version - Redisson 3.17.4
2023-03-22 17:23:29 [redisson-netty-2-5] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2023-03-22 17:23:29 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2023-03-22 17:23:30 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2023-03-22 17:23:30 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-03-22 17:23:30 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2023-03-22 17:23:30 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-03-22 17:23:30 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2023-03-22 17:23:31 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2023-03-22 17:23:31 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2023-03-22 17:23:31 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2023-03-22 17:23:31 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2023-03-22 17:23:31 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2023-03-22 17:23:31 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-03-22 17:23:31 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2023-03-22 17:23:31 [main] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2023-03-22 17:27:03 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.zhonghe.generator.test.TestDemo], using SpringBootContextLoader
2023-03-22 17:27:03 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.zhonghe.generator.test.TestDemo]: no resource found for suffixes {-context.xml, Context.groovy}.
2023-03-22 17:27:03 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2023-03-22 17:27:03 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@ed3068a, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@7c2b6087, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@3fffff43, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@a8e6492, org.springframework.test.context.support.DependencyInjectionTestExecutionListener@1c7fd41f, org.springframework.test.context.support.DirtiesContextTestExecutionListener@3b77a04f, org.springframework.test.context.transaction.TransactionalTestExecutionListener@7b324585, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@2e11485, org.springframework.test.context.event.EventPublishingTestExecutionListener@60dce7ea, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@662f5666]
2023-03-22 17:27:03 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2023-03-22 17:27:03 [main] INFO  com.zhonghe.generator.test.TestDemo - Starting TestDemo using Java 11.0.18 on lpgMacBook-Pro.lan with PID 8075 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator)
2023-03-22 17:27:03 [main] INFO  com.zhonghe.generator.test.TestDemo - The following 1 profile is active: "dev"
2023-03-22 17:27:05 [main] INFO  c.r.framework.config.JacksonConfig - 初始化 jackson 配置
2023-03-22 17:27:05 [main] INFO  c.ruoyi.framework.config.RedisConfig - 初始化 redis 配置
2023-03-22 17:27:05 [main] INFO  org.redisson.Version - Redisson 3.17.4
2023-03-22 17:27:05 [redisson-netty-2-3] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2023-03-22 17:27:06 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2023-03-22 17:27:06 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2023-03-22 17:27:06 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-03-22 17:27:06 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2023-03-22 17:27:06 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-03-22 17:27:06 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2023-03-22 17:27:08 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2023-03-22 17:27:08 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2023-03-22 17:27:08 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2023-03-22 17:27:11 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2023-03-22 17:27:12 [main] INFO  com.zhonghe.generator.test.TestDemo - Started TestDemo in 8.752 seconds (JVM running for 9.253)
2023-03-22 17:27:12 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => minio
2023-03-22 17:27:12 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qiniu
2023-03-22 17:27:12 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => image
2023-03-22 17:27:12 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qcloud
2023-03-22 17:27:12 [main] INFO  factory.com.zhonghe.oss.OssFactory - 初始化OSS工厂
2023-03-22 17:27:12 [main] INFO  c.r.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2023-03-22 17:27:12 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载参数缓存数据成功
2023-03-22 17:27:13 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载字典缓存数据成功
2023-03-22 17:27:14 [SpringApplicationShutdownHook] INFO  c.r.f.manager.ShutdownManager - ====关闭后台任务任务线程池====
2023-03-22 17:27:14 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2023-03-22 17:27:14 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2023-03-22 17:27:14 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-03-22 17:27:14 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
