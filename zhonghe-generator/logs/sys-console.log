2023-03-22 15:18:04 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.zhonghe.generator.test.TestDemo], using SpringBootContextLoader
2023-03-22 15:18:04 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.zhonghe.generator.test.TestDemo]: no resource found for suffixes {-context.xml, Context.groovy}.
2023-03-22 15:18:04 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2023-03-22 15:18:04 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@5f031ebd, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@4ee37ca3, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@45c8d09f, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@53812a9b, org.springframework.test.context.support.DependencyInjectionTestExecutionListener@14b030a0, org.springframework.test.context.support.DirtiesContextTestExecutionListener@41e350f1, org.springframework.test.context.transaction.TransactionalTestExecutionListener@422c3c7a, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@18230356, org.springframework.test.context.event.EventPublishingTestExecutionListener@d8305c2, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@56bca85b]
2023-03-22 15:18:04 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2023-03-22 15:18:04 [main] INFO  com.zhonghe.generator.test.TestDemo - Starting TestDemo using Java 11.0.18 on lpgMacBook-Pro.lan with PID 5925 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator)
2023-03-22 15:18:04 [main] INFO  com.zhonghe.generator.test.TestDemo - The following 1 profile is active: "dev"
2023-03-22 15:18:05 [main] INFO  c.r.framework.config.JacksonConfig - 初始化 jackson 配置
2023-03-22 15:18:06 [main] INFO  c.ruoyi.framework.config.RedisConfig - 初始化 redis 配置
2023-03-22 15:18:06 [main] INFO  org.redisson.Version - Redisson 3.17.4
2023-03-22 15:18:06 [redisson-netty-2-6] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2023-03-22 15:18:06 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2023-03-22 15:18:07 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2023-03-22 15:18:07 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-03-22 15:18:07 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2023-03-22 15:18:07 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-03-22 15:18:07 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2023-03-22 15:18:08 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2023-03-22 15:18:08 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2023-03-22 15:18:08 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2023-03-22 15:18:11 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2023-03-22 15:18:13 [main] INFO  com.zhonghe.generator.test.TestDemo - Started TestDemo in 9.114 seconds (JVM running for 9.72)
2023-03-22 15:18:13 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qiniu
2023-03-22 15:18:13 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => image
2023-03-22 15:18:13 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => minio
2023-03-22 15:18:13 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qcloud
2023-03-22 15:18:13 [main] INFO  factory.com.zhonghe.oss.OssFactory - 初始化OSS工厂
2023-03-22 15:18:13 [main] INFO  c.r.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2023-03-22 15:18:13 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载参数缓存数据成功
2023-03-22 15:18:13 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载字典缓存数据成功
2023-03-22 15:18:13 [registrationTask1] WARN  d.c.b.a.c.r.ApplicationRegistrator - Failed to register application as Application(name=ZhongHe-Vue-Plus, managementUrl=http://**************:61486/actuator, healthUrl=http://**************:61486/actuator/health, serviceUrl=http://**************:61486/) at spring-boot-admin ([http://localhost:29050/admin/instances]): I/O error on POST request for "http://localhost:29050/admin/instances": Connect to localhost:29050 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused (Connection refused); nested exception is org.apache.http.conn.HttpHostConnectException: Connect to localhost:29050 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused (Connection refused). Further attempts are logged on DEBUG level
2023-03-22 15:18:15 [SpringApplicationShutdownHook] INFO  c.r.f.manager.ShutdownManager - ====关闭后台任务任务线程池====
2023-03-22 15:18:15 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2023-03-22 15:18:15 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2023-03-22 15:18:15 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-03-22 15:18:15 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2023-03-22 15:27:53 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.zhonghe.generator.test.TestDemo], using SpringBootContextLoader
2023-03-22 15:27:53 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.zhonghe.generator.test.TestDemo]: no resource found for suffixes {-context.xml, Context.groovy}.
2023-03-22 15:27:53 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2023-03-22 15:27:53 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@ed3068a, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@7c2b6087, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@3fffff43, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@a8e6492, org.springframework.test.context.support.DependencyInjectionTestExecutionListener@1c7fd41f, org.springframework.test.context.support.DirtiesContextTestExecutionListener@3b77a04f, org.springframework.test.context.transaction.TransactionalTestExecutionListener@7b324585, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@2e11485, org.springframework.test.context.event.EventPublishingTestExecutionListener@60dce7ea, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@662f5666]
2023-03-22 15:27:53 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2023-03-22 15:27:53 [main] INFO  com.zhonghe.generator.test.TestDemo - Starting TestDemo using Java 11.0.18 on lpgMacBook-Pro.lan with PID 6013 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator)
2023-03-22 15:27:53 [main] INFO  com.zhonghe.generator.test.TestDemo - The following 1 profile is active: "dev"
2023-03-22 15:27:55 [main] INFO  c.r.framework.config.JacksonConfig - 初始化 jackson 配置
2023-03-22 15:27:55 [main] INFO  c.ruoyi.framework.config.RedisConfig - 初始化 redis 配置
2023-03-22 15:27:55 [main] INFO  org.redisson.Version - Redisson 3.17.4
2023-03-22 15:27:55 [redisson-netty-2-3] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2023-03-22 15:27:55 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2023-03-22 15:27:56 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2023-03-22 15:27:56 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-03-22 15:27:56 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2023-03-22 15:27:56 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-03-22 15:27:56 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2023-03-22 15:27:57 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2023-03-22 15:27:57 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2023-03-22 15:27:57 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2023-03-22 15:28:00 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2023-03-22 15:28:02 [main] INFO  com.zhonghe.generator.test.TestDemo - Started TestDemo in 8.885 seconds (JVM running for 9.405)
2023-03-22 15:28:02 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qiniu
2023-03-22 15:28:02 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => image
2023-03-22 15:28:02 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => minio
2023-03-22 15:28:02 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qcloud
2023-03-22 15:28:02 [main] INFO  factory.com.zhonghe.oss.OssFactory - 初始化OSS工厂
2023-03-22 15:28:02 [main] INFO  c.r.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2023-03-22 15:28:02 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载参数缓存数据成功
2023-03-22 15:28:02 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载字典缓存数据成功
2023-03-22 15:28:02 [registrationTask1] WARN  d.c.b.a.c.r.ApplicationRegistrator - Failed to register application as Application(name=ZhongHe-Vue-Plus, managementUrl=http://**************:62248/actuator, healthUrl=http://**************:62248/actuator/health, serviceUrl=http://**************:62248/) at spring-boot-admin ([http://localhost:29050/admin/instances]): I/O error on POST request for "http://localhost:29050/admin/instances": Connect to localhost:29050 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused (Connection refused); nested exception is org.apache.http.conn.HttpHostConnectException: Connect to localhost:29050 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused (Connection refused). Further attempts are logged on DEBUG level
2023-03-22 15:28:04 [SpringApplicationShutdownHook] INFO  c.r.f.manager.ShutdownManager - ====关闭后台任务任务线程池====
2023-03-22 15:28:04 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2023-03-22 15:28:04 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2023-03-22 15:28:04 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-03-22 15:28:04 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2023-03-22 15:28:56 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.zhonghe.generator.test.TestDemo], using SpringBootContextLoader
2023-03-22 15:28:56 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.zhonghe.generator.test.TestDemo]: no resource found for suffixes {-context.xml, Context.groovy}.
2023-03-22 15:28:56 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2023-03-22 15:28:56 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@609640d5, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@79da1ec0, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@19fb8826, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@192d74fb, org.springframework.test.context.support.DependencyInjectionTestExecutionListener@4bef0fe3, org.springframework.test.context.support.DirtiesContextTestExecutionListener@62ea3440, org.springframework.test.context.transaction.TransactionalTestExecutionListener@27953a83, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@556d0826, org.springframework.test.context.event.EventPublishingTestExecutionListener@66ce957f, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@55b5f5d2]
2023-03-22 15:28:57 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2023-03-22 15:28:57 [main] INFO  com.zhonghe.generator.test.TestDemo - Starting TestDemo using Java 11.0.18 on lpgMacBook-Pro.lan with PID 6031 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator)
2023-03-22 15:28:57 [main] INFO  com.zhonghe.generator.test.TestDemo - The following 1 profile is active: "dev"
2023-03-22 15:28:58 [main] INFO  c.r.framework.config.JacksonConfig - 初始化 jackson 配置
2023-03-22 15:28:59 [main] INFO  c.ruoyi.framework.config.RedisConfig - 初始化 redis 配置
2023-03-22 15:28:59 [main] INFO  org.redisson.Version - Redisson 3.17.4
2023-03-22 15:28:59 [redisson-netty-2-3] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2023-03-22 15:28:59 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2023-03-22 15:29:00 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2023-03-22 15:29:00 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-03-22 15:29:00 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2023-03-22 15:29:00 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-03-22 15:29:00 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2023-03-22 15:29:02 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2023-03-22 15:29:02 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2023-03-22 15:29:02 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2023-03-22 15:29:05 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2023-03-22 15:29:06 [main] INFO  com.zhonghe.generator.test.TestDemo - Started TestDemo in 9.886 seconds (JVM running for 10.99)
2023-03-22 15:29:06 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qiniu
2023-03-22 15:29:06 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => image
2023-03-22 15:29:06 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => minio
2023-03-22 15:29:07 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qcloud
2023-03-22 15:29:07 [main] INFO  factory.com.zhonghe.oss.OssFactory - 初始化OSS工厂
2023-03-22 15:29:07 [main] INFO  c.r.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2023-03-22 15:29:07 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载参数缓存数据成功
2023-03-22 15:29:07 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载字典缓存数据成功
2023-03-22 15:29:07 [registrationTask1] WARN  d.c.b.a.c.r.ApplicationRegistrator - Failed to register application as Application(name=ZhongHe-Vue-Plus, managementUrl=http://**************:62312/actuator, healthUrl=http://**************:62312/actuator/health, serviceUrl=http://**************:62312/) at spring-boot-admin ([http://localhost:29050/admin/instances]): I/O error on POST request for "http://localhost:29050/admin/instances": Connect to localhost:29050 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused (Connection refused); nested exception is org.apache.http.conn.HttpHostConnectException: Connect to localhost:29050 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused (Connection refused). Further attempts are logged on DEBUG level
2023-03-22 15:29:08 [SpringApplicationShutdownHook] INFO  c.r.f.manager.ShutdownManager - ====关闭后台任务任务线程池====
2023-03-22 15:29:08 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2023-03-22 15:29:08 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2023-03-22 15:29:08 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-03-22 15:29:08 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2023-03-22 15:29:50 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.zhonghe.generator.test.TestDemo], using SpringBootContextLoader
2023-03-22 15:29:50 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.zhonghe.generator.test.TestDemo]: no resource found for suffixes {-context.xml, Context.groovy}.
2023-03-22 15:29:50 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2023-03-22 15:29:50 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@609640d5, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@79da1ec0, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@19fb8826, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@192d74fb, org.springframework.test.context.support.DependencyInjectionTestExecutionListener@4bef0fe3, org.springframework.test.context.support.DirtiesContextTestExecutionListener@62ea3440, org.springframework.test.context.transaction.TransactionalTestExecutionListener@27953a83, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@556d0826, org.springframework.test.context.event.EventPublishingTestExecutionListener@66ce957f, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@55b5f5d2]
2023-03-22 15:29:50 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2023-03-22 15:29:50 [main] INFO  com.zhonghe.generator.test.TestDemo - Starting TestDemo using Java 11.0.18 on lpgMacBook-Pro.lan with PID 6053 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator)
2023-03-22 15:29:50 [main] INFO  com.zhonghe.generator.test.TestDemo - The following 1 profile is active: "dev"
2023-03-22 15:29:52 [main] INFO  c.r.framework.config.JacksonConfig - 初始化 jackson 配置
2023-03-22 15:29:52 [main] INFO  c.ruoyi.framework.config.RedisConfig - 初始化 redis 配置
2023-03-22 15:29:52 [main] INFO  org.redisson.Version - Redisson 3.17.4
2023-03-22 15:29:53 [redisson-netty-2-8] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2023-03-22 15:29:53 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2023-03-22 15:29:53 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2023-03-22 15:29:53 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-03-22 15:29:53 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2023-03-22 15:29:53 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-03-22 15:29:54 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2023-03-22 15:29:55 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2023-03-22 15:29:55 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2023-03-22 15:29:55 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2023-03-22 15:29:58 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2023-03-22 15:29:59 [main] INFO  com.zhonghe.generator.test.TestDemo - Started TestDemo in 9.515 seconds (JVM running for 10.358)
2023-03-22 15:30:00 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qiniu
2023-03-22 15:30:00 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => image
2023-03-22 15:30:00 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => minio
2023-03-22 15:30:00 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qcloud
2023-03-22 15:30:00 [main] INFO  factory.com.zhonghe.oss.OssFactory - 初始化OSS工厂
2023-03-22 15:30:00 [main] INFO  c.r.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2023-03-22 15:30:00 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载参数缓存数据成功
2023-03-22 15:30:00 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载字典缓存数据成功
2023-03-22 15:30:00 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:30:00 [registrationTask1] WARN  d.c.b.a.c.r.ApplicationRegistrator - Failed to register application as Application(name=ZhongHe-Vue-Plus, managementUrl=http://**************:62386/actuator, healthUrl=http://**************:62386/actuator/health, serviceUrl=http://**************:62386/) at spring-boot-admin ([http://localhost:29050/admin/instances]): I/O error on POST request for "http://localhost:29050/admin/instances": Connect to localhost:29050 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused (Connection refused); nested exception is org.apache.http.conn.HttpHostConnectException: Connect to localhost:29050 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused (Connection refused). Further attempts are logged on DEBUG level
2023-03-22 15:30:00 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:30:00 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:30:00 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:30:00 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:30:00 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:30:00 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:30:00 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:30:00 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:30:00 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:30:00 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:30:00 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:30:00 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:30:00 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:30:00 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:30:00 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:30:00 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:30:00 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:30:00 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:30:00 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:30:00 [schedule-pool-1] WARN  o.s.s.a.AnnotationAsyncExecutionInterceptor - Exception handler for async method 'public byte[] service.com.zhonghe.generator.GenTableServiceImpl.downloadCode(java.lang.String)' threw unexpected exception itself
exception.com.zhonghe.common.ServiceException: Exception message -
### Error querying database.  Cause: org.postgresql.util.PSQLException: ERROR: operator does not exist: bigint = character varying
  建议：No operator matches the given name and argument types. You might need to add explicit type casts.
  位置：567
### The error may exist in file [/Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator/target/classes/mapper/generator/GenTableMapper.xml]
### The error may involve mapper.com.zhonghe.generator.GenTableMapper.selectGenTableByName-Inline
### The error occurred while setting parameters
### SQL: SELECT t.table_id, t.table_name, t.table_comment, t.sub_table_name, t.sub_table_fk_name, t.class_name, t.tpl_category, t.package_name, t.module_name, t.business_name, t.function_name, t.function_author, t.gen_type, t.gen_path, t.options, t.remark,                c.column_id, c.column_name, c.column_comment, c.column_type, c.java_type, c.java_field, c.is_pk, c.is_increment, c.is_required, c.is_insert, c.is_edit, c.is_list, c.is_query, c.query_type, c.html_type, c.dict_type, c.sort         FROM gen_table t              LEFT JOIN gen_table_column c ON t.table_id = c.table_id         where t.table_name = ? order by c.sort
### Cause: org.postgresql.util.PSQLException: ERROR: operator does not exist: bigint = character varying
  建议：No operator matches the given name and argument types. You might need to add explicit type casts.
  位置：567
; bad SQL grammar []; nested exception is org.postgresql.util.PSQLException: ERROR: operator does not exist: bigint = character varying
  建议：No operator matches the given name and argument types. You might need to add explicit type casts.
  位置：567, Method name - downloadCode, Parameter value - [sys_user]
	at config.com.zhonghe.framework.AsyncConfig.lambda$getAsyncUncaughtExceptionHandler$0(AsyncConfig.java:50)
	at org.springframework.aop.interceptor.AsyncExecutionAspectSupport.handleError(AsyncExecutionAspectSupport.java:316)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:124)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:834)
2023-03-22 15:30:01 [SpringApplicationShutdownHook] INFO  c.r.f.manager.ShutdownManager - ====关闭后台任务任务线程池====
2023-03-22 15:30:01 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2023-03-22 15:30:02 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2023-03-22 15:30:02 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-03-22 15:30:02 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2023-03-22 15:31:38 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.zhonghe.generator.test.TestDemo], using SpringBootContextLoader
2023-03-22 15:31:38 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.zhonghe.generator.test.TestDemo]: no resource found for suffixes {-context.xml, Context.groovy}.
2023-03-22 15:31:38 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2023-03-22 15:31:38 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@ed3068a, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@7c2b6087, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@3fffff43, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@a8e6492, org.springframework.test.context.support.DependencyInjectionTestExecutionListener@1c7fd41f, org.springframework.test.context.support.DirtiesContextTestExecutionListener@3b77a04f, org.springframework.test.context.transaction.TransactionalTestExecutionListener@7b324585, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@2e11485, org.springframework.test.context.event.EventPublishingTestExecutionListener@60dce7ea, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@662f5666]
2023-03-22 15:31:38 [main] INFO  com.zhonghe.generator.test.TestDemo - Starting TestDemo using Java 11.0.18 on lpgMacBook-Pro.lan with PID 6092 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator)
2023-03-22 15:31:38 [main] INFO  com.zhonghe.generator.test.TestDemo - The following 1 profile is active: "dev"
2023-03-22 15:31:38 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2023-03-22 15:31:40 [main] INFO  c.r.framework.config.JacksonConfig - 初始化 jackson 配置
2023-03-22 15:31:40 [main] INFO  c.ruoyi.framework.config.RedisConfig - 初始化 redis 配置
2023-03-22 15:31:40 [main] INFO  org.redisson.Version - Redisson 3.17.4
2023-03-22 15:31:41 [redisson-netty-2-5] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2023-03-22 15:31:41 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2023-03-22 15:31:41 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2023-03-22 15:31:41 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-03-22 15:31:41 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2023-03-22 15:31:41 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-03-22 15:31:41 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2023-03-22 15:31:43 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2023-03-22 15:31:43 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2023-03-22 15:31:43 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2023-03-22 15:31:46 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2023-03-22 15:31:47 [main] INFO  com.zhonghe.generator.test.TestDemo - Started TestDemo in 8.801 seconds (JVM running for 9.428)
2023-03-22 15:31:47 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qiniu
2023-03-22 15:31:47 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => image
2023-03-22 15:31:47 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => minio
2023-03-22 15:31:47 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qcloud
2023-03-22 15:31:47 [main] INFO  factory.com.zhonghe.oss.OssFactory - 初始化OSS工厂
2023-03-22 15:31:47 [main] INFO  c.r.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2023-03-22 15:31:47 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载参数缓存数据成功
2023-03-22 15:31:48 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载字典缓存数据成功
2023-03-22 15:31:48 [registrationTask1] WARN  d.c.b.a.c.r.ApplicationRegistrator - Failed to register application as Application(name=ZhongHe-Vue-Plus, managementUrl=http://**************:62514/actuator, healthUrl=http://**************:62514/actuator/health, serviceUrl=http://**************:62514/) at spring-boot-admin ([http://localhost:29050/admin/instances]): I/O error on POST request for "http://localhost:29050/admin/instances": Connect to localhost:29050 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused (Connection refused); nested exception is org.apache.http.conn.HttpHostConnectException: Connect to localhost:29050 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused (Connection refused). Further attempts are logged on DEBUG level
2023-03-22 15:31:48 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:31:48 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:31:48 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:31:48 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:31:48 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:31:48 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:31:48 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:31:48 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:31:48 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:31:48 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:31:48 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:31:48 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:31:48 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:31:48 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:31:48 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:31:48 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:31:48 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:31:48 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:31:48 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:31:48 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:31:48 [schedule-pool-1] WARN  o.s.s.a.AnnotationAsyncExecutionInterceptor - Exception handler for async method 'public byte[] service.com.zhonghe.generator.GenTableServiceImpl.downloadCode(java.lang.String)' threw unexpected exception itself
exception.com.zhonghe.common.ServiceException: Exception message -
### Error querying database.  Cause: org.postgresql.util.PSQLException: ERROR: operator does not exist: bigint = character varying
  建议：No operator matches the given name and argument types. You might need to add explicit type casts.
  位置：567
### The error may exist in file [/Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator/target/classes/mapper/generator/GenTableMapper.xml]
### The error may involve mapper.com.zhonghe.generator.GenTableMapper.selectGenTableByName-Inline
### The error occurred while setting parameters
### SQL: SELECT t.table_id, t.table_name, t.table_comment, t.sub_table_name, t.sub_table_fk_name, t.class_name, t.tpl_category, t.package_name, t.module_name, t.business_name, t.function_name, t.function_author, t.gen_type, t.gen_path, t.options, t.remark,                c.column_id, c.column_name, c.column_comment, c.column_type, c.java_type, c.java_field, c.is_pk, c.is_increment, c.is_required, c.is_insert, c.is_edit, c.is_list, c.is_query, c.query_type, c.html_type, c.dict_type, c.sort         FROM gen_table t              LEFT JOIN gen_table_column c ON t.table_id = c.table_id         where t.table_name = ? order by c.sort
### Cause: org.postgresql.util.PSQLException: ERROR: operator does not exist: bigint = character varying
  建议：No operator matches the given name and argument types. You might need to add explicit type casts.
  位置：567
; bad SQL grammar []; nested exception is org.postgresql.util.PSQLException: ERROR: operator does not exist: bigint = character varying
  建议：No operator matches the given name and argument types. You might need to add explicit type casts.
  位置：567, Method name - downloadCode, Parameter value - [sys_user]
	at config.com.zhonghe.framework.AsyncConfig.lambda$getAsyncUncaughtExceptionHandler$0(AsyncConfig.java:50)
	at org.springframework.aop.interceptor.AsyncExecutionAspectSupport.handleError(AsyncExecutionAspectSupport.java:316)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:124)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:834)
2023-03-22 15:31:49 [SpringApplicationShutdownHook] INFO  c.r.f.manager.ShutdownManager - ====关闭后台任务任务线程池====
2023-03-22 15:31:49 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2023-03-22 15:31:49 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2023-03-22 15:31:49 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-03-22 15:31:49 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2023-03-22 15:33:41 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.zhonghe.generator.test.TestDemo], using SpringBootContextLoader
2023-03-22 15:33:41 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.zhonghe.generator.test.TestDemo]: no resource found for suffixes {-context.xml, Context.groovy}.
2023-03-22 15:33:41 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2023-03-22 15:33:41 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@ed3068a, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@7c2b6087, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@3fffff43, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@a8e6492, org.springframework.test.context.support.DependencyInjectionTestExecutionListener@1c7fd41f, org.springframework.test.context.support.DirtiesContextTestExecutionListener@3b77a04f, org.springframework.test.context.transaction.TransactionalTestExecutionListener@7b324585, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@2e11485, org.springframework.test.context.event.EventPublishingTestExecutionListener@60dce7ea, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@662f5666]
2023-03-22 15:33:42 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2023-03-22 15:33:42 [main] INFO  com.zhonghe.generator.test.TestDemo - Starting TestDemo using Java 11.0.18 on lpgMacBook-Pro.lan with PID 6124 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator)
2023-03-22 15:33:42 [main] INFO  com.zhonghe.generator.test.TestDemo - The following 1 profile is active: "dev"
2023-03-22 15:33:43 [main] INFO  c.r.framework.config.JacksonConfig - 初始化 jackson 配置
2023-03-22 15:33:43 [main] INFO  c.ruoyi.framework.config.RedisConfig - 初始化 redis 配置
2023-03-22 15:33:43 [main] INFO  org.redisson.Version - Redisson 3.17.4
2023-03-22 15:33:44 [redisson-netty-2-5] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2023-03-22 15:33:44 [redisson-netty-2-2] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2023-03-22 15:33:44 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2023-03-22 15:33:44 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-03-22 15:33:44 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2023-03-22 15:33:44 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-03-22 15:33:45 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2023-03-22 15:33:46 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2023-03-22 15:33:46 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2023-03-22 15:33:46 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2023-03-22 15:33:49 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2023-03-22 15:33:50 [main] INFO  com.zhonghe.generator.test.TestDemo - Started TestDemo in 8.823 seconds (JVM running for 9.395)
2023-03-22 15:33:50 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qiniu
2023-03-22 15:33:51 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => image
2023-03-22 15:33:51 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => minio
2023-03-22 15:33:51 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qcloud
2023-03-22 15:33:51 [main] INFO  factory.com.zhonghe.oss.OssFactory - 初始化OSS工厂
2023-03-22 15:33:51 [main] INFO  c.r.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2023-03-22 15:33:51 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载参数缓存数据成功
2023-03-22 15:33:52 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载字典缓存数据成功
2023-03-22 15:33:52 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:33:52 [registrationTask1] WARN  d.c.b.a.c.r.ApplicationRegistrator - Failed to register application as Application(name=ZhongHe-Vue-Plus, managementUrl=http://**************:62863/actuator, healthUrl=http://**************:62863/actuator/health, serviceUrl=http://**************:62863/) at spring-boot-admin ([http://localhost:29050/admin/instances]): I/O error on POST request for "http://localhost:29050/admin/instances": Connect to localhost:29050 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused (Connection refused); nested exception is org.apache.http.conn.HttpHostConnectException: Connect to localhost:29050 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused (Connection refused). Further attempts are logged on DEBUG level
2023-03-22 15:33:52 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:33:52 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:33:52 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:33:52 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:33:52 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:33:52 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:33:52 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:33:52 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:33:52 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:33:52 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:33:52 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:33:52 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:33:52 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:33:52 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:33:52 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:33:52 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:33:52 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:33:52 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:33:52 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:33:52 [schedule-pool-1] WARN  o.s.s.a.AnnotationAsyncExecutionInterceptor - Exception handler for async method 'public byte[] service.com.zhonghe.generator.GenTableServiceImpl.downloadCode(java.lang.String)' threw unexpected exception itself
exception.com.zhonghe.common.ServiceException: Exception message -
### Error querying database.  Cause: org.postgresql.util.PSQLException: ERROR: operator does not exist: bigint = character varying
  建议：No operator matches the given name and argument types. You might need to add explicit type casts.
  位置：567
### The error may exist in file [/Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator/target/classes/mapper/generator/GenTableMapper.xml]
### The error may involve mapper.com.zhonghe.generator.GenTableMapper.selectGenTableByName-Inline
### The error occurred while setting parameters
### SQL: SELECT t.table_id, t.table_name, t.table_comment, t.sub_table_name, t.sub_table_fk_name, t.class_name, t.tpl_category, t.package_name, t.module_name, t.business_name, t.function_name, t.function_author, t.gen_type, t.gen_path, t.options, t.remark,                c.column_id, c.column_name, c.column_comment, c.column_type, c.java_type, c.java_field, c.is_pk, c.is_increment, c.is_required, c.is_insert, c.is_edit, c.is_list, c.is_query, c.query_type, c.html_type, c.dict_type, c.sort         FROM gen_table t              LEFT JOIN gen_table_column c ON t.table_id = c.table_id         where t.table_name = ? order by c.sort
### Cause: org.postgresql.util.PSQLException: ERROR: operator does not exist: bigint = character varying
  建议：No operator matches the given name and argument types. You might need to add explicit type casts.
  位置：567
; bad SQL grammar []; nested exception is org.postgresql.util.PSQLException: ERROR: operator does not exist: bigint = character varying
  建议：No operator matches the given name and argument types. You might need to add explicit type casts.
  位置：567, Method name - downloadCode, Parameter value - [sys_user]
	at config.com.zhonghe.framework.AsyncConfig.lambda$getAsyncUncaughtExceptionHandler$0(AsyncConfig.java:50)
	at org.springframework.aop.interceptor.AsyncExecutionAspectSupport.handleError(AsyncExecutionAspectSupport.java:316)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:124)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java:264)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:834)
2023-03-22 15:33:53 [SpringApplicationShutdownHook] INFO  c.r.f.manager.ShutdownManager - ====关闭后台任务任务线程池====
2023-03-22 15:33:53 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2023-03-22 15:33:53 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2023-03-22 15:33:53 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-03-22 15:33:53 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2023-03-22 15:36:34 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.zhonghe.generator.test.TestDemo], using SpringBootContextLoader
2023-03-22 15:36:34 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.zhonghe.generator.test.TestDemo]: no resource found for suffixes {-context.xml, Context.groovy}.
2023-03-22 15:36:34 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2023-03-22 15:36:34 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@609640d5, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@79da1ec0, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@19fb8826, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@192d74fb, org.springframework.test.context.support.DependencyInjectionTestExecutionListener@4bef0fe3, org.springframework.test.context.support.DirtiesContextTestExecutionListener@62ea3440, org.springframework.test.context.transaction.TransactionalTestExecutionListener@27953a83, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@556d0826, org.springframework.test.context.event.EventPublishingTestExecutionListener@66ce957f, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@55b5f5d2]
2023-03-22 15:36:35 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2023-03-22 15:36:35 [main] INFO  com.zhonghe.generator.test.TestDemo - Starting TestDemo using Java 11.0.18 on lpgMacBook-Pro.lan with PID 6161 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator)
2023-03-22 15:36:35 [main] INFO  com.zhonghe.generator.test.TestDemo - The following 1 profile is active: "dev"
2023-03-22 15:36:36 [main] INFO  c.r.framework.config.JacksonConfig - 初始化 jackson 配置
2023-03-22 15:36:36 [main] INFO  c.ruoyi.framework.config.RedisConfig - 初始化 redis 配置
2023-03-22 15:36:36 [main] INFO  org.redisson.Version - Redisson 3.17.4
2023-03-22 15:36:37 [redisson-netty-2-8] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2023-03-22 15:36:37 [redisson-netty-2-2] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2023-03-22 15:36:37 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2023-03-22 15:36:37 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-03-22 15:36:37 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2023-03-22 15:36:37 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-03-22 15:36:38 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2023-03-22 15:36:39 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2023-03-22 15:36:39 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2023-03-22 15:36:39 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2023-03-22 15:36:43 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2023-03-22 15:36:44 [main] INFO  com.zhonghe.generator.test.TestDemo - Started TestDemo in 9.82 seconds (JVM running for 10.675)
2023-03-22 15:36:44 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qiniu
2023-03-22 15:36:44 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => image
2023-03-22 15:36:44 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => minio
2023-03-22 15:36:44 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qcloud
2023-03-22 15:36:44 [main] INFO  factory.com.zhonghe.oss.OssFactory - 初始化OSS工厂
2023-03-22 15:36:45 [main] INFO  c.r.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2023-03-22 15:36:45 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载参数缓存数据成功
2023-03-22 15:36:45 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载字典缓存数据成功
2023-03-22 15:36:45 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:36:45 [registrationTask1] WARN  d.c.b.a.c.r.ApplicationRegistrator - Failed to register application as Application(name=ZhongHe-Vue-Plus, managementUrl=http://**************:63031/actuator, healthUrl=http://**************:63031/actuator/health, serviceUrl=http://**************:63031/) at spring-boot-admin ([http://localhost:29050/admin/instances]): I/O error on POST request for "http://localhost:29050/admin/instances": Connect to localhost:29050 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused (Connection refused); nested exception is org.apache.http.conn.HttpHostConnectException: Connect to localhost:29050 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused (Connection refused). Further attempts are logged on DEBUG level
2023-03-22 15:36:45 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:36:45 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:36:45 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:36:45 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:36:45 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:36:45 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:36:45 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:36:45 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:36:45 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:36:45 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:36:45 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:36:45 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:36:45 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:36:45 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:36:45 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:36:45 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:36:45 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:36:45 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:36:45 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:36:51 [schedule-pool-1] WARN  o.s.s.a.AnnotationAsyncExecutionInterceptor - Exception handler for async method 'public byte[] service.com.zhonghe.generator.GenTableServiceImpl.downloadCode(java.lang.String)' threw unexpected exception itself
exception.com.zhonghe.common.ServiceException: Exception message -
### Error querying database.  Cause: org.postgresql.util.PSQLException: ERROR: operator does not exist: bigint = character varying
  建议：No operator matches the given name and argument types. You might need to add explicit type casts.
  位置：567
### The error may exist in file [/Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator/target/classes/mapper/generator/GenTableMapper.xml]
### The error may involve mapper.com.zhonghe.generator.GenTableMapper.selectGenTableByName-Inline
### The error occurred while setting parameters
### SQL: SELECT t.table_id, t.table_name, t.table_comment, t.sub_table_name, t.sub_table_fk_name, t.class_name, t.tpl_category, t.package_name, t.module_name, t.business_name, t.function_name, t.function_author, t.gen_type, t.gen_path, t.options, t.remark,                c.column_id, c.column_name, c.column_comment, c.column_type, c.java_type, c.java_field, c.is_pk, c.is_increment, c.is_required, c.is_insert, c.is_edit, c.is_list, c.is_query, c.query_type, c.html_type, c.dict_type, c.sort         FROM gen_table t              LEFT JOIN gen_table_column c ON t.table_id = c.table_id         where t.table_name = ? order by c.sort
### Cause: org.postgresql.util.PSQLException: ERROR: operator does not exist: bigint = character varying
  建议：No operator matches the given name and argument types. You might need to add explicit type casts.
  位置：567
; bad SQL grammar []; nested exception is org.postgresql.util.PSQLException: ERROR: operator does not exist: bigint = character varying
  建议：No operator matches the given name and argument types. You might need to add explicit type casts.
  位置：567, Method name - downloadCode, Parameter value - [sys_user]
	at config.com.zhonghe.framework.AsyncConfig.lambda$getAsyncUncaughtExceptionHandler$0(AsyncConfig.java:50)
	at org.springframework.aop.interceptor.AsyncExecutionAspectSupport.handleError(AsyncExecutionAspectSupport.java:316)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:124)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:834)
2023-03-22 15:36:52 [SpringApplicationShutdownHook] INFO  c.r.f.manager.ShutdownManager - ====关闭后台任务任务线程池====
2023-03-22 15:36:52 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2023-03-22 15:36:52 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2023-03-22 15:36:52 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-03-22 15:36:52 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2023-03-22 15:37:31 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.zhonghe.generator.test.TestDemo], using SpringBootContextLoader
2023-03-22 15:37:31 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.zhonghe.generator.test.TestDemo]: no resource found for suffixes {-context.xml, Context.groovy}.
2023-03-22 15:37:31 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2023-03-22 15:37:31 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@609640d5, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@79da1ec0, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@19fb8826, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@192d74fb, org.springframework.test.context.support.DependencyInjectionTestExecutionListener@4bef0fe3, org.springframework.test.context.support.DirtiesContextTestExecutionListener@62ea3440, org.springframework.test.context.transaction.TransactionalTestExecutionListener@27953a83, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@556d0826, org.springframework.test.context.event.EventPublishingTestExecutionListener@66ce957f, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@55b5f5d2]
2023-03-22 15:37:31 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2023-03-22 15:37:31 [main] INFO  com.zhonghe.generator.test.TestDemo - Starting TestDemo using Java 11.0.18 on lpgMacBook-Pro.lan with PID 6175 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator)
2023-03-22 15:37:31 [main] INFO  com.zhonghe.generator.test.TestDemo - The following 1 profile is active: "dev"
2023-03-22 15:37:33 [main] INFO  c.r.framework.config.JacksonConfig - 初始化 jackson 配置
2023-03-22 15:37:33 [main] INFO  c.ruoyi.framework.config.RedisConfig - 初始化 redis 配置
2023-03-22 15:37:33 [main] INFO  org.redisson.Version - Redisson 3.17.4
2023-03-22 15:37:34 [redisson-netty-2-4] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2023-03-22 15:37:34 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2023-03-22 15:37:34 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2023-03-22 15:37:34 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-03-22 15:37:34 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2023-03-22 15:37:35 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-03-22 15:37:35 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2023-03-22 15:37:36 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2023-03-22 15:37:36 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2023-03-22 15:37:36 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2023-03-22 15:37:39 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2023-03-22 15:37:41 [main] INFO  com.zhonghe.generator.test.TestDemo - Started TestDemo in 9.557 seconds (JVM running for 10.354)
2023-03-22 15:37:41 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qiniu
2023-03-22 15:37:41 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => image
2023-03-22 15:37:41 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => minio
2023-03-22 15:37:41 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qcloud
2023-03-22 15:37:41 [main] INFO  factory.com.zhonghe.oss.OssFactory - 初始化OSS工厂
2023-03-22 15:37:41 [main] INFO  c.r.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2023-03-22 15:37:41 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载参数缓存数据成功
2023-03-22 15:37:41 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载字典缓存数据成功
2023-03-22 15:37:41 [registrationTask1] WARN  d.c.b.a.c.r.ApplicationRegistrator - Failed to register application as Application(name=ZhongHe-Vue-Plus, managementUrl=http://**************:63097/actuator, healthUrl=http://**************:63097/actuator/health, serviceUrl=http://**************:63097/) at spring-boot-admin ([http://localhost:29050/admin/instances]): I/O error on POST request for "http://localhost:29050/admin/instances": Connect to localhost:29050 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused (Connection refused); nested exception is org.apache.http.conn.HttpHostConnectException: Connect to localhost:29050 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused (Connection refused). Further attempts are logged on DEBUG level
2023-03-22 15:37:41 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:37:41 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:37:41 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:37:41 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:37:41 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:37:41 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:37:41 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:37:41 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:37:41 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:37:41 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:37:41 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:37:41 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:37:41 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:37:41 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:37:41 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:37:41 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:37:41 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:37:41 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:37:41 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:37:41 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:37:55 [schedule-pool-1] WARN  o.s.s.a.AnnotationAsyncExecutionInterceptor - Exception handler for async method 'public byte[] service.com.zhonghe.generator.GenTableServiceImpl.downloadCode(java.lang.String)' threw unexpected exception itself
exception.com.zhonghe.common.ServiceException: Exception message -
### Error querying database.  Cause: org.postgresql.util.PSQLException: ERROR: operator does not exist: bigint = character varying
  建议：No operator matches the given name and argument types. You might need to add explicit type casts.
  位置：567
### The error may exist in file [/Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator/target/classes/mapper/generator/GenTableMapper.xml]
### The error may involve mapper.com.zhonghe.generator.GenTableMapper.selectGenTableByName-Inline
### The error occurred while setting parameters
### SQL: SELECT t.table_id, t.table_name, t.table_comment, t.sub_table_name, t.sub_table_fk_name, t.class_name, t.tpl_category, t.package_name, t.module_name, t.business_name, t.function_name, t.function_author, t.gen_type, t.gen_path, t.options, t.remark,                c.column_id, c.column_name, c.column_comment, c.column_type, c.java_type, c.java_field, c.is_pk, c.is_increment, c.is_required, c.is_insert, c.is_edit, c.is_list, c.is_query, c.query_type, c.html_type, c.dict_type, c.sort         FROM gen_table t              LEFT JOIN gen_table_column c ON t.table_id = c.table_id         where t.table_name = ? order by c.sort
### Cause: org.postgresql.util.PSQLException: ERROR: operator does not exist: bigint = character varying
  建议：No operator matches the given name and argument types. You might need to add explicit type casts.
  位置：567
; bad SQL grammar []; nested exception is org.postgresql.util.PSQLException: ERROR: operator does not exist: bigint = character varying
  建议：No operator matches the given name and argument types. You might need to add explicit type casts.
  位置：567, Method name - downloadCode, Parameter value - [sys_user]
	at config.com.zhonghe.framework.AsyncConfig.lambda$getAsyncUncaughtExceptionHandler$0(AsyncConfig.java:50)
	at org.springframework.aop.interceptor.AsyncExecutionAspectSupport.handleError(AsyncExecutionAspectSupport.java:316)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:124)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:834)
2023-03-22 15:37:56 [SpringApplicationShutdownHook] INFO  c.r.f.manager.ShutdownManager - ====关闭后台任务任务线程池====
2023-03-22 15:37:56 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2023-03-22 15:37:56 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2023-03-22 15:37:56 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-03-22 15:37:56 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2023-03-22 15:37:59 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.zhonghe.generator.test.TestDemo], using SpringBootContextLoader
2023-03-22 15:37:59 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.zhonghe.generator.test.TestDemo]: no resource found for suffixes {-context.xml, Context.groovy}.
2023-03-22 15:37:59 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2023-03-22 15:37:59 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@609640d5, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@79da1ec0, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@19fb8826, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@192d74fb, org.springframework.test.context.support.DependencyInjectionTestExecutionListener@4bef0fe3, org.springframework.test.context.support.DirtiesContextTestExecutionListener@62ea3440, org.springframework.test.context.transaction.TransactionalTestExecutionListener@27953a83, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@556d0826, org.springframework.test.context.event.EventPublishingTestExecutionListener@66ce957f, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@55b5f5d2]
2023-03-22 15:38:00 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2023-03-22 15:38:00 [main] INFO  com.zhonghe.generator.test.TestDemo - Starting TestDemo using Java 11.0.18 on lpgMacBook-Pro.lan with PID 6181 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator)
2023-03-22 15:38:00 [main] INFO  com.zhonghe.generator.test.TestDemo - The following 1 profile is active: "dev"
2023-03-22 15:38:01 [main] INFO  c.r.framework.config.JacksonConfig - 初始化 jackson 配置
2023-03-22 15:38:01 [main] INFO  c.ruoyi.framework.config.RedisConfig - 初始化 redis 配置
2023-03-22 15:38:01 [main] INFO  org.redisson.Version - Redisson 3.17.4
2023-03-22 15:38:02 [redisson-netty-2-5] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2023-03-22 15:38:02 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2023-03-22 15:38:02 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2023-03-22 15:38:02 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-03-22 15:38:02 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2023-03-22 15:38:02 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-03-22 15:38:03 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2023-03-22 15:38:04 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2023-03-22 15:38:04 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2023-03-22 15:38:04 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2023-03-22 15:38:08 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2023-03-22 15:38:09 [main] INFO  com.zhonghe.generator.test.TestDemo - Started TestDemo in 10.185 seconds (JVM running for 10.946)
2023-03-22 15:38:10 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qiniu
2023-03-22 15:38:10 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => image
2023-03-22 15:38:10 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => minio
2023-03-22 15:38:10 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qcloud
2023-03-22 15:38:10 [main] INFO  factory.com.zhonghe.oss.OssFactory - 初始化OSS工厂
2023-03-22 15:38:10 [main] INFO  c.r.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2023-03-22 15:38:10 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载参数缓存数据成功
2023-03-22 15:38:10 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载字典缓存数据成功
2023-03-22 15:38:10 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:38:10 [registrationTask1] WARN  d.c.b.a.c.r.ApplicationRegistrator - Failed to register application as Application(name=ZhongHe-Vue-Plus, managementUrl=http://**************:63142/actuator, healthUrl=http://**************:63142/actuator/health, serviceUrl=http://**************:63142/) at spring-boot-admin ([http://localhost:29050/admin/instances]): I/O error on POST request for "http://localhost:29050/admin/instances": Connect to localhost:29050 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused (Connection refused); nested exception is org.apache.http.conn.HttpHostConnectException: Connect to localhost:29050 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused (Connection refused). Further attempts are logged on DEBUG level
2023-03-22 15:38:10 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:38:10 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:38:10 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:38:10 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:38:10 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:38:10 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:38:10 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:38:10 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:38:10 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:38:10 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:38:10 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:38:10 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:38:10 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:38:10 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:38:10 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:38:10 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:38:10 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:38:10 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:38:10 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:38:33 [schedule-pool-1] WARN  o.s.s.a.AnnotationAsyncExecutionInterceptor - Exception handler for async method 'public byte[] service.com.zhonghe.generator.GenTableServiceImpl.downloadCode(java.lang.String)' threw unexpected exception itself
exception.com.zhonghe.common.ServiceException: Exception message -
### Error querying database.  Cause: org.postgresql.util.PSQLException: ERROR: operator does not exist: bigint = character varying
  建议：No operator matches the given name and argument types. You might need to add explicit type casts.
  位置：567
### The error may exist in file [/Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator/target/classes/mapper/generator/GenTableMapper.xml]
### The error may involve mapper.com.zhonghe.generator.GenTableMapper.selectGenTableByName-Inline
### The error occurred while setting parameters
### SQL: SELECT t.table_id, t.table_name, t.table_comment, t.sub_table_name, t.sub_table_fk_name, t.class_name, t.tpl_category, t.package_name, t.module_name, t.business_name, t.function_name, t.function_author, t.gen_type, t.gen_path, t.options, t.remark,                c.column_id, c.column_name, c.column_comment, c.column_type, c.java_type, c.java_field, c.is_pk, c.is_increment, c.is_required, c.is_insert, c.is_edit, c.is_list, c.is_query, c.query_type, c.html_type, c.dict_type, c.sort         FROM gen_table t              LEFT JOIN gen_table_column c ON t.table_id = c.table_id         where t.table_name = ? order by c.sort
### Cause: org.postgresql.util.PSQLException: ERROR: operator does not exist: bigint = character varying
  建议：No operator matches the given name and argument types. You might need to add explicit type casts.
  位置：567
; bad SQL grammar []; nested exception is org.postgresql.util.PSQLException: ERROR: operator does not exist: bigint = character varying
  建议：No operator matches the given name and argument types. You might need to add explicit type casts.
  位置：567, Method name - downloadCode, Parameter value - [sys_user]
	at config.com.zhonghe.framework.AsyncConfig.lambda$getAsyncUncaughtExceptionHandler$0(AsyncConfig.java:50)
	at org.springframework.aop.interceptor.AsyncExecutionAspectSupport.handleError(AsyncExecutionAspectSupport.java:316)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:124)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:834)
2023-03-22 15:38:33 [SpringApplicationShutdownHook] INFO  c.r.f.manager.ShutdownManager - ====关闭后台任务任务线程池====
2023-03-22 15:38:33 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2023-03-22 15:38:33 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2023-03-22 15:38:33 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-03-22 15:38:33 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2023-03-22 15:38:41 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.zhonghe.generator.test.TestDemo], using SpringBootContextLoader
2023-03-22 15:38:41 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.zhonghe.generator.test.TestDemo]: no resource found for suffixes {-context.xml, Context.groovy}.
2023-03-22 15:38:42 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2023-03-22 15:38:42 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@609640d5, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@79da1ec0, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@19fb8826, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@192d74fb, org.springframework.test.context.support.DependencyInjectionTestExecutionListener@4bef0fe3, org.springframework.test.context.support.DirtiesContextTestExecutionListener@62ea3440, org.springframework.test.context.transaction.TransactionalTestExecutionListener@27953a83, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@556d0826, org.springframework.test.context.event.EventPublishingTestExecutionListener@66ce957f, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@55b5f5d2]
2023-03-22 15:38:42 [main] INFO  com.zhonghe.generator.test.TestDemo - Starting TestDemo using Java 11.0.18 on lpgMacBook-Pro.lan with PID 6193 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator)
2023-03-22 15:38:42 [main] INFO  com.zhonghe.generator.test.TestDemo - The following 1 profile is active: "dev"
2023-03-22 15:38:42 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2023-03-22 15:38:44 [main] INFO  c.r.framework.config.JacksonConfig - 初始化 jackson 配置
2023-03-22 15:38:44 [main] INFO  c.ruoyi.framework.config.RedisConfig - 初始化 redis 配置
2023-03-22 15:38:44 [main] INFO  org.redisson.Version - Redisson 3.17.4
2023-03-22 15:38:44 [redisson-netty-2-5] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2023-03-22 15:38:44 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2023-03-22 15:38:45 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2023-03-22 15:38:45 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-03-22 15:38:45 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2023-03-22 15:38:45 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-03-22 15:38:45 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2023-03-22 15:38:46 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2023-03-22 15:38:46 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2023-03-22 15:38:46 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2023-03-22 15:38:50 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2023-03-22 15:38:51 [main] INFO  com.zhonghe.generator.test.TestDemo - Started TestDemo in 9.298 seconds (JVM running for 10.008)
2023-03-22 15:38:51 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qiniu
2023-03-22 15:38:51 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => image
2023-03-22 15:38:51 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => minio
2023-03-22 15:38:51 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qcloud
2023-03-22 15:38:51 [main] INFO  factory.com.zhonghe.oss.OssFactory - 初始化OSS工厂
2023-03-22 15:38:51 [main] INFO  c.r.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2023-03-22 15:38:51 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载参数缓存数据成功
2023-03-22 15:38:52 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载字典缓存数据成功
2023-03-22 15:38:52 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:38:52 [registrationTask1] WARN  d.c.b.a.c.r.ApplicationRegistrator - Failed to register application as Application(name=ZhongHe-Vue-Plus, managementUrl=http://**************:63197/actuator, healthUrl=http://**************:63197/actuator/health, serviceUrl=http://**************:63197/) at spring-boot-admin ([http://localhost:29050/admin/instances]): I/O error on POST request for "http://localhost:29050/admin/instances": Connect to localhost:29050 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused (Connection refused); nested exception is org.apache.http.conn.HttpHostConnectException: Connect to localhost:29050 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused (Connection refused). Further attempts are logged on DEBUG level
2023-03-22 15:38:52 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:38:52 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:38:52 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:38:52 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:38:52 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:38:52 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:38:52 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:38:52 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:38:52 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:38:52 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:38:52 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:38:52 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:38:52 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:38:52 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:38:52 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:38:52 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:38:52 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:38:52 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:38:52 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:39:20 [schedule-pool-1] WARN  o.s.s.a.AnnotationAsyncExecutionInterceptor - Exception handler for async method 'public byte[] service.com.zhonghe.generator.GenTableServiceImpl.downloadCode(java.lang.String)' threw unexpected exception itself
exception.com.zhonghe.common.ServiceException: Exception message -
### Error querying database.  Cause: org.postgresql.util.PSQLException: ERROR: operator does not exist: bigint = character varying
  建议：No operator matches the given name and argument types. You might need to add explicit type casts.
  位置：567
### The error may exist in file [/Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator/target/classes/mapper/generator/GenTableMapper.xml]
### The error may involve mapper.com.zhonghe.generator.GenTableMapper.selectGenTableByName-Inline
### The error occurred while setting parameters
### SQL: SELECT t.table_id, t.table_name, t.table_comment, t.sub_table_name, t.sub_table_fk_name, t.class_name, t.tpl_category, t.package_name, t.module_name, t.business_name, t.function_name, t.function_author, t.gen_type, t.gen_path, t.options, t.remark,                c.column_id, c.column_name, c.column_comment, c.column_type, c.java_type, c.java_field, c.is_pk, c.is_increment, c.is_required, c.is_insert, c.is_edit, c.is_list, c.is_query, c.query_type, c.html_type, c.dict_type, c.sort         FROM gen_table t              LEFT JOIN gen_table_column c ON t.table_id = c.table_id         where t.table_name = ? order by c.sort
### Cause: org.postgresql.util.PSQLException: ERROR: operator does not exist: bigint = character varying
  建议：No operator matches the given name and argument types. You might need to add explicit type casts.
  位置：567
; bad SQL grammar []; nested exception is org.postgresql.util.PSQLException: ERROR: operator does not exist: bigint = character varying
  建议：No operator matches the given name and argument types. You might need to add explicit type casts.
  位置：567, Method name - downloadCode, Parameter value - [sys_user]
	at config.com.zhonghe.framework.AsyncConfig.lambda$getAsyncUncaughtExceptionHandler$0(AsyncConfig.java:50)
	at org.springframework.aop.interceptor.AsyncExecutionAspectSupport.handleError(AsyncExecutionAspectSupport.java:316)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:124)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:834)
2023-03-22 15:39:20 [SpringApplicationShutdownHook] INFO  c.r.f.manager.ShutdownManager - ====关闭后台任务任务线程池====
2023-03-22 15:39:20 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2023-03-22 15:39:20 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2023-03-22 15:39:20 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-03-22 15:39:20 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2023-03-22 15:39:23 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.zhonghe.generator.test.TestDemo], using SpringBootContextLoader
2023-03-22 15:39:23 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.zhonghe.generator.test.TestDemo]: no resource found for suffixes {-context.xml, Context.groovy}.
2023-03-22 15:39:23 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2023-03-22 15:39:23 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@609640d5, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@79da1ec0, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@19fb8826, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@192d74fb, org.springframework.test.context.support.DependencyInjectionTestExecutionListener@4bef0fe3, org.springframework.test.context.support.DirtiesContextTestExecutionListener@62ea3440, org.springframework.test.context.transaction.TransactionalTestExecutionListener@27953a83, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@556d0826, org.springframework.test.context.event.EventPublishingTestExecutionListener@66ce957f, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@55b5f5d2]
2023-03-22 15:39:24 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2023-03-22 15:39:24 [main] INFO  com.zhonghe.generator.test.TestDemo - Starting TestDemo using Java 11.0.18 on lpgMacBook-Pro.lan with PID 6206 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator)
2023-03-22 15:39:24 [main] INFO  com.zhonghe.generator.test.TestDemo - The following 1 profile is active: "dev"
2023-03-22 15:39:25 [main] INFO  c.r.framework.config.JacksonConfig - 初始化 jackson 配置
2023-03-22 15:39:26 [main] INFO  c.ruoyi.framework.config.RedisConfig - 初始化 redis 配置
2023-03-22 15:39:26 [main] INFO  org.redisson.Version - Redisson 3.17.4
2023-03-22 15:39:26 [redisson-netty-2-5] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2023-03-22 15:39:26 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2023-03-22 15:39:27 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2023-03-22 15:39:27 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-03-22 15:39:27 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2023-03-22 15:39:27 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-03-22 15:39:27 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2023-03-22 15:39:28 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2023-03-22 15:39:28 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2023-03-22 15:39:28 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2023-03-22 15:39:32 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2023-03-22 15:39:36 [main] INFO  com.zhonghe.generator.test.TestDemo - Started TestDemo in 12.176 seconds (JVM running for 13.204)
2023-03-22 15:39:37 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qiniu
2023-03-22 15:39:37 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => image
2023-03-22 15:39:37 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => minio
2023-03-22 15:39:37 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qcloud
2023-03-22 15:39:37 [main] INFO  factory.com.zhonghe.oss.OssFactory - 初始化OSS工厂
2023-03-22 15:39:37 [main] INFO  c.r.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2023-03-22 15:39:37 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载参数缓存数据成功
2023-03-22 15:39:37 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载字典缓存数据成功
2023-03-22 15:39:38 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:39:38 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:39:38 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:39:38 [registrationTask1] WARN  d.c.b.a.c.r.ApplicationRegistrator - Failed to register application as Application(name=ZhongHe-Vue-Plus, managementUrl=http://**************:63251/actuator, healthUrl=http://**************:63251/actuator/health, serviceUrl=http://**************:63251/) at spring-boot-admin ([http://localhost:29050/admin/instances]): I/O error on POST request for "http://localhost:29050/admin/instances": Connect to localhost:29050 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused (Connection refused); nested exception is org.apache.http.conn.HttpHostConnectException: Connect to localhost:29050 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused (Connection refused). Further attempts are logged on DEBUG level
2023-03-22 15:39:38 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:39:38 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:39:38 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:39:38 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:39:38 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:39:38 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:39:38 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:39:38 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:39:38 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:39:38 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:39:38 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:39:38 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:39:38 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:39:38 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:39:38 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:39:38 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:39:38 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:40:28 [schedule-pool-1] WARN  o.s.s.a.AnnotationAsyncExecutionInterceptor - Exception handler for async method 'public byte[] service.com.zhonghe.generator.GenTableServiceImpl.downloadCode(java.lang.String)' threw unexpected exception itself
exception.com.zhonghe.common.ServiceException: Exception message -
### Error querying database.  Cause: org.postgresql.util.PSQLException: ERROR: operator does not exist: bigint = character varying
  建议：No operator matches the given name and argument types. You might need to add explicit type casts.
  位置：567
### The error may exist in file [/Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator/target/classes/mapper/generator/GenTableMapper.xml]
### The error may involve mapper.com.zhonghe.generator.GenTableMapper.selectGenTableByName-Inline
### The error occurred while setting parameters
### SQL: SELECT t.table_id, t.table_name, t.table_comment, t.sub_table_name, t.sub_table_fk_name, t.class_name, t.tpl_category, t.package_name, t.module_name, t.business_name, t.function_name, t.function_author, t.gen_type, t.gen_path, t.options, t.remark,                c.column_id, c.column_name, c.column_comment, c.column_type, c.java_type, c.java_field, c.is_pk, c.is_increment, c.is_required, c.is_insert, c.is_edit, c.is_list, c.is_query, c.query_type, c.html_type, c.dict_type, c.sort         FROM gen_table t              LEFT JOIN gen_table_column c ON t.table_id = c.table_id         where t.table_name = ? order by c.sort
### Cause: org.postgresql.util.PSQLException: ERROR: operator does not exist: bigint = character varying
  建议：No operator matches the given name and argument types. You might need to add explicit type casts.
  位置：567
; bad SQL grammar []; nested exception is org.postgresql.util.PSQLException: ERROR: operator does not exist: bigint = character varying
  建议：No operator matches the given name and argument types. You might need to add explicit type casts.
  位置：567, Method name - downloadCode, Parameter value - [sys_user]
	at config.com.zhonghe.framework.AsyncConfig.lambda$getAsyncUncaughtExceptionHandler$0(AsyncConfig.java:50)
	at org.springframework.aop.interceptor.AsyncExecutionAspectSupport.handleError(AsyncExecutionAspectSupport.java:316)
	at org.springframework.aop.interceptor.AsyncExecutionInterceptor.lambda$invoke$0(AsyncExecutionInterceptor.java:124)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask.run(ScheduledThreadPoolExecutor.java:304)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1128)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:628)
	at java.base/java.lang.Thread.run(Thread.java:834)
2023-03-22 15:40:28 [SpringApplicationShutdownHook] INFO  c.r.f.manager.ShutdownManager - ====关闭后台任务任务线程池====
2023-03-22 15:40:28 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2023-03-22 15:40:28 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2023-03-22 15:40:28 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-03-22 15:40:28 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2023-03-22 15:40:31 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.zhonghe.generator.test.TestDemo], using SpringBootContextLoader
2023-03-22 15:40:31 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.zhonghe.generator.test.TestDemo]: no resource found for suffixes {-context.xml, Context.groovy}.
2023-03-22 15:40:32 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2023-03-22 15:40:32 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@609640d5, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@79da1ec0, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@19fb8826, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@192d74fb, org.springframework.test.context.support.DependencyInjectionTestExecutionListener@4bef0fe3, org.springframework.test.context.support.DirtiesContextTestExecutionListener@62ea3440, org.springframework.test.context.transaction.TransactionalTestExecutionListener@27953a83, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@556d0826, org.springframework.test.context.event.EventPublishingTestExecutionListener@66ce957f, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@55b5f5d2]
2023-03-22 15:40:32 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2023-03-22 15:40:32 [main] INFO  com.zhonghe.generator.test.TestDemo - Starting TestDemo using Java 11.0.18 on lpgMacBook-Pro.lan with PID 6234 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator)
2023-03-22 15:40:32 [main] INFO  com.zhonghe.generator.test.TestDemo - The following 1 profile is active: "dev"
2023-03-22 15:40:40 [main] INFO  c.r.framework.config.JacksonConfig - 初始化 jackson 配置
2023-03-22 15:40:41 [main] INFO  c.ruoyi.framework.config.RedisConfig - 初始化 redis 配置
2023-03-22 15:40:41 [main] INFO  org.redisson.Version - Redisson 3.17.4
2023-03-22 15:40:41 [redisson-netty-2-6] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2023-03-22 15:40:42 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2023-03-22 15:40:43 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2023-03-22 15:40:43 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-03-22 15:40:43 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2023-03-22 15:40:43 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-03-22 15:40:44 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2023-03-22 15:40:46 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2023-03-22 15:40:46 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2023-03-22 15:40:46 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2023-03-22 15:40:53 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2023-03-22 15:40:58 [main] INFO  com.zhonghe.generator.test.TestDemo - Started TestDemo in 26.849 seconds (JVM running for 27.857)
2023-03-22 15:40:59 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qiniu
2023-03-22 15:40:59 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => image
2023-03-22 15:40:59 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => minio
2023-03-22 15:40:59 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qcloud
2023-03-22 15:40:59 [main] INFO  factory.com.zhonghe.oss.OssFactory - 初始化OSS工厂
2023-03-22 15:40:59 [main] INFO  c.r.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2023-03-22 15:40:59 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载参数缓存数据成功
2023-03-22 15:40:59 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载字典缓存数据成功
2023-03-22 15:41:00 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:41:00 [registrationTask1] WARN  d.c.b.a.c.r.ApplicationRegistrator - Failed to register application as Application(name=ZhongHe-Vue-Plus, managementUrl=http://**************:63359/actuator, healthUrl=http://**************:63359/actuator/health, serviceUrl=http://**************:63359/) at spring-boot-admin ([http://localhost:29050/admin/instances]): I/O error on POST request for "http://localhost:29050/admin/instances": Connect to localhost:29050 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused (Connection refused); nested exception is org.apache.http.conn.HttpHostConnectException: Connect to localhost:29050 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused (Connection refused). Further attempts are logged on DEBUG level
2023-03-22 15:41:00 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:41:00 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:41:00 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:41:00 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:41:00 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:41:00 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:41:00 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:41:00 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:41:00 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:41:00 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:41:00 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:41:00 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:41:00 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:41:00 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:41:00 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:41:00 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:41:00 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:41:00 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:41:00 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:41:15 [SpringApplicationShutdownHook] INFO  c.r.f.manager.ShutdownManager - ====关闭后台任务任务线程池====
2023-03-22 15:41:15 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2023-03-22 15:41:15 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2023-03-22 15:41:15 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-03-22 15:41:15 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2023-03-22 15:46:51 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.zhonghe.generator.test.TestDemo], using SpringBootContextLoader
2023-03-22 15:46:51 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.zhonghe.generator.test.TestDemo]: no resource found for suffixes {-context.xml, Context.groovy}.
2023-03-22 15:46:51 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2023-03-22 15:46:51 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@ed3068a, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@7c2b6087, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@3fffff43, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@a8e6492, org.springframework.test.context.support.DependencyInjectionTestExecutionListener@1c7fd41f, org.springframework.test.context.support.DirtiesContextTestExecutionListener@3b77a04f, org.springframework.test.context.transaction.TransactionalTestExecutionListener@7b324585, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@2e11485, org.springframework.test.context.event.EventPublishingTestExecutionListener@60dce7ea, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@662f5666]
2023-03-22 15:46:51 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2023-03-22 15:46:51 [main] INFO  com.zhonghe.generator.test.TestDemo - Starting TestDemo using Java 11.0.18 on lpgMacBook-Pro.lan with PID 6300 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator)
2023-03-22 15:46:51 [main] INFO  com.zhonghe.generator.test.TestDemo - The following 1 profile is active: "dev"
2023-03-22 15:46:53 [main] INFO  c.r.framework.config.JacksonConfig - 初始化 jackson 配置
2023-03-22 15:46:53 [main] INFO  c.ruoyi.framework.config.RedisConfig - 初始化 redis 配置
2023-03-22 15:46:53 [main] INFO  org.redisson.Version - Redisson 3.17.4
2023-03-22 15:46:54 [redisson-netty-2-5] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2023-03-22 15:46:54 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2023-03-22 15:46:54 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2023-03-22 15:46:54 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-03-22 15:46:54 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2023-03-22 15:46:54 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-03-22 15:46:55 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2023-03-22 15:46:57 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2023-03-22 15:46:57 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2023-03-22 15:46:57 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2023-03-22 15:48:44 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2023-03-22 15:48:48 [redisson-timer-4-1] ERROR o.r.c.handler.PingConnectionHandler - Unable to send PING command over channel: [id: 0x4221897c, L:/**************:63666 - R:*************/*************:29080]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://*************:29080]
	at org.redisson.client.RedisConnection.lambda$async$0(RedisConnection.java:244)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:834)
2023-03-22 15:48:48 [redisson-timer-4-1] ERROR o.r.c.handler.PingConnectionHandler - Unable to send PING command over channel: [id: 0x09d9c67f, L:/**************:63667 - R:*************/*************:29080]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://*************:29080]
	at org.redisson.client.RedisConnection.lambda$async$0(RedisConnection.java:244)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:834)
2023-03-22 15:48:48 [redisson-timer-4-1] ERROR o.r.c.handler.PingConnectionHandler - Unable to send PING command over channel: [id: 0xed30c17b, L:/**************:63669 - R:*************/*************:29080]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://*************:29080]
	at org.redisson.client.RedisConnection.lambda$async$0(RedisConnection.java:244)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:834)
2023-03-22 15:48:48 [redisson-timer-4-1] ERROR o.r.c.handler.PingConnectionHandler - Unable to send PING command over channel: [id: 0x275ad7bc, L:/**************:63670 - R:*************/*************:29080]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://*************:29080]
	at org.redisson.client.RedisConnection.lambda$async$0(RedisConnection.java:244)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:834)
2023-03-22 15:48:48 [redisson-timer-4-1] ERROR o.r.c.handler.PingConnectionHandler - Unable to send PING command over channel: [id: 0xf70ccc79, L:/**************:63668 - R:*************/*************:29080]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://*************:29080]
	at org.redisson.client.RedisConnection.lambda$async$0(RedisConnection.java:244)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:834)
2023-03-22 15:48:48 [redisson-timer-4-1] ERROR o.r.c.handler.PingConnectionHandler - Unable to send PING command over channel: [id: 0x5c1faa56, L:/**************:63671 - R:*************/*************:29080]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://*************:29080]
	at org.redisson.client.RedisConnection.lambda$async$0(RedisConnection.java:244)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:834)
2023-03-22 15:48:48 [redisson-timer-4-1] ERROR o.r.c.handler.PingConnectionHandler - Unable to send PING command over channel: [id: 0x6ace3ad4, L:/**************:63673 - R:*************/*************:29080]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://*************:29080]
	at org.redisson.client.RedisConnection.lambda$async$0(RedisConnection.java:244)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:834)
2023-03-22 15:48:48 [redisson-timer-4-1] ERROR o.r.c.handler.PingConnectionHandler - Unable to send PING command over channel: [id: 0x414c322d, L:/**************:63674 - R:*************/*************:29080]
org.redisson.client.RedisTimeoutException: Command execution timeout for command: (PING), params: [], Redis client: [addr=redis://*************:29080]
	at org.redisson.client.RedisConnection.lambda$async$0(RedisConnection.java:244)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:834)
2023-03-22 15:48:55 [main] INFO  com.zhonghe.generator.test.TestDemo - Started TestDemo in 69.943 seconds (JVM running for 70.486)
2023-03-22 15:48:56 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qiniu
2023-03-22 15:48:56 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => image
2023-03-22 15:48:56 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => minio
2023-03-22 15:48:56 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qcloud
2023-03-22 15:48:56 [main] INFO  factory.com.zhonghe.oss.OssFactory - 初始化OSS工厂
2023-03-22 15:48:56 [main] INFO  c.r.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2023-03-22 15:48:56 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载参数缓存数据成功
2023-03-22 15:48:56 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载字典缓存数据成功
2023-03-22 15:48:56 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:48:57 [registrationTask1] WARN  d.c.b.a.c.r.ApplicationRegistrator - Failed to register application as Application(name=ZhongHe-Vue-Plus, managementUrl=http://**************:63869/actuator, healthUrl=http://**************:63869/actuator/health, serviceUrl=http://**************:63869/) at spring-boot-admin ([http://localhost:29050/admin/instances]): I/O error on POST request for "http://localhost:29050/admin/instances": Connect to localhost:29050 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused (Connection refused); nested exception is org.apache.http.conn.HttpHostConnectException: Connect to localhost:29050 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused (Connection refused). Further attempts are logged on DEBUG level
2023-03-22 15:48:57 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:48:57 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:48:57 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:48:57 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:48:57 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:48:57 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:48:57 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:48:57 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:48:57 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:48:57 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:48:57 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:48:57 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:48:57 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:48:57 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:48:57 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:48:57 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:48:57 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:48:57 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:48:57 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:48:58 [SpringApplicationShutdownHook] INFO  c.r.f.manager.ShutdownManager - ====关闭后台任务任务线程池====
2023-03-22 15:48:59 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2023-03-22 15:48:59 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2023-03-22 15:48:59 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-03-22 15:48:59 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2023-03-22 15:49:54 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.zhonghe.generator.test.TestDemo], using SpringBootContextLoader
2023-03-22 15:49:54 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.zhonghe.generator.test.TestDemo]: no resource found for suffixes {-context.xml, Context.groovy}.
2023-03-22 15:49:54 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2023-03-22 15:49:54 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@609640d5, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@79da1ec0, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@19fb8826, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@192d74fb, org.springframework.test.context.support.DependencyInjectionTestExecutionListener@4bef0fe3, org.springframework.test.context.support.DirtiesContextTestExecutionListener@62ea3440, org.springframework.test.context.transaction.TransactionalTestExecutionListener@27953a83, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@556d0826, org.springframework.test.context.event.EventPublishingTestExecutionListener@66ce957f, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@55b5f5d2]
2023-03-22 15:49:55 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2023-03-22 15:49:55 [main] INFO  com.zhonghe.generator.test.TestDemo - Starting TestDemo using Java 11.0.18 on lpgMacBook-Pro.lan with PID 6379 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator)
2023-03-22 15:49:55 [main] INFO  com.zhonghe.generator.test.TestDemo - The following 1 profile is active: "dev"
2023-03-22 15:49:57 [main] INFO  c.r.framework.config.JacksonConfig - 初始化 jackson 配置
2023-03-22 15:49:58 [main] INFO  c.ruoyi.framework.config.RedisConfig - 初始化 redis 配置
2023-03-22 15:49:58 [main] INFO  org.redisson.Version - Redisson 3.17.4
2023-03-22 15:49:59 [redisson-netty-2-8] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2023-03-22 15:49:59 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2023-03-22 15:50:02 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2023-03-22 15:50:02 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-03-22 15:50:02 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2023-03-22 15:50:02 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-03-22 15:50:04 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2023-03-22 15:50:05 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2023-03-22 15:50:05 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2023-03-22 15:50:05 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2023-03-22 15:50:13 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2023-03-22 15:50:21 [main] INFO  com.zhonghe.generator.test.TestDemo - Started TestDemo in 26.213 seconds (JVM running for 27.392)
2023-03-22 15:50:21 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qiniu
2023-03-22 15:50:21 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => image
2023-03-22 15:50:21 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => minio
2023-03-22 15:50:21 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qcloud
2023-03-22 15:50:21 [main] INFO  factory.com.zhonghe.oss.OssFactory - 初始化OSS工厂
2023-03-22 15:50:21 [main] INFO  c.r.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2023-03-22 15:50:21 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载参数缓存数据成功
2023-03-22 15:50:22 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载字典缓存数据成功
2023-03-22 15:50:22 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:50:22 [registrationTask1] WARN  d.c.b.a.c.r.ApplicationRegistrator - Failed to register application as Application(name=ZhongHe-Vue-Plus, managementUrl=http://**************:63963/actuator, healthUrl=http://**************:63963/actuator/health, serviceUrl=http://**************:63963/) at spring-boot-admin ([http://localhost:29050/admin/instances]): I/O error on POST request for "http://localhost:29050/admin/instances": Connect to localhost:29050 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused (Connection refused); nested exception is org.apache.http.conn.HttpHostConnectException: Connect to localhost:29050 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused (Connection refused). Further attempts are logged on DEBUG level
2023-03-22 15:50:22 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:50:22 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:50:22 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:50:22 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:50:22 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:50:22 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:50:22 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:50:22 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:50:22 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:50:22 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:50:22 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:50:22 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:50:22 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:50:22 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:50:22 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:50:22 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:50:22 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:50:22 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:50:22 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:50:43 [SpringApplicationShutdownHook] INFO  c.r.f.manager.ShutdownManager - ====关闭后台任务任务线程池====
2023-03-22 15:50:43 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2023-03-22 15:50:43 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2023-03-22 15:50:43 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-03-22 15:50:43 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2023-03-22 15:52:14 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.zhonghe.generator.test.TestDemo], using SpringBootContextLoader
2023-03-22 15:52:14 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.zhonghe.generator.test.TestDemo]: no resource found for suffixes {-context.xml, Context.groovy}.
2023-03-22 15:52:14 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2023-03-22 15:52:14 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@609640d5, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@79da1ec0, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@19fb8826, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@192d74fb, org.springframework.test.context.support.DependencyInjectionTestExecutionListener@4bef0fe3, org.springframework.test.context.support.DirtiesContextTestExecutionListener@62ea3440, org.springframework.test.context.transaction.TransactionalTestExecutionListener@27953a83, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@556d0826, org.springframework.test.context.event.EventPublishingTestExecutionListener@66ce957f, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@55b5f5d2]
2023-03-22 15:52:14 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2023-03-22 15:52:14 [main] INFO  com.zhonghe.generator.test.TestDemo - Starting TestDemo using Java 11.0.18 on lpgMacBook-Pro.lan with PID 6403 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator)
2023-03-22 15:52:14 [main] INFO  com.zhonghe.generator.test.TestDemo - The following 1 profile is active: "dev"
2023-03-22 15:52:16 [main] INFO  c.r.framework.config.JacksonConfig - 初始化 jackson 配置
2023-03-22 15:52:16 [main] INFO  c.ruoyi.framework.config.RedisConfig - 初始化 redis 配置
2023-03-22 15:52:16 [main] INFO  org.redisson.Version - Redisson 3.17.4
2023-03-22 15:52:16 [redisson-netty-2-5] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2023-03-22 15:52:16 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2023-03-22 15:52:17 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2023-03-22 15:52:17 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-03-22 15:52:17 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2023-03-22 15:52:17 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-03-22 15:52:18 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2023-03-22 15:52:19 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2023-03-22 15:52:19 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2023-03-22 15:52:19 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2023-03-22 15:52:22 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2023-03-22 15:52:23 [main] INFO  com.zhonghe.generator.test.TestDemo - Started TestDemo in 9.295 seconds (JVM running for 10.079)
2023-03-22 15:52:24 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qiniu
2023-03-22 15:52:24 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => image
2023-03-22 15:52:24 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => minio
2023-03-22 15:52:24 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qcloud
2023-03-22 15:52:24 [main] INFO  factory.com.zhonghe.oss.OssFactory - 初始化OSS工厂
2023-03-22 15:52:24 [main] INFO  c.r.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2023-03-22 15:52:24 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载参数缓存数据成功
2023-03-22 15:52:24 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载字典缓存数据成功
2023-03-22 15:52:24 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:52:24 [registrationTask1] WARN  d.c.b.a.c.r.ApplicationRegistrator - Failed to register application as Application(name=ZhongHe-Vue-Plus, managementUrl=http://**************:64112/actuator, healthUrl=http://**************:64112/actuator/health, serviceUrl=http://**************:64112/) at spring-boot-admin ([http://localhost:29050/admin/instances]): I/O error on POST request for "http://localhost:29050/admin/instances": Connect to localhost:29050 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused (Connection refused); nested exception is org.apache.http.conn.HttpHostConnectException: Connect to localhost:29050 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused (Connection refused). Further attempts are logged on DEBUG level
2023-03-22 15:52:24 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:52:24 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:52:24 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:52:24 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:52:24 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:52:24 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:52:24 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:52:24 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:52:24 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:52:24 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:52:24 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:52:24 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:52:24 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:52:24 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:52:24 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:52:24 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:52:24 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:52:24 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 15:52:24 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:02:11 [SpringApplicationShutdownHook] INFO  c.r.f.manager.ShutdownManager - ====关闭后台任务任务线程池====
2023-03-22 16:02:11 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2023-03-22 16:02:11 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2023-03-22 16:02:11 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-03-22 16:02:11 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2023-03-22 16:02:13 [redisson-timer-4-1] ERROR o.r.eviction.MapCacheEvictionTask - Unable to evict elements for 'testCache'
org.redisson.client.RedisResponseTimeoutException: Redis server response timeout (3000 ms) occured after 0 retry attempts, is non-idempotent command: false. Increase nettyThreads and/or timeout settings. Try to define pingConnectionInterval setting. Command: (EVAL), params: [if redis.call('setnx', KEYS[6], ARGV[4]) == 0 then return -1;end;redis.call('expire', KEYS[6], ARGV[..., 6, testCache, redisson__timeout__set:{testCache}, redisson__idle__set:{testCache}, redisson_map_cache_expired:{testCache}, redisson__map_cache__last_access__set:{testCache}, redisson__execute_task_once_latch:{testCache}, *************, 100, ...], channel: [id: 0xfe300011, L:/**************:64091 - R:*************/*************:29080]
	at org.redisson.command.RedisExecutor.lambda$scheduleResponseTimeout$5(RedisExecutor.java:342)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:834)
2023-03-22 16:02:13 [redisson-timer-4-1] ERROR o.r.eviction.MapCacheEvictionTask - Unable to evict elements for 'redissonCacheMap'
org.redisson.client.RedisResponseTimeoutException: Redis server response timeout (3000 ms) occured after 0 retry attempts, is non-idempotent command: false. Increase nettyThreads and/or timeout settings. Try to define pingConnectionInterval setting. Command: (EVAL), params: [if redis.call('setnx', KEYS[6], ARGV[4]) == 0 then return -1;end;redis.call('expire', KEYS[6], ARGV[..., 6, redissonCacheMap, redisson__timeout__set:{redissonCacheMap}, redisson__idle__set:{redissonCacheMap}, redisson_map_cache_expired:{redissonCacheMap}, redisson__map_cache__last_access__set:{redissonCacheMap}, redisson__execute_task_once_latch:{redissonCacheMap}, *************, 100, ...], channel: [id: 0xf7c8ff93, L:/**************:64090 - R:*************/*************:29080]
	at org.redisson.command.RedisExecutor.lambda$scheduleResponseTimeout$5(RedisExecutor.java:342)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.run(HashedWheelTimer.java:715)
	at io.netty.util.concurrent.ImmediateExecutor.execute(ImmediateExecutor.java:34)
	at io.netty.util.HashedWheelTimer$HashedWheelTimeout.expire(HashedWheelTimer.java:703)
	at io.netty.util.HashedWheelTimer$HashedWheelBucket.expireTimeouts(HashedWheelTimer.java:790)
	at io.netty.util.HashedWheelTimer$Worker.run(HashedWheelTimer.java:503)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:834)
2023-03-22 16:15:44 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.zhonghe.generator.test.TestDemo], using SpringBootContextLoader
2023-03-22 16:15:44 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.zhonghe.generator.test.TestDemo]: no resource found for suffixes {-context.xml, Context.groovy}.
2023-03-22 16:15:44 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2023-03-22 16:15:44 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@ed3068a, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@7c2b6087, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@3fffff43, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@a8e6492, org.springframework.test.context.support.DependencyInjectionTestExecutionListener@1c7fd41f, org.springframework.test.context.support.DirtiesContextTestExecutionListener@3b77a04f, org.springframework.test.context.transaction.TransactionalTestExecutionListener@7b324585, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@2e11485, org.springframework.test.context.event.EventPublishingTestExecutionListener@60dce7ea, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@662f5666]
2023-03-22 16:15:44 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2023-03-22 16:15:44 [main] INFO  com.zhonghe.generator.test.TestDemo - Starting TestDemo using Java 11.0.18 on lpgMacBook-Pro.lan with PID 6963 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator)
2023-03-22 16:15:44 [main] INFO  com.zhonghe.generator.test.TestDemo - The following 1 profile is active: "dev"
2023-03-22 16:15:46 [main] INFO  c.r.framework.config.JacksonConfig - 初始化 jackson 配置
2023-03-22 16:15:46 [main] INFO  c.ruoyi.framework.config.RedisConfig - 初始化 redis 配置
2023-03-22 16:15:46 [main] INFO  org.redisson.Version - Redisson 3.17.4
2023-03-22 16:15:46 [redisson-netty-2-6] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2023-03-22 16:15:46 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2023-03-22 16:15:47 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2023-03-22 16:15:47 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-03-22 16:15:47 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2023-03-22 16:15:47 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-03-22 16:15:47 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2023-03-22 16:15:49 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2023-03-22 16:15:49 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2023-03-22 16:15:49 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2023-03-22 16:15:51 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2023-03-22 16:15:53 [main] INFO  com.zhonghe.generator.test.TestDemo - Started TestDemo in 8.562 seconds (JVM running for 9.124)
2023-03-22 16:15:53 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qiniu
2023-03-22 16:15:53 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => image
2023-03-22 16:15:53 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => minio
2023-03-22 16:15:53 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qcloud
2023-03-22 16:15:53 [main] INFO  factory.com.zhonghe.oss.OssFactory - 初始化OSS工厂
2023-03-22 16:15:53 [main] INFO  c.r.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2023-03-22 16:15:53 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载参数缓存数据成功
2023-03-22 16:15:53 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载字典缓存数据成功
2023-03-22 16:15:53 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:15:53 [registrationTask1] WARN  d.c.b.a.c.r.ApplicationRegistrator - Failed to register application as Application(name=ZhongHe-Vue-Plus, managementUrl=http://**************:65517/actuator, healthUrl=http://**************:65517/actuator/health, serviceUrl=http://**************:65517/) at spring-boot-admin ([http://localhost:29050/admin/instances]): I/O error on POST request for "http://localhost:29050/admin/instances": Connect to localhost:29050 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused (Connection refused); nested exception is org.apache.http.conn.HttpHostConnectException: Connect to localhost:29050 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused (Connection refused). Further attempts are logged on DEBUG level
2023-03-22 16:15:53 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:15:53 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:15:53 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:15:53 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:15:53 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:15:53 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:15:53 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:15:53 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:15:53 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:15:53 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:15:53 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:15:53 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:15:53 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:15:53 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:15:53 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:15:53 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:15:53 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:15:53 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:15:53 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:15:55 [SpringApplicationShutdownHook] INFO  c.r.f.manager.ShutdownManager - ====关闭后台任务任务线程池====
2023-03-22 16:15:55 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2023-03-22 16:15:55 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2023-03-22 16:15:55 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-03-22 16:15:55 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2023-03-22 16:17:33 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.zhonghe.generator.test.TestDemo], using SpringBootContextLoader
2023-03-22 16:17:33 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.zhonghe.generator.test.TestDemo]: no resource found for suffixes {-context.xml, Context.groovy}.
2023-03-22 16:17:33 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2023-03-22 16:17:33 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@ed3068a, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@7c2b6087, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@3fffff43, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@a8e6492, org.springframework.test.context.support.DependencyInjectionTestExecutionListener@1c7fd41f, org.springframework.test.context.support.DirtiesContextTestExecutionListener@3b77a04f, org.springframework.test.context.transaction.TransactionalTestExecutionListener@7b324585, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@2e11485, org.springframework.test.context.event.EventPublishingTestExecutionListener@60dce7ea, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@662f5666]
2023-03-22 16:17:33 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2023-03-22 16:17:33 [main] INFO  com.zhonghe.generator.test.TestDemo - Starting TestDemo using Java 11.0.18 on lpgMacBook-Pro.lan with PID 6996 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator)
2023-03-22 16:17:33 [main] INFO  com.zhonghe.generator.test.TestDemo - The following 1 profile is active: "dev"
2023-03-22 16:17:34 [main] INFO  c.r.framework.config.JacksonConfig - 初始化 jackson 配置
2023-03-22 16:17:35 [main] INFO  c.ruoyi.framework.config.RedisConfig - 初始化 redis 配置
2023-03-22 16:17:35 [main] INFO  org.redisson.Version - Redisson 3.17.4
2023-03-22 16:17:35 [redisson-netty-2-6] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2023-03-22 16:17:35 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2023-03-22 16:17:36 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2023-03-22 16:17:36 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-03-22 16:17:36 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2023-03-22 16:17:36 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-03-22 16:17:36 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2023-03-22 16:17:37 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2023-03-22 16:17:37 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2023-03-22 16:17:37 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2023-03-22 16:17:40 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2023-03-22 16:17:41 [main] INFO  com.zhonghe.generator.test.TestDemo - Started TestDemo in 8.56 seconds (JVM running for 9.265)
2023-03-22 16:17:42 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qiniu
2023-03-22 16:17:42 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => image
2023-03-22 16:17:42 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => minio
2023-03-22 16:17:42 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qcloud
2023-03-22 16:17:42 [main] INFO  factory.com.zhonghe.oss.OssFactory - 初始化OSS工厂
2023-03-22 16:17:42 [main] INFO  c.r.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2023-03-22 16:17:42 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载参数缓存数据成功
2023-03-22 16:17:42 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载字典缓存数据成功
2023-03-22 16:17:42 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:17:42 [registrationTask1] WARN  d.c.b.a.c.r.ApplicationRegistrator - Failed to register application as Application(name=ZhongHe-Vue-Plus, managementUrl=http://**************:49272/actuator, healthUrl=http://**************:49272/actuator/health, serviceUrl=http://**************:49272/) at spring-boot-admin ([http://localhost:29050/admin/instances]): I/O error on POST request for "http://localhost:29050/admin/instances": Connect to localhost:29050 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused (Connection refused); nested exception is org.apache.http.conn.HttpHostConnectException: Connect to localhost:29050 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused (Connection refused). Further attempts are logged on DEBUG level
2023-03-22 16:17:42 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:17:42 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:17:42 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:17:42 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:17:42 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:17:42 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:17:42 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:17:42 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:17:42 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:17:42 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:17:42 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:17:42 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:17:42 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:17:42 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:17:42 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:17:42 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:17:42 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:17:42 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:17:42 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:17:44 [SpringApplicationShutdownHook] INFO  c.r.f.manager.ShutdownManager - ====关闭后台任务任务线程池====
2023-03-22 16:17:44 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2023-03-22 16:17:44 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2023-03-22 16:17:44 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-03-22 16:17:44 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2023-03-22 16:25:13 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.zhonghe.generator.test.TestDemo], using SpringBootContextLoader
2023-03-22 16:25:13 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.zhonghe.generator.test.TestDemo]: no resource found for suffixes {-context.xml, Context.groovy}.
2023-03-22 16:25:14 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2023-03-22 16:25:14 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@ed3068a, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@7c2b6087, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@3fffff43, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@a8e6492, org.springframework.test.context.support.DependencyInjectionTestExecutionListener@1c7fd41f, org.springframework.test.context.support.DirtiesContextTestExecutionListener@3b77a04f, org.springframework.test.context.transaction.TransactionalTestExecutionListener@7b324585, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@2e11485, org.springframework.test.context.event.EventPublishingTestExecutionListener@60dce7ea, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@662f5666]
2023-03-22 16:25:14 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2023-03-22 16:25:14 [main] INFO  com.zhonghe.generator.test.TestDemo - Starting TestDemo using Java 11.0.18 on lpgMacBook-Pro.lan with PID 7121 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator)
2023-03-22 16:25:14 [main] INFO  com.zhonghe.generator.test.TestDemo - The following 1 profile is active: "dev"
2023-03-22 16:25:15 [main] INFO  c.r.framework.config.JacksonConfig - 初始化 jackson 配置
2023-03-22 16:25:16 [main] INFO  c.ruoyi.framework.config.RedisConfig - 初始化 redis 配置
2023-03-22 16:25:16 [main] INFO  org.redisson.Version - Redisson 3.17.4
2023-03-22 16:25:16 [redisson-netty-2-5] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2023-03-22 16:25:16 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2023-03-22 16:25:17 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2023-03-22 16:25:17 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-03-22 16:25:17 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2023-03-22 16:25:17 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-03-22 16:25:17 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2023-03-22 16:25:18 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2023-03-22 16:25:18 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2023-03-22 16:25:18 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2023-03-22 16:25:21 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2023-03-22 16:25:23 [main] INFO  com.zhonghe.generator.test.TestDemo - Started TestDemo in 8.981 seconds (JVM running for 9.604)
2023-03-22 16:25:23 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qiniu
2023-03-22 16:25:23 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => image
2023-03-22 16:25:23 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => minio
2023-03-22 16:25:23 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qcloud
2023-03-22 16:25:23 [main] INFO  factory.com.zhonghe.oss.OssFactory - 初始化OSS工厂
2023-03-22 16:25:23 [main] INFO  c.r.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2023-03-22 16:25:23 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载参数缓存数据成功
2023-03-22 16:25:23 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载字典缓存数据成功
2023-03-22 16:25:23 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:25:23 [registrationTask1] WARN  d.c.b.a.c.r.ApplicationRegistrator - Failed to register application as Application(name=ZhongHe-Vue-Plus, managementUrl=http://**************:50264/actuator, healthUrl=http://**************:50264/actuator/health, serviceUrl=http://**************:50264/) at spring-boot-admin ([http://localhost:29050/admin/instances]): I/O error on POST request for "http://localhost:29050/admin/instances": Connect to localhost:29050 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused (Connection refused); nested exception is org.apache.http.conn.HttpHostConnectException: Connect to localhost:29050 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused (Connection refused). Further attempts are logged on DEBUG level
2023-03-22 16:25:23 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:25:23 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:25:23 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:25:23 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:25:23 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:25:23 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:25:23 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:25:23 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:25:23 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:25:23 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:25:23 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:25:23 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:25:23 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:25:23 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:25:23 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:25:23 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:25:23 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:25:23 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:25:23 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:25:25 [SpringApplicationShutdownHook] INFO  c.r.f.manager.ShutdownManager - ====关闭后台任务任务线程池====
2023-03-22 16:25:25 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2023-03-22 16:25:25 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2023-03-22 16:25:25 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-03-22 16:25:25 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2023-03-22 16:27:29 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.zhonghe.generator.test.TestDemo], using SpringBootContextLoader
2023-03-22 16:27:29 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.zhonghe.generator.test.TestDemo]: no resource found for suffixes {-context.xml, Context.groovy}.
2023-03-22 16:27:29 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2023-03-22 16:27:29 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@ed3068a, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@7c2b6087, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@3fffff43, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@a8e6492, org.springframework.test.context.support.DependencyInjectionTestExecutionListener@1c7fd41f, org.springframework.test.context.support.DirtiesContextTestExecutionListener@3b77a04f, org.springframework.test.context.transaction.TransactionalTestExecutionListener@7b324585, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@2e11485, org.springframework.test.context.event.EventPublishingTestExecutionListener@60dce7ea, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@662f5666]
2023-03-22 16:27:29 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2023-03-22 16:27:29 [main] INFO  com.zhonghe.generator.test.TestDemo - Starting TestDemo using Java 11.0.18 on lpgMacBook-Pro.lan with PID 7148 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator)
2023-03-22 16:27:29 [main] INFO  com.zhonghe.generator.test.TestDemo - The following 1 profile is active: "dev"
2023-03-22 16:27:30 [main] INFO  c.r.framework.config.JacksonConfig - 初始化 jackson 配置
2023-03-22 16:27:31 [main] INFO  c.ruoyi.framework.config.RedisConfig - 初始化 redis 配置
2023-03-22 16:27:31 [main] INFO  org.redisson.Version - Redisson 3.17.4
2023-03-22 16:27:31 [redisson-netty-2-6] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2023-03-22 16:27:31 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2023-03-22 16:27:32 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2023-03-22 16:27:32 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-03-22 16:27:32 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2023-03-22 16:27:32 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-03-22 16:27:32 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2023-03-22 16:27:33 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2023-03-22 16:27:33 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2023-03-22 16:27:33 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2023-03-22 16:27:36 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2023-03-22 16:27:37 [main] INFO  com.zhonghe.generator.test.TestDemo - Started TestDemo in 8.609 seconds (JVM running for 9.191)
2023-03-22 16:27:38 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qiniu
2023-03-22 16:27:38 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => image
2023-03-22 16:27:38 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => minio
2023-03-22 16:27:38 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qcloud
2023-03-22 16:27:38 [main] INFO  factory.com.zhonghe.oss.OssFactory - 初始化OSS工厂
2023-03-22 16:27:38 [main] INFO  c.r.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2023-03-22 16:27:38 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载参数缓存数据成功
2023-03-22 16:27:38 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载字典缓存数据成功
2023-03-22 16:27:38 [registrationTask1] WARN  d.c.b.a.c.r.ApplicationRegistrator - Failed to register application as Application(name=ZhongHe-Vue-Plus, managementUrl=http://**************:50378/actuator, healthUrl=http://**************:50378/actuator/health, serviceUrl=http://**************:50378/) at spring-boot-admin ([http://localhost:29050/admin/instances]): I/O error on POST request for "http://localhost:29050/admin/instances": Connect to localhost:29050 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused (Connection refused); nested exception is org.apache.http.conn.HttpHostConnectException: Connect to localhost:29050 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused (Connection refused). Further attempts are logged on DEBUG level
2023-03-22 16:27:39 [SpringApplicationShutdownHook] INFO  c.r.f.manager.ShutdownManager - ====关闭后台任务任务线程池====
2023-03-22 16:27:39 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2023-03-22 16:27:39 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2023-03-22 16:27:39 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-03-22 16:27:39 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2023-03-22 16:57:51 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.zhonghe.generator.test.TestDemo], using SpringBootContextLoader
2023-03-22 16:57:51 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.zhonghe.generator.test.TestDemo]: no resource found for suffixes {-context.xml, Context.groovy}.
2023-03-22 16:57:51 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2023-03-22 16:57:51 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@ed3068a, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@7c2b6087, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@3fffff43, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@a8e6492, org.springframework.test.context.support.DependencyInjectionTestExecutionListener@1c7fd41f, org.springframework.test.context.support.DirtiesContextTestExecutionListener@3b77a04f, org.springframework.test.context.transaction.TransactionalTestExecutionListener@7b324585, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@2e11485, org.springframework.test.context.event.EventPublishingTestExecutionListener@60dce7ea, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@662f5666]
2023-03-22 16:57:51 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2023-03-22 16:57:51 [main] INFO  com.zhonghe.generator.test.TestDemo - Starting TestDemo using Java 11.0.18 on lpgMacBook-Pro.lan with PID 7492 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator)
2023-03-22 16:57:51 [main] INFO  com.zhonghe.generator.test.TestDemo - The following 1 profile is active: "dev"
2023-03-22 16:57:53 [main] INFO  c.r.framework.config.JacksonConfig - 初始化 jackson 配置
2023-03-22 16:57:53 [main] INFO  c.ruoyi.framework.config.RedisConfig - 初始化 redis 配置
2023-03-22 16:57:53 [main] INFO  org.redisson.Version - Redisson 3.17.4
2023-03-22 16:57:54 [redisson-netty-2-5] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2023-03-22 16:57:54 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2023-03-22 16:57:54 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2023-03-22 16:57:54 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-03-22 16:57:54 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2023-03-22 16:57:54 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-03-22 16:57:55 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2023-03-22 16:57:56 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2023-03-22 16:57:56 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2023-03-22 16:57:56 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2023-03-22 16:57:59 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2023-03-22 16:58:00 [main] INFO  com.zhonghe.generator.test.TestDemo - Started TestDemo in 8.773 seconds (JVM running for 9.473)
2023-03-22 16:58:00 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => minio
2023-03-22 16:58:00 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qiniu
2023-03-22 16:58:00 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => image
2023-03-22 16:58:00 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qcloud
2023-03-22 16:58:00 [main] INFO  factory.com.zhonghe.oss.OssFactory - 初始化OSS工厂
2023-03-22 16:58:00 [main] INFO  c.r.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2023-03-22 16:58:00 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载参数缓存数据成功
2023-03-22 16:58:01 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载字典缓存数据成功
2023-03-22 16:58:01 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:58:01 [registrationTask1] WARN  d.c.b.a.c.r.ApplicationRegistrator - Failed to register application as Application(name=ZhongHe-Vue-Plus, managementUrl=http://**************:57966/actuator, healthUrl=http://**************:57966/actuator/health, serviceUrl=http://**************:57966/) at spring-boot-admin ([http://localhost:29050/admin/instances]): I/O error on POST request for "http://localhost:29050/admin/instances": Connect to localhost:29050 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused (Connection refused); nested exception is org.apache.http.conn.HttpHostConnectException: Connect to localhost:29050 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused (Connection refused). Further attempts are logged on DEBUG level
2023-03-22 16:58:01 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:58:01 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:58:01 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:58:01 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:58:01 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:58:01 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:58:01 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 16:58:02 [SpringApplicationShutdownHook] INFO  c.r.f.manager.ShutdownManager - ====关闭后台任务任务线程池====
2023-03-22 16:58:02 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2023-03-22 16:58:02 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2023-03-22 16:58:02 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-03-22 16:58:02 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2023-03-22 17:01:51 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.zhonghe.generator.test.TestDemo], using SpringBootContextLoader
2023-03-22 17:01:51 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.zhonghe.generator.test.TestDemo]: no resource found for suffixes {-context.xml, Context.groovy}.
2023-03-22 17:01:51 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2023-03-22 17:01:51 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@ed3068a, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@7c2b6087, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@3fffff43, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@a8e6492, org.springframework.test.context.support.DependencyInjectionTestExecutionListener@1c7fd41f, org.springframework.test.context.support.DirtiesContextTestExecutionListener@3b77a04f, org.springframework.test.context.transaction.TransactionalTestExecutionListener@7b324585, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@2e11485, org.springframework.test.context.event.EventPublishingTestExecutionListener@60dce7ea, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@662f5666]
2023-03-22 17:01:52 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2023-03-22 17:01:52 [main] INFO  com.zhonghe.generator.test.TestDemo - Starting TestDemo using Java 11.0.18 on lpgMacBook-Pro.lan with PID 7529 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator)
2023-03-22 17:01:52 [main] INFO  com.zhonghe.generator.test.TestDemo - The following 1 profile is active: "dev"
2023-03-22 17:01:53 [main] INFO  c.r.framework.config.JacksonConfig - 初始化 jackson 配置
2023-03-22 17:01:53 [main] INFO  c.ruoyi.framework.config.RedisConfig - 初始化 redis 配置
2023-03-22 17:01:53 [main] INFO  org.redisson.Version - Redisson 3.17.4
2023-03-22 17:01:54 [redisson-netty-2-5] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2023-03-22 17:01:54 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2023-03-22 17:01:55 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2023-03-22 17:01:55 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-03-22 17:01:55 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2023-03-22 17:01:55 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-03-22 17:01:55 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2023-03-22 17:01:56 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2023-03-22 17:01:56 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2023-03-22 17:01:56 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2023-03-22 17:01:59 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2023-03-22 17:02:00 [main] INFO  com.zhonghe.generator.test.TestDemo - Started TestDemo in 9.145 seconds (JVM running for 9.758)
2023-03-22 17:02:01 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => minio
2023-03-22 17:02:01 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qiniu
2023-03-22 17:02:01 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => image
2023-03-22 17:02:01 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qcloud
2023-03-22 17:02:01 [main] INFO  factory.com.zhonghe.oss.OssFactory - 初始化OSS工厂
2023-03-22 17:02:01 [main] INFO  c.r.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2023-03-22 17:02:01 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载参数缓存数据成功
2023-03-22 17:02:01 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载字典缓存数据成功
2023-03-22 17:02:01 [registrationTask1] WARN  d.c.b.a.c.r.ApplicationRegistrator - Failed to register application as Application(name=ZhongHe-Vue-Plus, managementUrl=http://**************:59118/actuator, healthUrl=http://**************:59118/actuator/health, serviceUrl=http://**************:59118/) at spring-boot-admin ([http://localhost:29050/admin/instances]): I/O error on POST request for "http://localhost:29050/admin/instances": Connect to localhost:29050 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused (Connection refused); nested exception is org.apache.http.conn.HttpHostConnectException: Connect to localhost:29050 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused (Connection refused). Further attempts are logged on DEBUG level
2023-03-22 17:02:02 [SpringApplicationShutdownHook] INFO  c.r.f.manager.ShutdownManager - ====关闭后台任务任务线程池====
2023-03-22 17:02:02 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2023-03-22 17:02:02 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2023-03-22 17:02:02 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-03-22 17:02:02 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2023-03-22 17:08:39 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.zhonghe.generator.test.TestDemo], using SpringBootContextLoader
2023-03-22 17:08:39 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.zhonghe.generator.test.TestDemo]: no resource found for suffixes {-context.xml, Context.groovy}.
2023-03-22 17:08:39 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2023-03-22 17:08:39 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@609640d5, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@79da1ec0, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@19fb8826, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@192d74fb, org.springframework.test.context.support.DependencyInjectionTestExecutionListener@4bef0fe3, org.springframework.test.context.support.DirtiesContextTestExecutionListener@62ea3440, org.springframework.test.context.transaction.TransactionalTestExecutionListener@27953a83, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@556d0826, org.springframework.test.context.event.EventPublishingTestExecutionListener@66ce957f, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@55b5f5d2]
2023-03-22 17:08:39 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2023-03-22 17:08:39 [main] INFO  com.zhonghe.generator.test.TestDemo - Starting TestDemo using Java 11.0.18 on lpgMacBook-Pro.lan with PID 7732 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator)
2023-03-22 17:08:39 [main] INFO  com.zhonghe.generator.test.TestDemo - The following 1 profile is active: "dev"
2023-03-22 17:08:41 [main] INFO  c.r.framework.config.JacksonConfig - 初始化 jackson 配置
2023-03-22 17:08:41 [main] INFO  c.ruoyi.framework.config.RedisConfig - 初始化 redis 配置
2023-03-22 17:08:41 [main] INFO  org.redisson.Version - Redisson 3.17.4
2023-03-22 17:08:42 [redisson-netty-2-3] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2023-03-22 17:08:42 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2023-03-22 17:08:42 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2023-03-22 17:08:42 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-03-22 17:08:42 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2023-03-22 17:08:42 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-03-22 17:08:43 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2023-03-22 17:08:44 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2023-03-22 17:08:44 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2023-03-22 17:08:44 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2023-03-22 17:08:47 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2023-03-22 17:08:49 [main] INFO  com.zhonghe.generator.test.TestDemo - Started TestDemo in 9.372 seconds (JVM running for 10.268)
2023-03-22 17:08:49 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => minio
2023-03-22 17:08:49 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qiniu
2023-03-22 17:08:49 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => image
2023-03-22 17:08:49 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qcloud
2023-03-22 17:08:49 [main] INFO  factory.com.zhonghe.oss.OssFactory - 初始化OSS工厂
2023-03-22 17:08:49 [main] INFO  c.r.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2023-03-22 17:08:49 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载参数缓存数据成功
2023-03-22 17:08:49 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载字典缓存数据成功
2023-03-22 17:08:53 [registrationTask1] WARN  d.c.b.a.c.r.ApplicationRegistrator - Failed to register application as Application(name=ZhongHe-Vue-Plus, managementUrl=http://**************:61369/actuator, healthUrl=http://**************:61369/actuator/health, serviceUrl=http://**************:61369/) at spring-boot-admin ([http://localhost:29050/admin/instances]): I/O error on POST request for "http://localhost:29050/admin/instances": Connect to localhost:29050 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused (Connection refused); nested exception is org.apache.http.conn.HttpHostConnectException: Connect to localhost:29050 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused (Connection refused). Further attempts are logged on DEBUG level
2023-03-22 17:08:54 [SpringApplicationShutdownHook] INFO  c.r.f.manager.ShutdownManager - ====关闭后台任务任务线程池====
2023-03-22 17:08:54 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2023-03-22 17:08:54 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2023-03-22 17:08:54 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-03-22 17:08:54 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2023-03-22 17:11:15 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.zhonghe.generator.test.TestDemo], using SpringBootContextLoader
2023-03-22 17:11:15 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.zhonghe.generator.test.TestDemo]: no resource found for suffixes {-context.xml, Context.groovy}.
2023-03-22 17:11:15 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2023-03-22 17:11:15 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@ed3068a, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@7c2b6087, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@3fffff43, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@a8e6492, org.springframework.test.context.support.DependencyInjectionTestExecutionListener@1c7fd41f, org.springframework.test.context.support.DirtiesContextTestExecutionListener@3b77a04f, org.springframework.test.context.transaction.TransactionalTestExecutionListener@7b324585, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@2e11485, org.springframework.test.context.event.EventPublishingTestExecutionListener@60dce7ea, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@662f5666]
2023-03-22 17:11:15 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2023-03-22 17:11:15 [main] INFO  com.zhonghe.generator.test.TestDemo - Starting TestDemo using Java 11.0.18 on lpgMacBook-Pro.lan with PID 7793 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator)
2023-03-22 17:11:15 [main] INFO  com.zhonghe.generator.test.TestDemo - The following 1 profile is active: "dev"
2023-03-22 17:11:17 [main] INFO  c.r.framework.config.JacksonConfig - 初始化 jackson 配置
2023-03-22 17:11:17 [main] INFO  c.ruoyi.framework.config.RedisConfig - 初始化 redis 配置
2023-03-22 17:11:17 [main] INFO  org.redisson.Version - Redisson 3.17.4
2023-03-22 17:11:17 [redisson-netty-2-5] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2023-03-22 17:11:18 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2023-03-22 17:11:18 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2023-03-22 17:11:18 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-03-22 17:11:18 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2023-03-22 17:11:18 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-03-22 17:11:18 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2023-03-22 17:11:20 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2023-03-22 17:11:20 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2023-03-22 17:11:20 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2023-03-22 17:11:22 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2023-03-22 17:11:24 [main] INFO  com.zhonghe.generator.test.TestDemo - Started TestDemo in 8.777 seconds (JVM running for 9.346)
2023-03-22 17:11:24 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => minio
2023-03-22 17:11:24 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qiniu
2023-03-22 17:11:24 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => image
2023-03-22 17:11:24 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qcloud
2023-03-22 17:11:24 [main] INFO  factory.com.zhonghe.oss.OssFactory - 初始化OSS工厂
2023-03-22 17:11:24 [main] INFO  c.r.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2023-03-22 17:11:24 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载参数缓存数据成功
2023-03-22 17:11:24 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载字典缓存数据成功
2023-03-22 17:11:24 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 17:11:24 [registrationTask1] WARN  d.c.b.a.c.r.ApplicationRegistrator - Failed to register application as Application(name=ZhongHe-Vue-Plus, managementUrl=http://**************:62155/actuator, healthUrl=http://**************:62155/actuator/health, serviceUrl=http://**************:62155/) at spring-boot-admin ([http://localhost:29050/admin/instances]): I/O error on POST request for "http://localhost:29050/admin/instances": Connect to localhost:29050 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused (Connection refused); nested exception is org.apache.http.conn.HttpHostConnectException: Connect to localhost:29050 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused (Connection refused). Further attempts are logged on DEBUG level
2023-03-22 17:11:24 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 17:11:24 [main] WARN  c.r.f.h.CreateAndUpdateMetaObjectHandler - 自动注入警告 => 用户未登录
2023-03-22 17:11:26 [SpringApplicationShutdownHook] INFO  c.r.f.manager.ShutdownManager - ====关闭后台任务任务线程池====
2023-03-22 17:11:26 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2023-03-22 17:11:26 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2023-03-22 17:11:26 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-03-22 17:11:26 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2023-03-22 17:16:01 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.zhonghe.generator.test.TestDemo], using SpringBootContextLoader
2023-03-22 17:16:01 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.zhonghe.generator.test.TestDemo]: no resource found for suffixes {-context.xml, Context.groovy}.
2023-03-22 17:16:01 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2023-03-22 17:16:01 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@ed3068a, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@7c2b6087, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@3fffff43, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@a8e6492, org.springframework.test.context.support.DependencyInjectionTestExecutionListener@1c7fd41f, org.springframework.test.context.support.DirtiesContextTestExecutionListener@3b77a04f, org.springframework.test.context.transaction.TransactionalTestExecutionListener@7b324585, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@2e11485, org.springframework.test.context.event.EventPublishingTestExecutionListener@60dce7ea, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@662f5666]
2023-03-22 17:16:02 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2023-03-22 17:16:02 [main] INFO  com.zhonghe.generator.test.TestDemo - Starting TestDemo using Java 11.0.18 on lpgMacBook-Pro.lan with PID 7844 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator)
2023-03-22 17:16:02 [main] INFO  com.zhonghe.generator.test.TestDemo - The following 1 profile is active: "dev"
2023-03-22 17:16:03 [main] INFO  c.r.framework.config.JacksonConfig - 初始化 jackson 配置
2023-03-22 17:16:03 [main] INFO  c.ruoyi.framework.config.RedisConfig - 初始化 redis 配置
2023-03-22 17:16:03 [main] INFO  org.redisson.Version - Redisson 3.17.4
2023-03-22 17:16:04 [redisson-netty-2-5] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2023-03-22 17:16:04 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2023-03-22 17:16:04 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2023-03-22 17:16:04 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-03-22 17:16:04 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2023-03-22 17:16:04 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-03-22 17:16:05 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2023-03-22 17:16:06 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2023-03-22 17:16:06 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2023-03-22 17:16:06 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2023-03-22 17:16:09 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2023-03-22 17:16:10 [main] INFO  com.zhonghe.generator.test.TestDemo - Started TestDemo in 8.602 seconds (JVM running for 9.154)
2023-03-22 17:16:10 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => minio
2023-03-22 17:16:10 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qiniu
2023-03-22 17:16:10 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => image
2023-03-22 17:16:10 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qcloud
2023-03-22 17:16:10 [main] INFO  factory.com.zhonghe.oss.OssFactory - 初始化OSS工厂
2023-03-22 17:16:10 [main] INFO  c.r.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2023-03-22 17:16:10 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载参数缓存数据成功
2023-03-22 17:16:11 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载字典缓存数据成功
2023-03-22 17:16:11 [registrationTask1] WARN  d.c.b.a.c.r.ApplicationRegistrator - Failed to register application as Application(name=ZhongHe-Vue-Plus, managementUrl=http://**************:63472/actuator, healthUrl=http://**************:63472/actuator/health, serviceUrl=http://**************:63472/) at spring-boot-admin ([http://localhost:29050/admin/instances]): I/O error on POST request for "http://localhost:29050/admin/instances": Connect to localhost:29050 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused (Connection refused); nested exception is org.apache.http.conn.HttpHostConnectException: Connect to localhost:29050 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused (Connection refused). Further attempts are logged on DEBUG level
2023-03-22 17:16:12 [SpringApplicationShutdownHook] INFO  c.r.f.manager.ShutdownManager - ====关闭后台任务任务线程池====
2023-03-22 17:16:12 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2023-03-22 17:16:12 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2023-03-22 17:16:12 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-03-22 17:16:12 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2023-03-22 17:23:27 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.zhonghe.generator.test.TestDemo], using SpringBootContextLoader
2023-03-22 17:23:27 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.zhonghe.generator.test.TestDemo]: no resource found for suffixes {-context.xml, Context.groovy}.
2023-03-22 17:23:27 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2023-03-22 17:23:27 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@ed3068a, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@7c2b6087, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@3fffff43, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@a8e6492, org.springframework.test.context.support.DependencyInjectionTestExecutionListener@1c7fd41f, org.springframework.test.context.support.DirtiesContextTestExecutionListener@3b77a04f, org.springframework.test.context.transaction.TransactionalTestExecutionListener@7b324585, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@2e11485, org.springframework.test.context.event.EventPublishingTestExecutionListener@60dce7ea, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@662f5666]
2023-03-22 17:23:27 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2023-03-22 17:23:27 [main] INFO  com.zhonghe.generator.test.TestDemo - Starting TestDemo using Java 11.0.18 on lpgMacBook-Pro.lan with PID 8022 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator)
2023-03-22 17:23:27 [main] INFO  com.zhonghe.generator.test.TestDemo - The following 1 profile is active: "dev"
2023-03-22 17:23:28 [main] INFO  c.r.framework.config.JacksonConfig - 初始化 jackson 配置
2023-03-22 17:23:28 [main] INFO  c.ruoyi.framework.config.RedisConfig - 初始化 redis 配置
2023-03-22 17:23:29 [main] INFO  org.redisson.Version - Redisson 3.17.4
2023-03-22 17:23:29 [redisson-netty-2-5] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2023-03-22 17:23:29 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2023-03-22 17:23:30 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2023-03-22 17:23:30 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-03-22 17:23:30 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2023-03-22 17:23:30 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-03-22 17:23:30 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2023-03-22 17:23:31 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2023-03-22 17:23:31 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2023-03-22 17:23:31 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2023-03-22 17:23:31 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'genController' defined in file [/Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator/target/classes/com/ruoyi/generator/controller/GenController.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'genTableServiceImpl' defined in file [/Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator/target/classes/com/ruoyi/generator/service/GenTableServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'genTableMapper' defined in file [/Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator/target/classes/com/ruoyi/generator/mapper/GenTableMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception; nested exception is org.springframework.core.NestedIOException: Failed to parse mapping resource: 'file [/Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-system/target/classes/mapper/system/SysNoticeDeptMapper.xml]'; nested exception is com.baomidou.mybatisplus.core.exceptions.MybatisPlusException: @TableId can't more than one in Class: "domain.com.zhonghe.system.SysNoticeDept".
2023-03-22 17:23:31 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2023-03-22 17:23:31 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2023-03-22 17:23:31 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-03-22 17:23:31 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
2023-03-22 17:23:31 [main] INFO  o.a.catalina.core.StandardService - Stopping service [Tomcat]
2023-03-22 17:23:32 [main] ERROR o.s.boot.SpringApplication - Application run failed
org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'genController' defined in file [/Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator/target/classes/com/ruoyi/generator/controller/GenController.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'genTableServiceImpl' defined in file [/Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator/target/classes/com/ruoyi/generator/service/GenTableServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'genTableMapper' defined in file [/Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator/target/classes/com/ruoyi/generator/mapper/GenTableMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception; nested exception is org.springframework.core.NestedIOException: Failed to parse mapping resource: 'file [/Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-system/target/classes/mapper/system/SysNoticeDeptMapper.xml]'; nested exception is com.baomidou.mybatisplus.core.exceptions.MybatisPlusException: @TableId can't more than one in Class: "domain.com.zhonghe.system.SysNoticeDept".
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:800)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:229)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1372)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1222)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:145)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:745)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:420)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307)
	at org.springframework.boot.test.context.SpringBootContextLoader.loadContext(SpringBootContextLoader.java:144)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContextInternal(DefaultCacheAwareContextLoaderDelegate.java:99)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:124)
	at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:124)
	at org.springframework.test.context.support.DependencyInjectionTestExecutionListener.injectDependencies(DependencyInjectionTestExecutionListener.java:118)
	at org.springframework.test.context.support.DependencyInjectionTestExecutionListener.prepareTestInstance(DependencyInjectionTestExecutionListener.java:83)
	at org.springframework.test.context.TestContextManager.prepareTestInstance(TestContextManager.java:248)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.createTest(SpringJUnit4ClassRunner.java:227)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner$1.runReflectiveCall(SpringJUnit4ClassRunner.java:289)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.methodBlock(SpringJUnit4ClassRunner.java:291)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:246)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:97)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:190)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at com.intellij.junit4.JUnit4IdeaTestRunner.startRunnerWithArgs(JUnit4IdeaTestRunner.java:69)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater$1.execute(IdeaTestRunner.java:38)
	at com.intellij.rt.execution.junit.TestsRepeater.repeat(TestsRepeater.java:11)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:35)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:235)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:54)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'genTableServiceImpl' defined in file [/Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator/target/classes/com/ruoyi/generator/service/GenTableServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'genTableMapper' defined in file [/Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator/target/classes/com/ruoyi/generator/mapper/GenTableMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception; nested exception is org.springframework.core.NestedIOException: Failed to parse mapping resource: 'file [/Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-system/target/classes/mapper/system/SysNoticeDeptMapper.xml]'; nested exception is com.baomidou.mybatisplus.core.exceptions.MybatisPlusException: @TableId can't more than one in Class: "domain.com.zhonghe.system.SysNoticeDept".
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:800)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:229)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1372)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1222)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:887)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 46 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'genTableMapper' defined in file [/Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator/target/classes/com/ruoyi/generator/mapper/GenTableMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception; nested exception is org.springframework.core.NestedIOException: Failed to parse mapping resource: 'file [/Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-system/target/classes/mapper/system/SysNoticeDeptMapper.xml]'; nested exception is com.baomidou.mybatisplus.core.exceptions.MybatisPlusException: @TableId can't more than one in Class: "domain.com.zhonghe.system.SysNoticeDept".
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByType(AbstractAutowireCapableBeanFactory.java:1534)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1417)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:887)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 60 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception; nested exception is org.springframework.core.NestedIOException: Failed to parse mapping resource: 'file [/Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-system/target/classes/mapper/system/SysNoticeDeptMapper.xml]'; nested exception is com.baomidou.mybatisplus.core.exceptions.MybatisPlusException: @TableId can't more than one in Class: "domain.com.zhonghe.system.SysNoticeDept".
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:658)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:638)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByType(AbstractAutowireCapableBeanFactory.java:1519)
	... 72 common frames omitted
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception; nested exception is org.springframework.core.NestedIOException: Failed to parse mapping resource: 'file [/Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-system/target/classes/mapper/system/SysNoticeDeptMapper.xml]'; nested exception is com.baomidou.mybatisplus.core.exceptions.MybatisPlusException: @TableId can't more than one in Class: "domain.com.zhonghe.system.SysNoticeDept".
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:185)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:653)
	... 85 common frames omitted
Caused by: org.springframework.core.NestedIOException: Failed to parse mapping resource: 'file [/Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-system/target/classes/mapper/system/SysNoticeDeptMapper.xml]'; nested exception is com.baomidou.mybatisplus.core.exceptions.MybatisPlusException: @TableId can't more than one in Class: "domain.com.zhonghe.system.SysNoticeDept".
	at com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean.buildSqlSessionFactory(MybatisSqlSessionFactoryBean.java:576)
	at com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean.afterPropertiesSet(MybatisSqlSessionFactoryBean.java:445)
	at com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean.getObject(MybatisSqlSessionFactoryBean.java:609)
	at com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(MybatisPlusAutoConfiguration.java:218)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:154)
	... 86 common frames omitted
Caused by: com.baomidou.mybatisplus.core.exceptions.MybatisPlusException: @TableId can't more than one in Class: "domain.com.zhonghe.system.SysNoticeDept".
	at com.baomidou.mybatisplus.core.toolkit.ExceptionUtils.mpe(ExceptionUtils.java:49)
	at com.baomidou.mybatisplus.core.metadata.TableInfoHelper.initTableFields(TableInfoHelper.java:307)
	at com.baomidou.mybatisplus.core.metadata.TableInfoHelper.initTableInfo(TableInfoHelper.java:176)
	at com.baomidou.mybatisplus.core.metadata.TableInfoHelper.initTableInfo(TableInfoHelper.java:153)
	at com.baomidou.mybatisplus.core.injector.AbstractSqlInjector.inspectInject(AbstractSqlInjector.java:48)
	at com.baomidou.mybatisplus.core.MybatisMapperAnnotationBuilder.parserInjector(MybatisMapperAnnotationBuilder.java:132)
	at com.baomidou.mybatisplus.core.MybatisMapperAnnotationBuilder.parse(MybatisMapperAnnotationBuilder.java:122)
	at com.baomidou.mybatisplus.core.MybatisMapperRegistry.addMapper(MybatisMapperRegistry.java:94)
	at com.baomidou.mybatisplus.core.MybatisConfiguration.addMapper(MybatisConfiguration.java:129)
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.bindMapperForNamespace(XMLMapperBuilder.java:432)
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.parse(XMLMapperBuilder.java:97)
	at com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean.buildSqlSessionFactory(MybatisSqlSessionFactoryBean.java:574)
	... 94 common frames omitted
2023-03-22 17:23:32 [main] ERROR o.s.test.context.TestContextManager - Caught exception while allowing TestExecutionListener [org.springframework.test.context.support.DependencyInjectionTestExecutionListener@1c7fd41f] to prepare test instance [com.zhonghe.generator.test.TestDemo@6cf47d05]
java.lang.IllegalStateException: Failed to load ApplicationContext
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:132)
	at org.springframework.test.context.support.DefaultTestContext.getApplicationContext(DefaultTestContext.java:124)
	at org.springframework.test.context.support.DependencyInjectionTestExecutionListener.injectDependencies(DependencyInjectionTestExecutionListener.java:118)
	at org.springframework.test.context.support.DependencyInjectionTestExecutionListener.prepareTestInstance(DependencyInjectionTestExecutionListener.java:83)
	at org.springframework.test.context.TestContextManager.prepareTestInstance(TestContextManager.java:248)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.createTest(SpringJUnit4ClassRunner.java:227)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner$1.runReflectiveCall(SpringJUnit4ClassRunner.java:289)
	at org.junit.internal.runners.model.ReflectiveCallable.run(ReflectiveCallable.java:12)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.methodBlock(SpringJUnit4ClassRunner.java:291)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:246)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.runChild(SpringJUnit4ClassRunner.java:97)
	at org.junit.runners.ParentRunner$4.run(ParentRunner.java:331)
	at org.junit.runners.ParentRunner$1.schedule(ParentRunner.java:79)
	at org.junit.runners.ParentRunner.runChildren(ParentRunner.java:329)
	at org.junit.runners.ParentRunner.access$100(ParentRunner.java:66)
	at org.junit.runners.ParentRunner$2.evaluate(ParentRunner.java:293)
	at org.springframework.test.context.junit4.statements.RunBeforeTestClassCallbacks.evaluate(RunBeforeTestClassCallbacks.java:61)
	at org.springframework.test.context.junit4.statements.RunAfterTestClassCallbacks.evaluate(RunAfterTestClassCallbacks.java:70)
	at org.junit.runners.ParentRunner$3.evaluate(ParentRunner.java:306)
	at org.junit.runners.ParentRunner.run(ParentRunner.java:413)
	at org.springframework.test.context.junit4.SpringJUnit4ClassRunner.run(SpringJUnit4ClassRunner.java:190)
	at org.junit.runner.JUnitCore.run(JUnitCore.java:137)
	at com.intellij.junit4.JUnit4IdeaTestRunner.startRunnerWithArgs(JUnit4IdeaTestRunner.java:69)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater$1.execute(IdeaTestRunner.java:38)
	at com.intellij.rt.execution.junit.TestsRepeater.repeat(TestsRepeater.java:11)
	at com.intellij.rt.junit.IdeaTestRunner$Repeater.startRunnerWithArgs(IdeaTestRunner.java:35)
	at com.intellij.rt.junit.JUnitStarter.prepareStreamsAndStart(JUnitStarter.java:235)
	at com.intellij.rt.junit.JUnitStarter.main(JUnitStarter.java:54)
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'genController' defined in file [/Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator/target/classes/com/ruoyi/generator/controller/GenController.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'genTableServiceImpl' defined in file [/Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator/target/classes/com/ruoyi/generator/service/GenTableServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'genTableMapper' defined in file [/Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator/target/classes/com/ruoyi/generator/mapper/GenTableMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception; nested exception is org.springframework.core.NestedIOException: Failed to parse mapping resource: 'file [/Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-system/target/classes/mapper/system/SysNoticeDeptMapper.xml]'; nested exception is com.baomidou.mybatisplus.core.exceptions.MybatisPlusException: @TableId can't more than one in Class: "domain.com.zhonghe.system.SysNoticeDept".
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:800)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:229)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1372)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1222)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:918)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:583)
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:145)
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:745)
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:420)
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:307)
	at org.springframework.boot.test.context.SpringBootContextLoader.loadContext(SpringBootContextLoader.java:144)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContextInternal(DefaultCacheAwareContextLoaderDelegate.java:99)
	at org.springframework.test.context.cache.DefaultCacheAwareContextLoaderDelegate.loadContext(DefaultCacheAwareContextLoaderDelegate.java:124)
	... 27 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'genTableServiceImpl' defined in file [/Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator/target/classes/com/ruoyi/generator/service/GenTableServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'genTableMapper' defined in file [/Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator/target/classes/com/ruoyi/generator/mapper/GenTableMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception; nested exception is org.springframework.core.NestedIOException: Failed to parse mapping resource: 'file [/Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-system/target/classes/mapper/system/SysNoticeDeptMapper.xml]'; nested exception is com.baomidou.mybatisplus.core.exceptions.MybatisPlusException: @TableId can't more than one in Class: "domain.com.zhonghe.system.SysNoticeDept".
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:800)
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:229)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1372)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1222)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:887)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 46 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'genTableMapper' defined in file [/Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator/target/classes/com/ruoyi/generator/mapper/GenTableMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception; nested exception is org.springframework.core.NestedIOException: Failed to parse mapping resource: 'file [/Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-system/target/classes/mapper/system/SysNoticeDeptMapper.xml]'; nested exception is com.baomidou.mybatisplus.core.exceptions.MybatisPlusException: @TableId can't more than one in Class: "domain.com.zhonghe.system.SysNoticeDept".
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByType(AbstractAutowireCapableBeanFactory.java:1534)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1417)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:887)
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:791)
	... 60 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/baomidou/mybatisplus/autoconfigure/MybatisPlusAutoConfiguration.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception; nested exception is org.springframework.core.NestedIOException: Failed to parse mapping resource: 'file [/Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-system/target/classes/mapper/system/SysNoticeDeptMapper.xml]'; nested exception is com.baomidou.mybatisplus.core.exceptions.MybatisPlusException: @TableId can't more than one in Class: "domain.com.zhonghe.system.SysNoticeDept".
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:658)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:638)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542)
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208)
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByType(AbstractAutowireCapableBeanFactory.java:1519)
	... 72 common frames omitted
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception; nested exception is org.springframework.core.NestedIOException: Failed to parse mapping resource: 'file [/Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-system/target/classes/mapper/system/SysNoticeDeptMapper.xml]'; nested exception is com.baomidou.mybatisplus.core.exceptions.MybatisPlusException: @TableId can't more than one in Class: "domain.com.zhonghe.system.SysNoticeDept".
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:185)
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:653)
	... 85 common frames omitted
Caused by: org.springframework.core.NestedIOException: Failed to parse mapping resource: 'file [/Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-system/target/classes/mapper/system/SysNoticeDeptMapper.xml]'; nested exception is com.baomidou.mybatisplus.core.exceptions.MybatisPlusException: @TableId can't more than one in Class: "domain.com.zhonghe.system.SysNoticeDept".
	at com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean.buildSqlSessionFactory(MybatisSqlSessionFactoryBean.java:576)
	at com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean.afterPropertiesSet(MybatisSqlSessionFactoryBean.java:445)
	at com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean.getObject(MybatisSqlSessionFactoryBean.java:609)
	at com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(MybatisPlusAutoConfiguration.java:218)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:566)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:154)
	... 86 common frames omitted
Caused by: com.baomidou.mybatisplus.core.exceptions.MybatisPlusException: @TableId can't more than one in Class: "domain.com.zhonghe.system.SysNoticeDept".
	at com.baomidou.mybatisplus.core.toolkit.ExceptionUtils.mpe(ExceptionUtils.java:49)
	at com.baomidou.mybatisplus.core.metadata.TableInfoHelper.initTableFields(TableInfoHelper.java:307)
	at com.baomidou.mybatisplus.core.metadata.TableInfoHelper.initTableInfo(TableInfoHelper.java:176)
	at com.baomidou.mybatisplus.core.metadata.TableInfoHelper.initTableInfo(TableInfoHelper.java:153)
	at com.baomidou.mybatisplus.core.injector.AbstractSqlInjector.inspectInject(AbstractSqlInjector.java:48)
	at com.baomidou.mybatisplus.core.MybatisMapperAnnotationBuilder.parserInjector(MybatisMapperAnnotationBuilder.java:132)
	at com.baomidou.mybatisplus.core.MybatisMapperAnnotationBuilder.parse(MybatisMapperAnnotationBuilder.java:122)
	at com.baomidou.mybatisplus.core.MybatisMapperRegistry.addMapper(MybatisMapperRegistry.java:94)
	at com.baomidou.mybatisplus.core.MybatisConfiguration.addMapper(MybatisConfiguration.java:129)
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.bindMapperForNamespace(XMLMapperBuilder.java:432)
	at org.apache.ibatis.builder.xml.XMLMapperBuilder.parse(XMLMapperBuilder.java:97)
	at com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean.buildSqlSessionFactory(MybatisSqlSessionFactoryBean.java:574)
	... 94 common frames omitted
2023-03-22 17:27:03 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Neither @ContextConfiguration nor @ContextHierarchy found for test class [com.zhonghe.generator.test.TestDemo], using SpringBootContextLoader
2023-03-22 17:27:03 [main] INFO  o.s.t.c.s.AbstractContextLoader - Could not detect default resource locations for test class [com.zhonghe.generator.test.TestDemo]: no resource found for suffixes {-context.xml, Context.groovy}.
2023-03-22 17:27:03 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Loaded default TestExecutionListener class names from location [META-INF/spring.factories]: [org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener, org.springframework.test.context.web.ServletTestExecutionListener, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener, org.springframework.test.context.event.ApplicationEventsTestExecutionListener, org.springframework.test.context.support.DependencyInjectionTestExecutionListener, org.springframework.test.context.support.DirtiesContextTestExecutionListener, org.springframework.test.context.transaction.TransactionalTestExecutionListener, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener, org.springframework.test.context.event.EventPublishingTestExecutionListener]
2023-03-22 17:27:03 [main] INFO  o.s.b.t.c.SpringBootTestContextBootstrapper - Using TestExecutionListeners: [org.springframework.test.context.web.ServletTestExecutionListener@ed3068a, org.springframework.test.context.support.DirtiesContextBeforeModesTestExecutionListener@7c2b6087, org.springframework.test.context.event.ApplicationEventsTestExecutionListener@3fffff43, org.springframework.boot.test.mock.mockito.MockitoTestExecutionListener@a8e6492, org.springframework.test.context.support.DependencyInjectionTestExecutionListener@1c7fd41f, org.springframework.test.context.support.DirtiesContextTestExecutionListener@3b77a04f, org.springframework.test.context.transaction.TransactionalTestExecutionListener@7b324585, org.springframework.test.context.jdbc.SqlScriptsTestExecutionListener@2e11485, org.springframework.test.context.event.EventPublishingTestExecutionListener@60dce7ea, org.springframework.boot.test.mock.mockito.ResetMocksTestExecutionListener@662f5666]
2023-03-22 17:27:03 [background-preinit] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.3.Final
2023-03-22 17:27:03 [main] INFO  com.zhonghe.generator.test.TestDemo - Starting TestDemo using Java 11.0.18 on lpgMacBook-Pro.lan with PID 8075 (started by lpg in /Users/<USER>/Documents/工作/中合通用平台/zhonghe-Vue-Plus/zhonghe-generator)
2023-03-22 17:27:03 [main] INFO  com.zhonghe.generator.test.TestDemo - The following 1 profile is active: "dev"
2023-03-22 17:27:05 [main] INFO  c.r.framework.config.JacksonConfig - 初始化 jackson 配置
2023-03-22 17:27:05 [main] INFO  c.ruoyi.framework.config.RedisConfig - 初始化 redis 配置
2023-03-22 17:27:05 [main] INFO  org.redisson.Version - Redisson 3.17.4
2023-03-22 17:27:05 [redisson-netty-2-3] INFO  o.r.c.p.MasterPubSubConnectionPool - 1 connections initialized for *************/*************:29080
2023-03-22 17:27:06 [redisson-netty-2-3] INFO  o.r.c.pool.MasterConnectionPool - 8 connections initialized for *************/*************:29080
2023-03-22 17:27:06 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-auto-1"]
2023-03-22 17:27:06 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2023-03-22 17:27:06 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.64]
2023-03-22 17:27:06 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2023-03-22 17:27:06 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource detect P6SPY plugin and enabled it
2023-03-22 17:27:08 [main] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1,master} inited
2023-03-22 17:27:08 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource - add a datasource named [master] success
2023-03-22 17:27:08 [main] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
2023-03-22 17:27:11 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-auto-1"]
2023-03-22 17:27:12 [main] INFO  com.zhonghe.generator.test.TestDemo - Started TestDemo in 8.752 seconds (JVM running for 9.253)
2023-03-22 17:27:12 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => minio
2023-03-22 17:27:12 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qiniu
2023-03-22 17:27:12 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => image
2023-03-22 17:27:12 [main] INFO  c.r.s.s.impl.SysOssConfigServiceImpl - 发布刷新OSS配置 => qcloud
2023-03-22 17:27:12 [main] INFO  factory.com.zhonghe.oss.OssFactory - 初始化OSS工厂
2023-03-22 17:27:12 [main] INFO  c.r.s.runner.SystemApplicationRunner - 初始化OSS配置成功
2023-03-22 17:27:12 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载参数缓存数据成功
2023-03-22 17:27:13 [main] INFO  c.r.s.runner.SystemApplicationRunner - 加载字典缓存数据成功
2023-03-22 17:27:13 [registrationTask1] WARN  d.c.b.a.c.r.ApplicationRegistrator - Failed to register application as Application(name=ZhongHe-Vue-Plus, managementUrl=http://**************:50409/actuator, healthUrl=http://**************:50409/actuator/health, serviceUrl=http://**************:50409/) at spring-boot-admin ([http://localhost:29050/admin/instances]): I/O error on POST request for "http://localhost:29050/admin/instances": Connect to localhost:29050 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused (Connection refused); nested exception is org.apache.http.conn.HttpHostConnectException: Connect to localhost:29050 [localhost/127.0.0.1, localhost/0:0:0:0:0:0:0:1] failed: Connection refused (Connection refused). Further attempts are logged on DEBUG level
2023-03-22 17:27:14 [SpringApplicationShutdownHook] INFO  c.r.f.manager.ShutdownManager - ====关闭后台任务任务线程池====
2023-03-22 17:27:14 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource start closing ....
2023-03-22 17:27:14 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closing ...
2023-03-22 17:27:14 [SpringApplicationShutdownHook] INFO  c.alibaba.druid.pool.DruidDataSource - {dataSource-1} closed
2023-03-22 17:27:14 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - dynamic-datasource all closed success,bye
