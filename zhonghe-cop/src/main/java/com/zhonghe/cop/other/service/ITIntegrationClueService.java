package com.zhonghe.cop.other.service;

import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.cop.other.domain.query.TIntegrationClueQuery;
import com.zhonghe.cop.other.domain.vo.TIntegrationClueVo;

/**
 * @Author: lpg
 * @Date: 2025/02/28
 * @Description:
 */
public interface ITIntegrationClueService {
    /**
     * 分页查询数据
     */
    TableDataInfo<TIntegrationClueVo> queryPageList(TIntegrationClueQuery query, PageQuery pageQuery);
}
