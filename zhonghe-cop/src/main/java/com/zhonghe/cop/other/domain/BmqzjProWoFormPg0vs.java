package com.zhonghe.cop.other.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

/**
 * 东莞市12345热线工单信息
 *
 * @TableName bmqzj_pro_wo_form_pg_0vs
 */
@Data
@TableName("public.bmqzj_pro_wo_form_pg_0vs")
public class BmqzjProWoFormPg0vs implements Serializable {

    private static final long serialVersionUID = -4670321738687578248L;
    /**
     * 数据id
     */
    private String dataId;
    /**
     * 主键ID
     */
    private Long id;
    /**
     * 受理单id
     */
    private String proShId;
    /**
     * 受理人id
     */
    private String sysUserId;
    /**
     * 受理人工号
     */
    private String sysUserNo;
    /**
     * 受理人名称
     */
    private String sysUserName;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 诉求来源
     */
    private Integer callType;
    /**
     * 区分和工作台的提交时间
     */
    private Date zcSubmitime;
    /**
     * 工单编号
     */
    private String code;
    /**
     * 工单标题
     */
    private String title;
    /**
     * 工单内容
     */
    private String content;
    /**
     * 配置的具体字典值
     */
    private String area;
    /**
     * 配置的具体字典值
     */
    private String street;
    /**
     * 事发地址-详细地址
     */
    private String scene;
    /**
     * 备注地址
     */
    private String sceneRemark;
    /**
     * 客户id
     */
    private String customerId;
    /**
     * 工单类型
     */
    private Integer type;
    /**
     * 联系人
     */
    private String linkmanName;
    /**
     * 事项id
     */
    private String actId;
    /**
     * 事项名称
     */
    private String actName;
    /**
     * 事项目录id
     */
    private String itemCatalogId;
    /**
     * 事项版本号
     */
    private Integer actVersion;
    /**
     * 事项所属部门id
     */
    private String actOrgId;
    /**
     * 五级点选
     */
    private String actArea;
    /**
     * 四级点选
     */
    private String actAffect;
    /**
     * 二级点选
     */
    private String actObjectType;
    /**
     * 三级点选
     */
    private String actSubObject;
    /**
     * 一级点选
     */
    private String actBusiness;
    /**
     * 事项业务点选描叙
     */
    private String actBusiRemark;
    /**
     * 事项关键字
     */
    private String actKeyWord;
    /**
     * 是否保密
     */
    private Integer isSecrecy;
    /**
     * 呼叫队列名
     */
    private String callQueue;
    /**
     * 是否公开
     */
    private String ispublic;
    /**
     * 紧急度
     */
    private Integer sla;
    /**
     * 读取突发原因
     */
    private String slaType;
    /**
     * 工单分类
     */
    private Integer formSort;
    /**
     * 分类主单id
     */
    private String sortWoId;
    /**
     * 工单状态
     */
    private Integer state;
    /**
     * 工单标记
     */
    private Integer formFlag;
    /**
     * 工单变化属性标识
     */
    private String proWoChangeSign;
    /**
     * 诉求人性质
     */
    private Integer formUserNature;
    /**
     * 部门处理结果满意度
     */
    private String approve;
    /**
     * 部门处理结果满意度渠道
     */
    private String approveSource;
    /**
     * 部门处理结果满意度回复时间
     */
    private Date approveTime;
    /**
     * 热线满意度
     */
    private String hotlineApprove;
    /**
     * 热线满意度评价时间
     */
    private Date hotlineApproveTime;
    /**
     * 热线时限
     */
    private Date hotHandleTime;
    /**
     * 处理时限
     */
    private String handleTime;
    /**
     * 监察状态
     */
    private String supervision;
    /**
     * 流程id
     */
    private String proId;
    /**
     * 工单进度百分比
     */
    private Integer progress;
    /**
     * 处理标志
     */
    private Integer visionFlag;
    /**
     * 当前部门
     */
    private String curOrgId;
    /**
     * 当前部门名称
     */
    private String curOrgName;
    /**
     * 办结时间
     */
    @ApiModelProperty("办结时间")
    private Date endTime;
    /**
     * 办结结果
     */
    @Size(max = -1, message = "编码长度不能超过-1")
    @ApiModelProperty("办结结果")
    @Length(max = -1, message = "编码长度不能超过-1")
    private String handleResult;
    /**
     * 处理部门id
     */
    @Size(max = 36, message = "编码长度不能超过36")
    @ApiModelProperty("处理部门id")
    @Length(max = 36, message = "编码长度不能超过36")
    private String handleOrgId;
    /**
     * 处理部门名称
     */
    @Size(max = 100, message = "编码长度不能超过100")
    @ApiModelProperty("处理部门名称")
    @Length(max = 100, message = "编码长度不能超过100")
    private String handleOrgName;
    /**
     * 状态更新时间
     */
    @ApiModelProperty("状态更新时间")
    private Date stateTime;
    /**
     * 是否签收
     */
    @ApiModelProperty("是否签收")
    private Integer isLock;
    /**
     * 签收人
     */
    @Size(max = 36, message = "编码长度不能超过36")
    @ApiModelProperty("签收人")
    @Length(max = 36, message = "编码长度不能超过36")
    private String lockUserId;
    /**
     * 签收人帐号
     */
    @Size(max = 30, message = "编码长度不能超过30")
    @ApiModelProperty("签收人帐号")
    @Length(max = 30, message = "编码长度不能超过30")
    private String lockUserNo;
    /**
     * 锁单人名称
     */
    @Size(max = 50, message = "编码长度不能超过50")
    @ApiModelProperty("锁单人名称")
    @Length(max = 50, message = "编码长度不能超过50")
    private String lockUserName;
    /**
     * 热线第一次审核时间
     */
    @ApiModelProperty("热线第一次审核时间")
    private Date firstShTime;
    /**
     * 延期待办机构ID
     */
    @Size(max = 36, message = "编码长度不能超过36")
    @ApiModelProperty("延期待办机构ID")
    @Length(max = 36, message = "编码长度不能超过36")
    private String delayOrgId;
    /**
     * 延期流程id
     */
    @Size(max = 36, message = "编码长度不能超过36")
    @ApiModelProperty("延期流程id")
    @Length(max = 36, message = "编码长度不能超过36")
    private String delayProid;
    /**
     * 延期标识
     */
    @ApiModelProperty("延期标识")
    private Integer delayFlag;
    /**
     * 延期流程待办角色id
     */
    @Size(max = 36, message = "编码长度不能超过36")
    @ApiModelProperty("延期流程待办角色id")
    @Length(max = 36, message = "编码长度不能超过36")
    private String delayGroupId;
    /**
     * 撤单结果标识作废
     */
    @Size(max = 20, message = "编码长度不能超过20")
    @ApiModelProperty("撤单结果标识作废")
    @Length(max = 20, message = "编码长度不能超过20")
    private String canncel;
    /**
     * 主流程待办角色id
     */
    @Size(max = 36, message = "编码长度不能超过36")
    @ApiModelProperty("主流程待办角色id")
    @Length(max = 36, message = "编码长度不能超过36")
    private String proGroupId;
    /**
     * 指定待办人id
     */
    @Size(max = 36, message = "编码长度不能超过36")
    @ApiModelProperty("指定待办人id")
    @Length(max = 36, message = "编码长度不能超过36")
    private String assignUserId;
    /**
     * 是否认证
     */
    @ApiModelProperty("是否认证")
    private Integer authentication;
    /**
     * 割接字段
     */
    @Size(max = 100, message = "编码长度不能超过100")
    @ApiModelProperty("割接字段")
    @Length(max = 100, message = "编码长度不能超过100")
    private String staff;
    /**
     * 是否抽取分析
     */
    @Size(max = 20, message = "编码长度不能超过20")
    @ApiModelProperty("是否抽取分析")
    @Length(max = 20, message = "编码长度不能超过20")
    private String isAiGet;
    /**
     * 工单标签
     */
    @Size(max = 100, message = "编码长度不能超过100")
    @ApiModelProperty("工单标签")
    @Length(max = 100, message = "编码长度不能超过100")
    private String formLabel;
    /**
     * 是否领导接电
     */
    @Size(max = 20, message = "编码长度不能超过20")
    @ApiModelProperty("是否领导接电")
    @Length(max = 20, message = "编码长度不能超过20")
    private String isLeaderCall;
    /**
     * 领导职位
     */
    @Size(max = 50, message = "编码长度不能超过50")
    @ApiModelProperty("领导职位")
    @Length(max = 50, message = "编码长度不能超过50")
    private String leaderPost;
    /**
     * 领导姓名
     */
    @Size(max = 50, message = "编码长度不能超过50")
    @ApiModelProperty("领导姓名")
    @Length(max = 50, message = "编码长度不能超过50")
    private String leaderName;
    /**
     * 归档原因
     */
    @Size(max = 20, message = "编码长度不能超过20")
    @ApiModelProperty("归档原因")
    @Length(max = 20, message = "编码长度不能超过20")
    private String endReason;
    /**
     * 转派市民诉求
     */
    @Size(max = 36, message = "编码长度不能超过36")
    @ApiModelProperty("转派市民诉求")
    @Length(max = 36, message = "编码长度不能超过36")
    private String trunSmAppeal;
    /**
     * 转派回复方式
     */
    @Size(max = 20, message = "编码长度不能超过20")
    @ApiModelProperty("转派回复方式")
    @Length(max = 20, message = "编码长度不能超过20")
    private String trunReply;
    /**
     * 是否联合审定
     */
    @ApiModelProperty("是否联合审定")
    private Integer isUnion;
    /**
     * 关联工单id
     */
    @Size(max = 200, message = "编码长度不能超过200")
    @ApiModelProperty("关联工单id")
    @Length(max = 200, message = "编码长度不能超过200")
    private String reProWoId;
    /**
     * 是否重办
     */
    @ApiModelProperty("是否重办")
    private Integer isReHandle;
    /**
     * 业务模版事项ID
     */
    @Size(max = 36, message = "编码长度不能超过36")
    @ApiModelProperty("业务模版事项ID")
    @Length(max = 36, message = "编码长度不能超过36")
    private String busTemplateId;
    /**
     * 模板版本号
     */
    @ApiModelProperty("模板版本号")
    private Integer busTemplateVersion;
    /**
     * 工单种类
     */
    @ApiModelProperty("工单种类")
    private Integer orderType;
    /**
     * 是否不允许再次退单
     */
    @ApiModelProperty("是否不允许再次退单")
    private Integer canBack;
    /**
     * 是否是疑难工单
     */
    @ApiModelProperty("是否是疑难工单")
    private Integer isDifficult;
    /**
     * 疑难工单类型
     */
    @Size(max = 20, message = "编码长度不能超过20")
    @ApiModelProperty("疑难工单类型")
    @Length(max = 20, message = "编码长度不能超过20")
    private String difficultType;
    /**
     * 是否城管事项类工单
     */
    @ApiModelProperty("是否城管事项类工单")
    private Integer isCitymanageAct;
    /**
     * 撤单流程id
     */
    @Size(max = 20, message = "编码长度不能超过20")
    @ApiModelProperty("撤单流程id")
    @Length(max = 20, message = "编码长度不能超过20")
    private String revokeProid;
    /**
     * 撤单流程待办角色id
     */
    @Size(max = 36, message = "编码长度不能超过36")
    @ApiModelProperty("撤单流程待办角色id")
    @Length(max = 36, message = "编码长度不能超过36")
    private String revokeGroupId;
    /**
     * 督办流程id
     */
    @Size(max = 20, message = "编码长度不能超过20")
    @ApiModelProperty("督办流程id")
    @Length(max = 20, message = "编码长度不能超过20")
    private String dbProcId;
    /**
     * 督办流程待办角色id
     */
    @Size(max = 36, message = "编码长度不能超过36")
    @ApiModelProperty("督办流程待办角色id")
    @Length(max = 36, message = "编码长度不能超过36")
    private String dbGroupId;
    /**
     * 处理期限工作日
     */
    @ApiModelProperty("处理期限工作日")
    private Integer handleWorkDay;
    /**
     * 热线派单职能局的时间
     */
    @ApiModelProperty("热线派单职能局的时间")
    private Date toZnjTime;
    /**
     * 转热线复核时间
     */
    @ApiModelProperty("转热线复核时间")
    private Date toRxfhTime;
    /**
     * 职能局回退热线时间
     */
    @ApiModelProperty("职能局回退热线时间")
    private Date backTime;
    /**
     * 是否现场踏勘
     */
    @ApiModelProperty("是否现场踏勘")
    private Integer isScout;
    /**
     * 回退机构id
     */
    @Size(max = 36, message = "编码长度不能超过36")
    @ApiModelProperty("回退机构id")
    @Length(max = 36, message = "编码长度不能超过36")
    private String backOrgId;
    /**
     * 回退机构名称
     */
    @Size(max = 50, message = "编码长度不能超过50")
    @ApiModelProperty("回退机构名称")
    @Length(max = 50, message = "编码长度不能超过50")
    private String backOrgName;
    /**
     * 当事人姓名
     */
    @Size(max = 50, message = "编码长度不能超过50")
    @ApiModelProperty("当事人姓名")
    @Length(max = 50, message = "编码长度不能超过50")
    private String partyName;
    /**
     * 代反映原因
     */
    @Size(max = 300, message = "编码长度不能超过300")
    @ApiModelProperty("代反映原因")
    @Length(max = 300, message = "编码长度不能超过300")
    private String partyReason;
    /**
     * 代理单位名称
     */
    @Size(max = 200, message = "编码长度不能超过200")
    @ApiModelProperty("代理单位名称")
    @Length(max = 200, message = "编码长度不能超过200")
    private String partyCompany;
    /**
     * 是否需要回访
     */
    @ApiModelProperty("是否需要回访")
    private Integer isReVisit;
    /**
     * 代反映原因类型
     */
    @ApiModelProperty("代反映原因类型")
    private Integer partyReasonType;
    /**
     * 涉事主体
     */
    @Size(max = 200, message = "编码长度不能超过200")
    @ApiModelProperty("涉事主体")
    @Length(max = 200, message = "编码长度不能超过200")
    private String thingSubject;
    /**
     * 主体地址
     */
    @Size(max = 200, message = "编码长度不能超过200")
    @ApiModelProperty("主体地址")
    @Length(max = 200, message = "编码长度不能超过200")
    private String subjectAddress;
    /**
     * 回复方式备注
     */
    @Size(max = 600, message = "编码长度不能超过600")
    @ApiModelProperty("回复方式备注")
    @Length(max = 600, message = "编码长度不能超过600")
    private String reviweRemark;
    /**
     * 语种
     */
    @Size(max = 50, message = "编码长度不能超过50")
    @ApiModelProperty("语种")
    @Length(max = 50, message = "编码长度不能超过50")
    private String language;
    /**
     * 地市
     */
    @Size(max = 50, message = "编码长度不能超过50")
    @ApiModelProperty("地市")
    @Length(max = 50, message = "编码长度不能超过50")
    private String callAreaCity;
    /**
     * 消费维权市民诉求
     */
    @Size(max = 50, message = "编码长度不能超过50")
    @ApiModelProperty("消费维权市民诉求")
    @Length(max = 50, message = "编码长度不能超过50")
    private String xwSmComment;
    /**
     * 消费维权市民自定义诉求
     */
    @Size(max = -1, message = "编码长度不能超过-1")
    @ApiModelProperty("消费维权市民自定义诉求")
    @Length(max = -1, message = "编码长度不能超过-1")
    private String xwSmZdyComment;
    /**
     * 非消费维权市民诉求
     */
    @Size(max = -1, message = "编码长度不能超过-1")
    @ApiModelProperty("非消费维权市民诉求")
    @Length(max = -1, message = "编码长度不能超过-1")
    private String noxwSmComment;
    /**
     * 建案件和调解事项ID
     */
    @Size(max = 36, message = "编码长度不能超过36")
    @ApiModelProperty("建案件和调解事项ID")
    @Length(max = 36, message = "编码长度不能超过36")
    private String gsActId;
    /**
     * 工商两建事项版本号
     */
    @ApiModelProperty("工商两建事项版本号")
    private Integer gsActVersion;
    /**
     * 热线批准来源
     */
    @Size(max = 10, message = "编码长度不能超过10")
    @ApiModelProperty("热线批准来源")
    @Length(max = 10, message = "编码长度不能超过10")
    private String hotlineApproveSource;
    /**
     * 是否两建工单
     */
    @ApiModelProperty("是否两建工单")
    private Integer isLj;
    /**
     * 预受理编号
     */
    @Size(max = 50, message = "编码长度不能超过50")
    @ApiModelProperty("预受理编号")
    @Length(max = 50, message = "编码长度不能超过50")
    private String leaderWoCode;
    /**
     * 预受理时间
     */
    @ApiModelProperty("预受理时间")
    private Date leaderCreateTime;
    /**
     * 市民身份
     */
    @ApiModelProperty("市民身份")
    private Integer customerIdentity;
    /**
     * 工单进程状态
     */
    @ApiModelProperty("工单进程状态")
    private Integer multiState;
    /**
     * 渠道市民原始诉求内容
     */
    @Size(max = -1, message = "编码长度不能超过-1")
    @ApiModelProperty("渠道市民原始诉求内容")
    @Length(max = -1, message = "编码长度不能超过-1")
    private String yssmContent;
    /**
     * 性别
     */
    @ApiModelProperty("性别")
    private Integer sex;
    /**
     * 工单智能分派类别
     */
    @Size(max = 10, message = "编码长度不能超过10")
    @ApiModelProperty("工单智能分派类别")
    @Length(max = 10, message = "编码长度不能超过10")
    private String woAisendType;
    /**
     * 工单特殊数据主题分类
     */
    @ApiModelProperty("工单特殊数据主题分类")
    private Integer specialDataType;
    /**
     * 微客服会话id
     */
    @Size(max = 32, message = "编码长度不能超过32")
    @ApiModelProperty("微客服会话id")
    @Length(max = 32, message = "编码长度不能超过32")
    private String sessionId;
    /**
     * 是否职能局退单
     */
    @ApiModelProperty("是否职能局退单")
    private Integer isOrgBack;
    /**
     * 会办流程标识
     */
    @Size(max = 10, message = "编码长度不能超过10")
    @ApiModelProperty("会办流程标识")
    @Length(max = 10, message = "编码长度不能超过10")
    private String isProMeet;
    /**
     * 职能局受理时间
     */
    @ApiModelProperty("职能局受理时间")
    private Date znslTime;
    /**
     * 是否第一次分派
     */
    @Size(max = 10, message = "编码长度不能超过10")
    @ApiModelProperty("是否第一次分派")
    @Length(max = 10, message = "编码长度不能超过10")
    private String isFirstCheck;
    /**
     * 受理发送短信标识
     */
    @ApiModelProperty("受理发送短信标识")
    private Integer isSendCheckSms;
    /**
     * 自动接单标识
     */
    @ApiModelProperty("自动接单标识")
    private Integer isAutoCheck;
    /**
     * 是否发送过话务满意度短信
     */
    @ApiModelProperty("是否发送过话务满意度短信")
    private Integer isSendHwsms;
    /**
     * 复核发送满意度短信
     */
    @ApiModelProperty("复核发送满意度短信")
    private Integer fhSendMydsms;
    /**
     * 民生管家工单标识
     */
    @ApiModelProperty("民生管家工单标识")
    private Integer msManageForm;
    /**
     * 标签组
     */
    @Size(max = -1, message = "编码长度不能超过-1")
    @ApiModelProperty("标签组")
    @Length(max = -1, message = "编码长度不能超过-1")
    private String actTagItemValues;
    /**
     * 自动派单解锁标识
     */
    @ApiModelProperty("自动派单解锁标识")
    private Integer autoAssignUnlock;
    /**
     * 是否签收
     */
    @ApiModelProperty("是否签收")
    private Integer isGsSign;
    /**
     * 是否紧急联动
     */
    @ApiModelProperty("是否紧急联动")
    private Integer isUrgencyUnion;
    /**
     * 微信表单的模板id
     */
    @Size(max = 36, message = "编码长度不能超过36")
    @ApiModelProperty("微信表单的模板id")
    @Length(max = 36, message = "编码长度不能超过36")
    private String lastModelId;
    /**
     * 首派责任工单
     */
    @ApiModelProperty("首派责任工单")
    private Integer firstAssignDuty;
    /**
     * 是否需要质检
     */
    @ApiModelProperty("是否需要质检")
    private Integer isCheck;
    /**
     * 新增时间
     */
    @ApiModelProperty("新增时间")
    private Date addTime;
    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date cdTime;
    /**
     * 批次号
     */
    @Size(max = 13, message = "编码长度不能超过13")
    @ApiModelProperty("批次号")
    @Length(max = 13, message = "编码长度不能超过13")
    private String cdBatch;
    /**
     * 操作方式
     */
    @Size(max = 1, message = "编码长度不能超过1")
    @ApiModelProperty("操作方式")
    @Length(max = 1, message = "编码长度不能超过1")
    private String cdOperation;
    /**
     * 扩展1
     */
    @Size(max = 200, message = "编码长度不能超过200")
    @ApiModelProperty("扩展1")
    @Length(max = 200, message = "编码长度不能超过200")
    private String extend1;
    /**
     * 扩展2
     */
    @Size(max = 200, message = "编码长度不能超过200")
    @ApiModelProperty("扩展2")
    @Length(max = 200, message = "编码长度不能超过200")
    private String extend2;
    /**
     * 全字段MD5
     */
    @Size(max = 40, message = "编码长度不能超过40")
    @ApiModelProperty("全字段MD5")
    @Length(max = 40, message = "编码长度不能超过40")
    private String dataMd5;
    /**
     * 数据区域
     */
    @Size(max = 12, message = "编码长度不能超过12")
    @ApiModelProperty("数据区域")
    @Length(max = 12, message = "编码长度不能超过12")
    private String dataArea;
    /**
     * 运营方技术字段,非业务字段,请忽略并勿使用
     */
    @ApiModelProperty("运营方技术字段,非业务字段,请忽略并勿使用")
    private Date dmpShareCdTimeIdatat;
    /**
     * 用数库自动配置的入库时间技术字段,非业务字段,请勿挂接
     */
    @ApiModelProperty("用数库自动配置的入库时间技术字段,非业务字段,请勿挂接")
    private Date dataSharingCdTimeIdatat;


}
