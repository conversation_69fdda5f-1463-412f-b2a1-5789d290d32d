package com.zhonghe.cop.other.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.cop.other.domain.TDgsnSpecialTaskQs;
import com.zhonghe.cop.other.domain.query.TDgsnSpecialTaskQsQuery;
import com.zhonghe.cop.other.domain.vo.TDgsnSpecialTaskQsVo;
import com.zhonghe.cop.other.mapper.TDgsnSpecialTaskQsMapper;
import com.zhonghe.cop.other.service.ITDgsnSpecialTaskQsService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * @Author: lpg
 * @Date: 2025/02/28
 * @Description:
 */
@RequiredArgsConstructor
@Service
@DS("data")
public class TDgsnSpecialTaskQsServiceImpl implements ITDgsnSpecialTaskQsService {
    private final TDgsnSpecialTaskQsMapper tDgsnSpecialTaskQsMapper;

    /**
     * 分页查询数据
     */
    @Override
    public TableDataInfo<TDgsnSpecialTaskQsVo> queryPageList(TDgsnSpecialTaskQsQuery query, PageQuery pageQuery) {
        LambdaQueryWrapper<TDgsnSpecialTaskQs> qw = new LambdaQueryWrapper<>();
        qw.like(StringUtils.isNotBlank(query.getTaskCode()), TDgsnSpecialTaskQs::getTaskCode, query.getTaskCode());
        qw.like(StringUtils.isNotBlank(query.getTitle()), TDgsnSpecialTaskQs::getTitle, query.getTitle());
        qw.like(StringUtils.isNotBlank(query.getEntityName()), TDgsnSpecialTaskQs::getEntityName, query.getEntityName());
        qw.eq(ObjectUtil.isNotNull(query.getTerm()), TDgsnSpecialTaskQs::getTerm, query.getTerm());
        qw.like(TDgsnSpecialTaskQs::getTitle, "实地核查涉税企业情况");
        qw.orderByDesc(TDgsnSpecialTaskQs::getCreateDate);
        Page<TDgsnSpecialTaskQsVo> page = tDgsnSpecialTaskQsMapper.selectVoPage(pageQuery.build(), qw);
        return TableDataInfo.build(page);
    }
}
