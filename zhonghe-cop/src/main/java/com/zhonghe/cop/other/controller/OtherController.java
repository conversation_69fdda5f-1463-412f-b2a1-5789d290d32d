package com.zhonghe.cop.other.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.cop.other.domain.query.BmqzjProWoFormFinishPgQuery;
import com.zhonghe.cop.other.domain.query.TDgsnSpecialTaskQsQuery;
import com.zhonghe.cop.other.domain.query.TIntegrationClueQuery;
import com.zhonghe.cop.other.domain.query.TTaskOrderNewQuery;
import com.zhonghe.cop.other.domain.vo.BmqzjProWoFormFinishPgVo;
import com.zhonghe.cop.other.domain.vo.TDgsnSpecialTaskQsVo;
import com.zhonghe.cop.other.domain.vo.TIntegrationClueVo;
import com.zhonghe.cop.other.domain.vo.TTaskOrderNewVo;
import com.zhonghe.cop.other.service.IBmqzjProWoFormFinishPgService;
import com.zhonghe.cop.other.service.ITDgsnSpecialTaskQsService;
import com.zhonghe.cop.other.service.ITIntegrationClueService;
import com.zhonghe.cop.other.service.ITTaskOrderNewService;
import com.zhonghe.cop.rep.domain.vo.CopDataVo;
import com.zhonghe.cop.rep.service.IOtherService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: lpg
 * @Date: 2025/02/28
 * @Description:
 */
@Validated
@Api(value = "12345和智网对接", tags = {"12345和智网对接"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/cop/other")
public class OtherController {
    private final IBmqzjProWoFormFinishPgService iBmqzjProWoFormFinishPgService;
    private final ITIntegrationClueService itIntegrationClueService;
    private final ITDgsnSpecialTaskQsService itDgsnSpecialTaskQsService;
    private final ITTaskOrderNewService itTaskOrderNewService;
    private final IOtherService otherService;

    /**
     * 查询12345热线
     */
    @SaCheckLogin
    @ApiOperation("查询12345热线")
    @GetMapping("/12345Hotline")
    public TableDataInfo<BmqzjProWoFormFinishPgVo> query12345Hotline(BmqzjProWoFormFinishPgQuery query, PageQuery pageQuery) {
        return iBmqzjProWoFormFinishPgService.queryPageList(query, pageQuery);
    }

    /**
     * 网格案事件(作废)
     */
    @Deprecated
    @SaCheckLogin
    @ApiOperation("网格案事件")
    @GetMapping("/gridCase")
    public TableDataInfo<TIntegrationClueVo> queryGridCase(TIntegrationClueQuery query, PageQuery pageQuery) {
        return itIntegrationClueService.queryPageList(query, pageQuery);
    }

    /**
     * 专项任务
     */
    @SaCheckLogin
    @ApiOperation("专项任务")
    @GetMapping("/specialTask")
    public TableDataInfo<TDgsnSpecialTaskQsVo> querySpecialTask(TDgsnSpecialTaskQsQuery query, PageQuery pageQuery) {
        return itDgsnSpecialTaskQsService.queryPageList(query, pageQuery);
    }

    /**
     * 巡查工单
     */
    @SaCheckLogin
    @ApiOperation("巡查工单")
    @GetMapping("/patrolWorkOrder")
    public TableDataInfo<TTaskOrderNewVo> queryPatrolWorkOrder(TTaskOrderNewQuery query,
                                                               PageQuery pageQuery) {
        return itTaskOrderNewService.queryPageList(query, pageQuery);
    }

    /**
     * (New)通过企业名称查数据
     */
    @SaCheckLogin
    @ApiOperation("(New)通过企业名称查数据")
    @GetMapping("/queryByCopName")
    public R<CopDataVo> queryByCopName(@RequestParam String copName) {
        return R.ok(otherService.queryByCopName(copName));
    }


}
