package com.zhonghe.cop.other.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.cop.other.domain.TIntegrationClue;
import com.zhonghe.cop.other.domain.query.TIntegrationClueQuery;
import com.zhonghe.cop.other.domain.vo.TIntegrationClueVo;
import com.zhonghe.cop.other.mapper.TIntegrationClueMapper;
import com.zhonghe.cop.other.service.ITIntegrationClueService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * @Author: lpg
 * @Date: 2025/02/28
 * @Description:
 */
@RequiredArgsConstructor
@Service
@DS("data")
public class ITIntegrationClueServiceImpl implements ITIntegrationClueService {
    private final TIntegrationClueMapper tIntegrationClueMapper;

    @Override
    public TableDataInfo<TIntegrationClueVo> queryPageList(TIntegrationClueQuery query, PageQuery pageQuery) {
        LambdaQueryWrapper<TIntegrationClue> qw = new LambdaQueryWrapper<>();
        qw.like(StringUtils.isNotBlank(query.getDangerId()), TIntegrationClue::getDangerId, query.getDangerId());
        qw.like(StringUtils.isNotBlank(query.getSubjectName()), TIntegrationClue::getSubjectName, query.getSubjectName());
        qw.like(StringUtils.isNotBlank(query.getBuildAddress()), TIntegrationClue::getBuildAddress, query.getBuildAddress());
        qw.eq(ObjectUtil.isNotNull(query.getVerTerm()), TIntegrationClue::getVerTerm, query.getVerTerm());
        qw.orderByDesc(TIntegrationClue::getCreateDate);
        Page<TIntegrationClueVo> page = tIntegrationClueMapper.selectVoPage(pageQuery.build(), qw);
        return TableDataInfo.build(page);
    }
}
