package com.zhonghe.cop.other.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author: lpg
 * @Date: 2025/02/28
 * @Description:
 */
@Data
@TableName("public.t_dgsn_special_task_qs")
public class TDgsnSpecialTaskQs implements Serializable {
    private static final long serialVersionUID = 2474053621263250948L;
    /**
     * 主键
     */
    @TableId
    private String zjid;

    /**
     * 任务批次号
     */
    private String taskCode;

    /**
     * 任务标题
     */
    private String title;

    /**
     * 网格
     */
    private String gridId;

    /**
     * 任务数
     */
    private String taskCount;

    /**
     * 任务级别
     */
    private String taskLevel;

    /**
     * 任务描述
     */
    private String taskDesc;

    /**
     * 任务类型
     */
    private String taskType;

    /**
     * 部门
     */
    private String deptId;

    /**
     * 处理期限
     */
    private Date term;

    /**
     * 任务对象
     */
    private String taskObject;

    /**
     * 数据来源
     */
    private String dataSource;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 创建人姓名
     */
    private String createName;

    /**
     * 更新人姓名
     */
    private String updateName;

    /**
     * 删除标志
     */
    private String delFlag;

    /**
     * 是否机动队 0 否 1 是
     */
    private String mobileTeams;

    /**
     * 任务性质 1 单次检查 2 持续检查
     */
    private String taskNature;

    /**
     * 是否草稿 0 否 1 是
     */
    private String draftFlag;

    /**
     * 启停标志 0启动 1停止
     */
    private String stopFlag;

    /**
     * 发放方式 1：精确发放 2：范围发放
     */
    private String grantWay;

    /**
     * 巡查事项json格式
     */
    private String taskItem;

    /**
     * 任务形式  1:对象式发布 2:台账式发布
     */
    private String taskForm;

    /**
     * 是否选择任务对象  0:否 1:是
     */
    private String chooseTarget;

    /**
     * 导入的文件名
     */
    private String importFileName;

    /**
     * etl日期
     */
    private String etlDt;

    /**
     * 重跑标识
     */
    private String isReload;

    /**
     * 重跑时间
     */
    private Date sysReloadDt;

    /**
     * 数据日期
     */
    private String datDt;

    /**
     * 质量稽查标签
     */
    private String gldmQutyFlag;

    /**
     * 开始时间
     */
    private String gldmStaDt;

    /**
     * 结束时间
     */
    private String gldmEndDt;

    /**
     * 是否当前记录
     */
    private String isCurrent;

    /**
     * 非业务主键id
     */
    private long id;

    /**
     * 新增时间
     */
    private Date addTime;

    /**
     * 同步时间
     */
    private Date cdTime;

    /**
     * 批次号
     */
    private String cdBatch;

    /**
     * 增量标识
     */
    private String cdOperation;

    /**
     * 实体id
     */
    private String entityId;

    /**
     * 实体名称
     */
    private String entityName;

    /**
     * 运营方技术字段,非业务字段,请忽略并勿使用
     */
    private Date dmpShareCdTimeIdatat;

    /**
     * 用数库自动配置的入库时间技术字段,非业务字段,请勿挂接
     */
    private Date dataSharingCdTimeIdatat;
}
