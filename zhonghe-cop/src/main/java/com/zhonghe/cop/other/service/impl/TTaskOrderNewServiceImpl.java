package com.zhonghe.cop.other.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.cop.other.domain.TTaskOrderNew;
import com.zhonghe.cop.other.domain.query.TTaskOrderNewQuery;
import com.zhonghe.cop.other.domain.vo.TTaskOrderNewVo;
import com.zhonghe.cop.other.mapper.TTaskOrderNewMapper;
import com.zhonghe.cop.other.service.ITTaskOrderNewService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * @Author: lpg
 * @Date: 2025/02/28
 * @Description:
 */
@RequiredArgsConstructor
@Service
@DS("data")
public class TTaskOrderNewServiceImpl implements ITTaskOrderNewService {
    private final TTaskOrderNewMapper tTaskOrderNewMapper;

    /**
     * 分页查询数据
     */
    @Override
    public TableDataInfo<TTaskOrderNewVo> queryPageList(TTaskOrderNewQuery query, PageQuery pageQuery) {
        LambdaQueryWrapper<TTaskOrderNew> qw = new LambdaQueryWrapper<>();
        qw.like(StringUtils.isNotBlank(query.getOrderNum()), TTaskOrderNew::getOrderNum, query.getOrderNum());
        qw.like(StringUtils.isNotBlank(query.getOrderName()), TTaskOrderNew::getOrderName, query.getOrderName());
        qw.like(StringUtils.isNotBlank(query.getSubjectName()), TTaskOrderNew::getSubjectName, query.getSubjectName());
        qw.like(StringUtils.isNotBlank(query.getSubjectType()), TTaskOrderNew::getSubjectType, query.getSubjectType());
        qw.eq(StringUtils.isNotBlank(query.getCheckState()), TTaskOrderNew::getCheckState, query.getCheckState());
        qw.eq(ObjectUtil.isNotNull(query.getCheckEndDate()), TTaskOrderNew::getCheckEndDate, query.getCheckEndDate());
        qw.like(TTaskOrderNew::getItemClassName, "税收检查");
        qw.like(TTaskOrderNew::getGridNameZ, "石龙镇");
        qw.orderByDesc(TTaskOrderNew::getCreateDate);
        IPage<TTaskOrderNewVo> page = tTaskOrderNewMapper.selectVoPage(pageQuery.build(), qw);
        return TableDataInfo.build(page);
    }
}
