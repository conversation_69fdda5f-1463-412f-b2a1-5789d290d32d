package com.zhonghe.cop.other.service;

import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.cop.other.domain.query.TTaskOrderNewQuery;
import com.zhonghe.cop.other.domain.vo.TTaskOrderNewVo;

/**
 * @Author: lpg
 * @Date: 2025/02/28
 * @Description:
 */
public interface ITTaskOrderNewService {
    /**
     * 分页查询数据
     */
    TableDataInfo<TTaskOrderNewVo> queryPageList(TTaskOrderNewQuery query, PageQuery pageQuery);
}
