package com.zhonghe.cop.other.domain;

/**
 * @Author: lpg
 * @Date: 2025/02/28
 * @Description:
 */

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 东莞市12345热线已办结工单信息
 */
@Data
@TableName("public.bmqzj_pro_wo_form_finish_pg")
public class BmqzjProWoFormFinishPg implements Serializable {

    private static final long serialVersionUID = 5554504680155093735L;
    /**
     * 主键
     */
    @TableId(value = "data_id", type = com.baomidou.mybatisplus.annotation.IdType.ASSIGN_UUID)
    private String dataId;

    /**
     * 主键
     */
    private String hisId;

    /**
     * 受理单id
     */
    private String proShId;

    /**
     * 受理人id
     */
    private String sysUserId;

    /**
     * 受理人工号
     */
    private String sysUserNo;

    /**
     * 受理人名称
     */
    private String sysUserName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 诉求来源：字典值，数字
     */
    private Integer callType;

    /**
     * 区分和工作台的提交时间
     */
    private Date zcSubmitime;

    /**
     * 工单编号
     */
    private String code;

    /**
     * 工单标题
     */
    private String title;

    /**
     * 工单内容
     */
    private String content;

    /**
     * 配置的具体字典值
     */
    private String area;

    /**
     * 配置的具体字典值
     */
    private String street;

    /**
     * 事发地址-详细地址
     */
    private String scene;

    /**
     * 备注地址
     */
    private String sceneRemark;

    /**
     * 客户id
     */
    private String customerId;

    /**
     * 0-咨询，1-投诉，2-举报，3-建议，4-表扬，5-骚扰-字典配置
     */
    private Integer type;

    /**
     * 联系人
     */
    private String linkmanName;

    /**
     * 事项id
     */
    private String actId;

    /**
     * 事项名称
     */
    private String actName;

    /**
     * 事项目录id
     */
    private String itemCatalogId;

    /**
     * 事项版本号
     */
    private Integer actVersion;

    /**
     * 事项所属部门id
     */
    private String actOrgId;

    /**
     * 事项影响情况
     */
    private String actArea;

    /**
     * 事项影响情况
     */
    private String actAffect;

    /**
     * 事项对象分类
     */
    private String actObjectType;

    /**
     * 事项提交对象
     */
    private String actSubObject;

    /**
     * 事项业务点选
     */
    private String actBusiness;

    /**
     * 事项业务点选描叙
     */
    private String actBusiRemark;

    /**
     * 事项关键字：多个就逗号隔开
     */
    private String actKeyWord;

    /**
     * 默认是0：不保密，1-保密
     */
    private Integer isSecrecy;

    /**
     * 呼叫队列名
     */
    private String callQueue;

    /**
     * 来电号码
     */
    private String callNo;

    /**
     * 呼叫人称呼
     */
    private String callName;

    /**
     * n-不公开，y-公开
     */

    private String ispublic;

    /**
     * 紧急度，默认是1：非突发，2-紧急，3-突发
     */

    private Integer sla;

    /**
     * 读取突发原因字典值
     */

    private String slaType;

    /**
     * 维护到字典表：0-主单  默认1-催办单，2-追加单，3-撤单，6-多媒体子单
     */

    private Integer formSort;

    /**
     * 分类主单id
     */

    private String sortWoId;

    /**
     * 参考字典值  -类型  gdzt，20：未锁定，21：已锁定，22：待完善，23：已完善，24：未回复，25：已关闭，26：已发布，27：已失效
     */
    private Integer state;

    /**
     * 0-待补充材料，1-待协调处理--参考字典值
     */
    private Integer formFlag;

    /**
     * 工单变化属性标识：1-追加，2-撤单，3-延期，4-修改，5-补充
     */
    private String proWoChangeSign;

    /**
     * 诉求人性质：1-个人，2-企业-字典值
     */

    private Integer formUserNature;

    /**
     * 部门处理结果满意度
     */
    private String approve;

    /**
     * IVR-话务，SMS：短信，WEB：网站，WEIXIN：微信，APP：APP渠道
     */
    private String approveSource;

    /**
     * 部门处理结果满意度回复时间
     */
    private Date approveTime;

    /**
     * 热线满意度
     */
    private String hotlineApprove;

    /**
     * IVR-话务，SMS：短信
     */

    private String hotlineApproveSource;

    /**
     * 热线满意度评价时间
     */
    private Date hotlineApproveTime;

    /**
     * 热线时限
     */
    private Date hotHandleTime;

    /**
     * 处理时限
     */
    private String handleTime;

    /**
     * 0-告警1-预警 字典值jczt
     */
    private String supervision;

    /**
     * 主工单id
     */
    private String proId;

    /**
     * 工单进度百分比
     */
    private Integer progress;

    /**
     * 0-默认，未派发，1-职能局审核，2-职能局处理
     */
    private Integer visionFlag;

    /**
     * 当前部门
     */
    private String curOrgId;

    /**
     * 当前部门名称
     */
    private String curOrgName;

    /**
     * 办结时间
     */
    private Date endTime;

    /**
     * 办结结果
     */
    private String handleResult;

    /**
     * 处理部门id
     */
    private String handleOrgId;

    /**
     * 处理部门名称
     */
    private String handleOrgName;

    /**
     * 状态更新时间
     */
    private Date stateTime;

    /**
     * 是否签收，0-否（默认），1-是
     */

    private Integer isLock;

    /**
     * 签收人
     */
    private String lockUserId;

    /**
     * 签收人帐号
     */
    private String lockUserNo;

    /**
     * 锁单人名称
     */

    private String lockUserName;

    /**
     * 热线第一次审核时间
     */
    private Date firstShTime;

    /**
     * 延期待办机构ID
     */
    private String delayOrgId;

    /**
     * 延期流程id
     */
    private String delayProid;

    /**
     * 延期标识：1-延期中，2-同意，3-拒绝
     */
    private Integer delayFlag;

    /**
     * 延期流程待办角色id
     */

    private String delayGroupId;

    /**
     * 主流程待办角色id
     */
    private String proGroupId;

    /**
     * 指定待办人id
     */
    private String assignUserId;

    /**
     * 0-默认，0-非认证，1-认证
     */
    private Integer authentication;

    /**
     * 由割接人员填写
     */
    private String staff;

    /**
     * 是否抽取分析：0-未抽取（默认），1-已抽取
     */
    private String isAiGet;

    /**
     * 工单标签：1-英语，2-录音推荐，取字典
     */
    private String formLabel;

    /**
     * 是否领导接电：默认0-非领导接电，1-领导接电
     */
    private String isLeaderCall;

    /**
     * 领导职位
     */
    private String leaderPost;

    /**
     * 领导姓名
     */
    private String leaderName;

    /**
     * 归档原因：读取配置字典
     */
    private String endReason;

    /**
     * 转派市民诉求：读取字典值
     */
    private String trunSmAppeal;

    /**
     * 转派回复方式：读取字典值
     */
    private String trunReply;

    /**
     * 是否联合审定：默认是0，1-是
     */
    private Integer isUnion;

    /**
     * 重办主单id
     */
    private String reProWoId;

    /**
     * 是否重办：0-否，1-是。默认是0
     */
    private Integer isReHandle;

    /**
     * 业务模版事项ID
     */
    private String busTemplateId;

    /**
     * 模板版本号
     */
    private Integer busTemplateVersion;

    /**
     * 1-热线会办
     */
    private Integer orderType;

    /**
     * 不允许再次退单 ：1 -不能 0 -能
     */
    private Integer canBack;

    /**
     * 是否是疑难工单 ： 0-否，1-是。 默认是0
     */
    private Integer isDifficult;

    /**
     * 疑难工单类型
     */
    private String difficultType;

    /**
     * 是否城管事项类工单：0-否，1-是，默认0
     */
    private Integer isCitymanageAct;

    /**
     * 撤单流程id
     */
    private String revokeProid;

    /**
     * 撤单流程待办角色id
     */
    private String revokeGroupId;

    /**
     * 督办流程id
     */

    private String dbProcId;

    /**
     * 督办流程待办角色id
     */

    private String dbGroupId;

    /**
     * 处理期限工作日
     */

    private Integer handleWorkDay;

    /**
     * 热线派单职能局的时间
     */
    private Date toZnjTime;

    /**
     * 转热线复核时间
     */
    private Date toRxfhTime;

    /**
     * 职能局回退热线时间
     */
    private Date backTime;

    /**
     * 是否现场踏勘，1-是，0-否。默认：0
     */
    private Integer isScout;

    /**
     * 回退机构id
     */
    private String backOrgId;

    /**
     * 回退机构名称
     */
    private String backOrgName;

    /**
     * 地市
     */
    private String callAreaCity;

    /**
     * 语种
     */
    private String language;

    /**
     * 当事人姓名
     */
    private String partyName;

    /**
     * 当事人证件类型
     */
    private String partyCertType;

    /**
     * 当事人证件号码
     */
    private String partyCertNo;

    /**
     * 代反映原因类型:1-行为障碍,2-语言障碍,3-律师来电,4-自定义,字典值：dfyyy
     */
    private Integer partyReasonType;

    /**
     * 代反映原因
     */
    private String partyReason;

    /**
     * 代理单位名称
     */
    private String partyCompany;

    /**
     * 是否需要回访：1-是，0-否。默认是0
     */
    private Integer isReVisit;

    /**
     * 回复方式备注
     */
    private String reviweRemark;

    /**
     * 涉事主题
     */
    private String thingSubject;

    /**
     * 主体地址
     */
    private String subjectAddress;

    /**
     * 消费维权市民诉求
     */
    private String xwSmComment;

    /**
     * 非消费维权市民诉求
     */
    private String noxwSmComment;

    /**
     * 呼叫id
     */
    private String callId;

    /**
     * 是否两建工单：1-是，0-否。默认是0
     */
    private Integer isLj;

    /**
     * 消费维权市民自定义诉求
     */
    private String xwSmZdyComment;

    /**
     * 工商两建事项版本号
     */
    private Integer gsActVersion;

    /**
     * 市民身份：1-当事人  2-代理人 3- 单位经办人
     */
    private Integer customerIdentity;

    /**
     * 预受理编号
     */
    private String leaderWoCode;

    /**
     * 渠道市民原始诉求内容
     */
    private String yssmContent;

    /**
     * 两建事项id
     */
    private String gsActId;

    /**
     * 证件类型：字典值：zjlx
     */
    private String certType;

    /**
     * 证件号码
     */

    private String certCode;

    /**
     * 性别:1-男，2-女
     */
    private String sex;

    /**
     * 工单特殊数据主题分类 1:不忘初心，牢记使命 2：xxx
     */
    private String specialDataType;

    /**
     * 微客服会话id
     */
    private String sessionId;

    /**
     * 是否职能局退单：1-是，0-否，默认0
     */
    private Integer isOrgBack;

    /**
     * 会办流程标识：MEET：会办，MASTER：主办，默认是MASTER
     */
    private String isProMeet;

    /**
     * 热线分派后，该值为Y，部门受理一次后，该值为N
     */
    private String isFirstCheck;

    /**
     * 受理发送短信标识：1-是，0-否，默认0
     */
    private String isSendCheckSms;

    /**
     * 自动接单标识：1-是，0-否，默认0
     */
    private String isAutoCheck;

    /**
     * 是否发送过话务满意度短信
     */
    private String isSendHwsms;

    /**
     * 复核发送满意度短信，0-不发送，1-发送
     */
    private String fhSendMydsms;

    /**
     * 民生管家工单标识：1-是，0-否
     */
    private String msManageForm;

    /**
     * 标签组
     */
    private String actTagItemValues;

    /**
     * 自动派单解锁标识：0-默认，1-自动派单解锁
     */
    private String autoAssignUnlock;

    /**
     * 是否紧急联动：0-否，1-是，默认0
     */
    private Integer isUrgencyUnion;

    /**
     * 微信表单的模板id
     */
    private String lastModelId;

    /**
     * 是否签收:1-已签收保存，0-未签收保存，2-已反馈
     */
    private String isGsSign;

    /**
     * 首派责任工单：0-否，1-是，默认0
     */
    private String firstAssignDuty;

    /**
     * 新增时间
     */
    private Date addTime;

    /**
     * 更新时间
     */
    private Date cdTime;

    /**
     * 批次号
     */
    private String cdBatch;

    /**
     * 操作方式
     */
    private String cdOperation;

    /**
     * 扩展1
     */
    private String extend1;

    /**
     * 扩展2
     */
    private String extend2;

    /**
     * 全字段MD5
     */

    private String dataMd5;

    /**
     * 数据区域
     */
    private String dataArea;

    /**
     * 运营方技术字段,非业务字段,请忽略并勿使用
     */
    private String dmpShareCdTimeIdatat;

    /**
     * 用数库自动配置的入库时间技术字段,非业务字段,请勿挂接
     */
    private String dataSharingCdTimeIdatat;
}
