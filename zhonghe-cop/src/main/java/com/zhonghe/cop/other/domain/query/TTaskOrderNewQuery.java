package com.zhonghe.cop.other.domain.query;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author: lpg
 * @Date: 2025/02/28
 * @Description:
 */
@Data
public class TTaskOrderNewQuery implements Serializable {
    private static final long serialVersionUID = 6448650520158021238L;
    /**
     * 主键
     */
    private String zjid;

    /**
     * 主体id
     */
    private String subjectId;

    /**
     * 工单名称（用部门名称前缀命名）
     */
    private String orderName;

    /**
     * 所属网格id
     */
    private String gridId;

    /**
     * 工单流水号
     */
    private String orderNum;

    /**
     * 巡查状态
     */
    private String checkState;

    /**
     * 生成日期
     */
    private Date orderCreateDate;

    /**
     * 巡查期限（截至日期）
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date checkEndDate;

    /**
     * 创建人id
     */
    private String createBy;

    /**
     * 创建人姓名
     */
    private String createName;

    /**
     * 创建时间
     */
    private Date createDate;

    /**
     * 更新人id
     */
    private String updateBy;

    /**
     * 更新人姓名
     */
    private String updateName;

    /**
     * 更新时间
     */
    private Date updateDate;

    /**
     * 删除标识 0：未删除  1：已删除
     */
    @TableField
    private String delFlag;

    /**
     * 部门ID
     */
    private String deptId;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 首检人ID
     */
    private String firstUserId;

    /**
     * 首检人姓名
     */
    private String firstUserName;

    /**
     * 最后检查人ID
     */
    private String lastUserId;

    /**
     * 最后检查人姓名
     */
    private String lastUserName;

    /**
     * 检查次数
     */
    private int checkNum;

    /**
     * 生成隐患数
     */
    private int dangerNum;

    /**
     * 是否产生隐患
     */
    private String isHaveDanger;

    /**
     * 开门情况
     */
    private String openDoorState;

    /**
     * 提醒状态
     */
    private String remindState;

    /**
     * 主体类型
     */
    private String subjectType;

    /**
     * 主体名称
     */
    private String subjectName;

    /**
     * 建筑ID
     */
    private String buildId;

    /**
     * 建筑地址
     */
    private String buildAddress;

    /**
     * 事项分类ID
     */
    private String itemClassId;

    /**
     * 事项分类名称
     */
    private String itemClassName;

    /**
     * 作业标签ID
     */
    private String labelId;

    /**
     * 首检时间
     */
    private Date firstCheckDate;

    /**
     * 最后检查时间
     */
    private Date lastCheckDate;

    /**
     * 工单级别
     */
    private String orderGrade;

    /**
     * 工单批次
     */
    private String orderBatch;

    /**
     * 作业标签名称（多个中间以,隔开）
     */
    private String labelName;

    /**
     * 处理人电话
     */
    private String dealTel;

    /**
     * 处理机构
     */
    private String dealOrg;

    /**
     * 描述
     */
    private String remakes;

    /**
     * 取证文件
     */
    private String picFileUrls;

    /**
     * 网格名称
     */
    private String gridNameZ;

    /**
     * 网格id
     */
    private String townId;

    /**
     * 非业务主键
     */
    private int id;

    /**
     * 新增时间
     */
    private Date addTime;

    /**
     * 增量标识
     */
    private String cdOperation;

    /**
     * 增量时间
     */
    private Date cdTime;

    /**
     * 批次号
     */
    private String cdBatch;

    /**
     * 运营方技术字段,非业务字段,请忽略并勿使用
     */
    private Date dmpShareCdTimeIdatat;

    /**
     * 用数库自动配置的入库时间技术字段,非业务字段,请勿挂接
     */
    private Date dataSharingCdTimeIdatat;

}
