package com.zhonghe.cop.other.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhonghe.common.core.mapper.BaseMapperPlus;
import com.zhonghe.cop.other.domain.BmqzjProWoFormFinishPg;
import com.zhonghe.cop.other.domain.TIntegrationClue;
import com.zhonghe.cop.other.domain.vo.BmqzjProWoFormFinishPgVo;
import com.zhonghe.cop.other.domain.vo.TIntegrationClueVo;
import org.apache.ibatis.annotations.Param;

/**
 * @Author: lpg
 * @Date: 2025/02/28
 * @Description:
 */
public interface TIntegrationClueMapper extends BaseMapperPlus<TIntegrationClueMapper,
    TIntegrationClue, TIntegrationClueVo> {
    /**
     * 分页查询数据
     *
     * @param query
     * @param build
     * @return
     */
    Page<BmqzjProWoFormFinishPgVo> selectPageList(@Param(Constants.WRAPPER) Wrapper<BmqzjProWoFormFinishPg> query,
                                                  Page<BmqzjProWoFormFinishPg> build);
}
