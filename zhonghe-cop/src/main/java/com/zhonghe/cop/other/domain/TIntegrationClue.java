package com.zhonghe.cop.other.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author: lpg
 * @Date: 2025/02/28
 * @Description:
 */
@Data
@TableName("public.t_integration_clue")
public class TIntegrationClue implements Serializable {
    private static final long serialVersionUID = 4526046963033527076L;
    /**
     * 案事件id
     */
    @TableId
    private String dangerId;

    /**
     * 主体id
     */
    private String subjectId;

    /**
     * 主体名称
     */
    private String subjectName;

    /**
     * 建筑物地址
     */
    private String buildAddress;

    /**
     * 经度
     */
    private String lng;

    /**
     * 纬度
     */
    private String lat;

    /**
     * 隐患描述
     */
    private String dangerDescribe;

    /**
     * 处置部门编码
     */
    private String disposalDeptNo;

    /**
     * 处置部门名称
     */
    private String disposalDeptName;

    /**
     * 处置期限
     */
    private int disPeriod;

    /**
     * 处理人姓名
     */
    private String createName;

    /**
     * 语音路径
     */
    private String soundUrl;

    /**
     * 图片路径
     */
    private String imageUrl;

    /**
     * 处置截止日期
     */
    private Date verTerm;

    /**
     * 生成时间
     */
    private Date createDate;

    /**
     * 代理主键
     */
    private String dsesUuid;

    /**
     * 新增时间
     */
    private Date addTime;

    /**
     * 增量标识
     */
    private String cdOperation;

    /**
     * 增量时间
     */
    private Date cdTime;

    /**
     * 批次号
     */
    private String cdBatch;

    /**
     * 非业务主键
     */
    private int id;

    /**
     * 运营方技术字段,非业务字段,请忽略并勿使用
     */
    private Date dmpShareCdTimeIdatat;

    /**
     * 用数库自动配置的入库时间技术字段,非业务字段,请勿挂接
     */
    private Date dataSharingCdTimeIdatat;


}
