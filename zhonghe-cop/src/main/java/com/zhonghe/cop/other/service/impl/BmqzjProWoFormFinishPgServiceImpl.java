package com.zhonghe.cop.other.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.cop.other.domain.BmqzjProWoFormFinishPg;
import com.zhonghe.cop.other.domain.query.BmqzjProWoFormFinishPgQuery;
import com.zhonghe.cop.other.domain.vo.BmqzjProWoFormFinishPgVo;
import com.zhonghe.cop.other.mapper.BmqzjProWoFormFinishPgMapper;
import com.zhonghe.cop.other.service.IBmqzjProWoFormFinishPgService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * @Author: lpg
 * @Date: 2025/02/28
 * @Description:
 */
@RequiredArgsConstructor
@Service
@DS("data")
public class BmqzjProWoFormFinishPgServiceImpl implements IBmqzjProWoFormFinishPgService {
    private final BmqzjProWoFormFinishPgMapper bmqzjProWoFormFinishPgMapper;


    @Override
    public TableDataInfo<BmqzjProWoFormFinishPgVo> queryPageList(BmqzjProWoFormFinishPgQuery query, PageQuery pageQuery) {
        QueryWrapper<BmqzjProWoFormFinishPg> qw = new QueryWrapper<>();
        qw.like(StringUtils.isNotBlank(query.getCode()), "d1.code", query.getCode());
        qw.like(StringUtils.isNotBlank(query.getTitle()), "d1.title", query.getTitle());
        qw.in("d1.act_name", "就业保障类/劳动报酬/拖欠或克扣工资", "就业保障类/劳动争议/劳资纠纷");
        qw.eq("d1.area", "石龙镇");
        qw.orderByDesc("d1.create_time");
        Page<BmqzjProWoFormFinishPgVo> page = bmqzjProWoFormFinishPgMapper.selectPageList(qw, pageQuery.build());
        return TableDataInfo.build(page);
    }
}
