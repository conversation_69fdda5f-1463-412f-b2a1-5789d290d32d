package com.zhonghe.cop.plan.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.zhonghe.common.annotation.Anonymous;
import com.zhonghe.common.annotation.Log;
import com.zhonghe.common.annotation.RepeatSubmit;
import com.zhonghe.common.core.controller.BaseController;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.common.enums.BusinessType;
import com.zhonghe.cop.plan.domain.bo.TPlaAccBo;
import com.zhonghe.cop.plan.domain.vo.TPlaAccVo;
import com.zhonghe.cop.plan.service.ITPlaAccService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 预案文书附件
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
@Validated
@Api(value = "预案文书附件控制器", tags = {"预案文书附件管理"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/cop/plaAcc")
public class TPlaAccController extends BaseController {

    private final ITPlaAccService iTPlaAccService;


    /**
     * 根据档案ID查询预案文书附件
     */
    @ApiOperation("根据档案ID查询预案文书附件(cop:plaAcc:list)")
    @SaCheckPermission("cop:plaAcc:list")
    @GetMapping("/docList/{docId}")
    public R<List<TPlaAccVo>> queryListByDocId(@Validated @ApiParam("文档ID") @NotNull(message = "文档ID不能为空") @PathVariable("docId") Long docId) {
        return R.ok(iTPlaAccService.queryListByDocId(docId));
    }

    /**
     * 新增文书附件
     */
    @ApiOperation("新增文书附件(cop:plaAcc:add)")
    @SaCheckPermission("cop:plaAcc:add")
    @Log(title = "新增文书附件", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping(consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<Void> add(@Validated TPlaAccBo bo) {
        return toAjax(iTPlaAccService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 下载预案文书附件
     */
    @ApiOperation("下载预案文书附件(cop:plaAcc:download)")
    @Anonymous
    @GetMapping("/download/{plaAccId}")
    public void download(@ApiParam("主键")
                         @PathVariable Long plaAccId, HttpServletResponse response) {
        iTPlaAccService.download(plaAccId, response);
    }

    /**
     * 删除预案文书附件
     */
    @ApiOperation("删除预案文书附件(cop:plaAcc:remove)")
    @SaCheckPermission("cop:plaAcc:remove")
    @Log(title = "预案文书附件", businessType = BusinessType.DELETE)
    @DeleteMapping("/{plaAccIds}")
    public R<Void> remove(@ApiParam("主键串")
                          @NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] plaAccIds) {
        return toAjax(iTPlaAccService.deleteWithValidByIds(Arrays.asList(plaAccIds), true) ? 1 : 0);
    }
}
