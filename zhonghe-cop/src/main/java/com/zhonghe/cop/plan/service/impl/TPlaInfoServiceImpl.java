package com.zhonghe.cop.plan.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhonghe.common.constant.Constants;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.domain.entity.SysDept;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.exception.ServiceException;
import com.zhonghe.common.utils.DateUtils;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.cop.bam.domain.TCopInformation;
import com.zhonghe.cop.bam.domain.vo.TCopInformationVo;
import com.zhonghe.cop.bam.mapper.TCopInformationMapper;
import com.zhonghe.cop.plan.domain.TPlaAcc;
import com.zhonghe.cop.plan.domain.TPlaHandle;
import com.zhonghe.cop.plan.domain.TPlaInfo;
import com.zhonghe.cop.plan.domain.bo.TPlaHandleBo;
import com.zhonghe.cop.plan.domain.bo.TPlaInfoBo;
import com.zhonghe.cop.plan.domain.query.TPlaInfoQuery;
import com.zhonghe.cop.plan.domain.vo.TPlaAccVo;
import com.zhonghe.cop.plan.domain.vo.TPlaInfoVo;
import com.zhonghe.cop.plan.domain.vo.table.TPlaInfoTableVo;
import com.zhonghe.cop.plan.em.IsPlan;
import com.zhonghe.cop.plan.mapper.TPlaAccMapper;
import com.zhonghe.cop.plan.mapper.TPlaHandleMapper;
import com.zhonghe.cop.plan.mapper.TPlaInfoMapper;
import com.zhonghe.cop.plan.service.ITPlaInfoService;
import com.zhonghe.cop.rep.mapper.TRepGarrepMapper;
import com.zhonghe.system.mapper.SysDeptMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

/**
 * 预案基础信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
@RequiredArgsConstructor
@Service
public class TPlaInfoServiceImpl implements ITPlaInfoService {

    private final TPlaInfoMapper baseMapper;
    private final TPlaHandleMapper plaHandleMapper;
    private final TCopInformationMapper copInformationMapper;
    private final TPlaAccMapper plaAccMapper;
    private final SysDeptMapper sysDeptMapper;
    private final TRepGarrepMapper tRepGarrepMapper;

    /**
     * 查询预案基础信息
     */
    @Override
    public TPlaInfoVo queryById(Long plaInfoId) {
        TPlaInfoVo tPlaInfoVo = baseMapper.selectVoById(plaInfoId);
        TCopInformation tCopInformation = copInformationMapper.selectById(tPlaInfoVo.getCopId());
        tPlaInfoVo.setCopName(tCopInformation.getCopName());
        tPlaInfoVo.setPropertyId(tCopInformation.getPropertyId());
        tPlaInfoVo.setCopOwner(tCopInformation.getCopOwner());
        tPlaInfoVo.setCopPhone(tCopInformation.getCopPhone());
        tPlaInfoVo.setCopAddress(tCopInformation.getCopAddress());

        //查询部门名称ID转为Long类型数组
        if (StringUtils.isNotBlank(tPlaInfoVo.getDepts())) {
            String[] stringArray = tPlaInfoVo.getDepts().split(",");
            Long[] deptIds = new Long[stringArray.length];

            for (int i = 0; i < stringArray.length; i++) {
                deptIds[i] = Long.parseLong(stringArray[i].trim());
            }
            if (deptIds.length > 0) {
                List<SysDept> sysDepts = sysDeptMapper.selectBatchIds(Arrays.asList(deptIds));
                //部门名称,逗号分割
                Optional<String> deptNames = sysDepts.stream().map(SysDept::getDeptName).reduce((a, b) -> a + "," + b);
                deptNames.ifPresent(tPlaInfoVo::setDeptNames);
            }
            //部门ID数组转String数组
            String[] ids = new String[stringArray.length];
            for (int i = 0; i < stringArray.length; i++) {
                ids[i] = stringArray[i].trim();
            }
            tPlaInfoVo.setDeptIds(ids);
        }
        //查询文书附件
        List<TPlaAccVo> tPlaAccVos = plaAccMapper.selectVoList(Wrappers.<TPlaAcc>lambdaQuery().eq(TPlaAcc::getDocId, plaInfoId));
        tPlaInfoVo.setPlaAccs(tPlaAccVos);
        return tPlaInfoVo;
    }

    /**
     * 查询预案基础信息列表
     */
    @Override
    public TableDataInfo<TPlaInfoTableVo> queryPageList(TPlaInfoQuery bo, PageQuery pageQuery) {
        QueryWrapper<TPlaInfo> qw = buildQueryWrapper(bo);
        Page<TPlaInfoTableVo> result = baseMapper.selectPlanInfoList(pageQuery.build(), qw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询预案基础信息列表
     */
    @Override
    public List<TPlaInfoTableVo> queryList(TPlaInfoQuery bo) {
        QueryWrapper<TPlaInfo> lqw = buildQueryWrapper(bo);
        return baseMapper.selectPlanInfoList(lqw);
    }

    private QueryWrapper<TPlaInfo> buildQueryWrapper(TPlaInfoQuery bo) {
        QueryWrapper<TPlaInfo> qw = Wrappers.query();
        qw.like(StringUtils.isNotBlank(bo.getCopName()), "tci.cop_name", bo.getCopName());
        if (bo.getCreateTime() != null) {
            qw.eq("tpi.create_time", bo.getCreateTime());
        }
        qw.eq(bo.getIsPlan() != null, "tpi.is_plan", bo.getIsPlan());
        qw.eq("tpi.del_flag", Constants.DEL_FLAG_NORMAL);
        qw.orderByDesc("tpi.create_time");
        return qw;
    }

    /**
     * 新增预案基础信息
     */
    @Override
    public Boolean insertByBo(TPlaInfoBo bo) {
        TPlaInfo add = BeanUtil.toBean(bo, TPlaInfo.class);
        String[] deptIds = bo.getDeptIds();
        //涉及部门ID串（逗号分割，例：1,2,3 ）
        if (deptIds != null && deptIds.length > 0) {
            add.setDepts(StringUtils.join(deptIds, ","));
        }
        add.setRepDate(DateUtil.parse(DateUtils.getDate(), DateUtils.YYYY_MM_DD));
        add.setIsPlan(IsPlan.NO.getCode());
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setPlaInfoId(add.getPlaInfoId());
        }
        return flag;
    }

    /**
     * 修改预案基础信息
     */
    @Override
    public Boolean updateByBo(TPlaInfoBo bo) {
        TPlaInfo update = BeanUtil.toBean(bo, TPlaInfo.class);
        String[] deptIds = bo.getDeptIds();
        //涉及部门ID串（逗号分割，例：1,2,3 ）
        if (deptIds != null && deptIds.length > 0) {
            update.setDepts(StringUtils.join(deptIds, ","));
        }
        update.setIsPlan(IsPlan.YES.getCode());
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(TPlaInfo entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void planTask() {
        //连续2个月因欠薪而高危的企业进行预案
        List<TCopInformationVo> tCopInformations = tRepGarrepMapper.select2MonthCop();
        tCopInformations.forEach(item -> {
            TPlaInfo tPlaInfo = baseMapper.selectOne(Wrappers.<TPlaInfo>lambdaQuery().eq(TPlaInfo::getCopId, item.getCopId()));
            if (tPlaInfo != null) {
                baseMapper.delete(Wrappers.<TPlaInfo>lambdaQuery().eq(TPlaInfo::getCopId, item.getCopId()));
            }
            TPlaInfo tPlaInfo1 = new TPlaInfo();
            tPlaInfo1.setCopId(item.getCopId());
            tPlaInfo1.setIsPlan(IsPlan.NO.getCode());
            tPlaInfo1.setRepDate(DateUtil.parse(DateUtils.getDate(), DateUtils.YYYY_MM_DD));
            baseMapper.insert(tPlaInfo1);
        });

    }

    @Override
    public List<TPlaAccVo> selectAccList(Long plaHandleId) {
        return plaAccMapper.selectVoList(Wrappers.<TPlaAcc>lambdaQuery().eq(TPlaAcc::getDocId, plaHandleId));
    }

    @Override
    public Boolean report(TPlaHandleBo bo) {
        TPlaHandle tPlaHandle = BeanUtil.toBean(bo, TPlaHandle.class);
        //已经预案的才可以上报
        TPlaInfo tPlaInfo = baseMapper.selectById(tPlaHandle.getPlaInfoId());

        if (IsPlan.NO.getCode().equals(tPlaInfo.getIsPlan())) {
            throw new ServiceException("未预案的不能上报");
        }
        return plaHandleMapper.insert(tPlaHandle) > 0;
    }

    @Override
    public Boolean plan(Long plaInfoId) {
        TPlaInfo tPlaInfo = baseMapper.selectById(plaInfoId);
        //已经预案的不能预案
        if (IsPlan.YES.getCode().equals(tPlaInfo.getIsPlan())) {
            throw new ServiceException("已经预案的不能预案");
        }
        //预案
        tPlaInfo.setIsPlan(IsPlan.YES.getCode());
        return baseMapper.updateById(tPlaInfo) > 0;
    }

    /**
     * 批量删除预案基础信息
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //已经预案的不能删除
            baseMapper.selectBatchIds(ids).forEach(item -> {
                if (IsPlan.YES.getCode().equals(item.getIsPlan())) {
                    throw new ServiceException("已经预案的不能删除");
                }
            });
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
