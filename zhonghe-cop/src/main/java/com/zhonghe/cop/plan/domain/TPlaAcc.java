package com.zhonghe.cop.plan.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zhonghe.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 预案文书附件对象 t_pla_acc
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("cop.t_pla_acc")
public class TPlaAcc extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 预案文书附件ID
     */
    @TableId
    private Long plaAccId;
    /**
     * 是否删除
     */
    @TableLogic
    private String delFlag;
    /**
     * 备注
     */
    private String note;
    /**
     * 预案ID（根据附件部门类型对应t_pla_info或t_pla_handle的主键）
     */
    private Long docId;
    /**
     * 附件部门类型（默认0，0——领导小组、1——成员部门）
     */
    private Long deptType;
    /**
     * 附件类型ID
     */
    private Long accTypeId;
    /**
     * 附件名称
     */
    private String accName;
    /**
     * 附件url
     */
    private String accUrl;

}
