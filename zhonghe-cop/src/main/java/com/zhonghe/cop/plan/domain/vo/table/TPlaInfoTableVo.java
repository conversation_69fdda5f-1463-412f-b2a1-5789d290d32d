package com.zhonghe.cop.plan.domain.vo.table;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.util.Date;

/**
 * @Author: lpg
 * @Date: 2023/12/07/14:53
 * @Description:
 */
@Data
public class TPlaInfoTableVo {
    /**
     * 预案基础信息ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long plaInfoId;

    /**
     * 企业名称
     */
    private String copName;

    /**
     * 企业性质
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Integer propertyId;
    /**
     * 企业法人
     */
    private String copOwner;
    /**
     * 企业电话
     */
    private String copPhone;
    /**
     * 生成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date createTime;
    /**
     * 是否预案
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Integer isPlan;
}
