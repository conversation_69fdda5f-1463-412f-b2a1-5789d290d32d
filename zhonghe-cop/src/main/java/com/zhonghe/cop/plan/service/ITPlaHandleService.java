package com.zhonghe.cop.plan.service;

import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.cop.plan.domain.bo.TPlaHandleBo;
import com.zhonghe.cop.plan.domain.query.TPlaInfoQuery;
import com.zhonghe.cop.plan.domain.vo.TPlaHandleInfoVo;
import com.zhonghe.cop.plan.domain.vo.table.TPlaHandleTableVo;

import java.util.Collection;
import java.util.List;

/**
 * 预案上报Service接口
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
public interface ITPlaHandleService {

    /**
     * 查询预案上报
     */
    TPlaHandleInfoVo queryById(Long plaInfoId);

    /**
     * 查询预案上报列表
     */
    TableDataInfo<TPlaHandleTableVo> queryPageList(TPlaInfoQuery bo, PageQuery pageQuery);

    /**
     * 查询预案上报列表
     */
    List<TPlaHandleTableVo> queryList(TPlaInfoQuery bo);

    /**
     * 修改预案上报
     */
    Boolean insertByBo(TPlaHandleBo bo);

    /**
     * 修改预案上报
     */
    Boolean updateByBo(TPlaHandleBo bo);

    /**
     * 校验并批量删除预案上报信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
