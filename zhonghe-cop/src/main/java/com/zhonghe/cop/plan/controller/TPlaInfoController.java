package com.zhonghe.cop.plan.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.zhonghe.common.annotation.Log;
import com.zhonghe.common.annotation.RepeatSubmit;
import com.zhonghe.common.core.controller.BaseController;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import com.zhonghe.common.enums.BusinessType;
import com.zhonghe.common.utils.poi.ExcelUtil;
import com.zhonghe.cop.plan.domain.bo.TPlaInfoBo;
import com.zhonghe.cop.plan.domain.query.TPlaInfoQuery;
import com.zhonghe.cop.plan.domain.vo.TPlaInfoVo;
import com.zhonghe.cop.plan.domain.vo.table.TPlaInfoTableVo;
import com.zhonghe.cop.plan.service.ITPlaInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 预案基础信息
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
@Validated
@Api(value = "预案基础信息控制器", tags = {"预案基础信息管理"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/cop/plaInfo")
public class TPlaInfoController extends BaseController {

    private final ITPlaInfoService iTPlaInfoService;

    /**
     * 查询预案基础信息列表
     */
    @ApiOperation("查询预案基础信息列表(cop:plaInfo:list)")
    @SaCheckPermission("cop:plaInfo:list")
    @GetMapping("/list")
    public TableDataInfo<TPlaInfoTableVo> list(TPlaInfoQuery bo, PageQuery pageQuery) {
        return iTPlaInfoService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出预案基础信息列表
     */
    @ApiOperation("导出预案基础信息列表(cop:plaInfo:export)")
    @SaCheckPermission("cop:plaInfo:export")
    @Log(title = "预案基础信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(TPlaInfoQuery bo, HttpServletResponse response) {
        List<TPlaInfoTableVo> list = iTPlaInfoService.queryList(bo);
        ExcelUtil.exportExcel(list, "预案基础信息", TPlaInfoTableVo.class, response);
    }

    /**
     * 获取预案基础信息详细信息
     */
    @ApiOperation("获取预案基础信息详细信息(cop:plaInfo:query)")
    @SaCheckPermission("cop:plaInfo:query")
    @GetMapping("/{plaInfoId}")
    public R<TPlaInfoVo> getInfo(@ApiParam("主键")
                                 @NotNull(message = "主键不能为空")
                                 @PathVariable("plaInfoId") Long plaInfoId) {
        return R.ok(iTPlaInfoService.queryById(plaInfoId));
    }

    /**
     * 新增预案基础信息
     */
    @ApiOperation("新增预案基础信息(cop:plaInfo:add)")
    @SaCheckPermission("cop:plaInfo:add")
    @Log(title = "预案基础信息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody TPlaInfoBo bo) {
        return toAjax(iTPlaInfoService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改预案基础信息
     */
    @ApiOperation("修改预案基础信息(cop:plaInfo:edit)")
    @SaCheckPermission("cop:plaInfo:edit")
    @Log(title = "预案基础信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody TPlaInfoBo bo) {
        return toAjax(iTPlaInfoService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除预案基础信息
     */
    @ApiOperation("删除预案基础信息(cop:plaInfo:remove)")
    @SaCheckPermission("cop:plaInfo:remove")
    @Log(title = "预案基础信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{plaInfoIds}")
    public R<Void> remove(@ApiParam("主键串")
                          @NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] plaInfoIds) {
        return toAjax(iTPlaInfoService.deleteWithValidByIds(Arrays.asList(plaInfoIds), true) ? 1 : 0);
    }

    /**
     * 预案
     */
    @ApiOperation("预案(cop:plaInfo:plan)")
    @SaCheckPermission("cop:plaInfo:plan")
    @Log(title = "预案", businessType = BusinessType.DELETE)
    @PostMapping("/plan/{plaInfoId}")
    public R<Void> plan(@ApiParam("主键")
                        @NotNull(message = "主键不能为空")
                        @PathVariable("plaInfoId") Long plaInfoId) {
        return toAjax(iTPlaInfoService.plan(plaInfoId));
    }


}
