package com.zhonghe.cop.plan.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zhonghe.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;


/**
 * 预案基础信息对象 t_pla_info
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("cop.t_pla_info")
public class TPlaInfo extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 预案基础信息ID
     */
    @TableId
    private Long plaInfoId;
    /**
     * 是否删除
     */
    @TableLogic
    private String delFlag;
    /**
     * 备注
     */
    private String note;
    /**
     * 报表月份
     */
    private Date repDate;
    /**
     * 企业ID
     */
    private Long copId;
    /**
     * 月汇总报表ID（对应t_rep_garrep主键）
     */
    private Long repGarrepId;
    /**
     * 是否需要预案（默认0，0——不需要、1——需要）
     */
    private Integer isPlan;
    /**
     * 涉及部门ID串（逗号分割，例：1,2,3 ）
     */
    private String depts;
    /**
     * 批示内容
     */
    private String despacho;

}
