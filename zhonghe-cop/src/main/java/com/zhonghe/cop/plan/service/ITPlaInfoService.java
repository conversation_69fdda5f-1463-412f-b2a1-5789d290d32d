package com.zhonghe.cop.plan.service;

import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.cop.plan.domain.bo.TPlaHandleBo;
import com.zhonghe.cop.plan.domain.bo.TPlaInfoBo;
import com.zhonghe.cop.plan.domain.query.TPlaInfoQuery;
import com.zhonghe.cop.plan.domain.vo.TPlaAccVo;
import com.zhonghe.cop.plan.domain.vo.TPlaInfoVo;
import com.zhonghe.cop.plan.domain.vo.table.TPlaInfoTableVo;

import java.util.Collection;
import java.util.List;

/**
 * 预案基础信息Service接口
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
public interface ITPlaInfoService {

    /**
     * 查询预案基础信息
     */
    TPlaInfoVo queryById(Long plaInfoId);

    /**
     * 查询预案基础信息列表
     */
    TableDataInfo<TPlaInfoTableVo> queryPageList(TPlaInfoQuery bo, PageQuery pageQuery);

    /**
     * 查询预案基础信息列表
     */
    List<TPlaInfoTableVo> queryList(TPlaInfoQuery bo);

    /**
     * 修改预案基础信息
     */
    Boolean insertByBo(TPlaInfoBo bo);

    /**
     * 修改预案基础信息
     */
    Boolean updateByBo(TPlaInfoBo bo);

    /**
     * 校验并批量删除预案基础信息信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 预案
     *
     * @param plaInfoId
     * @return
     */
    Boolean plan(Long plaInfoId);

    /**
     * 上报
     *
     * @param bo
     * @return
     */
    Boolean report(TPlaHandleBo bo);

    /**
     * 查询预案上报文书附件
     *
     * @param plaHandleId
     * @return
     */
    List<TPlaAccVo> selectAccList(Long plaHandleId);

    /**
     * 连续2个月因欠薪而高危的企业进行预案
     */
    void planTask();
}
