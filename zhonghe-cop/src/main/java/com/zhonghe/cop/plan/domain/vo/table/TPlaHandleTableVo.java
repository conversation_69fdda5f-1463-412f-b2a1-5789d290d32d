package com.zhonghe.cop.plan.domain.vo.table;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @Author: lpg
 * @Date: 2023/12/07/14:53
 * @Description:
 */
@Data
public class TPlaHandleTableVo {
    /**
     * 预案上报ID
     */
    @ExcelProperty(value = "预案上报ID")
    @ApiModelProperty("预案上报ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long plaHandleId;

    /**
     * 企业名称
     */
    @ApiModelProperty("企业名称")
    private String copName;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    @ApiModelProperty("备注")
    private String note;

    /**
     * 预案ID（对应t_pla_info表中的主键）
     */
    @ExcelProperty(value = "预案ID")
    @ApiModelProperty("预案ID（对应t_pla_info表中的主键）")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long plaInfoId;

    /**
     * 高危情况
     */
    @ExcelProperty(value = "高危情况")
    @ApiModelProperty("高危情况")
    private String copCondition;

    /**
     * 预案措施
     */
    @ExcelProperty(value = "预案措施")
    @ApiModelProperty("预案措施")
    private String planMeasure;

    /**
     * 预案依据
     */
    @ExcelProperty(value = "预案依据")
    @ApiModelProperty("预案依据")
    private String planBasis;

    /**
     * 协助请求
     */
    @ExcelProperty(value = "协助请求")
    @ApiModelProperty("协助请求")
    private String planHelp;

    /**
     * 其他内容
     */
    @ExcelProperty(value = "其他内容")
    @ApiModelProperty("其他内容")
    private String planOther;

    /**
     * 报表月份
     */
    @ExcelProperty(value = "报表月份")
    @ApiModelProperty("报表月份")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date repDate;


}
