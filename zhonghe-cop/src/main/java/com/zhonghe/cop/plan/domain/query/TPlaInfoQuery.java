package com.zhonghe.cop.plan.domain.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;


/**
 * 预案基础信息业务对象 t_pla_info
 *
 * <AUTHOR>
 * @date 2023-12-07
 */

@Data
@ApiModel("预案基础信息查询对象")
public class TPlaInfoQuery {

    private static final long serialVersionUID = 9074381222147286808L;

    /**
     * 企业名称
     */
    @ApiModelProperty(value = "企业名称")
    private String copName;

    /**
     * 生成时间
     */
    @ApiModelProperty(value = "生成时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date createTime;

    /**
     * 是否需要预案（默认0，0——不需要、1——需要）
     */
    @ApiModelProperty(value = "是否需要预案（默认0，0——不需要、1——需要）")
    private Integer isPlan;


}
