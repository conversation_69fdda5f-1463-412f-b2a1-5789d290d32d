package com.zhonghe.cop.plan.domain.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;


/**
 * 预案文书附件业务对象 t_pla_acc
 *
 * <AUTHOR>
 * @date 2023-12-07
 */

@Data
@ApiModel("预案文书附件业务对象")
public class TPlaAccBo implements Serializable {

    private static final long serialVersionUID = 2684124238464963503L;
    /**
     * 文档ID（根据附件部门类型对应t_pla_info或t_pla_handle的主键）
     */
    @ApiModelProperty(value = "文档ID（根据附件部门类型对应t_pla_info或t_pla_handle的主键）")
    @NotNull(message = "文档ID不能为空")
    private Long docId;

    /**
     * 附件类型ID
     */
    @ApiModelProperty(value = "附件类型ID", required = true)
    @NotNull(message = "附件类型ID不能为空")
    private Long accTypeId;

    /**
     * 附件名称
     */
    @ApiModelProperty(value = "附件名称", required = true)
    @NotBlank(message = "附件名称不能为空")
    private String accName;

    /**
     * 附件
     */
    @ApiModelProperty(value = "附件", required = true)
    @NotNull(message = "附件不能为空")
    private MultipartFile accFile;

}
