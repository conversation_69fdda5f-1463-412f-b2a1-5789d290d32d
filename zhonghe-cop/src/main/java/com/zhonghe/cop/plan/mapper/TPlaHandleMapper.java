package com.zhonghe.cop.plan.mapper;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhonghe.common.core.mapper.BaseMapperPlus;
import com.zhonghe.cop.plan.domain.TPlaHandle;
import com.zhonghe.cop.plan.domain.TPlaInfo;
import com.zhonghe.cop.plan.domain.vo.TPlaHandleVo;
import com.zhonghe.cop.plan.domain.vo.table.TPlaHandleTableVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 预案上报Mapper接口
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
public interface TPlaHandleMapper extends BaseMapperPlus<TPlaHandleMapper, TPlaHandle, TPlaHandleVo> {

    /**
     * 分页查询预案基础信息列表
     *
     * @param plaHandle
     * @param lqw
     * @return
     */
    Page<TPlaHandleTableVo> selectPlaHandleList(Page<TPlaHandle> plaHandle, @Param(Constants.WRAPPER) Wrapper<TPlaInfo> lqw);

    /**
     * 查询预案基础信息列表
     */
    List<TPlaHandleTableVo> selectPlaHandleList(@Param(Constants.WRAPPER) Wrapper<TPlaInfo> lqw);
}
