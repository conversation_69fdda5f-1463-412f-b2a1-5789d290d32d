package com.zhonghe.cop.plan.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.io.FileUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.exception.ServiceException;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.cop.plan.domain.TPlaAcc;
import com.zhonghe.cop.plan.domain.bo.TPlaAccBo;
import com.zhonghe.cop.plan.domain.vo.TPlaAccVo;
import com.zhonghe.cop.plan.mapper.TPlaAccMapper;
import com.zhonghe.cop.plan.service.ITPlaAccService;
import com.zhonghe.system.domain.SysOss;
import com.zhonghe.system.service.ISysOssService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 预案文书附件Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
@RequiredArgsConstructor
@Service
public class TPlaAccServiceImpl implements ITPlaAccService {

    private final TPlaAccMapper baseMapper;
    private final ISysOssService iSysOssService;

    /**
     * 查询预案文书附件
     */
    @Override
    public TPlaAccVo queryById(Long plaAccId) {
        return baseMapper.selectVoById(plaAccId);
    }

    /**
     * 查询预案文书附件列表
     */
    @Override
    public TableDataInfo<TPlaAccVo> queryPageList(TPlaAccBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<TPlaAcc> lqw = buildQueryWrapper(bo);
        Page<TPlaAccVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询预案文书附件列表
     */
    @Override
    public List<TPlaAccVo> queryList(TPlaAccBo bo) {
        LambdaQueryWrapper<TPlaAcc> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<TPlaAcc> buildQueryWrapper(TPlaAccBo bo) {
        LambdaQueryWrapper<TPlaAcc> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getAccTypeId() != null, TPlaAcc::getAccTypeId, bo.getAccTypeId());
        lqw.like(StringUtils.isNotBlank(bo.getAccName()), TPlaAcc::getAccName, bo.getAccName());
        return lqw;
    }

    /**
     * 新增预案文书附件
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(TPlaAccBo bo) {
        TPlaAcc add = BeanUtil.toBean(bo, TPlaAcc.class);
        SysOss sysOss = iSysOssService.uploadFile(bo.getAccFile());
        add.setAccUrl(sysOss.getUrl());
        return baseMapper.insert(add) > 0;
    }

    /**
     * 修改预案文书附件
     */
    @Override
    public Boolean updateByBo(TPlaAccBo bo) {
        TPlaAcc update = BeanUtil.toBean(bo, TPlaAcc.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(TPlaAcc entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public void download(Long plaAccId, HttpServletResponse response) {
        TPlaAcc tPlaAcc = baseMapper.selectById(plaAccId);
        if (StringUtils.isBlank(tPlaAcc.getAccUrl())) {
            throw new ServiceException("文件数据不存在!");
        }
        //截取文件url后缀
        String suffix = FileUtil.getSuffix(tPlaAcc.getAccUrl());
        iSysOssService.downloadFile(tPlaAcc.getAccUrl(), tPlaAcc.getAccName() + "." + suffix, response);
    }

    @Override
    public List<TPlaAccVo> queryListByDocId(Long docId) {
        return baseMapper.selectVoList(Wrappers.lambdaQuery(TPlaAcc.class).eq(TPlaAcc::getDocId, docId).orderByDesc(TPlaAcc::getCreateTime));
    }

    /**
     * 批量删除预案文书附件
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            List<String> keys = baseMapper.selectBatchIds(ids).stream().map(TPlaAcc::getAccUrl).collect(Collectors.toList());
            iSysOssService.deleteByKeys(keys);
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
