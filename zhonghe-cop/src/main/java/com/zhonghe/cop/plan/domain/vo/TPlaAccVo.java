package com.zhonghe.cop.plan.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.zhonghe.common.annotation.ExcelDictFormat;
import com.zhonghe.common.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 预案文书附件视图对象 t_pla_acc
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
@Data
@ApiModel("预案文书附件视图对象")
@ExcelIgnoreUnannotated
public class TPlaAccVo {

    private static final long serialVersionUID = 1L;

    /**
     * 预案文书附件ID
     */
    @ExcelProperty(value = "预案文书附件ID")
    @ApiModelProperty("预案文书附件ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long plaAccId;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    @ApiModelProperty("备注")
    private String note;

    /**
     * 预案ID（根据附件部门类型对应t_pla_info或t_pla_handle的主键）
     */
    @ExcelProperty(value = "预案ID", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "根=据附件部门类型对应t_pla_info或t_pla_handle的主键")
    @ApiModelProperty("预案ID（根据附件部门类型对应t_pla_info或t_pla_handle的主键）")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long plaHandleId;

    /**
     * 附件部门类型（默认0，0——领导小组、1——成员部门）
     */
    @ExcelProperty(value = "附件部门类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "默=认0，0——领导小组、1——成员部门")
    @ApiModelProperty("附件部门类型（默认0，0——领导小组、1——成员部门）")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long deptType;

    /**
     * 附件类型ID
     */
    @ExcelProperty(value = "附件类型ID")
    @ApiModelProperty("附件类型ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long accTypeId;

    /**
     * 附件名称
     */
    @ExcelProperty(value = "附件名称")
    @ApiModelProperty("附件名称")
    private String accName;

    /**
     * 附件url
     */
    @ExcelProperty(value = "附件url")
    @ApiModelProperty("附件url")
    private String accUrl;


}
