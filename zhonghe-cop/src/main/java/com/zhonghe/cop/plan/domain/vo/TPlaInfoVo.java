package com.zhonghe.cop.plan.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 预案基础信息视图对象 t_pla_info
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
@Data
@ApiModel("预案基础信息视图对象")
@ExcelIgnoreUnannotated
public class TPlaInfoVo implements TransPojo, Serializable {

    private static final long serialVersionUID = 1L;

    private Map<String, Object> transMap = new HashMap<>();

    /**
     * 预案基础信息ID
     */
    @ExcelProperty(value = "预案基础信息ID")
    @ApiModelProperty("预案基础信息ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long plaInfoId;
    /**
     * 企业ID
     */
    @ApiModelProperty("企业ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long copId;
    /**
     * 企业名称
     */
    @ApiModelProperty("企业名称")
    private String copName;

    /**
     * 企业性质
     */
    @ApiModelProperty("企业性质")
    @JsonSerialize(using = ToStringSerializer.class)
    @Trans(type = TransType.DICTIONARY, target = TPlaInfoVo.class, key = "cop_property", ref = "copProperty")
    private Long propertyId;
    /**
     * 企业性质
     */
    @ApiModelProperty("企业性质")
    private String copProperty;
    /**
     * 企业法人
     */
    @ApiModelProperty("企业法人")
    private String copOwner;
    /**
     * 企业电话
     */
    @ApiModelProperty("企业电话")
    private String copPhone;
    /**
     * 企业地址
     */
    @ApiModelProperty("企业地址")
    private String copAddress;
    /**
     * 生成时间
     */
    @ApiModelProperty("生成时间")
    private Date createTime;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String note;

    /**
     * 是否需要预案（默认0，0——不需要、1——需要）
     */
    @ApiModelProperty("是否需要预案（默认0，0——不需要、1——需要）")
    @JsonSerialize(using = ToStringSerializer.class)
    private Integer isPlan;

    /**
     * 涉及部门ID
     */
    @ApiModelProperty(value = "涉及部门ID）")
    private String[] deptIds;
    /**
     * 涉及部门ID串（逗号分割，例：1,2,3 ）
     */
    @ApiModelProperty("涉及部门ID串（逗号分割，例：1,2,3 ）")
    private String depts;
    /**
     * 部门名称
     */
    @ApiModelProperty("部门名称")
    private String deptNames;

    /**
     * 批示内容
     */
    @ApiModelProperty("批示内容")
    private String despacho;

    /**
     * 高危情况
     */
    @ApiModelProperty("高危情况")
    private String copCondition;

    /**
     * 预案措施
     */
    @ApiModelProperty("预案措施")
    private String planMeasure;

    /**
     * 预案依据
     */
    @ApiModelProperty("预案依据")
    private String planBasis;

    /**
     * 协助请求
     */
    @ApiModelProperty("协助请求")
    private String planHelp;

    /**
     * 其他内容
     */
    @ApiModelProperty("其他内容")
    private String planOther;

    /**
     * 文书附件
     */
    @ApiModelProperty("文书附件")
    private List<TPlaAccVo> plaAccs;


}
