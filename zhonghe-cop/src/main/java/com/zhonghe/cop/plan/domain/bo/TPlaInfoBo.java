package com.zhonghe.cop.plan.domain.bo;

import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;


/**
 * 预案基础信息业务对象 t_pla_info
 *
 * <AUTHOR>
 * @date 2023-12-07
 */

@Data
@ApiModel("预案基础信息业务对象")
public class TPlaInfoBo implements Serializable {

    private static final long serialVersionUID = 9074381222147286808L;
    /**
     * 预案基础信息ID
     */
    @ApiModelProperty(value = "预案基础信息ID", required = true)
    @NotNull(message = "预案基础信息ID不能为空", groups = {EditGroup.class})
    private Long plaInfoId;


    /**
     * 企业ID
     */
    @ApiModelProperty(value = "企业ID", required = true)
    @NotNull(message = "企业ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long copId;

    /**
     * 涉及部门ID
     */
    @ApiModelProperty(value = "涉及部门ID）", required = true)
    @NotEmpty(message = "涉及部门ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private String[] deptIds;

    /**
     * 批示内容
     */
    @ApiModelProperty(value = "批示内容", required = true)
    @NotBlank(message = "批示内容不能为空", groups = {AddGroup.class, EditGroup.class})
    @Length(max = 4000, message = "批示内容长度不能超过4000个字符", groups = {AddGroup.class, EditGroup.class})
    private String despacho;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Length(max = 4000, message = "备注长度不能超过4000个字符", groups = {AddGroup.class, EditGroup.class})
    private String note;


}
