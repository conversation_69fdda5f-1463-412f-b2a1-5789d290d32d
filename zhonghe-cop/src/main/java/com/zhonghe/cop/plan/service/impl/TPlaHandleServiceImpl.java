package com.zhonghe.cop.plan.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhonghe.common.constant.Constants;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.exception.ServiceException;
import com.zhonghe.common.helper.LoginHelper;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.cop.bam.domain.vo.TCopInformationVo;
import com.zhonghe.cop.bam.mapper.TCopInformationMapper;
import com.zhonghe.cop.plan.domain.TPlaAcc;
import com.zhonghe.cop.plan.domain.TPlaHandle;
import com.zhonghe.cop.plan.domain.TPlaInfo;
import com.zhonghe.cop.plan.domain.bo.TPlaHandleBo;
import com.zhonghe.cop.plan.domain.query.TPlaInfoQuery;
import com.zhonghe.cop.plan.domain.vo.TPlaAccVo;
import com.zhonghe.cop.plan.domain.vo.TPlaHandleInfoVo;
import com.zhonghe.cop.plan.domain.vo.TPlaHandleVo;
import com.zhonghe.cop.plan.domain.vo.TPlaInfoVo;
import com.zhonghe.cop.plan.domain.vo.table.TPlaHandleTableVo;
import com.zhonghe.cop.plan.em.IsPlan;
import com.zhonghe.cop.plan.mapper.TPlaAccMapper;
import com.zhonghe.cop.plan.mapper.TPlaHandleMapper;
import com.zhonghe.cop.plan.mapper.TPlaInfoMapper;
import com.zhonghe.cop.plan.service.ITPlaHandleService;
import com.zhonghe.cop.score.domain.vo.CopRiskVo;
import com.zhonghe.cop.score.service.ITCopScoreYearService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * 预案上报Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
@RequiredArgsConstructor
@Service
public class TPlaHandleServiceImpl implements ITPlaHandleService {

    private final TPlaHandleMapper baseMapper;
    private final TPlaAccMapper plaAccMapper;
    private final TPlaInfoMapper plaInfoMapper;
    private final TCopInformationMapper copInformationMapper;
    private final ITCopScoreYearService iTCopScoreYearService;

    /**
     * 查询预案上报
     */
    @Override
    public TPlaHandleInfoVo queryById(Long plaInfoId) {
        TPlaInfoVo tPlaInfo = plaInfoMapper.selectVoById(plaInfoId);
        Assert.notNull(tPlaInfo, "预案不存在");
        //查询本部门上报的预案
        TPlaHandleVo tPlaHandle = baseMapper.selectVoOne(Wrappers.<TPlaHandle>lambdaQuery().eq(TPlaHandle::getPlaInfoId, plaInfoId).eq(TPlaHandle::getDeptId, LoginHelper.getDeptId()));
        List<CopRiskVo> copRiskVos = iTCopScoreYearService.queryRisk(tPlaInfo.getCopId());
        //查询企业信息
        TCopInformationVo copInformation = copInformationMapper.selectVoById(tPlaInfo.getCopId());
        //查询文书附件
        if (ObjectUtil.isNotNull(tPlaHandle)) {
            List<TPlaAccVo> tPlaAccVos = plaAccMapper.selectVoList(Wrappers.<TPlaAcc>lambdaQuery().eq(TPlaAcc::getDocId, tPlaHandle.getPlaHandleId()).orderByDesc(TPlaAcc::getCreateTime));
            tPlaHandle.setPlaAccs(tPlaAccVos);
            //设置批示内容
            tPlaHandle.setDespacho(tPlaInfo.getDespacho());
        }

        return TPlaHandleInfoVo.builder().plaHandle(tPlaHandle).copInformation(copInformation).copRisks(copRiskVos).build();
    }

    /**
     * 查询预案上报列表
     */
    @Override
    public TableDataInfo<TPlaHandleTableVo> queryPageList(TPlaInfoQuery bo, PageQuery pageQuery) {
        QueryWrapper<TPlaInfo> qw = buildQueryWrapper(bo);
        Page<TPlaHandleTableVo> result = baseMapper.selectPlaHandleList(pageQuery.build(), qw);
        for (TPlaHandleTableVo record : result.getRecords()) {
            //查询本部门预案信息
            TPlaHandleVo tPlaHandleVo = baseMapper.selectVoOne(Wrappers.<TPlaHandle>lambdaQuery()
                .select(TPlaHandle::getPlaHandleId)
                .eq(TPlaHandle::getPlaInfoId, record.getPlaInfoId()).eq(TPlaHandle::getDeptId, LoginHelper.getDeptId()));
            if (ObjectUtil.isNotNull(tPlaHandleVo)) {
                record.setPlaHandleId(tPlaHandleVo.getPlaHandleId());
            }
        }
        return TableDataInfo.build(result);
    }

    /**
     * 查询预案上报列表
     */
    @Override
    public List<TPlaHandleTableVo> queryList(TPlaInfoQuery bo) {
        QueryWrapper<TPlaInfo> qw = buildQueryWrapper(bo);
        return baseMapper.selectPlaHandleList(qw);
    }

    private QueryWrapper<TPlaInfo> buildQueryWrapper(TPlaInfoQuery bo) {
        QueryWrapper<TPlaInfo> qw = Wrappers.query();
        qw.like(StringUtils.isNotBlank(bo.getCopName()), "tci.cop_name", bo.getCopName());
        if (bo.getCreateTime() != null) {
            qw.eq("tpi.create_time", bo.getCreateTime());
        }
        qw.eq(bo.getIsPlan() != null, "tpi.is_plan", bo.getIsPlan());
        qw.eq("tpi.del_flag", Constants.DEL_FLAG_NORMAL);
        qw.like("tpi.depts", LoginHelper.getDeptId());
        qw.orderByDesc("tpi.create_time");
        return qw;
    }

    /**
     * 新增预案上报
     */
    @Override
    public Boolean insertByBo(TPlaHandleBo bo) {
        TPlaHandle add = BeanUtil.toBean(bo, TPlaHandle.class);
        //已经预案的才可以上报
        boolean flag;
        if (bo.getPlaHandleId() == null) {
            add.setDeptId(LoginHelper.getDeptId());
            flag = baseMapper.insert(add) > 0;
            if (flag) {
                bo.setPlaHandleId(add.getPlaHandleId());
            }
        } else {
            TPlaInfo tPlaInfo = plaInfoMapper.selectById(bo.getPlaInfoId());

            if (IsPlan.NO.getCode().equals(tPlaInfo.getIsPlan())) {
                throw new ServiceException("未预案的不能上报");
            }
            flag = updateByBo(bo);
        }

        return flag;
    }

    /**
     * 修改预案上报
     */
    @Override
    public Boolean updateByBo(TPlaHandleBo bo) {
        TPlaHandle update = BeanUtil.toBean(bo, TPlaHandle.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(TPlaHandle entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除预案上报
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
