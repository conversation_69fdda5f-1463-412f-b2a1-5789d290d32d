package com.zhonghe.cop.plan.mapper;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhonghe.common.core.mapper.BaseMapperPlus;
import com.zhonghe.cop.plan.domain.TPlaInfo;
import com.zhonghe.cop.plan.domain.vo.TPlaInfoVo;
import com.zhonghe.cop.plan.domain.vo.table.TPlaInfoTableVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 预案基础信息Mapper接口
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
public interface TPlaInfoMapper extends BaseMapperPlus<TPlaInfoMapper, TPlaInfo, TPlaInfoVo> {

    /**
     * 分页查询预案基础信息列表
     *
     * @param TPlaInfo
     * @param lqw
     * @return
     */
    Page<TPlaInfoTableVo> selectPlanInfoList(Page<TPlaInfo> TPlaInfo, @Param(Constants.WRAPPER) Wrapper<TPlaInfo> lqw);

    /**
     * 查询预案基础信息列表
     */
    List<TPlaInfoTableVo> selectPlanInfoList(@Param(Constants.WRAPPER) Wrapper<TPlaInfo> lqw);
}
