package com.zhonghe.cop.plan.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zhonghe.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 预案上报对象 t_pla_handle
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("cop.t_pla_handle")
public class TPlaHandle extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 预案上报ID
     */
    @TableId
    private Long plaHandleId;
    /**
     * 是否删除
     */
    @TableLogic
    private String delFlag;
    /**
     * 备注
     */
    private String note;
    /**
     * 预案ID（对应t_pla_info表中的主键）
     */
    private Long plaInfoId;
    /**
     * 部门ID
     */
    private Long deptId;
    /**
     * 高危情况
     */
    private String copCondition;
    /**
     * 预案措施
     */
    private String planMeasure;
    /**
     * 预案依据
     */
    private String planBasis;
    /**
     * 协助请求
     */
    private String planHelp;
    /**
     * 其他内容
     */
    private String planOther;

}
