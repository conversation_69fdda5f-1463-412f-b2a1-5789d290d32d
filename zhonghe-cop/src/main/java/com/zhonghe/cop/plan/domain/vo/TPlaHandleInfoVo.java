package com.zhonghe.cop.plan.domain.vo;

import com.zhonghe.cop.bam.domain.vo.TCopInformationVo;
import com.zhonghe.cop.score.domain.vo.CopRiskVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 * 预案上报视图对象 t_pla_handle
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
@Builder
@Data
@ApiModel("预案上报视图对象")
public class TPlaHandleInfoVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 企业信息
     */
    @ApiModelProperty("企业信息")
    private TCopInformationVo copInformation;

    /**
     * 预案上报信息
     */
    @ApiModelProperty("预案上报信息")
    private TPlaHandleVo plaHandle;

    /**
     * 企业风险评级
     */
    @ApiModelProperty("企业风险评级")
    private List<CopRiskVo> copRisks;
}
