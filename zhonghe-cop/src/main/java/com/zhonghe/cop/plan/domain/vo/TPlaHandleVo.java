package com.zhonghe.cop.plan.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.zhonghe.common.annotation.ExcelDictFormat;
import com.zhonghe.common.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


/**
 * 预案上报视图对象 t_pla_handle
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
@Data
@ApiModel("预案上报视图对象")
@ExcelIgnoreUnannotated
public class TPlaHandleVo {

    private static final long serialVersionUID = 1L;

    /**
     * 预案上报ID
     */
    @ExcelProperty(value = "预案上报ID")
    @ApiModelProperty("预案上报ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long plaHandleId;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    @ApiModelProperty("备注")
    private String note;

    /**
     * 预案ID（对应t_pla_info表中的主键）
     */
    @ExcelProperty(value = "预案ID", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "对=应t_pla_info表中的主键")
    @ApiModelProperty("预案ID（对应t_pla_info表中的主键）")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long plaInfoId;

    /**
     * 高危情况
     */
    @ExcelProperty(value = "高危情况")
    @ApiModelProperty("高危情况")
    private String copCondition;

    /**
     * 预案措施
     */
    @ExcelProperty(value = "预案措施")
    @ApiModelProperty("预案措施")
    private String planMeasure;

    /**
     * 预案依据
     */
    @ExcelProperty(value = "预案依据")
    @ApiModelProperty("预案依据")
    private String planBasis;

    /**
     * 协助请求
     */
    @ExcelProperty(value = "协助请求")
    @ApiModelProperty("协助请求")
    private String planHelp;

    /**
     * 其他内容
     */
    @ExcelProperty(value = "其他内容")
    @ApiModelProperty("其他内容")
    private String planOther;

    /**
     * 批示内容
     */
    @ExcelProperty(value = "批示内容")
    @ApiModelProperty("批示内容")
    private String despacho;

    /**
     * 文书附件
     */
    @ApiModelProperty("文书附件")
    private List<TPlaAccVo> plaAccs;


}
