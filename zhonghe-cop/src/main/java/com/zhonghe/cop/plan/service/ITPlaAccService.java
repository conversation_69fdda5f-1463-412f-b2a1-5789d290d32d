package com.zhonghe.cop.plan.service;

import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.cop.plan.domain.bo.TPlaAccBo;
import com.zhonghe.cop.plan.domain.vo.TPlaAccVo;

import javax.servlet.http.HttpServletResponse;
import java.util.Collection;
import java.util.List;

/**
 * 预案文书附件Service接口
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
public interface ITPlaAccService {

    /**
     * 查询预案文书附件
     */
    TPlaAccVo queryById(Long plaAccId);

    /**
     * 查询预案文书附件列表
     */
    TableDataInfo<TPlaAccVo> queryPageList(TPlaAccBo bo, PageQuery pageQuery);

    /**
     * 查询预案文书附件列表
     */
    List<TPlaAccVo> queryList(TPlaAccBo bo);

    /**
     * 修改预案文书附件
     */
    Boolean insertByBo(TPlaAccBo bo);

    /**
     * 修改预案文书附件
     */
    Boolean updateByBo(TPlaAccBo bo);

    /**
     * 校验并批量删除预案文书附件信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据文书id查询附件
     *
     * @param docId
     * @return
     */
    List<TPlaAccVo> queryListByDocId(Long docId);

    /**
     * 下载附件
     *
     * @param plaAccId
     * @param response
     */
    void download(Long plaAccId, HttpServletResponse response);
}
