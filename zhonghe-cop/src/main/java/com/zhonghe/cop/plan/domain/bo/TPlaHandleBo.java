package com.zhonghe.cop.plan.domain.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.io.Serializable;


/**
 * 预案上报业务对象 t_pla_handle
 *
 * <AUTHOR>
 * @date 2023-12-07
 */

@Data
@ApiModel("预案上报业务对象")
public class TPlaHandleBo implements Serializable {

    private static final long serialVersionUID = 8154902154795211132L;
    /**
     * 预案上报ID
     */
    @ApiModelProperty(value = "预案上报ID")
    private Long plaHandleId;

    /**
     * 预案ID（对应t_pla_info表中的主键）
     */
    @ApiModelProperty(value = "预案ID（对应t_pla_info表中的主键）")
    @NotNull(message = "预案ID（对应t_pla_info表中的主键）不能为空")
    private Long plaInfoId;

    /**
     * 高危情况
     */
    @ApiModelProperty(value = "高危情况")
    @Length(max = 2000, message = "高危情况不能超过2000个字符")
    private String copCondition;

    /**
     * 预案措施
     */
    @ApiModelProperty(value = "预案措施")
    @Length(max = 2000, message = "预案措施不能超过2000个字符")
    private String planMeasure;

    /**
     * 预案依据
     */
    @ApiModelProperty(value = "预案依据")
    @Length(max = 2000, message = "预案依据不能超过2000个字符")
    private String planBasis;

    /**
     * 协助请求
     */
    @ApiModelProperty(value = "协助请求")
    @Length(max = 2000, message = "协助请求不能超过2000个字符")
    private String planHelp;

    /**
     * 其他内容
     */
    @ApiModelProperty(value = "其他内容")
    @Length(max = 2000, message = "其他内容不能超过2000个字符")
    private String planOther;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Length(max = 2000, message = "备注不能超过2000个字符")
    private String note;


}
