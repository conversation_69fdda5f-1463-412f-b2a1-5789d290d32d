package com.zhonghe.cop.plan.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.zhonghe.common.annotation.Log;
import com.zhonghe.common.core.controller.BaseController;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.enums.BusinessType;
import com.zhonghe.common.utils.poi.ExcelUtil;
import com.zhonghe.cop.plan.domain.bo.TPlaHandleBo;
import com.zhonghe.cop.plan.domain.query.TPlaInfoQuery;
import com.zhonghe.cop.plan.domain.vo.TPlaHandleInfoVo;
import com.zhonghe.cop.plan.domain.vo.table.TPlaHandleTableVo;
import com.zhonghe.cop.plan.service.ITPlaHandleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 预案上报
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
@Validated
@Api(value = "预案上报控制器", tags = {"预案上报管理"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/cop/plaHandle")
public class TPlaHandleController extends BaseController {

    private final ITPlaHandleService iTPlaHandleService;

    /**
     * 查询预案上报列表
     */
    @ApiOperation("查询预案上报列表(cop:plaHandle:list)")
    @SaCheckPermission("cop:plaHandle:list")
    @GetMapping("/list")
    public TableDataInfo<TPlaHandleTableVo> list(TPlaInfoQuery bo, PageQuery pageQuery) {
        return iTPlaHandleService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询预案上报详细信息
     */
    @ApiOperation("查询预案上报详细信息(cop:plaHandle:query)")
    @SaCheckPermission("cop:plaHandle:query")
    @GetMapping("/{plaInfoId}")
    public R<TPlaHandleInfoVo> queryById(@PathVariable("plaInfoId") Long plaInfoId) {
        return R.ok(iTPlaHandleService.queryById(plaInfoId));
    }

    /**
     * 导出预案上报列表
     */
    @ApiOperation("导出预案上报列表(cop:plaHandle:export)")
    @SaCheckPermission("cop:plaHandle:export")
    @Log(title = "预案上报", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(TPlaInfoQuery bo, HttpServletResponse response) {
        List<TPlaHandleTableVo> list = iTPlaHandleService.queryList(bo);
        ExcelUtil.exportExcel(list, "预案上报", TPlaHandleTableVo.class, response);
    }


    /**
     * 预案上报
     */
    @ApiOperation("预案上报(cop:plaInfo:report)")
    @SaCheckPermission("cop:plaInfo:report")
    @Log(title = "预案上报", businessType = BusinessType.DELETE)
    @PostMapping()
    public R<Void> report(@Validated @RequestBody TPlaHandleBo bo) {
        return toAjax(iTPlaHandleService.insertByBo(bo));
    }


}
