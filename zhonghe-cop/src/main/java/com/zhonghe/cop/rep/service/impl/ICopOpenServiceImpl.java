package com.zhonghe.cop.rep.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zhonghe.common.constant.Constants;
import com.zhonghe.common.core.domain.entity.SysDictData;
import com.zhonghe.common.exception.ServiceException;
import com.zhonghe.common.utils.DateUtils;
import com.zhonghe.cop.bam.constants.CopConstants;
import com.zhonghe.cop.bam.domain.query.TPerPerformanceQuery;
import com.zhonghe.cop.bam.domain.vo.DeptPerformanceVo;
import com.zhonghe.cop.bam.mapper.TPerPerformanceMapper;
import com.zhonghe.cop.rep.domain.TRepGarrep;
import com.zhonghe.cop.rep.domain.vo.OpenMonthReport;
import com.zhonghe.cop.rep.domain.vo.OpenPerformance;
import com.zhonghe.cop.rep.domain.vo.QueryWarningVo;
import com.zhonghe.cop.rep.domain.vo.table.TRepGarrepTableVo;
import com.zhonghe.cop.rep.em.Level;
import com.zhonghe.cop.rep.mapper.TRepGarrepMapper;
import com.zhonghe.cop.rep.service.ICopOpenService;
import com.zhonghe.system.service.ISysDictDataService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: lpg
 * @Date: 2024/03/07
 * @Description:
 */
@Service
@RequiredArgsConstructor
public class ICopOpenServiceImpl implements ICopOpenService {
    private final TRepGarrepMapper tRepGarrepMapper;
    private final ISysDictDataService dictDataService;
    private final TPerPerformanceMapper tPerPerformanceMapper;


    @Override
    public List<OpenMonthReport> queryMonthReport(String repDate) {
        List<OpenMonthReport> openMonthReports = new ArrayList<>();
        List<TRepGarrepTableVo> tRepGarrepTableVos = tRepGarrepMapper.selectRepGarrepList(buildQueryWrapper(repDate));

        List<SysDictData> copPropertys = dictDataService.selectListByDictCode(CopConstants.COP_PROPERTY);


        for (TRepGarrepTableVo tRepGarrepTableVo : tRepGarrepTableVos) {
            OpenMonthReport openMonthReport = BeanUtil.toBean(tRepGarrepTableVo, OpenMonthReport.class);
            //上报日期-1个月
            DateTime dateTime = DateUtil.offsetMonth(tRepGarrepTableVo.getRepDate(), -1);
            openMonthReport.setRepDate(dateTime);

            //风险级别名称
            openMonthReport.setLevelName(Level.getNameByCode(tRepGarrepTableVo.getLevel()));
            //企业性质
            copPropertys.forEach(a -> {
                if (ObjectUtil.isNotNull(tRepGarrepTableVo.getPropertyId()) && a.getDictValue().equals(tRepGarrepTableVo.getPropertyId().toString())) {
                    openMonthReport.setCopProperty(a.getDictLabel());
                }
            });
            //所属镇街
            openMonthReport.setOrgCN("石龙镇");
            openMonthReports.add(openMonthReport);
        }
        return openMonthReports;
    }

    private QueryWrapper<TRepGarrep> buildQueryWrapper(String repDate) {
        QueryWrapper<TRepGarrep> qw = Wrappers.query();
        //上报日期+1个月
        if (StringUtils.isNotBlank(repDate)) {
            DateTime dateTime = DateUtil.offsetMonth(DateUtil.parse(repDate, DateUtils.YYYY_MM), 1);
            qw.apply("to_char(trg.rep_date,'yyyy-MM') = {0}", dateTime.toString(DateUtils.YYYY_MM));
        }
        qw.eq("trg.del_flag", Constants.DEL_FLAG_NORMAL);
        qw.orderByDesc("trg.create_time");
        return qw;
    }


    @Override
    public List<QueryWarningVo> queryWarn(String sjny) {
        //sjny=202406，转换成正确的日期格式
        String regex = "^[1-9]\\d{3}(0[1-9]|1[0-2])$";
        if (!sjny.matches(regex)) {
            throw new ServiceException("数据年月格式不正确");
        }
        String year = sjny.substring(0, 4);
        String month = sjny.substring(4);
        String repDate = year + "-" + month;

        List<TRepGarrepTableVo> tRepGarrepTableVos = tRepGarrepMapper.selectRepGarrepList(buildQueryWrapper(repDate));
        List<QueryWarningVo> queryWarningVos = new ArrayList<>();
        tRepGarrepTableVos.forEach(entity -> {
            QueryWarningVo r = new QueryWarningVo();
            //存在问题的企业
            r.setQymc(entity.getCopName());
            //主键id
            r.setFid(entity.getRepGarrepId());
            //企业风险等级
            r.setFxdj(Level.getNameByCode(entity.getLevel()));
            //工商注册号
            r.setGszch(entity.getBusinessLicence());
            //数据年月
            r.setSjny(sjny);
            //所属镇街
            r.setSszj("石龙镇");
            //拖欠电费金额（元）
            r.setTqdfje(entity.getProenergymoney());
            //拖欠电费月数（月）
            r.setTqdfys(entity.getProenergymonth());
            //拖欠工资金额（元）
            r.setTqgzje(entity.getPropaymoney());
            //拖欠工资人数
            r.setTqgzrs(entity.getPropaypopulation());
            //拖欠工资月数
            r.setTqgzys(entity.getPropaymonth());
            //拖欠社保金额（元）
            r.setTqsbje(entity.getProinsuremoney());
            //拖欠社保月数（月）
            r.setTqsbys(entity.getProinsuremonth());
            //拖欠税费金额（元）
            r.setTqsfje(entity.getProtaxmoney());
            //拖欠水费金额（元）
            r.setTqsfje1(entity.getProwatermoney());
            //欠税费月数（月）
            r.setTqsfys(entity.getProtaxmonth());
            //拖欠水费月数（月）
            r.setTqsfys1(entity.getProwatermonth());
            //拖欠租金金额（元）
            r.setTqzjje(entity.getProrentmoney());
            //拖欠租金月数（月）
            r.setTqzjys(entity.getProrentmonth());

            //统一社会信用代码
            r.setTyshxydm(entity.getCorporateCode());
            queryWarningVos.add(r);
        });

        return queryWarningVos;
    }

    @Override
    public List<OpenPerformance> queryPerformance(String repYear) {
        List<OpenPerformance> openPerformances = new ArrayList<>();

        TPerPerformanceQuery query = new TPerPerformanceQuery();
        if (StringUtils.isNotBlank(repYear)) {
            query.setYear(Integer.valueOf(repYear));
        }

        List<DeptPerformanceVo> deptPerformanceVos = tPerPerformanceMapper.selectDeptPerformance(query);
        //部门名称，党建办、纪检监察办、领导小组办公室排除
        deptPerformanceVos.removeIf(deptPerformanceVo -> "党建办".equals(deptPerformanceVo.getDeptName()) || "纪检监察办".equals(deptPerformanceVo.getDeptName()) || "领导小组办公室".equals(deptPerformanceVo.getDeptName()));
        deptPerformanceVos.forEach(entity -> {
            OpenPerformance openPerformance = BeanUtil.toBean(entity, OpenPerformance.class);
            //量化考核年份
            openPerformance.setRepYear(entity.getYear());
            //一月
            openPerformance.setJan(entity.getJanuary());
            //二月
            openPerformance.setFeb(entity.getFebruary());
            //三月
            openPerformance.setMar(entity.getMarch());
            //四月
            openPerformance.setApr(entity.getApril());
            //五月
            openPerformance.setMay(entity.getMay());
            //六月
            openPerformance.setJune(entity.getJune());
            //七月
            openPerformance.setJuly(entity.getJuly());
            //八月
            openPerformance.setAug(entity.getAugust());
            //九月
            openPerformance.setSept(entity.getSeptember());
            //十月
            openPerformance.setOct(entity.getOctober());
            //十一月
            openPerformance.setNov(entity.getNovember());
            //十二月
            openPerformance.setDec(entity.getDecember());
            //所属镇街
            openPerformance.setOrgCN("石龙镇");
            openPerformances.add(openPerformance);
        });

        return openPerformances;
    }

}
