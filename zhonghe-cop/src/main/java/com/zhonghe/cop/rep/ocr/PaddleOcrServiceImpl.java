package com.zhonghe.cop.rep.ocr;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.cop.rep.config.OcrConfig;
import com.zhonghe.cop.rep.ocr.util.PDFToImageBytes;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.InputStream;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @Description: {PaddleOCR服务实现}
 * @date 2025/07/22
 */
@Slf4j
@Service("paddleOcrService")
public class PaddleOcrServiceImpl implements IOcrService {

    public static final OkHttpClient HTTP_CLIENT = new OkHttpClient().newBuilder()
        .readTimeout(6000, TimeUnit.SECONDS)
        .build();

    @Autowired
    private OcrConfig ocrConfig;

    /**
     * 从原始字符串中截取 startKey 和 endKey 之间的内容。
     */
    public static String extractBetween(String text, String startKey, String endKey) {
        if (text == null || text.isEmpty()) {
            return "";
        }

        int startIndex = 0;

        if (startKey != null && !startKey.isEmpty()) {
            startIndex = text.indexOf(startKey);
            if (startIndex == -1) {
                return "";
            }
            startIndex += startKey.length();
        }

        int endIndex = text.length();

        if (endKey != null && !endKey.isEmpty()) {
            endIndex = text.indexOf(endKey, startIndex);
            if (endIndex == -1) {
                endIndex = text.length();
            }
        }

        if (startIndex >= endIndex) {
            return "";
        }

        return text.substring(startIndex, endIndex).trim();
    }

    /**
     * 从字符串中去掉末尾的日期（保留人名）
     */
    public static String removeDateSuffix(String text) {
        if (text == null) {
            return "";
        }
        return text.replaceFirst("[\\d一二三四五六七八九零〇]{2,4}年\\s*\\d{1,2}月\\s*\\d{1,2}日.*$", "").trim();
    }

    /**
     * 从字符串中提取第一个日期（支持格式：2025年7月31日）
     */
    public static String extractDateFromText(String text) {
        if (text == null) {
            return "";
        }

        Pattern pattern = Pattern.compile("\\d{4}年\\s*\\d{1,2}月\\s*\\d{1,2}日");
        Matcher matcher = pattern.matcher(text);
        if (matcher.find()) {
            return matcher.group().replaceAll("\\s+", "");
        }

        return "";
    }

    @Override
    public Map<String, String> extractFieldsFromDocument(InputStream inputStream, Collection<String> keyList) {
        String htmlContent = getHtmlFromPaddleOcr(inputStream);
        return extractFieldsFromHtml(htmlContent);
    }

    /**
     * 调用PaddleOCR服务获取HTML内容
     */
    @SneakyThrows
    private String getHtmlFromPaddleOcr(InputStream inputStream) {
        String ocrUrl = ocrConfig.getPaddle().getUrl();
        String apiKey = ocrConfig.getPaddle().getApiKey();
        log.info("使用PaddleOCR服务进行表格识别，URL: {}", ocrUrl);

        List<byte[]> bytes = PDFToImageBytes.convertPDFToImageBytes(inputStream);

        try {
            // 将InputStream转换为字节数组
//            ByteArrayOutputStream baos = new ByteArrayOutputStream();
//            byte[] buffer = new byte[1024];
//            int length;
//            while ((length = inputStream.read(buffer)) != -1) {
//                baos.write(buffer, 0, length);
//            }
//            byte[] fileBytes = baos.toByteArray();
            String fileName = System.currentTimeMillis() + "";
//            File tempFile = FileUtil.createTempFile(fileName, ".png", true);
//            FileUtil.writeBytes(bytes.get(0), tempFile);

            OkHttpClient client = new OkHttpClient().newBuilder().readTimeout(60 * 10, TimeUnit.SECONDS)
                .build();
            RequestBody body = new MultipartBody.Builder().setType(MultipartBody.FORM)
                .addFormDataPart("file", fileName + ".png",
                    RequestBody.create(MediaType.parse("application/octet-stream"),
                        bytes.get(0)))
                .build();
            Request request = new Request.Builder()
                .url(ocrUrl)
                .method("POST", body)
                .addHeader("Accept", "*/*")
                .addHeader("Connection", "keep-alive")
                .build();
            Response response = client.newCall(request).execute();

            String json = response.body().string();
            log.info("PaddleOCR服务响应: {}", json);

//            tempFile.delete();

            if (!response.isSuccessful()) {
                log.error("PaddleOCR服务调用失败，状态码: {}, 响应: {}", response.code(), json);
                throw new RuntimeException("PaddleOCR服务调用失败: " + json);
            }

//            Response response = client.newCall(request).execute();
            // 解析响应，获取HTML内容
            com.alibaba.fastjson.JSONObject jsonObject = JSON.parseObject(json);

            // 根据实际的PaddleOCR响应格式解析HTML
            if (jsonObject.containsKey("data")) {
                com.alibaba.fastjson.JSONArray tablesResult = jsonObject.getJSONArray("data");
                com.alibaba.fastjson.JSONObject result_0 = JSON.parseObject(tablesResult.get(0).toString());

                if (result_0.containsKey("table_res_list")) {
                    // 如果没有直接的HTML，从table_res_list中构建HTML
                    com.alibaba.fastjson.JSONArray table_res_list = JSON.parseArray(result_0.get("table_res_list").toString());
                    JSONObject table_res = JSON.parseObject(table_res_list.get(0).toString());
                    if (table_res.containsKey("pred_html")) {
                        log.info("pred_html===============>: {}", table_res.get("pred_html"));
                        return table_res.getString("pred_html");
                    }
                }
            }
            log.error("PaddleOCR服务返回格式不支持: {}", json);
            throw new RuntimeException("PaddleOCR服务返回格式不支持");

        } catch (IOException e) {
            log.error("调用PaddleOCR服务时发生IO异常", e);
            throw new RuntimeException("调用PaddleOCR服务失败", e);
        }
    }

    /**
     * 从HTML内容中提取字段值
     */
    private Map<String, String> extractFieldsFromHtml(String htmlContent) {
        Document doc = Jsoup.parse(htmlContent);
        Elements rows = doc.select("tr");

        LinkedHashMap<String, String> flatMap = new LinkedHashMap<>();
        String lastSection = null;
        for (Element row : rows) {
            Elements tds = row.select("td");
            if (tds.size() == 0) {
                continue;
            }

            // 处理只有一格的行（如签名等）
            if (tds.size() == 1) {
                String key = "备注";
                String value = tds.get(0).text().trim();
                if (value.contains("签名")) {
                    key = "来访人签名：";
                }
                if (value.contains("法律文书指定送达地址")) {
                    key = "法律文书指定送达地址：";
                }
                flatMap.put(key, value);
                continue;
            }

            // 处理有rowspan的section名
            if (tds.get(0).hasAttr("rowspan")) {
                lastSection = tds.get(0).text().replaceAll("\\s+", "");
                // 跳过section名，处理后面的键值对
                for (int i = 1; i < tds.size(); i += 2) {
                    if (i + 1 < tds.size()) {
                        String key = tds.get(i).text().replaceAll("\\s+", "");
                        String value = tds.get(i + 1).text().trim();
                        if (key.contains("联系电话") && flatMap.containsKey("联系电话")) {
                            flatMap.put("企业联系电话", value);
                        } else {
                            flatMap.put(key, value);
                        }
                    }
                }
                continue;
            }

            // 普通的键值对行
            for (int i = 0; i < tds.size(); i += 2) {
                if (i + 1 < tds.size()) {
                    String key = tds.get(i).text().replaceAll("\\s+", "");
                    String value = tds.get(i + 1).text().trim();
                    if (key.contains("联系电话") && flatMap.containsKey("联系电话")) {
                        flatMap.put("企业联系电话", value);
                    } else {
                        flatMap.put(key, value);
                    }
                }
            }
        }
        if (ObjectUtil.isNull(flatMap)) {
            return new LinkedHashMap<>();
        }
        processSpecialFields(flatMap);
        return flatMap;
    }


    /**
     * 处理特殊字段（如签名、日期等）
     */
    private void processSpecialFields(Map<String, String> resultMap) {
        Map<String, String> hashMap = new LinkedHashMap<>();
        for (Map.Entry<String, String> entry : resultMap.entrySet()) {
            String k = entry.getKey();
            String v = entry.getValue();

            if (StringUtils.isBlank(k) || StringUtils.isBlank(v)) {
                continue;
            }
            hashMap.put(k, v);

            // 处理法律文书指定送达地址
            if (k.contains("法律文书指定送达地址")) {
                String v1 = extractBetween(k, "法律文书指定送达地址：", "");
                String v2 = extractBetween(k, "签名：", "法律文书指定送达地址：");
                hashMap.put("法律文书指定送达地址", v1);
                hashMap.put("签名", v2);
            }

            // 处理来访人签名
            if (k.contains("来访人签名")) {
                String v1 = extractBetween(v, "来访人签名：", "劳动保障监察员");
                hashMap.put("来访人签名", removeDateSuffix(v1));
                hashMap.put("来访日期", extractDateFromText(v1));

                String v2 = extractBetween(v, "劳动保障监察员签名：", "");
                hashMap.put("劳动保障监察员签名", removeDateSuffix(v2));
                hashMap.put("劳动保障监察日期", extractDateFromText(v2));
            }
        }
        resultMap.clear();
        resultMap.putAll(hashMap);
    }

    @Override
    public String getServiceType() {
        return "paddle";
    }
}
