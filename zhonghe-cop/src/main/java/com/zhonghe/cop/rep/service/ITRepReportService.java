package com.zhonghe.cop.rep.service;

import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.cop.rep.domain.TRepReport;
import com.zhonghe.cop.rep.domain.bo.HandDeptReportBo;
import com.zhonghe.cop.rep.domain.bo.ReportAuditBo;
import com.zhonghe.cop.rep.domain.bo.ReportResultAuditBo;
import com.zhonghe.cop.rep.domain.bo.TRepReportBo;
import com.zhonghe.cop.rep.domain.dto.DeptTargetDto;
import com.zhonghe.cop.rep.domain.excel.TRepReportExcel;
import com.zhonghe.cop.rep.domain.query.TRepReportQuery;
import com.zhonghe.cop.rep.domain.vo.MonthReportVo;
import com.zhonghe.cop.rep.domain.vo.QuarterReportVo;
import com.zhonghe.cop.rep.domain.vo.TRepGarrepInfoVo;
import com.zhonghe.cop.rep.domain.vo.TRepGarrepMapVo;
import com.zhonghe.cop.rep.domain.vo.TRepReportInfoVo;
import com.zhonghe.cop.rep.domain.vo.YearReportVo;
import com.zhonghe.cop.rep.domain.vo.table.TRepReportTableVo;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 部门上报Service接口
 *
 * <AUTHOR>
 * @date 2023-12-06
 */
public interface ITRepReportService {

    String getTarNote(TRepReport bo);

    /**
     * 查询部门上报
     */
    TRepReportInfoVo queryById(Long repReportId);

    /**
     * 分页查询部门上报
     */
    TableDataInfo<TRepReportTableVo> queryPageList(TRepReportQuery bo, PageQuery pageQuery);

    /**
     * 查询部门上报
     */
    List<TRepReportTableVo> queryList(TRepReportQuery bo);

    /**
     * 新增部门上报
     */
    Boolean insertByBo(TRepReportBo bo);

    /**
     * 根据企业合并上报数据
     *
     * @param repReport
     */
    void mergeCopLevelData(TRepReport repReport);

    /**
     * 修改图层数据
     *
     * @param bo
     */
    void updateBigMapCopLevelData(TRepReport bo);

    /**
     * 删除大屏图层数据
     */
    void deleteCopLevelData();

    void deleteCopLevelDataByCopId(Long copId);

    /**
     * 修改部门上报
     */
    Boolean updateByBo(TRepReportBo bo);

    /**
     * 校验并批量删除部门上报
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 处理上报
     *
     * @param bo
     * @return
     */
    Boolean handleReport(HandDeptReportBo bo, Long repReportId);

    /**
     * 查询指标
     *
     * @return
     */
    List<DeptTargetDto> queryTarget();

    /**
     * 审核
     *
     * @param bo
     * @return
     */
    Boolean audit(ReportAuditBo bo);

    /**
     * 审核退回
     *
     * @param repReportId
     * @return
     */
    Boolean auditBack(Long repReportId);

    /**
     * 查询月度报表
     *
     * @param year
     * @return
     */
    List<MonthReportVo> queryMonthReport(Integer year);

    /**
     * 查询季度报表
     *
     * @param year
     * @return
     */
    List<QuarterReportVo> queryQuarterReport(Integer year);

    /**
     * 查询年度报表
     *
     * @param startYear
     * @param endYear
     * @return
     */
    List<YearReportVo> queryYearReport(Integer startYear, Integer endYear);

    /**
     * 本月无异常上报
     *
     * @return
     */
    Boolean noException();

    /**
     * 处理结果审核
     *
     * @param bo
     * @return
     */
    Boolean resultAudit(ReportResultAuditBo bo);

    boolean point();

    boolean point(Long copId);

    /**
     * 根据企业ID查询最新一条上报信息
     *
     * @param copId
     * @return
     */
    TRepGarrepMapVo queryFirstByCopId(Long copId, String yearMonth);


    boolean dataFormat(String year, String level);

    /**
     * 构建模板下载
     *
     * @param response
     */
    void buildTempLate(HttpServletResponse response);

    /**
     * 解析excel数据
     *
     * @param file
     */
    Map<String, Object> importExcelFile(MultipartFile file);

    /**
     * 导入数据
     *
     * @param tRepReportExcels
     * @return
     */
    Boolean importData(List<TRepReportExcel> tRepReportExcels);

    /**
     * 处理完成上报
     * <p>
     * 如果是一个部门就直接将大屏风险企业去掉；如果存在多个部门上报的情况，对操作给予提示：该企业存在多个部门上报，请联系管理员进行风险降级；
     *
     * @param repReportId
     * @return
     */
    Boolean handleFinishReport(Long repReportId);

    /**
     * 判断企业是否在大屏图层数据中
     *
     * @param copId
     * @return
     */
    Boolean copExistBigMap(Long copId);

    /**
     * 查询企业已上报的时间段
     *
     * @param copId
     * @return
     */
    List<String> queryRepDateRange(@NotNull(message = "主键不能为空") Long copId);

    /**
     * 查询企业上报历史
     *
     * @param copId
     * @param repDate
     * @return
     */
    TRepGarrepInfoVo queryRepHistory(@NotNull(message = "主键不能为空") Long copId, String repDate);
}
