package com.zhonghe.cop.rep.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.zhonghe.common.annotation.ExcelDictFormat;
import com.zhonghe.common.convert.ExcelDictConvert;
import com.zhonghe.framework.config.StripTrailingZerosBigDecimalSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;


/**
 * 部门上报视图对象 t_rep_report
 *
 * <AUTHOR>
 * @date 2023-12-06
 */
@Data
@ApiModel("部门上报视图对象")
@ExcelIgnoreUnannotated
public class TRepReportVo {

    private static final long serialVersionUID = 1L;

    /**
     * 部门上报ID
     */
    @ExcelProperty(value = "部门上报ID")
    @ApiModelProperty("部门上报ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long repReportId;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    @ApiModelProperty("备注")
    private String note;

    /**
     * 上报日期
     */
    @ExcelProperty(value = "上报日期")
    @ApiModelProperty("上报日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date repDate;

    /**
     * 上报用户ID
     */
    @ExcelProperty(value = "上报用户ID")
    @ApiModelProperty("上报用户ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    /**
     * 企业ID
     */
    @ExcelProperty(value = "企业ID")
    @ApiModelProperty("企业ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long copId;

    /**
     * 部门ID
     */
    @ExcelProperty(value = "部门ID")
    @ApiModelProperty("部门ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long deptId;
    /**
     * 部门名称
     */
    @ExcelProperty(value = "部门名称")
    @ApiModelProperty("部门名称")
    private String deptName;

    /**
     * 指标ID（对应t_rep_target表主键）
     */
    @ExcelProperty(value = "指标ID")
    @ApiModelProperty("指标ID（对应t_rep_target表主键）")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long tarId;

    /**
     * 指标值(数值)
     */
    @ExcelProperty(value = "指标值(数值)")
    @ApiModelProperty("指标值(数值)")
    private String tarDecimal;

    /**
     * 指标值(描述)
     */
    @ExcelProperty(value = "指标值(描述)")
    @ApiModelProperty("指标值(描述)")
    private String tarNvarchar;

    /**
     * 处理结果(默认0,0-未处理，1-已处理)
     */
    @ExcelProperty(value = "处理结果")
    @ExcelDictFormat(readConverterExp = "0=未处理,1=已处理")
    @ApiModelProperty("处理结果(默认0,0-未处理，1-已处理)")
    @JsonSerialize(using = ToStringSerializer.class)
    private Integer despacho;
    /**
     * 审核状态（默认0，0——未审核、1——已审核、2退回）
     */
    @ExcelProperty(value = "审核状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "默=认0，0——未审核、1——已审核、2退回")
    @ApiModelProperty("审核状态（默认0，0——未审核、1——已审核、2退回）")
    @JsonSerialize(using = ToStringSerializer.class)
    private Integer auditing;

    /**
     * 处理结果审核（默认0，0——未处理、1——未审核、2——已审核、3退回）
     */
    @ExcelProperty(value = "处理结果审核")
    @ApiModelProperty("处理结果审核（默认0，0——未处理、1——未审核、2——已审核、3退回）")
    @JsonSerialize(using = ToStringSerializer.class)
    private Integer resultAuditing;

    /**
     * 审核意见
     */
    @ApiModelProperty(value = "审核意见")
    private String auditingComment;

    /**
     * 处理结果审核意见
     */
    @ApiModelProperty(value = "处理结果审核意见")
    private String resultAuditingComment;

    /**
     * 处理状态（默认0，0-未处理，1-已处理）
     */
    @ApiModelProperty("处理状态（默认0，0-未处理，1-已处理）")
    private Integer finishStatus;

    /**
     * 企业是否正常生产经营（0-否，1-是）
     */
    @ApiModelProperty(value = "企业是否正常生产经营（0-否，1-是）")
    private String copnormal;
    /**
     * 企业是否出现不正常情况（0-否，1-是）
     */
    @ApiModelProperty(value = "企业是否出现不正常情况（0-否，1-是）")
    private String copabnormal;
    /**
     * 企业是否出现重大异常情况（0-否，1-是）
     */
    @ApiModelProperty(value = "企业是否出现重大异常情况（0-否，1-是）")
    private String copmajorabnormal;

    /**
     * 风险级别（默认0，0——正常、1——问题、2——风险险、3——危险、4——高危）
     */
    @ExcelProperty(value = "风险级别", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "默=认0，0——正常、1——问题、2——风险险、3——危险、4——高危")
    @ApiModelProperty("风险级别（默认0，0——正常、1——问题、2——风险险、3——危险、4——高危）")
    @JsonSerialize(using = ToStringSerializer.class)
    private Integer level;
    /**
     * 拖欠工资月数（月）
     */
    @ApiModelProperty(value = "拖欠工资月数（月）")
    @JsonSerialize(using = StripTrailingZerosBigDecimalSerializer.class)
    private BigDecimal propaymonth;

    /**
     * 拖欠工资（月）
     */
    @ApiModelProperty(value = "拖欠工资（月）")
    private String propaymonths;

    /**
     * 拖欠工资涉及人数（人）
     */
    @ApiModelProperty(value = "拖欠工资涉及人数（人）")
    @JsonSerialize(using = StripTrailingZerosBigDecimalSerializer.class)
    private BigDecimal propaypopulation;

    /**
     * 拖欠工资金额（元）
     */
    @ApiModelProperty(value = "拖欠工资金额（元）")
    private BigDecimal propaymoney;

    /**
     * 拖欠税款月数（月）
     */
    @ApiModelProperty(value = "拖欠税款月数（月）")
    @JsonSerialize(using = StripTrailingZerosBigDecimalSerializer.class)
    private BigDecimal protaxmonth;

    /**
     * 拖欠税款（月）
     */
    @ApiModelProperty(value = "拖欠税款（月）")
    private String protaxmonths;

    /**
     * 拖欠税款金额（元）
     */
    @ApiModelProperty(value = "拖欠税款金额（元）")
    private BigDecimal protaxmoney;

    /**
     * 拖欠社保月数（月）
     */
    @ApiModelProperty(value = "拖欠社保月数（月）")
    @JsonSerialize(using = StripTrailingZerosBigDecimalSerializer.class)
    private BigDecimal proinsuremonth;

    /**
     * 拖欠社保（月）
     */
    @ApiModelProperty(value = "拖欠社保（月）")
    private String proinsuremonts;

    /**
     * 拖欠社保金额（元）
     */
    @ApiModelProperty(value = "拖欠社保金额（元）")
    private BigDecimal proinsuremoney;

    /**
     * 拖欠水费月数（月）
     */
    @ApiModelProperty(value = "拖欠水费月数（月）")
    @JsonSerialize(using = StripTrailingZerosBigDecimalSerializer.class)
    private BigDecimal prowatermonth;

    /**
     * 拖欠水费（月）
     */
    @ApiModelProperty(value = "拖欠水费（月）")
    private Object prowatermonths;

    /**
     * 拖欠水费金额（元）
     */
    @ApiModelProperty(value = "拖欠水费金额（元）")
    private BigDecimal prowatermoney;

    /**
     * 拖欠电费（月）
     */
    @ApiModelProperty(value = "拖欠电费（月）")
    private String proenergymonths;

    /**
     * 拖欠电费金额（元）
     */
    @ApiModelProperty(value = "拖欠电费金额（元）")
    private BigDecimal proenergymoney;

    /**
     * 拖欠租金月数（月）
     */
    @ApiModelProperty(value = "拖欠租金月数（月）")
    @JsonSerialize(using = StripTrailingZerosBigDecimalSerializer.class)
    private BigDecimal prorentmonth;

    /**
     * 拖欠租金（月）
     */
    @ApiModelProperty(value = "拖欠租金（月）")
    private Object prorentmonts;

    /**
     * 拖欠租金金额（元）
     */
    @ApiModelProperty(value = "拖欠租金金额（元）")
    private BigDecimal prorentmoney;

    /**
     * 其他问题
     */
    @ApiModelProperty(value = "其他问题")
    private String problem;

    /**
     * 监管措施
     */
    @ApiModelProperty(value = "监管措施")
    private String copoversee;

    /**
     * 建议措施
     */
    @ApiModelProperty(value = "建议措施")
    private String copadvice;

    /**
     * 采取措施
     */
    @ApiModelProperty(value = "采取措施")
    private String copdeal;

    /**
     * 企业现状
     */
    @ApiModelProperty(value = "企业现状")
    private String copresult;

    /**
     * 后续工作
     */
    @ApiModelProperty(value = "后续工作")
    private String copafterwork;

    /**
     * 请求协助
     */
    @ApiModelProperty(value = "请求协助")
    private String cophelp;
    /**
     * 其他
     */
    private String other;
    /**
     * 欠薪详情
     */
    private List<TRepReportProPayVo> proPayList = new ArrayList<>();

}
