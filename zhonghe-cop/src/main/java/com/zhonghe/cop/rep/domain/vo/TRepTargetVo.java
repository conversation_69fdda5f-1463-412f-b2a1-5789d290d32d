package com.zhonghe.cop.rep.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 上报指标视图对象 t_rep_target
 *
 * <AUTHOR>
 * @date 2023-12-06
 */
@Data
@ApiModel("上报指标视图对象")
@ExcelIgnoreUnannotated
public class TRepTargetVo {

    private static final long serialVersionUID = 1L;

    /**
     * 上报指标ID
     */
    @ExcelProperty(value = "上报指标ID")
    @ApiModelProperty("上报指标ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long repTargetId;

    /**
     * 说明
     */
    @ExcelProperty(value = "说明")
    @ApiModelProperty("说明")
    private String note;

    /**
     * 指标名称
     */
    @ExcelProperty(value = "指标名称")
    @ApiModelProperty("指标名称")
    private String tarName;

    /**
     * 指标值类型(默认0，0——数值、1——描述)
     */
    @ExcelProperty(value = "指标值类型(默认0，0——数值、1——描述)")
    @ApiModelProperty("指标值类型(默认0，0——数值、1——描述)")
    private Long tarValueType;

    /**
     * 指标代码
     */
    @ExcelProperty(value = "指标代码")
    @ApiModelProperty("指标代码")
    private String tarCode;

    /**
     * 指标类型(默认0，0——普通指标、1——处理结果)
     */
    @ExcelProperty(value = "指标类型(默认0，0——普通指标、1——处理结果)")
    @ApiModelProperty("指标类型(默认0，0——普通指标、1——处理结果)")
    private Long tarType;


}
