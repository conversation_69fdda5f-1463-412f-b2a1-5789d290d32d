package com.zhonghe.cop.rep.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhonghe.common.constant.Constants;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.exception.ServiceException;
import com.zhonghe.common.helper.LoginHelper;
import com.zhonghe.common.utils.BeanCopyUtils;
import com.zhonghe.common.utils.DateUtils;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.cop.bam.domain.vo.TCopInformationVo;
import com.zhonghe.cop.bam.mapper.TCopInformationMapper;
import com.zhonghe.cop.rep.domain.TExiReport;
import com.zhonghe.cop.rep.domain.bo.DespachoBo;
import com.zhonghe.cop.rep.domain.bo.HandExiReportBo;
import com.zhonghe.cop.rep.domain.bo.TExiReportBo;
import com.zhonghe.cop.rep.domain.query.TExiReportQuery;
import com.zhonghe.cop.rep.domain.vo.TExiReportVo;
import com.zhonghe.cop.rep.domain.vo.table.TExiReportTableVo;
import com.zhonghe.cop.rep.em.DespachoStatus;
import com.zhonghe.cop.rep.mapper.TExiReportMapper;
import com.zhonghe.cop.rep.service.ITExiReportService;
import com.zhonghe.system.mapper.SysDeptMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

/**
 * 紧急事件上报Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
@RequiredArgsConstructor
@Service
public class TExiReportServiceImpl implements ITExiReportService {

    private final TExiReportMapper baseMapper;
    private final SysDeptMapper sysDeptMapper;
    private final TCopInformationMapper copInformationMapper;

    /**
     * 查询紧急事件上报
     */
    @Override
    public TExiReportVo queryById(Long exiReportId) {
        TExiReportVo tExiReportVo = baseMapper.selectVoById(exiReportId);
        TCopInformationVo TCopInformationVo = copInformationMapper.selectVoById(tExiReportVo.getCopId());
        BeanCopyUtils.copy(TCopInformationVo, tExiReportVo);

        if (StringUtils.isNotBlank(tExiReportVo.getDepts())) {
            Long[] deptIds = Arrays.stream(tExiReportVo.getDepts().split(",")).map(Long::valueOf).toArray(Long[]::new);
            tExiReportVo.setDeptIds(Convert.toStrArray(tExiReportVo.getDepts()));
            sysDeptMapper.selectBatchIds(Arrays.asList(deptIds)).forEach(sysDept -> {
                tExiReportVo.getDeptNames().add(sysDept.getDeptName());
            });
        }

        return tExiReportVo;
    }

    /**
     * 查询紧急事件上报列表
     */
    @Override
    public TableDataInfo<TExiReportTableVo> queryPageList(TExiReportQuery bo, PageQuery pageQuery) {
        QueryWrapper<TExiReport> lqw = buildQueryWrapper(bo);
        Page<TExiReportTableVo> result = baseMapper.selectExiReportList(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询紧急事件上报列表
     */
    @Override
    public List<TExiReportTableVo> queryList(TExiReportQuery bo) {
        QueryWrapper<TExiReport> lqw = buildQueryWrapper(bo);
        return baseMapper.selectExiReportList(lqw);
    }

    private QueryWrapper<TExiReport> buildQueryWrapper(TExiReportQuery bo) {
        QueryWrapper<TExiReport> qw = Wrappers.query();
        qw.like(StringUtils.isNotBlank(bo.getCopName()), "tci.cop_name", bo.getCopName());
        qw.eq(bo.getDespacho() != null, "ter.despacho", bo.getDespacho());
        if (StringUtils.isNotBlank(bo.getDf())) {
            qw.like("ter.depts", LoginHelper.getDeptId());
        }
        qw.eq("ter.del_flag", Constants.DEL_FLAG_NORMAL);
        qw.orderByDesc("ter.create_time");
        return qw;
    }

    /**
     * 新增紧急事件上报
     */
    @Override
    public Boolean insertByBo(TExiReportBo bo) {
        TExiReport add = BeanUtil.toBean(bo, TExiReport.class);
        add.setDeptId(LoginHelper.getDeptId());
        add.setRepDate(DateUtils.getNowDate());
        add.setDespacho(DespachoStatus.UNDEAL.getCode());
        //查询企业
        TCopInformationVo TCopInformationVo = copInformationMapper.selectVoById(bo.getCopId());
        Assert.notNull(TCopInformationVo, "企业不存在");
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setExiReportId(add.getExiReportId());
        }
        return flag;
    }

    /**
     * 修改紧急事件上报
     */
    @Override
    public Boolean updateByBo(TExiReportBo bo) {
        TExiReport update = BeanUtil.toBean(bo, TExiReport.class);
        update.setDeptId(LoginHelper.getDeptId());
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(TExiReport entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public void htmlToDoc(MultipartFile file, HttpServletResponse response) {
        //TODO
    }


    @Override
    public Boolean despacho(DespachoBo bo) {
        TExiReport tExiReport = baseMapper.selectById(bo.getExiReportId());
        //修改紧急事件上报
        tExiReport.setDespachoNote(bo.getDespachoNote());
        tExiReport.setDespacho(DespachoStatus.DEAL.getCode());
        String[] deptIds = bo.getDeptIds();
        tExiReport.setDepts(StringUtils.join(deptIds, ","));

        return baseMapper.updateById(tExiReport) > 0;
    }

    @Override
    public Boolean handleReport(HandExiReportBo bo) {
        TExiReport tExiReport = baseMapper.selectById(bo.getExiReportId());
        //修改紧急事件上报
        tExiReport.setCopdeal(bo.getCopdeal());
        tExiReport.setCopresult(bo.getCopresult());
        tExiReport.setCopafterwork(bo.getCopafterwork());
        tExiReport.setCophelp(bo.getCophelp());
        return baseMapper.updateById(tExiReport) > 0;
    }

    /**
     * 批量删除紧急事件上报
     */
    @Override
    @Transactional
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // 未处理才可以删除
            baseMapper.selectBatchIds(ids).forEach(exiHandle -> {
                if (exiHandle.getDespacho().equals(DespachoStatus.DEAL.getCode())) {
                    throw new ServiceException("已处理的紧急事件不能删除");
                }
            });
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
