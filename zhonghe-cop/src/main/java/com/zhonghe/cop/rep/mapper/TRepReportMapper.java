package com.zhonghe.cop.rep.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhonghe.common.annotation.DataColumn;
import com.zhonghe.common.annotation.DataPermission;
import com.zhonghe.common.core.mapper.BaseMapperPlus;
import com.zhonghe.cop.rep.domain.TRepReport;
import com.zhonghe.cop.rep.domain.dto.CopLevelDto;
import com.zhonghe.cop.rep.domain.vo.MonthReportVo;
import com.zhonghe.cop.rep.domain.vo.QuarterReportVo;
import com.zhonghe.cop.rep.domain.vo.TRepReportVo;
import com.zhonghe.cop.rep.domain.vo.YearReportVo;
import com.zhonghe.cop.rep.domain.vo.table.TRepReportTableVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 部门上报，根据指标动态生成部门上报大报，然后上报时同时插入，大报用来查询，本作数据根备份Mapper接口
 *
 * <AUTHOR>
 * @date 2023-12-06
 */
public interface TRepReportMapper extends BaseMapperPlus<TRepReportMapper, TRepReport, TRepReportVo> {

    /**
     * 分页查询上报列表
     *
     * @param build
     * @param lqw
     * @return
     */
    @DataPermission({
        @DataColumn(key = "deptName", value = "d.dept_id")
    })
    Page<TRepReportTableVo> selectRepReportList(Page<TRepReport> build, @Param(Constants.WRAPPER) Wrapper<TRepReport> lqw);

    /**
     * 查询上报列表
     *
     * @param lqw
     * @return
     */
    List<TRepReportTableVo> selectRepReportList(@Param(Constants.WRAPPER) Wrapper<TRepReport> lqw);

    /**
     * 查询年度报表
     *
     * @param year
     * @return
     */
    List<MonthReportVo> queryMonthReport(Integer year);

    /**
     * 查询季度报表
     */
    List<QuarterReportVo> queryQuarterReport(Integer year);

    /**
     * 查询年度报表
     */
    List<YearReportVo> queryYearReport(@Param("startYear") Integer startYear, @Param("endYear") Integer endYear);

    List<CopLevelDto> dataFormat(@Param("year") String year, @Param("level") String level);
}
