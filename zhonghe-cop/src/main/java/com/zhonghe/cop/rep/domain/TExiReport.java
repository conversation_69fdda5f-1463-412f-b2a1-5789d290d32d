package com.zhonghe.cop.rep.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zhonghe.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;


/**
 * 紧急事件上报对象 t_exi_report
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("cop.t_exi_report")
public class TExiReport extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 紧急事件上报ID
     */
    @TableId
    private Long exiReportId;
    /**
     * 是否删除
     */
    @TableLogic
    private String delFlag;
    /**
     * 备注
     */
    private String note;
    /**
     * 部门ID
     */
    private Long deptId;
    /**
     * 企业ID
     */
    private Long copId;
    /**
     * 事件描述
     */
    private String description;
    /**
     * 上报时间
     */
    private Date repDate;
    /**
     * 处理结果0-未处理，1-已处理
     */
    private Integer despacho;
    /**
     * 处理批示
     */
    private String despachoNote;

    /**
     * 采取措施
     */
    private String copdeal;

    /**
     * 企业现状
     */
    private String copresult;

    /**
     * 后续工作
     */
    private String copafterwork;

    /**
     * 请求协助
     */
    private String cophelp;
    /**
     * 涉及部门
     */
    private String depts;

}
