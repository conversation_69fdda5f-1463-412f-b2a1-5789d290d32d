package com.zhonghe.cop.rep.domain.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


/**
 * 汇总上报
 *
 * <AUTHOR>
 * @date 2023-12-06
 */

@Data
@ApiModel("报表上报查询对象")
public class TRepGarrepQuery implements Serializable {

    private static final long serialVersionUID = 1569118841566072056L;

    /**
     * 企业名称
     */
    @ApiModelProperty(value = "企业名称")
    private String copName;

    /**
     * 组织
     */
    @ApiModelProperty(value = "组织")
    private String orgName;

    /**
     * 上报日期
     */
    @ApiModelProperty(value = "上报日期")
    private String repDate;

    /**
     * 上报日期范围
     */
    @ApiModelProperty(value = "上报日期范围(年月)")
    private String[] repDateRange;

    /**
     * 风险级别（默认0，0——正常、1——问题、2——风险险、3——危险、4——高危）
     */
    @ApiModelProperty(value = "风险级别（默认0，0——正常、1——问题、2——风险险、3——危险、4——高危）")
    private Integer level;


}
