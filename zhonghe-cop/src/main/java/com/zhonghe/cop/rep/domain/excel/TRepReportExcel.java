package com.zhonghe.cop.rep.domain.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import java.math.BigDecimal;

/**
 * @Author: lpg
 * @Date: 2023/12/28/12:00
 * @Description:
 */
@Data
public class TRepReportExcel {

    /**
     * 企业名称
     */
    @ExcelProperty(value = "企业名称")
    @Length(max = 200, message = "企业名称长度不能超过200")
    private String copName;
    /**
     * 企业风险评级
     */
    @ExcelProperty(value = "企业风险评级")
    @Length(max = 200, message = "企业风险评级长度不能超过200")
    private String copLevel;

    /**
     * 拖欠工资月数（月）
     */
    @ExcelProperty(value = "拖欠工资月数（月）")
    private BigDecimal propaymonth;

    /**
     * 拖欠工资涉及人数（人）
     */
    @ExcelProperty(value = "拖欠工资涉及人数（人）")
    private BigDecimal propaypopulation;

    /**
     * 拖欠工资金额（元）
     */
    @ExcelProperty(value = "拖欠工资金额（元）")
    private BigDecimal propaymoney;

    /**
     * 拖欠税款月数（月）
     */
    @ExcelProperty(value = "拖欠税款月数（月）")
    private BigDecimal protaxmonth;

    /**
     * 拖欠税款金额（元）
     */
    @ExcelProperty(value = "拖欠税款金额（元）")
    private BigDecimal protaxmoney;

    /**
     * 拖欠社保月数（月）
     */
    @ExcelProperty(value = "拖欠社保月数（月）")
    private BigDecimal proinsuremonth;

    /**
     * 拖欠社保金额（元）
     */
    @ExcelProperty(value = "拖欠社保金额（元）")
    private BigDecimal proinsuremoney;

    /**
     * 拖欠水费月数（月）
     */
    @ExcelProperty(value = "拖欠水费月数（月）")
    private BigDecimal prowatermonth;

    /**
     * 拖欠水费金额（元）
     */
    @ExcelProperty(value = "拖欠水费金额（元）")
    private BigDecimal prowatermoney;

    /**
     * 拖欠电费月数（月）
     */
    @ExcelProperty(value = "拖欠电费月数（月）")
    private BigDecimal proenergymonth;

    /**
     * 拖欠电费金额（元）
     */
    @ExcelProperty(value = "拖欠电费金额（元）")
    private BigDecimal proenergymoney;

    /**
     * 拖欠租金月数（月）
     */
    @ExcelProperty(value = "拖欠租金月数（月）")
    private BigDecimal prorentmonth;

    /**
     * 拖欠租金金额（元）
     */
    @ExcelProperty(value = "拖欠租金金额（元）")
    private BigDecimal prorentmoney;

    /**
     * 其他问题
     */
    @ExcelProperty(value = "其他问题")
    @Length(max = 4000, message = "其他问题长度不能超过4000")
    private String problem;

    /**
     * 监管措施
     */
    @ExcelProperty(value = "监管措施")
    @Length(max = 4000, message = "监管措施长度不能超过4000")
    private String copoversee;

    /**
     * 建议措施
     */
    @ExcelProperty(value = "建议措施")
    @Length(max = 4000, message = "建议措施长度不能超过4000")
    private String copadvice;
}
