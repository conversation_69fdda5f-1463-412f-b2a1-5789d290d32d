package com.zhonghe.cop.rep.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description: {OCR配置类}
 * @date 2025/07/22
 */
@Data
@Component
@ConfigurationProperties(prefix = "ocr")
public class OcrConfig {

    /**
     * OCR服务配置
     */
    private Service service = new Service();

    /**
     * 百度OCR服务配置
     */
    private Baidu baidu = new Baidu();

    /**
     * PaddleOCR服务配置
     */
    private Paddle paddle = new Paddle();

    @Data
    public static class Service {
        /**
         * OCR服务类型: baidu(百度OCR) 或 paddle(PaddleOCR)
         */
        private String type = "baidu";
    }

    @Data
    public static class Baidu {
        /**
         * 百度OCR应用ID
         */
        private String appId;

        /**
         * 百度OCR API Key
         */
        private String apiKey;

        /**
         * 百度OCR Secret Key
         */
        private String secretKey;
    }

    @Data
    public static class Paddle {
        /**
         * PaddleOCR服务地址
         */
        private String url = "";

        /**
         * API密钥（可选）
         */
        private String apiKey = "";
    }
}
