package com.zhonghe.cop.rep.domain.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * 部门上报业务对象
 *
 * <AUTHOR>
 * @date 2023-12-06
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ApiModel("部门上报业务对象")
public class TRepReportBo implements Serializable {

    private static final long serialVersionUID = 4600420628703403276L;
    /**
     * 上报ID
     */
    @ApiModelProperty(value = "上报ID", required = true)
    @NotNull(message = "上报ID不能为空", groups = {EditGroup.class})
    private Long repReportId;

    /**
     * 上报日期
     */
    @ApiModelProperty(value = "上报日期", required = true)
    @NotNull(message = "上报日期不能为空", groups = {AddGroup.class, EditGroup.class})
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date repDate;

    /**
     * 企业ID
     */
    @ApiModelProperty(value = "企业ID", required = true)
    @NotNull(message = "企业ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long copId;
    /**
     * 企业是否正常生产经营（0-否，1-是）
     */
    @ApiModelProperty(value = "企业是否正常生产经营（0-否，1-是）")
    private String copnormal;
    /**
     * 企业是否出现不正常情况（0-否，1-是）
     */
    @ApiModelProperty(value = "企业是否出现不正常情况（0-否，1-是）")
    private String copabnormal;
    /**
     * 企业是否出现重大异常情况（0-否，1-是）
     */
    @ApiModelProperty(value = "企业是否出现重大异常情况（0-否，1-是）")
    private String copmajorabnormal;

    /**
     * 风险等级（默认0，0——正常、1——问题、2——风险、3——危险、4——高危）
     */
    @ApiModelProperty(value = "风险等级", required = true)
    @NotNull(message = "风险等级不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer level;

    /**
     * 拖欠工资月数（月）
     */
    @ApiModelProperty(value = "拖欠工资月数（月）")
    @DecimalMin(value = "0.00", message = "拖欠工资月数（月）不能小于0", groups = {AddGroup.class, EditGroup.class})
    @DecimalMax(value = "99999999.99", message = "拖欠工资月数（月）不能大于99999999.99", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal propaymonth;

    /**
     * 拖欠工资月数（月）
     */
    @ApiModelProperty(value = "拖欠工资（月）")
    private String propaymonths;

    /**
     * 拖欠工资涉及人数（人）
     */
    @ApiModelProperty(value = "拖欠工资涉及人数（人）")
    @DecimalMin(value = "0.00", message = "拖欠工资涉及人数（人）不能小于0", groups = {AddGroup.class, EditGroup.class})
    @DecimalMax(value = "99999999.99", message = "拖欠工资涉及人数（人）不能大于99999999.99", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal propaypopulation;

    /**
     * 拖欠工资金额（元）
     */
    @ApiModelProperty(value = "拖欠工资金额（元）")
    @DecimalMin(value = "0.00", message = "拖欠工资金额（元）不能小于0", groups = {AddGroup.class, EditGroup.class})
    @DecimalMax(value = "99999999.99", message = "拖欠工资金额（元）不能大于99999999.99", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal propaymoney;

    /**
     * 拖欠税款月数（月）
     */
    @ApiModelProperty(value = "拖欠税款月数（月）")
    @DecimalMin(value = "0.00", message = "拖欠税款月数（月）不能小于0", groups = {AddGroup.class, EditGroup.class})
    @DecimalMax(value = "99999999.99", message = "拖欠税款月数（月）不能大于99999999.99", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal protaxmonth;

    /**
     * 拖欠税款（月）
     */
    @ApiModelProperty(value = "拖欠税款（月）")
    private String protaxmonths;

    /**
     * 拖欠税款金额（元）
     */
    @ApiModelProperty(value = "拖欠税款金额（元）")
    @DecimalMin(value = "0.00", message = "拖欠税款金额（元）不能小于0", groups = {AddGroup.class, EditGroup.class})
    @DecimalMax(value = "99999999.99", message = "拖欠税款金额（元）不能大于99999999.99", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal protaxmoney;

    /**
     * 拖欠社保月数（月）
     */
    @ApiModelProperty(value = "拖欠社保月数（月）")
    @DecimalMin(value = "0.00", message = "拖欠社保月数（月）不能小于0", groups = {AddGroup.class, EditGroup.class})
    @DecimalMax(value = "99999999.99", message = "拖欠社保月数（月）不能大于99999999.99", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal proinsuremonth;

    /**
     * 拖欠社保（月）
     */
    @ApiModelProperty(value = "拖欠社保（月）")
    private String proinsuremonths;

    /**
     * 拖欠社保金额（元）
     */
    @ApiModelProperty(value = "拖欠社保金额（元）")
    @DecimalMin(value = "0.00", message = "拖欠社保金额（元）不能小于0", groups = {AddGroup.class, EditGroup.class})
    @DecimalMax(value = "99999999.99", message = "拖欠社保金额（元）不能大于99999999.99", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal proinsuremoney;

    /**
     * 拖欠水费月数（月）
     */
    @ApiModelProperty(value = "拖欠水费月数（月）")
    @DecimalMin(value = "0.00", message = "拖欠水费月数（月）不能小于0", groups = {AddGroup.class, EditGroup.class})
    @DecimalMax(value = "99999999.99", message = "拖欠水费月数（月）不能大于99999999.99", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal prowatermonth;

    /**
     * 拖欠水费（月）
     */
    @ApiModelProperty(value = "拖欠水费（月）")
    private String prowatermonths;


    /**
     * 拖欠水费金额（元）
     */
    @ApiModelProperty(value = "拖欠水费金额（元）")
    @DecimalMin(value = "0.00", message = "拖欠水费金额（元）不能小于0", groups = {AddGroup.class, EditGroup.class})
    @DecimalMax(value = "99999999.99", message = "拖欠水费金额（元）不能大于99999999.99", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal prowatermoney;

    /**
     * 拖欠电费月数（月）
     */
    @ApiModelProperty(value = "拖欠电费月数（月）")
    @DecimalMin(value = "0.00", message = "拖欠电费月数（月）不能小于0", groups = {AddGroup.class, EditGroup.class})
    @DecimalMax(value = "99999999.99", message = "拖欠电费月数（月）不能大于99999999.99", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal proenergymonth;

    /**
     * 拖欠电费（月）
     */
    @ApiModelProperty(value = "拖欠电费（月）")
    private String proenergymonths;

    /**
     * 拖欠电费金额（元）
     */
    @ApiModelProperty(value = "拖欠电费金额（元）")
    @DecimalMin(value = "0.00", message = "拖欠电费金额（元）不能小于0", groups = {AddGroup.class, EditGroup.class})
    @DecimalMax(value = "99999999.99", message = "拖欠电费金额（元）不能大于99999999.99", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal proenergymoney;

    /**
     * 拖欠租金月数（月）
     */
    @ApiModelProperty(value = "拖欠租金月数（月）")
    @DecimalMin(value = "0.00", message = "拖欠租金月数（月）不能小于0")
    @DecimalMax(value = "99999999.99", message = "拖欠租金月数（月）不能大于99999999.99", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal prorentmonth;

    /**
     * 拖欠租金（月）
     */
    @ApiModelProperty(value = "拖欠租金（月）")
    private String prorentmonths;

    /**
     * 拖欠租金金额（元）
     */
    @ApiModelProperty(value = "拖欠租金金额（元）")
    @DecimalMin(value = "0.00", message = "拖欠租金金额（元）不能小于0", groups = {AddGroup.class, EditGroup.class})
    @DecimalMax(value = "99999999.99", message = "拖欠租金金额（元）不能大于99999999.99", groups = {AddGroup.class, EditGroup.class})
    private BigDecimal prorentmoney;

    /**
     * 其他
     */
    @ApiModelProperty(value = "其他")
    @Length(max = 1000, message = "其他长度不能超过1000")
    private String other;

    /**
     * 其他问题
     */
    @ApiModelProperty(value = "其他问题")
    @Length(max = 4000, message = "其他问题长度不能超过4000")
    private String problem;

    /**
     * 监控措施
     */
    @ApiModelProperty(value = "监控措施")
    @Length(max = 4000, message = "监控措施长度不能超过4000")
    @NotBlank(message = "监控措施不能为空", groups = {AddGroup.class, EditGroup.class})
    private String copoversee;

    /**
     * 建议措施
     */
    @ApiModelProperty(value = "建议措施")
    @Length(max = 4000, message = "建议措施长度不能超过4000")
    @NotBlank(message = "建议措施不能为空", groups = {AddGroup.class, EditGroup.class})
    private String copadvice;

    /**
     * 采取措施
     */
    @ApiModelProperty(value = "采取措施")
    @Length(max = 4000, message = "采取措施长度不能超过4000")
    private String copdeal;

    /**
     * 企业现状
     */
    @ApiModelProperty(value = "企业现状")
    @Length(max = 4000, message = "企业现状长度不能超过4000")
    private String copresult;

    /**
     * 后续工作
     */
    @ApiModelProperty(value = "后续工作")
    @Length(max = 4000, message = "后续工作长度不能超过4000")
    private String copafterwork;

    /**
     * 请求协助
     */
    @ApiModelProperty(value = "请求协助")
    @Length(max = 4000, message = "请求协助长度不能超过4000")
    private String cophelp;


}
