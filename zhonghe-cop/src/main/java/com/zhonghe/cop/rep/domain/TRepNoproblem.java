package com.zhonghe.cop.rep.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zhonghe.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 无异常上报对象 t_rep_noproblem
 *
 * <AUTHOR>
 * @date 2024-01-23
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("cop.t_rep_noproblem")
public class TRepNoproblem extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 无异常上报ID
     */
    @TableId(value = "rep_noproblem_id")
    private Long repNoproblemId;
    /**
     * 是否删除
     */
    @TableLogic
    private String delFlag;
    /**
     * 备注
     */
    private String note;
    /**
     * 上报日期（月份）
     */
    private Date repDate;
    /**
     * 上报用户ID
     */
    private Long userId;
    /**
     * 部门ID
     */
    private Long deptId;
    /**
     * 审核状态（默认0，0——未审核、1——已审核、2退回）
     */
    private Integer auditing;

}
