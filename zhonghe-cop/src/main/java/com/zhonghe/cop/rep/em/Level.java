package com.zhonghe.cop.rep.em;

import lombok.Getter;

/**
 * @Author: lpg
 * @Date: 2023/12/08/13:41
 * @Description: 风险等级
 */
@Getter
public enum Level {
    /**
     * 正常
     */
    NORMAL(0, "正常", 0),
    /**
     * 问题
     */
    PROBLEM(1, "问题", 4),
    /**
     * 风险
     */
    RISK(2, "风险", 6),
    /**
     * 危险
     */
    DANGER(3, "危险", 8),
    /**
     * 高危
     */
    HIGH_DANGER(4, "高危", 10);

    private final Integer code;
    private final String name;
    /**
     * 扣分
     */
    private final Integer deductScore;

    Level(Integer code, String name, Integer deductScore) {
        this.code = code;
        this.name = name;
        this.deductScore = deductScore;
    }

    /**
     * 根据code获取扣分
     */
    public static Integer getDeductScoreByCode(Integer code) {
        for (Level level : Level.values()) {
            if (level.getCode().equals(code)) {
                return level.getDeductScore();
            }
        }
        return null;

    }

    /**
     * 根据code获取name
     *
     * @param code
     * @return
     */
    public static String getNameByCode(Integer code) {
        for (Level level : Level.values()) {
            if (level.getCode().equals(code)) {
                return level.getName();
            }
        }
        return null;
    }

    public static Integer getCodeByName(String name) {
        for (Level level : Level.values()) {
            if (level.getName().equals(name)) {
                return level.getCode();
            }
        }
        return null;

    }

    public static boolean isExist(String string) {
        for (Level level : Level.values()) {
            if (level.getName().equals(string)) {
                return true;
            }
        }
        return false;
    }
}
