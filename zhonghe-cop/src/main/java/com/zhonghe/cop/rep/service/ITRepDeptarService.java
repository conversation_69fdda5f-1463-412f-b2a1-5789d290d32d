package com.zhonghe.cop.rep.service;

import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.cop.rep.domain.bo.TRepDeptarBo;
import com.zhonghe.cop.rep.domain.vo.TRepDeptarVo;

import java.util.Collection;
import java.util.List;

/**
 * 指标-部门关联，用于生成上报报Service接口
 *
 * <AUTHOR>
 * @date 2023-12-06
 */
public interface ITRepDeptarService {

    /**
     * 查询指标-部门关联，用于生成上报报
     */
    TRepDeptarVo queryById(Long repDeptarId);

    /**
     * 查询指标-部门关联，用于生成上报报列表
     */
    TableDataInfo<TRepDeptarVo> queryPageList(TRepDeptarBo bo, PageQuery pageQuery);

    /**
     * 查询指标-部门关联，用于生成上报报列表
     */
    List<TRepDeptarVo> queryList(TRepDeptarBo bo);

    /**
     * 修改指标-部门关联，用于生成上报报
     */
    Boolean insertByBo(TRepDeptarBo bo);

    /**
     * 修改指标-部门关联，用于生成上报报
     */
    Boolean updateByBo(TRepDeptarBo bo);

    /**
     * 校验并批量删除指标-部门关联，用于生成上报报信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
