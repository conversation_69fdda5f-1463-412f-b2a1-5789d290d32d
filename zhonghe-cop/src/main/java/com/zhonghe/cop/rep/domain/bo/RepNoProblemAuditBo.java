package com.zhonghe.cop.rep.domain.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Author: lpg
 * @Date: 2023/12/14/09:21
 * @Description:
 */
@Data
@ApiModel("无异常上报审核对象")
public class RepNoProblemAuditBo implements Serializable {
    private static final long serialVersionUID = 821702688771636991L;
    /**
     * 无异常上报ID
     */
    @ApiModelProperty(value = "无异常上报ID", required = true)
    @NotNull(message = "无异常上报ID不能为空")
    private Long repNoproblemId;

    /**
     * 审核状态（默认0，0——未审核、1——已审核、2退回）
     */
    @ApiModelProperty(value = "审核状态（默认0，0-未审核、1-已审核、2-退回）", required = true)
    @NotNull(message = "审核状态不能为空")
    private Integer auditing;
}
