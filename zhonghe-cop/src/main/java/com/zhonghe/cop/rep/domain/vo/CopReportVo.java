package com.zhonghe.cop.rep.domain.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import lombok.Data;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: lpg
 * @Date: 2023/12/17/09:21
 * @Description:
 */
@Data
public class CopReportVo implements Serializable, TransPojo {
    private static final long serialVersionUID = 8605765471539244232L;
    private Map<String, Object> transMap = new HashMap<>();
    /**
     * 企业ID
     */
    private Long copId;

    /**
     * 企业名称
     */
    private String copName;
    /**
     * 维度
     */
    private String lon;
    /**
     * 经度
     */
    private String lat;

    /**
     * 风险级别ID
     */
    @Trans(type = TransType.DICTIONARY, target = CopReportVo.class, key = "cop_level", ref = "levelName")
    private Long level;

    /**
     * 风险级别名称
     */
    private String levelName;
}
