package com.zhonghe.cop.rep.domain.bo;

import com.zhonghe.common.core.domain.BaseEntity;
import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


/**
 * 上报指标业务对象 t_rep_target
 *
 * <AUTHOR>
 * @date 2023-12-06
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("上报指标业务对象")
public class TRepTargetBo extends BaseEntity {

    private static final long serialVersionUID = 3073331504353946246L;
    /**
     * 上报指标ID
     */
    @ApiModelProperty(value = "上报指标ID", required = true)
    @NotNull(message = "上报指标ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long repTargetId;

    /**
     * 说明
     */
    @ApiModelProperty(value = "说明", required = true)
    @NotBlank(message = "说明不能为空", groups = {AddGroup.class, EditGroup.class})
    private String note;

    /**
     * 指标名称
     */
    @ApiModelProperty(value = "指标名称", required = true)
    @NotBlank(message = "指标名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String tarName;

    /**
     * 指标值类型(默认0，0——数值、1——描述)
     */
    @ApiModelProperty(value = "指标值类型(默认0，0——数值、1——描述)", required = true)
    @NotNull(message = "指标值类型(默认0，0——数值、1——描述)不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long tarValueType;

    /**
     * 指标代码
     */
    @ApiModelProperty(value = "指标代码", required = true)
    @NotBlank(message = "指标代码不能为空", groups = {AddGroup.class, EditGroup.class})
    private String tarCode;

    /**
     * 指标类型(默认0，0——普通指标、1——处理结果)
     */
    @ApiModelProperty(value = "指标类型(默认0，0——普通指标、1——处理结果)", required = true)
    @NotNull(message = "指标类型(默认0，0——普通指标、1——处理结果)不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long tarType;


}
