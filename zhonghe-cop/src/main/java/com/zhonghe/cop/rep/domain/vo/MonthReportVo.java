package com.zhonghe.cop.rep.domain.vo;

import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * @Author: lpg
 * @Date: 2023/12/08/15:10
 * @Description: 报表对象
 */
@ApiModel("月报表对象")
@Data
public class MonthReportVo {
    /**
     * 年份
     */
    private Integer year;
    /**
     * 上报总数
     */
    private Integer total;
    /**
     * 1月份
     */
    private Integer january;
    /**
     * 2月份
     */
    private Integer february;
    /**
     * 3月份
     */
    private Integer march;
    /**
     * 4月份
     */
    private Integer april;
    /**
     * 5月份
     */
    private Integer may;
    /**
     * 6月份
     */
    private Integer june;
    /**
     * 7月份
     */
    private Integer july;
    /**
     * 8月份
     */
    private Integer august;
    /**
     * 9月份
     */
    private Integer september;
    /**
     * 10月份
     */
    private Integer october;
    /**
     * 11月份
     */
    private Integer november;
    /**
     * 12月份
     */
    private Integer december;


}
