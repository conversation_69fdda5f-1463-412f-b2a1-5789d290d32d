package com.zhonghe.cop.rep.domain.bo;

import com.zhonghe.cop.rep.domain.TRepReportPropay;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: lpg
 * @Date: 2023/12/06
 * @Description: 处理部门上报业务对象
 */
@Data
public class HandDeptReportBo implements Serializable {
    private static final long serialVersionUID = 6143956265902778147L;
    /**
     * 采取措施
     */
    @ApiModelProperty(value = "采取措施")
    @Length(max = 4000, message = "采取措施长度不能超过4000")
    @NotBlank(message = "采取措施不能为空")
    private String copdeal;

    /**
     * 企业现状
     */
    @ApiModelProperty(value = "企业现状")
    @Length(max = 4000, message = "企业现状长度不能超过4000")
    @NotBlank(message = "企业现状不能为空")
    private String copresult;

    /**
     * 后续工作
     */
    @ApiModelProperty(value = "后续工作")
    @Length(max = 4000, message = "后续工作长度不能超过4000")
    @NotBlank(message = "后续工作不能为空")
    private String copafterwork;

    /**
     * 请求协助
     */
    @ApiModelProperty(value = "请求协助")
    @Length(max = 4000, message = "请求协助长度不能超过4000")
    private String cophelp;

    /**
     * 欠薪详情
     */
    private List<TRepReportPropay> proPayList = new ArrayList<>();
}
