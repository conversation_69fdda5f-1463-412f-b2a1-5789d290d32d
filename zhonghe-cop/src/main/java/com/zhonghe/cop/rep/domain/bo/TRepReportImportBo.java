package com.zhonghe.cop.rep.domain.bo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;


/**
 * 部门上报业务导入对象
 *
 * <AUTHOR>
 * @date 2023-12-06
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Data
@ApiModel("部门上报业务导入对象")
public class TRepReportImportBo implements Serializable {

    private static final long serialVersionUID = 4600420628703403276L;


    /**
     * 风险等级（默认0，0——正常、1——问题、2——风险险、3——危险、4——高危）
     */
    @ApiModelProperty(value = "风险等级", required = true)
    @NotNull(message = "风险等级不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer levelName;

    /**
     * 拖欠工资月数（月）
     */
    @ApiModelProperty(value = "拖欠工资月数（月）")
    private BigDecimal propaymonth;

    /**
     * 拖欠工资涉及人数（人）
     */
    @ApiModelProperty(value = "拖欠工资涉及人数（人）")
    private BigDecimal propaypopulation;

    /**
     * 拖欠工资金额（元）
     */
    @ApiModelProperty(value = "拖欠工资金额（元）")
    private BigDecimal propaymoney;

    /**
     * 拖欠税款月数（月）
     */
    @ApiModelProperty(value = "拖欠税款月数（月）")
    private BigDecimal protaxmonth;

    /**
     * 拖欠税款金额（元）
     */
    @ApiModelProperty(value = "拖欠税款金额（元）")
    private BigDecimal protaxmoney;

    /**
     * 拖欠社保月数（月）
     */
    @ApiModelProperty(value = "拖欠社保月数（月）")
    private BigDecimal proinsuremonth;

    /**
     * 拖欠社保金额（元）
     */
    @ApiModelProperty(value = "拖欠社保金额（元）")
    private BigDecimal proinsuremoney;

    /**
     * 拖欠水费月数（月）
     */
    @ApiModelProperty(value = "拖欠水费月数（月）")
    private BigDecimal prowatermonth;

    /**
     * 拖欠水费金额（元）
     */
    @ApiModelProperty(value = "拖欠水费金额（元）")
    private BigDecimal prowatermoney;

    /**
     * 拖欠电费月数（月）
     */
    @ApiModelProperty(value = "拖欠电费月数（月）")
    private BigDecimal proenergymonth;

    /**
     * 拖欠电费金额（元）
     */
    @ApiModelProperty(value = "拖欠电费金额（元）")
    private BigDecimal proenergymoney;

    /**
     * 拖欠租金月数（月）
     */
    @ApiModelProperty(value = "拖欠租金月数（月）")
    private BigDecimal prorentmonth;

    /**
     * 拖欠租金金额（元）
     */
    @ApiModelProperty(value = "拖欠租金金额（元）")
    private BigDecimal prorentmoney;

    /**
     * 其他问题
     */
    @ApiModelProperty(value = "其他问题")
    @Length(max = 4000, message = "其他问题长度不能超过4000")
    private String problem;

    /**
     * 监管措施
     */
    @ApiModelProperty(value = "监管措施")
    @Length(max = 4000, message = "监管措施长度不能超过4000")
    private String copoversee;

    /**
     * 建议措施
     */
    @ApiModelProperty(value = "建议措施")
    @Length(max = 4000, message = "建议措施长度不能超过4000")
    private String copadvice;

    /**
     * 采取措施
     */
    @ApiModelProperty(value = "采取措施")
    @Length(max = 4000, message = "采取措施长度不能超过4000")
    private String copdeal;

    /**
     * 企业现状
     */
    @ApiModelProperty(value = "企业现状")
    @Length(max = 4000, message = "企业现状长度不能超过4000")
    private String copresult;

    /**
     * 后续工作
     */
    @ApiModelProperty(value = "后续工作")
    @Length(max = 4000, message = "后续工作长度不能超过4000")
    private String copafterwork;

    /**
     * 请求协助
     */
    @ApiModelProperty(value = "请求协助")
    @Length(max = 4000, message = "请求协助长度不能超过4000")
    private String cophelp;


}
