package com.zhonghe.cop.rep.em;

import java.util.Objects;

/**
 * @Author: lpg
 * @Date: 2025/07/17
 * @Description:
 */
public enum ComplaintType {

    WAGE_ARREARS("1", "拖欠工资", "拖欠 {} 月份工资 {} 元"),
    RESIGNATION_WAGE_UNPAID("2", "辞职到期不发工资", "辞职到期不发工资 {} 元"),
    DISMISSAL_WAGE_UNPAID("3", "开除不结工资", "开除不结工资 {} 元"),
    WAGE_DEDUCTION("4", "克扣工资", "克扣工资 {} 元"),
    CERTIFICATE_DEPOSIT_DEDUCTION("5", "扣证件、押金", "扣证件、押金 {} 元"),
    OVERTIME_WORK("6", "超时工作", "超时工作 {} 时/天"),
    BELOW_MINIMUM_WAGE("7", "工资低于市最低工资标准", "工资低于市最低工资标准"),
    RESIGNATION_NOT_APPROVED("8", "辞职不批", "辞职不批"),
    NO_LABOR_CONTRACT("9", "没有签订劳动合同", "没有签订劳动合同"),
    INSURANCE_ISSUE("10", "保险问题", "保险问题"),
    CONTRACT_TERMINATION_COMPENSATION("11", "解除劳动合同、经济补偿金等问题", "解除劳动合同、经济补偿金等问题"),
    ENTERPRISE_RESTRUCTURING("12", "企业改制职工安置问题", "企业改制职工安置问题"),
    OTHER("13", "其它", "其它： {}");

    private final String type;
    private final String name;
    private final String template;

    ComplaintType(String type, String name, String template) {
        this.type = type;
        this.name = name;
        this.template = template;
    }

    /**
     * 根据code获取枚举
     */
    public static ComplaintType getByCode(String code) {
        for (ComplaintType type : values()) {
            if (Objects.equals(type.type, code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 使用示例
     */
    public static void main(String[] args) {
        // 示例1：拖欠工资
        String content1 = ComplaintType.WAGE_ARREARS.format("3", "5000");
        System.out.println(content1); // 输出：拖欠 3 月份工资 5000 元

        // 示例2：超时工作
        String content2 = ComplaintType.OVERTIME_WORK.format("12");
        System.out.println(content2); // 输出：超时工作 12 时/天

        // 示例3：其它
        String content3 = ComplaintType.OTHER.format("未缴纳住房公积金");
        System.out.println(content3); // 输出：其它 未缴纳住房公积金
    }

    public String getCode() {
        return type;
    }

    public String getName() {
        return name;
    }

    public String getTemplate() {
        return template;
    }

    /**
     * 格式化投诉内容
     *
     * @param args 占位符参数
     * @return 格式化后的内容
     */
    public String format(Object... args) {
        if (args == null || args.length == 0) {
            return template;
        }

        try {
            // 计算模板中占位符的数量
            int placeholderCount = template.length() - template.replace("{}", "").length();
            placeholderCount = placeholderCount / 2; // 每个 {} 占2个字符

            // 如果参数数量不足，用空字符串补齐
            Object[] safeArgs = new Object[placeholderCount];
            for (int i = 0; i < placeholderCount; i++) {
                if (i < args.length && args[i] != null) {
                    safeArgs[i] = args[i];
                } else {
                    safeArgs[i] = ""; // 用空字符串替代缺失的参数
                }
            }

            return String.format(template.replace("{}", "%s"), safeArgs);
        } catch (Exception e) {
            // 如果格式化失败，返回原始模板
            return template;
        }
    }
}
