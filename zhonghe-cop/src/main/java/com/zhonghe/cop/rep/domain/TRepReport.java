package com.zhonghe.cop.rep.domain;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zhonghe.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;


/**
 * 部门上报，根据指标动态生成部门上报大报，然后上报时同时插入，大报用来查询，本作数据根备份对象 t_rep_report
 *
 * <AUTHOR>
 * @date 2023-12-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("cop.t_rep_report")
public class TRepReport extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 部门上报ID
     */
    @TableId
    private Long repReportId;
    /**
     * 是否删除
     */
    @TableLogic
    private String delFlag;
    /**
     * 备注
     */
    private String note;
    /**
     * 上报日期
     */
    private Date repDate;
    /**
     * 上报用户ID
     */
    private Long userId;
    /**
     * 企业ID
     */
    private Long copId;
    /**
     * 部门ID
     */
    private Long deptId;
    /**
     * 指标ID（对应t_rep_target表主键）
     */
    private Long tarId;
    /**
     * 指标值(数值)
     */
    private BigDecimal tarDecimal;
    /**
     * 指标值(描述)
     */
    private String tarNvarchar;
    /**
     * 处理结果(默认0,0-未处理，1-已处理)
     */
    private Integer despacho;
    /**
     * 审核状态（默认0，0——未审核、1——已审核、2退回）
     */
    private Integer auditing;
    /**
     * 处理结果审核（默认0，0——未处理、1——未审核、2——已审核、3退回）
     */
    private Integer resultAuditing;
    /**
     * 审核意见
     */
    private String auditingComment;
    /**
     * 处理结果审核意见
     */
    private String resultAuditingComment;
    /**
     * 是否处理完成(默认0，0-未完成，1-已完成)
     */
    private Integer finishStatus;
    /**
     * 企业是否正常生产经营（0-否，1-是）
     */
    private String copnormal;
    /**
     * 企业是否出现不正常情况（0-否，1-是）
     */
    private String copabnormal;
    /**
     * 企业是否出现重大异常情况（0-否，1-是）
     */
    private String copmajorabnormal;


    /**
     * 风险级别（默认0，0——正常、1——问题、2——风险险、3——危险、4——高危）
     */
    @TableField(value = "\"level\"")
    private Integer level;
    /**
     * 数据批号（32位随机数，用来表示整批记录）
     */
    private String batchNum;

    /**
     * 拖欠工资月数（月）
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal propaymonth;

    /**
     * 拖欠工资月（月）
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String propaymonths;

    /**
     * 拖欠工资涉及人数（人）
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal propaypopulation;

    /**
     * 拖欠工资金额（元）
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal propaymoney;

    /**
     * 拖欠税款月数（月）
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal protaxmonth;

    /**
     * 拖欠税款月（月）
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String protaxmonths;

    /**
     * 拖欠税款金额（元）
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal protaxmoney;

    /**
     * 拖欠社保月数（月）
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal proinsuremonth;

    /**
     * 拖欠社保月（月）
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String proinsuremonths;

    /**
     * 拖欠社保金额（元）
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal proinsuremoney;

    /**
     * 拖欠水费月数（月）
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal prowatermonth;

    /**
     * 拖欠水费月（月）
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String prowatermonths;

    /**
     * 拖欠水费金额（元）
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal prowatermoney;

    /**
     * 拖欠电费月数（月）
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal proenergymonth;


    /**
     * 拖欠电费月（月）
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String proenergymonths;

    /**
     * 拖欠电费金额（元）
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal proenergymoney;

    /**
     * 拖欠租金月数（月）
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal prorentmonth;

    /**
     * 拖欠租金月（月）
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private String prorentmonths;

    /**
     * 拖欠租金金额（元）
     */
    @TableField(updateStrategy = FieldStrategy.IGNORED)
    private BigDecimal prorentmoney;
    /**
     * 其他
     */
    private String other;

    /**
     * 其他问题
     */
    private String problem = "";

    /**
     * 监控措施
     */
    private String copoversee = "";

    /**
     * 建议措施
     */
    private String copadvice = "";

    /**
     * 采取措施
     */
    private String copdeal = "";

    /**
     * 企业现状
     */
    private String copresult = "";

    /**
     * 后续工作
     */
    private String copafterwork = "";

    /**
     * 请求协助
     */
    private String cophelp = "";


}
