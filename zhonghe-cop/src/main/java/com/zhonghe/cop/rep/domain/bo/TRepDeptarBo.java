package com.zhonghe.cop.rep.domain.bo;

import com.zhonghe.common.core.domain.BaseEntity;
import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


/**
 * 指标-部门关联，用于生成上报报业务对象 t_rep_deptar
 *
 * <AUTHOR>
 * @date 2023-12-06
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("指标-部门关联业务对象")
public class TRepDeptarBo extends BaseEntity {

    private static final long serialVersionUID = -4101369565827520902L;
    /**
     * 部门指标ID
     */
    @ApiModelProperty(value = "部门指标ID", required = true)
    @NotNull(message = "部门指标ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long repDeptarId;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注", required = true)
    @NotBlank(message = "备注不能为空", groups = {AddGroup.class, EditGroup.class})
    private String note;

    /**
     * 部门ID
     */
    @ApiModelProperty(value = "部门ID", required = true)
    @NotNull(message = "部门ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long depId;

    /**
     * 指标ID（对应t_rep_target表主键）
     */
    @ApiModelProperty(value = "指标ID（对应t_rep_target表主键）", required = true)
    @NotNull(message = "指标ID（对应t_rep_target表主键）不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long repTargetId;

    /**
     * 排序
     */
    @ApiModelProperty(value = "排序", required = true)
    @NotNull(message = "排序不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long order;


}
