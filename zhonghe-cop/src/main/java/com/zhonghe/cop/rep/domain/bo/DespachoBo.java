package com.zhonghe.cop.rep.domain.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Author: lpg
 * @Date: 2023/12/06
 * @Description: 处理批示业务对象
 */
@Data
public class DespachoBo implements Serializable {
    private static final long serialVersionUID = 6143956265902778147L;

    /**
     * 紧急事件上报ID
     */
    @ApiModelProperty(value = "紧急事件上报ID")
    @NotNull(message = "紧急事件上报ID不能为空")
    private Long exiReportId;
    /**
     * 处理批示
     */
    @ApiModelProperty(value = "处理批示")
    @NotBlank(message = "处理批示不能为空")
    @Length(max = 4000, message = "处理批示长度不能超过4000")
    private String despachoNote;

    /**
     * 涉及部门IDs
     */
    @ApiModelProperty(value = "涉及部门IDs")
    @NotNull(message = "涉及部门不能为空")
    private String[] deptIds;
}
