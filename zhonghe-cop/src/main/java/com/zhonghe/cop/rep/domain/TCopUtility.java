package com.zhonghe.cop.rep.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 企业每月用水用电量统计表
 *
 * @TableName t_cop_utility
 */
@Data
@TableName("cop.t_cop_utility")
public class TCopUtility implements Serializable {

    private static final long serialVersionUID = -3778837397690441163L;
    /**
     * 主键ID
     */
    @NotNull(message = "[主键ID]不能为空")
    @ApiModelProperty("主键ID")
    private Long id;
    /**
     * 企业ID
     */
    @NotNull(message = "[企业ID]不能为空")
    @ApiModelProperty("企业ID")
    private Long copId;
    /**
     * 统计年月
     */
    @NotNull(message = "[统计年月]不能为空")
    @ApiModelProperty("统计年月")
    private Date yearMonth;
    /**
     * 用水量（立方米）
     */
    @ApiModelProperty("用水量（立方米）")
    private BigDecimal waterUsage;
    /**
     * 用电量（千瓦时）
     */
    @ApiModelProperty("用电量（千瓦时）")
    private BigDecimal electricityUsage;
    /**
     * 水费（元）
     */
    @ApiModelProperty("水费（元）")
    private BigDecimal waterCost;
    /**
     * 电费（元）
     */
    @ApiModelProperty("电费（元）")
    private BigDecimal electricityCost;
    /**
     * 数据来源
     */
    @ApiModelProperty("数据来源")
    private String dataSource;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;
    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private Date updateTime;
    /**
     * 创建人
     */
    @Size(max = 64, message = "编码长度不能超过64")
    @ApiModelProperty("创建人")
    @Length(max = 64, message = "编码长度不能超过64")
    private String createBy;
    /**
     * 更新人
     */
    @Size(max = 64, message = "编码长度不能超过64")
    @ApiModelProperty("更新人")
    @Length(max = 64, message = "编码长度不能超过64")
    private String updateBy;
    /**
     * 备注
     */
    @Size(max = -1, message = "编码长度不能超过-1")
    @ApiModelProperty("备注")
    @Length(max = -1, message = "编码长度不能超过-1")
    private String remark;
}
