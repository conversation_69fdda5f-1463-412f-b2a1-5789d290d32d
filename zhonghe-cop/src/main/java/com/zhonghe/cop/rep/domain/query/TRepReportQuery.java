package com.zhonghe.cop.rep.domain.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


/**
 * 汇总报，指标字段根据动态添加业务对象 t_rep_garrep
 *
 * <AUTHOR>
 * @date 2023-12-06
 */

@Data
@ApiModel("报表上报查询对象")
public class TRepReportQuery implements Serializable {

    private static final long serialVersionUID = 1569118841566072056L;

    /**
     * 企业名称
     */
    @ApiModelProperty(value = "企业名称")
    private String copName;

    /**
     * 上报日期
     */
    @ApiModelProperty(value = "上报日期")
    private String repDate;

    /**
     * 审核状态（默认0，0——未审核、1——已审核、2退回）
     */
    @ApiModelProperty(value = "审核状态（默认0，0——未审核、1——已审核、2退回）")
    private Integer auditing;

    /**
     * 处理结果审核（默认-1，-1——未处理、0——未审核、1——已审核、2退回）
     */
    @ApiModelProperty(value = "处理结果审核（默认-1，-1——未处理、0——未审核、1——已审核、2退回）")
    private Integer resultAuditing;

    /**
     * 风险级别（默认0，0——正常、1——问题、2——风险险、3——危险、4——高危）
     */
    @ApiModelProperty(value = "风险级别（默认0，0——正常、1——问题、2——风险险、3——危险、4——高危）")
    private Integer level;


}
