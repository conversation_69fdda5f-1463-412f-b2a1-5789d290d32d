package com.zhonghe.cop.rep.mapper;

import com.zhonghe.common.core.mapper.BaseMapperPlus;
import com.zhonghe.cop.rep.domain.TRepTarget;
import com.zhonghe.cop.rep.domain.vo.TRepTargetVo;

import java.util.List;

/**
 * 上报指标Mapper接口
 *
 * <AUTHOR>
 * @date 2023-12-06
 */
public interface TRepTargetMapper extends BaseMapperPlus<TRepTargetMapper, TRepTarget, TRepTargetVo> {

    /**
     * 查询部门指标信息
     *
     * @param userId
     * @return
     */
    List<TRepTarget> selectListByDeptId(Long userId);
}
