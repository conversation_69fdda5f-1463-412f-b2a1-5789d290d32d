package com.zhonghe.cop.rep.domain.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


/**
 * 紧急事件上报业务对象 t_exi_report
 *
 * <AUTHOR>
 * @date 2023-12-07
 */

@Data
@ApiModel("紧急事件上报查询对象")
public class TExiReportQuery implements Serializable {

    private static final long serialVersionUID = -2852995041618615928L;

    /**
     * 企业名称
     */
    @ApiModelProperty(value = "企业名称")
    private String copName;

    /**
     * 处理结果 0-未处理，1-已处理
     */
    @ApiModelProperty("处理结果 0-未处理，1-已处理")
    private Integer despacho;

    /**
     * 是否涉及部门
     */
    @ApiModelProperty("是否涉及部门")
    private String df;
}
