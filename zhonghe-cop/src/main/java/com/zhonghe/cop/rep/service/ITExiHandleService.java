package com.zhonghe.cop.rep.service;

import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.cop.rep.domain.bo.TExiHandleBo;
import com.zhonghe.cop.rep.domain.vo.TExiHandleVo;

import java.util.Collection;
import java.util.List;

/**
 * 紧急事件处理Service接口
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
public interface ITExiHandleService {

    /**
     * 查询紧急事件处理
     */
    TExiHandleVo queryById(Long exiHandleId);

    /**
     * 查询紧急事件处理列表
     */
    TableDataInfo<TExiHandleVo> queryPageList(TExiHandleBo bo, PageQuery pageQuery);

    /**
     * 查询紧急事件处理列表
     */
    List<TExiHandleVo> queryList(TExiHandleBo bo);

    /**
     * 修改紧急事件处理
     */
    Boolean insertByBo(TExiHandleBo bo);

    /**
     * 修改紧急事件处理
     */
    Boolean updateByBo(TExiHandleBo bo);

    /**
     * 校验并批量删除紧急事件处理信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
