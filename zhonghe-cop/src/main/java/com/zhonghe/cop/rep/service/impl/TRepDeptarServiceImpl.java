package com.zhonghe.cop.rep.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.cop.rep.domain.TRepDeptar;
import com.zhonghe.cop.rep.domain.bo.TRepDeptarBo;
import com.zhonghe.cop.rep.domain.vo.TRepDeptarVo;
import com.zhonghe.cop.rep.mapper.TRepDeptarMapper;
import com.zhonghe.cop.rep.service.ITRepDeptarService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 指标-部门关联，用于生成上报报Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-06
 */
@RequiredArgsConstructor
@Service
public class TRepDeptarServiceImpl implements ITRepDeptarService {

    private final TRepDeptarMapper baseMapper;

    /**
     * 查询指标-部门关联，用于生成上报报
     */
    @Override
    public TRepDeptarVo queryById(Long repDeptarId) {
        return baseMapper.selectVoById(repDeptarId);
    }

    /**
     * 查询指标-部门关联，用于生成上报报列表
     */
    @Override
    public TableDataInfo<TRepDeptarVo> queryPageList(TRepDeptarBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<TRepDeptar> lqw = buildQueryWrapper(bo);
        Page<TRepDeptarVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询指标-部门关联，用于生成上报报列表
     */
    @Override
    public List<TRepDeptarVo> queryList(TRepDeptarBo bo) {
        LambdaQueryWrapper<TRepDeptar> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<TRepDeptar> buildQueryWrapper(TRepDeptarBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<TRepDeptar> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getRepDeptarId() != null, TRepDeptar::getRepDeptarId, bo.getRepDeptarId());
        lqw.eq(StringUtils.isNotBlank(bo.getNote()), TRepDeptar::getNote, bo.getNote());
        lqw.eq(bo.getDepId() != null, TRepDeptar::getDepId, bo.getDepId());
        lqw.eq(bo.getRepTargetId() != null, TRepDeptar::getRepTargetId, bo.getRepTargetId());
        lqw.eq(bo.getOrder() != null, TRepDeptar::getOrder, bo.getOrder());
        return lqw;
    }

    /**
     * 新增指标-部门关联，用于生成上报报
     */
    @Override
    public Boolean insertByBo(TRepDeptarBo bo) {
        TRepDeptar add = BeanUtil.toBean(bo, TRepDeptar.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setRepDeptarId(add.getRepDeptarId());
        }
        return flag;
    }

    /**
     * 修改指标-部门关联，用于生成上报报
     */
    @Override
    public Boolean updateByBo(TRepDeptarBo bo) {
        TRepDeptar update = BeanUtil.toBean(bo, TRepDeptar.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(TRepDeptar entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除指标-部门关联，用于生成上报报
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
