package com.zhonghe.cop.rep.domain.vo.table;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.zhonghe.common.annotation.ExcelDictFormat;
import com.zhonghe.common.convert.ExcelDateConvert;
import com.zhonghe.common.convert.ExcelDictConvert;
import com.zhonghe.framework.config.StripTrailingZerosBigDecimalSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;


/**
 * 部门上报
 *
 * <AUTHOR>
 * @date 2023-12-06
 */
@Data
@ApiModel("部门上报视图对象")
@ExcelIgnoreUnannotated
public class TRepReportTableVo implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 汇总报表ID
     */
    @ApiModelProperty("汇总报表ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long repReportId;


    /**
     * 上报日期
     */

    @ExcelProperty(value = "上报日期", converter = ExcelDateConvert.class)
    @ApiModelProperty("上报日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date repDate;
    /**
     * 企业ID
     */
    @ApiModelProperty("企业ID")
    private Long copId;
    /**
     * 企业名称
     */
    @ExcelProperty(value = "企业名称")
    @ApiModelProperty("企业名称")
    private String copName;

    /**
     * 企业法人
     */
    @ExcelProperty(value = "企业法人")
    @ApiModelProperty("企业法人")
    private String copOwner;
    /**
     * 企业电话
     */
    @ExcelProperty(value = "企业电话")
    @ApiModelProperty("企业电话")
    private String copPhone;

    /**
     * 企业性质
     */
    @ExcelProperty(value = "企业性质", converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "cop_property")
    @ApiModelProperty("企业性质ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long propertyId;


    /**
     * 上报部门
     */
    @ExcelProperty(value = "上报部门")
    @ApiModelProperty("上报部门")
    private String deptName;

    /**
     * 风险等级（默认0，0——正常、1——问题、2——风险险、3——危险、4——高危）
     */
    @ExcelProperty(value = "风险等级", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=正常,1=问题,2=风险险,3=危险,4=高危")
    @ApiModelProperty("风险等级（默认0，0——正常、1——问题、2——风险险、3——危险、4——高危）")
    @JsonSerialize(using = ToStringSerializer.class)
    private Integer level;


    /**
     * 审核状态（默认0，0——未审核、1——已审核、2退回）
     */
    @ExcelProperty(value = "审核状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=未审核,1=已审核,2=退回")
    @ApiModelProperty(value = "审核状态（默认0，0——未审核、1——已审核、2退回）")
    @JsonSerialize(using = ToStringSerializer.class)
    private Integer auditing;

    /**
     * 处理结果（默认0，-0——未处理、1——未审核、2——已审核、3退回）
     */
    @ExcelProperty(value = "处理结果审核", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=未处理,1=未审核,2=已审核,3=退回")
    @ApiModelProperty(value = "处理结果审核（默认0，-0——未处理、1——未审核、2——已审核、3退回）")
    @JsonSerialize(using = ToStringSerializer.class)
    private Integer resultAuditing;

    /**
     * 处理状态（默认0，0-未完成，1-已完成）
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("完成状态（默认0，0-未完成，1-已完成）")
    private Integer finishStatus;

    /**
     * 拖欠工资月数（月）
     */
    @ApiModelProperty(value = "拖欠工资月数")
    @JsonSerialize(using = StripTrailingZerosBigDecimalSerializer.class)
    private BigDecimal propaymonth;

    /**
     * 拖欠工资涉及人数（人）
     */
    @ApiModelProperty(value = "拖欠工资涉及人数")
    private BigDecimal propaypopulation;

    /**
     * 拖欠工资金额（元）
     */
    @ApiModelProperty(value = "拖欠工资金额")
    private BigDecimal propaymoney;

    /**
     * 拖欠税款月数（月）
     */
    @ApiModelProperty(value = "拖欠税款月数")
    @JsonSerialize(using = StripTrailingZerosBigDecimalSerializer.class)
    private BigDecimal protaxmonth;

    /**
     * 拖欠税款金额（元）
     */
    @ApiModelProperty(value = "拖欠税款金额")
    private BigDecimal protaxmoney;

    /**
     * 拖欠社保月数（月）
     */
    @ApiModelProperty(value = "拖欠社保月数")
    @JsonSerialize(using = StripTrailingZerosBigDecimalSerializer.class)
    private BigDecimal proinsuremonth;

    /**
     * 拖欠社保金额（元）
     */
    @ApiModelProperty(value = "拖欠社保金额")
    private BigDecimal proinsuremoney;

    /**
     * 拖欠水费月数（月）
     */
    @ApiModelProperty(value = "拖欠水费月数")
    @JsonSerialize(using = StripTrailingZerosBigDecimalSerializer.class)
    private BigDecimal prowatermonth;

    /**
     * 拖欠水费金额（元）
     */
    @ApiModelProperty(value = "拖欠水费金额")
    private BigDecimal prowatermoney;

    /**
     * 拖欠电费月数（月）
     */
    @ApiModelProperty(value = "拖欠电费月数")
    @JsonSerialize(using = StripTrailingZerosBigDecimalSerializer.class)
    private BigDecimal proenergymonth;

    /**
     * 拖欠电费金额（元）
     */
    @ApiModelProperty(value = "拖欠电费金额")
    private BigDecimal proenergymoney;

    /**
     * 拖欠租金月数（月）
     */
    @ApiModelProperty(value = "拖欠租金月数")
    @JsonSerialize(using = StripTrailingZerosBigDecimalSerializer.class)
    private BigDecimal prorentmonth;

    /**
     * 拖欠租金金额（元）
     */
    @ApiModelProperty(value = "拖欠租金金额")
    private BigDecimal prorentmoney;

    /**
     * 其他问题
     */
    @ApiModelProperty("其他问题")
    private String problem;

    /**
     * 监控措施
     */
    @ApiModelProperty("监控措施")
    private String copoversee;

    /**
     * 建议措施
     */
    @ApiModelProperty("建议措施")
    private String copadvice;

    /**
     * 采取措施
     */
    @ApiModelProperty("采取措施")
    private String copdeal;

    /**
     * 企业现状
     */
    @ApiModelProperty("企业现状")
    private String copresult;

    /**
     * 后续工作
     */
    @ApiModelProperty("后续工作")
    private String copafterwork;

    /**
     * 请求协助
     */
    @ApiModelProperty("请求协助")
    private String cophelp;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String note;
    /**
     * 其他
     */
    private String other;


}
