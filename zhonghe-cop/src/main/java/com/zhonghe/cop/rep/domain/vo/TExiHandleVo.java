package com.zhonghe.cop.rep.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zhonghe.common.annotation.ExcelDictFormat;
import com.zhonghe.common.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 紧急事件处理视图对象 t_exi_handle
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
@Data
@ApiModel("紧急事件处理视图对象")
@ExcelIgnoreUnannotated
public class TExiHandleVo {

    private static final long serialVersionUID = 1L;

    /**
     * 紧急事件处理ID
     */
    @ExcelProperty(value = "紧急事件处理ID")
    @ApiModelProperty("紧急事件处理ID")
    private Long exiHandleId;

    /**
     * 说明
     */
    @ExcelProperty(value = "说明")
    @ApiModelProperty("说明")
    private String note;

    /**
     * 上报ID（对应t_exi_report表主键）
     */
    @ExcelProperty(value = "上报ID", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "对=应t_exi_report表主键")
    @ApiModelProperty("上报ID（对应t_exi_report表主键）")
    private Long exiReportId;

    /**
     * 描述
     */
    @ExcelProperty(value = "描述")
    @ApiModelProperty("描述")
    private String description;

    /**
     * 处理结果
     */
    @ExcelProperty(value = "处理结果")
    @ApiModelProperty("处理结果")
    private String despacho;

    /**
     * 涉及部门ID串（逗号分割，例：1,2,3 ）
     */
    @ExcelProperty(value = "涉及部门ID串", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "逗=号分割，例：1,2,3")
    @ApiModelProperty("涉及部门ID串（逗号分割，例：1,2,3 ）")
    private String depts;


}
