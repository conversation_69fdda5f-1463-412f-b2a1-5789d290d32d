package com.zhonghe.cop.rep.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.zhonghe.common.annotation.Anonymous;
import com.zhonghe.common.annotation.Log;
import com.zhonghe.common.annotation.RepeatSubmit;
import com.zhonghe.common.core.controller.BaseController;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.core.validate.EditGroup;
import com.zhonghe.common.enums.BusinessType;
import com.zhonghe.common.utils.poi.ExcelUtil;
import com.zhonghe.cop.bam.em.CopTarget;
import com.zhonghe.cop.rep.domain.bo.TRepGarrepBo;
import com.zhonghe.cop.rep.domain.dto.OwsDto;
import com.zhonghe.cop.rep.domain.query.TRepGarrepQuery;
import com.zhonghe.cop.rep.domain.vo.CopReportResultVo;
import com.zhonghe.cop.rep.domain.vo.TRepGarrepInfoVo;
import com.zhonghe.cop.rep.domain.vo.table.TRepGarrepTableVo;
import com.zhonghe.cop.rep.handler.CustomerWriteHandler;
import com.zhonghe.cop.rep.service.ITRepGarrepService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 汇总上报
 *
 * <AUTHOR>
 * @date 2023-12-06
 */
@Validated
@Api(value = "汇总上报控制器", tags = {"汇总上报管理"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/cop/rep/garrep")
public class TRepGarrepController extends BaseController {

    private final ITRepGarrepService iTRepGarrepService;

    /**
     * 查询汇总报表列表
     */
    @ApiOperation("查询汇总报表列表(cop:rep:garrep:list)")
    @SaCheckPermission("cop:rep:garrep:list")
    @GetMapping("/list")
    public TableDataInfo<TRepGarrepTableVo> list(TRepGarrepQuery bo, PageQuery pageQuery) {
        return iTRepGarrepService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出汇总报表列表
     */
    @ApiOperation("导出汇总报表列表(cop:rep:garrep:export)")
    @SaCheckPermission("cop:rep:garrep:export")
    @Log(title = "汇总报表列表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(TRepGarrepQuery bo, HttpServletResponse response) {
        List<TRepGarrepTableVo> list = iTRepGarrepService.queryList(bo);
        //风险级别倒序
        list.sort(Comparator.comparing(TRepGarrepTableVo::getLevel).reversed());
        //设置报表日期减一个月
        list.forEach(a -> {
            DateTime dateTime = DateUtil.offsetMonth(a.getRepDate(), -1);
            String repDate = iTRepGarrepService.queryAllRepDate(a.getCopId(), dateTime.toString("yyyy-MM"));
            a.setRepDateStr(repDate);
            String repProblem = iTRepGarrepService.queryAllRepProblem(a.getCopId(), dateTime.toString("yyyy-MM"));
            a.setNote(repProblem);
        });

        Map<String, String> map = new HashMap<>();
        if (StringUtils.isNotBlank(bo.getRepDate())) {
            map.put("repMonth", DateUtil.format(DateUtil.parse(bo.getRepDate(), "yyyy-MM"), "MM"));
            map.put("repDate", bo.getRepDate());
        }
        if (bo.getRepDateRange() != null && bo.getRepDateRange().length > 0) {
            map.put("repMonth", DateUtil.format(DateUtil.parse(bo.getRepDateRange()[0], "yyyy-MM"), "MM"));
            if (bo.getRepDateRange()[0].equals(bo.getRepDateRange()[1])) {
                map.put("repDate", bo.getRepDateRange()[0]);
            } else {
                map.put("repDate", bo.getRepDateRange()[0] + "至" + bo.getRepDateRange()[1]);
            }
        }
        map.put("exportDate", DateUtil.format(new Date(), "yyyy-MM-dd"));
        //添加序号
        for (int i = 0; i < list.size(); i++) {
            list.get(i).setIndex(i + 1);
        }
        //根据风险级别统计数据
        list.stream().collect(Collectors.groupingBy(TRepGarrepTableVo::getLevel, Collectors.counting())).forEach((k, v) -> {
            map.put("level" + k, v.toString());
        });
        map.put("total", String.valueOf(list.size()));
        for (TRepGarrepTableVo tRepGarrepTableVo : list) {
            tRepGarrepTableVo.setNote(String.join("\n", CopTarget.formatFromObject(tRepGarrepTableVo)));
        }
        ExcelUtil.exportExcel(list, "防范企业欠薪逃匿风险预警月报表（" + map.get("repDate") + "汇总表）", TRepGarrepTableVo.class,
            new CustomerWriteHandler(map, list.size()), response);
    }

    /**
     * 获取汇总报表详细信息
     */
    @ApiOperation("获取汇总报表详细信息(cop:rep:garrep:query)")
    @SaCheckPermission("cop:rep:garrep:query")
    @GetMapping("/{repGarrepId}")
    public R<TRepGarrepInfoVo> getInfo(@ApiParam("主键")
                                       @NotNull(message = "主键不能为空")
                                       @PathVariable("repGarrepId") Long repGarrepId) {
        return R.ok(iTRepGarrepService.queryById(repGarrepId));
    }

    /**
     * 修改报表上报
     */
    @ApiOperation("修改报表上报(cop:rep:garrep:edit)")
    @SaCheckPermission("cop:rep:garrep:edit")
    @Log(title = "修改报表上报", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody TRepGarrepBo bo) {
        return toAjax(iTRepGarrepService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 手动汇总（作废）
     */
    @Deprecated
    @ApiOperation("手动汇总(cop:rep:garrep:manualGather)")
    @SaCheckPermission("cop:rep:garrep:manualGather")
    @Log(title = "手动汇总", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/manualGather")
    public R<Void> manual(@NotBlank(message = "汇总日期不能为空") String gatherDate) {
        return toAjax(iTRepGarrepService.manualGather(gatherDate) ? 1 : 0);
    }

    /**
     * 查询部门上报统计展示
     */
    @ApiOperation("查询部门上报统计展示(cop:rep:garrep:list)")
    @Anonymous
    @GetMapping("/copLevelReport")
    public R<CopReportResultVo> copLevelReport(@RequestParam @ApiParam("年月") String
                                                   yearMonth, @RequestParam(required = false) @ApiParam("企业名称") String copName) {
        return R.ok(iTRepGarrepService.copLevelReport(yearMonth, copName));
    }

    @ApiOperation("查询GIS地图点位")
    @GetMapping("/ows")
    @Anonymous
    public OwsDto ows(@RequestParam @ApiParam("年月") String
                          yearMonth, @NotNull(message = "风险级别不能为空") Integer level) {
        return iTRepGarrepService.ows(yearMonth, level);
    }

}
