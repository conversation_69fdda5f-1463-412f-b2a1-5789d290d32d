package com.zhonghe.cop.rep.domain.vo.table;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 紧急事件上报视图对象 t_exi_report
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
@Data
@ApiModel("紧急事件上报视图对象")
@ExcelIgnoreUnannotated
public class TExiReportTableVo {

    private static final long serialVersionUID = 1L;

    /**
     * 紧急事件上报ID
     */
    @ExcelProperty(value = "紧急事件上报ID")
    @ApiModelProperty("紧急事件上报ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long exiReportId;


    /**
     * 部门名称
     */
    @ExcelProperty(value = "部门名称")
    @ApiModelProperty("部门名称")
    private String deptName;

    /**
     * 企业名称
     */
    @ExcelProperty(value = "企业名称")
    @ApiModelProperty("企业名称")
    private String copName;

    /**
     * 企业性质
     */
    @ExcelProperty(value = "企业性质")
    @ApiModelProperty("企业性质")
    private String copProperty;

    /**
     * 事件描述
     */
    @ExcelProperty(value = "事件描述")
    @ApiModelProperty("事件描述")
    private String description;

    /**
     * 处理结果
     */
    @ExcelProperty(value = "处理结果")
    @ApiModelProperty("处理结果")
    private String despacho;


}
