package com.zhonghe.cop.rep.em;

import lombok.Getter;

/**
 * @Author: lpg
 * @Date: 2023/12/13/09:34
 * @Description:
 */
@Getter
public enum TargetType {
    /**
     * 0-普通指标
     */
    NORMAL(0, "普通指标"),
    /**
     * 1-处理结果
     */
    RESULT(1, "处理结果");


    private Integer code;
    private String name;

    TargetType(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

}
