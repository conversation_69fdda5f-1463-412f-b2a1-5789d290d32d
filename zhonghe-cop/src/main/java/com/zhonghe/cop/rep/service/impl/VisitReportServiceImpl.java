package com.zhonghe.cop.rep.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.config.ConfigureBuilder;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.exception.ServiceException;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.common.utils.poi.ExcelUtil;
import com.zhonghe.cop.rep.domain.VisitReport;
import com.zhonghe.cop.rep.domain.bo.RegisterHandleResultBo;
import com.zhonghe.cop.rep.domain.bo.VisitReportBo;
import com.zhonghe.cop.rep.domain.query.VisitReportQuery;
import com.zhonghe.cop.rep.domain.vo.VisitReportExportVo;
import com.zhonghe.cop.rep.domain.vo.VisitReportVo;
import com.zhonghe.cop.rep.em.ComplaintType;
import com.zhonghe.cop.rep.handler.CustomerWriteHandler;
import com.zhonghe.cop.rep.mapper.VisitReportMapper;
import com.zhonghe.cop.rep.ocr.BaiDuOcrServiceImpl;
import com.zhonghe.cop.rep.ocr.IBaiDuOcrService;
import com.zhonghe.cop.rep.ocr.IOcrService;
import com.zhonghe.cop.rep.ocr.OcrServiceFactory;
import com.zhonghe.cop.rep.service.IVisitReportService;
import com.zhonghe.system.domain.SysOss;
import com.zhonghe.system.service.ISysOssService;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 信访上报Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class VisitReportServiceImpl implements IVisitReportService {

    private final VisitReportMapper baseMapper;
    private final ISysOssService sysOssService;
    private final IBaiDuOcrService iBaiDuOcrService;
    private final OcrServiceFactory ocrServiceFactory;

    /**
     * 从诉求选项文本中提取“被选中的内容”
     * 规则：如果前缀没有 “口” 或 “□”，视为选中
     */
    public static List<String> extractSelectedItems(String rawText) {
        List<String> selectedItems = new ArrayList<>();

        // 替换换行为分号后统一拆分（句号/分号/换行都可能分隔）
        rawText = rawText.replaceAll("[\\r\\n]+", "；");

        // 拆分每一项
        String[] items = rawText.split("[；;]");

        for (String item : items) {
            item = item.trim();
            if (item.isEmpty()) {
                continue;
            }

            // 判断是否为未选中（前缀包含口或□）
            if (item.matches("^[口□].*")) {
                continue;
            }

            // 去掉序号 “1.”、“13。”、“13。” 等（保留内容）
            item = item.replaceFirst("^\\d+[\\.。]", "").trim();

            if (!item.isEmpty()) {
                selectedItems.add(item);
            }
        }

        return selectedItems;
    }

    public static String flattenTextToSingleLine(String multilineText) {
        if (multilineText == null) {
            return "";
        }

        // 替换换行符为空格，再规范中文分号
        return multilineText
            .replaceAll("[\\r\\n]+", "") // 去除所有换行符
            .replaceAll("；\\s*", "；")    // 清理分号后面的空格
            .replaceAll("元；", "元；")   // 保留清晰分隔
            .replaceAll("时/天；", "时/天；")
            .trim();
    }

    /**
     * 查询信访上报
     */
    @Override
    public VisitReportVo queryById(Long visitReportId) {
        VisitReportVo visitReportVo = baseMapper.selectVoById(visitReportId);
        if (StringUtils.isNotBlank(visitReportVo.getAppealContentJson())) {
            visitReportVo.setAppealContents(JSON.parseArray(visitReportVo.getAppealContentJson(), VisitReportBo.AppealContent.class));
        }
        return visitReportVo;
    }

    /**
     * 分页查询信访上报列表
     */
    @Override
    public TableDataInfo<VisitReportVo> queryPageList(VisitReportQuery query, PageQuery pageQuery) {
        LambdaQueryWrapper<VisitReport> lqw = buildQueryWrapper(query);
        Page<VisitReportVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询信访上报列表
     */
    @Override
    public List<VisitReportVo> queryList(VisitReportQuery query) {
        LambdaQueryWrapper<VisitReport> lqw = buildQueryWrapper(query);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<VisitReport> buildQueryWrapper(VisitReportQuery query) {
        LambdaQueryWrapper<VisitReport> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(query.getRspUnit()), VisitReport::getRspUnit, query.getRspUnit());
        lqw.ge(ObjectUtil.isNotNull(query.getRegisterStartDate()), VisitReport::getRegisterDate, query.getRegisterStartDate());
        lqw.le(ObjectUtil.isNotNull(query.getRegisterEndDate()), VisitReport::getRegisterDate, query.getRegisterEndDate());
        lqw.eq(ObjectUtil.isNotNull(query.getVerifyStatus()), VisitReport::getVerifyStatus, query.getVerifyStatus());
        lqw.like(StringUtils.isNotBlank(query.getCptName()), VisitReport::getCptName, query.getCptName());
        lqw.like(StringUtils.isNotBlank(query.getCptPhone()), VisitReport::getCptPhone, query.getCptPhone());
        //录入日期yyyy-MM
        lqw.apply(ObjectUtil.isNotNull(query.getInputDate()), "to_char(input_date,'yyyy-MM') = {0}", DateUtil.format(query.getInputDate(), "yyyy-MM"));
        lqw.orderByDesc(VisitReport::getCreateTime);
        return lqw;
    }

    /**
     * 新增信访上报
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(VisitReportBo bo) {
        VisitReport add = BeanUtil.toBean(bo, VisitReport.class);
        validEntityBeforeSave(add);

        if (add.getVerifyStatus() == null) {
            add.setVerifyStatus(0);
        }
        List<VisitReportBo.AppealContent> appealContents = bo.getAppealContents();
        add.setAppealContentJson(JSON.toJSONString(appealContents));
        add.setInputDate(new Date());

        List<String> contentList = new ArrayList<>();
        for (VisitReportBo.AppealContent appealContent : appealContents) {
            if (appealContent.getIsSelect()) {
                contentList.add(Objects.requireNonNull(ComplaintType.getByCode(appealContent.getType())).format(appealContent.getValues().toArray()));
            }
        }
        add.setAppealContent(StringUtils.join(contentList, "\n"));

        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setVisitReportId(add.getVisitReportId());
        }
        return flag;
    }

    /**
     * 修改信访上报
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(VisitReportBo bo) {
        VisitReport update = BeanUtil.toBean(bo, VisitReport.class);
        validEntityBeforeSave(update);

        if (update.getVerifyStatus() == null) {
            update.setVerifyStatus(0);
        }
        List<VisitReportBo.AppealContent> appealContents = bo.getAppealContents();
        update.setAppealContentJson(JSON.toJSONString(appealContents));
        update.setInputDate(new Date());

        List<String> contentList = new ArrayList<>();
        for (VisitReportBo.AppealContent appealContent : appealContents) {
            if (appealContent.getIsSelect()) {
                contentList.add(Objects.requireNonNull(ComplaintType.getByCode(appealContent.getType())).format(appealContent.getValues().toArray()));
            }
        }
        update.setAppealContent(StringUtils.join(contentList, "\n"));
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(VisitReport entity) {
        // TODO 做一些数据校验,如唯一约束
        if (StringUtils.isBlank(entity.getCptName())) {
            throw new ServiceException("投诉人姓名不能为空");
        }
        if (StringUtils.isBlank(entity.getRspUnit())) {
            throw new ServiceException("被投诉人单位不能为空");
        }
    }

    /**
     * 校验并批量删除信访上报
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * PDF上传并OCR识别
     */
    @SneakyThrows
    @Override
    public synchronized VisitReportVo uploadPdfAndOcr(MultipartFile file) {
        log.info("PDF上传并OCR识别.....");
        InputStream is = file.getInputStream();
//        List<byte[]> bytes = PDFToImageBytes.convertPDFToImageBytes(is);

        Map<String, String> keyMap = new LinkedHashMap<>();
        keyMap.put("姓名", "cptName");
        keyMap.put("性别", "cptGender");
        keyMap.put("联系电话", "cptPhone");
        keyMap.put("身份证号码", "cptIdCard");
        keyMap.put("居住地址", "cptAddress");
        keyMap.put("企业名称", "rspUnit");
        keyMap.put("企业人数", "rspUnitPopulation");
        keyMap.put("企业地址", "rspUnitAddress");
        keyMap.put("统一社会信用代码", "rspUnitCreditCode");
        keyMap.put("企业负责人", "rspUnitIncharge");
        keyMap.put("企业联系电话", "rspUnitPhone");
        keyMap.put("在诉求内容上打钩“√”", "appealContentSelect");
        keyMap.put("在诉求内容上打钩\"√\"", "appealContentSelect");
        keyMap.put("具体内容和投诉请求", "cptContent");
        keyMap.put("证据材料", "evidenceOther");

        keyMap.put("承办人意见", "handlerOpinion");
        keyMap.put("法律文书指定送达地址", "legalAddress");
        keyMap.put("签名", "legalSignature");
        keyMap.put("来访人签名", "visitorSignature");
        keyMap.put("来访日期", "visitorDate");
        keyMap.put("劳动保障监察员签名", "laborSignature");
        keyMap.put("劳动保障监察日期", "laborDate");

        IOcrService ocrService = ocrServiceFactory.getOcrService();
        log.info("使用OCR服务: {}", ocrService.getServiceType());

        Map<String, String> ocrResultMap = ocrService.extractFieldsFromDocument(is, keyMap.keySet());

        Map<String, String> entityFieldMap = new LinkedHashMap<>();
        for (Map.Entry<String, String> entry : ocrResultMap.entrySet()) {
            String ocrKey = entry.getKey();
            String ocrValue = entry.getValue();
            String fieldName = keyMap.get(ocrKey);
            if (fieldName != null) {
                entityFieldMap.put(fieldName, ocrValue);
            }
        }
        String string = entityFieldMap.get("evidenceOther").replaceAll("\\n", "");
        String copyIdCardNum = BaiDuOcrServiceImpl.extractBetween(string, "身份证复印件", "份");
        entityFieldMap.put("evidenceIdCardCopyNum", copyIdCardNum);
        String evidenceOther = BaiDuOcrServiceImpl.extractBetween(string, "其它：", "");
        entityFieldMap.put("evidenceOther", evidenceOther);


        VisitReportVo visitReportVo = new VisitReportVo();
        BeanUtil.fillBeanWithMap(entityFieldMap, visitReportVo, true);
        visitReportVo.setVerifyStatus(0);

        // 解析options并组装AppealContent列表
        List<VisitReportBo.AppealContent> appealContents = parseOptionsToAppealContents(entityFieldMap.get("appealContentSelect"));
        visitReportVo.setAppealContents(appealContents);

        log.info("绑定结果:{}", visitReportVo);


        SysOss sysOss = sysOssService.upload(file);
        visitReportVo.setSourceFileOssId(sysOss.getOssId());
        return visitReportVo;
    }

    /**
     * 源文件预览
     */
    @Override
    public void previewSourceFile(Long visitReportId, HttpServletRequest request, HttpServletResponse response) {
        VisitReportVo visitReport = baseMapper.selectVoById(visitReportId);
        if (visitReport == null) {
            throw new ServiceException("信访上报记录不存在");
        }

        if (ObjectUtil.isNull(visitReport.getSourceFileOssId())) {
            throw new ServiceException("源文件不存在");
        }

        // 使用OSS服务预览文件
        sysOssService.downloadFromOss(visitReport.getSourceFileOssId(), request, response);
    }

    /**
     * 处理结果登记
     */
    @Override
    public Boolean registerHandleResult(RegisterHandleResultBo bo) {
        LambdaUpdateWrapper<VisitReport> lqw = Wrappers.lambdaUpdate();
        lqw.eq(VisitReport::getVisitReportId, bo.getVisitReportId());
        lqw.set(VisitReport::getHandleResult, bo.getHandleResult());
        lqw.set(VisitReport::getRegisterDate, new Date());
        return baseMapper.update(null, lqw) > 0;
    }

    /**
     * 导出Word文档
     */
    @Override
    public void exportWord(Long visitReportId, HttpServletResponse response) {
        log.info("开始导出Word文档，visitReportId: {}", visitReportId);

        InputStream inputStream = null;
        XWPFTemplate template = null;

        try {
            VisitReport visitReport = baseMapper.selectById(visitReportId);
            if (visitReport == null) {
                throw new RuntimeException("未找到对应的来访记录");
            }

            Map<String, Object> dataMap = new LinkedHashMap<>();
            dataMap.put("1", visitReport.getCptName());
            dataMap.put("2", visitReport.getCptGender());
            dataMap.put("3", visitReport.getCptIdCard());
            dataMap.put("4", visitReport.getCptPhone());
            dataMap.put("5", visitReport.getCptAddress());
            dataMap.put("6", visitReport.getRspUnit());
            dataMap.put("7", visitReport.getRspUnitPopulation());
            dataMap.put("8", visitReport.getRspUnitAddress());
            dataMap.put("9", visitReport.getRspUnitCreditCode());
            dataMap.put("10", visitReport.getRspUnitIncharge());
            dataMap.put("11", visitReport.getRspUnitPhone());
            dataMap.put("12", visitReport.getCptContent());
            dataMap.put("13", visitReport.getEvidenceIdCardCopyNum());
            dataMap.put("14", visitReport.getEvidenceOther());
            dataMap.put("15", visitReport.getHandlerOpinion());
            dataMap.put("16", visitReport.getLegalAddress());
            dataMap.put("17", visitReport.getLegalSignature());
            dataMap.put("18", visitReport.getVisitorSignature());
            //来访日期拆分成年月日
            Date visitorDate = visitReport.getVisitorDate();
            if (visitorDate != null) {
                dataMap.put("19", DateUtil.format(visitorDate, "yyyy"));
                dataMap.put("20", DateUtil.format(visitorDate, "MM"));
                dataMap.put("21", DateUtil.format(visitorDate, "dd"));
            }
            dataMap.put("22", visitReport.getLaborSignature());

            //劳动保障监察日期拆分成年月日
            Date laborDate = visitReport.getLaborDate();
            if (laborDate != null) {
                dataMap.put("23", DateUtil.format(laborDate, "yyyy"));
                dataMap.put("24", DateUtil.format(laborDate, "MM"));
                dataMap.put("25", DateUtil.format(laborDate, "dd"));
            }

            String appealContentJson = visitReport.getAppealContentJson();
            if (StringUtils.isNotBlank(appealContentJson)) {
                List<VisitReportBo.AppealContent> appealContents = JSON.parseArray(appealContentJson, VisitReportBo.AppealContent.class);
                for (VisitReportBo.AppealContent appealContent : appealContents) {
                    if (appealContent.getIsSelect()) {
                        dataMap.put("a" + appealContent.getType(), "☑" + appealContent.getType());
                        // 查看是否有值，用a1_v1、a1_v2...
                        for (int i = 0; i < appealContent.getValues().size(); i++) {
                            dataMap.put("a" + appealContent.getType() + "_v" + (i + 1), appealContent.getValues().get(i));
                        }
                    } else {
                        dataMap.put("a" + appealContent.getType(), "□" + appealContent.getType());
                    }
                }
            }
//            processAppealContent(dataMap, visitReport.getAppealContentSelect());

            ConfigureBuilder configureBuilder = Configure.builder();
            //清除标记
            configureBuilder.setValidErrorHandler(new Configure.ClearHandler());

            inputStream = getClass().getClassLoader().getResourceAsStream("word/石龙人社分局来访人员登记表.docx");
            if (inputStream == null) {
                throw new RuntimeException("模板文件不存在");
            }

            // 5. 渲染模板
            template = XWPFTemplate.compile(inputStream, configureBuilder.build()).render(dataMap);

            // 6. 设置响应头
            String fileName = "来访登记表_" + visitReport.getCptName() + "_" + System.currentTimeMillis() + ".docx";
            response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8"));
            response.setHeader("Access-Control-Expose-Headers", "Content-Disposition");

            // 7. 输出到响应流
            template.writeAndClose(response.getOutputStream());

            log.info("Word文档导出成功");

        } catch (Exception e) {
            log.error("导出Word文档失败", e);
            throw new RuntimeException("导出失败：" + e);
        } finally {
            // 关闭资源
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.error("关闭输入流失败", e);
                }
            }
        }
    }

    /**
     * 处理诉求内容复选框
     */
    private void processAppealContent(Map<String, Object> dataMap, String appealContentSelect) {
        // 初始化所有复选框为未选中
        for (ComplaintType type : ComplaintType.values()) {
            dataMap.put("appeal_" + type.getCode(), "□");
        }

        // 根据选中的内容设置复选框
        if (StringUtils.isNotBlank(appealContentSelect)) {
            String[] selectedTypes = appealContentSelect.split(",");
            for (String typeCode : selectedTypes) {
                dataMap.put("appeal_" + typeCode.trim(), "☑");
            }
        }
    }

    /**
     * 导出Excel
     */
    @Override
    public void exportExcel(List<VisitReportVo> list, HttpServletResponse response) {
        List<VisitReportExportVo> visitReportExportVos = BeanUtil.copyToList(list, VisitReportExportVo.class);
        for (int i = 0; i < visitReportExportVos.size(); i++) {
            visitReportExportVos.get(i).setCaseNumber(String.valueOf(i + 1));
        }
        Map<String, String> map = new HashMap<>();
        map.put("visitDate", DateUtil.format(new Date(), "yyyy年MM月"));
        ExcelUtil.exportExcel(visitReportExportVos, DateUtil.format(new Date(), "yyyy年MM月") + "劳动争议信访来访人员登记表", VisitReportExportVo.class,
            new CustomerWriteHandler(map, list.size()), response);
    }

    /**
     * 解析options字符串并组装AppealContent列表
     *
     * @param options 选项字符串，格式如："1.拖欠2月份工资1000元；口2。辞职到期不发工资元；..."
     * @return AppealContent列表，包含所有ComplaintType枚举选项和选中状态
     */
    public List<VisitReportBo.AppealContent> parseOptionsToAppealContents(String options) {
        List<VisitReportBo.AppealContent> appealContents = new ArrayList<>();

        // 获取所有ComplaintType枚举值
        ComplaintType[] allTypes = ComplaintType.values();

        // 解析options字符串，提取选中的项目和对应的值
        Map<String, List<String>> selectedOptionsMap = parseOptionsString(options);

        // 为每个ComplaintType创建AppealContent对象
        for (ComplaintType type : allTypes) {
            VisitReportBo.AppealContent appealContent = new VisitReportBo.AppealContent();
            appealContent.setType(type.getCode());

            // 检查该类型是否被选中，并设置values
            String typeCode = type.getCode();
            if (selectedOptionsMap.containsKey(typeCode)) {
                appealContent.setIsSelect(true);
                appealContent.setValues(selectedOptionsMap.get(typeCode));
            } else {
                appealContent.setIsSelect(false);
                appealContent.setValues(new ArrayList<>());
            }

            appealContents.add(appealContent);
        }

        return appealContents;
    }

    /**
     * 解析options字符串，提取选中的选项和对应的参数值
     *
     * @param options 选项字符串
     * @return Map<选项编号, 参数值列表>
     */
    private Map<String, List<String>> parseOptionsString(String options) {
        Map<String, List<String>> resultMap = new HashMap<>();

        if (StringUtils.isBlank(options)) {
            return resultMap;
        }

        // 按行分割或按分号分割
        String[] lines = flattenTextToSingleLine(options).split("[\\n;；]");

        for (String line : lines) {
            if (StringUtils.isBlank(line)) {
                continue;
            }

            // 检查是否有选中标记（没有口或□前缀表示选中）
            boolean isSelected = !line.trim().startsWith("口") && !line.trim().startsWith("□");

            // 清理行内容，去除前缀符号（口、□等）
            String cleanLine = line.trim().replaceAll("^[口□■▪▫]", "").trim();

            // 匹配模式：数字.内容 或 数字。内容（支持中文句号）
            Pattern pattern = Pattern.compile("^(\\d+)[.。](.*?)$");
            Matcher matcher = pattern.matcher(cleanLine);

            if (matcher.find() && isSelected) {
                String typeCode = matcher.group(1);
                String content = matcher.group(2).trim();

                // 根据ComplaintType的模板提取参数值
                List<String> values = extractValuesFromContent(typeCode, content);

                if (values.isEmpty()) {
                    values.add("");
                }
                resultMap.put(typeCode, values);
            }
        }

        return resultMap;
    }

    /**
     * 根据投诉类型和内容提取参数值
     *
     * @param typeCode 投诉类型编码
     * @param content  内容字符串
     * @return 提取的参数值列表
     */
    private List<String> extractValuesFromContent(String typeCode, String content) {
        List<String> values = new ArrayList<>();

        ComplaintType type = ComplaintType.getByCode(typeCode);
        if (type == null) {
            return values;
        }

        String template = type.getTemplate();

        // 根据不同的投诉类型提取参数
        switch (type) {
            case WAGE_ARREARS:
                // 模板：拖欠 {} 月份工资 {} 元
                // 内容示例：拖欠2月份工资1000元
                Pattern wagePattern = Pattern.compile("拖欠(\\d+)月份工资(\\d+)元");
                Matcher wageMatcher = wagePattern.matcher(content);
                if (wageMatcher.find()) {
                    values.add(wageMatcher.group(1)); // 月份
                    values.add(wageMatcher.group(2)); // 金额
                }
                break;

            case RESIGNATION_WAGE_UNPAID:
            case DISMISSAL_WAGE_UNPAID:
            case WAGE_DEDUCTION:
            case CERTIFICATE_DEPOSIT_DEDUCTION:
                // 模板：xxx {} 元
                Pattern amountPattern = Pattern.compile("(\\d+)元");
                Matcher amountMatcher = amountPattern.matcher(content);
                if (amountMatcher.find()) {
                    values.add(amountMatcher.group(1)); // 金额
                }
                break;

            case OVERTIME_WORK:
                // 模板：超时工作 {} 时/天
                Pattern overtimePattern = Pattern.compile("超时工作(\\d+)时/天");
                Matcher overtimeMatcher = overtimePattern.matcher(content);
                if (overtimeMatcher.find()) {
                    values.add(overtimeMatcher.group(1)); // 小时数
                }
                break;

            case OTHER:
                // 模板：其它： {}
                // 提取冒号后的内容
                if (content.contains("：") || content.contains(":")) {
                    String[] parts = content.split("[：:]", 2);
                    if (parts.length > 1 && StringUtils.isNotBlank(parts[1])) {
                        values.add(parts[1].trim());
                    }
                }
                break;

            default:
                // 对于没有参数的类型，如果内容不为空，则表示被选中
                if (StringUtils.isNotBlank(content)) {
                    values.add("selected");
                }
                break;
        }

        return values;
    }

    /**
     * 测试方法：用于验证parseOptionsToAppealContents方法的功能
     * 可以在开发环境中调用此方法进行测试
     */
    public void testParseOptionsToAppealContents() {
        String testOptions = "1.拖欠2月份工资1000元；口2。辞职到期不发工资元；\n口3。开除不结工资\n元；□4。克扣工资\n元；□5。扣证件、押金\n元；\n□6。超时工作\n时/天；□7。工资低于市最低工资标准；□8。辞职不批；\n□9。没有签订劳动合同；□10。保险问题；□11。解除劳动合同、经济补偿金等问题；\n□12。企业改制职工安置问题；13。其它：";

        List<VisitReportBo.AppealContent> result = parseOptionsToAppealContents(testOptions);

        log.info("解析结果总数: {}", result.size());
        for (VisitReportBo.AppealContent content : result) {
            log.info("类型: {}, 名称: {}, 选中: {}, 值: {}",
                content.getType(), content.getContent(), content.getIsSelect(), content.getValues());
        }
    }

}
