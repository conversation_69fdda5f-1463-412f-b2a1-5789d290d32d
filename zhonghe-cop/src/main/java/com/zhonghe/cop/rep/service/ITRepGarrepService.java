package com.zhonghe.cop.rep.service;

import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.cop.rep.domain.bo.TRepGarrepBo;
import com.zhonghe.cop.rep.domain.dto.OwsDto;
import com.zhonghe.cop.rep.domain.query.TRepGarrepQuery;
import com.zhonghe.cop.rep.domain.vo.CopReportResultVo;
import com.zhonghe.cop.rep.domain.vo.TRepGarrepInfoVo;
import com.zhonghe.cop.rep.domain.vo.table.TRepGarrepTableVo;

import java.util.Collection;
import java.util.List;

/**
 * 汇总报，指标字段根据动态添加Service接口
 *
 * <AUTHOR>
 * @date 2023-12-06
 */
public interface ITRepGarrepService {

    /**
     * 查询汇总报，指标字段根据动态添加
     */
    TRepGarrepInfoVo queryById(Long repGarrepId);

    /**
     * 查询汇总报，指标字段根据动态添加列表
     */
    TableDataInfo<TRepGarrepTableVo> queryPageList(TRepGarrepQuery bo, PageQuery pageQuery);

    /**
     * 获取每月1号的汇总消息
     *
     * @return
     */
    String buildFirstMonthMes();

    /**
     * 查询汇总报，指标字段根据动态添加列表
     */
    List<TRepGarrepTableVo> queryList(TRepGarrepQuery bo);

    /**
     * 修改汇总报，指标字段根据动态添加
     */
    Boolean insertByBo(TRepGarrepBo bo);

    /**
     * 修改汇总报，指标字段根据动态添加
     */
    Boolean updateByBo(TRepGarrepBo bo);

    /**
     * 校验并批量删除汇总报，指标字段根据动态添加信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 手动汇总
     *
     * @param gatherDate
     * @return
     */
    Boolean manualGather(String gatherDate);

    /**
     * 查询企业统计
     *
     * @param yearMonth
     * @return
     */
    CopReportResultVo copLevelReport(String yearMonth, String copName);

    OwsDto ows(String yearMonth, Integer level);

    /**
     * 每月1号清除上个月的大屏数据
     */
    void removeNotInBeforeMonthBigMapData();

    /**
     * @param string
     * @return 所有上报时间
     */
    String queryAllRepDate(Long copId, String string);

    /**
     * 查询所有的上报问题
     *
     * @param copId
     * @param string
     * @return
     */
    String queryAllRepProblem(Long copId, String string);
}
