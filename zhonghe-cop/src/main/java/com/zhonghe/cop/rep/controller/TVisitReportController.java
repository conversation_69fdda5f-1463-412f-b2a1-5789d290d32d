package com.zhonghe.cop.rep.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import com.zhonghe.common.annotation.Log;
import com.zhonghe.common.core.controller.BaseController;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import com.zhonghe.common.enums.BusinessType;
import com.zhonghe.common.utils.poi.ExcelUtil;
import com.zhonghe.cop.rep.domain.bo.RegisterHandleResultBo;
import com.zhonghe.cop.rep.domain.bo.VisitReportBo;
import com.zhonghe.cop.rep.domain.query.VisitReportQuery;
import com.zhonghe.cop.rep.domain.vo.VisitReportExportVo;
import com.zhonghe.cop.rep.domain.vo.VisitReportVo;
import com.zhonghe.cop.rep.handler.CustomerWriteHandler;
import com.zhonghe.cop.rep.service.IVisitReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 信访上报控制器
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
@Validated
@Api(value = "信访上报控制器", tags = {"信访上报"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/cop/visitReport")
public class TVisitReportController extends BaseController {

    private final IVisitReportService visitReportService;

    /**
     * 查询信访上报列表(cop:visitReport:list)
     */
    @ApiOperation("查询信访上报列表(cop:visitReport:list)")
    @SaCheckPermission("cop:visitReport:list")
    @GetMapping("/list")
    public TableDataInfo<VisitReportVo> list(VisitReportQuery query, PageQuery pageQuery) {
        return visitReportService.queryPageList(query, pageQuery);
    }

    /**
     * 导出信访上报列表(cop:visitReport:export)
     */
    @ApiOperation("导出信访上报列表(cop:visitReport:export)")
    @SaCheckPermission("cop:visitReport:export")
    @Log(title = "信访上报", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(VisitReportQuery query, HttpServletResponse response) {
        Assert.notNull(query.getInputDate(), "录入日期不能为空");
        List<VisitReportVo> list = visitReportService.queryList(query);
        List<VisitReportExportVo> visitReportExportVos = BeanUtil.copyToList(list, VisitReportExportVo.class);
        for (int i = 0; i < visitReportExportVos.size(); i++) {
            visitReportExportVos.get(i).setCaseNumber(String.valueOf(i + 1));
        }
        Map<String, String> map = new HashMap<>();
        map.put("inputDate", DateUtil.format(query.getInputDate(), "yyyy年MM月"));
        ExcelUtil.exportExcel(visitReportExportVos, DateUtil.format(new Date(), "yyyy年MM月") + "劳动争议信访来访人员登记表", VisitReportExportVo.class,
            new CustomerWriteHandler(map, list.size()), response);
    }

    /**
     * 获取信访上报详细信息(cop:visitReport:query)
     */
    @ApiOperation("获取信访上报详细信息(cop:visitReport:query)")
    @SaCheckPermission("cop:visitReport:query")
    @GetMapping("/{visitReportId}")
    public R<VisitReportVo> getInfo(@ApiParam("信访上报ID")
                                    @PathVariable Long visitReportId) {
        return R.ok(visitReportService.queryById(visitReportId));
    }

    /**
     * 新增信访上报
     */
    @ApiOperation("新增信访上报(cop:visitReport:add)")
    @SaCheckPermission("cop:visitReport:add")
    @Log(title = "信访上报", businessType = BusinessType.INSERT)
    @PostMapping
    public R<Void> add(@Validated(AddGroup.class) @RequestBody VisitReportBo bo) {
        return toAjax(visitReportService.insertByBo(bo));
    }

    /**
     * 修改信访上报
     */
    @ApiOperation("修改信访上报(cop:visitReport:edit)")
    @SaCheckPermission("cop:visitReport:edit")
    @Log(title = "信访上报", businessType = BusinessType.UPDATE)
    @PutMapping
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody VisitReportBo bo) {
        return toAjax(visitReportService.updateByBo(bo));
    }

    /**
     * 删除信访上报
     */
    @ApiOperation("删除信访上报（cop:visitReport:remove)")
    @SaCheckPermission("cop:visitReport:remove")
    @Log(title = "信访上报", businessType = BusinessType.DELETE)
    @DeleteMapping("/{visitReportIds}")
    public R<Void> remove(@ApiParam("信访上报ID串")
                          @PathVariable Long[] visitReportIds) {
        return toAjax(visitReportService.deleteWithValidByIds(Arrays.asList(visitReportIds), true));
    }

    /**
     * PDF上传并OCR识别
     */
    @ApiOperation("PDF上传并OCR识别(cop:visitReport:doOcr)")
    @SaCheckPermission("cop:visitReport:doOcr")
    @PostMapping("/doOcr")
    public R<VisitReportVo> doOcr(MultipartFile file) {
        return R.ok(visitReportService.uploadPdfAndOcr(file));
    }

    /**
     * 文件预览
     */
    @ApiOperation("源文件预览(cop:visitReport:previewSourceFile)")
    @SaCheckPermission("cop:visitReport:previewSourceFile")
    @GetMapping("/previewSourceFile/{visitReportId}")
    public void previewSourceFile(@ApiParam("信访上报ID")
                                  @PathVariable Long visitReportId,
                                  HttpServletRequest request,
                                  HttpServletResponse response) {
        visitReportService.previewSourceFile(visitReportId, request, response);
    }

    /**
     * 处理结果登记
     */
    @ApiOperation("处理结果登记(cop:visitReport:handleResult)")
    @SaCheckPermission("cop:visitReport:handleResult")
    @Log(title = "信访上报", businessType = BusinessType.UPDATE)
    @PostMapping("/handleResult")
    public R<Void> registerHandleResult(@RequestBody RegisterHandleResultBo bo) {
        return toAjax(visitReportService.registerHandleResult(bo));
    }

    /**
     * 导出Word文档
     */
    @ApiOperation("导出Word文档(cop:visitReport:exportWord)")
    @SaCheckPermission("cop:visitReport:exportWord")
    @Log(title = "信访上报", businessType = BusinessType.EXPORT)
    @GetMapping("/exportWord/{visitReportId}")
    public void exportWord(@ApiParam("信访上报ID")
                           @PathVariable Long visitReportId,
                           HttpServletResponse response) {
        visitReportService.exportWord(visitReportId, response);
    }

}
