package com.zhonghe.cop.rep.domain.bo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Author: lpg
 * @Date: 2023/12/06
 * @Description: 处理反馈业务对象
 */
@Data
public class HandExiReportBo implements Serializable {
    private static final long serialVersionUID = 6143956265902778147L;

    /**
     * 紧急事件上报ID
     */
    @ApiModelProperty(value = "紧急事件上报ID")
    @NotNull(message = "紧急事件上报ID不能为空")
    private Long exiReportId;
    /**
     * 采取措施
     */
    @ApiModelProperty(value = "采取措施")
    @Length(max = 4000, message = "采取措施长度不能超过4000")
    @NotBlank(message = "采取措施不能为空")
    private String copdeal;

    /**
     * 企业现状
     */
    @ApiModelProperty(value = "企业现状")
    @Length(max = 4000, message = "企业现状长度不能超过4000")
    @NotBlank(message = "企业现状不能为空")
    private String copresult;

    /**
     * 后续工作
     */
    @ApiModelProperty(value = "后续工作")
    @Length(max = 4000, message = "后续工作长度不能超过4000")
    @NotBlank(message = "后续工作不能为空")
    private String copafterwork;

    /**
     * 请求协助
     */
    @ApiModelProperty(value = "请求协助")
    @Length(max = 4000, message = "请求协助长度不能超过4000")
    private String cophelp;

}
