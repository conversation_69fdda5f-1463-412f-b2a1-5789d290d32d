package com.zhonghe.cop.rep.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zhonghe.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;


/**
 * 汇总报，指标字段根据动态添加对象 t_rep_garrep
 *
 * <AUTHOR>
 * @date 2023-12-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("cop.t_rep_garrep")
public class TRepGarrep extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 汇总报表ID
     */
    @TableId
    private Long repGarrepId;
    /**
     * 是否删除
     */
    @TableLogic
    private String delFlag;
    /**
     * 备注
     */
    private String note;
    /**
     * 汇总月份
     */
    private Date repDate;
    /**
     * 企业ID
     */
    private Long copId;
    /**
     * 部门ID
     */
    private Long deptId;
    /**
     * 各风险等级（逗号分隔，如1,1,2,4）
     */
    private String levelAll;
    /**
     * 最终风险等级（默认0，0——正常、1——问题、2——风险、3——危险、4——高危）
     */
    @TableField(value = "\"level\"")
    private Integer level;
    /**
     * 拖欠工资月数（月）
     */
    private BigDecimal propaymonth;
    /**
     * 拖欠工资（月）
     */
    private String propaymonths;

    /**
     * 拖欠工资涉及人数（人）
     */
    private BigDecimal propaypopulation;

    /**
     * 拖欠工资金额（元）
     */
    private BigDecimal propaymoney;

    /**
     * 拖欠税款月数（月）
     */
    private BigDecimal protaxmonth;

    /**
     * 拖欠税款（月）
     */
    private String protaxmonths;

    /**
     * 拖欠税款金额（元）
     */
    private BigDecimal protaxmoney;

    /**
     * 拖欠社保月数（月）
     */
    private BigDecimal proinsuremonth;

    /**
     * 拖欠社保（月）
     */
    private String proinsuremonths;

    /**
     * 拖欠社保金额（元）
     */
    private BigDecimal proinsuremoney;

    /**
     * 拖欠水费月数（月）
     */
    private BigDecimal prowatermonth;

    /**
     * 拖欠水费（月）
     */
    private String prowatermonths;

    /**
     * 拖欠水费金额（元）
     */
    private BigDecimal prowatermoney;

    /**
     * 拖欠电费月数（月）
     */
    private BigDecimal proenergymonth;

    /**
     * 拖欠电费（月）
     */
    private String proenergymonths;

    /**
     * 拖欠电费金额（元）
     */
    private BigDecimal proenergymoney;

    /**
     * 拖欠租金月数（月）
     */
    private BigDecimal prorentmonth;

    /**
     * 拖欠租金（月）
     */
    private String prorentmonths;

    /**
     * 拖欠租金金额（元）
     */
    private BigDecimal prorentmoney;

    /**
     * 其他
     */
    private String other;

    /**
     * 其他问题
     */
    private String problem;

    /**
     * 监控措施
     */
    private String copoversee;

    /**
     * 建议措施
     */
    private String copadvice;

    /**
     * 采取措施
     */
    private String copdeal;

    /**
     * 企业现状
     */
    private String copresult;

    /**
     * 后续工作
     */
    private String copafterwork;

    /**
     * 请求协助
     */
    private String cophelp;


}
