package com.zhonghe.cop.rep.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zhonghe.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 上报指标对象 t_rep_target
 *
 * <AUTHOR>
 * @date 2023-12-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("cop.t_rep_target")
public class TRepTarget extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 上报指标ID
     */
    @TableId
    private Long repTargetId;
    /**
     * 是否删除
     */
    @TableLogic
    private String delFlag;
    /**
     * 说明
     */
    private String note;
    /**
     * 指标名称
     */
    private String tarName;
    /**
     * 指标值类型(默认0，0——数值、1——描述)
     */
    private Long tarValueType;
    /**
     * 指标代码
     */
    private String tarCode;
    /**
     * 指标类型(默认0，0——普通指标、1——处理结果)
     */
    private Long tarType;

}
