package com.zhonghe.cop.rep.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zhonghe.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 指标-部门关联，用于生成上报报对象 t_rep_deptar
 *
 * <AUTHOR>
 * @date 2023-12-06
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("cop.t_rep_deptar")
public class TRepDeptar extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 部门指标ID
     */
    @TableId
    private Long repDeptarId;
    /**
     * 是否删除
     */
    @TableLogic
    private String delFlag;
    /**
     * 备注
     */
    private String note;
    /**
     * 部门ID
     */
    private Long depId;
    /**
     * 指标ID（对应t_rep_target表主键）
     */
    private Long repTargetId;
    /**
     * 排序
     */
    @TableField("\"order\"")
    private Integer order;

}
