package com.zhonghe.cop.rep.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhonghe.common.constant.Constants;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.domain.entity.SysDept;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.exception.ServiceException;
import com.zhonghe.common.helper.IdGeneratorHelper;
import com.zhonghe.common.helper.LoginHelper;
import com.zhonghe.common.utils.BeanCopyUtils;
import com.zhonghe.common.utils.DateUtils;
import com.zhonghe.common.utils.poi.ExcelUtil;
import com.zhonghe.cop.bam.constants.CopConstants;
import com.zhonghe.cop.bam.domain.TCopInformation;
import com.zhonghe.cop.bam.domain.TPerPerformance;
import com.zhonghe.cop.bam.domain.vo.TCopInformationVo;
import com.zhonghe.cop.bam.mapper.TCopInformationMapper;
import com.zhonghe.cop.bam.mapper.TPerPerformanceMapper;
import com.zhonghe.cop.rep.domain.DangerousEnterprises;
import com.zhonghe.cop.rep.domain.HighRiskEnterprises;
import com.zhonghe.cop.rep.domain.ProblemEnterprises;
import com.zhonghe.cop.rep.domain.RiskEnterprises;
import com.zhonghe.cop.rep.domain.TRepGarrep;
import com.zhonghe.cop.rep.domain.TRepNoproblem;
import com.zhonghe.cop.rep.domain.TRepReport;
import com.zhonghe.cop.rep.domain.TRepReportPropay;
import com.zhonghe.cop.rep.domain.TRepTarget;
import com.zhonghe.cop.rep.domain.bo.HandDeptReportBo;
import com.zhonghe.cop.rep.domain.bo.ReportAuditBo;
import com.zhonghe.cop.rep.domain.bo.ReportResultAuditBo;
import com.zhonghe.cop.rep.domain.bo.TRepReportBo;
import com.zhonghe.cop.rep.domain.dto.CopLevelDto;
import com.zhonghe.cop.rep.domain.dto.DeptTargetDto;
import com.zhonghe.cop.rep.domain.dto.DynamicExcelData;
import com.zhonghe.cop.rep.domain.excel.TRepReportExcel;
import com.zhonghe.cop.rep.domain.query.TRepReportQuery;
import com.zhonghe.cop.rep.domain.vo.MonthReportVo;
import com.zhonghe.cop.rep.domain.vo.QuarterReportVo;
import com.zhonghe.cop.rep.domain.vo.TRepGarrepInfoVo;
import com.zhonghe.cop.rep.domain.vo.TRepGarrepMapVo;
import com.zhonghe.cop.rep.domain.vo.TRepGarrepVo;
import com.zhonghe.cop.rep.domain.vo.TRepReportInfoVo;
import com.zhonghe.cop.rep.domain.vo.TRepReportProPayVo;
import com.zhonghe.cop.rep.domain.vo.TRepReportVo;
import com.zhonghe.cop.rep.domain.vo.YearReportVo;
import com.zhonghe.cop.rep.domain.vo.table.TRepReportTableVo;
import com.zhonghe.cop.rep.em.AuditingStatus;
import com.zhonghe.cop.rep.em.DespachoStatus;
import com.zhonghe.cop.rep.em.FinishStatus;
import com.zhonghe.cop.rep.em.IsDeal;
import com.zhonghe.cop.rep.em.Level;
import com.zhonghe.cop.rep.em.ResultAuditingStatus;
import com.zhonghe.cop.rep.em.TargetType;
import com.zhonghe.cop.rep.mapper.DangerousEnterprisesMapper;
import com.zhonghe.cop.rep.mapper.HighRiskEnterprisesMapper;
import com.zhonghe.cop.rep.mapper.ProblemEnterprisesMapper;
import com.zhonghe.cop.rep.mapper.RiskEnterprisesMapper;
import com.zhonghe.cop.rep.mapper.TRepGarrepMapper;
import com.zhonghe.cop.rep.mapper.TRepNoproblemMapper;
import com.zhonghe.cop.rep.mapper.TRepReportMapper;
import com.zhonghe.cop.rep.mapper.TRepReportPropayMapper;
import com.zhonghe.cop.rep.mapper.TRepTargetMapper;
import com.zhonghe.cop.rep.service.ITRepReportService;
import com.zhonghe.cop.score.service.ITCopScoreRecordService;
import com.zhonghe.cop.utils.CoordinateTransformUtil;
import com.zhonghe.cop.utils.ExcelUtils;
import com.zhonghe.system.mapper.SysDeptMapper;
import com.zhonghe.system.service.ISysDictDataService;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import net.postgis.jdbc.geometry.Point;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * 部门上报Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-06
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class TRepReportServiceImpl implements ITRepReportService {

    private static final String HTML = "<span style='color:red'>(%s)</span>";
    private final TRepReportMapper baseMapper;
    private final TRepGarrepMapper repGarrepMapper;
    private final TRepTargetMapper repTargetMapper;
    private final TCopInformationMapper tCopInformationMapper;
    private final ITCopScoreRecordService itCopScoreRecordService;
    private final TPerPerformanceMapper tPerPerformanceMapper;
    private final TRepTargetMapper tRepTargetMapper;
    private final SysDeptMapper sysDeptMapper;

    private final ProblemEnterprisesMapper problemEnterprisesMapper;
    private final RiskEnterprisesMapper riskEnterprisesMapper;
    private final DangerousEnterprisesMapper dangerousEnterprisesMapper;
    private final HighRiskEnterprisesMapper highRiskEnterprisesMapper;

    private final ISysDictDataService iSysDictDataService;
    private final TRepNoproblemMapper repNoproblemMapper;
    private final TRepReportPropayMapper tRepReportPropayMapper;

    private static void complateLevel(List<TRepReport> repReports, TRepReport add) {
        if (!repReports.isEmpty()) {
            //获取最新上报的一条数据
            TRepReport tRepReport = repReports.stream().findFirst().get();
            //如果本次上报的风险等级小于上个月的风险等级，则不予上报
            if (add.getLevel() < tRepReport.getLevel()) {
                throw new ServiceException("风险等级不能低于上个月的风险等级");
            }

        }
    }

    public static void main(String[] args) {
        List<TRepReport> tRepReports = new ArrayList<>();
        System.out.println(tRepReports.stream().allMatch(a -> a.getFinishStatus().equals(FinishStatus.FINISHED.getCode())));
    }

    @Override
    public String getTarNote(TRepReport tRepReport) {
        List<String> tars = Arrays.asList(
            "propaymonths",//拖欠工资（月）
            "propaypopulation",//拖欠工资涉及人数（人）
            "propaymoney",//拖欠工资金额（元）
            "protaxmonths",//拖欠税款（月）
            "protaxmoney",// 拖欠税款金额（元）
            "proinsuremonths",//拖欠社保（月）
            "proinsuremoney",//拖欠社保金额（元）
            "prowatermonths",//拖欠水费（月）
            "prowatermoney",//拖欠水费金额（元）
            "proenergymonths",//拖欠电费（月）
            "proenergymoney",//拖欠电费金额（元）
            "prorentmonths",//拖欠租金（月）
            "prorentmoney",//拖欠租金金额（元）
            "other"//其他
        );

        Map<String, String> monthTargetsMap = new HashMap<>();
        monthTargetsMap.put("propaymonths", "拖欠工资（月）");
        monthTargetsMap.put("protaxmonths", "拖欠税款（月）");
        monthTargetsMap.put("proinsuremonths", "拖欠社保（月）");
        monthTargetsMap.put("prowatermonths", "拖欠水费（月）");
        monthTargetsMap.put("proenergymonths", "拖欠电费（月）");
        monthTargetsMap.put("prorentmonths", "拖欠租金（月）");
        //查询所有指标信息
        Map<String, String> targetMap =
            tRepTargetMapper.selectList(Wrappers.<TRepTarget>lambdaQuery().eq(TRepTarget::getTarType, TargetType.NORMAL.getCode()))
                .stream().collect(Collectors.toMap(TRepTarget::getTarCode, TRepTarget::getTarName));

        StringBuilder sb = new StringBuilder();
        //根据字段确定指标设置备注信息
        // 获取类的所有字段
        Field[] fields = TRepReport.class.getDeclaredFields();

        for (Field field : fields) {
            // 如果你想获取字段的值，需要设置字段为可访问
            field.setAccessible(true);
            try {
                Object value = field.get(tRepReport);

                if (value instanceof BigDecimal && isZeroValue(value)) {
                    field.set(tRepReport, null);
                    value = null;
                }

                // 如果字段值不为 null，将字段名添加到集合中
                if (value != null && tars.contains(field.getName().toLowerCase()) && !isZeroValue(value)) {
                    String tarName = targetMap.get(field.getName().toUpperCase());
                    if (StrUtil.isNotBlank(tarName) && !monthTargetsMap.containsKey(field.getName().toLowerCase())) {
                        sb.append(tarName).append(":").append(value).append(";");
                    }
                    if (monthTargetsMap.containsKey(field.getName().toLowerCase())) {
                        sb.append(monthTargetsMap.get(field.getName().toLowerCase())).append(":");
                        String formattedDates = Arrays.stream(value.toString().split(","))
                            .map(a -> DateUtil.format(DateUtil.parse(a, "yyyy-MM"), "yyyy年MM月"))
                            .collect(Collectors.joining("、"));
                        sb.append(formattedDates).append(";");
                    }
                }
                if (field.getName().equals("other") && value != null && StringUtils.isNotBlank(value.toString())) {
                    sb.append("其他").append(":").append(value).append(";");
                }


            } catch (IllegalAccessException e) {
                System.out.println("Field Value: " + e.getMessage());
            }
        }
        return sb.toString();
    }

    /**
     * 判断值是否为零（支持不同类型）
     */
    private boolean isZeroValue(Object value) {
        if (value == null) {
            return true;
        }

        if (value instanceof BigDecimal) {
            return ((BigDecimal) value).compareTo(BigDecimal.ZERO) == 0;
        } else if (value instanceof String) {
            String strValue = (String) value;
            return strValue.trim().isEmpty() || "0".equals(strValue.trim()) || "0.00".equals(strValue.trim());
        } else if (value instanceof Integer) {
            return ((Integer) value) == 0;
        } else if (value instanceof Long) {
            return ((Long) value) == 0L;
        } else if (value instanceof Double) {
            return ((Double) value) == 0.0;
        } else if (value instanceof Float) {
            return ((Float) value) == 0.0f;
        }

        // 对于其他类型，尝试转换为字符串进行判断
        String strValue = value.toString().trim();
        return strValue.isEmpty() || "0".equals(strValue) || "0.00".equals(strValue);
    }

    /**
     * 查询部门上报
     */
    @Override
    public TRepReportInfoVo queryById(Long repReportId) {
        TRepReportVo tRepReportVo = baseMapper.selectVoById(repReportId);
        List<TRepReportProPayVo> tRepReportProPayVos = tRepReportPropayMapper.selectVoList(Wrappers.<TRepReportPropay>lambdaQuery()
            .eq(TRepReportPropay::getRepReportId, repReportId).orderByDesc(TRepReportPropay::getCreateTime));
        tRepReportVo.setProPayList(tRepReportProPayVos);
        if (ObjectUtil.isNotNull(tRepReportVo)) {
            TCopInformationVo TCopInformationVo = tCopInformationMapper.selectVoById(tRepReportVo.getCopId());
            return TRepReportInfoVo.builder().repReport(tRepReportVo).copInformation(TCopInformationVo).build();
        }
        return null;
    }

    /**
     * 查询部门上报
     */
    @Override
    public TableDataInfo<TRepReportTableVo> queryPageList(TRepReportQuery bo, PageQuery pageQuery) {
        QueryWrapper<TRepReport> lqw = buildQueryWrapper(bo);
        if ("level".equals(pageQuery.getOrderByColumn())) {
            pageQuery.setOrderByColumn("\"level\"");
        }
        Page<TRepReportTableVo> result = baseMapper.selectRepReportList(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 构建查询条件
     *
     * @param bo
     * @return
     */
    private QueryWrapper<TRepReport> buildQueryWrapper(TRepReportQuery bo) {
        QueryWrapper<TRepReport> qw = Wrappers.query();
        //企业名称
        qw.like(StringUtils.isNotBlank(bo.getCopName()), "tci.cop_name", bo.getCopName());
        //上报日期
        qw.apply(StringUtils.isNotBlank(bo.getRepDate()), "to_char(trr.rep_date,'yyyy-MM') = {0}", bo.getRepDate());
        //审核状态
        qw.eq(bo.getAuditing() != null, "trr.auditing", bo.getAuditing());
        //处理结果审核
        qw.eq(bo.getResultAuditing() != null, "trr.result_auditing", bo.getResultAuditing());
        //风险级别
        qw.eq(bo.getLevel() != null, "trr.level", bo.getLevel());
        qw.eq("trr.del_flag", Constants.DEL_FLAG_NORMAL);
        qw.orderByDesc("trr.rep_date", "trr.create_time");
        return qw;
    }

    /**
     * 查询部门上报
     */
    @Override
    public List<TRepReportTableVo> queryList(TRepReportQuery bo) {
        QueryWrapper<TRepReport> lqw = buildQueryWrapper(bo);
        return baseMapper.selectRepReportList(lqw);
    }

    /**
     * 合并部门上报数据
     * 一、上个月的数据比较
     * 1.如果上个月的数据存在，判断上个月的风险等级，如果上个月的风险等级小于本次上报的风险等级，则已这个月的为准
     * 2.如果上个月的数据不存在，以本次上报的为准
     * 3.如果风险等级与上个月相同升一级
     * 二、不同部门的数据比较
     * 1.如果两个部门报同一个企业的，以最新的为准
     * 2.如果两个部门风险等级相同，升一级
     *
     * @param repReport
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void mergeCopLevelData(TRepReport repReport) {

        if (repReport.getFinishStatus().equals(FinishStatus.FINISHED.getCode())) {
            log.info("已处理完成的数据不进行合并,repReportId:{}", repReport.getRepReportId());
            return;
        }
        CopyOptions copyOptions = new CopyOptions().setIgnoreProperties("createTime", "createBy", "updateTime", "updateBy");

        AtomicReference<TRepGarrep> save = new AtomicReference<>();
        //本月报表日期
        var repDate = DateUtil.format(DateUtil.beginOfMonth(DateUtil.offsetMonth(repReport.getRepDate(), 1)), DateUtils.YYYY_MM_DD);
        //上个月报表日期
        var lastRepDate = DateUtil.format(DateUtil.beginOfMonth(repReport.getRepDate()), DateUtils.YYYY_MM_DD);

        //上个月的数据比较
        var tRepGarrep = repGarrepMapper.selectOne(Wrappers.<TRepGarrep>lambdaQuery()
            .eq(TRepGarrep::getCopId, repReport.getCopId())
            .apply("TO_CHAR(rep_date, 'yyyy-MM-dd') =  {0}", lastRepDate));

        if (ObjectUtil.isNotNull(tRepGarrep) && tRepGarrep.getLevel() <= repReport.getLevel()) {

            //如果上个月的风险等级小于本次上报的风险等级，则已这个月的为准
            if (tRepGarrep.getLevel() < repReport.getLevel()) {
                TRepGarrep repGarrep = BeanUtil.toBean(repReport, TRepGarrep.class, copyOptions);
                save.set(repGarrep);
            }
            //如果上个月的风险等级等于本次的风险等级，则升一级
            if (tRepGarrep.getLevel().equals(repReport.getLevel()) && !repReport.getLevel().equals(Level.HIGH_DANGER.getCode())) {
                TRepGarrep repGarrep = BeanUtil.toBean(repReport, TRepGarrep.class, copyOptions);
                repGarrep.setLevel(repReport.getLevel() + 1);
                save.set(repGarrep);
            } else {
                TRepGarrep repGarrep = BeanUtil.toBean(repReport, TRepGarrep.class, copyOptions);
                save.set(repGarrep);
            }
        } else {
            var repGarrep = BeanUtil.toBean(repReport, TRepGarrep.class, copyOptions);
            save.set(repGarrep);
        }
        LambdaQueryWrapper<TRepReport> lambdaQueryWrapper = Wrappers.<TRepReport>lambdaQuery()
            .eq(TRepReport::getCopId, repReport.getCopId())
            .eq(TRepReport::getAuditing, AuditingStatus.AUDITED.getCode())
            .apply("TO_CHAR(rep_date, 'yyyy-MM') =  {0}", DateUtil.format(DateUtil.beginOfMonth(repReport.getRepDate()), "yyyy-MM"))
            .orderByDesc(TRepReport::getCreateTime);

        //不同部门的数据比较，如果存在两个部门不同的数据则进行处理
        List<TRepReport> tRepReports = baseMapper.selectList(lambdaQueryWrapper);


        //获取最新的一条数据
        if (!tRepReports.isEmpty()) {
            //所有企业风险级别
            String levelAll = tRepReports.stream().map(TRepReport::getLevel).map(Objects::toString).distinct().collect(Collectors.joining(","));
            if (save.get() != null) {
                save.get().setLevelAll(levelAll);
            }
            var tRepReport = tRepReports.stream().findFirst().get();

            if (!repReport.getLevel().equals(Level.HIGH_DANGER.getCode())) {
                var repGarrep = BeanUtil.toBean(repReport, TRepGarrep.class, copyOptions);

                //两个部门不同
                if (!Objects.equals(repReport.getDeptId(), tRepReport.getDeptId())) {
                    //同一个企业
                    //获取部门最大的风险等级
                    Integer maxLevel = tRepReports.stream().map(TRepReport::getLevel).max(Integer::compareTo).get();
                    repGarrep.setLevel(maxLevel);
                }
                //风险等级相同升一级
                if (repReport.getLevel().equals(tRepReport.getLevel())) {
                    //获取部门最大的风险等级
                    Integer maxLevel = tRepReports.stream().map(TRepReport::getLevel).max(Integer::compareTo).get();
                    repGarrep.setLevel(maxLevel + 1);
                }
                save.set(repGarrep);
            }

        }


        TRepGarrep garrep = save.get();
        //如果风险等级大于高危自动设置高危
        if (garrep.getLevel() > Level.HIGH_DANGER.getCode()) {
            garrep.setLevel(Level.HIGH_DANGER.getCode());
        }
        StringBuilder problem = new StringBuilder();
        StringBuilder copoversee = new StringBuilder();
        StringBuilder copadvice = new StringBuilder();

        StringBuilder copdeal = new StringBuilder();
        StringBuilder copresult = new StringBuilder();
        StringBuilder copafterwork = new StringBuilder();
        StringBuilder cophelp = new StringBuilder();

        //查询不同部门的上报数据根据部门进行分组，合并采取措施、企业现状、后续工作、请求协助
        Map<Long, List<TRepReport>> deptRepMap = tRepReports.stream().collect(Collectors.groupingBy(TRepReport::getDeptId));

        deptRepMap.forEach((k, v) -> {
            Optional<TRepReport> max = v.stream().max(Comparator.comparing(TRepReport::getCreateTime));

            //合并最新时间的拖欠工资月数（月）
            max.ifPresent(tRepReport -> {
                if (tRepReport.getPropaymonth() != null) {
                    garrep.setPropaymonth(tRepReport.getPropaymonth());
                }
            });
            //合并最新时间的拖欠工资（月）
            max.ifPresent(tRepReport -> {
                if (tRepReport.getPropaymonths() != null) {
                    garrep.setPropaymonths(tRepReport.getPropaymonths());
                }
            });
            //合并最新时间的拖欠工资人数（人）
            max.ifPresent(tRepReport -> {
                if (tRepReport.getPropaypopulation() != null) {
                    garrep.setPropaypopulation(tRepReport.getPropaypopulation());
                }
            });
            //合并最新时间的拖欠工资金额（元）
            max.ifPresent(tRepReport -> {
                if (tRepReport.getPropaymoney() != null) {
                    garrep.setPropaymoney(tRepReport.getPropaymoney());
                }
            });

            //合并最新时间的拖欠税款月数（月）
            max.ifPresent(tRepReport -> {
                if (tRepReport.getProtaxmonth() != null) {
                    garrep.setProtaxmonth(tRepReport.getProtaxmonth());
                }
            });
            //合并最新时间的拖欠税款（月）
            max.ifPresent(tRepReport -> {
                if (tRepReport.getProtaxmonths() != null) {
                    garrep.setProtaxmonths(tRepReport.getProtaxmonths());
                }
            });
            //合并最新时间的拖欠税款金额（元）
            max.ifPresent(tRepReport -> {
                if (tRepReport.getProtaxmoney() != null) {
                    garrep.setProtaxmoney(tRepReport.getProtaxmoney());
                }
            });
            //合并最新时间的拖欠社保月数（月）
            max.ifPresent(tRepReport -> {
                if (tRepReport.getProinsuremonth() != null) {
                    garrep.setProinsuremonth(tRepReport.getProinsuremonth());
                }
            });
            //合并最新时间的拖欠社保（月）
            max.ifPresent(tRepReport -> {
                if (tRepReport.getProinsuremonths() != null) {
                    garrep.setProinsuremonths(tRepReport.getProinsuremonths());
                }
            });
            //合并最新时间的拖欠社保金额（元）
            max.ifPresent(tRepReport -> {
                if (tRepReport.getProinsuremoney() != null) {
                    garrep.setProinsuremoney(tRepReport.getProinsuremoney());
                }
            });
            //合并最新时间的拖欠水费月数（月）
            max.ifPresent(tRepReport -> {
                if (tRepReport.getProwatermonth() != null) {
                    garrep.setProwatermonth(tRepReport.getProwatermonth());
                }
            });
            //合并最新时间的拖欠水费（月）
            max.ifPresent(tRepReport -> {
                if (tRepReport.getProwatermonths() != null) {
                    garrep.setProwatermonths(tRepReport.getProwatermonths());
                }
            });
            //合并最新时间的拖欠水费金额（元）
            max.ifPresent(tRepReport -> {
                if (tRepReport.getProwatermoney() != null) {
                    garrep.setProwatermoney(tRepReport.getProwatermoney());
                }
            });
            //合并最新时间的拖欠电费月数（月）
            max.ifPresent(tRepReport -> {
                if (tRepReport.getProenergymonth() != null) {
                    garrep.setProenergymonth(tRepReport.getProenergymonth());
                }
            });
            //合并最新时间的拖欠电费（月）
            max.ifPresent(tRepReport -> {
                if (tRepReport.getProenergymonths() != null) {
                    garrep.setProenergymonths(tRepReport.getProenergymonths());
                }
            });
            //合并最新时间的拖欠电费金额（元）
            max.ifPresent(tRepReport -> {
                if (tRepReport.getProenergymoney() != null) {
                    garrep.setProenergymoney(tRepReport.getProenergymoney());
                }
            });
            //合并最新时间的拖欠房租月数（月）
            max.ifPresent(tRepReport -> {
                if (tRepReport.getProrentmonth() != null) {
                    garrep.setProrentmonth(tRepReport.getProrentmonth());
                }
            });
            //合并最新时间的拖欠房租（月）
            max.ifPresent(tRepReport -> {
                if (tRepReport.getProrentmonths() != null) {
                    garrep.setProrentmonths(tRepReport.getProrentmonths());
                }
            });
            //合并最新时间的拖欠房租金额（元）
            max.ifPresent(tRepReport -> {
                if (tRepReport.getProrentmoney() != null) {
                    garrep.setProrentmoney(tRepReport.getProrentmoney());
                }
            });
            //合并其他
            max.ifPresent(tRepReport -> {
                if (StringUtils.isNotBlank(tRepReport.getOther())) {
                    garrep.setOther(tRepReport.getOther());
                }
            });
            //合并处理反馈
            String _problem = v.stream().map(TRepReport::getProblem).filter(StringUtils::isNotBlank).collect(Collectors.joining(";"));
            if (StringUtils.isNotBlank(_problem)) {
                problem.append(_problem).append(";");
            }
            //合并监控措施
            String _copoversee = v.stream().map(TRepReport::getCopoversee).filter(StringUtils::isNotBlank).collect(Collectors.joining(";"));
            if (StringUtils.isNotBlank(_copoversee)) {
                copoversee.append(_copoversee).append(";");
            }
            //合并建议措施
            String _copadvice = v.stream().map(TRepReport::getCopadvice).filter(StringUtils::isNotBlank).collect(Collectors.joining(";"));
            if (StringUtils.isNotBlank(_copadvice)) {
                copadvice.append(_copadvice).append(";");
            }

            //合并采取措施,多个合并结尾加分号
            String _copdeal = v.stream().map(TRepReport::getCopdeal).filter(StringUtils::isNotBlank).collect(Collectors.joining(";"));
            if (StringUtils.isNotBlank(_copdeal)) {
                copdeal.append(_copdeal).append(";");
            }
            //合并企业现状
            String _copresult = v.stream().map(TRepReport::getCopresult).filter(StringUtils::isNotBlank).collect(Collectors.joining(";"));
            if (StringUtils.isNotBlank(_copresult)) {
                copresult.append(_copresult).append(";");
            }
            //合并后续工作
            String _copafterwork = v.stream().map(TRepReport::getCopafterwork).filter(StringUtils::isNotBlank).collect(Collectors.joining(";"));
            if (StringUtils.isNotBlank(_copafterwork)) {
                copafterwork.append(_copafterwork).append(";");
            }
            //合并请求协助
            String _cophelp = v.stream().map(TRepReport::getCophelp).filter(StringUtils::isNotBlank).collect(Collectors.joining(";"));
            if (StringUtils.isNotBlank(_cophelp)) {
                cophelp.append(_cophelp).append(";");
            }

        });


        //查询是否存在人社部门上报的数据
        Set<Long> deptIds = deptRepMap.keySet();
        List<SysDept> sysDepts = sysDeptMapper.selectBatchIds(deptIds);
        boolean matchRsDept = sysDepts.stream().anyMatch(sd -> sd.getDeptName().contains("人社分局"));
        //如果存在人社分局的数据，涉及拖欠工资的数额及月份，以人社为准
        if (matchRsDept) {
            //获取最新人设分局的数据
            Optional<SysDept> rsDept = sysDepts.stream().filter(a -> a.getDeptName().contains("人社分局")).findFirst();
            if (rsDept.isPresent()) {
                Long deptId = rsDept.get().getDeptId();
                deptRepMap.get(deptId).stream().max(Comparator.comparing(TRepReport::getCreateTime)).ifPresent(tRepReport -> {
                    //合并最新时间的拖欠工资月数（月）
                    if (tRepReport.getPropaymonth() != null) {
                        garrep.setPropaymonth(tRepReport.getPropaymonth());
                    }
                    //合并最新时间的拖欠工资（月）
                    if (tRepReport.getPropaymonths() != null) {
                        garrep.setPropaymonths(tRepReport.getPropaymonths());
                    }
                    //合并最新时间的拖欠工资人数（人）
                    if (tRepReport.getPropaypopulation() != null) {
                        garrep.setPropaypopulation(tRepReport.getPropaypopulation());
                    }
                    //合并最新时间的拖欠工资金额（元）
                    if (tRepReport.getPropaymoney() != null) {
                        garrep.setPropaymoney(tRepReport.getPropaymoney());
                    }
                });
            }


        }


        //删除本月报表数据
        repGarrepMapper.delete(Wrappers.<TRepGarrep>lambdaQuery()
            .eq(TRepGarrep::getCopId, repReport.getCopId())
            .apply("TO_CHAR(rep_date, 'yyyy-MM-dd') =  {0}", repDate));

        garrep.setProblem(problem.toString());
        garrep.setCopoversee(copoversee.toString());
        garrep.setCopadvice(copadvice.toString());

        garrep.setCopdeal(copdeal.toString());
        garrep.setCopresult(copresult.toString());
        garrep.setCopafterwork(copafterwork.toString());
        garrep.setCophelp(cophelp.toString());
        garrep.setRepDate(DateUtil.parse(repDate, "yyyy-MM"));
        //存在问题设置
        String note = getTarNote(BeanUtil.toBean(garrep, TRepReport.class));
        garrep.setNote(note);
        repGarrepMapper.insert(garrep);
        //更新企业风险等级
        TRepReport bean = BeanUtil.toBean(garrep, TRepReport.class, copyOptions);
        updateBigMapCopLevelData(bean);


    }

    /**
     * 新增部门上报
     * <p>
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(TRepReportBo bo) {
        TRepReport add = BeanUtil.toBean(bo, TRepReport.class);
        add.setDeptId(LoginHelper.getDeptId());
        add.setUserId(LoginHelper.getUserId());
        add.setNote(getTarNote(add));

        //判断企业是否存在坐标
        TCopInformation tCopInformation = tCopInformationMapper.selectById(bo.getCopId());
//        if (ObjectUtil.isNull(tCopInformation.getGeom())) {
//            throw new ServiceException("企业定位信息不存在，请及时联系更新!");
//        }

        if (StringUtils.isBlank(add.getNote())) {
            throw new ServiceException("上报指标信息不能为空");
        }
        //本部门每个月只能上报一次
        boolean tRepReportExists = baseMapper.exists(Wrappers.<TRepReport>lambdaQuery()
            .eq(TRepReport::getCopId, add.getCopId())
            .eq(TRepReport::getDeptId, add.getDeptId())
            .ne(TRepReport::getAuditing, AuditingStatus.RETURNED.getCode())
            .apply("TO_CHAR(rep_date, 'YYYY-MM') =  {0}", DateUtil.format(DateUtil.beginOfMonth(add.getRepDate()), "yyyy-MM")));
        if (tRepReportExists) {
            throw new ServiceException("本月已经上报");
        }

       /* //查询最近上报一次的数据
        List<TRepReport> repReports = baseMapper.selectList(Wrappers.<TRepReport>lambdaQuery()
            .eq(TRepReport::getCopId, add.getCopId())
            .ne(TRepReport::getAuditing, AuditingStatus.RETURNED.getCode())
            .orderByDesc(TRepReport::getCreateTime)
            .apply("TO_CHAR(rep_date, 'YYYY-MM') =  {0}", DateUtil.format(DateUtil.beginOfMonth(add.getRepDate()), "yyyy-MM")));

        //风险等级判定
        complateLevel(repReports, add);*/

        //判定上报是高危、企业人数不超过5人强制为危险
        if (add.getLevel().equals(Level.HIGH_DANGER.getCode()) && (ObjectUtil.isNull(tCopInformation.getCopPopulation()) || tCopInformation.getCopPopulation() <= 5)) {
            add.setLevel(Level.DANGER.getCode());
        }

        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setRepReportId(add.getRepReportId());
        }

        //更新绩效考核信息，查询部门本月是否上报
        Date date = DateUtil.beginOfMonth(new Date());
        TPerPerformance tPerPerformance = tPerPerformanceMapper.selectOne(Wrappers.<TPerPerformance>lambdaQuery()
            .eq(TPerPerformance::getDeptId, LoginHelper.getDeptId()).eq(TPerPerformance::getRepDate, date));
        if (tPerPerformance != null) {
            //设置已上报
            tPerPerformance.setIsDeal(IsDeal.YES.getCode());
            tPerPerformanceMapper.updateById(tPerPerformance);
        } else {
            //新增绩效考核信息
            tPerPerformance = new TPerPerformance();
            tPerPerformance.setDeptId(LoginHelper.getDeptId());
            tPerPerformance.setRepDate(date);
            tPerPerformance.setIsDeal(IsDeal.YES.getCode());
            tPerPerformanceMapper.insert(tPerPerformance);
        }


        return flag;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateBigMapCopLevelData(TRepReport bo) {
        TCopInformation tCopInformation = tCopInformationMapper.selectById(bo.getCopId());
        String copProperty = iSysDictDataService.selectDictLabel(CopConstants.COP_PROPERTY, String.valueOf(tCopInformation.getPropertyId()));

        CopLevelDto copLevelDto = new CopLevelDto();
        copLevelDto.setEnterpriseName(tCopInformation.getCopName());
        copLevelDto.setLegalPerson(tCopInformation.getCopOwner());
        copLevelDto.setContactPhone(tCopInformation.getCopPhone());
        copLevelDto.setEnterpriseId(tCopInformation.getCopId());
        copLevelDto.setEnterpriseAddress(tCopInformation.getCopAddress());
        copLevelDto.setEnterpriseProperty(copProperty);
        copLevelDto.setKeyId(IdGeneratorHelper.next());
        //copLevelDto.setReportPerson(); //上报人
        if (bo.getDeptId() != null) {
            SysDept sysDept = sysDeptMapper.selectById(bo.getDeptId());
            copLevelDto.setRepDept(sysDept != null ? sysDept.getDeptName() : null);
        }
        copLevelDto.setRepDate(bo.getRepDate());
        copLevelDto.setRepReason(getTarNote(bo));
        copLevelDto.setEnterpriseIdNo(String.valueOf(bo.getCopId()));

        //其他问题
        copLevelDto.setProblem(bo.getProblem());
        //监控措施
        copLevelDto.setCopoversee(bo.getCopoversee());
        //建议措施
        copLevelDto.setCopadvice(bo.getCopadvice());
        //采取措施
        copLevelDto.setCopdeal(bo.getCopdeal());
        //企业现状
        copLevelDto.setCopresult(bo.getCopresult());
        //后续工作
        copLevelDto.setCopafterwork(bo.getCopafterwork());
        //请求协助
        copLevelDto.setCophelp(bo.getCophelp());


        if (tCopInformation.getGeom() != null) {
            copLevelDto.setGeom(tCopInformation.getGeom());
        }
        //删除企业风险相关数据表
        deleteCopLevelDataByCopId(bo.getCopId());

        if (Level.PROBLEM.getCode().equals(bo.getLevel())) {
            copLevelDto.setEnterpriseType(Level.PROBLEM.getName());
            problemEnterprisesMapper.insert(BeanUtil.toBean(copLevelDto, ProblemEnterprises.class));
        }
        if (Level.RISK.getCode().equals(bo.getLevel())) {
            copLevelDto.setEnterpriseType(Level.RISK.getName());
            riskEnterprisesMapper.insert(BeanUtil.toBean(copLevelDto, RiskEnterprises.class));
        }
        if (Level.DANGER.getCode().equals(bo.getLevel())) {
            copLevelDto.setEnterpriseType(Level.DANGER.getName());
            dangerousEnterprisesMapper.insert(BeanUtil.toBean(copLevelDto, DangerousEnterprises.class));
        }
        if (Level.HIGH_DANGER.getCode().equals(bo.getLevel())) {
            copLevelDto.setEnterpriseType(Level.HIGH_DANGER.getName());
            highRiskEnterprisesMapper.insert(BeanUtil.toBean(copLevelDto, HighRiskEnterprises.class));
        }
    }

    //删除企业风险相关数据表
    @Override
    public void deleteCopLevelData() {
        //删除表全部数据
        problemEnterprisesMapper.delete(Wrappers.emptyWrapper());
        riskEnterprisesMapper.delete(Wrappers.emptyWrapper());
        dangerousEnterprisesMapper.delete(Wrappers.emptyWrapper());
        highRiskEnterprisesMapper.delete(Wrappers.emptyWrapper());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteCopLevelDataByCopId(Long copId) {
        problemEnterprisesMapper.delete(Wrappers.<ProblemEnterprises>lambdaQuery().eq(ProblemEnterprises::getEnterpriseId, copId));
        riskEnterprisesMapper.delete(Wrappers.<RiskEnterprises>lambdaQuery().eq(RiskEnterprises::getEnterpriseId, copId));
        dangerousEnterprisesMapper.delete(Wrappers.<DangerousEnterprises>lambdaQuery().eq(DangerousEnterprises::getEnterpriseId, copId));
        highRiskEnterprisesMapper.delete(Wrappers.<HighRiskEnterprises>lambdaQuery().eq(HighRiskEnterprises::getEnterpriseId, copId));
    }

    /**
     * 修改部门上报，根据指标动态生成部门上报大报，然后上报时同时插入，大报用来查询，本作数据根备份
     */
    @Override
    public Boolean updateByBo(TRepReportBo bo) {
        TRepReport update = BeanUtil.toBean(bo, TRepReport.class);
        //已审核的不能修改
        TRepReport tRepReport = baseMapper.selectById(bo.getRepReportId());
        //查询最近上报一次的数据
        /*List<TRepReport> repReports = baseMapper.selectList(Wrappers.<TRepReport>lambdaQuery()
            .eq(TRepReport::getCopId, update.getCopId())
            .ne(TRepReport::getAuditing, AuditingStatus.RETURNED.getCode())
            .orderByDesc(TRepReport::getCreateTime)
            .apply("TO_CHAR(rep_date, 'YYYY-MM') =  {0}", DateUtil.format(DateUtil.beginOfMonth(update.getRepDate()), "yyyy-MM")));

        //风险等级判定
        complateLevel(repReports, update);*/
        if (AuditingStatus.AUDITED.getCode().equals(tRepReport.getAuditing())) {
            throw new ServiceException("已审核，不能修改");
        }
        update.setNote(getTarNote(update));
        return baseMapper.updateById(update) > 0;
    }

    @Override
    public Boolean copExistBigMap(Long copId) {
        return problemEnterprisesMapper.exists(Wrappers.<ProblemEnterprises>lambdaQuery().eq(ProblemEnterprises::getEnterpriseId, copId))
            || riskEnterprisesMapper.exists(Wrappers.<RiskEnterprises>lambdaQuery().eq(RiskEnterprises::getEnterpriseId, copId))
            || dangerousEnterprisesMapper.exists(Wrappers.<DangerousEnterprises>lambdaQuery().eq(DangerousEnterprises::getEnterpriseId, copId))
            || highRiskEnterprisesMapper.exists(Wrappers.<HighRiskEnterprises>lambdaQuery().eq(HighRiskEnterprises::getEnterpriseId, copId));
    }

    @Override
    public TRepGarrepInfoVo queryRepHistory(Long copId, String repDate) {
        TCopInformationVo tCopInformationVo = tCopInformationMapper.selectVoById(copId);
        List<TRepReportVo> repReportVos = baseMapper.selectVoList(Wrappers.<TRepReport>lambdaQuery()
            .eq(TRepReport::getCopId, copId)
            .apply("to_char(rep_date, 'yyyy-MM') = {0}", repDate)
            .orderByDesc(TRepReport::getCreateTime)
        );
        for (TRepReportVo repReportVo : repReportVos) {
            List<TRepReportProPayVo> tRepReportProPayVos = tRepReportPropayMapper.selectVoList(Wrappers.<TRepReportPropay>lambdaQuery()
                .eq(TRepReportPropay::getRepReportId, repReportVo.getRepReportId()).orderByDesc(TRepReportPropay::getCreateTime));
            repReportVo.setProPayList(tRepReportProPayVos);
        }
//        List<TRepReportVo> repReportVos = repGarrepMapper.selectRepReportList(copId, repDate);
        return TRepGarrepInfoVo.builder().copInformation(tCopInformationVo).repReport(repReportVos).build();
    }

    @Override
    public List<String> queryRepDateRange(Long copId) {
        List<TRepReport> tRepReports = baseMapper.selectList(Wrappers.<TRepReport>lambdaQuery().select(TRepReport::getRepDate)
            .eq(TRepReport::getCopId, copId)
            .eq(TRepReport::getAuditing, AuditingStatus.AUDITED.getCode())
            .orderByDesc(TRepReport::getRepDate));
        return tRepReports.stream().map(a -> DateUtil.format(a.getRepDate(), DateUtils.YYYY_MM)).distinct().collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean handleFinishReport(Long repReportId) {
        TRepReport tRepReport = baseMapper.selectById(repReportId);
        if (!tRepReport.getAuditing().equals(AuditingStatus.AUDITED.getCode())) {
            throw new ServiceException("未审核，不能处理");
        }
        if (tRepReport.getFinishStatus().equals(FinishStatus.FINISHED.getCode())) {
            throw new ServiceException("已处理，不能重复处理");
        }
        tRepReport.setFinishStatus(FinishStatus.FINISHED.getCode());
        boolean flag = baseMapper.updateById(tRepReport) > 0;

        if (flag) {
            //查询该企业本月是否存在多个部门上报
            String repDate = DateUtil.format(DateUtil.beginOfMonth(tRepReport.getRepDate()), DateUtils.YYYY_MM);
            List<TRepReport> tRepReports = baseMapper.selectList(Wrappers.<TRepReport>lambdaQuery()
                .eq(TRepReport::getCopId, tRepReport.getCopId())
                .ne(TRepReport::getDeptId, tRepReport.getDeptId())
                .apply("TO_CHAR(rep_date, 'yyyy-MM') = {0}", repDate));

            if (tRepReports.stream().allMatch(a -> a.getFinishStatus().equals(FinishStatus.FINISHED.getCode()))) {
                //throw new ServiceException("该企业本月存在多个部门上报，请联系管理员进行风险降级");
                //删除大屏数据
                deleteCopLevelDataByCopId(tRepReport.getCopId());
            }

        }

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean importData(List<TRepReportExcel> tRepReportExcels) {

        for (TRepReportExcel repReportExcel : tRepReportExcels) {

            TRepReport copy = BeanCopyUtils.copy(repReportExcel, TRepReport.class);
            if (copy != null) {
                copy.setLevel(Level.getCodeByName(repReportExcel.getCopLevel()));
                //上报日期
                copy.setRepDate(DateUtil.parse(DateUtils.getDate(), DateUtils.YYYY_MM_DD));

                //查询企业ID
                TCopInformation tCopInformation = tCopInformationMapper.selectOne(Wrappers.<TCopInformation>lambdaQuery().eq(TCopInformation::getCopName, repReportExcel.getCopName()));
                if (tCopInformation != null) {
                    copy.setCopId(tCopInformation.getCopId());
                }
            }
            insertByBo(BeanUtil.toBean(copy, TRepReportBo.class));
        }
        return true;
    }

    @Override
    @SneakyThrows
    public Map<String, Object> importExcelFile(MultipartFile file) {
        Map<String, Object> result = new LinkedHashMap<>();
        List<JSONObject> jsonObjects = ExcelUtil.dr(1, file.getInputStream(), new TypeReference<JSONObject>() {
        });
        List<Map<String, String>> columns = new ArrayList<>();
        List<TRepReportExcel> successList = new ArrayList<>();
        List<TRepReportExcel> errorList = new ArrayList<>();
        for (JSONObject jsonObject : jsonObjects) {
            Map<String, String> entity = new LinkedHashMap<>();

            AtomicBoolean flag = new AtomicBoolean(true);

            AtomicInteger i = new AtomicInteger();

            jsonObject.forEach((k, v) -> {
                //反射获取字段
                Field[] declaredFields = TRepReportExcel.class.getDeclaredFields();

                for (Field declaredField : declaredFields) {
                    declaredField.setAccessible(true);

                    if (i.get() == 0) {
                        Map<String, String> column = new LinkedHashMap<>();
                        column.put("code", declaredField.getName());
                        column.put("name", declaredField.getAnnotation(ExcelProperty.class).value()[0]);
                        columns.add(column);
                    }


                    //字段名称相等
                    if (declaredField.getAnnotation(ExcelProperty.class).value()[0].equals(k)) {

                        if ("企业名称".equals(k) && "A".equals(v.toString())) {
                            entity.put(declaredField.getName(), String.format(HTML, "企业名称不能为空"));
                            flag.set(false);
                            return;
                        }
                        //判断企业是否存在
                        if ("企业名称".equals(k) && !tCopInformationMapper.exists(Wrappers.<TCopInformation>lambdaQuery().eq(TCopInformation::getCopName, v.toString()))) {
                            entity.put(declaredField.getName(), String.format(HTML, "企业不存在"));
                            flag.set(false);
                            return;
                        }
                        if ("企业风险评级".equals(k) && StrUtil.isBlank(v.toString())) {
                            entity.put(declaredField.getName(), String.format(HTML, "企业风险评级不能为空"));
                            flag.set(false);
                            return;
                        }
                        //判断风险级别是否存在
                        if ("企业风险评级".equals(k) && !Level.isExist(v.toString())) {
                            entity.put(declaredField.getName(), String.format(HTML, "企业风险评级不正确"));
                            flag.set(false);
                            return;
                        }

                        entity.put(declaredField.getName(), v.toString());
                    }
                }
                i.getAndIncrement();
            });
            TRepReportExcel bean = BeanUtil.mapToBean(entity, TRepReportExcel.class, true);

            if (flag.get()) {
                successList.add(bean);
            } else {
                errorList.add(bean);
            }

        }
        result.put("successList", successList);
        result.put("errorList", errorList);
        //设置列
        result.put("columns", columns);
        System.out.println(result);
        return result;
    }

    @Override
    @SneakyThrows
    public void buildTempLate(HttpServletResponse response) {
        //使用LinkedHashMap进行表头字段映射
        LinkedHashMap<String, DynamicExcelData> nameMap = new LinkedHashMap<>();
        nameMap.put("copName", new DynamicExcelData("企业名称", ""));
        nameMap.put("levelName", new DynamicExcelData("企业风险评级", ""));
        List<DeptTargetDto> deptTargetDtos = queryTarget();
        for (int i = 0; i < deptTargetDtos.size(); i++) {
            nameMap.put(deptTargetDtos.get(i).getCode(), new DynamicExcelData(deptTargetDtos.get(i).getName(), ""));
        }
        nameMap.put("problem", new DynamicExcelData("其他问题", ""));
        nameMap.put("copoversee", new DynamicExcelData("监管措施", ""));
        nameMap.put("copadvice", new DynamicExcelData("建议措施", ""));

        ExcelUtils.dynamicExport(response, nameMap, new ArrayList<>(), "部门上报模板");
    }

    @Override
    public boolean dataFormat(String year, String level) {
        List<CopLevelDto> copLevelDtos = baseMapper.dataFormat(year, level);
        for (CopLevelDto copLevelDto : copLevelDtos) {
            if (level.equals(Level.PROBLEM.getName())) {
                ProblemEnterprises problemEnterprises = problemEnterprisesMapper.selectById(copLevelDto);
                if (problemEnterprises == null) {
                    copLevelDto.setEnterpriseIdNo(String.valueOf(copLevelDto.getEnterpriseId()));
                }
            }
        }
        return false;
    }

    @Override
    public TRepGarrepMapVo queryFirstByCopId(Long copId, String yearMonth) {
        //+1月
        yearMonth = DateUtil.format(DateUtil.offsetMonth(DateUtil.parse(yearMonth, "yyyy-MM"), 1), "yyyy-MM");
        List<TRepGarrepVo> tRepGarrepVos = repGarrepMapper.selectVoList(Wrappers.<TRepGarrep>lambdaQuery().eq(TRepGarrep::getCopId, copId)
            .apply("TO_CHAR(rep_date, 'YYYY-MM') =  {0}", yearMonth)
            .orderByDesc(TRepGarrep::getCreateTime));
        //查询最新的一条
        TRepGarrepVo tRepReportVo = null;
        Optional<TRepGarrepVo> first = tRepGarrepVos.stream().findFirst();
        if (first.isPresent()) {
            tRepReportVo = first.get();
        }
        //查询企业信息
        if (ObjectUtil.isNotNull(tRepReportVo)) {
            TCopInformationVo TCopInformationVo = tCopInformationMapper.selectVoById(tRepReportVo.getCopId());
            return TRepGarrepMapVo.builder().repReport(tRepReportVo).copInformation(TCopInformationVo).build();
        }
        return null;
    }

    @Override
    @Transactional
    public boolean point(Long copId) {
        TCopInformation tCopInformation = tCopInformationMapper.selectById(copId);
        double[] doubles = CoordinateTransformUtil.bd09towgs84(Double.parseDouble(tCopInformation.getLon()), Double.parseDouble(tCopInformation.getLat()));
        double x = doubles[0];
        double y = doubles[1];
        tCopInformation.setLon(String.valueOf(x));
        tCopInformation.setLat(String.valueOf(y));
        tCopInformation.setGeom(new Point(x, y));
        return tCopInformationMapper.updateById(tCopInformation) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean point() {
        List<TCopInformation> tCopInformations = tCopInformationMapper.selectList();
        for (TCopInformation tCopInformation : tCopInformations) {
            if (tCopInformation.getGeom() == null) {
                if (StrUtil.isNotBlank(tCopInformation.getLon()) && StrUtil.isNotBlank(tCopInformation.getLat())) {
                    double[] doubles = CoordinateTransformUtil.bd09towgs84(Double.parseDouble(tCopInformation.getLon()), Double.parseDouble(tCopInformation.getLat()));
                    double x = doubles[0];
                    double y = doubles[1];
                    tCopInformation.setLon(String.valueOf(x));
                    tCopInformation.setLat(String.valueOf(y));
                    tCopInformation.setGeom(new Point(x, y));
                    System.out.println(tCopInformation.getGeom());
                    System.out.println(tCopInformation.getLon());
                    System.out.println(tCopInformation.getLat());
                }
            } else {
                if (StrUtil.isNotBlank(tCopInformation.getLon()) && StrUtil.isNotBlank(tCopInformation.getLat())) {
                    double[] doubles = CoordinateTransformUtil.bd09towgs84(Double.parseDouble(tCopInformation.getLon()), Double.parseDouble(tCopInformation.getLat()));
                    double x = doubles[0];
                    double y = doubles[1];
                    tCopInformation.setLon(String.valueOf(x));
                    tCopInformation.setLat(String.valueOf(y));
                }
            }
        }
        return tCopInformationMapper.updateBatchById(tCopInformations);
    }

    @Override
    @Transactional
    public Boolean resultAudit(ReportResultAuditBo bo) {
        List<TRepReport> tRepReports = baseMapper.selectList(Wrappers.<TRepReport>lambdaQuery().in(TRepReport::getRepReportId, Arrays.asList(bo.getRepReportIds())));

        for (TRepReport tRepReport : tRepReports) {
            //处理结果未审核或退回的才能审核
            if (!(ResultAuditingStatus.UNAUDITED.getCode().equals(tRepReport.getResultAuditing()) || ResultAuditingStatus.RETURNED.getCode().equals(tRepReport.getResultAuditing()))) {
                throw new ServiceException("未审核或者处理结果退回的才能审核");
            }
            //设置审核意见
            tRepReport.setResultAuditingComment(bo.getResultAuditingComment());
            //设置已审核
            tRepReport.setResultAuditing(bo.getResultAuditing());
            if (ResultAuditingStatus.AUDITED.getCode().equals(bo.getResultAuditing())) {
                //处理结果审核通过后设置,已处理
                tRepReport.setDespacho(DespachoStatus.DEAL.getCode());
            } else {
                //处理结果审核不通过后设置,未处理
                tRepReport.setDespacho(DespachoStatus.UNDEAL.getCode());
            }
            boolean flag = baseMapper.updateById(tRepReport) > 0;

            if (flag && (ResultAuditingStatus.AUDITED.getCode().equals(bo.getResultAuditing()))) {
                //合并企业风险等级数据
                mergeCopHandleResultData(tRepReport);
            }
        }

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean noException() {
        Date date = DateUtil.beginOfMonth(new Date());

        //新增本月无异常上报
        TRepNoproblem tRepNoproblem = repNoproblemMapper.selectOne(Wrappers.<TRepNoproblem>lambdaQuery()
            .eq(TRepNoproblem::getDeptId, LoginHelper.getDeptId())
            .eq(TRepNoproblem::getRepDate, date));

        if (ObjectUtil.isNull(tRepNoproblem)) {
            tRepNoproblem = new TRepNoproblem();
            tRepNoproblem.setDeptId(LoginHelper.getDeptId());
            tRepNoproblem.setAuditing(AuditingStatus.UNAUDITED.getCode());
            tRepNoproblem.setRepDate(date);
            tRepNoproblem.setUserId(LoginHelper.getUserId());
            repNoproblemMapper.insert(tRepNoproblem);
        } else {
            if (!tRepNoproblem.getAuditing().equals(AuditingStatus.RETURNED.getCode())) {
                throw new ServiceException("本月已上报，不能重复上报");
            } else {
                tRepNoproblem.setAuditing(AuditingStatus.UNAUDITED.getCode());
                repNoproblemMapper.updateById(tRepNoproblem);
            }
        }

        return true;
    }

    @Override
    public List<YearReportVo> queryYearReport(Integer startYear, Integer endYear) {
        int start = startYear == null ? DateUtil.year(new Date()) - 10 : startYear;
        int end = endYear == null ? DateUtil.year(new Date()) : endYear;
        return baseMapper.queryYearReport(start, end);
    }

    @Override
    public List<QuarterReportVo> queryQuarterReport(Integer year) {
        List<QuarterReportVo> quarterReports = baseMapper.queryQuarterReport(year == null ? DateUtil.year(new Date()) : year);


        for (int i = 0; i < quarterReports.size(); i++) {

            //查询下一个月1月份数据
            int thisYear = quarterReports.get(i).getYear();

            //手动查询
            Field[] declaredFields = QuarterReportVo.class.getDeclaredFields();
            for (Field declaredField : declaredFields) {
                declaredField.setAccessible(true);

                if ("q1".equals(declaredField.getName())) {
                    List<String> months = Arrays.asList("02", "03", "04");
                    Long count = 0L;
                    for (String month : months) {
                        count += repGarrepMapper.selectCount(Wrappers.<TRepGarrep>lambdaQuery()
                            .apply("TO_CHAR(rep_date, 'yyyy-MM') = {0}", thisYear + "-" + month));
                    }
                    //重新计算数量
                    quarterReports.get(i).setQ1(Math.toIntExact(count));
                }

                if ("q2".equals(declaredField.getName())) {
                    List<String> months = Arrays.asList("05", "06", "07");
                    Long count = 0L;
                    for (String month : months) {
                        count += repGarrepMapper.selectCount(Wrappers.<TRepGarrep>lambdaQuery()
                            .apply("TO_CHAR(rep_date, 'yyyy-MM') = {0}", thisYear + "-" + month));
                    }
                    //重新计算数量
                    quarterReports.get(i).setQ2(Math.toIntExact(count));
                }

                if ("q3".equals(declaredField.getName())) {
                    List<String> months = Arrays.asList("08", "09", "10");
                    Long count = 0L;
                    for (String month : months) {
                        count += repGarrepMapper.selectCount(Wrappers.<TRepGarrep>lambdaQuery()
                            .apply("TO_CHAR(rep_date, 'yyyy-MM') = {0}", thisYear + "-" + month));
                    }
                    //重新计算数量
                    quarterReports.get(i).setQ3(Math.toIntExact(count));
                }

                if ("q4".equals(declaredField.getName())) {
                    List<String> months = Arrays.asList("11", "12");
                    Long count = 0L;
                    for (String month : months) {
                        count += repGarrepMapper.selectCount(Wrappers.<TRepGarrep>lambdaQuery()
                            .apply("TO_CHAR(rep_date, 'yyyy-MM') = {0}", thisYear + "-" + month));
                    }
                    count += repGarrepMapper.selectCount(Wrappers.<TRepGarrep>lambdaQuery()
                        .apply("TO_CHAR(rep_date, 'yyyy-MM') = {0}", thisYear + 1 + "-" + "01"));
                    //重新计算数量
                    quarterReports.get(i).setQ4(Math.toIntExact(count));
                }
            }

            quarterReports.get(i).setTotal(
                quarterReports.get(i).getQ1()
                    + quarterReports.get(i).getQ2()
                    + quarterReports.get(i).getQ3()
                    + quarterReports.get(i).getQ4()
            );

        }
        return quarterReports;
    }

    @Override
    public List<MonthReportVo> queryMonthReport(Integer year) {
        List<MonthReportVo> monthReports = baseMapper.queryMonthReport(year == null ? DateUtil.year(new Date()) : year);
        for (int i = 0; i < monthReports.size(); i++) {

            //对12月份进行特殊处理
            Field[] declaredFields = MonthReportVo.class.getDeclaredFields();
            for (Field declaredField : declaredFields) {
                declaredField.setAccessible(true);
                if ("december".equals(declaredField.getName())) {
                    //查询下一个月1月份数据
                    int thisYear = monthReports.get(i).getYear();
                    if (DateUtil.year(new Date()) == year) {
                        thisYear = DateUtil.year(new Date()) - 1;
                    }
                    if ((year - DateUtil.year(new Date()) > 0)) {
                        thisYear = year - 2;
                    }
                    Long count = repGarrepMapper.selectCount(Wrappers.<TRepGarrep>lambdaQuery()
                        .apply("TO_CHAR(rep_date, 'yyyy-MM') = {0}", thisYear + 1 + "-01"));
                    //重新计算数量
                    monthReports.get(i).setDecember(Math.toIntExact(count));
                    if (Objects.equals(monthReports.get(i).getYear(), year)) {
                        monthReports.get(i).setDecember(0);
                    }
                }
            }

            monthReports.get(i).setTotal(
                monthReports.get(i).getJanuary()
                    + monthReports.get(i).getFebruary()
                    + monthReports.get(i).getMarch()
                    + monthReports.get(i).getApril()
                    + monthReports.get(i).getMay()
                    + monthReports.get(i).getJune()
                    + monthReports.get(i).getJuly()
                    + monthReports.get(i).getAugust()
                    + monthReports.get(i).getSeptember()
                    + monthReports.get(i).getOctober()
                    + monthReports.get(i).getNovember()
                    + monthReports.get(i).getDecember()
            );


        }
        return monthReports;
    }

    @Override
    public Boolean auditBack(Long repReportId) {
        TRepReport tRepReport = baseMapper.selectById(repReportId);
        // 未审核的不能退回
        if (!AuditingStatus.AUDITED.getCode().equals(tRepReport.getAuditing())) {
            throw new ServiceException("未审核，不能退回");
        }
        //只有未处理的才能退
        if (!ResultAuditingStatus.UNHANDLED.getCode().equals(tRepReport.getResultAuditing())) {
            throw new ServiceException("已处理，不能退回");
        }
        //设置审核意见
        tRepReport.setAuditingComment(null);
        // 设置审核退回
        tRepReport.setAuditing(AuditingStatus.UNAUDITED.getCode());
        return baseMapper.updateById(tRepReport) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean audit(ReportAuditBo bo) {
        List<TRepReport> tRepReports = baseMapper.selectList(Wrappers.<TRepReport>lambdaQuery().in(TRepReport::getRepReportId, Arrays.asList(bo.getRepReportIds())));
        for (TRepReport tRepReport : tRepReports) {

            if (AuditingStatus.AUDITED.getCode().equals(tRepReport.getAuditing())) {
                throw new ServiceException("已审核，不能重复审核");
            }
            //设置审核意见
            tRepReport.setAuditingComment(bo.getAuditingComment());
            //设置已审核
            tRepReport.setAuditing(bo.getAuditing());
            //设置未处理
            tRepReport.setResultAuditing(ResultAuditingStatus.UNHANDLED.getCode());

            boolean flag = baseMapper.updateById(tRepReport) > 0;

            if (flag && AuditingStatus.AUDITED.getCode().equals(bo.getAuditing())) {
                //合并企业风险等级数据，只合并处理结果部分

                mergeCopLevelData(tRepReport);

                //根据上报的风险等级进行扣分
                itCopScoreRecordService.deductionScore(tRepReport.getCopId(), tRepReport.getLevel(), tRepReport.getRepReportId());
            }
        }
        return true;
    }

    @Transactional
    public void mergeCopHandleResultData(TRepReport tRepReport) {

        //查询月报表数据
        TRepGarrep tRepGarrep = repGarrepMapper.selectOne(Wrappers.<TRepGarrep>lambdaQuery()
            .eq(TRepGarrep::getCopId, tRepReport.getCopId())
            .apply("TO_CHAR(rep_date, 'YYYY-MM') =  {0}", DateUtil.format(DateUtil.beginOfMonth(DateUtil.offsetMonth(tRepReport.getRepDate(), 1)), "yyyy-MM")));

        if (tRepGarrep != null) {
            //合并采取措施,如果有值则拼接，没有则直接赋值
            if (StringUtils.isNotBlank(tRepGarrep.getCopdeal())) {
                tRepGarrep.setCopdeal(tRepGarrep.getCopdeal() + tRepReport.getCopdeal() + ";");
            } else {
                if (StringUtils.isNotBlank(tRepReport.getCopdeal())) {
                    tRepGarrep.setCopdeal(tRepReport.getCopdeal() + ";");
                }
            }
            //合并企业现状
            if (StringUtils.isNotBlank(tRepGarrep.getCopresult())) {
                tRepGarrep.setCopresult(tRepGarrep.getCopresult() + tRepReport.getCopresult() + ";");
            } else {
                if (StringUtils.isNotBlank(tRepReport.getCopresult())) {
                    tRepGarrep.setCopresult(tRepReport.getCopresult() + ";");
                }
            }
            //合并后续工作
            if (StringUtils.isNotBlank(tRepGarrep.getCopafterwork())) {
                tRepGarrep.setCopafterwork(tRepGarrep.getCopafterwork() + tRepReport.getCopafterwork() + ";");
            } else {
                if (StringUtils.isNotBlank(tRepReport.getCopafterwork())) {
                    tRepGarrep.setCopafterwork(tRepReport.getCopafterwork() + ";");
                }
            }
            //合并请求协助
            if (StringUtils.isNotBlank(tRepGarrep.getCophelp())) {
                tRepGarrep.setCophelp(tRepGarrep.getCophelp() + tRepReport.getCophelp() + ";");
            } else {
                if (StringUtils.isNotBlank(tRepReport.getCophelp())) {
                    tRepGarrep.setCophelp(tRepReport.getCophelp() + ";");
                }
            }
            repGarrepMapper.updateById(tRepGarrep);
            //更新大屏
            updateBigMapCopLevelData(BeanUtil.toBean(tRepGarrep, TRepReport.class));
        }
    }

    @Override
    public List<DeptTargetDto> queryTarget() {
        List<DeptTargetDto> targets = new ArrayList<>();
        List<TRepTarget> tRepTargets = repTargetMapper.selectListByDeptId(LoginHelper.getDeptId());

        Map<String, String> monthTargetsMap = new HashMap<>();
        monthTargetsMap.put("propaymonth", "拖欠工资（月）");
        monthTargetsMap.put("protaxmonth", "拖欠税款（月）");
        monthTargetsMap.put("proinsuremonth", "拖欠社保（月）");
        monthTargetsMap.put("prowatermonth", "拖欠水费（月）");
        monthTargetsMap.put("proenergymonth", "拖欠电费（月）");
        monthTargetsMap.put("prorentmonth", "拖欠租金（月）");

        for (TRepTarget tRepTarget : tRepTargets) {
            String code = tRepTarget.getTarCode().toLowerCase();

            if (monthTargetsMap.containsKey(code)) {
                DeptTargetDto negativeDto = new DeptTargetDto();
                negativeDto.setCode(code + "s");
                negativeDto.setName(monthTargetsMap.get(code));
                targets.add(negativeDto);
            }

            DeptTargetDto dto = new DeptTargetDto();
            dto.setCode(code);
            dto.setName(tRepTarget.getTarName());
            targets.add(dto);
        }
        return targets;
    }

    @Override
    @Transactional
    public Boolean handleReport(HandDeptReportBo bo, Long repReportId) {
        TRepReport tRepReport = baseMapper.selectById(repReportId);
        //审核通过的才能进行处理上报
        if (!AuditingStatus.AUDITED.getCode().equals(tRepReport.getAuditing())) {
            throw new ServiceException("未审核，不能处理上报");
        }
        TRepReport copy = BeanCopyUtils.copy(bo, tRepReport);
        //设置已处理未审核
        if (copy != null) {
            copy.setResultAuditing(ResultAuditingStatus.UNAUDITED.getCode());
        }
        boolean flag = baseMapper.updateById(copy) > 0;

        if (flag) {
            tRepReportPropayMapper.delete(Wrappers.<TRepReportPropay>lambdaQuery().eq(TRepReportPropay::getRepReportId, repReportId));

            if (CollectionUtils.isNotEmpty(bo.getProPayList())) {
                for (TRepReportPropay tRepReportPropay : bo.getProPayList()) {
                    tRepReportPropay.setRepReportId(repReportId);
                }
                tRepReportPropayMapper.insertBatch(bo.getProPayList());
            }
        }
        return flag;
    }

    /**
     * 批量删除部门上报
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            for (TRepReport tRepReport : baseMapper.selectBatchIds(ids)) {
                if (AuditingStatus.AUDITED.getCode().equals(tRepReport.getAuditing())) {
                    throw new ServiceException("已审核，不能删除");
                }
            }
            //删除扣分记录
            itCopScoreRecordService.deleteByRepReportIds(ids);

        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
