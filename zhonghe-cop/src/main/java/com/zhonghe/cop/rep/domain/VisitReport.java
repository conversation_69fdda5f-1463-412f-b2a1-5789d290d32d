package com.zhonghe.cop.rep.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zhonghe.common.core.domain.BaseEntity;
import lombok.Data;

import java.util.Date;

/**
 * 信访上报表
 *
 * @TableName t_visit_report
 */
@Data
@TableName(value = "cop.t_visit_report")
public class VisitReport extends BaseEntity {
    private static final long serialVersionUID = -4130514613208635785L;
    /**
     * 信访上报ID
     */
    @TableId
    private Long visitReportId;

    /**
     * 投诉人姓名
     */
    private String cptName;

    /**
     * 投诉人性别
     */
    private String cptGender;

    /**
     * 投诉人电话
     */
    private String cptPhone;

    /**
     * 投诉人身份证
     */
    private String cptIdCard;

    /**
     * 投诉人地址
     */
    private String cptAddress;

    /**
     * 被投诉人单位
     */
    private String rspUnit;

    /**
     * 被投诉人单位人数
     */
    private Integer rspUnitPopulation;

    /**
     * 被投诉人单位地址
     */
    private String rspUnitAddress;

    /**
     * 被投诉人单位统一社会信用代码
     */
    private String rspUnitCreditCode;

    /**
     * 被投诉人单位负责人
     */
    private String rspUnitIncharge;

    /**
     * 被投诉人单位电话
     */
    private String rspUnitPhone;

    /**
     * 诉求内容选择
     */
    private String appealContentJson;

    /**
     * 诉求内容文本
     */
    private String appealContent;

    /**
     * 具体内容和投诉请求
     */
    private String cptContent;

    /**
     * 证据材料身份证复印份数
     */
    private Integer evidenceIdCardCopyNum;

    /**
     * 其他证据
     */
    private String evidenceOther;

    /**
     * 承办人意见
     */
    private String handlerOpinion;

    /**
     * 法律援助地址
     */
    private String legalAddress;

    /**
     * 法律援助签名
     */
    private String legalSignature;

    /**
     * 法律援助日期
     */
    private Date legalDate;

    /**
     * 来访人签名
     */
    private String visitorSignature;

    /**
     * 来访日期
     */
    private Date visitorDate;

    /**
     * 劳动保障监察员签名
     */
    private String laborSignature;

    /**
     * 劳动保障监察员日期
     */
    private Date laborDate;

    /**
     * 登记日期
     */
    private Date registerDate;
    /**
     * 录入日期
     */
    private Date inputDate;

    /**
     * 信息核对状态 0-未核对 1-已核对
     */
    private Integer verifyStatus;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    @TableLogic
    private String delFlag;


    /**
     * 处理结果
     */
    private String handleResult;

    /**
     * 源文件ID
     */
    private Long sourceFileOssId;

}
