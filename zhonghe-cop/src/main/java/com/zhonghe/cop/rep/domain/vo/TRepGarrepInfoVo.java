package com.zhonghe.cop.rep.domain.vo;

import com.zhonghe.cop.bam.domain.vo.TCopInformationVo;
import io.swagger.annotations.ApiModel;
import lombok.Builder;
import lombok.Data;

import java.util.List;


/**
 * 汇总报，指标字段根据动态添加视图对象 t_rep_garrep
 *
 * <AUTHOR>
 * @date 2023-12-06
 */
@Builder
@Data
@ApiModel("汇总报表信息对象")
public class TRepGarrepInfoVo {

    private static final long serialVersionUID = 1L;

    /**
     * 企业信息
     */
    private TCopInformationVo copInformation;

    /**
     * 汇总报表信息
     */
    private List<TRepReportVo> repReport;

}
