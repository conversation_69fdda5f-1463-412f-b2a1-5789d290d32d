package com.zhonghe.cop.rep.domain.bo;

import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;
import java.io.Serializable;


/**
 * 汇总报，指标字段根据动态添加业务对象 t_rep_garrep
 *
 * <AUTHOR>
 * @date 2023-12-06
 */

@Data
@ApiModel("报表上报业务对象")
public class TRepGarrepBo implements Serializable {

    private static final long serialVersionUID = 1569118841566072056L;
    /**
     * 汇总报表ID
     */
    @ApiModelProperty(value = "汇总报表ID", required = true)
    @NotNull(message = "汇总报表ID不能为空", groups = {EditGroup.class})
    private Long repGarrepId;

    /**
     * 风险等级（默认0，0——正常、1——问题、2——风险险、3——危险、4——高危）
     */
    @ApiModelProperty(value = "最终风险等级（默认0，0——正常、1——问题、2——风险险、3——危险、4——高危）", required = true)
    @NotNull(message = "最终风险等级（默认0，0——正常、1——问题、2——风险险、3——危险、4——高危）不能为空", groups = {AddGroup.class, EditGroup.class})
    private Integer level;

    /**
     * 其他问题
     */
    @ApiModelProperty(value = "其他问题")
    @Length(max = 4000, message = "其他问题长度不能超过4000")
    private String problem;

    /**
     * 监管措施
     */
    @ApiModelProperty(value = "监管措施")
    @Length(max = 4000, message = "监管措施长度不能超过4000")
    private String copoversee;

    /**
     * 建议措施
     */
    @ApiModelProperty(value = "建议措施")
    @Length(max = 4000, message = "建议措施长度不能超过4000")
    private String copadvice;

}
