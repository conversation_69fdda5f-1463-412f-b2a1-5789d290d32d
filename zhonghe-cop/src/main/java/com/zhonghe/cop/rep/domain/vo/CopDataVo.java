package com.zhonghe.cop.rep.domain.vo;

import com.zhonghe.cop.other.domain.vo.BmqzjProWoFormPg0vsVo;
import com.zhonghe.cop.other.domain.vo.TDgsnSpecialTaskQsVo;
import com.zhonghe.cop.other.domain.vo.TIntegrationClueVo;
import com.zhonghe.cop.other.domain.vo.TTaskOrderNewVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: lpg
 * @Date: 2025/07/14
 * @Description:
 */
@Data
public class CopDataVo {
    /**
     * 12345热线
     */
    @ApiModelProperty("12345热线")
    private List<BmqzjProWoFormPg0vsVo> bmqzjProWoFormList;

    private List<TIntegrationClueVo> integrationClueList;
    /**
     * 专项任务
     */
    @ApiModelProperty("专项任务")
    private List<TDgsnSpecialTaskQsVo> dgsnSpecialTaskQsList;
    /**
     * 巡查工单
     */
    @ApiModelProperty("巡查工单")
    private List<TTaskOrderNewVo> taskOrderNewList;
}
