package com.zhonghe.cop.rep.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: lpg
 * @Date: 2024/03/07
 * @Description: 对外提供的月报汇总数据
 */
@Data
public class OpenMonthReport {
    /**
     * 上报日期
     */
    @JsonFormat(pattern = "yyyy-MM")
    private Date repDate;
    /**
     * 拖欠工资月数（月）
     */
    @JsonProperty("PROPAYMONTH")
    private BigDecimal propaymonth;

    /**
     * 拖欠工资涉及人数（人）
     */
    @JsonProperty("PROPAYPOPULATION")
    private BigDecimal propaypopulation;

    /**
     * 拖欠工资金额（元）
     */
    @JsonProperty("PROPAYMONEY")
    private BigDecimal propaymoney;

    /**
     * 拖欠税款月数（月）
     */
    @JsonProperty("PROTAXMONTH")
    private BigDecimal protaxmonth;

    /**
     * 拖欠税款金额（元）
     */
    @JsonProperty("PROTAXMONEY")
    private BigDecimal protaxmoney;

    /**
     * 拖欠社保月数（月）
     */
    @JsonProperty("PROINSUREMONTH")
    private BigDecimal proinsuremonth;

    /**
     * 拖欠社保金额（元）
     */
    @JsonProperty("PROINSUREMONEY")
    private BigDecimal proinsuremoney;

    /**
     * 拖欠水费月数（月）
     */
    @JsonProperty("PROWATERMONTH")
    private BigDecimal prowatermonth;

    /**
     * 拖欠水费金额（元）
     */
    @JsonProperty("PROWATERMONEY")
    private BigDecimal prowatermoney;

    /**
     * 拖欠电费月数（月）
     */
    @JsonProperty("PROENERGYMONTH")
    private BigDecimal proenergymonth;

    /**
     * 拖欠电费金额（元）
     */
    @JsonProperty("PROENERGYMONEY")
    private BigDecimal proenergymoney;

    /**
     * 拖欠租金月数（月）
     */
    @JsonProperty("PRORENTMONTH")
    private BigDecimal prorentmonth;

    /**
     * 拖欠租金金额（元）
     */
    @JsonProperty("PRORENTMONEY")
    private BigDecimal prorentmoney;
    /**
     * 其他问题
     */
    @JsonProperty("PROBLEM")
    private String problem;
    /**
     * 监控措施
     */
    @JsonProperty("COPOVERSEE")
    private String copoversee;
    /**
     * 建议措施
     */
    @JsonProperty("COPADVICE")
    private String copadvice;
    /**
     * 采取措施
     */
    @JsonProperty("COPDEAL")
    private String copdeal;
    /**
     * 企业现状
     */
    @JsonProperty("COPRESULT")
    private String copresult;
    /**
     * 处置结果
     */
    @JsonProperty("COPAFTERWORK")
    private String copafterwork;
    /**
     * 请求协助
     */
    @JsonProperty("COPHELP")
    private String cophelp;
    /**
     * 风险级别
     */
    private String levelName;
    /**
     * 企业名称
     */
    private String copName;
    /**
     * 法人代表
     */
    private String copOwner;
    /**
     * 企业地址
     */
    private String copAddress;
    /**
     * 企业联系电话
     */
    private String copPhone;
    /**
     * 企业性质
     */
    private String copProperty;
    /**
     * 所属镇街
     */
    private String orgCN;


}
