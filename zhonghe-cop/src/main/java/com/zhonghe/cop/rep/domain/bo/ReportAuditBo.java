package com.zhonghe.cop.rep.domain.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Author: lpg
 * @Date: 2023/12/14/09:21
 * @Description:
 */
@Data
@ApiModel("报表审核对象")
public class ReportAuditBo implements Serializable {
    private static final long serialVersionUID = 821702688771636991L;
    /**
     * 部门上报ID
     */
    @ApiModelProperty(value = "部门上报ID", required = true)
    @NotEmpty(message = "部门上报ID不能为空")
    private Long[] repReportIds;

    /**
     * 审核状态（默认0，0——未审核、1——已审核、2退回）
     */
    @ApiModelProperty(value = "审核状态（默认0，0-未审核、1-已审核、2-退回）")
    @NotNull(message = "审核状态不能为空")
    private Integer auditing;

    /**
     * 审核意见
     */
    @ApiModelProperty(value = "审核意见")
    @Length(max = 2000, message = "审核意见长度不能超过2000个字符")
    private String auditingComment;
}
