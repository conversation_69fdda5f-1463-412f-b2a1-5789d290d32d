package com.zhonghe.cop.rep.ocr;

import com.alibaba.fastjson.JSON;
import com.zhonghe.cop.rep.config.OcrConfig;
import com.zhonghe.cop.rep.ocr.vo.AccurateTableBodyVo;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.util.Base64;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @Description: {百度ocr接口}
 * @date 2025/07/17
 */
@Slf4j
@Service("baiDuOcrService")
public class BaiDuOcrServiceImpl implements IBaiDuOcrService {
    public static final OkHttpClient HTTP_CLIENT = new OkHttpClient().newBuilder().readTimeout(300, TimeUnit.SECONDS).build();

    @Autowired
    private OcrConfig ocrConfig;

    /**
     * 获取文件base64编码
     *
     * @param is        文件
     * @param urlEncode 如果Content-Type是application/x-www-form-urlencoded时,传true
     * @return base64编码信息，不带文件头
     * @throws IOException IO异常
     */
    static String getFileContentAsBase64(InputStream is, boolean urlEncode) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        int length;
        while ((length = is.read(buffer)) != -1) {
            baos.write(buffer, 0, length);
        }
        byte[] b = baos.toByteArray();

        // 转换为Base64
        String base64 = Base64.getEncoder().encodeToString(b);

        // URL编码（如果需要）
        if (urlEncode) {
            base64 = URLEncoder.encode(base64, "utf-8");
        }

        return base64;
    }

    /**
     * 从原始字符串中截取 startKey 和 endKey 之间的内容。
     * - 如果 startKey 为空，则从头开始；
     * - 如果 endKey 为空，则截取到尾；
     * - 如果 startKey 不存在，则返回空；
     * - 如果 endKey 不存在，则截取到结尾；
     */
    public static String extractBetween(String text, String startKey, String endKey) {
        if (text == null || text.isEmpty()) {
            return "";
        }

        int startIndex = 0;

        if (startKey != null && !startKey.isEmpty()) {
            startIndex = text.indexOf(startKey);
            if (startIndex == -1) {
                return "";
            }
            startIndex += startKey.length(); // 跳过 startKey
        }

        int endIndex = text.length();

        if (endKey != null && !endKey.isEmpty()) {
            endIndex = text.indexOf(endKey, startIndex);
            if (endIndex == -1) {
                endIndex = text.length(); // 没找到 endKey，就截取到末尾
            }
        }

        if (startIndex >= endIndex) {
            return "";
        }

        return text.substring(startIndex, endIndex).trim();
    }

    /**
     * 从字符串中去掉末尾的日期（保留人名）
     * 处理如：“张三2025年7月31日” → “张三”
     */
    public static String removeDateSuffix(String text) {
        if (text == null) {
            return "";
        }

        // 匹配常见的日期格式（年/月/日/点/空格）
        return text.replaceFirst("[\\d一二三四五六七八九零〇]{2,4}年\\s*\\d{1,2}月\\s*\\d{1,2}日.*$", "").trim();
    }

 /*   public static void main(String[] args) throws IOException {
        MediaType mediaType = MediaType.parse("application/x-www-form-urlencoded");
        // pdf_file 可以通过 getFileContentAsBase64("C:\fakepath\副本石龙人社分局来访人员登记表.pdf") 方法获取,如果Content-Type是application/x-www-form-urlencoded时,第二个参数传true
        RequestBody body = RequestBody.create(mediaType, "pdf_file=" + getFileContentAsBase64() + "&cell_contents=false&return_excel=false");
        Request request = new Request.Builder()
            .url("https://aip.baidubce.com/rest/2.0/ocr/v1/table?access_token=" + getAccessToken())
            .method("POST", body)
            .addHeader("Content-Type", "application/x-www-form-urlencoded")
            .addHeader("Accept", "application/json")
            .build();
        Response response = HTTP_CLIENT.newCall(request).execute();
        System.out.println(response.body().string());

    }*/

    /**
     * 从字符串中提取第一个日期（支持格式：2025年7月31日）
     */
    public static String extractDateFromText(String text) {
        if (text == null) {
            return "";
        }

        // 支持中文格式的日期（如 2025年7月31日）
        Pattern pattern = Pattern.compile("\\d{4}年\\s*\\d{1,2}月\\s*\\d{1,2}日");

        Matcher matcher = pattern.matcher(text);
        if (matcher.find()) {
            return matcher.group().replaceAll("\\s+", ""); // 清除中间空格
        }

        return "";
    }

    /**
     * 获取百度OCR App ID
     */
    private String getAppId() {
        return ocrConfig.getBaidu().getAppId();
    }

    /**
     * 获取百度OCR API Key
     */
    private String getApiKey() {
        return ocrConfig.getBaidu().getApiKey();
    }

    /**
     * 获取百度OCR Secret Key
     */
    private String getSecretKey() {
        return ocrConfig.getBaidu().getSecretKey();
    }

    /**
     * 从用户的AK，SK生成鉴权签名（Access Token）
     *
     * @return 鉴权签名（Access Token）
     * @throws IOException IO异常
     */
    public String getAccessToken() throws IOException {
        MediaType mediaType = MediaType.parse("application/x-www-form-urlencoded");
        RequestBody body = RequestBody.create(mediaType, "grant_type=client_credentials&client_id=" + getApiKey()
            + "&client_secret=" + getSecretKey());
        Request request = new Request.Builder()
            .url("https://aip.baidubce.com/oauth/2.0/token")
            .method("POST", body)
            .addHeader("Content-Type", "application/x-www-form-urlencoded")
            .build();
        Response response = HTTP_CLIENT.newCall(request).execute();
        return JSON.parseObject(response.body().string()).getString("access_token");
    }

    @Override
    public Map<String, String> extractFieldsFromDocument(InputStream inputStream, Collection<String> keyList) {
        // 先调用table方法获取结构化数据
        List<AccurateTableBodyVo> cells = table(inputStream);
        // 然后从结构化数据中提取字段
        return generateJsonFromOcrByKeyList(cells, keyList);
    }

    /**
     * 根据用户给定的key列表，自动在OCR结果中查找key并提取其对应的值（支持跨行、相邻行、右侧/下方等常见证件布局）
     *
     * @param cells   OCR识别结果
     * @param keyList 需要查找的字段名列表（如["姓名", "性别", "身份证号码"]）
     * @throws IOException
     */
    @Override
    public Map<String, String> generateJsonFromOcrByKeyList(
        List<AccurateTableBodyVo> cells,
        Collection<String> keyList) {

        Map<String, String> resultMap = new LinkedHashMap<>();
        for (String key : keyList) {
            resultMap.put(key, ""); // 初始化空值
        }

        for (int i = 0; i < cells.size(); i++) {
            AccurateTableBodyVo cell = cells.get(i);
            String text = cell.getWords().trim().replaceAll("\\n", "");

            for (String key : keyList) {
                if (text.contains("法律文书指定送达地址")) {
                    String v1 = extractBetween(text, "法律文书指定送达地址：", "签名");
                    String v2 = extractBetween(text, "签名：", "");
                    resultMap.put("法律文书指定送达地址", v1);
                    resultMap.put("签名", v2);
                }
                if (text.contains("来访人签名")) {
                    String v1 = extractBetween(text, "来访人签名：", "劳动保障监察员");
                    resultMap.put("来访人签名", removeDateSuffix(v1));
                    resultMap.put("来访日期", extractDateFromText(v1));

                    String v2 = extractBetween(text, "劳动保障监察员签名：", "");
                    resultMap.put("劳动保障监察员签名", removeDateSuffix(v2));
                    resultMap.put("劳动保障监察日期", extractDateFromText(v2));
                }
                if (key.contains(text)) {
                    for (AccurateTableBodyVo nextCell : cells) {
                        if (Objects.equals(nextCell.getRow_start(), cell.getRow_start())
                            && Objects.equals(nextCell.getCol_start(), cell.getCol_end())) {
                            String value = nextCell.getWords().trim();
                            resultMap.put(key, value);
                            break;
                        }
                    }
                }
            }
        }

        return resultMap;
    }

    /**
     * 表格文字识别V2
     *
     * @return
     */
    @Override
    @SneakyThrows
    public List<AccurateTableBodyVo> table(InputStream inputStream) {
       /* // 初始化一个AipOcr
        AipOcr client = new AipOcr(APP_ID, API_KEY, SECRET_KEY);

        // 传入可选参数调用接口
        HashMap<String, Object> options = new HashMap<String, Object>();
        options.put("recognize_granularity", "big");
        options.put("detect_direction", "true");
        options.put("vertexes_location", "true");
        options.put("probability", "false");

        String json = "";

        if (file != null) {
            // 参数为本地图片二进制数组
            JSONObject res = client.table(file, options);
            json = res.toString(2);
        }*/

        MediaType mediaType = MediaType.parse("application/x-www-form-urlencoded");
        // pdf_file 可以通过 getFileContentAsBase64("C:\fakepath\副本石龙人社分局来访人员登记表.pdf") 方法获取,如果Content-Type是application/x-www-form-urlencoded时,第二个参数传true
        RequestBody body = RequestBody.create(mediaType, "pdf_file=" + getFileContentAsBase64(inputStream, true) + "&cell_contents=false&return_excel=false");
        Request request = new Request.Builder()
            .url("https://aip.baidubce.com/rest/2.0/ocr/v1/table?access_token=" + getAccessToken())
            .method("POST", body)
            .addHeader("Content-Type", "application/x-www-form-urlencoded")
            .addHeader("Accept", "application/json")
            .build();
        Response response = HTTP_CLIENT.newCall(request).execute();
        String json = response.body().string();
        log.info("baidu ocr result======================>:" + json);
        com.alibaba.fastjson.JSONObject jsonObject = JSON.parseObject(json);
        com.alibaba.fastjson.JSONArray tablesResult = jsonObject.getJSONArray("tables_result");
        com.alibaba.fastjson.JSONObject t_body = JSON.parseObject(tablesResult.get(0).toString());
        System.out.println(t_body.get("body").toString());
        return JSON.parseArray(t_body.get("body").toString(), AccurateTableBodyVo.class);

       /* String str = "[\n" +
            "    {\n" +
            "        \"col_start\": 0,\n" +
            "        \"row_start\": 0,\n" +
            "        \"words\": \"来访人情况\",\n" +
            "        \"col_end\": 1,\n" +
            "        \"row_end\": 2,\n" +
            "        \"cell_location\": [\n" +
            "            {\n" +
            "                \"x\": 48,\n" +
            "                \"y\": 118\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 131,\n" +
            "                \"y\": 117\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 131,\n" +
            "                \"y\": 246\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 48,\n" +
            "                \"y\": 246\n" +
            "            }\n" +
            "        ]\n" +
            "    },\n" +
            "    {\n" +
            "        \"col_start\": 1,\n" +
            "        \"row_start\": 0,\n" +
            "        \"words\": \"姓名\",\n" +
            "        \"col_end\": 2,\n" +
            "        \"row_end\": 1,\n" +
            "        \"cell_location\": [\n" +
            "            {\n" +
            "                \"x\": 132,\n" +
            "                \"y\": 118\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 234,\n" +
            "                \"y\": 119\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 234,\n" +
            "                \"y\": 183\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 132,\n" +
            "                \"y\": 183\n" +
            "            }\n" +
            "        ]\n" +
            "    },\n" +
            "    {\n" +
            "        \"col_start\": 2,\n" +
            "        \"row_start\": 0,\n" +
            "        \"words\": \"李鹏\",\n" +
            "        \"col_end\": 3,\n" +
            "        \"row_end\": 1,\n" +
            "        \"cell_location\": [\n" +
            "            {\n" +
            "                \"x\": 234,\n" +
            "                \"y\": 119\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 348,\n" +
            "                \"y\": 119\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 348,\n" +
            "                \"y\": 183\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 234,\n" +
            "                \"y\": 183\n" +
            "            }\n" +
            "        ]\n" +
            "    },\n" +
            "    {\n" +
            "        \"col_start\": 3,\n" +
            "        \"row_start\": 0,\n" +
            "        \"words\": \"性别\",\n" +
            "        \"col_end\": 4,\n" +
            "        \"row_end\": 1,\n" +
            "        \"cell_location\": [\n" +
            "            {\n" +
            "                \"x\": 348,\n" +
            "                \"y\": 119\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 410,\n" +
            "                \"y\": 119\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 410,\n" +
            "                \"y\": 183\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 348,\n" +
            "                \"y\": 183\n" +
            "            }\n" +
            "        ]\n" +
            "    },\n" +
            "    {\n" +
            "        \"col_start\": 4,\n" +
            "        \"row_start\": 0,\n" +
            "        \"words\": \"男\",\n" +
            "        \"col_end\": 5,\n" +
            "        \"row_end\": 1,\n" +
            "        \"cell_location\": [\n" +
            "            {\n" +
            "                \"x\": 410,\n" +
            "                \"y\": 119\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 507,\n" +
            "                \"y\": 119\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 507,\n" +
            "                \"y\": 183\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 410,\n" +
            "                \"y\": 183\n" +
            "            }\n" +
            "        ]\n" +
            "    },\n" +
            "    {\n" +
            "        \"col_start\": 5,\n" +
            "        \"row_start\": 0,\n" +
            "        \"words\": \"联系电话\",\n" +
            "        \"col_end\": 6,\n" +
            "        \"row_end\": 1,\n" +
            "        \"cell_location\": [\n" +
            "            {\n" +
            "                \"x\": 507,\n" +
            "                \"y\": 119\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 626,\n" +
            "                \"y\": 119\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 626,\n" +
            "                \"y\": 183\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 507,\n" +
            "                \"y\": 183\n" +
            "            }\n" +
            "        ]\n" +
            "    },\n" +
            "    {\n" +
            "        \"col_start\": 6,\n" +
            "        \"row_start\": 0,\n" +
            "        \"words\": \"1782891212\",\n" +
            "        \"col_end\": 7,\n" +
            "        \"row_end\": 1,\n" +
            "        \"cell_location\": [\n" +
            "            {\n" +
            "                \"x\": 626,\n" +
            "                \"y\": 119\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 772,\n" +
            "                \"y\": 119\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 772,\n" +
            "                \"y\": 183\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 626,\n" +
            "                \"y\": 183\n" +
            "            }\n" +
            "        ]\n" +
            "    },\n" +
            "    {\n" +
            "        \"col_start\": 1,\n" +
            "        \"row_start\": 1,\n" +
            "        \"words\": \"身份证号码\",\n" +
            "        \"col_end\": 2,\n" +
            "        \"row_end\": 2,\n" +
            "        \"cell_location\": [\n" +
            "            {\n" +
            "                \"x\": 132,\n" +
            "                \"y\": 183\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 234,\n" +
            "                \"y\": 183\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 234,\n" +
            "                \"y\": 247\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 132,\n" +
            "                \"y\": 247\n" +
            "            }\n" +
            "        ]\n" +
            "    },\n" +
            "    {\n" +
            "        \"col_start\": 2,\n" +
            "        \"row_start\": 1,\n" +
            "        \"words\": \"42090121213232323\",\n" +
            "        \"col_end\": 4,\n" +
            "        \"row_end\": 2,\n" +
            "        \"cell_location\": [\n" +
            "            {\n" +
            "                \"x\": 233,\n" +
            "                \"y\": 182\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 409,\n" +
            "                \"y\": 182\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 409,\n" +
            "                \"y\": 246\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 233,\n" +
            "                \"y\": 246\n" +
            "            }\n" +
            "        ]\n" +
            "    },\n" +
            "    {\n" +
            "        \"col_start\": 4,\n" +
            "        \"row_start\": 1,\n" +
            "        \"words\": \"居住地址\",\n" +
            "        \"col_end\": 5,\n" +
            "        \"row_end\": 2,\n" +
            "        \"cell_location\": [\n" +
            "            {\n" +
            "                \"x\": 410,\n" +
            "                \"y\": 183\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 507,\n" +
            "                \"y\": 183\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 507,\n" +
            "                \"y\": 247\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 410,\n" +
            "                \"y\": 247\n" +
            "            }\n" +
            "        ]\n" +
            "    },\n" +
            "    {\n" +
            "        \"col_start\": 5,\n" +
            "        \"row_start\": 1,\n" +
            "        \"words\": \"广东东莞\",\n" +
            "        \"col_end\": 7,\n" +
            "        \"row_end\": 2,\n" +
            "        \"cell_location\": [\n" +
            "            {\n" +
            "                \"x\": 506,\n" +
            "                \"y\": 182\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 771,\n" +
            "                \"y\": 182\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 771,\n" +
            "                \"y\": 246\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 506,\n" +
            "                \"y\": 246\n" +
            "            }\n" +
            "        ]\n" +
            "    },\n" +
            "    {\n" +
            "        \"col_start\": 0,\n" +
            "        \"row_start\": 2,\n" +
            "        \"words\": \"被投诉人情况\",\n" +
            "        \"col_end\": 1,\n" +
            "        \"row_end\": 5,\n" +
            "        \"cell_location\": [\n" +
            "            {\n" +
            "                \"x\": 48,\n" +
            "                \"y\": 246\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 131,\n" +
            "                \"y\": 246\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 131,\n" +
            "                \"y\": 436\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 48,\n" +
            "                \"y\": 436\n" +
            "            }\n" +
            "        ]\n" +
            "    },\n" +
            "    {\n" +
            "        \"col_start\": 1,\n" +
            "        \"row_start\": 2,\n" +
            "        \"words\": \"企业名称\",\n" +
            "        \"col_end\": 2,\n" +
            "        \"row_end\": 3,\n" +
            "        \"cell_location\": [\n" +
            "            {\n" +
            "                \"x\": 132,\n" +
            "                \"y\": 247\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 234,\n" +
            "                \"y\": 247\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 234,\n" +
            "                \"y\": 309\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 132,\n" +
            "                \"y\": 309\n" +
            "            }\n" +
            "        ]\n" +
            "    },\n" +
            "    {\n" +
            "        \"col_start\": 2,\n" +
            "        \"row_start\": 2,\n" +
            "        \"words\": \"创新科技有限公司\",\n" +
            "        \"col_end\": 5,\n" +
            "        \"row_end\": 3,\n" +
            "        \"cell_location\": [\n" +
            "            {\n" +
            "                \"x\": 233,\n" +
            "                \"y\": 246\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 506,\n" +
            "                \"y\": 246\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 506,\n" +
            "                \"y\": 308\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 233,\n" +
            "                \"y\": 308\n" +
            "            }\n" +
            "        ]\n" +
            "    },\n" +
            "    {\n" +
            "        \"col_start\": 5,\n" +
            "        \"row_start\": 2,\n" +
            "        \"words\": \"企业人数\",\n" +
            "        \"col_end\": 6,\n" +
            "        \"row_end\": 3,\n" +
            "        \"cell_location\": [\n" +
            "            {\n" +
            "                \"x\": 507,\n" +
            "                \"y\": 247\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 626,\n" +
            "                \"y\": 247\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 626,\n" +
            "                \"y\": 309\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 507,\n" +
            "                \"y\": 309\n" +
            "            }\n" +
            "        ]\n" +
            "    },\n" +
            "    {\n" +
            "        \"col_start\": 6,\n" +
            "        \"row_start\": 2,\n" +
            "        \"words\": \"1\",\n" +
            "        \"col_end\": 7,\n" +
            "        \"row_end\": 3,\n" +
            "        \"cell_location\": [\n" +
            "            {\n" +
            "                \"x\": 626,\n" +
            "                \"y\": 247\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 772,\n" +
            "                \"y\": 247\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 772,\n" +
            "                \"y\": 309\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 626,\n" +
            "                \"y\": 309\n" +
            "            }\n" +
            "        ]\n" +
            "    },\n" +
            "    {\n" +
            "        \"col_start\": 1,\n" +
            "        \"row_start\": 3,\n" +
            "        \"words\": \"企业地址\",\n" +
            "        \"col_end\": 2,\n" +
            "        \"row_end\": 4,\n" +
            "        \"cell_location\": [\n" +
            "            {\n" +
            "                \"x\": 132,\n" +
            "                \"y\": 309\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 234,\n" +
            "                \"y\": 309\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 234,\n" +
            "                \"y\": 373\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 132,\n" +
            "                \"y\": 373\n" +
            "            }\n" +
            "        ]\n" +
            "    },\n" +
            "    {\n" +
            "        \"col_start\": 2,\n" +
            "        \"row_start\": 3,\n" +
            "        \"words\": \"广东东莞\",\n" +
            "        \"col_end\": 4,\n" +
            "        \"row_end\": 4,\n" +
            "        \"cell_location\": [\n" +
            "            {\n" +
            "                \"x\": 233,\n" +
            "                \"y\": 308\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 409,\n" +
            "                \"y\": 308\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 409,\n" +
            "                \"y\": 372\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 233,\n" +
            "                \"y\": 372\n" +
            "            }\n" +
            "        ]\n" +
            "    },\n" +
            "    {\n" +
            "        \"col_start\": 4,\n" +
            "        \"row_start\": 3,\n" +
            "        \"words\": \"统一社会\\n信用代码\",\n" +
            "        \"col_end\": 5,\n" +
            "        \"row_end\": 4,\n" +
            "        \"cell_location\": [\n" +
            "            {\n" +
            "                \"x\": 410,\n" +
            "                \"y\": 309\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 507,\n" +
            "                \"y\": 309\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 507,\n" +
            "                \"y\": 373\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 410,\n" +
            "                \"y\": 373\n" +
            "            }\n" +
            "        ]\n" +
            "    },\n" +
            "    {\n" +
            "        \"col_start\": 5,\n" +
            "        \"row_start\": 3,\n" +
            "        \"words\": \"4000900020102B\",\n" +
            "        \"col_end\": 7,\n" +
            "        \"row_end\": 4,\n" +
            "        \"cell_location\": [\n" +
            "            {\n" +
            "                \"x\": 506,\n" +
            "                \"y\": 308\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 771,\n" +
            "                \"y\": 308\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 771,\n" +
            "                \"y\": 372\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 506,\n" +
            "                \"y\": 372\n" +
            "            }\n" +
            "        ]\n" +
            "    },\n" +
            "    {\n" +
            "        \"col_start\": 1,\n" +
            "        \"row_start\": 4,\n" +
            "        \"words\": \"企业负责人\",\n" +
            "        \"col_end\": 2,\n" +
            "        \"row_end\": 5,\n" +
            "        \"cell_location\": [\n" +
            "            {\n" +
            "                \"x\": 132,\n" +
            "                \"y\": 373\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 234,\n" +
            "                \"y\": 373\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 234,\n" +
            "                \"y\": 437\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 132,\n" +
            "                \"y\": 437\n" +
            "            }\n" +
            "        ]\n" +
            "    },\n" +
            "    {\n" +
            "        \"col_start\": 2,\n" +
            "        \"row_start\": 4,\n" +
            "        \"words\": \"李鹏\",\n" +
            "        \"col_end\": 4,\n" +
            "        \"row_end\": 5,\n" +
            "        \"cell_location\": [\n" +
            "            {\n" +
            "                \"x\": 233,\n" +
            "                \"y\": 372\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 409,\n" +
            "                \"y\": 372\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 409,\n" +
            "                \"y\": 436\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 233,\n" +
            "                \"y\": 436\n" +
            "            }\n" +
            "        ]\n" +
            "    },\n" +
            "    {\n" +
            "        \"col_start\": 4,\n" +
            "        \"row_start\": 4,\n" +
            "        \"words\": \"联系电话\",\n" +
            "        \"col_end\": 5,\n" +
            "        \"row_end\": 5,\n" +
            "        \"cell_location\": [\n" +
            "            {\n" +
            "                \"x\": 410,\n" +
            "                \"y\": 373\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 507,\n" +
            "                \"y\": 373\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 507,\n" +
            "                \"y\": 437\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 410,\n" +
            "                \"y\": 437\n" +
            "            }\n" +
            "        ]\n" +
            "    },\n" +
            "    {\n" +
            "        \"col_start\": 5,\n" +
            "        \"row_start\": 4,\n" +
            "        \"words\": \"1782891212\",\n" +
            "        \"col_end\": 7,\n" +
            "        \"row_end\": 5,\n" +
            "        \"cell_location\": [\n" +
            "            {\n" +
            "                \"x\": 506,\n" +
            "                \"y\": 372\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 771,\n" +
            "                \"y\": 372\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 771,\n" +
            "                \"y\": 436\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 506,\n" +
            "                \"y\": 436\n" +
            "            }\n" +
            "        ]\n" +
            "    },\n" +
            "    {\n" +
            "        \"col_start\": 0,\n" +
            "        \"row_start\": 5,\n" +
            "        \"words\": \"在诉\\n求内\\n容上\\n打钩“√”\",\n" +
            "        \"col_end\": 1,\n" +
            "        \"row_end\": 6,\n" +
            "        \"cell_location\": [\n" +
            "            {\n" +
            "                \"x\": 49,\n" +
            "                \"y\": 437\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 132,\n" +
            "                \"y\": 437\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 132,\n" +
            "                \"y\": 627\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 49,\n" +
            "                \"y\": 627\n" +
            "            }\n" +
            "        ]\n" +
            "    },\n" +
            "    {\n" +
            "        \"col_start\": 1,\n" +
            "        \"row_start\": 5,\n" +
            "        \"words\": \"1.拖欠2月份工资1000元；口2。辞职到期不发工资元；\\n口3。开除不结工资\\n元；□4。克扣工资\\n元；□5。扣证件、押金\\n元；\\n□6。超时工作\\n时/天；□7。工资低于市最低工资标准；□8。辞职不批；\\n□9。没有签订劳动合同；□10。保险问题；□11。解除劳动合同、经济补偿金等问题；\\n□12。企业改制职工安置问题；13。其它：\",\n" +
            "        \"col_end\": 7,\n" +
            "        \"row_end\": 6,\n" +
            "        \"cell_location\": [\n" +
            "            {\n" +
            "                \"x\": 131,\n" +
            "                \"y\": 436\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 771,\n" +
            "                \"y\": 436\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 771,\n" +
            "                \"y\": 626\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 131,\n" +
            "                \"y\": 626\n" +
            "            }\n" +
            "        ]\n" +
            "    },\n" +
            "    {\n" +
            "        \"col_start\": 0,\n" +
            "        \"row_start\": 6,\n" +
            "        \"words\": \"具体\\n内容\\n和\\n投诉\\n请求\",\n" +
            "        \"col_end\": 1,\n" +
            "        \"row_end\": 7,\n" +
            "        \"cell_location\": [\n" +
            "            {\n" +
            "                \"x\": 49,\n" +
            "                \"y\": 627\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 132,\n" +
            "                \"y\": 627\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 132,\n" +
            "                \"y\": 754\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 49,\n" +
            "                \"y\": 754\n" +
            "            }\n" +
            "        ]\n" +
            "    },\n" +
            "    {\n" +
            "        \"col_start\": 1,\n" +
            "        \"row_start\": 6,\n" +
            "        \"words\": \"要求偿还所有工资\",\n" +
            "        \"col_end\": 7,\n" +
            "        \"row_end\": 7,\n" +
            "        \"cell_location\": [\n" +
            "            {\n" +
            "                \"x\": 131,\n" +
            "                \"y\": 626\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 771,\n" +
            "                \"y\": 626\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 771,\n" +
            "                \"y\": 753\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 131,\n" +
            "                \"y\": 753\n" +
            "            }\n" +
            "        ]\n" +
            "    },\n" +
            "    {\n" +
            "        \"col_start\": 0,\n" +
            "        \"row_start\": 7,\n" +
            "        \"words\": \"证据材料\",\n" +
            "        \"col_end\": 1,\n" +
            "        \"row_end\": 8,\n" +
            "        \"cell_location\": [\n" +
            "            {\n" +
            "                \"x\": 49,\n" +
            "                \"y\": 754\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 132,\n" +
            "                \"y\": 754\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 132,\n" +
            "                \"y\": 822\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 49,\n" +
            "                \"y\": 822\n" +
            "            }\n" +
            "        ]\n" +
            "    },\n" +
            "    {\n" +
            "        \"col_start\": 1,\n" +
            "        \"row_start\": 7,\n" +
            "        \"words\": \"1.身份证复印件2份；2。其它：\\n已经提交相关材料\",\n" +
            "        \"col_end\": 7,\n" +
            "        \"row_end\": 8,\n" +
            "        \"cell_location\": [\n" +
            "            {\n" +
            "                \"x\": 131,\n" +
            "                \"y\": 753\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 771,\n" +
            "                \"y\": 753\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 771,\n" +
            "                \"y\": 821\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 131,\n" +
            "                \"y\": 821\n" +
            "            }\n" +
            "        ]\n" +
            "    },\n" +
            "    {\n" +
            "        \"col_start\": 0,\n" +
            "        \"row_start\": 8,\n" +
            "        \"words\": \"承办人意\\n见\",\n" +
            "        \"col_end\": 1,\n" +
            "        \"row_end\": 9,\n" +
            "        \"cell_location\": [\n" +
            "            {\n" +
            "                \"x\": 49,\n" +
            "                \"y\": 822\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 132,\n" +
            "                \"y\": 822\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 132,\n" +
            "                \"y\": 892\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 49,\n" +
            "                \"y\": 892\n" +
            "            }\n" +
            "        ]\n" +
            "    },\n" +
            "    {\n" +
            "        \"col_start\": 1,\n" +
            "        \"row_start\": 8,\n" +
            "        \"words\": \"同意\",\n" +
            "        \"col_end\": 7,\n" +
            "        \"row_end\": 9,\n" +
            "        \"cell_location\": [\n" +
            "            {\n" +
            "                \"x\": 131,\n" +
            "                \"y\": 821\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 771,\n" +
            "                \"y\": 821\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 771,\n" +
            "                \"y\": 891\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 131,\n" +
            "                \"y\": 891\n" +
            "            }\n" +
            "        ]\n" +
            "    },\n" +
            "    {\n" +
            "        \"col_start\": 0,\n" +
            "        \"row_start\": 9,\n" +
            "        \"words\": \"法律文书指定送达地址：\\n广东东莞汇星\\n签名：李鹏\",\n" +
            "        \"col_end\": 7,\n" +
            "        \"row_end\": 10,\n" +
            "        \"cell_location\": [\n" +
            "            {\n" +
            "                \"x\": 48,\n" +
            "                \"y\": 891\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 771,\n" +
            "                \"y\": 891\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 771,\n" +
            "                \"y\": 956\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 48,\n" +
            "                \"y\": 956\n" +
            "            }\n" +
            "        ]\n" +
            "    },\n" +
            "    {\n" +
            "        \"col_start\": 0,\n" +
            "        \"row_start\": 10,\n" +
            "        \"words\": \"来访人签名：张三2025年7月0日劳动保障监察员签名：李四2025年7月31日\",\n" +
            "        \"col_end\": 7,\n" +
            "        \"row_end\": 11,\n" +
            "        \"cell_location\": [\n" +
            "            {\n" +
            "                \"x\": 48,\n" +
            "                \"y\": 956\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 771,\n" +
            "                \"y\": 956\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 771,\n" +
            "                \"y\": 1021\n" +
            "            },\n" +
            "            {\n" +
            "                \"x\": 48,\n" +
            "                \"y\": 1021\n" +
            "            }\n" +
            "        ]\n" +
            "    }\n" +
            "]";
        return JSON.parseArray(str, AccurateTableBodyVo.class);*/
    }

    @Override
    public String getServiceType() {
        return "baidu";
    }
}
