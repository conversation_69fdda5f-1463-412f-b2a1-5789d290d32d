package com.zhonghe.cop.rep.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Assert;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhonghe.common.constant.Constants;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.utils.DateUtils;
import com.zhonghe.cop.bam.constants.CopConstants;
import com.zhonghe.cop.bam.domain.vo.TCopInformationVo;
import com.zhonghe.cop.bam.mapper.TCopInformationMapper;
import com.zhonghe.cop.rep.domain.DangerousEnterprises;
import com.zhonghe.cop.rep.domain.HighRiskEnterprises;
import com.zhonghe.cop.rep.domain.ProblemEnterprises;
import com.zhonghe.cop.rep.domain.RiskEnterprises;
import com.zhonghe.cop.rep.domain.TRepGarrep;
import com.zhonghe.cop.rep.domain.TRepReport;
import com.zhonghe.cop.rep.domain.bo.TRepGarrepBo;
import com.zhonghe.cop.rep.domain.dto.OwsDto;
import com.zhonghe.cop.rep.domain.query.TRepGarrepQuery;
import com.zhonghe.cop.rep.domain.vo.CopLevelVo;
import com.zhonghe.cop.rep.domain.vo.CopReportResultVo;
import com.zhonghe.cop.rep.domain.vo.CopReportVo;
import com.zhonghe.cop.rep.domain.vo.TRepGarrepInfoVo;
import com.zhonghe.cop.rep.domain.vo.TRepGarrepVo;
import com.zhonghe.cop.rep.domain.vo.TRepReportVo;
import com.zhonghe.cop.rep.domain.vo.table.TRepGarrepTableVo;
import com.zhonghe.cop.rep.em.AuditingStatus;
import com.zhonghe.cop.rep.em.Level;
import com.zhonghe.cop.rep.mapper.DangerousEnterprisesMapper;
import com.zhonghe.cop.rep.mapper.HighRiskEnterprisesMapper;
import com.zhonghe.cop.rep.mapper.ProblemEnterprisesMapper;
import com.zhonghe.cop.rep.mapper.RiskEnterprisesMapper;
import com.zhonghe.cop.rep.mapper.TRepGarrepMapper;
import com.zhonghe.cop.rep.mapper.TRepReportMapper;
import com.zhonghe.cop.rep.service.ITRepGarrepService;
import com.zhonghe.cop.rep.service.ITRepReportService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.YearMonth;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 汇总报，指标字段根据动态添加Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-06
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class TRepGarrepServiceImpl implements ITRepGarrepService {

    private final TRepGarrepMapper baseMapper;
    private final TRepReportMapper tRepReportMapper;
    private final TCopInformationMapper copInformationMapper;
    private final ITRepReportService iTRepReportService;

    private final DangerousEnterprisesMapper dangerousEnterprisesMapper;
    private final HighRiskEnterprisesMapper highRiskEnterprisesMapper;
    private final ProblemEnterprisesMapper problemEnterprisesMapper;
    private final RiskEnterprisesMapper riskEnterprisesMapper;

    /**
     * 查询汇总报，指标字段根据动态添加
     */
    @Override
    public TRepGarrepInfoVo queryById(Long repGarrepId) {
        TRepGarrepVo tRepGarrepVo = baseMapper.selectVoById(repGarrepId);
        //查询企业相关信息
        TCopInformationVo tCopInformationVo = copInformationMapper.selectVoById(tRepGarrepVo.getCopId());
        //查询部门上报信息
        DateTime dateTime = DateUtil.offsetMonth(tRepGarrepVo.getRepDate(), -1);
        Assert.notNull(tCopInformationVo, "企业信息不存在");
        List<TRepReportVo> repReportVos = baseMapper.
            selectRepReportList(tCopInformationVo.getCopId(), DateUtil.format(dateTime, DateUtils.YYYY_MM));

        TRepGarrepInfoVo garrepInfoVo = TRepGarrepInfoVo.builder().copInformation(tCopInformationVo).repReport(repReportVos).build();
        return garrepInfoVo;
    }

    /**
     * 查询汇总报，指标字段根据动态添加列表
     */
    @Override
    public TableDataInfo<TRepGarrepTableVo> queryPageList(TRepGarrepQuery bo, PageQuery pageQuery) {
        QueryWrapper<TRepGarrep> lqw = buildQueryWrapper(bo);
        if ("level".equals(pageQuery.getOrderByColumn())) {
            pageQuery.setOrderByColumn("\"level\"");
        }
        Page<TRepGarrepTableVo> result = baseMapper.selectRepGarrepList(pageQuery.build(), lqw);
        //报表日期减一个月
        result.getRecords().forEach(a -> {
            DateTime dateTime = DateUtil.offsetMonth(a.getRepDate(), -1);
            a.setRepDate(dateTime);
        });
        return TableDataInfo.build(result);
    }

    @Override
    public String buildFirstMonthMes() {
        //查询上月报表数据
        QueryWrapper<TRepGarrep> qw = Wrappers.query();
        //获取上个月报表时间
        qw.apply("to_char(trg.rep_date,'yyyy-MM') = {0}", DateUtil.date().toString(DateUtils.YYYY_MM));
        qw.eq("trg.del_flag", Constants.DEL_FLAG_NORMAL);
        List<TRepGarrepTableVo> repGarrepList = baseMapper.selectRepGarrepList(qw);

        Map<Integer, Long> groupedByLevel = repGarrepList.stream()
            .collect(Collectors.groupingBy(TRepGarrepTableVo::getLevel, Collectors.counting()));
        String message = String.format(CopConstants.MONTH_REPORT_TEMPLATE,
            DateUtil.month(DateUtil.offsetMonth(new Date(), -1)) + 1,
            groupedByLevel.get(1) == null ? 0 : groupedByLevel.get(1),
            groupedByLevel.get(2) == null ? 0 : groupedByLevel.get(2),
            groupedByLevel.get(3) == null ? 0 : groupedByLevel.get(3),
            groupedByLevel.get(4) == null ? 0 : groupedByLevel.get(4)
        );
        return message;
    }

    /**
     * 查询汇总报，指标字段根据动态添加列表
     */
    @Override
    public List<TRepGarrepTableVo> queryList(TRepGarrepQuery bo) {
        QueryWrapper<TRepGarrep> lqw = buildQueryWrapper(bo);
        return baseMapper.selectRepGarrepList(lqw);
    }

    private QueryWrapper<TRepGarrep> buildQueryWrapper(TRepGarrepQuery bo) {
        QueryWrapper<TRepGarrep> qw = Wrappers.query();
        //企业名称
        qw.like(StringUtils.isNotBlank(bo.getCopName()), "tci.cop_name", bo.getCopName());
        //上报日期+1个月
        if (StringUtils.isNotBlank(bo.getRepDate())) {
            DateTime dateTime = DateUtil.offsetMonth(DateUtil.parse(bo.getRepDate(), DateUtils.YYYY_MM), 1);
            qw.apply("to_char(trg.rep_date,'yyyy-MM') = {0}", dateTime.toString(DateUtils.YYYY_MM));
            //qw.apply("to_char(trg.rep_date,'yyyy-MM') = {0}", bo.getRepDate());
        }
        if (bo.getRepDateRange() != null && bo.getRepDateRange().length > 0) {
            //开始结束时间+1个月
            DateTime startTime = DateUtil.offsetMonth(DateUtil.parse(bo.getRepDateRange()[0], DateUtils.YYYY_MM), 1);
            DateTime endTime = DateUtil.offsetMonth(DateUtil.parse(bo.getRepDateRange()[1], DateUtils.YYYY_MM), 1);
            qw.between("trg.rep_date", startTime, endTime);
        }
        //风险级别
        qw.eq(bo.getLevel() != null, "trg.level", bo.getLevel());
        //组织
        qw.eq(StringUtils.isNotBlank(bo.getOrgName()), "tdo.org_name", bo.getOrgName());
        qw.eq("trg.del_flag", Constants.DEL_FLAG_NORMAL);
        qw.orderByDesc("trg.create_time");
        return qw;
    }

    /**
     * 新增汇总报，指标字段根据动态添加
     */
    @Override
    @Transactional
    public Boolean insertByBo(TRepGarrepBo bo) {
        TRepGarrep add = BeanUtil.toBean(bo, TRepGarrep.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setRepGarrepId(add.getRepGarrepId());
        }


        return flag;
    }

    /**
     * 修改汇总报，指标字段根据动态添加
     */
    @Override
    @Transactional
    public Boolean updateByBo(TRepGarrepBo bo) {
        TRepGarrep update = BeanUtil.toBean(bo, TRepGarrep.class);
        validEntityBeforeSave(update);
        boolean flag = baseMapper.updateById(update) > 0;
        if (flag) {
            TRepGarrep tRepGarrep = baseMapper.selectById(update.getRepGarrepId());
            //判断上报时间在本月或者上个月，才更新数据
            int thisMonth = YearMonth.parse(DateUtil.format(new Date(), DateUtils.YYYY_MM)).getMonthValue();
            //实际上报月份减一个月
            int repMonth = YearMonth.parse(DateUtil.offsetMonth(tRepGarrep.getRepDate(), -1).toString(DateUtils.YYYY_MM)).getMonthValue();

            //查询当前企业是否在图层数据中
            boolean copExistBigMap = iTRepReportService.copExistBigMap(tRepGarrep.getCopId());
            if ((repMonth == thisMonth || repMonth == (thisMonth - 1)) && copExistBigMap) {
                //同步更新大屏风险企业数据
                iTRepReportService.updateBigMapCopLevelData(BeanUtil.toBean(tRepGarrep, TRepReport.class));
            }
        }
        return flag;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(TRepGarrep entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public String queryAllRepProblem(Long copId, String date) {
        List<TRepReport> tRepReports =
            tRepReportMapper.selectList(Wrappers.<TRepReport>lambdaQuery().select(TRepReport::getNote)
                .eq(TRepReport::getCopId, copId)
                .eq(TRepReport::getAuditing, AuditingStatus.AUDITED.getCode())
                .apply("to_char(rep_date,'yyyy-MM') = {0}", date).orderByAsc(TRepReport::getRepDate));
        return tRepReports.stream().map(TRepReport::getNote).filter(StringUtils::isNotBlank).collect(Collectors.joining("\n"));
    }

    @Override
    public String queryAllRepDate(Long copId, String date) {
        List<TRepReport> tRepReports = tRepReportMapper.selectList(Wrappers.<TRepReport>lambdaQuery().select(TRepReport::getRepDate)
            .eq(TRepReport::getCopId, copId)
            .eq(TRepReport::getAuditing, AuditingStatus.AUDITED.getCode())
            .apply("to_char(rep_date,'yyyy-MM') = {0}", date).orderByAsc(TRepReport::getRepDate));
        //将上报时间拼接
        LinkedHashSet<String> repDates = tRepReports.stream().map(a -> DateUtil.format(a.getRepDate(), DateUtils.YYYY_MM_DD)).collect(Collectors.toCollection(LinkedHashSet::new));
        return String.join("；", repDates);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeNotInBeforeMonthBigMapData() {
        //获取上个月的日期
        DateTime dateTime = DateUtil.beginOfMonth(DateUtil.date());
        String beforeMonth = DateUtil.format(dateTime, DateUtils.YYYY_MM_DD);
        log.info("{},清除不在上个月的大屏数据", beforeMonth);

        //删除不在上个月的数据
        highRiskEnterprisesMapper.delete(Wrappers.<HighRiskEnterprises>lambdaQuery().apply("to_char(rep_date,'yyyy-MM-dd') != {0}", beforeMonth));
        dangerousEnterprisesMapper.delete(Wrappers.<DangerousEnterprises>lambdaQuery().apply("to_char(rep_date,'yyyy-MM-dd') != {0}", beforeMonth));
        problemEnterprisesMapper.delete(Wrappers.<ProblemEnterprises>lambdaQuery().apply("to_char(rep_date,'yyyy-MM-dd') != {0}", beforeMonth));
        riskEnterprisesMapper.delete(Wrappers.<RiskEnterprises>lambdaQuery().apply("to_char(rep_date,'yyyy-MM-dd') != {0}", beforeMonth));
    }

    @Override
    public OwsDto ows(String yearMonth, Integer levelValue) {
        //查询月+1的数据
        DateTime dateTime = DateUtil.offsetMonth(DateUtil.parse(yearMonth, DateUtils.YYYY_MM), 1);
        yearMonth = dateTime.toString(DateUtils.YYYY_MM);
        List<CopReportVo> copReportVos = baseMapper.copReportList(yearMonth, "");
        Map<Long, List<CopReportVo>> listMap = copReportVos.stream().collect(Collectors.groupingBy(CopReportVo::getLevel));
        //List<Level> allLevels = Arrays.asList(Level.PROBLEM, Level.RISK, Level.DANGER, Level.HIGH_DANGER);

        List<OwsDto.Features> featuresList = new ArrayList<>();
        //for (Level level : allLevels) {
        List<CopReportVo> copReportVoList = listMap.get(Long.parseLong(levelValue.toString()));
        if (copReportVoList != null) {
            for (CopReportVo copReportVo : copReportVoList) {
                OwsDto.Features features = new OwsDto.Features();
                OwsDto.GeometryDto geometryDto = new OwsDto.GeometryDto();
                geometryDto.setCoordinates(Arrays.asList(copReportVo.getLon(), copReportVo.getLat()));
                features.setGeometry(geometryDto);

                features.setProperties(BeanUtil.beanToMap(copReportVo));
                featuresList.add(features);
            }
            //}
        }
        OwsDto owsDto = new OwsDto();
        owsDto.setFeatures(featuresList);
        return owsDto;
    }

    @Override
    public CopReportResultVo copLevelReport(String yearMonth, String copName) {
        //查询月+1的数据
        DateTime dateTime = DateUtil.offsetMonth(DateUtil.parse(yearMonth, DateUtils.YYYY_MM), 1);
        yearMonth = dateTime.toString(DateUtils.YYYY_MM);

        CopReportResultVo copReportResultVo = new CopReportResultVo();
        //根据level显示风险级别
        List<CopLevelVo> copLevels = baseMapper.copLevelReportList(yearMonth);
        List<CopLevelVo> copLevelVos = new ArrayList<>();
        List<Level> allLevels = Arrays.asList(Level.PROBLEM, Level.RISK, Level.DANGER, Level.HIGH_DANGER);

        for (Level level : allLevels) {
            CopLevelVo newCopLevelVo = new CopLevelVo(level.getCode(), level.getName(), 0);

            copLevels.stream()
                .filter(copLevelVo -> ObjectUtil.isNotNull(copLevelVo.getLevel()) && copLevelVo.getLevel().equals(level.getCode()))
                .findFirst()
                .ifPresent(copLevelVo -> newCopLevelVo.setCount(copLevelVo.getCount()));

            copLevelVos.add(newCopLevelVo);
        }
        List<CopReportVo> copReportVos = baseMapper.copReportList(yearMonth, copName);
        copReportResultVo.setCopLevels(copLevelVos);

        copReportResultVo.setCopReports(copReportVos);
        return copReportResultVo;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean manualGather(String gatherDate) {

        //报表日期下个月1号
       /* String nextMonth = DateUtil.offsetMonth(DateUtil.parse(gatherDate, DateUtils.YYYY_MM), 1).toString(DateUtils.YYYY_MM);

        //查询企业上报数据，根据年月
        *//*List<TRepReport> tRepReports = repReportMapper.selectList(Wrappers.<TRepReport>lambdaQuery()
            .eq(TRepReport::getAuditing, AuditingStatus.AUDITED.getCode()).apply("to_char(rep_date,'yyyy-MM') = {0}", gatherDate));*//*

         *//*for (TRepReport tRepReport : tRepReports) {
            iTRepReportService.mergeCopLevelData(tRepReport);
        }*//*
        //查询汇总报表等于1号时，更新风险企业图层数据
        if (DateUtil.dayOfMonth(new Date()) == 1) {
            //删除企业相关数据
            iTRepReportService.deleteCopLevelData();
            List<TRepGarrep> tRepGarreps = baseMapper.selectList(Wrappers.<TRepGarrep>lambdaQuery().apply("to_char(rep_date,'yyyy-MM') = {0}", nextMonth));

            tRepGarreps.forEach(tRepGarrep -> {
                TRepReport tRepReport = BeanUtil.toBean(tRepGarrep, TRepReport.class);
                iTRepReportService.updateBigMapCopLevelData(tRepReport);
            });
        }*/

        return true;
    }

    /**
     * 批量删除汇总报，指标字段根据动态添加
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
