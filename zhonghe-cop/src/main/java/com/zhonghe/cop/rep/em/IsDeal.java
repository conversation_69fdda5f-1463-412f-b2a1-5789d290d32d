package com.zhonghe.cop.rep.em;

import lombok.Getter;

/**
 * @Author: lpg
 * @Date: 2023/12/13/14:45
 * @Description:
 */
@Getter
public enum IsDeal {
    /**
     * 未上报
     */
    NO(0, "未上报"),

    /**
     * 已上报
     */
    YES(1, "已上报");

    private final Integer code;
    private final String name;

    IsDeal(Integer code, String name) {
        this.code = code;
        this.name = name;
    }
}
