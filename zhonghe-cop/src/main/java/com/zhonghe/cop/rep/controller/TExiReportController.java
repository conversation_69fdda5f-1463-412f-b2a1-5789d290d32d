package com.zhonghe.cop.rep.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.zhonghe.common.annotation.Log;
import com.zhonghe.common.annotation.RepeatSubmit;
import com.zhonghe.common.core.controller.BaseController;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import com.zhonghe.common.enums.BusinessType;
import com.zhonghe.common.utils.poi.ExcelUtil;
import com.zhonghe.cop.rep.domain.bo.DespachoBo;
import com.zhonghe.cop.rep.domain.bo.HandExiReportBo;
import com.zhonghe.cop.rep.domain.bo.TExiReportBo;
import com.zhonghe.cop.rep.domain.query.TExiReportQuery;
import com.zhonghe.cop.rep.domain.vo.TExiReportVo;
import com.zhonghe.cop.rep.domain.vo.table.TExiReportTableVo;
import com.zhonghe.cop.rep.service.ITExiReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 紧急事件上报
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
@Validated
@Api(value = "紧急事件上报控制器", tags = {"紧急事件上报管理"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/cop/exiReport")
public class TExiReportController extends BaseController {

    private final ITExiReportService iTExiReportService;

    /**
     * 查询紧急事件上报列表
     */
    @ApiOperation("查询紧急事件上报列表（cop:exi:report:list）")
    @SaCheckPermission("cop:exi:report:list")
    @GetMapping("/list")
    public TableDataInfo<TExiReportTableVo> list(TExiReportQuery bo, PageQuery pageQuery) {
        return iTExiReportService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出紧急事件上报列表
     */
    @ApiOperation("导出紧急事件上报列表(cop:exi:report:export)")
    @SaCheckPermission("cop:exi:report:export")
    @Log(title = "紧急事件上报", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(TExiReportQuery bo, HttpServletResponse response) {
        List<TExiReportTableVo> list = iTExiReportService.queryList(bo);
        ExcelUtil.exportExcel(list, "紧急事件上报", TExiReportTableVo.class, response);
    }

    /**
     * 获取紧急事件上报详细信息
     */
    @ApiOperation("获取紧急事件上报详细信息(cop:exi:report:query)")
    @SaCheckPermission("cop:exi:report:query")
    @GetMapping("/{exiReportId}")
    public R<TExiReportVo> getInfo(@ApiParam("主键")
                                   @NotNull(message = "主键不能为空")
                                   @PathVariable("exiReportId") Long exiReportId) {
        return R.ok(iTExiReportService.queryById(exiReportId));
    }

    /**
     * 新增紧急事件上报
     */
    @ApiOperation("新增紧急事件上报(cop:exi:report:add)")
    @SaCheckPermission("cop:exi:report:add")
    @Log(title = "紧急事件上报", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody TExiReportBo bo) {
        return toAjax(iTExiReportService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改紧急事件上报
     */
    @ApiOperation("修改紧急事件上报(cop:exi:report:edit)")
    @SaCheckPermission("cop:exi:report:edit")
    @Log(title = "紧急事件上报", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody TExiReportBo bo) {
        return toAjax(iTExiReportService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除紧急事件上报
     */
    @ApiOperation("删除紧急事件上报(cop:exi:report:remove)")
    @SaCheckPermission("cop:exi:report:remove")
    @Log(title = "紧急事件上报", businessType = BusinessType.DELETE)
    @DeleteMapping("/{exiReportIds}")
    public R<Void> remove(@ApiParam("主键串")
                          @NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] exiReportIds) {
        return toAjax(iTExiReportService.deleteWithValidByIds(Arrays.asList(exiReportIds), true) ? 1 : 0);
    }

    /**
     * 处理批示
     */
    @ApiOperation("处理批示(cop:exi:report:handle)")
    @SaCheckPermission("cop:exi:report:handle")
    @Log(title = "处理批示", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/despacho")
    public R<Void> despacho(@Validated @RequestBody DespachoBo bo) {
        return toAjax(iTExiReportService.despacho(bo) ? 1 : 0);
    }

    /**
     * 处理上报
     */
    @ApiOperation("处理上报(cop:exi:report:handle)")
    @SaCheckPermission("cop:exi:report:handle")
    @Log(title = "处理上报", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/handle")
    public R<Void> edit(@Validated @RequestBody HandExiReportBo bo) {
        return toAjax(iTExiReportService.handleReport(bo) ? 1 : 0);
    }
}
