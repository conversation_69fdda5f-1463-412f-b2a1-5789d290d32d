package com.zhonghe.cop.rep.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zhonghe.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 紧急事件处理对象 t_exi_handle
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
@Deprecated
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("cop.t_exi_handle")
public class TExiHandle extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 紧急事件处理ID
     */
    @TableId
    private Long exiHandleId;
    /**
     * 是否删除
     */
    @TableLogic
    private String delFlag;
    /**
     * 说明
     */
    private String note;
    /**
     * 上报ID（对应t_exi_report表主键）
     */
    private Long exiReportId;
    /**
     * 描述
     */
    private String description;
    /**
     * 处理结果0-未处理，1-已处理
     */
    private String despacho;
    /**
     * 涉及部门ID串（逗号分割，例：1,2,3 ）
     */
    private String depts;

}
