package com.zhonghe.cop.rep.domain.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * @Author: lpg
 * @Date: 2023/12/19/18:20
 * @Description:
 */
@Data
public class OwsDto implements Serializable {
    private static final long serialVersionUID = -1735520742345803211L;
    private String type = "FeatureCollection";
    private List<Features> features;
    private Map<String, Object> crs;
    private Integer totalFeatures = 4;
    private Integer numberMatched = 4;
    private Integer numberReturned = 4;
    private Date timeStamp = new Date();


    public OwsDto() {
        Map<String, Object> properties = new HashMap<>();
        properties.put("name", "urn:ogc:def:crs:EPSG::4326");
        Map<String, Object> crs = new HashMap<>();
        crs.put("type", "name");
        crs.put("properties", properties);
        this.crs = crs;
    }

    @Data
    public static class Features implements Serializable {
        private static final long serialVersionUID = -6051780277850026740L;
        private String type = "Feature";
        private String id = UUID.randomUUID().toString();
        private GeometryDto geometry;
        private String geometry_name = "geom";
        private Map<String, Object> properties;
        private List<Double> bbox;
    }

    @Data
    public static class GeometryDto implements Serializable {
        private static final long serialVersionUID = 3429743902471365026L;
        private String type = "Point";
        private List<String> coordinates;
    }
}
