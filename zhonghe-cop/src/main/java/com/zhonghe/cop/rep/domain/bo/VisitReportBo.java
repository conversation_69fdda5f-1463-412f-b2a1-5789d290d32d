package com.zhonghe.cop.rep.domain.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zhonghe.common.core.domain.BaseEntity;
import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 信访上报业务对象 t_visit_report
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("信访上报业务对象")
public class VisitReportBo extends BaseEntity {

    private static final long serialVersionUID = -698495619951472557L;
    /**
     * 信访上报ID
     */
    @ApiModelProperty("信访上报ID")
    @NotNull(message = "信访上报ID不能为空", groups = {EditGroup.class})
    private Long visitReportId;

    /**
     * 投诉人姓名
     */
    @ApiModelProperty("投诉人姓名")
    @NotBlank(message = "投诉人姓名不能为空", groups = {AddGroup.class, EditGroup.class})
    private String cptName;

    /**
     * 投诉人性别
     */
    @ApiModelProperty("投诉人性别")
    private String cptGender;

    /**
     * 投诉人电话
     */
    @ApiModelProperty("投诉人电话")
    private String cptPhone;

    /**
     * 投诉人身份证
     */
    @ApiModelProperty("投诉人身份证")
    private String cptIdCard;

    /**
     * 投诉人地址
     */
    @ApiModelProperty("投诉人地址")
    private String cptAddress;

    /**
     * 被投诉人单位
     */
    @ApiModelProperty("被投诉人单位")
    @NotBlank(message = "被投诉人单位不能为空", groups = {AddGroup.class, EditGroup.class})
    private String rspUnit;

    /**
     * 被投诉人单位人数
     */
    @ApiModelProperty("被投诉人单位人数")
    private Integer rspUnitPopulation;

    /**
     * 被投诉人单位地址
     */
    @ApiModelProperty("被投诉人单位地址")
    private String rspUnitAddress;

    /**
     * 被投诉人单位统一社会信用代码
     */
    @ApiModelProperty("被投诉人单位统一社会信用代码")
    private String rspUnitCreditCode;

    /**
     * 被投诉人单位负责人
     */
    @ApiModelProperty("被投诉人单位负责人")
    private String rspUnitIncharge;

    /**
     * 被投诉人单位电话
     */
    @ApiModelProperty("被投诉人单位电话")
    private String rspUnitPhone;

    /**
     * 诉求内容选择1.拖欠    月份工资     元；2.辞职到期不发工资     元；
     * 3.开除不结工资     元；4.克扣工资     元；5.扣证件、押金     元；
     * 6.超时工作      时/天；7.工资低于市最低工资标准；8.辞职不批；
     * 9.没有签订劳动合同；10.保险问题；11.解除劳动合同、经济补偿金等问题；
     * 12.企业改制职工安置问题；13.其它
     */
    @ApiModelProperty("诉求内容选择")
    private List<AppealContent> appealContents = new ArrayList<>();
    /**
     * 诉求内容文本
     */
    @ApiModelProperty("诉求内容文本")
    private String appealContent;

    /**
     * 具体内容和投诉请求
     */
    @ApiModelProperty("具体内容和投诉请求")
    private String cptContent;

    /**
     * 证据材料身份证复印份数
     */
    @ApiModelProperty("证据材料身份证复印份数")
    private Integer evidenceIdCardCopyNum;

    /**
     * 其他证据
     */
    @ApiModelProperty("其他证据")
    private String evidenceOther;

    /**
     * 承办人意见
     */
    @ApiModelProperty("承办人意见")
    private String handlerOpinion;

    /**
     * 法律援助地址
     */
    @ApiModelProperty("法律援助地址")
    private String legalAddress;

    /**
     * 法律援助签名
     */
    @ApiModelProperty("法律援助签名")
    private String legalSignature;


    /**
     * 来访人签名
     */
    @ApiModelProperty("来访人签名")
    private String visitorSignature;

    /**
     * 来访日期
     */
    @ApiModelProperty("来访日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date visitorDate;

    /**
     * 劳动保障监察员签名
     */
    @ApiModelProperty("劳动保障监察员签名")
    private String laborSignature;

    /**
     * 劳动保障监察员日期
     */
    @ApiModelProperty("劳动保障监察员日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date laborDate;

    /**
     * 信息核对状态 0-未核对 1-已核对
     */
    @ApiModelProperty("信息核对状态 0-未核对 1-已核对")
    private Integer verifyStatus;

    /**
     * 处理结果
     */
    @ApiModelProperty("处理结果")
    private String handleResult;

    /**
     * 源文件OssId
     */
    @ApiModelProperty("源文件OssId")
    private Long sourceFileOssId;


    @Data
    public static class AppealContent {
        private String type;
        private Boolean isSelect = false;
        private String content;
        private List<String> values = new ArrayList<>();
    }
}
