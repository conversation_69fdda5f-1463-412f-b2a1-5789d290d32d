package com.zhonghe.cop.rep.service;

import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.cop.rep.domain.bo.RepNoProblemAuditBo;
import com.zhonghe.cop.rep.domain.bo.TRepNoproblemBo;
import com.zhonghe.cop.rep.domain.vo.TRepNoproblemVo;

import java.util.Collection;
import java.util.List;

/**
 * 无异常上报Service接口
 *
 * <AUTHOR>
 * @date 2024-01-23
 */
public interface ITRepNoproblemService {

    /**
     * 查询无异常上报
     */
    TRepNoproblemVo queryById(Long repNoproblemId);

    /**
     * 查询无异常上报列表
     */
    TableDataInfo<TRepNoproblemVo> queryPageList(TRepNoproblemBo bo, PageQuery pageQuery);

    /**
     * 查询无异常上报列表
     */
    List<TRepNoproblemVo> queryList(TRepNoproblemBo bo);

    /**
     * 修改无异常上报
     */
    Boolean insertByBo(TRepNoproblemBo bo);

    /**
     * 修改无异常上报
     */
    Boolean updateByBo(TRepNoproblemBo bo);

    /**
     * 校验并批量删除无异常上报信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 查询部门无异常上报
     *
     * @return
     */
    TRepNoproblemVo queryNoProblem();

    /**
     * 审核无异常上报
     *
     * @param bo
     * @return
     */
    Boolean auditNoProblem(RepNoProblemAuditBo bo);
}
