package com.zhonghe.cop.rep.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.zhonghe.cop.bam.constants.CopConstants;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * 紧急事件上报视图对象 t_exi_report
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
@Data
@ApiModel("紧急事件上报视图对象")
@ExcelIgnoreUnannotated
public class TExiReportVo implements Serializable, TransPojo {

    private static final long serialVersionUID = 1L;
    private Map<String, Object> transMap;
    /**
     * 紧急事件上报ID
     */
    @ExcelProperty(value = "紧急事件上报ID")
    @ApiModelProperty("紧急事件上报ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long exiReportId;

    /**
     * 企业ID
     */
    @ExcelProperty(value = "企业ID")
    @ApiModelProperty("企业ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long copId;

    /**
     * 企业名称
     */
    @ExcelProperty(value = "企业名称")
    @ApiModelProperty("企业名称")
    private String copName;

    /**
     * 企业法人
     */
    @ExcelProperty(value = "企业法人")
    @ApiModelProperty("企业法人")
    private String copOwner;

    /**
     * 企业人员规模
     */
    @ExcelProperty(value = "企业人员规模")
    @ApiModelProperty("企业人员规模")
    private Integer copPopulation;

    /**
     * 企业地址
     */
    @ExcelProperty(value = "企业地址")
    @ApiModelProperty("企业地址")
    private String copAddress;

    /**
     * 企业电话
     */
    @ExcelProperty(value = "企业电话")
    @ApiModelProperty("企业电话")
    private String copPhone;
    /**
     * 企业性质ID
     */
    @ExcelProperty(value = "企业性质ID")
    @ApiModelProperty("企业性质ID")
    @JsonSerialize(using = ToStringSerializer.class)
    @Trans(type = TransType.DICTIONARY, key = CopConstants.COP_PROPERTY, ref = "copProperty")
    private Long propertyId;

    /**
     * 企业性质
     */
    @ExcelProperty(value = "企业性质")
    @ApiModelProperty("企业性质")
    private String copProperty;


    /**
     * 事件描述
     */
    @ExcelProperty(value = "事件描述")
    @ApiModelProperty("事件描述")
    private String description;

    /**
     * 上报时间
     */
    @ExcelProperty(value = "上报时间")
    @ApiModelProperty("上报时间")
    private Date repDate;
    /**
     * 采取措施
     */
    @ApiModelProperty(value = "采取措施")
    private String copdeal;

    /**
     * 企业现状
     */
    @ApiModelProperty(value = "企业现状")
    private String copresult;

    /**
     * 后续工作
     */
    @ApiModelProperty(value = "后续工作")
    private String copafterwork;

    /**
     * 请求协助
     */
    @ApiModelProperty(value = "请求协助")
    private String cophelp;

    /**
     * 处理结果0-未处理，1-已处理
     */
    @ApiModelProperty(value = "处理结果0-未处理，1-已处理")
    @JsonSerialize(using = ToStringSerializer.class)
    private Integer despacho;
    /**
     * 处理结果
     */
    @ApiModelProperty(value = "处理批示")
    private String despachoNote;

    /**
     * 涉及部门
     */
    private String depts;

    /**
     * 涉及部门集合
     */
    @ApiModelProperty(value = "涉及部门")
    private String[] deptIds;

    /**
     * 涉及部门名称
     */
    @ApiModelProperty(value = "涉及部门名称")
    private List<String> deptNames = new ArrayList<>();


}
