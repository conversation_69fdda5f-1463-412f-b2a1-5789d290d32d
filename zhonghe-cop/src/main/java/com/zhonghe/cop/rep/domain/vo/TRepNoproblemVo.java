package com.zhonghe.cop.rep.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zhonghe.common.annotation.ExcelDictFormat;
import com.zhonghe.common.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;


/**
 * 无异常上报视图对象 t_rep_noproblem
 *
 * <AUTHOR>
 * @date 2024-01-23
 */
@Data
@ApiModel("无异常上报视图对象")
@ExcelIgnoreUnannotated
public class TRepNoproblemVo {

    private static final long serialVersionUID = 1L;

    /**
     * 无异常上报ID
     */
    @ExcelProperty(value = "无异常上报ID")
    @ApiModelProperty("无异常上报ID")
    private Long repNoproblemId;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    @ApiModelProperty("备注")
    private String note;

    /**
     * 上报日期（月份）
     */
    @ApiModelProperty("上报日期（月份）")
    private Date repDate;

    /**
     * 上报用户ID
     */
    @ExcelProperty(value = "上报用户ID")
    @ApiModelProperty("上报用户ID")
    private Long userId;

    /**
     * 部门ID
     */
    @ExcelProperty(value = "部门ID")
    @ApiModelProperty("部门ID")
    private Long deptId;

    /**
     * 审核状态（默认0，0——未审核、1——已审核、2退回）
     */
    @ExcelProperty(value = "审核状态", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "默=认0，0——未审核、1——已审核、2退回")
    @ApiModelProperty("审核状态（默认0，0——未审核、1——已审核、2退回）")
    private Integer auditing;
    /**
     * 审核状态名称
     */
    private String auditingName;


}
