package com.zhonghe.cop.rep.service;

import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.cop.rep.domain.bo.RegisterHandleResultBo;
import com.zhonghe.cop.rep.domain.bo.VisitReportBo;
import com.zhonghe.cop.rep.domain.query.VisitReportQuery;
import com.zhonghe.cop.rep.domain.vo.VisitReportVo;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Collection;
import java.util.List;

/**
 * 信访上报Service接口
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
public interface IVisitReportService {

    /**
     * 查询信访上报
     */
    VisitReportVo queryById(Long visitReportId);

    /**
     * 分页查询信访上报列表
     */
    TableDataInfo<VisitReportVo> queryPageList(VisitReportQuery query, PageQuery pageQuery);

    /**
     * 查询信访上报列表
     */
    List<VisitReportVo> queryList(VisitReportQuery query);

    /**
     * 新增信访上报
     */
    Boolean insertByBo(VisitReportBo bo);

    /**
     * 修改信访上报
     */
    Boolean updateByBo(VisitReportBo bo);

    /**
     * 校验并批量删除信访上报
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * PDF上传并OCR识别（TODO: 暂时手动录入）
     */
    VisitReportVo uploadPdfAndOcr(MultipartFile file);

    /**
     * 源文件预览
     */
    void previewSourceFile(Long visitReportId, HttpServletRequest request, HttpServletResponse response);

    /**
     * 处理结果登记
     */
    Boolean registerHandleResult(RegisterHandleResultBo bo);

    /**
     * 导出Word文档
     */
    void exportWord(Long visitReportId, HttpServletResponse response);

    /**
     * 导出Excel
     */
    void exportExcel(List<VisitReportVo> list, HttpServletResponse response);

}
