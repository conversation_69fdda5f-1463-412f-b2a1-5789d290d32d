package com.zhonghe.cop.rep.ocr;


import com.zhonghe.cop.rep.ocr.vo.AccurateTableBodyVo;

import java.io.InputStream;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description: {百度OCR接口 - 保持向后兼容}
 * @date 2025/07/17
 */
public interface IBaiDuOcrService extends IOcrService {

    /**
     * 根据用户给定的key列表，自动在OCR结果中查找key并提取其对应的值
     *
     * @param accurateTableBodyVo OCR识别结果
     * @param keyList             需要查找的字段名列表
     * @return 提取的键值对
     */
    Map<String, String> generateJsonFromOcrByKeyList(
        List<AccurateTableBodyVo> accurateTableBodyVo,
        Collection<String> keyList);

    /**
     * 表格文字识别
     *
     * @param inputStream 输入流
     * @return OCR识别结果
     */
    List<AccurateTableBodyVo> table(InputStream inputStream);
}
