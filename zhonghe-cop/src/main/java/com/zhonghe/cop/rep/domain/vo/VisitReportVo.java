package com.zhonghe.cop.rep.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.zhonghe.cop.rep.domain.bo.VisitReportBo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 信访上报视图对象 t_visit_report
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
@Data
@ApiModel("信访上报视图对象")
@ExcelIgnoreUnannotated
public class VisitReportVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 信访上报ID
     */
    @ExcelProperty(value = "信访上报ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long visitReportId;


    /**
     * 投诉人姓名
     */
    @ExcelProperty(value = "投诉人姓名")
    @ApiModelProperty("投诉人姓名")
    private String cptName;

    /**
     * 投诉人性别
     */
    @ExcelProperty(value = "投诉人性别")
    @ApiModelProperty("投诉人性别")
    private String cptGender;

    /**
     * 投诉人电话
     */
    @ExcelProperty(value = "投诉人电话")
    @ApiModelProperty("投诉人电话")
    private String cptPhone;

    /**
     * 投诉人身份证
     */
    @ExcelProperty(value = "投诉人身份证")
    @ApiModelProperty("投诉人身份证")
    private String cptIdCard;

    /**
     * 投诉人地址
     */
    @ExcelProperty(value = "投诉人地址")
    @ApiModelProperty("投诉人地址")
    private String cptAddress;

    /**
     * 被投诉人单位
     */
    @ExcelProperty(value = "被投诉人单位")
    @ApiModelProperty("被投诉人单位")
    private String rspUnit;

    /**
     * 被投诉人单位人数
     */
    @ExcelProperty(value = "被投诉人单位人数")
    @ApiModelProperty("被投诉人单位人数")
    private Integer rspUnitPopulation;

    /**
     * 被投诉人单位地址
     */
    @ExcelProperty(value = "被投诉人单位地址")
    @ApiModelProperty("被投诉人单位地址")
    private String rspUnitAddress;

    /**
     * 被投诉人单位统一社会信用代码
     */
    @ExcelProperty(value = "被投诉人单位统一社会信用代码")
    @ApiModelProperty("被投诉人单位统一社会信用代码")
    private String rspUnitCreditCode;

    /**
     * 被投诉人单位负责人
     */
    @ExcelProperty(value = "被投诉人单位负责人")
    @ApiModelProperty("被投诉人单位负责人")
    private String rspUnitIncharge;

    /**
     * 被投诉人单位电话
     */
    @ExcelProperty(value = "被投诉人单位电话")
    @ApiModelProperty("被投诉人单位电话")
    private String rspUnitPhone;


    /**
     * 诉求内容文本
     */
    @ApiModelProperty("诉求内容")
    private List<VisitReportBo.AppealContent> appealContents = new ArrayList<>();

    /**
     * 诉求内容
     */
    @ApiModelProperty("诉求内容")
    private String appealContent;
    /**
     * 诉求内容选择
     */
    private String appealContentJson;

    /**
     * 具体内容和投诉请求
     */
    @ExcelProperty(value = "具体内容和投诉请求")
    @ApiModelProperty("具体内容和投诉请求")
    private String cptContent;

    /**
     * 证据材料身份证复印份数
     */
    @ExcelProperty(value = "身份证复印份数")
    @ApiModelProperty("身份证复印份数")
    private Integer evidenceIdCardCopyNum;

    /**
     * 其他证据
     */
    @ExcelProperty(value = "其他证据")
    @ApiModelProperty("其他证据")
    private String evidenceOther;

    /**
     * 承办人意见
     */
    @ExcelProperty(value = "承办人意见")
    @ApiModelProperty("承办人意见")
    private String handlerOpinion;

    /**
     * 法律援助地址
     */
    @ExcelProperty(value = "法律援助地址")
    @ApiModelProperty("法律援助地址")
    private String legalAddress;

    /**
     * 法律援助签名
     */
    @ExcelProperty(value = "法律援助签名")
    @ApiModelProperty("法律援助签名")
    private String legalSignature;

    /**
     * 来访人签名
     */
    @ExcelProperty(value = "来访人签名")
    @ApiModelProperty("来访人签名")
    private String visitorSignature;

    /**
     * 来访日期
     */
    @ExcelProperty(value = "来访日期")
    @ApiModelProperty("来访日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date visitorDate;

    /**
     * 劳动保障监察员签名
     */
    @ExcelProperty(value = "劳动保障监察员签名")
    @ApiModelProperty("劳动保障监察员签名")
    private String laborSignature;

    /**
     * 劳动保障监察员日期
     */
    @ExcelProperty(value = "劳动保障监察员日期")
    @ApiModelProperty("劳动保障监察员日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date laborDate;

    /**
     * 信息核对状态 0-未核对 1-已核对
     */
    @ExcelProperty(value = "信息核对状态")
    @ApiModelProperty("信息核对状态 0-未核对 1-已核对")
    private Integer verifyStatus;

    /**
     * 处理结果
     */
    @ExcelProperty(value = "处理结果")
    @ApiModelProperty("处理结果")
    private String handleResult;

    /**
     * 源文件ossId
     */
    @ExcelProperty(value = "源文件源文件ossId")
    @ApiModelProperty("源文件源文件ossId")
    private Long sourceFileOssId;

    /**
     * 登记日期
     */
    @ExcelProperty(value = "登记日期")
    @ApiModelProperty("登记日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date registerDate;
    /**
     * 录入日期
     */
    @ExcelProperty(value = "录入日期")
    @ApiModelProperty("录入日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date inputDate;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;


}
