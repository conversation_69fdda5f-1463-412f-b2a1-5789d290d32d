package com.zhonghe.cop.rep.domain.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * @Author: lpg
 * @Date: 2024/03/07
 * @Description: 对外提供的绩效汇总数据
 */
@Data
public class OpenPerformance {
    /**
     * 部门名称
     */
    private String deptName;
    /**
     * 量化考核年份
     */
    private Integer repYear;

    /**
     * 1为已上报、0为未上报
     */

    /**
     * Jan 1月
     */
    @JsonProperty("Jan")
    private Integer jan;
    /**
     * Feb 2月
     */
    @JsonProperty("Feb")
    private Integer feb;
    /**
     * Mar 3月
     */
    @JsonProperty("Mar")
    private Integer mar;
    /**
     * Apr 4月
     */
    @JsonProperty("Apr")
    private Integer apr;
    /**
     * May 5月
     */
    @JsonProperty("May")
    private Integer may;
    /**
     * June 6月
     */
    @JsonProperty("June")
    private Integer june;
    /**
     * July 7月
     */
    @JsonProperty("July")
    private Integer july;
    /**
     * Aug 8月
     */
    @JsonProperty("Aug")
    private Integer aug;
    /**
     * Sept 9月
     */
    @JsonProperty("Sept")
    private Integer sept;
    /**
     * Oct
     */
    @JsonProperty("Oct")
    private Integer oct;
    /**
     * Nov
     */
    @JsonProperty("Nov")
    private Integer nov;
    /**
     * Dec
     */
    @JsonProperty("Dec")
    private Integer dec;
    /**
     * 所属镇街
     */
    private String orgCN;

}
