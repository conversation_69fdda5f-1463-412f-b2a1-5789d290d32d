package com.zhonghe.cop.rep.service;

import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.cop.rep.domain.bo.DespachoBo;
import com.zhonghe.cop.rep.domain.bo.HandExiReportBo;
import com.zhonghe.cop.rep.domain.bo.TExiReportBo;
import com.zhonghe.cop.rep.domain.query.TExiReportQuery;
import com.zhonghe.cop.rep.domain.vo.TExiReportVo;
import com.zhonghe.cop.rep.domain.vo.table.TExiReportTableVo;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Collection;
import java.util.List;

/**
 * 紧急事件上报Service接口
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
public interface ITExiReportService {

    /**
     * 查询紧急事件上报
     */
    TExiReportVo queryById(Long exiReportId);

    /**
     * 查询紧急事件上报列表
     */
    TableDataInfo<TExiReportTableVo> queryPageList(TExiReportQuery bo, PageQuery pageQuery);

    /**
     * 查询紧急事件上报列表
     */
    List<TExiReportTableVo> queryList(TExiReportQuery bo);

    /**
     * 修改紧急事件上报
     */
    Boolean insertByBo(TExiReportBo bo);

    /**
     * 修改紧急事件上报
     */
    Boolean updateByBo(TExiReportBo bo);

    /**
     * 校验并批量删除紧急事件上报信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 处理上报
     *
     * @param bo
     * @return
     */
    Boolean handleReport(HandExiReportBo bo);

    /**
     * 处理批示
     *
     * @param bo
     * @return
     */
    Boolean despacho(DespachoBo bo);

    /**
     * html转doc
     *
     * @param file
     * @param response
     */
    void htmlToDoc(MultipartFile file, HttpServletResponse response);
}
