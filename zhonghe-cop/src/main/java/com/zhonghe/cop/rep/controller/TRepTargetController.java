package com.zhonghe.cop.rep.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.zhonghe.common.annotation.Log;
import com.zhonghe.common.annotation.RepeatSubmit;
import com.zhonghe.common.core.controller.BaseController;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import com.zhonghe.common.enums.BusinessType;
import com.zhonghe.common.utils.poi.ExcelUtil;
import com.zhonghe.cop.rep.domain.bo.TRepTargetBo;
import com.zhonghe.cop.rep.domain.vo.TRepTargetVo;
import com.zhonghe.cop.rep.service.ITRepTargetService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 上报指标Controller
 *
 * <AUTHOR>
 * @date 2023-12-06
 */
@Deprecated
@Validated
@Api(value = "上报指标控制器", tags = {"上报指标管理"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/cop/rep/target")
public class TRepTargetController extends BaseController {

    private final ITRepTargetService iTRepTargetService;

    /**
     * 查询上报指标列表
     */
    @ApiOperation("查询上报指标列表")
    @SaCheckPermission("cop:repTarget:list")
    @GetMapping("/list")
    public TableDataInfo<TRepTargetVo> list(TRepTargetBo bo, PageQuery pageQuery) {
        return iTRepTargetService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出上报指标列表
     */
    @ApiOperation("导出上报指标列表")
    @SaCheckPermission("cop:repTarget:export")
    @Log(title = "上报指标", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(TRepTargetBo bo, HttpServletResponse response) {
        List<TRepTargetVo> list = iTRepTargetService.queryList(bo);
        ExcelUtil.exportExcel(list, "上报指标", TRepTargetVo.class, response);
    }

    /**
     * 获取上报指标详细信息
     */
    @ApiOperation("获取上报指标详细信息")
    @SaCheckPermission("cop:repTarget:query")
    @GetMapping("/{repTargetId}")
    public R<TRepTargetVo> getInfo(@ApiParam("主键")
                                   @NotNull(message = "主键不能为空")
                                   @PathVariable("repTargetId") Long repTargetId) {
        return R.ok(iTRepTargetService.queryById(repTargetId));
    }

    /**
     * 新增上报指标
     */
    @ApiOperation("新增上报指标")
    @SaCheckPermission("cop:repTarget:add")
    @Log(title = "上报指标", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody TRepTargetBo bo) {
        return toAjax(iTRepTargetService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改上报指标
     */
    @ApiOperation("修改上报指标")
    @SaCheckPermission("cop:repTarget:edit")
    @Log(title = "上报指标", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody TRepTargetBo bo) {
        return toAjax(iTRepTargetService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除上报指标
     */
    @ApiOperation("删除上报指标")
    @SaCheckPermission("cop:repTarget:remove")
    @Log(title = "上报指标", businessType = BusinessType.DELETE)
    @DeleteMapping("/{repTargetIds}")
    public R<Void> remove(@ApiParam("主键串")
                          @NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] repTargetIds) {
        return toAjax(iTRepTargetService.deleteWithValidByIds(Arrays.asList(repTargetIds), true) ? 1 : 0);
    }
}
