package com.zhonghe.cop.rep.ocr;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description: {OCR服务工厂类}
 * @date 2025/07/22
 */
@Slf4j
@Component
public class OcrServiceFactory {

    @Value("${ocr.service.type:baidu}")
    private String ocrServiceType;

    @Autowired
    private List<IOcrService> ocrServices;

    private Map<String, IOcrService> serviceMap = new HashMap<>();

    @PostConstruct
    public void init() {
        for (IOcrService service : ocrServices) {
            serviceMap.put(service.getServiceType(), service);
            log.info("注册OCR服务: {} -> {}", service.getServiceType(), service.getClass().getSimpleName());
        }
        log.info("当前配置的OCR服务类型: {}", ocrServiceType);
    }

    /**
     * 获取当前配置的OCR服务
     *
     * @return OCR服务实例
     */
    public IOcrService getOcrService() {
        IOcrService service = serviceMap.get(ocrServiceType);
        if (service == null) {
            log.warn("未找到配置的OCR服务类型: {}，使用默认的百度OCR服务", ocrServiceType);
            service = serviceMap.get("baidu");
            if (service == null) {
                throw new RuntimeException("未找到可用的OCR服务");
            }
        }
        log.debug("使用OCR服务: {} ({})", service.getServiceType(), service.getClass().getSimpleName());
        return service;
    }

    /**
     * 根据服务类型获取OCR服务
     *
     * @param serviceType 服务类型
     * @return OCR服务实例
     */
    public IOcrService getOcrService(String serviceType) {
        IOcrService service = serviceMap.get(serviceType);
        if (service == null) {
            throw new RuntimeException("未找到指定的OCR服务类型: " + serviceType);
        }
        return service;
    }

    /**
     * 获取所有可用的OCR服务类型
     *
     * @return 服务类型列表
     */
    public String[] getAvailableServiceTypes() {
        return serviceMap.keySet().toArray(new String[0]);
    }

    /**
     * 检查指定的服务类型是否可用
     *
     * @param serviceType 服务类型
     * @return 是否可用
     */
    public boolean isServiceTypeAvailable(String serviceType) {
        return serviceMap.containsKey(serviceType);
    }
}
