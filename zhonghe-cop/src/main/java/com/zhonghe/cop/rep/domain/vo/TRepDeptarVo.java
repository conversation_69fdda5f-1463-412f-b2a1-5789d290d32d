package com.zhonghe.cop.rep.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zhonghe.common.annotation.ExcelDictFormat;
import com.zhonghe.common.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 指标-部门关联，用于生成上报报视图对象 t_rep_deptar
 *
 * <AUTHOR>
 * @date 2023-12-06
 */
@Data
@ApiModel("指标-部门关联，用于生成上报报视图对象")
@ExcelIgnoreUnannotated
public class TRepDeptarVo {

    private static final long serialVersionUID = 1L;

    /**
     * 部门指标ID
     */
    @ExcelProperty(value = "部门指标ID")
    @ApiModelProperty("部门指标ID")
    private Long repDeptarId;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    @ApiModelProperty("备注")
    private String note;

    /**
     * 部门ID
     */
    @ExcelProperty(value = "部门ID")
    @ApiModelProperty("部门ID")
    private Long depId;

    /**
     * 指标ID（对应t_rep_target表主键）
     */
    @ExcelProperty(value = "指标ID", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "对=应t_rep_target表主键")
    @ApiModelProperty("指标ID（对应t_rep_target表主键）")
    private Long repTargetId;

    /**
     * 排序
     */
    @ExcelProperty(value = "排序")
    @ApiModelProperty("排序")
    private Long order;


}
