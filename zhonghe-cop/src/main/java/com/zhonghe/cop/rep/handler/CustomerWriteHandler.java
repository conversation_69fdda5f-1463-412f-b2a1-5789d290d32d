package com.zhonghe.cop.rep.handler;

import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.handler.CellWriteHandler;
import com.alibaba.excel.write.handler.RowWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import com.alibaba.excel.write.style.column.AbstractColumnWidthStyleStrategy;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.springframework.util.PropertyPlaceholderHelper;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

/**
 * @Author: lpg
 * @Date: 2024/01/31
 * @Description:
 */
public class CustomerWriteHandler extends AbstractColumnWidthStyleStrategy implements CellWriteHandler, RowWriteHandler {
    private final Map<Integer, Integer> maxRowsMap = new HashMap<>();
    PropertyPlaceholderHelper placeholderHelper = new PropertyPlaceholderHelper("${", "}");
    private Map<String, String> paramMap;
    private int totalRows;

    public CustomerWriteHandler() {
    }

    public CustomerWriteHandler(Map<String, String> paramMap, int totalRows) {
        this.paramMap = paramMap;
        this.totalRows = totalRows + 4;
    }

    public static String getCellValue(Cell cell) {
        String cellValue;
        switch (cell.getCellType()) {
            case STRING:
                cellValue = cell.getStringCellValue();
                break;
            case BOOLEAN:
                cellValue = String.valueOf(cell.getBooleanCellValue());
                break;
            case NUMERIC:
                cellValue = String.valueOf(cell.getNumericCellValue());
                break;
            default:
                cellValue = "";
                break;
        }
        return cellValue;
    }

    @Override
    public void beforeCellCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Row row, Head head, Integer integer, Integer integer1, Boolean aBoolean) {
        List<String> headNameList = head.getHeadNameList();
        if (CollectionUtils.isNotEmpty(headNameList)) {
            Properties properties = new Properties();
            properties.putAll(paramMap);
            for (int i = 0; i < headNameList.size(); i++) {
                String s = headNameList.get(i);
                String newValue = placeholderHelper.replacePlaceholders(s, properties);
                headNameList.set(i, newValue);
            }
        }
    }

    @Override
    protected void setColumnWidth(WriteSheetHolder writeSheetHolder, List<WriteCellData<?>> cellDataList, Cell cell,
                                  Head head, Integer relativeRowIndex, Boolean isHead) {

        boolean needSetWidth = isHead || CollectionUtils.isNotEmpty(cellDataList);
        if (needSetWidth) {
            // 包含\n的内容修改行高
            String cellValue = getCellValue(cell);
            boolean contains = StringUtils.contains(cellValue, "\n");
            if (contains) {
                int rows = cellValue.split("\n").length;
                int rowIndex = cell.getRowIndex();
                Integer maxRows = maxRowsMap.get(rowIndex);
                if (maxRows == null || maxRows < rows) {
                    cell.getRow().setHeightInPoints(rows * 15);
                    maxRowsMap.put(rowIndex, rows);
                }
            }
        }
    }


    /*@Override
    public void afterRowDispose(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Row row, Integer relativeRowIndex, Boolean isHead) {
        int rowNum = row.getRowNum();
        //判断是否是最后一行数据
        if (rowNum == totalRows - 1) {
            //添加一行统计数据，并合并单元格
            Row totalRow = writeSheetHolder.getSheet().createRow(rowNum + 1);

            //合并单元格
            writeSheetHolder.getSheet().addMergedRegion(new org.apache.poi.ss.util.CellRangeAddress(rowNum + 1, rowNum + 1, 0, 16));
            //假设你需要合并的是新建的这一行中的前五个单元格
            Cell cell = totalRow.createCell(0);

            Properties properties = new Properties();
            properties.putAll(paramMap);

            String foot = "高危企业${level4}家、危险企业${level3}家、风险企业${level2}家、问题企业${level1}家     总数${total}家";
            String value = placeholderHelper.replacePlaceholders(foot, properties);
            cell.setCellValue(value);
            // 创建单元格样式和字体
            CellStyle cellStyle = writeSheetHolder.getSheet().getWorkbook().createCellStyle();
            Font font = writeSheetHolder.getSheet().getWorkbook().createFont();
            font.setFontName("宋体");

            //单元格格式
            cellStyle.setAlignment(HorizontalAlignment.CENTER); // 水平居中
            cellStyle.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中

            cell.setCellStyle(cellStyle);

        }
    }*/
}
