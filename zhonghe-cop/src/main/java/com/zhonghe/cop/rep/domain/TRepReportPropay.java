package com.zhonghe.cop.rep.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zhonghe.common.core.domain.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: lpg
 * @Date: 2025/08/11
 * @Description:
 */
@Data
@TableName("cop.t_rep_report_propay")
public class TRepReportPropay extends BaseEntity {
    private static final long serialVersionUID = 7467904079264737797L;

    @TableId("rep_report_propay_id")
    private Long repReportPropayId;
    private Long repReportId;
    private BigDecimal propaymonth;
    private String propaymonths;
    private BigDecimal propaypopulation;
    private BigDecimal propaymoney;
    private String propaytype;
    @TableLogic
    private String delFlag;
}
