package com.zhonghe.cop.rep.em;

import lombok.Getter;

/**
 * @Author: lpg
 * @Date: 2023/12/07
 * @Description: 紧急事件上报处理结果
 */
@Getter
public enum DespachoStatus {
    /**
     * 未处理
     */
    UNDEAL(0, "未处理"),
    /**
     * 已处理
     */
    DEAL(1, "已处理");

    private Integer code;
    private String name;

    DespachoStatus(Integer code, String name) {
        this.code = code;
        this.name = name;
    }
}
