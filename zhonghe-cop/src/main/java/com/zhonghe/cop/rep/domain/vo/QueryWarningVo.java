package com.zhonghe.cop.rep.domain.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zhonghe.framework.config.StripTrailingZerosBigDecimalSerializer;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: lpg
 * @Date: 2024/03/07
 * @Description: 查询预警信息
 */
@Data
public class QueryWarningVo {
    /**
     * 企业存在的问题
     */
    private String czwt;
    /**
     * 主键id
     */
    private Long fid;
    /**
     * 企业风险等级
     */
    private String fxdj;
    /**
     * 工商注册号
     */
    private String gszch;
    /**
     * 企业名称
     */
    private String qymc;
    /**
     * 数据年月
     */
    private String sjny;
    /**
     * 所属镇街
     */
    private String sszj;
    /**
     * 拖欠电费金额（元）
     */
    private BigDecimal tqdfje;
    /**
     * 拖欠电费月数（月）
     */
    @JsonSerialize(using = StripTrailingZerosBigDecimalSerializer.class)
    private BigDecimal tqdfys;
    /**
     * 拖欠工资金额（元）
     */
    private BigDecimal tqgzje;
    /**
     * 拖欠工资人数
     */
    @JsonSerialize(using = StripTrailingZerosBigDecimalSerializer.class)
    private BigDecimal tqgzrs;
    /**
     * 拖欠工资月数
     */
    @JsonSerialize(using = StripTrailingZerosBigDecimalSerializer.class)
    private BigDecimal tqgzys;
    /**
     * 拖欠社保金额（元）
     */
    private BigDecimal tqsbje;
    /**
     * 拖欠社保月数（月）
     */
    @JsonSerialize(using = StripTrailingZerosBigDecimalSerializer.class)
    private BigDecimal tqsbys;
    /**
     * 拖欠税费金额（元）
     */
    private BigDecimal tqsfje;
    /**
     * 拖欠水费金额（元）
     */
    private BigDecimal tqsfje1;
    /**
     * 欠税费月数（月）
     */
    @JsonSerialize(using = StripTrailingZerosBigDecimalSerializer.class)
    private BigDecimal tqsfys;
    /**
     * 拖欠水费月数（月）
     */
    @JsonSerialize(using = StripTrailingZerosBigDecimalSerializer.class)
    private BigDecimal tqsfys1;
    /**
     * 拖欠租金金额（元）
     */
    private BigDecimal tqzjje;
    /**
     * 拖欠租金月数（月）
     */
    @JsonSerialize(using = StripTrailingZerosBigDecimalSerializer.class)
    private BigDecimal tqzjys;
    /**
     * 统一社会信用代码
     */
    private String tyshxydm;
}
