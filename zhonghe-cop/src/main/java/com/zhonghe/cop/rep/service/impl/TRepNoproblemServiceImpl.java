package com.zhonghe.cop.rep.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.helper.LoginHelper;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.cop.bam.domain.TPerPerformance;
import com.zhonghe.cop.bam.mapper.TPerPerformanceMapper;
import com.zhonghe.cop.rep.domain.TRepNoproblem;
import com.zhonghe.cop.rep.domain.bo.RepNoProblemAuditBo;
import com.zhonghe.cop.rep.domain.bo.TRepNoproblemBo;
import com.zhonghe.cop.rep.domain.vo.TRepNoproblemVo;
import com.zhonghe.cop.rep.em.AuditingStatus;
import com.zhonghe.cop.rep.em.IsDeal;
import com.zhonghe.cop.rep.mapper.TRepNoproblemMapper;
import com.zhonghe.cop.rep.service.ITRepNoproblemService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 无异常上报Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-23
 */
@RequiredArgsConstructor
@Service
public class TRepNoproblemServiceImpl implements ITRepNoproblemService {

    private final TRepNoproblemMapper baseMapper;
    private final TPerPerformanceMapper tPerPerformanceMapper;

    /**
     * 查询无异常上报
     */
    @Override
    public TRepNoproblemVo queryById(Long repNoproblemId) {
        return baseMapper.selectVoById(repNoproblemId);
    }

    /**
     * 查询无异常上报列表
     */
    @Override
    public TableDataInfo<TRepNoproblemVo> queryPageList(TRepNoproblemBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<TRepNoproblem> lqw = buildQueryWrapper(bo);
        Page<TRepNoproblemVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询无异常上报列表
     */
    @Override
    public List<TRepNoproblemVo> queryList(TRepNoproblemBo bo) {
        LambdaQueryWrapper<TRepNoproblem> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<TRepNoproblem> buildQueryWrapper(TRepNoproblemBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<TRepNoproblem> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getNote()), TRepNoproblem::getNote, bo.getNote());
        lqw.eq(bo.getRepDate() != null, TRepNoproblem::getRepDate, bo.getRepDate());
        lqw.eq(bo.getUserId() != null, TRepNoproblem::getUserId, bo.getUserId());
        lqw.eq(bo.getDeptId() != null, TRepNoproblem::getDeptId, bo.getDeptId());
        lqw.eq(bo.getAuditing() != null, TRepNoproblem::getAuditing, bo.getAuditing());
        return lqw;
    }

    /**
     * 新增无异常上报
     */
    @Override
    public Boolean insertByBo(TRepNoproblemBo bo) {
        TRepNoproblem add = BeanUtil.toBean(bo, TRepNoproblem.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setRepNoproblemId(add.getRepNoproblemId());
        }
        return flag;
    }

    /**
     * 修改无异常上报
     */
    @Override
    public Boolean updateByBo(TRepNoproblemBo bo) {
        TRepNoproblem update = BeanUtil.toBean(bo, TRepNoproblem.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(TRepNoproblem entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean auditNoProblem(RepNoProblemAuditBo bo) {
        TRepNoproblem tRepNoproblem = baseMapper.selectById(bo.getRepNoproblemId());

        if (tRepNoproblem == null) {
            return false;
        }
        if (tRepNoproblem.getAuditing().equals(AuditingStatus.AUDITED.getCode())) {
            throw new RuntimeException("已审核不能再次审核");
        }
        if (bo.getAuditing().equals(AuditingStatus.AUDITED.getCode())) {
            DateTime date = DateUtil.beginOfMonth(new Date());
            TPerPerformance tPerPerformance = tPerPerformanceMapper.selectOne(Wrappers.<TPerPerformance>lambdaQuery()
                .eq(TPerPerformance::getDeptId, LoginHelper.getDeptId()).eq(TPerPerformance::getRepDate, date));


            if (ObjectUtil.isNull(tPerPerformance)) {
                //不存在绩效考核则插入
                tPerPerformance = new TPerPerformance();
                tPerPerformance.setDeptId(LoginHelper.getDeptId());
                tPerPerformance.setRepDate(date);
                tPerPerformance.setIsDeal(IsDeal.YES.getCode());
                tPerPerformanceMapper.insert(tPerPerformance);
            } else {
                //存在则更新
                tPerPerformance.setIsDeal(IsDeal.YES.getCode());
                tPerPerformanceMapper.updateById(tPerPerformance);
            }
        }
        tRepNoproblem.setAuditing(bo.getAuditing());
        return baseMapper.updateById(tRepNoproblem) > 0;
    }

    @Override
    public TRepNoproblemVo queryNoProblem() {
        TRepNoproblem tRepNoproblem = baseMapper
            .selectOne(Wrappers.<TRepNoproblem>lambdaQuery()
                .eq(TRepNoproblem::getRepDate, DateUtil.beginOfMonth(new Date())).eq(TRepNoproblem::getDeptId, LoginHelper.getDeptId()));
        if (ObjectUtil.isNull(tRepNoproblem)) {
            return null;
        }
        TRepNoproblemVo repNoproblemVo = BeanUtil.toBean(tRepNoproblem, TRepNoproblemVo.class);
        //审核状态名称
        repNoproblemVo.setAuditingName(AuditingStatus.values()[repNoproblemVo.getAuditing()].getName());
        return repNoproblemVo;
    }

    /**
     * 批量删除无异常上报
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
