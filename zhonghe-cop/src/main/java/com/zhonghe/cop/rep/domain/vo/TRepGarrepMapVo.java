package com.zhonghe.cop.rep.domain.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zhonghe.cop.bam.domain.vo.TCopInformationVo;
import io.swagger.annotations.ApiModel;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: lpg
 * @Date: 2023/12/06
 * @Description: 报表信息视图对象
 */
@Builder
@JsonSerialize
@Data
@ApiModel("报表信息视图对象")
public class TRepGarrepMapVo implements Serializable {
    private static final long serialVersionUID = -3922931915591273592L;
    /**
     * 企业信息
     */
    private TCopInformationVo copInformation;

    /**
     * 企业风险月报
     */
    private TRepGarrepVo repReport;
}
