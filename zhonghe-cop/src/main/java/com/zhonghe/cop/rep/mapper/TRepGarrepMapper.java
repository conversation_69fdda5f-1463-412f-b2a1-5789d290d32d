package com.zhonghe.cop.rep.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhonghe.common.core.mapper.BaseMapperPlus;
import com.zhonghe.cop.bam.domain.vo.TCopInformationVo;
import com.zhonghe.cop.rep.domain.TRepGarrep;
import com.zhonghe.cop.rep.domain.vo.CopLevelVo;
import com.zhonghe.cop.rep.domain.vo.CopReportVo;
import com.zhonghe.cop.rep.domain.vo.TRepGarrepVo;
import com.zhonghe.cop.rep.domain.vo.TRepReportVo;
import com.zhonghe.cop.rep.domain.vo.table.TRepGarrepTableVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 汇总报，指标字段根据动态添加Mapper接口
 *
 * <AUTHOR>
 * @date 2023-12-06
 */
public interface TRepGarrepMapper extends BaseMapperPlus<TRepGarrepMapper, TRepGarrep, TRepGarrepVo> {

    /**
     * 分页查询上报列表
     *
     * @param build
     * @param lqw
     * @return
     */
    Page<TRepGarrepTableVo> selectRepGarrepList(Page<TRepGarrep> build, @Param(Constants.WRAPPER) Wrapper<TRepGarrep> lqw);

    /**
     * 查询上报列表
     *
     * @param lqw
     * @return
     */
    List<TRepGarrepTableVo> selectRepGarrepList(@Param(Constants.WRAPPER) Wrapper<TRepGarrep> lqw);

    /**
     * 查询上报列表
     *
     * @param copId
     * @param repDate
     * @return
     */
    List<TRepReportVo> selectRepReportList(@Param("copId") Long copId, @Param("repDate") String repDate);

    /**
     * 查询上报风险统计
     *
     * @param yearMonth
     * @return
     */
    List<CopLevelVo> copLevelReportList(@Param("yearMonth") String yearMonth);

    /**
     * 查询企业上报列表
     *
     * @param yearMonth
     * @return
     */
    List<CopReportVo> copReportList(@Param("yearMonth") String yearMonth, @Param("copName") String copName);

    /**
     * 连续2个月因欠薪而高危的企业
     *
     * @return
     */
    List<TCopInformationVo> select2MonthCop();
}
