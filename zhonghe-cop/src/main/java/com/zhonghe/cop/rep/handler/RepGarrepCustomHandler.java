package com.zhonghe.cop.rep.handler;

import com.alibaba.excel.write.handler.RowWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import org.apache.poi.ss.usermodel.Row;

/**
 * @Author: lpg
 * @Date: 2024/01/31
 * @Description:
 */
public class RepGarrepCustomHandler implements RowWriteHandler {


    @Override
    public void beforeRowCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder,
                                Integer rowIndex, Integer relativeRowIndex, Boolean isHead) {


    }

    @Override
    public void afterRowCreate(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Row row, Integer relativeRowIndex,
                               Boolean isHead) {
        // 这段代码会在新行创建之后立刻被调用
    }

    @Override
    public void afterRowDispose(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Row row, Integer relativeRowIndex,
                                Boolean isHead) {
        // 这段代码会在每一行写完之后被调用，包括头和数据，可以用来关闭流、释放资源。
    }
}
