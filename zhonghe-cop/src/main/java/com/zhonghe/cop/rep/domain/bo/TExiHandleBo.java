package com.zhonghe.cop.rep.domain.bo;

import com.zhonghe.common.core.domain.BaseEntity;
import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


/**
 * 紧急事件处理业务对象 t_exi_handle
 *
 * <AUTHOR>
 * @date 2023-12-07
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("紧急事件处理业务对象")
public class TExiHandleBo extends BaseEntity {

    private static final long serialVersionUID = 479892880039608744L;
    /**
     * 紧急事件处理ID
     */
    @ApiModelProperty(value = "紧急事件处理ID", required = true)
    @NotNull(message = "紧急事件处理ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long exiHandleId;

    /**
     * 说明
     */
    @ApiModelProperty(value = "说明", required = true)
    @NotBlank(message = "说明不能为空", groups = {AddGroup.class, EditGroup.class})
    private String note;

    /**
     * 上报ID（对应t_exi_report表主键）
     */
    @ApiModelProperty(value = "上报ID（对应t_exi_report表主键）", required = true)
    @NotNull(message = "上报ID（对应t_exi_report表主键）不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long exiReportId;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述", required = true)
    @NotBlank(message = "描述不能为空", groups = {AddGroup.class, EditGroup.class})
    private String description;

    /**
     * 处理结果
     */
    @ApiModelProperty(value = "处理结果", required = true)
    @NotBlank(message = "处理结果不能为空", groups = {AddGroup.class, EditGroup.class})
    private String despacho;

    /**
     * 涉及部门ID串（逗号分割，例：1,2,3 ）
     */
    @ApiModelProperty(value = "涉及部门ID串（逗号分割，例：1,2,3 ）", required = true)
    @NotBlank(message = "涉及部门ID串（逗号分割，例：1,2,3 ）不能为空", groups = {AddGroup.class, EditGroup.class})
    private String depts;


}
