package com.zhonghe.cop.rep.domain.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 信访上报查询对象 t_visit_report
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
@Data
@ApiModel("信访上报查询对象")
public class VisitReportQuery implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 被投诉企业
     */
    @ApiModelProperty("被投诉企业")
    private String rspUnit;

    /**
     * 登记日期开始
     */
    @ApiModelProperty("登记日期开始")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date registerStartDate;

    /**
     * 登记日期结束
     */
    @ApiModelProperty("登记日期结束")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date registerEndDate;


    /**
     * 录入日期
     */
    @ApiModelProperty("录入日期")
    @DateTimeFormat(pattern = "yyyy-MM")
    private Date inputDate;

    /**
     * 信息核对状态 0-未核对 1-已核对
     */
    @ApiModelProperty("信息核对状态 0-未核对 1-已核对")
    private Integer verifyStatus;

    /**
     * 投诉人姓名
     */
    @ApiModelProperty("投诉人姓名")
    private String cptName;

    /**
     * 投诉人电话
     */
    @ApiModelProperty("投诉人电话")
    private String cptPhone;

}
