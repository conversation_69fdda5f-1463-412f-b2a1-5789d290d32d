package com.zhonghe.cop.rep.ocr;

import java.io.InputStream;
import java.util.Collection;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description: {通用OCR服务接口}
 * @date 2025/07/22
 */
public interface IOcrService {

    /**
     * 根据用户给定的key列表，直接从输入流中提取对应的值
     *
     * @param inputStream 输入流
     * @param keyList     需要查找的字段名列表
     * @return 提取的键值对
     */
    Map<String, String> extractFieldsFromDocument(InputStream inputStream, Collection<String> keyList);

    /**
     * 获取OCR服务类型
     *
     * @return 服务类型
     */
    String getServiceType();
}
