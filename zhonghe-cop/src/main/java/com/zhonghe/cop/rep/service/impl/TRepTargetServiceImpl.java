package com.zhonghe.cop.rep.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.cop.rep.domain.TRepTarget;
import com.zhonghe.cop.rep.domain.bo.TRepTargetBo;
import com.zhonghe.cop.rep.domain.vo.TRepTargetVo;
import com.zhonghe.cop.rep.mapper.TRepTargetMapper;
import com.zhonghe.cop.rep.service.ITRepTargetService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 上报指标Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-06
 */
@RequiredArgsConstructor
@Service
public class TRepTargetServiceImpl implements ITRepTargetService {

    private final TRepTargetMapper baseMapper;

    /**
     * 查询上报指标
     */
    @Override
    public TRepTargetVo queryById(Long repTargetId) {
        return baseMapper.selectVoById(repTargetId);
    }

    /**
     * 查询上报指标列表
     */
    @Override
    public TableDataInfo<TRepTargetVo> queryPageList(TRepTargetBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<TRepTarget> lqw = buildQueryWrapper(bo);
        Page<TRepTargetVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询上报指标列表
     */
    @Override
    public List<TRepTargetVo> queryList(TRepTargetBo bo) {
        LambdaQueryWrapper<TRepTarget> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<TRepTarget> buildQueryWrapper(TRepTargetBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<TRepTarget> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getRepTargetId() != null, TRepTarget::getRepTargetId, bo.getRepTargetId());
        lqw.eq(StringUtils.isNotBlank(bo.getNote()), TRepTarget::getNote, bo.getNote());
        lqw.like(StringUtils.isNotBlank(bo.getTarName()), TRepTarget::getTarName, bo.getTarName());
        lqw.eq(bo.getTarValueType() != null, TRepTarget::getTarValueType, bo.getTarValueType());
        lqw.eq(StringUtils.isNotBlank(bo.getTarCode()), TRepTarget::getTarCode, bo.getTarCode());
        lqw.eq(bo.getTarType() != null, TRepTarget::getTarType, bo.getTarType());
        return lqw;
    }

    /**
     * 新增上报指标
     */
    @Override
    public Boolean insertByBo(TRepTargetBo bo) {
        TRepTarget add = BeanUtil.toBean(bo, TRepTarget.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setRepTargetId(add.getRepTargetId());
        }
        return flag;
    }

    /**
     * 修改上报指标
     */
    @Override
    public Boolean updateByBo(TRepTargetBo bo) {
        TRepTarget update = BeanUtil.toBean(bo, TRepTarget.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(TRepTarget entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除上报指标
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
