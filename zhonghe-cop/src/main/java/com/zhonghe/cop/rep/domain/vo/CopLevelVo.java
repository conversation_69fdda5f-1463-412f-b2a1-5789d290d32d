package com.zhonghe.cop.rep.domain.vo;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: lpg
 * @Date: 2023/12/17/09:09
 * @Description:
 */
@Data
@NoArgsConstructor
public class CopLevelVo implements Serializable, TransPojo {

    private static final long serialVersionUID = 4314183886210506088L;
    private Map<String, Object> transMap = new HashMap<>();
    /**
     * 风险级别ID
     */
    @Trans(type = TransType.DICTIONARY, target = CopLevelVo.class, key = "cop_level", ref = "levelName")
    private Integer level;

    /**
     * 风险级别名称
     */
    private String levelName;

    /**
     * 总数
     */
    private Integer count;

    public CopLevelVo(Integer level, String levelName, Integer count) {
        this.level = level;
        this.levelName = levelName;
        this.count = count;
    }
}
