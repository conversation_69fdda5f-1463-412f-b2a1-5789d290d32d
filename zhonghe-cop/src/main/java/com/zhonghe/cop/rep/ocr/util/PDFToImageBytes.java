package com.zhonghe.cop.rep.ocr.util;

import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.ImageType;
import org.apache.pdfbox.rendering.PDFRenderer;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: lpg
 * @Date: 2025/07/17
 * @Description:
 */
public class PDFToImageBytes {
    /**
     * 将PDF转换为适合OCR识别的图片字节数组
     *
     * @param pdfFile PDF文件
     * @return 每页对应的图片字节数组列表
     */
    public static List<byte[]> convertPDFToImageBytes(InputStream pdfFile) throws IOException {
        List<byte[]> imageBytesList = new ArrayList<>();

        try (PDDocument document = PDDocument.load(pdfFile)) {
            PDFRenderer pdfRenderer = new PDFRenderer(document);

            for (int page = 0; page < document.getNumberOfPages(); page++) {
                // OCR推荐使用300 DPI，灰度图像
                BufferedImage bufferedImage = pdfRenderer.renderImageWithDPI(page, 100, ImageType.GRAY);

                // 转换为PNG格式（无损压缩，适合OCR）
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                ImageIO.write(bufferedImage, "png", baos);
                baos.flush();

                byte[] imageBytes = baos.toByteArray();
                imageBytesList.add(imageBytes);
                baos.close();
            }
        }

        return imageBytesList;
    }
}
