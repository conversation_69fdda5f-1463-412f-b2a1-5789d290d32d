package com.zhonghe.cop.rep.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 高危企业视图对象 high_risk_enterprises
 *
 * <AUTHOR>
 * @date 2023-12-15
 */
@Data
@ApiModel("高危企业视图对象")
@ExcelIgnoreUnannotated
public class HighRiskEnterprisesVo {

    private static final long serialVersionUID = 1L;

    /**
     * 企业ID
     */
    @ExcelProperty(value = "企业ID")
    @ApiModelProperty("企业ID")
    private Long enterpriseId;

    /**
     * 企业名称
     */
    @ExcelProperty(value = "企业名称")
    @ApiModelProperty("企业名称")
    private String enterpriseName;

    /**
     * 企业法人
     */
    @ExcelProperty(value = "企业法人")
    @ApiModelProperty("企业法人")
    private String legalPerson;

    /**
     * 联系电话
     */
    @ExcelProperty(value = "联系电话")
    @ApiModelProperty("联系电话")
    private String contactPhone;

    /**
     * 企业地址
     */
    @ExcelProperty(value = "企业地址")
    @ApiModelProperty("企业地址")
    private String enterpriseAddress;

    /**
     * 企业性质
     */
    @ExcelProperty(value = "企业性质")
    @ApiModelProperty("企业性质")
    private String enterpriseProperty;


}
