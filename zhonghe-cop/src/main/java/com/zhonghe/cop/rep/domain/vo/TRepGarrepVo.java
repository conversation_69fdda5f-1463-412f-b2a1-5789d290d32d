package com.zhonghe.cop.rep.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;


/**
 * 汇总报，指标字段根据动态添加视图对象 t_rep_garrep
 *
 * <AUTHOR>
 * @date 2023-12-06
 */
@Data
@ApiModel("汇总报表信息对象")
public class TRepGarrepVo {

    private static final long serialVersionUID = 1L;

    /**
     * 汇总报表ID
     */
    @ApiModelProperty("汇总报表ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long repGarrepId;

    /**
     * 企业ID
     */
    @ApiModelProperty("企业ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long copId;
    /**
     * 上报日期
     */
    @ApiModelProperty("上报日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date repDate;

    /**
     * 最终风险等级（默认0，0——正常、1——问题、2——风险险、3——危险、4——高危）
     */
    @ApiModelProperty("最终风险等级（默认0，0——正常、1——问题、2——风险险、3——危险、4——高危）")
    @JsonSerialize(using = ToStringSerializer.class)
    private Integer level;

    /**
     * 拖欠工资月数（月）
     */
    @ApiModelProperty("拖欠工资月数（月）")
    private BigDecimal propaymonth;

    /**
     * 拖欠工资涉及人数（人）
     */
    @ApiModelProperty("拖欠工资涉及人数（人）")
    private BigDecimal propaypopulation;

    /**
     * 拖欠工资金额（元）
     */
    @ApiModelProperty("拖欠工资金额（元）")
    private BigDecimal propaymoney;

    /**
     * 拖欠税款月数（月）
     */
    @ApiModelProperty("拖欠税款月数（月）")
    private BigDecimal protaxmonth;

    /**
     * 拖欠税款金额（元）
     */
    @ApiModelProperty("拖欠税款金额（元）")
    private BigDecimal protaxmoney;

    /**
     * 拖欠社保月数（月）
     */
    @ApiModelProperty("拖欠社保月数（月）")
    private BigDecimal proinsuremonth;

    /**
     * 拖欠社保金额（元）
     */
    @ApiModelProperty("拖欠社保金额（元）")
    private BigDecimal proinsuremoney;

    /**
     * 拖欠水费月数（月）
     */
    @ApiModelProperty("拖欠水费月数（月）")
    private BigDecimal prowatermonth;

    /**
     * 拖欠水费金额（元）
     */
    @ApiModelProperty("拖欠水费金额（元）")
    private BigDecimal prowatermoney;

    /**
     * 拖欠电费月数（月）
     */
    @ApiModelProperty("拖欠电费月数（月）")
    private BigDecimal proenergymonth;

    /**
     * 拖欠电费金额（元）
     */
    @ApiModelProperty("拖欠电费金额（元）")
    private BigDecimal proenergymoney;

    /**
     * 拖欠租金月数（月）
     */
    @ApiModelProperty("拖欠租金月数（月）")
    private BigDecimal prorentmonth;

    /**
     * 拖欠租金金额（元）
     */
    @ApiModelProperty("拖欠租金金额（元）")
    private BigDecimal prorentmoney;

    /**
     * 存在问题
     */
    @ApiModelProperty("存在问题")
    private String note;

    /**
     * 其他问题
     */
    @ApiModelProperty("其他问题")
    private String problem;


    /**
     * 监管措施
     */
    @ApiModelProperty("监管措施")
    private String copoversee;

    /**
     * 建议措施
     */
    @ApiModelProperty("建议措施")
    private String copadvice;

    /**
     * 采取措施
     */
    @ApiModelProperty("采取措施")
    private String copdeal;

    /**
     * 企业现状
     */
    @ApiModelProperty("企业现状")
    private String copresult;

    /**
     * 后续工作
     */
    @ApiModelProperty("后续工作")
    private String copafterwork;

    /**
     * 请求协助
     */
    @ApiModelProperty("请求协助")
    private String cophelp;

}
