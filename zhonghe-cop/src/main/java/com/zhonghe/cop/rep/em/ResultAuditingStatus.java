package com.zhonghe.cop.rep.em;

import lombok.Getter;

/**
 * @Author: lpg
 * @Date: 2023/12/07
 * @Description: 处理结果审核状态
 */
@Getter
public enum ResultAuditingStatus {
    /**
     * 默认0,0-未处理、1-未审核、2-已审核、3-退回
     */
    UNHANDLED(0, "未处理"),
    UNAUDITED(1, "未审核"),
    AUDITED(2, "已审核"),
    RETURNED(3, "退回");

    private final Integer code;
    private final String name;

    ResultAuditingStatus(Integer code, String name) {
        this.code = code;
        this.name = name;
    }
}
