package com.zhonghe.cop.rep.mapper;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhonghe.common.core.mapper.BaseMapperPlus;
import com.zhonghe.cop.rep.domain.TExiReport;
import com.zhonghe.cop.rep.domain.vo.TExiReportVo;
import com.zhonghe.cop.rep.domain.vo.table.TExiReportTableVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 紧急事件上报Mapper接口
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
public interface TExiReportMapper extends BaseMapperPlus<TExiReportMapper, TExiReport, TExiReportVo> {

    /**
     * 分页查询紧急事件上报列表
     *
     * @param build
     * @param lqw
     * @return
     */
    Page<TExiReportTableVo> selectExiReportList(Page<TExiReport> build, @Param(Constants.WRAPPER) Wrapper<TExiReport> lqw);

    /**
     * 查询紧急事件上报列表
     *
     * @param lqw
     * @return
     */
    List<TExiReportTableVo> selectExiReportList(@Param(Constants.WRAPPER) Wrapper<TExiReport> lqw);
}
