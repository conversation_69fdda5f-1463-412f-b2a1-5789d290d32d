package com.zhonghe.cop.rep.service;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zhonghe.cop.rep.domain.TCopUtility;
import com.zhonghe.cop.rep.domain.TRepReport;
import com.zhonghe.cop.rep.mapper.TCopUtilityMapper;
import com.zhonghe.cop.rep.mapper.TRepReportMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Random;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Author: lpg
 * @Date: 2025/07/15
 * @Description:
 */
@RequiredArgsConstructor
@Service
public class GenCopUtilityService {

    private final TRepReportMapper repReportMapper;
    private final TCopUtilityMapper tCopUtilityMapper;

    public static List<TCopUtility> generateYearlyCosts(Long copId, int year) {
        List<TCopUtility> list = new ArrayList<>();
        Random random = new Random();

        for (int month = 1; month <= 12; month++) {
            TCopUtility cost = new TCopUtility();
            cost.setCopId(copId);

            // 设置年月日期
            LocalDate date = LocalDate.of(year, month, 1);
            Date yearMonth = java.sql.Date.valueOf(date);
            cost.setYearMonth(yearMonth);

            // 季节性调整因子
            double seasonalFactor = getSeasonalFactor(month);

            // 基础用量
            BigDecimal baseWaterUsage = new BigDecimal("2500");
            BigDecimal baseElectricityUsage = new BigDecimal("50000");

            // 生成实际用量（基础用量 * 季节系数 * 随机波动 80%-120%）
            BigDecimal waterUsage = baseWaterUsage
                .multiply(BigDecimal.valueOf(seasonalFactor))
                .multiply(BigDecimal.valueOf(0.8 + random.nextDouble() * 0.4))
                .setScale(2, RoundingMode.HALF_UP);

            BigDecimal electricityUsage = baseElectricityUsage
                .multiply(BigDecimal.valueOf(seasonalFactor))
                .multiply(BigDecimal.valueOf(0.8 + random.nextDouble() * 0.4))
                .setScale(2, RoundingMode.HALF_UP);

            // 设置用量
            cost.setWaterUsage(waterUsage);
            cost.setElectricityUsage(electricityUsage);

            // 计算费用（水费2.5元/立方米，电费0.8元/千瓦时）
            BigDecimal waterCost = waterUsage.multiply(BigDecimal.valueOf(2.5)).setScale(2, RoundingMode.HALF_UP);
            BigDecimal electricityCost = electricityUsage.multiply(BigDecimal.valueOf(0.8)).setScale(2, RoundingMode.HALF_UP);

            cost.setWaterCost(waterCost);
            cost.setElectricityCost(electricityCost);
            cost.setDataSource("0");
            list.add(cost);
        }

        return list;
    }

    // 季节性因子
    private static double getSeasonalFactor(int month) {
        switch (month) {
            case 12:
            case 1:
            case 2:
                return 1.1; // 冬季
            case 6:
            case 7:
            case 8:
                return 1.3; // 夏季
            case 3:
            case 4:
            case 5:
            case 9:
            case 10:
            case 11:
                return 0.9; // 春秋季
            default:
                return 1.0;
        }
    }

    public void doGenData(String year) {
        List<TRepReport> tRepReports = repReportMapper.selectList(Wrappers.<TRepReport>lambdaQuery().apply("to_char(rep_date,'yyyy') = {0}", String.valueOf(year)));

        Set<Long> collect = tRepReports.stream().map(TRepReport::getCopId).collect(Collectors.toSet());


        for (Long copId : collect) {
            List<TCopUtility> costs = generateYearlyCosts(copId, Integer.parseInt(year));

            costs.forEach(cost ->
                System.out.printf("企业ID: %s, 年月: %s, 水费: %s, 电费: %s%n",
                    cost.getCopId(), cost.getYearMonth(), cost.getWaterCost(), cost.getElectricityCost())
            );
            tCopUtilityMapper.insertBatch(costs);
        }
    }
}
