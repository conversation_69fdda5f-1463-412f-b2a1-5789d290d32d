package com.zhonghe.cop.rep.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.zhonghe.common.core.controller.BaseController;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.cop.rep.domain.vo.MonthReportVo;
import com.zhonghe.cop.rep.domain.vo.QuarterReportVo;
import com.zhonghe.cop.rep.domain.vo.YearReportVo;
import com.zhonghe.cop.rep.service.ITRepReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 报表统计
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
@Validated
@Api(value = "报表统计控制器", tags = {"报表统计管理"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/cop/repStatistics")
public class RepStatisticsController extends BaseController {

    private final ITRepReportService itRepReportService;


    /**
     * 查询月份案件汇总
     */
    @ApiOperation("查询月份案件汇总(cop:rep:statistics:monthReport)")
    @SaCheckPermission("cop:rep:statistics:monthReport")
    @GetMapping("/monthReport")
    public R<List<MonthReportVo>> queryYearReport(@ApiParam("年份") @NotNull(message = "年份不能为空") @RequestParam(value = "year", required = false) Integer year) {
        return R.ok(itRepReportService.queryMonthReport(year));
    }

    /**
     * 查询季度案件汇总
     */
    @ApiOperation("查询季度案件汇总(cop:rep:statistics:quarterReport)")
    @SaCheckPermission("cop:rep:statistics:quarterReport")
    @GetMapping("/quarterReport")
    public R<List<QuarterReportVo>> queryQuarterReport(@ApiParam("年份") @NotNull(message = "年份不能为空") @RequestParam(value = "year", required = false) Integer year) {
        return R.ok(itRepReportService.queryQuarterReport(year));
    }

    /**
     * 查询年度案件汇总
     */
    @ApiOperation("查询年度案件汇总(cop:rep:statistics:yearReport)")
    @SaCheckPermission("cop:rep:statistics:yearReport")
    @GetMapping("/yearReport")
    public R<List<YearReportVo>> queryYearReport(@ApiParam("开始年份") @NotNull(message = "开始年份不能为空") @RequestParam(value = "startYear", required = false) Integer startYear,
                                                 @ApiParam("结束年份") @NotNull(message = "结束年份不能为空") @RequestParam(value = "endYear", required = false) Integer endYear) {
        return R.ok(itRepReportService.queryYearReport(startYear, endYear));
    }
}
