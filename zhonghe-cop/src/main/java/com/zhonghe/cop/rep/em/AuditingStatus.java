package com.zhonghe.cop.rep.em;

import lombok.Getter;

/**
 * @Author: lpg
 * @Date: 2023/12/07
 * @Description: 审核状态
 */
@Getter
public enum AuditingStatus {
    /**
     * 0——未审核、1——已审核、2退回
     */
    UNAUDITED(0, "未审核"),
    AUDITED(1, "已审核"),
    RETURNED(2, "退回");

    private final Integer code;
    private final String name;

    AuditingStatus(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

}
