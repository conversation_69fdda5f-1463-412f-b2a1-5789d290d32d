package com.zhonghe.cop.rep.domain.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zhonghe.framework.config.StripTrailingZerosBigDecimalSerializer;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: lpg
 * @Date: 2025/08/11
 * @Description:
 */
@Data
public class TRepReportProPayVo {
    private static final long serialVersionUID = 7467904079264737797L;

    private Long repReportPropayId;
    private Long repReportId;
    @JsonSerialize(using = StripTrailingZerosBigDecimalSerializer.class)
    private BigDecimal propaymonth;
    private String propaymonths;
    @JsonSerialize(using = StripTrailingZerosBigDecimalSerializer.class)
    private BigDecimal propaypopulation;
    private BigDecimal propaymoney;
    private String propaytype;
}
