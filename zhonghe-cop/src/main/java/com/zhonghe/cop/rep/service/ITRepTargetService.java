package com.zhonghe.cop.rep.service;


import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.cop.rep.domain.bo.TRepTargetBo;
import com.zhonghe.cop.rep.domain.vo.TRepTargetVo;

import java.util.Collection;
import java.util.List;

/**
 * 上报指标Service接口
 *
 * <AUTHOR>
 * @date 2023-12-06
 */
public interface ITRepTargetService {

    /**
     * 查询上报指标
     */
    TRepTargetVo queryById(Long repTargetId);

    /**
     * 查询上报指标列表
     */
    TableDataInfo<TRepTargetVo> queryPageList(TRepTargetBo bo, PageQuery pageQuery);

    /**
     * 查询上报指标列表
     */
    List<TRepTargetVo> queryList(TRepTargetBo bo);

    /**
     * 修改上报指标
     */
    Boolean insertByBo(TRepTargetBo bo);

    /**
     * 修改上报指标
     */
    Boolean updateByBo(TRepTargetBo bo);

    /**
     * 校验并批量删除上报指标信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
