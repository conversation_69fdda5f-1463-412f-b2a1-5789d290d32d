package com.zhonghe.cop.rep.domain.vo.table;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentStyle;
import com.alibaba.excel.annotation.write.style.HeadStyle;
import com.alibaba.excel.enums.BooleanEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.zhonghe.common.annotation.ExcelDictFormat;
import com.zhonghe.common.convert.ExcelDictConvert;
import com.zhonghe.framework.config.StripTrailingZerosBigDecimalSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;


/**
 * 汇总报表
 *
 * <AUTHOR>
 * @date 2023-12-06
 */
@ContentStyle(wrapped = BooleanEnum.TRUE)
@HeadStyle(fillForegroundColor = 9)
@ColumnWidth(30)
@Data
@ApiModel("汇总报表视图对象")
@ExcelIgnoreUnannotated
public class TRepGarrepTableVo {

    private static final long serialVersionUID = 1L;

    private static final String head1 = "防范企业欠薪逃匿风险预警月报表（汇总表）";
    private static final String head2 = "汇总年月：${repDate}";
    private static final String head3 = "填报人：　　　　　　　　　　　　 　　 填表日期：${exportDate}　　　　　　　　　　　　 内部资料 禁止外传";
    /**
     * 汇总报表ID
     */
    @ApiModelProperty("汇总报表ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long repGarrepId;

    /**
     * 序号
     */
    @ExcelProperty(value = {head1, head2, head3, "序号"})
    @ApiModelProperty("序号")
    private Integer index;
    /**
     * 企业ID
     */
    private Long copId;

    /**
     * 企业名称
     */
    @ExcelProperty({head1, head2, head3, "企业名称"})
    @ApiModelProperty("企业名称")
    private String copName;

    /**
     * 企业性质
     */
    @ExcelProperty(value = {head1, head2, head3, "企业性质"}, converter = ExcelDictConvert.class)
    @ExcelDictFormat(dictType = "cop_property")
    @ApiModelProperty("企业性质ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Integer propertyId;

    /**
     * 企业地址
     */
    @ExcelProperty({head1, head2, head3, "企业地址"})
    @ApiModelProperty("企业地址")
    private String copAddress;

    /**
     * 企业法人
     */
    @ExcelProperty({head1, head2, head3, "企业法人"})
    @ApiModelProperty("企业法人")
    private String copOwner;


    /**
     * 企业电话
     */
    @ExcelProperty({head1, head2, head3, "企业电话"})
    @ApiModelProperty("企业电话")
    private String copPhone;

    /**
     * 上报日期
     */
    //@ExcelProperty(value = {head1, head2, head3, "上报日期"}, converter = ExcelDateConvert.class)
    @ApiModelProperty("上报日期")
    @JsonFormat(pattern = "yyyy-MM")
    private Date repDate;
    /**
     * 拼接上报日期
     */
    @ExcelProperty(value = {head1, head2, head3, "上报日期"})
    private String repDateStr;

    /**
     * 存在问题
     */
    @ExcelProperty(value = {head1, head2, head3, "存在问题"})
    @ApiModelProperty("存在问题")
    private String note;


    /**
     * 风险等级（默认0，0——正常、1——问题、2——风险、3——危险、4——高危）
     */
    @ExcelProperty(value = {head1, head2, head3, "风险等级"}, converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "0=正常,1=问题,2=风险,3=危险,4=高危")
    @ApiModelProperty("风险等级（默认0，0——正常、1——问题、2——风险、3——危险、4——高危）")
    @JsonSerialize(using = ToStringSerializer.class)
    private Integer level;

    /**
     * 拖欠工资月数（月）
     */
    //@ExcelProperty(value = {head1, head2, head3, "拖欠工资月数（月）"})
    @JsonSerialize(using = StripTrailingZerosBigDecimalSerializer.class)
    private BigDecimal propaymonth;

    /**
     * 拖欠工资（月）
     */
    private String propaymonths;

    /**
     * 拖欠工资涉及人数（人）
     */
    //@ExcelProperty(value = {head1, head2, head3, "拖欠工资涉及人数（人）"})
    @JsonSerialize(using = StripTrailingZerosBigDecimalSerializer.class)
    private BigDecimal propaypopulation;

    /**
     * 拖欠工资金额（元）
     */
    //@ExcelProperty(value = {head1, head2, head3, "拖欠工资金额（元）"})
    private BigDecimal propaymoney;

    /**
     * 拖欠税款月数（月）
     */
    //@ExcelProperty(value = {head1, head2, head3, "拖欠税款月数（月）"})
    @JsonSerialize(using = StripTrailingZerosBigDecimalSerializer.class)
    private BigDecimal protaxmonth;

    /**
     * 拖欠税款（月）
     */
    private String protaxmonths;

    /**
     * 拖欠税款金额（元）
     */
    //@ExcelProperty(value = {head1, head2, head3, "拖欠税款金额（元）"})
    private BigDecimal protaxmoney;

    /**
     * 拖欠社保月数（月）
     */
    //@ExcelProperty(value = {head1, head2, head3, "拖欠社保月数（月）"})
    @JsonSerialize(using = StripTrailingZerosBigDecimalSerializer.class)
    private BigDecimal proinsuremonth;

    /**
     * 拖欠社保（月）
     */
    private String proinsuremonths;

    /**
     * 拖欠社保金额（元）
     */
    //@ExcelProperty(value = {head1, head2, head3, "拖欠社保金额（元）"})
    private BigDecimal proinsuremoney;

    /**
     * 拖欠水费月数（月）
     */
    //@ExcelProperty(value = {head1, head2, head3, "拖欠水费月数（月）"})
    @JsonSerialize(using = StripTrailingZerosBigDecimalSerializer.class)
    private BigDecimal prowatermonth;

    /**
     * 拖欠水费（月）
     */
    private String prowatermonths;

    /**
     * 拖欠水费金额（元）
     */
    //@ExcelProperty(value = {head1, head2, head3, "拖欠水费金额（元）"})
    private BigDecimal prowatermoney;

    /**
     * 拖欠电费月数（月）
     */
    //@ExcelProperty(value = {head1, head2, head3, "拖欠电费月数（月）"})
    @JsonSerialize(using = StripTrailingZerosBigDecimalSerializer.class)
    private BigDecimal proenergymonth;

    /**
     * 拖欠电费（月）
     */
    private String proenergymonths;

    /**
     * 拖欠电费金额（元）
     */
    //@ExcelProperty(value = {head1, head2, head3, "拖欠电费金额（元）"})
    private BigDecimal proenergymoney;

    /**
     * 拖欠租金月数（月）
     */
    //@ExcelProperty(value = {head1, head2, head3, "拖欠租金月数（月）"})
    @JsonSerialize(using = StripTrailingZerosBigDecimalSerializer.class)
    private BigDecimal prorentmonth;

    /**
     * 拖欠租金（月）
     */
    private String prorentmonths;

    /**
     * 拖欠租金金额（元）
     */
    //@ExcelProperty(value = {head1, head2, head3, "拖欠租金金额（元）"})
    private BigDecimal prorentmoney;

    /**
     * 其他
     */
    private String other;


    /**
     * 监控措施
     */
    @ExcelProperty(value = {head1, head2, head3, "监控措施"})
    private String copoversee;

    /**
     * 建议措施
     */
    @ExcelProperty(value = {head1, head2, head3, "建议措施"})
    private String copadvice;

    /**
     * 采取措施
     */
    @ExcelProperty(value = {head1, head2, head3, "采取措施"})
    private String copdeal;

    /**
     * 企业现状
     */
    @ExcelProperty(value = {head1, head2, head3, "企业现状"})
    private String copresult;

    /**
     * 后续工作
     */
    @ExcelProperty(value = {head1, head2, head3, "后续工作"})
    private String copafterwork;

    /**
     * 请求协助
     */
    @ExcelProperty(value = {head1, head2, head3, "请求协助"})
    private String cophelp;

    /**
     * 核查反馈
     */
    @ExcelProperty(value = {head1, head2, head3, "核查反馈"})
    private String problem;

    /**
     * 组织机构代码证
     */
    private String corporateCode;
    /**
     * 营业执照注册号
     */
    private String businessLicence;


}
