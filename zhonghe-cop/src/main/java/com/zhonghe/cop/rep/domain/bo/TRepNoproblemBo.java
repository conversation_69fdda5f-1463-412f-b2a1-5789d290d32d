package com.zhonghe.cop.rep.domain.bo;

import com.zhonghe.common.core.domain.BaseEntity;
import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 无异常上报业务对象 t_rep_noproblem
 *
 * <AUTHOR>
 * @date 2024-01-23
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("无异常上报业务对象")
public class TRepNoproblemBo extends BaseEntity {

    private static final long serialVersionUID = 7442271060756605693L;
    /**
     * 无异常上报ID
     */
    @ApiModelProperty(value = "无异常上报ID", required = true)
    @NotNull(message = "无异常上报ID不能为空", groups = {EditGroup.class})
    private Long repNoproblemId;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注", required = true)
    @NotBlank(message = "备注不能为空", groups = {AddGroup.class, EditGroup.class})
    private String note;

    /**
     * 上报日期（月份）
     */
    @ApiModelProperty(value = "上报日期（月份）", required = true)
    @NotNull(message = "上报日期（月份）不能为空", groups = {AddGroup.class, EditGroup.class})
    private Date repDate;

    /**
     * 上报用户ID
     */
    @ApiModelProperty(value = "上报用户ID", required = true)
    @NotNull(message = "上报用户ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long userId;

    /**
     * 部门ID
     */
    @ApiModelProperty(value = "部门ID", required = true)
    @NotNull(message = "部门ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long deptId;

    /**
     * 审核状态（默认0，0——未审核、1——已审核、2退回）
     */
    @ApiModelProperty(value = "审核状态（默认0，0——未审核、1——已审核、2退回）", required = true)
    @NotNull(message = "审核状态（默认0，0——未审核、1——已审核、2退回）不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long auditing;


}
