package com.zhonghe.cop.rep.service;

import com.zhonghe.cop.rep.domain.vo.OpenMonthReport;
import com.zhonghe.cop.rep.domain.vo.OpenPerformance;
import com.zhonghe.cop.rep.domain.vo.QueryWarningVo;

import java.util.List;

/**
 * @Author: lpg
 * @Date: 2024/03/07
 * @Description:
 */
public interface ICopOpenService {
    /**
     * 获取月报汇总数据
     *
     * @param repDate
     * @return
     */
    List<OpenMonthReport> queryMonthReport(String repDate);

    /**
     * 获取绩效汇总数据
     *
     * @param repYear
     * @return
     */
    List<OpenPerformance> queryPerformance(String repYear);

    /**
     * 查询预警信息
     *
     * @param sjny
     * @return
     */
    List<QueryWarningVo> queryWarn(String sjny);
}
