package com.zhonghe.cop.rep.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zhonghe.system.mappedtypes.GeometryTypeHandler;
import lombok.Data;
import net.postgis.jdbc.geometry.Geometry;

import java.io.Serializable;
import java.util.Date;


/**
 * 月度危险企业对象 month_dangerous_enterprises
 *
 * <AUTHOR>
 * @date 2023-12-15
 */
@Data
@TableName("cop.month_dangerous_enterprises")
public class MonthDangerousEnterprises implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 企业ID
     */
    @TableId(value = "enterprise_id")
    private Long enterpriseId;
    /**
     * 企业ID2
     */
    private Long enterpriseIdNo;
    /**
     * 问题类型
     */
    private String enterpriseType;
    /**
     * 企业名称
     */
    private String enterpriseName;
    /**
     * 企业法人
     */
    private String legalPerson;
    /**
     * 联系电话
     */
    private String contactPhone;
    /**
     * 企业地址
     */
    private String enterpriseAddress;
    /**
     * 企业性质
     */
    private String enterpriseProperty;

    @TableField(typeHandler = GeometryTypeHandler.class)
    private Geometry geom;
    /**
     * 上报人
     */
    private String repPerson;
    /**
     * 上报部门
     */
    private String repDept;
    /**
     * 上报人时间
     */
    private Date repDate;
    /**
     * 上报原因
     */
    private String repReason;


    /**
     * 其他问题
     */
    private String problem;

    /**
     * 监控措施
     */
    private String copoversee;

    /**
     * 建议措施
     */
    private String copadvice;

    /**
     * 采取措施
     */
    private String copdeal;

    /**
     * 企业现状
     */
    private String copresult;

    /**
     * 后续工作
     */
    private String copafterwork;

    /**
     * 请求协助
     */
    private String cophelp;

}
