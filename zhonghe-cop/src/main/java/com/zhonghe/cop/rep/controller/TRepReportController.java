package com.zhonghe.cop.rep.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaCheckPermission;
import com.zhonghe.common.annotation.Anonymous;
import com.zhonghe.common.annotation.Log;
import com.zhonghe.common.annotation.RepeatSubmit;
import com.zhonghe.common.core.controller.BaseController;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import com.zhonghe.common.enums.BusinessType;
import com.zhonghe.common.exception.ServiceException;
import com.zhonghe.common.utils.poi.ExcelUtil;
import com.zhonghe.cop.rep.domain.bo.HandDeptReportBo;
import com.zhonghe.cop.rep.domain.bo.RepNoProblemAuditBo;
import com.zhonghe.cop.rep.domain.bo.ReportAuditBo;
import com.zhonghe.cop.rep.domain.bo.ReportResultAuditBo;
import com.zhonghe.cop.rep.domain.bo.TRepReportBo;
import com.zhonghe.cop.rep.domain.dto.DeptTargetDto;
import com.zhonghe.cop.rep.domain.excel.TRepReportExcel;
import com.zhonghe.cop.rep.domain.query.TRepReportQuery;
import com.zhonghe.cop.rep.domain.vo.TRepGarrepInfoVo;
import com.zhonghe.cop.rep.domain.vo.TRepGarrepMapVo;
import com.zhonghe.cop.rep.domain.vo.TRepNoproblemVo;
import com.zhonghe.cop.rep.domain.vo.TRepReportInfoVo;
import com.zhonghe.cop.rep.domain.vo.table.TRepReportTableVo;
import com.zhonghe.cop.rep.service.GenCopUtilityService;
import com.zhonghe.cop.rep.service.ITRepNoproblemService;
import com.zhonghe.cop.rep.service.ITRepReportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 部门上报
 *
 * <AUTHOR>
 * @date 2023-12-06
 */
@Validated
@Api(value = "部门上报控制器", tags = {"部门上报，根据指标动态生成部门上报大报，然后上报时同时插入，大报用来查询，本作数据根备份管理"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/cop/repReport")
public class TRepReportController extends BaseController {

    private final ITRepReportService iTRepReportService;
    private final ITRepNoproblemService repNoproblemService;
    private final GenCopUtilityService genCopUtilityService;

    /**
     * 查询部门上报
     */
    @ApiOperation("查询部门上报(cop:rep:report:list)")
    @SaCheckPermission("cop:rep:report:list")
    @GetMapping("/list")
    public TableDataInfo<TRepReportTableVo> list(TRepReportQuery bo, PageQuery pageQuery) {
        return iTRepReportService.queryPageList(bo, pageQuery);
    }

    /**
     * 上报模板下载
     */
    @ApiOperation("模板下载")
    @SaCheckLogin
    @GetMapping("/template/download")
    public void templateDownload(HttpServletResponse response) {
        iTRepReportService.buildTempLate(response);
    }

    /**
     * 解析excel部门上报数据
     */
    @ApiOperation("解析excel部门上报数据(cop:rep:report:import)")
    @SaCheckPermission("cop:rep:report:import")
    @Log(title = "解析excel部门上报数据", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/importFile")
    public R<Map<String, Object>> importFile(@RequestParam("file") MultipartFile file) {
        // 获取其后缀
        String extension = Objects.requireNonNull(file.getOriginalFilename()).substring(file.getOriginalFilename().lastIndexOf(".") + 1);
        if (!("xls".equals(extension) || "xlsx".equals(extension))) {
            throw new ServiceException("文件格式有误,请检查上传文件格式!");
        }
        return R.ok(iTRepReportService.importExcelFile(file));
    }

    /**
     * 批量导入部门上报数据
     */
    @ApiOperation("批量导入部门上报数据(cop:rep:report:import)")
    @SaCheckPermission("cop:rep:report:import")
    @Log(title = "批量导入部门上报数据", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/importData")
    public R<Void> importData(@RequestBody List<TRepReportExcel> tRepReportExcels) {
        return toAjax(iTRepReportService.importData(tRepReportExcels) ? 1 : 0);
    }

    /**
     * 导出部门上报
     */
    @ApiOperation("导出部门上报(cop:rep:report:export)")
    @SaCheckPermission("cop:rep:report:export")
    @Log(title = "导出部门上报", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(TRepReportQuery bo, HttpServletResponse response) {
        List<TRepReportTableVo> list = iTRepReportService.queryList(bo);
        ExcelUtil.exportExcel(list, "防范企业欠薪逃匿风险预警月报表", TRepReportTableVo.class, response);
    }

    /**
     * 获取部门上报详细信息
     */
    @ApiOperation("获取部门上报详细信息(cop:rep:report:query)")
    @SaCheckPermission("cop:rep:report:query")
    @GetMapping("/{repReportId}")
    public R<TRepReportInfoVo> getInfo(@ApiParam("主键")
                                       @NotNull(message = "主键不能为空")
                                       @PathVariable("repReportId") Long repReportId) {
        return R.ok(iTRepReportService.queryById(repReportId));
    }

    /**
     * (New)查询企业已上报的时间段
     */
    @ApiOperation("(New)查询企业已上报的时间段(cop:rep:report:query)")
    @SaCheckPermission("cop:rep:report:query")
    @GetMapping("/repDateRange/{copId}")
    public R<List<String>> queryRepDateRange(@ApiParam("主键")
                                             @NotNull(message = "主键不能为空")
                                             @PathVariable("copId") Long copId) {
        return R.ok(iTRepReportService.queryRepDateRange(copId));
    }

    /**
     * (New)查询企业上报历史记录
     */
    @ApiOperation("(New)查询企业上报历史记录(cop:rep:garrep:query)")
    @SaCheckPermission("cop:rep:garrep:query")
    @GetMapping("/repHistory/{copId}")
    public R<TRepGarrepInfoVo> queryRepHistory(@ApiParam("主键")
                                               @NotNull(message = "主键不能为空")
                                               @PathVariable("copId") Long copId,
                                               @RequestParam("repDate") String repDate) {
        return R.ok(iTRepReportService.queryRepHistory(copId, repDate));
    }

    /**
     * 根据企业ID查询最新一条上报信息
     */
    @ApiOperation("根据企业ID查询最新一条上报信息(cop:rep:report:query)")
    @SaCheckPermission("cop:rep:report:query")
    @GetMapping("/first/{copId}")
    public R<TRepGarrepMapVo> queryFirstByCopId(@ApiParam("企业ID")
                                                @NotNull(message = "企业ID不能为空")
                                                @PathVariable("copId") Long copId, @NotBlank(message = "me") @ApiParam("年月") @RequestParam String yearMonth) {
        return R.ok(iTRepReportService.queryFirstByCopId(copId, yearMonth));
    }

    /**
     * 新增部门上报
     */
    @ApiOperation("新增部门上报(cop:rep:report:add)")
    @SaCheckPermission("cop:rep:report:add")
    @Log(title = "新增部门上报", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody TRepReportBo bo) {
        return toAjax(iTRepReportService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改部门上报
     */
    @ApiOperation("修改部门上报(cop:rep:report:edit)")
    @SaCheckPermission("cop:rep:report:edit")
    @Log(title = "部门上报", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody TRepReportBo bo) {
        return toAjax(iTRepReportService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除部门上报
     */
    @ApiOperation("删除部门上报(cop:rep:report:remove)")
    @SaCheckPermission("cop:rep:report:remove")
    @Log(title = "部门上报", businessType = BusinessType.DELETE)
    @DeleteMapping("/{repReportIds}")
    public R<Void> remove(@ApiParam("主键串")
                          @NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] repReportIds) {
        return toAjax(iTRepReportService.deleteWithValidByIds(Arrays.asList(repReportIds), true) ? 1 : 0);
    }

    /**
     * 处理上报
     */
    @ApiOperation("处理上报(cop:rep:report:handle)")
    @SaCheckPermission("cop:rep:report:handle")
    @Log(title = "处理上报", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/handle/{repReportId}")
    public R<Void> handleReport(@Validated @RequestBody HandDeptReportBo bo, @Validated @NotNull @PathVariable("repReportId") @ApiParam("部门上报ID") Long repReportId) {
        return toAjax(iTRepReportService.handleReport(bo, repReportId) ? 1 : 0);
    }

    /**
     * 处理完成
     */
    @ApiOperation("处理完成(cop:rep:report:handle)")
    @SaCheckPermission("cop:rep:report:handle")
    @Log(title = "处理完成", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/handleFinishReport/{repReportId}")
    public R<Void> handleFinishReport(@Validated @NotNull @PathVariable("repReportId") @ApiParam("部门上报ID") Long repReportId) {
        return toAjax(iTRepReportService.handleFinishReport(repReportId) ? 1 : 0);
    }

    /**
     * 本月无异常上报
     */
    @ApiOperation("本月无异常上报(cop:rep:report:noException)")
    @SaCheckPermission("cop:rep:report:noException")
    @Log(title = "本月无异常上报", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/noException")
    public R<Void> noException() {
        return toAjax(iTRepReportService.noException() ? 1 : 0);
    }

    /**
     * 查询部门指标选项
     */
    @ApiOperation("查询部门指标选项")
    @Anonymous
    @GetMapping("/target")
    public R<List<DeptTargetDto>> queryTarget() {
        return R.ok(iTRepReportService.queryTarget());
    }

    /**
     * 报表审核
     */
    @ApiOperation("报表审核(cop:rep:report:audit)")
    @SaCheckPermission("cop:rep:report:audit")
    @Log(title = "报表审核", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/audit")
    public R<Void> audit(@Validated @RequestBody ReportAuditBo bo) {
        return toAjax(iTRepReportService.audit(bo) ? 1 : 0);
    }

    /**
     * 审核退回
     */
    @ApiOperation("审核退回(cop:rep:report:audit)")
    @SaCheckPermission("cop:rep:report:audit")
    @Log(title = "审核退回", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/auditBack/{repReportId}")
    public R<Void> auditBack(@Validated @NotNull(message = "部门上报ID不能为空") @PathVariable("repReportId") @ApiParam("部门上报ID") Long repReportId) {
        return toAjax(iTRepReportService.auditBack(repReportId) ? 1 : 0);
    }

    /**
     * 处理结果审核
     */
    @ApiOperation("处理结果审核(cop:rep:report:audit)")
    @SaCheckPermission("cop:rep:report:audit")
    @Log(title = "处理结果审核", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/resultAudit")
    public R<Void> resultAudit(@Validated @RequestBody ReportResultAuditBo bo) {
        return toAjax(iTRepReportService.resultAudit(bo) ? 1 : 0);
    }

    /**
     * 查询部门无异常上报信息
     */
    @ApiOperation("查询部门无异常上报信息(cop:rep:report:query)")
    @SaCheckPermission("cop:rep:report:query")
    @GetMapping("/queryNoProblem")
    public R<TRepNoproblemVo> queryNoProblem() {
        return R.ok(repNoproblemService.queryNoProblem());
    }

    /**
     * 审核部门无异常上报
     */
    @ApiOperation("审核部门无异常上报(cop:rep:report:audit)")
    @SaCheckPermission("cop:rep:report:audit")
    @Log(title = "审核部门无异常上报", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/auditNoProblem")
    public R<Void> auditNoProblem(@Validated @RequestBody RepNoProblemAuditBo bo) {
        return toAjax(repNoproblemService.auditNoProblem(bo) ? 1 : 0);
    }

    /**
     * 坐标转换
     *
     * @return
     */
    @GetMapping("/point")
    @Anonymous
    public R<Void> point() {
        return toAjax(iTRepReportService.point() ? 1 : 0);
    }

    /**
     * 坐标转换
     *
     * @return
     */
    @GetMapping("/point/{copId}")
    @Anonymous
    public R<Void> point(@PathVariable Long copId) {
        return toAjax(iTRepReportService.point(copId) ? 1 : 0);
    }

    /**
     * 图层数据同步
     */
    @GetMapping("/dataFormat")
    @Anonymous
    public R<Void> dataFormat(String year, String level) {
        return toAjax(iTRepReportService.dataFormat(year, level) ? 1 : 0);
    }

    /**
     * @param year
     * @return
     */
    @Anonymous
    @GetMapping("/genCopUtility")
    public R<String> genData(String year) {
        genCopUtilityService.doGenData(year);
        return R.ok();
    }
}
