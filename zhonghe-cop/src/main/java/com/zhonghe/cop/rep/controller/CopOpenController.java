package com.zhonghe.cop.rep.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import com.zhonghe.common.annotation.Anonymous;
import com.zhonghe.common.constant.Constants;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.common.core.domain.model.LoginBody;
import com.zhonghe.cop.rep.domain.vo.OpenMonthReport;
import com.zhonghe.cop.rep.domain.vo.OpenPerformance;
import com.zhonghe.cop.rep.domain.vo.QueryWarningVo;
import com.zhonghe.cop.rep.service.ICopOpenService;
import com.zhonghe.system.service.SysLoginService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Author: lpg
 * @Date: 2024/03/07
 * @Description: 对外接口
 */
@Validated
@Api(value = "对外接口", tags = {"对外接口"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/ent_risk/api")
public class CopOpenController {

    private final SysLoginService loginService;
    private final ICopOpenService copOpenService;

    /**
     * 获取token
     */
    @PostMapping("/token")
    @Anonymous
    public R<Map<String, Object>> getToken(@Validated LoginBody loginBody) {
        Map<String, Object> ajax = new HashMap<>();
        // 生成令牌
        String token = loginService.login(loginBody.getUsername(), loginBody.getPassword(), loginBody.getCode(),
            loginBody.getUuid());
        ajax.put(Constants.TOKEN, "Bearer " + token);
        return R.ok(ajax);
    }

    /**
     * 获取月报汇总数据接入接口
     */
    @SaCheckLogin
    @PostMapping("/monthreport")
    public R<List<OpenMonthReport>> queryMonthReport(@ApiParam("汇总日期") @RequestParam(value = "repDate", required = false) String repDate) {
        return R.ok(copOpenService.queryMonthReport(repDate));
    }

    /**
     * 获取企业考核数据接口
     */
    @SaCheckLogin
    @PostMapping("/performance")
    public R<List<OpenPerformance>> queryPerformance(@ApiParam("年") @RequestParam(value = "repYear", required = false) String repYear) {
        return R.ok(copOpenService.queryPerformance(repYear));
    }

    /**
     * --------------------------------------------------------东莞市镇街预警信息---------------------------------------------------------
     */
    /**
     * 预警信息接入接口
     *
     * @param sjny 202406
     * @return
     */
    @ApiOperation("预警信息接入接口")
    @SaCheckLogin
    @PostMapping("/queryWarn")
    public R<List<QueryWarningVo>> queryWarn(@ApiParam("数据年月") @RequestParam(value = "sjny") String sjny) {
        return R.ok(copOpenService.queryWarn(sjny));
    }
}
