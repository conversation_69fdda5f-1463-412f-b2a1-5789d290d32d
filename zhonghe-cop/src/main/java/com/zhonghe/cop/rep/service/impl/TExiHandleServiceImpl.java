package com.zhonghe.cop.rep.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.cop.rep.domain.TExiHandle;
import com.zhonghe.cop.rep.domain.bo.TExiHandleBo;
import com.zhonghe.cop.rep.domain.vo.TExiHandleVo;
import com.zhonghe.cop.rep.mapper.TExiHandleMapper;
import com.zhonghe.cop.rep.service.ITExiHandleService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 紧急事件处理Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
@RequiredArgsConstructor
@Service
public class TExiHandleServiceImpl implements ITExiHandleService {

    private final TExiHandleMapper baseMapper;

    /**
     * 查询紧急事件处理
     */
    @Override
    public TExiHandleVo queryById(Long exiHandleId) {
        return baseMapper.selectVoById(exiHandleId);
    }

    /**
     * 查询紧急事件处理列表
     */
    @Override
    public TableDataInfo<TExiHandleVo> queryPageList(TExiHandleBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<TExiHandle> lqw = buildQueryWrapper(bo);
        Page<TExiHandleVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询紧急事件处理列表
     */
    @Override
    public List<TExiHandleVo> queryList(TExiHandleBo bo) {
        LambdaQueryWrapper<TExiHandle> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<TExiHandle> buildQueryWrapper(TExiHandleBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<TExiHandle> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getExiHandleId() != null, TExiHandle::getExiHandleId, bo.getExiHandleId());
        lqw.eq(StringUtils.isNotBlank(bo.getNote()), TExiHandle::getNote, bo.getNote());
        lqw.eq(bo.getExiReportId() != null, TExiHandle::getExiReportId, bo.getExiReportId());
        lqw.eq(StringUtils.isNotBlank(bo.getDescription()), TExiHandle::getDescription, bo.getDescription());
        lqw.eq(StringUtils.isNotBlank(bo.getDespacho()), TExiHandle::getDespacho, bo.getDespacho());
        lqw.eq(StringUtils.isNotBlank(bo.getDepts()), TExiHandle::getDepts, bo.getDepts());
        return lqw;
    }

    /**
     * 新增紧急事件处理
     */
    @Override
    public Boolean insertByBo(TExiHandleBo bo) {
        TExiHandle add = BeanUtil.toBean(bo, TExiHandle.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setExiHandleId(add.getExiHandleId());
        }
        return flag;
    }

    /**
     * 修改紧急事件处理
     */
    @Override
    public Boolean updateByBo(TExiHandleBo bo) {
        TExiHandle update = BeanUtil.toBean(bo, TExiHandle.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(TExiHandle entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除紧急事件处理
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
