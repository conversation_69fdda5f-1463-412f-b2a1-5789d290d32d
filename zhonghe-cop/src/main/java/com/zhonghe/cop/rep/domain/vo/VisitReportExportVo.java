package com.zhonghe.cop.rep.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.DateTimeFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 信访上报导出对象
 *
 * <AUTHOR>
 * @date 2025-07-17
 */
@Data
@ApiModel("信访上报导出对象")
@ExcelIgnoreUnannotated
@ColumnWidth(30)
public class VisitReportExportVo implements Serializable {

    private static final long serialVersionUID = 1L;

    private static final String HEADER = "${inputDate}劳动争议信访来访人员登记表";
    private static final String REMARK = "备注";

    /**
     * 信访上报ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long visitReportId;

    /**
     * 案件号
     */
    @ExcelProperty(value = {HEADER, "案件号"})
    private String caseNumber;

    /**
     * 受案时间
     */
    @ExcelProperty(value = {HEADER, "受案时间"})
    @JsonFormat(pattern = "yyyy-MM-dd")
    @DateTimeFormat(value = "yyyy-MM-dd")
    private Date visitorDate;

    /**
     * 投诉人
     */
    @ExcelProperty(value = {HEADER, "投诉人"})
    private String cptName;

    /**
     * 电话
     */
    @ExcelProperty(value = {HEADER, "电话"})
    private String cptGender;

    /**
     * 投诉人电话
     */
    @ExcelProperty(value = {HEADER, "投诉人电话"})
    @ApiModelProperty("投诉人电话")
    private String cptPhone;

    /**
     * 人数
     */
    @ExcelProperty(value = {HEADER, "人数"})
    private Integer rspUnitPopulation;


    /**
     * 被投诉单位
     */
    @ExcelProperty(value = {HEADER, "被投诉单位"})
    private String rspUnit;


    /**
     * 单位负责人
     */
    @ExcelProperty(value = {HEADER, "单位负责人"})
    private String rspUnitIncharge;

    /**
     * 单位电话
     */
    @ExcelProperty(value = {HEADER, "单位电话"})
    private String rspUnitPhone;

    /**
     * 申诉内容和投诉请求
     */
    @ExcelProperty(value = {HEADER, "申诉内容和要求"})
    private String cptContent;

    /**
     * 涉案金额
     */
    @ExcelProperty(value = {HEADER, "涉案金额"})
    private String appealContent;


    /**
     * 处理结果
     */
    @ExcelProperty(value = {HEADER, "处理结果"})
    @ApiModelProperty("处理结果")
    private String handleResult;

    /**
     * 备注
     */
    @ExcelProperty(value = {REMARK})
    private String remark;
}

