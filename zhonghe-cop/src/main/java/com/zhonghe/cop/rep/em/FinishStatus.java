package com.zhonghe.cop.rep.em;

/**
 * @Author: lpg
 * @Date: 2024/02/29
 * @Description:
 */
public enum FinishStatus {
    /**
     * 未完成
     */
    UNFINISHED(0, "未完成"),
    /**
     * 已完成
     */
    FINISHED(1, "已完成");

    private Integer code;
    private String desc;

    FinishStatus(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
