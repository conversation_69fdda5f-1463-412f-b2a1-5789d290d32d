package com.zhonghe.cop.rep.domain.bo;

import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


/**
 * 紧急事件上报业务对象 t_exi_report
 *
 * <AUTHOR>
 * @date 2023-12-07
 */

@Data
@ApiModel("紧急事件上报业务对象")
public class TExiReportBo {

    private static final long serialVersionUID = -2852995041618615928L;
    /**
     * 紧急事件上报ID
     */
    @ApiModelProperty(value = "紧急事件上报ID", required = true)
    @NotNull(message = "紧急事件上报ID不能为空", groups = {EditGroup.class})
    private Long exiReportId;

    /**
     * 企业ID
     */
    @ApiModelProperty(value = "企业ID", required = true)
    @NotNull(message = "企业ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long copId;

    /**
     * 描述
     */
    @ApiModelProperty(value = "描述", required = true)
    @NotBlank(message = "描述不能为空", groups = {AddGroup.class, EditGroup.class})
    @Length(max = 2000, message = "描述长度不能超过2000个字符")
    private String description;


}
