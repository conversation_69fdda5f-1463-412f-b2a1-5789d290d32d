package com.zhonghe.cop.rep.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zhonghe.cop.other.domain.BmqzjProWoFormPg0vs;
import com.zhonghe.cop.other.domain.TDgsnSpecialTaskQs;
import com.zhonghe.cop.other.domain.TIntegrationClue;
import com.zhonghe.cop.other.domain.TTaskOrderNew;
import com.zhonghe.cop.other.domain.vo.BmqzjProWoFormPg0vsVo;
import com.zhonghe.cop.other.domain.vo.TDgsnSpecialTaskQsVo;
import com.zhonghe.cop.other.domain.vo.TIntegrationClueVo;
import com.zhonghe.cop.other.domain.vo.TTaskOrderNewVo;
import com.zhonghe.cop.other.mapper.BmqzjProWoFormPg0vsMapper;
import com.zhonghe.cop.other.mapper.TDgsnSpecialTaskQsMapper;
import com.zhonghe.cop.other.mapper.TIntegrationClueMapper;
import com.zhonghe.cop.other.mapper.TTaskOrderNewMapper;
import com.zhonghe.cop.rep.domain.vo.CopDataVo;
import com.zhonghe.cop.rep.service.IOtherService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author: lpg
 * @Date: 2025/07/14
 * @Description:
 */
@DS("data")
@Service
@RequiredArgsConstructor
public class OtherServiceImpl implements IOtherService {
    private final BmqzjProWoFormPg0vsMapper bmqzjProWoFormPg0vsMapper;
    private final TIntegrationClueMapper tIntegrationClueMapper;
    private final TDgsnSpecialTaskQsMapper tDgsnSpecialTaskQsMapper;
    private final TTaskOrderNewMapper tTaskOrderNewMapper;

    @Override
    public CopDataVo queryByCopName(String copName) {
        CopDataVo vo = new CopDataVo();
        List<BmqzjProWoFormPg0vsVo> bmqzjProWoFormFinishPgs = bmqzjProWoFormPg0vsMapper.selectVoList(Wrappers.<BmqzjProWoFormPg0vs>lambdaQuery()
            .like(BmqzjProWoFormPg0vs::getThingSubject, copName)
            .orderByDesc(BmqzjProWoFormPg0vs::getCreateTime));
        List<TIntegrationClueVo> tIntegrationClueVos = tIntegrationClueMapper.selectVoList(Wrappers.<TIntegrationClue>lambdaQuery().like(TIntegrationClue::getSubjectName, copName)
            .orderByDesc(TIntegrationClue::getCreateDate));
        List<TDgsnSpecialTaskQsVo> tDgsnSpecialTaskQsVos = tDgsnSpecialTaskQsMapper.selectVoList(Wrappers.<TDgsnSpecialTaskQs>lambdaQuery().like(TDgsnSpecialTaskQs::getEntityName, copName)
            .orderByDesc(TDgsnSpecialTaskQs::getCreateDate));
        List<TTaskOrderNewVo> tTaskOrderNewVos = tTaskOrderNewMapper.selectVoList(Wrappers.<TTaskOrderNew>lambdaQuery().like(TTaskOrderNew::getSubjectName, copName)
            .orderByDesc(TTaskOrderNew::getCreateDate));

        vo.setBmqzjProWoFormList(bmqzjProWoFormFinishPgs);
        vo.setIntegrationClueList(tIntegrationClueVos);
        vo.setDgsnSpecialTaskQsList(tDgsnSpecialTaskQsVos);
        vo.setTaskOrderNewList(tTaskOrderNewVos);

        return vo;
    }
}
