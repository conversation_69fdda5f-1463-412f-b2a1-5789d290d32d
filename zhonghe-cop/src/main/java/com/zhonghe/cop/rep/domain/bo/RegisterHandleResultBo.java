package com.zhonghe.cop.rep.domain.bo;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Author: lpg
 * @Date: 2025/07/17
 * @Description:
 */
@Data
public class RegisterHandleResultBo implements Serializable {
    private static final long serialVersionUID = 5445167467751505480L;

    @NotNull(message = "信访上报ID不能为空")
    private Long visitReportId;
    /**
     * 处理结果
     */
    private String handleResult;
}
