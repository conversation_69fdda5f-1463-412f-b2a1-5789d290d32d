package com.zhonghe.cop.knowledge.domain.bo;

import com.zhonghe.common.core.domain.BaseEntity;
import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


/**
 * 知识库业务对象 t_knowledge_base
 *
 * <AUTHOR>
 * @date 2025-08-11
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("知识库业务对象")
public class TKnowledgeBaseBo extends BaseEntity {

    private static final long serialVersionUID = -1569531508943313484L;
    /**
     * $column.columnComment
     */
    @ApiModelProperty(value = "$column.columnComment", required = true)
    @NotNull(message = "$column.columnComment不能为空", groups = {EditGroup.class})
    private Long knowledgeBaseId;

    /**
     * 法律文件名称
     */
    @ApiModelProperty(value = "法律文件名称", required = true)
    @NotBlank(message = "法律文件名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String legalDocumentName;

    /**
     * 作者
     */
    @ApiModelProperty(value = "作者", required = true)
    @NotBlank(message = "作者不能为空", groups = {AddGroup.class, EditGroup.class})
    private String author;

    /**
     * 知识库类型
     */
    @ApiModelProperty(value = "知识库类型", required = true)
    @NotBlank(message = "知识库类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private String knowledgeBaseType;

    /**
     * $column.columnComment
     */
    @ApiModelProperty(value = "$column.columnComment", required = true)
    @NotNull(message = "$column.columnComment不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long knowledgeBaseTypeId;

    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号", required = true)
    @NotBlank(message = "版本号不能为空", groups = {AddGroup.class, EditGroup.class})
    private String versionsNo;

    /**
     * 版本日期
     */
    @ApiModelProperty(value = "版本日期", required = true)
    @NotBlank(message = "版本日期不能为空", groups = {AddGroup.class, EditGroup.class})
    private String versionsTime;

    /**
     * 状态 0-启用 1-停用
     */
    @ApiModelProperty(value = "状态 0-启用 1-停用", required = true)
    @NotBlank(message = "状态 0-启用 1-停用不能为空", groups = {AddGroup.class, EditGroup.class})
    private String status;

    /**
     * 文件地址
     */
    @ApiModelProperty(value = "文件地址", required = true)
    @NotBlank(message = "文件地址不能为空", groups = {AddGroup.class, EditGroup.class})
    private String ossPath;

    /**
     * 向量库关联字段
     */
    @ApiModelProperty(value = "向量库关联字段", required = true)
    @NotBlank(message = "向量库关联字段不能为空", groups = {AddGroup.class, EditGroup.class})
    private String vectorLibraryRel;


}
