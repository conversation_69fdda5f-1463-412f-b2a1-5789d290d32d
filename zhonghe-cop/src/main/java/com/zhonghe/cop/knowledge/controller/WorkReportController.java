package com.zhonghe.cop.knowledge.controller;

import com.zhonghe.common.core.controller.BaseController;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.cop.knowledge.service.WorkReportServer;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @BelongsProject: noemap
 * @BelongsPackage: com.zhonghe.cop.knowledge.controller
 * @Author: shaorh
 * @Description: 工作汇报
 * @CreateTime: 2025-08-12  09:40
 * @Description: TODO
 * @Version: 1.0
 */
@Validated
@Api(value = "工作汇报", tags = {"工作汇报"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/cop/workReport")
public class WorkReportController extends BaseController {

    private final WorkReportServer workReportServer;

    /**
     * 查询工作汇报内容(周报)
     */
    @RequestMapping("/query/lastWeek")
    public R<String> lastWeek() {

        return R.ok(workReportServer.lastWeek());
    }

    /**
     * 查询工作汇报内容(年报)
     */
    @RequestMapping("/query/thisYear")
    public R<String> thisYear() {
        return R.ok(workReportServer.thisYear());
    }

}
