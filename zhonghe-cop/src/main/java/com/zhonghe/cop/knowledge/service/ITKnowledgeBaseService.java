package com.zhonghe.cop.knowledge.service;

import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.cop.knowledge.domain.bo.TKnowledgeBaseBo;
import com.zhonghe.cop.knowledge.domain.vo.TKnowledgeBaseVo;

import java.util.Collection;
import java.util.List;

/**
 * 知识库Service接口
 *
 * <AUTHOR>
 * @date 2025-08-11
 */
public interface ITKnowledgeBaseService {

    /**
     * 查询知识库
     */
    TKnowledgeBaseVo queryById(Long knowledgeBaseId);


    /**
     * 查询知识库列表
     */
    List<TKnowledgeBaseVo> queryList(TKnowledgeBaseBo bo);

    /**
     * 修改知识库
     */
    Boolean insertByBo(TKnowledgeBaseBo bo);

    /**
     * 修改知识库
     */
    Boolean updateByBo(TKnowledgeBaseBo bo);

    /**
     * 校验并批量删除知识库信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    TableDataInfo<TKnowledgeBaseVo> queryPageList(TKnowledgeBaseBo bo, PageQuery pageQuery);

    boolean updateStatusById(TKnowledgeBaseBo bo);
}
