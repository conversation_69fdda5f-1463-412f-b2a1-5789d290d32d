package com.zhonghe.cop.knowledge.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.cop.knowledge.domain.TKnowledgeBase;
import com.zhonghe.cop.knowledge.domain.bo.TKnowledgeBaseBo;
import com.zhonghe.cop.knowledge.domain.vo.TKnowledgeBaseVo;
import com.zhonghe.cop.knowledge.mapper.TKnowledgeBaseMapper;
import com.zhonghe.cop.knowledge.service.ITKnowledgeBaseService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 知识库Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-08-11
 */
@RequiredArgsConstructor
@Service
public class TKnowledgeBaseServiceImpl implements ITKnowledgeBaseService {

    private final TKnowledgeBaseMapper baseMapper;


    @Override
    public TableDataInfo<TKnowledgeBaseVo> queryPageList(TKnowledgeBaseBo bo, PageQuery pageQuery) {
        IPage<TKnowledgeBaseVo> iPage = baseMapper.selectVoPage(pageQuery.build(), buildQueryWrapper(bo));

        return TableDataInfo.build(iPage);
    }


    /**
     * 查询知识库
     */
    @Override
    public TKnowledgeBaseVo queryById(Long knowledgeBaseId) {
        return baseMapper.selectVoById(knowledgeBaseId);
    }


    /**
     * 查询知识库列表
     */
    @Override
    public List<TKnowledgeBaseVo> queryList(TKnowledgeBaseBo bo) {
        LambdaQueryWrapper<TKnowledgeBase> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<TKnowledgeBase> buildQueryWrapper(TKnowledgeBaseBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<TKnowledgeBase> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getLegalDocumentName()), TKnowledgeBase::getLegalDocumentName, bo.getLegalDocumentName());
        lqw.eq(StringUtils.isNotBlank(bo.getAuthor()), TKnowledgeBase::getAuthor, bo.getAuthor());
        lqw.eq(StringUtils.isNotBlank(bo.getKnowledgeBaseType()), TKnowledgeBase::getKnowledgeBaseType, bo.getKnowledgeBaseType());
        lqw.eq(bo.getKnowledgeBaseTypeId() != null, TKnowledgeBase::getKnowledgeBaseTypeId, bo.getKnowledgeBaseTypeId());
        lqw.eq(StringUtils.isNotBlank(bo.getVersionsNo()), TKnowledgeBase::getVersionsNo, bo.getVersionsNo());
        lqw.eq(StringUtils.isNotBlank(bo.getVersionsTime()), TKnowledgeBase::getVersionsTime, bo.getVersionsTime());
        lqw.eq(StringUtils.isNotBlank(bo.getStatus()), TKnowledgeBase::getStatus, bo.getStatus());
        lqw.eq(StringUtils.isNotBlank(bo.getOssPath()), TKnowledgeBase::getOssPath, bo.getOssPath());
        lqw.eq(StringUtils.isNotBlank(bo.getVectorLibraryRel()), TKnowledgeBase::getVectorLibraryRel, bo.getVectorLibraryRel());
        return lqw;
    }

    /**
     * 新增知识库
     */
    @Override
    public Boolean insertByBo(TKnowledgeBaseBo bo) {
        TKnowledgeBase add = BeanUtil.toBean(bo, TKnowledgeBase.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setKnowledgeBaseId(add.getKnowledgeBaseId());
        }
        return flag;
    }

    /**
     * 修改知识库
     */
    @Override
    public Boolean updateByBo(TKnowledgeBaseBo bo) {
        TKnowledgeBase update = BeanUtil.toBean(bo, TKnowledgeBase.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    @Override
    public boolean updateStatusById(TKnowledgeBaseBo bo) {
        return baseMapper.update(null, Wrappers.<TKnowledgeBase>lambdaUpdate()
                .set(TKnowledgeBase::getStatus, bo.getStatus())
                .eq(TKnowledgeBase::getKnowledgeBaseId, bo.getKnowledgeBaseId())) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(TKnowledgeBase entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除知识库
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
