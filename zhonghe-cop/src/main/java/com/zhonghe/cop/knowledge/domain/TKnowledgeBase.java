package com.zhonghe.cop.knowledge.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zhonghe.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 知识库对象 t_knowledge_base
 *
 * <AUTHOR>
 * @date 2025-08-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("cop.t_knowledge_base")
public class TKnowledgeBase extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    @TableId(value = "knowledge_base_id")
    private Long knowledgeBaseId;
    /**
     * 法律文件名称
     */
    private String legalDocumentName;
    /**
     * 作者
     */
    private String author;
    /**
     * 知识库类型
     */
    private String knowledgeBaseType;
    /**
     * $column.columnComment
     */
    private Long knowledgeBaseTypeId;
    /**
     * 版本号
     */
    private String versionsNo;
    /**
     * 版本日期
     */
    private String versionsTime;
    /**
     * 状态 0-启用 1-停用
     */
    private String status;
    /**
     * 文件地址
     */
    private String ossPath;
    /**
     * 向量库关联字段
     */
    private String vectorLibraryRel;
    /**
     * 是否删除
     */
    @TableLogic
    private String delFlag;

}
