package com.zhonghe.cop.knowledge.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zhonghe.cop.knowledge.service.WorkReportServer;
import com.zhonghe.cop.rep.domain.TRepReport;
import com.zhonghe.cop.rep.domain.vo.table.TRepReportTableVo;
import com.zhonghe.cop.rep.em.Level;
import com.zhonghe.cop.rep.mapper.TRepReportMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @BelongsPackage: com.zhonghe.cop.knowledge.service.impl
 * @CreateTime: 2025-08-12  09:48
 * @Description: TODO
 * @Version: 1.0
 */
@RequiredArgsConstructor
@Service
public class WorkReportServerImpl implements WorkReportServer {

    private final TRepReportMapper tRepReportMapper;


    @Override
    public String lastWeek() {
        //获取上周的企业风险数据
        Map<String, String> lastWeek = getLastWeek();
        String start = lastWeek.get("start");
        String end = lastWeek.get("end");
        List<TRepReportTableVo> tableVo = tRepReportMapper.selectRepReportList(new LambdaQueryWrapper<TRepReport>()
            .between(TRepReport::getRepDate, DateUtil.parse(start, "yyyy-MM-dd HH:mm:ss"), DateUtil.parse(end, "yyyy-MM-dd HH:mm:ss"))
            .notIn(TRepReport::getLevel, Arrays.asList(Level.PROBLEM.getCode(), Level.NORMAL.getCode())));

        //对数据进行分类


        //将数据转成work文件

        return "";
    }

    @Override
    public String thisYear() {
        return "";
    }

    /**
     * 获取上周日期
     * @return
     */
    private Map<String, String> getLastWeek() {
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        // 上周：从上周一00:00:00到上周日23:59:59
        LocalDate lastWeekStart = LocalDate.now().with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY)).minusWeeks(1);
        LocalDate lastWeekEnd = LocalDate.now().with(TemporalAdjusters.previousOrSame(DayOfWeek.SUNDAY));
        LocalDateTime startDateTime = lastWeekStart.atStartOfDay();
        LocalDateTime endDateTime = lastWeekEnd.atTime(LocalTime.MAX);

        Map<String, String> map = new HashMap<>();
        map.put("start", startDateTime.format(dateTimeFormatter));
        map.put("end", endDateTime.format(dateTimeFormatter));
        return map;
    }
}
