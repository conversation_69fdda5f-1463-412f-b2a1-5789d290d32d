package com.zhonghe.cop.knowledge.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zhonghe.common.annotation.ExcelDictFormat;
import com.zhonghe.common.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 知识库视图对象 t_knowledge_base
 *
 * <AUTHOR>
 * @date 2025-08-11
 */
@Data
@ApiModel("知识库视图对象")
@ExcelIgnoreUnannotated
public class TKnowledgeBaseVo {

    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    @ExcelProperty(value = "${comment}", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "$column.readConverterExp()")
    @ApiModelProperty("$column.columnComment")
    private Long knowledgeBaseId;

    /**
     * 法律文件名称
     */
    @ExcelProperty(value = "法律文件名称")
    @ApiModelProperty("法律文件名称")
    private String legalDocumentName;

    /**
     * 作者
     */
    @ExcelProperty(value = "作者")
    @ApiModelProperty("作者")
    private String author;

    /**
     * 知识库类型
     */
    @ExcelProperty(value = "知识库类型")
    @ApiModelProperty("知识库类型")
    private String knowledgeBaseType;

    /**
     * $column.columnComment
     */
    @ExcelProperty(value = "${comment}", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "$column.readConverterExp()")
    @ApiModelProperty("$column.columnComment")
    private Long knowledgeBaseTypeId;

    /**
     * 版本号
     */
    @ExcelProperty(value = "版本号")
    @ApiModelProperty("版本号")
    private String versionsNo;

    /**
     * 版本日期
     */
    @ExcelProperty(value = "版本日期")
    @ApiModelProperty("版本日期")
    private String versionsTime;

    /**
     * 状态 0-启用 1-停用
     */
    @ExcelProperty(value = "状态 0-启用 1-停用")
    @ApiModelProperty("状态 0-启用 1-停用")
    private String status;

    /**
     * 文件地址
     */
    @ExcelProperty(value = "文件地址")
    @ApiModelProperty("文件地址")
    private String ossPath;

    /**
     * 向量库关联字段
     */
    @ExcelProperty(value = "向量库关联字段")
    @ApiModelProperty("向量库关联字段")
    private String vectorLibraryRel;


}
