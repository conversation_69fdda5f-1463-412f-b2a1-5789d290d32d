package com.zhonghe.cop.knowledge.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.aspose.words.Document;
import com.zhonghe.common.annotation.Log;
import com.zhonghe.common.annotation.RepeatSubmit;
import com.zhonghe.common.core.controller.BaseController;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import com.zhonghe.common.enums.BusinessType;
import com.zhonghe.common.exception.ServiceException;
import com.zhonghe.common.utils.file.FileUtils;
import com.zhonghe.common.utils.poi.ExcelUtil;
import com.zhonghe.cop.knowledge.domain.bo.TKnowledgeBaseBo;
import com.zhonghe.cop.knowledge.domain.vo.TKnowledgeBaseVo;
import com.zhonghe.cop.knowledge.service.ITKnowledgeBaseService;
import com.zhonghe.system.domain.SysOss;
import com.zhonghe.system.service.ISysOssService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.Cleanup;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Arrays;
import java.util.List;

/**
 * 知识库Controller
 *
 * <AUTHOR>
 * @date 2025-08-11
 */
@Validated
@Api(value = "知识库控制器", tags = {"知识库管理"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/system/knowledgeBase")
public class TKnowledgeBaseController extends BaseController {

    private final ITKnowledgeBaseService iTKnowledgeBaseService;
    private final ISysOssService iSysOssService;

    /**
     * 查询知识库列表
     */
    @ApiOperation("查询知识库列表")
    @SaCheckPermission("system:knowledgeBase:list")
    @GetMapping("/list")
    public TableDataInfo<TKnowledgeBaseVo> list(TKnowledgeBaseBo bo, PageQuery pageQuery) {
        return iTKnowledgeBaseService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出知识库列表
     */
    @ApiOperation("导出知识库列表")
    @SaCheckPermission("system:knowledgeBase:export")
    @Log(title = "知识库", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(TKnowledgeBaseBo bo, HttpServletResponse response) {
        List<TKnowledgeBaseVo> list = iTKnowledgeBaseService.queryList(bo);
        ExcelUtil.exportExcel(list, "知识库", TKnowledgeBaseVo.class, response);
    }

    /**
     * 获取知识库详细信息
     */
    @ApiOperation("获取知识库详细信息")
    @SaCheckPermission("system:knowledgeBase:query")
    @GetMapping("/{knowledgeBaseId}")
    public R<TKnowledgeBaseVo> getInfo(@ApiParam("主键")
                                       @NotNull(message = "主键不能为空")
                                       @PathVariable("knowledgeBaseId") Long knowledgeBaseId) {
        return R.ok(iTKnowledgeBaseService.queryById(knowledgeBaseId));
    }

    /**
     * 新增知识库
     */
    @ApiOperation("新增知识库")
    @SaCheckPermission("system:knowledgeBase:add")
    @Log(title = "知识库", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody TKnowledgeBaseBo bo) {
        return toAjax(iTKnowledgeBaseService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改知识库
     */
    @ApiOperation("修改知识库")
    @SaCheckPermission("system:knowledgeBase:edit")
    @Log(title = "知识库", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody TKnowledgeBaseBo bo) {
        return toAjax(iTKnowledgeBaseService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除知识库
     */
    @ApiOperation("删除知识库")
    @SaCheckPermission("system:knowledgeBase:remove")
    @Log(title = "知识库", businessType = BusinessType.DELETE)
    @DeleteMapping("/{knowledgeBaseIds}")
    public R<Void> remove(@ApiParam("主键串")
                          @NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] knowledgeBaseIds) {
        return toAjax(iTKnowledgeBaseService.deleteWithValidByIds(Arrays.asList(knowledgeBaseIds), true) ? 1 : 0);
    }

    /**
     * docx转pdf预览
     */
    @GetMapping("/docx2pdf/{ossId}")
    public void docx2pdf(@PathVariable Long ossId, HttpServletResponse response) throws Exception {
        SysOss sysOss = iSysOssService.getById(ossId);
        if (!sysOss.getFileSuffix().contains("docx")) {
            throw new ServiceException("文件格式不支持预览.");
        }
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        @Cleanup InputStream is = iSysOssService.getInputStreamById(ossId);
        Document document = new Document(is);
        document.save(os, com.aspose.words.SaveFormat.PDF);
        response.reset();
        response.setHeader("Access-Control-Allow-Origin", "*");
        response.setHeader("Access-Control-Allow-Credentials", "true");
        response.setHeader("Access-Control-Allow-Methods", "*");
        response.setHeader("Access-Control-Allow-Headers", "*");
        response.setContentType(MediaType.APPLICATION_PDF_VALUE);
        FileUtils.setAttachmentResponseHeader(response, sysOss.getOriginalName().replace("docx", "pdf"));
        OutputStream out = response.getOutputStream();
        out.write(os.toByteArray());
        out.close();
    }

    /**
     * 启用/禁用状态(修改数据status字段)
     */
    @ApiOperation("启用/禁用状态(修改数据status字段)")
    @PutMapping("/changeStatus")
    public R<Void> changeStatus(@RequestBody TKnowledgeBaseBo bo) {
        return toAjax(iTKnowledgeBaseService.updateStatusById(bo) ? 1 : 0);
    }
}
