package com.zhonghe.cop.doc.mapper;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhonghe.common.core.mapper.BaseMapperPlus;
import com.zhonghe.cop.doc.domain.TDocHandle;
import com.zhonghe.cop.doc.domain.TDocInfo;
import com.zhonghe.cop.doc.domain.table.TDocInfoTableVo;
import com.zhonghe.cop.doc.domain.vo.TDocHandleVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 档案上报Mapper接口
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
public interface TDocHandleMapper extends BaseMapperPlus<TDocHandleMapper, TDocHandle, TDocHandleVo> {

    /**
     * 查询上报列表
     *
     * @param docInfoId
     * @return
     */
    List<TDocHandleVo> selectReportList(@Param("docInfoId") Long docInfoId);

    /**
     * 分页查询档案基本列表
     *
     * @param build
     * @param lqw
     * @return
     */
    Page<TDocInfoTableVo> selectDocHandleList(Page<TDocInfo> build, @Param(Constants.WRAPPER) Wrapper<TDocInfo> lqw);

    /**
     * 查询档案基本列表
     *
     * @param lqw
     * @return
     */
    List<TDocInfoTableVo> selectDocHandleList(@Param(Constants.WRAPPER) Wrapper<TDocInfo> lqw);
}
