package com.zhonghe.cop.doc.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.zhonghe.common.annotation.ExcelDictFormat;
import com.zhonghe.common.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 档案文书附件视图对象 t_doc_acc
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
@Data
@ApiModel("档案文书附件视图对象")
@ExcelIgnoreUnannotated
public class TDocAccVo {

    private static final long serialVersionUID = 1L;

    /**
     * 档案文书附件ID
     */
    @ExcelProperty(value = "档案文书附件ID")
    @ApiModelProperty("档案文书附件ID")
    private Long docAccId;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    @ApiModelProperty("备注")
    private String note;

    /**
     * 档案ID（根据附件部门类型对应T_DOC_Info或T_DOC_Handle的主键）
     */
    @ExcelProperty(value = "档案ID", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "根=据附件部门类型对应T_DOC_Info或T_DOC_Handle的主键")
    @ApiModelProperty("档案ID（根据附件部门类型对应T_DOC_Info或T_DOC_Handle的主键）")
    private Long docId;

    /**
     * 附件部门类型（默认0，0——领导小组、1——成员部门）
     */
    @ExcelProperty(value = "附件部门类型", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "默=认0，0——领导小组、1——成员部门")
    @ApiModelProperty("附件部门类型（默认0，0——领导小组、1——成员部门）")
    private Long deptType;

    /**
     * 附件类型ID（对应T_DIC_DocAccType表主键）
     */
    @ExcelProperty(value = "附件类型ID", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "对=应T_DIC_DocAccType表主键")
    @ApiModelProperty("附件类型ID（对应T_DIC_DocAccType表主键）")
    @JsonSerialize(using = ToStringSerializer.class)
    private Integer accTypeId;

    /**
     * 附件名称
     */
    @ExcelProperty(value = "附件名称")
    @ApiModelProperty("附件名称")
    private String accName;

    /**
     * 附件url
     */
    @ExcelProperty(value = "附件url")
    @ApiModelProperty("附件url")
    private String accUrl;


}
