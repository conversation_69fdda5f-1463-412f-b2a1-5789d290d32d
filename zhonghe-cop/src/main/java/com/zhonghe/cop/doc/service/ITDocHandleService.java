package com.zhonghe.cop.doc.service;

import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.cop.doc.domain.bo.TDocHandleBo;
import com.zhonghe.cop.doc.domain.query.TDocInfoQuery;
import com.zhonghe.cop.doc.domain.table.TDocInfoTableVo;
import com.zhonghe.cop.doc.domain.vo.TDocHandleInfoVo;

import java.util.Collection;
import java.util.List;

/**
 * 档案上报Service接口
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
public interface ITDocHandleService {

    /**
     * 查询档案上报
     */
    TDocHandleInfoVo queryByDocInfoId(Long docInfoId);

    /**
     * 查询档案上报列表
     */
    TableDataInfo<TDocInfoTableVo> queryPageList(TDocInfoQuery bo, PageQuery pageQuery);

    /**
     * 查询档案上报列表
     */
    List<TDocInfoTableVo> queryList(TDocInfoQuery bo);

    /**
     * 修改档案上报
     */
    Boolean insertByBo(TDocHandleBo bo);

    /**
     * 修改档案上报
     */
    Boolean updateByBo(TDocHandleBo bo);

    /**
     * 校验并批量删除档案上报信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

}
