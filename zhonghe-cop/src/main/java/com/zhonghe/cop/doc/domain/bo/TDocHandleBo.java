package com.zhonghe.cop.doc.domain.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;


/**
 * 档案上报业务对象 t_doc_handle
 *
 * <AUTHOR>
 * @date 2023-12-07
 */

@Data
@ApiModel("档案上报业务对象")
public class TDocHandleBo implements Serializable {

    private static final long serialVersionUID = 754512894211319740L;
    /**
     * 档案上报ID
     */
    @ApiModelProperty(value = "档案上报ID")
    private Long docHandleId;


    /**
     * 档案ID（对应t_doc_info表中的主键）
     */
    @ApiModelProperty(value = "档案ID（对应t_doc_info表中的主键）", required = true)
    @NotNull(message = "档案ID（对应t_doc_info表中的主键）不能为空")
    private Long docInfoId;


    /**
     * 处理时间
     */
    @ApiModelProperty(value = "处理时间", required = true)
    @NotNull(message = "处理时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date dealDate;

    /**
     * 事件描述
     */
    @ApiModelProperty(value = "事件描述")
    @NotBlank(message = "事件描述不能为空")
    @Length(max = 2000, message = "事件描述最大长度为2000")
    private String dealDescription;

    /**
     * 处理依据
     */
    @ApiModelProperty(value = "处理依据")
    @Length(max = 2000, message = "处理依据最大长度为2000")
    private String dealBasis;

    /**
     * 处理措施
     */
    @ApiModelProperty(value = "处理措施")
    @Length(max = 2000, message = "处理措施最大长度为2000")
    private String dealMeasure;

    /**
     * 处理结果
     */
    @ApiModelProperty(value = "处理结果")
    @Length(max = 2000, message = "处理结果最大长度为2000")
    private String dealResult;

    /**
     * 事件启发
     */
    @ApiModelProperty(value = "事件启发")
    @Length(max = 2000, message = "事件启发最大长度为2000")
    private String dealEnlighten;

    /**
     * 事件处置报告
     */
    @ApiModelProperty(value = "事件处置报告")
    @Length(max = 2000, message = "事件处置报告最大长度为2000")
    private String dealReport;


    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Length(max = 2000, message = "备注最大长度为2000")
    private String note;

}
