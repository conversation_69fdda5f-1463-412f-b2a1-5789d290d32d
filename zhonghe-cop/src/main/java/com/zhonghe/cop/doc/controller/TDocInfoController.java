package com.zhonghe.cop.doc.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.zhonghe.common.annotation.Log;
import com.zhonghe.common.annotation.RepeatSubmit;
import com.zhonghe.common.core.controller.BaseController;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import com.zhonghe.common.enums.BusinessType;
import com.zhonghe.common.utils.poi.ExcelUtil;
import com.zhonghe.cop.doc.domain.bo.TDocAccBo;
import com.zhonghe.cop.doc.domain.bo.TDocInfoBo;
import com.zhonghe.cop.doc.domain.query.TDocInfoQuery;
import com.zhonghe.cop.doc.domain.table.TDocInfoTableVo;
import com.zhonghe.cop.doc.domain.vo.QueryDocInfoVo;
import com.zhonghe.cop.doc.domain.vo.TDocHandleVo;
import com.zhonghe.cop.doc.service.ITDocInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 档案基本信息
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
@Validated
@Api(value = "档案基本控制器", tags = {"档案基本管理"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/cop/docInfo")
public class TDocInfoController extends BaseController {

    private final ITDocInfoService iTDocInfoService;

    /**
     * 查询档案基本列表
     */
    @ApiOperation("查询档案基本列表(cop:docInfo:list)")
    @SaCheckPermission("cop:docInfo:list")
    @GetMapping("/list")
    public TableDataInfo<TDocInfoTableVo> list(TDocInfoQuery bo, PageQuery pageQuery) {
        return iTDocInfoService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出档案基本列表
     */
    @ApiOperation("导出档案基本列表(cop:docInfo:export)")
    @SaCheckPermission("cop:docInfo:export")
    @Log(title = "档案基本", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(TDocInfoQuery bo, HttpServletResponse response) {
        List<TDocInfoTableVo> list = iTDocInfoService.queryList(bo);
        ExcelUtil.exportExcel(list, "档案基本", TDocInfoTableVo.class, response);
    }

    /**
     * 获取档案基本详细信息
     */
    @ApiOperation("获取档案基本详细信息(cop:docInfo:query)")
    @SaCheckPermission("cop:docInfo:query")
    @GetMapping("/{docInfoId}")
    public R<QueryDocInfoVo> getInfo(@ApiParam("主键")
                                     @NotNull(message = "主键不能为空")
                                     @PathVariable("docInfoId") Long docInfoId) {
        return R.ok(iTDocInfoService.queryById(docInfoId));
    }

    /**
     * 查询上报内容
     */
    @ApiOperation("查询上报内容(cop:docInfo:report)")
    @SaCheckPermission("cop:docInfo:report")
    @GetMapping("/report/{docInfoId}")
    public R<List<TDocHandleVo>> queryReport(@ApiParam("主键")
                                             @NotNull(message = "主键不能为空")
                                             @PathVariable("docInfoId") Long docInfoId) {
        return R.ok(iTDocInfoService.queryReport(docInfoId));
    }

    /**
     * 新增档案基本信息
     */
    @ApiOperation("新增档案基本(cop:docInfo:add)")
    @SaCheckPermission("cop:docInfo:add")
    @Log(title = "档案基本", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody TDocInfoBo bo) {
        return toAjax(iTDocInfoService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改档案基本信息
     */
    @ApiOperation("修改档案基本信息(cop:docInfo:edit)")
    @SaCheckPermission("cop:docInfo:edit")
    @Log(title = "档案基本", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> update(@Validated(EditGroup.class) @RequestBody TDocInfoBo bo) {
        return toAjax(iTDocInfoService.updateByBo(bo) ? 1 : 0);
    }


    /**
     * 删除档案基本信息
     */
    @ApiOperation("删除档案基本信息(cop:docInfo:remove)")
    @SaCheckPermission("cop:docInfo:remove")
    @Log(title = "档案基本", businessType = BusinessType.DELETE)
    @DeleteMapping("/{docInfoIds}")
    public R<Void> remove(@ApiParam("主键串")
                          @NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] docInfoIds) {
        return toAjax(iTDocInfoService.deleteWithValidByIds(Arrays.asList(docInfoIds), true) ? 1 : 0);
    }

    /**
     * 新增文书附件
     */
    @ApiOperation("新增文书附件(cop:docInfo:acc)")
    @SaCheckPermission("cop:docInfo:acc")
    @Log(title = "新增文书附件", businessType = BusinessType.DELETE)
    @PostMapping("/docAcc")
    public R<Void> acc(@Validated TDocAccBo bo) {
        return toAjax(iTDocInfoService.docAcc(bo) ? 1 : 0);
    }

}
