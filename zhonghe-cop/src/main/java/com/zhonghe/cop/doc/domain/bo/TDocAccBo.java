package com.zhonghe.cop.doc.domain.bo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


/**
 * 档案文书附件业务对象 t_doc_acc
 *
 * <AUTHOR>
 * @date 2023-12-07
 */

@Data
@ApiModel("档案文书附件业务对象")
public class TDocAccBo {

    private static final long serialVersionUID = -5258879982238336963L;

    /**
     * 档案ID（根据附件部门类型对应T_DOC_Info或T_DOC_Handle的主键）
     */
    @ApiModelProperty(value = "档案ID（根据附件部门类型对应T_DOC_Info或T_DOC_Handle的主键）", required = true)
    @NotNull(message = "档案ID（根据附件部门类型对应T_DOC_Info或T_DOC_Handle的主键）不能为空")
    private Long docId;


    /**
     * 附件类型ID（对应T_DIC_DocAccType表主键）
     */
    @ApiModelProperty(value = "附件类型ID（对应T_DIC_DocAccType表主键）", required = true)
    @NotNull(message = "附件类型ID（对应T_DIC_DocAccType表主键）不能为空")
    private Integer accTypeId;

    /**
     * 附件名称
     */
    @ApiModelProperty(value = "附件名称", required = true)
    @NotBlank(message = "附件名称不能为空")
    @Length(max = 255, message = "附件名称长度不能超过255个字符")
    private String accName;

    /**
     * 附件
     */
    @ApiModelProperty(value = "附件", required = true)
    @NotNull(message = "附件不能为空")
    private MultipartFile accFile;


}
