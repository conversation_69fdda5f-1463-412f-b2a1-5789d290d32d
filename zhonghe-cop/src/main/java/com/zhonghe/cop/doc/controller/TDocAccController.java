package com.zhonghe.cop.doc.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.zhonghe.common.annotation.Anonymous;
import com.zhonghe.common.annotation.Log;
import com.zhonghe.common.annotation.RepeatSubmit;
import com.zhonghe.common.core.controller.BaseController;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.common.enums.BusinessType;
import com.zhonghe.cop.doc.domain.bo.TDocAccBo;
import com.zhonghe.cop.doc.domain.vo.TDocAccVo;
import com.zhonghe.cop.doc.service.ITDocAccService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.http.MediaType;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 档案文书附件
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
@Validated
@Api(value = "档案文书附件控制器", tags = {"档案文书附件管理"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/cop/docAcc")
public class TDocAccController extends BaseController {

    private final ITDocAccService iTDocAccService;

    /**
     * 根据档案ID查询档案文书附件列表
     */
    @ApiOperation("根据档案ID查询档案文书附件列表(cop:docAcc:query)")
    @SaCheckPermission("cop:docAcc:query")
    @GetMapping("/docList/{docId}")
    public R<List<TDocAccVo>> queryListByDocId(@Validated @ApiParam("档案ID") @PathVariable Long docId) {
        return R.ok(iTDocAccService.queryListByDocId(docId));
    }

    /**
     * 新增档案文书附件
     */
    @ApiOperation("新增档案文书附件(cop:docAcc:add)")
    @SaCheckPermission("cop:docAcc:add")
    @Log(title = "新增档案文书附件", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping(consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public R<Void> add(@Validated TDocAccBo bo) {
        return toAjax(iTDocAccService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 下载档案文书附件
     */
    @ApiOperation("下载档案文书附件(cop:docAcc:download)")
    @Anonymous
    @GetMapping("/download/{docAccId}")
    public void download(@ApiParam("主键")
                         @NotNull(message = "主键不能为空")
                         @PathVariable Long docAccId, HttpServletResponse response) {
        iTDocAccService.download(docAccId, response);
    }


    /**
     * 删除档案文书附件
     */
    @ApiOperation("删除档案文书附件(cop:docAcc:remove)")
    @SaCheckPermission("cop:docAcc:remove")
    @Log(title = "档案文书附件", businessType = BusinessType.DELETE)
    @DeleteMapping("/{docAccIds}")
    public R<Void> remove(@ApiParam("主键串")
                          @NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] docAccIds) {
        return toAjax(iTDocAccService.deleteWithValidByIds(Arrays.asList(docAccIds), true) ? 1 : 0);
    }
}
