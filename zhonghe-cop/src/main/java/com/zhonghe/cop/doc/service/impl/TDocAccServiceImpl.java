package com.zhonghe.cop.doc.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.io.FileUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.cop.doc.domain.TDocAcc;
import com.zhonghe.cop.doc.domain.bo.TDocAccBo;
import com.zhonghe.cop.doc.domain.vo.TDocAccVo;
import com.zhonghe.cop.doc.mapper.TDocAccMapper;
import com.zhonghe.cop.doc.service.ITDocAccService;
import com.zhonghe.system.domain.SysOss;
import com.zhonghe.system.service.ISysOssService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.util.Collection;
import java.util.List;

/**
 * 档案文书附件Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
@RequiredArgsConstructor
@Service
public class TDocAccServiceImpl implements ITDocAccService {

    private final TDocAccMapper baseMapper;
    private final ISysOssService iSysOssService;

    public static void main(String[] args) {
        System.out.println(FileUtil.getSuffix("2023/04/07/0575bf13d5c744269438bb37c0626ff6.png"));
    }

    /**
     * 查询档案文书附件
     */
    @Override
    public TDocAccVo queryById(Long docAccId) {
        return baseMapper.selectVoById(docAccId);
    }

    /**
     * 查询档案文书附件列表
     */
    @Override
    public TableDataInfo<TDocAccVo> queryPageList(TDocAccBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<TDocAcc> lqw = buildQueryWrapper(bo);
        Page<TDocAccVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询档案文书附件列表
     */
    @Override
    public List<TDocAccVo> queryList(TDocAccBo bo) {
        LambdaQueryWrapper<TDocAcc> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<TDocAcc> buildQueryWrapper(TDocAccBo bo) {
        LambdaQueryWrapper<TDocAcc> lqw = Wrappers.lambdaQuery();
        return lqw;
    }

    /**
     * 新增档案文书附件
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(TDocAccBo bo) {
        TDocAcc add = BeanUtil.toBean(bo, TDocAcc.class);

        SysOss sysOss = iSysOssService.uploadFile(bo.getAccFile());
        add.setAccUrl(sysOss.getUrl());

        boolean flag = baseMapper.insert(add) > 0;

        return flag;
    }

    /**
     * 修改档案文书附件
     */
    @Override
    public Boolean updateByBo(TDocAccBo bo) {
        TDocAcc update = BeanUtil.toBean(bo, TDocAcc.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(TDocAcc entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public void download(Long docAccId, HttpServletResponse response) {
        TDocAccVo docAccVo = baseMapper.selectVoById(docAccId);
        //截取文件url后缀
        String suffix = FileUtil.getSuffix(docAccVo.getAccUrl());
        iSysOssService.downloadFile(docAccVo.getAccUrl(), docAccVo.getAccName() + "." + suffix, response);
    }

    @Override
    public List<TDocAccVo> queryListByDocId(Long docId) {
        return baseMapper.selectVoList(Wrappers.lambdaQuery(TDocAcc.class).eq(TDocAcc::getDocId, docId).orderByDesc(TDocAcc::getCreateTime));
    }

    /**
     * 批量删除档案文书附件
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
