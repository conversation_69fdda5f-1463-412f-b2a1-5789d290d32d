package com.zhonghe.cop.doc.domain.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 档案基础信息查询对象
 *
 * <AUTHOR>
 * @date 2023-12-07
 */

@Data
@ApiModel("档案基础信息查询对象")
public class TDocInfoQuery {

    private static final long serialVersionUID = 9074381222147286808L;

    /**
     * 企业名称
     */
    @ApiModelProperty(value = "企业名称")
    private String copName;
    /**
     * 企业法人
     */
    @ApiModelProperty(value = "企业法人")
    private String copOwner;
    /**
     * 企业电话
     */
    @ApiModelProperty(value = "企业电话")
    private String copPhone;

}
