package com.zhonghe.cop.doc.domain.table;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author: lpg
 * @Date: 2023/12/07/14:53
 * @Description:
 */
@Data
public class TDocInfoTableVo implements Serializable {
    private static final long serialVersionUID = -5072816712637810065L;

    /**
     * 档案上报ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long docHandleId;
    /**
     * 档案基础信息ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long docInfoId;

    /**
     * 企业名称
     */
    private String copName;

    /**
     * 企业性质
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Integer propertyId;
    /**
     * 企业法人
     */
    private String copOwner;
    /**
     * 企业电话
     */
    private String copPhone;
    /**
     * 企业地址
     */
    private String copAddress;
    /**
     * 事件描述
     */
    private String dealDescription;
    /**
     * 建档时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date docDate;
}
