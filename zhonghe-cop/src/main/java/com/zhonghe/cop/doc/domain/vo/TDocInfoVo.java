package com.zhonghe.cop.doc.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.zhonghe.common.annotation.ExcelDictFormat;
import com.zhonghe.common.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;


/**
 * 档案基本视图对象 t_doc_info
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
@Data
@ApiModel("档案基本视图对象")
@ExcelIgnoreUnannotated
public class TDocInfoVo {

    private static final long serialVersionUID = 1L;

    /**
     * 档案信息ID
     */
    @ExcelProperty(value = "档案信息ID")
    @ApiModelProperty("档案信息ID")
    private Long docInfoId;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    @ApiModelProperty("备注")
    private String note;

    /**
     * 企业ID
     */
    @ExcelProperty(value = "企业ID")
    @ApiModelProperty("企业ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long copId;

    /**
     * 建档时间
     */
    @ExcelProperty(value = "建档时间")
    @ApiModelProperty("建档时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date docDate;

    /**
     * 事件描述
     */
    @ExcelProperty(value = "事件描述")
    @ApiModelProperty("事件描述")
    private String dealDescription;

    /**
     * 处理依据
     */
    @ExcelProperty(value = "处理依据")
    @ApiModelProperty("处理依据")
    private String dealBasis;

    /**
     * 处理措施
     */
    @ExcelProperty(value = "处理措施")
    @ApiModelProperty("处理措施")
    private String dealMeasure;

    /**
     * 处理结果
     */
    @ExcelProperty(value = "处理结果")
    @ApiModelProperty("处理结果")
    private String dealResult;

    /**
     * 事件启发
     */
    @ExcelProperty(value = "事件启发")
    @ApiModelProperty("事件启发")
    private String dealEnlighten;

    /**
     * 事件处置报告
     */
    @ExcelProperty(value = "事件处置报告")
    @ApiModelProperty("事件处置报告")
    private String dealReport;

    /**
     * 涉及部门ID串（逗号分割，例：1,2,3 ）
     */
    @ExcelProperty(value = "涉及部门ID串", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "逗=号分割，例：1,2,3")
    @ApiModelProperty("涉及部门ID串（逗号分割，例：1,2,3 ）")
    private String depts;
    /**
     * 涉及部门ID
     */
    @ExcelProperty(value = "涉及部门ID")
    @ApiModelProperty("涉及部门ID")
    private String[] deptIds;
    /**
     * 部门名称
     */
    @ExcelProperty(value = "部门名称")
    @ApiModelProperty("部门名称")
    private String deptNames;

    /**
     * 文书附件
     */
    @ApiModelProperty("文书附件")
    private List<TDocAccVo> docAccs;

}
