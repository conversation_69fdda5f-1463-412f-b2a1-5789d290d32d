package com.zhonghe.cop.doc.domain.vo;

import com.zhonghe.cop.bam.domain.vo.TCopInformationVo;
import com.zhonghe.cop.score.domain.vo.CopRiskVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;


/**
 * 档案基本视图对象 t_doc_info
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
@Data
@ApiModel("档案基本视图对象")
public class QueryDocInfoVo implements Serializable {

    private static final long serialVersionUID = -4168314369349937590L;

    /**
     * 企业信息
     */
    @ApiModelProperty("企业信息")
    private TCopInformationVo copInformation;
    /**
     * 档案信息
     */
    @ApiModelProperty("档案信息")
    private TDocInfoVo docInfo;

    /**
     * 档案处理信息
     */
    @ApiModelProperty("档案处理信息")
    private List<TDocHandleVo> docHandles;

    /**
     * 历年风险评级
     */
    @ApiModelProperty("历年风险评级")
    private List<CopRiskVo> docRisks;
}
