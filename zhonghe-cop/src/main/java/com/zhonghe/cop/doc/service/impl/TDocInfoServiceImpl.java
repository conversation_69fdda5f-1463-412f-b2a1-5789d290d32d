package com.zhonghe.cop.doc.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhonghe.common.constant.Constants;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.domain.entity.SysDept;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.exception.ServiceException;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.cop.bam.domain.vo.TCopInformationVo;
import com.zhonghe.cop.bam.mapper.TCopInformationMapper;
import com.zhonghe.cop.doc.domain.TDocAcc;
import com.zhonghe.cop.doc.domain.TDocHandle;
import com.zhonghe.cop.doc.domain.TDocInfo;
import com.zhonghe.cop.doc.domain.bo.TDocAccBo;
import com.zhonghe.cop.doc.domain.bo.TDocInfoBo;
import com.zhonghe.cop.doc.domain.query.TDocInfoQuery;
import com.zhonghe.cop.doc.domain.table.TDocInfoTableVo;
import com.zhonghe.cop.doc.domain.vo.QueryDocInfoVo;
import com.zhonghe.cop.doc.domain.vo.TDocAccVo;
import com.zhonghe.cop.doc.domain.vo.TDocHandleVo;
import com.zhonghe.cop.doc.domain.vo.TDocInfoVo;
import com.zhonghe.cop.doc.mapper.TDocAccMapper;
import com.zhonghe.cop.doc.mapper.TDocHandleMapper;
import com.zhonghe.cop.doc.mapper.TDocInfoMapper;
import com.zhonghe.cop.doc.service.ITDocInfoService;
import com.zhonghe.cop.score.domain.vo.CopRiskVo;
import com.zhonghe.cop.score.service.ITCopScoreYearService;
import com.zhonghe.system.domain.SysOss;
import com.zhonghe.system.mapper.SysDeptMapper;
import com.zhonghe.system.service.ISysOssService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 档案基本Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
@RequiredArgsConstructor
@Service
public class TDocInfoServiceImpl implements ITDocInfoService {

    private final TDocInfoMapper baseMapper;
    private final TDocHandleMapper docHandleMapper;
    private final TDocAccMapper docAccMapper;
    private final ISysOssService iSysOssService;
    private final TCopInformationMapper copInformationMapper;
    private final SysDeptMapper sysDeptMapper;
    private final ITCopScoreYearService iTCopScoreYearService;

    /**
     * 查询档案基本
     */
    @Override
    public QueryDocInfoVo queryById(Long docInfoId) {
        QueryDocInfoVo queryDocInfoVo = new QueryDocInfoVo();

        TDocInfoVo tDocInfoVo = baseMapper.selectVoById(docInfoId);
        // 附件
        List<TDocAccVo> tDocAccs = docAccMapper.selectVoList(Wrappers.<TDocAcc>lambdaQuery().eq(TDocAcc::getDocId, docInfoId));
        tDocInfoVo.setDocAccs(tDocAccs);

        if (StringUtils.isNotBlank(tDocInfoVo.getDepts())) {
            //查询部门名称ID转为Long类型数组
            String[] stringArray = tDocInfoVo.getDepts().split(",");
            Long[] deptIds = new Long[stringArray.length];

            for (int i = 0; i < stringArray.length; i++) {
                deptIds[i] = Long.parseLong(stringArray[i].trim());
            }
            if (deptIds.length > 0) {
                List<SysDept> sysDepts = sysDeptMapper.selectBatchIds(Arrays.asList(deptIds));
                //部门名称,逗号分割
                Optional<String> deptNames = sysDepts.stream().map(SysDept::getDeptName).reduce((a, b) -> a + "," + b);
                deptNames.ifPresent(tDocInfoVo::setDeptNames);
            }
            //部门ID数组转String数组
            String[] ids = new String[stringArray.length];
            for (int i = 0; i < stringArray.length; i++) {
                ids[i] = stringArray[i].trim();
            }
            tDocInfoVo.setDeptIds(ids);
        }

        //查询企业信息
        TCopInformationVo tCopInformation = copInformationMapper.selectVoById(tDocInfoVo.getCopId());

        //查询处理部门信息
        Map<Long, String> deptMap = sysDeptMapper.selectList().stream().collect(Collectors.toMap(SysDept::getDeptId, SysDept::getDeptName));
        List<TDocHandleVo> tDocHandleVoList = docHandleMapper.selectVoList(Wrappers.<TDocHandle>lambdaQuery().eq(TDocHandle::getDocInfoId, docInfoId));
        tDocHandleVoList.forEach(tDocHandleVo -> {
            String deptName = deptMap.get(tDocHandleVo.getDeptId());
            if (deptName != null) {
                tDocHandleVo.setDeptName(deptName);
            }
            //查询附件
            List<TDocAccVo> tDocHandleAccs = docAccMapper.selectVoList(Wrappers.<TDocAcc>lambdaQuery().eq(TDocAcc::getDocId, tDocHandleVo.getDocHandleId()));
            tDocHandleVo.setDocAccs(tDocHandleAccs);
        });
        //历年风险评级
        List<CopRiskVo> copRiskVos = iTCopScoreYearService.queryRisk(tDocInfoVo.getCopId());

        queryDocInfoVo.setDocInfo(tDocInfoVo);
        queryDocInfoVo.setCopInformation(tCopInformation);
        queryDocInfoVo.setDocHandles(tDocHandleVoList);
        queryDocInfoVo.setDocRisks(copRiskVos);

        return queryDocInfoVo;
    }

    /**
     * 查询档案基本列表
     */
    @Override
    public TableDataInfo<TDocInfoTableVo> queryPageList(TDocInfoQuery bo, PageQuery pageQuery) {
        QueryWrapper<TDocInfo> lqw = buildQueryWrapper(bo);
        Page<TDocInfoTableVo> result = baseMapper.selectDocInfoList(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询档案基本列表
     */
    @Override
    public List<TDocInfoTableVo> queryList(TDocInfoQuery bo) {
        QueryWrapper<TDocInfo> lqw = buildQueryWrapper(bo);
        return baseMapper.selectDocInfoList(lqw);
    }

    private QueryWrapper<TDocInfo> buildQueryWrapper(TDocInfoQuery bo) {
        QueryWrapper<TDocInfo> qw = Wrappers.query();
        qw.like(StringUtils.isNotBlank(bo.getCopName()), "tci.cop_name", bo.getCopName());
        qw.like(StringUtils.isNotBlank(bo.getCopOwner()), "tci.cop_owner", bo.getCopOwner());
        qw.like(StringUtils.isNotBlank(bo.getCopPhone()), "tci.cop_phone", bo.getCopPhone());
        qw.eq("tdi.del_flag", Constants.DEL_FLAG_NORMAL);
        qw.orderByDesc("tdi.create_time");
        return qw;
    }

    /**
     * 新增档案基本
     */
    @Override
    public Boolean insertByBo(TDocInfoBo bo) {
        TDocInfo add = BeanUtil.toBean(bo, TDocInfo.class);
        String[] deptIds = bo.getDeptIds();
        //涉及部门ID串（逗号分割，例：1,2,3 ）
        if (deptIds != null && deptIds.length > 0) {
            add.setDepts(StringUtils.join(deptIds, ","));
        }
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setDocInfoId(add.getDocInfoId());
        }
        return flag;
    }

    /**
     * 修改档案基本
     */
    @Override
    public Boolean updateByBo(TDocInfoBo bo) {
        TDocInfo update = BeanUtil.toBean(bo, TDocInfo.class);
        String[] deptIds = bo.getDeptIds();
        //涉及部门ID串（逗号分割，例：1,2,3 ）
        if (deptIds != null && deptIds.length > 0) {
            update.setDepts(StringUtils.join(deptIds, ","));
        }
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(TDocInfo entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public List<TDocHandleVo> queryReport(Long docInfoId) {
        return docHandleMapper.selectReportList(docInfoId);
    }

    @Override
    @Transactional
    public Boolean docAcc(TDocAccBo bo) {
        TDocAcc tDocAcc = BeanUtil.toBean(bo, TDocAcc.class);
        SysOss upload = iSysOssService.upload(bo.getAccFile());
        tDocAcc.setAccUrl(upload.getUrl());
        return docAccMapper.insert(tDocAcc) > 0;
    }

    /**
     * 批量删除档案基本
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //已经上报的档案不能删除
            boolean exists = docHandleMapper.exists(Wrappers.<TDocHandle>lambdaQuery().in(TDocHandle::getDocInfoId, ids));
            if (exists) {
                throw new ServiceException("已经上报的档案不能删除");
            }
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
