package com.zhonghe.cop.doc.domain.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;


/**
 * 档案基本业务对象 t_doc_info
 *
 * <AUTHOR>
 * @date 2023-12-07
 */

@Data
@ApiModel("档案基本业务对象")
public class TDocInfoBo implements Serializable {

    private static final long serialVersionUID = 5870766220666383329L;
    /**
     * 档案ID
     */
    @ApiModelProperty(value = "档案ID", required = true)
    @NotNull(message = "档案ID不能为空", groups = {EditGroup.class})
    private Long docInfoId;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String note;

    /**
     * 企业ID
     */
    @ApiModelProperty(value = "企业ID", required = true)
    @NotNull(message = "企业ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long copId;

    /**
     * 建档时间
     */
    @ApiModelProperty(value = "建档时间", required = true)
    @NotNull(message = "建档时间不能为空", groups = {AddGroup.class, EditGroup.class})
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date docDate;

    /**
     * 事件描述
     */
    @ApiModelProperty(value = "事件描述")
    @Length(max = 2000, message = "事件描述长度不能超过2000个字符", groups = {AddGroup.class, EditGroup.class})
    private String dealDescription;

    /**
     * 处理依据
     */
    @ApiModelProperty(value = "处理依据")
    @Length(max = 2000, message = "处理依据长度不能超过2000个字符", groups = {AddGroup.class, EditGroup.class})
    private String dealBasis;

    /**
     * 处理措施
     */
    @ApiModelProperty(value = "处理措施")
    @Length(max = 2000, message = "处理措施长度不能超过2000个字符", groups = {AddGroup.class, EditGroup.class})
    private String dealMeasure;

    /**
     * 处理结果
     */
    @ApiModelProperty(value = "处理结果")
    @Length(max = 2000, message = "处理结果长度不能超过2000个字符", groups = {AddGroup.class, EditGroup.class})
    private String dealResult;

    /**
     * 事件启发
     */
    @ApiModelProperty(value = "事件启发")
    @Length(max = 2000, message = "事件启发长度不能超过2000个字符", groups = {AddGroup.class, EditGroup.class})
    private String dealEnlighten;

    /**
     * 事件处置报告
     */
    @ApiModelProperty(value = "事件处置报告")
    @Length(max = 2000, message = "事件启发长度不能超过2000个字符", groups = {AddGroup.class, EditGroup.class})
    private String dealReport;

    /**
     * 涉及部门ID串（逗号分割，例：1,2,3 ）
     */
    @ApiModelProperty(value = "涉及部门ID串（逗号分割，例：1,2,3 ）", required = true)
    @NotEmpty(message = "涉及部门不能为空", groups = {AddGroup.class, EditGroup.class})
    private String[] deptIds;


}
