package com.zhonghe.cop.doc.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.zhonghe.common.annotation.Log;
import com.zhonghe.common.annotation.RepeatSubmit;
import com.zhonghe.common.core.controller.BaseController;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.enums.BusinessType;
import com.zhonghe.cop.doc.domain.bo.TDocHandleBo;
import com.zhonghe.cop.doc.domain.query.TDocInfoQuery;
import com.zhonghe.cop.doc.domain.table.TDocInfoTableVo;
import com.zhonghe.cop.doc.domain.vo.TDocHandleInfoVo;
import com.zhonghe.cop.doc.service.ITDocHandleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 档案上报信息
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
@Validated
@Api(value = "档案上报控制器", tags = {"档案上报管理"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/cop/docHandle")
public class TDocHandleController extends BaseController {

    private final ITDocHandleService iTDocHandleService;

    /**
     * 查询档案上报列表
     */
    @ApiOperation("查询档案上报列表(cop:docHandle:list)")
    @SaCheckPermission("cop:docHandle:list")
    @GetMapping("/list")
    public TableDataInfo<TDocInfoTableVo> list(TDocInfoQuery bo, PageQuery pageQuery) {
        return iTDocHandleService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询档案上报详细信息
     */
    @ApiOperation("查询预案上报详细信息(cop:plaHandle:query)")
    @SaCheckPermission("cop:plaHandle:query")
    @GetMapping("/{docInfoId}")
    public R<TDocHandleInfoVo> queryByDocInfoId(@PathVariable("docInfoId") Long docInfoId) {
        return R.ok(iTDocHandleService.queryByDocInfoId(docInfoId));
    }

    /**
     * 处理档案上报
     */
    @ApiOperation("处理档案上报(cop:docHandle:add)")
    @SaCheckPermission("cop:docHandle:add")
    @Log(title = "档案上报", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated @RequestBody TDocHandleBo bo) {
        return toAjax(iTDocHandleService.insertByBo(bo) ? 1 : 0);
    }


}
