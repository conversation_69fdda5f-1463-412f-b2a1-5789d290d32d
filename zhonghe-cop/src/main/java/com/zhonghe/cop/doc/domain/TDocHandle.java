package com.zhonghe.cop.doc.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zhonghe.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;


/**
 * 档案上报对象 t_doc_handle
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("cop.t_doc_handle")
public class TDocHandle extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 档案上报ID
     */
    @TableId
    private Long docHandleId;
    /**
     * 是否删除0否1是
     */
    @TableLogic
    private String delFlag;
    /**
     * 说明
     */
    private String note;
    /**
     * 档案ID（对应t_doc_info表中的主键）
     */
    private Long docInfoId;
    /**
     * 部门ID
     */
    private Long deptId;
    /**
     * 处理时间
     */
    private Date dealDate;
    /**
     * 事件描述
     */
    private String dealDescription;
    /**
     * 处理依据
     */
    private String dealBasis;
    /**
     * 处理措施
     */
    private String dealMeasure;
    /**
     * 处理结果
     */
    private String dealResult;
    /**
     * 事件启发
     */
    private String dealEnlighten;
    /**
     * 事件处置报告
     */
    private String dealReport;

}
