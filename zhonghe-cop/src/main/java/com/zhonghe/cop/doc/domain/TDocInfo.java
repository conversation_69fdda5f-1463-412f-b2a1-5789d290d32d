package com.zhonghe.cop.doc.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zhonghe.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;


/**
 * 档案基本对象 t_doc_info
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("cop.t_doc_info")
public class TDocInfo extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 档案ID
     */
    @TableId
    private Long docInfoId;
    /**
     * 是否删除
     */
    @TableLogic
    private String delFlag;
    /**
     * 备注
     */
    private String note;
    /**
     * 企业ID
     */
    private Long copId;
    /**
     * 建档时间
     */
    private Date docDate;
    /**
     * 事件描述
     */
    private String dealDescription;
    /**
     * 处理依据
     */
    private String dealBasis;
    /**
     * 处理措施
     */
    private String dealMeasure;
    /**
     * 处理结果
     */
    private String dealResult;
    /**
     * 事件启发
     */
    private String dealEnlighten;
    /**
     * 事件处置报告
     */
    private String dealReport;
    /**
     * 涉及部门ID串（逗号分割，例：1,2,3 ）
     */
    private String depts;
}
