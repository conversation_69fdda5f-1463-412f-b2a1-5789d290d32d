package com.zhonghe.cop.doc.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhonghe.common.constant.Constants;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.helper.LoginHelper;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.cop.bam.domain.vo.TCopInformationVo;
import com.zhonghe.cop.bam.mapper.TCopInformationMapper;
import com.zhonghe.cop.doc.domain.TDocAcc;
import com.zhonghe.cop.doc.domain.TDocHandle;
import com.zhonghe.cop.doc.domain.TDocInfo;
import com.zhonghe.cop.doc.domain.bo.TDocHandleBo;
import com.zhonghe.cop.doc.domain.query.TDocInfoQuery;
import com.zhonghe.cop.doc.domain.table.TDocInfoTableVo;
import com.zhonghe.cop.doc.domain.vo.TDocAccVo;
import com.zhonghe.cop.doc.domain.vo.TDocHandleInfoVo;
import com.zhonghe.cop.doc.domain.vo.TDocHandleVo;
import com.zhonghe.cop.doc.domain.vo.TDocInfoVo;
import com.zhonghe.cop.doc.mapper.TDocAccMapper;
import com.zhonghe.cop.doc.mapper.TDocHandleMapper;
import com.zhonghe.cop.doc.mapper.TDocInfoMapper;
import com.zhonghe.cop.doc.service.ITDocHandleService;
import com.zhonghe.cop.score.domain.vo.CopRiskVo;
import com.zhonghe.cop.score.service.ITCopScoreYearService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * 档案上报Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
@RequiredArgsConstructor
@Service
public class TDocHandleServiceImpl implements ITDocHandleService {

    private final TDocHandleMapper baseMapper;
    private final TDocInfoMapper docInfoMapper;
    private final TCopInformationMapper copInformationMapper;
    private final ITCopScoreYearService iTCopScoreYearService;
    private final TDocAccMapper tDocAccMapper;


    /**
     * 查询档案上报
     */
    @Override
    public TDocHandleInfoVo queryByDocInfoId(Long docInfoId) {
        TDocHandleInfoVo tDocHandleInfoVo = new TDocHandleInfoVo();

        //查询档案信息
        TDocInfoVo tDocInfoVo = docInfoMapper.selectVoById(docInfoId);
        //查询本部门上报处理信息
        TDocHandleVo tDocHandleVo = baseMapper.selectVoOne(Wrappers.<TDocHandle>lambdaQuery().eq(TDocHandle::getDocInfoId, docInfoId).eq(TDocHandle::getDeptId, LoginHelper.getDeptId()));
        tDocHandleInfoVo.setDocHandle(tDocHandleVo);
        //查询附件信息
        if (tDocHandleVo != null) {
            List<TDocAccVo> tDocAccVos = tDocAccMapper.selectVoList(Wrappers.<TDocAcc>lambdaQuery().eq(TDocAcc::getDocId, tDocHandleVo.getDocHandleId()));
            tDocHandleVo.setDocAccs(tDocAccVos);
        }
        //查询企业信息
        TCopInformationVo tCopInformation = copInformationMapper.selectVoById(tDocInfoVo.getCopId());
        tDocHandleInfoVo.setCopInformation(tCopInformation);
        //风险级别
        List<CopRiskVo> copRiskVos = iTCopScoreYearService.queryRisk(tDocInfoVo.getCopId());
        tDocHandleInfoVo.setCopRisks(copRiskVos);

        return tDocHandleInfoVo;
    }

    /**
     * 查询档案上报列表
     */
    @Override
    public TableDataInfo<TDocInfoTableVo> queryPageList(TDocInfoQuery bo, PageQuery pageQuery) {
        QueryWrapper<TDocInfo> lqw = buildQueryWrapper(bo);
        Page<TDocInfoTableVo> result = baseMapper.selectDocHandleList(pageQuery.build(), lqw);
        for (TDocInfoTableVo record : result.getRecords()) {
            //查询当前部门上报处理信息
            TDocHandleVo tDocHandleVo = baseMapper.selectVoOne(Wrappers.<TDocHandle>lambdaQuery()
                .select(TDocHandle::getDocHandleId)
                .eq(TDocHandle::getDocInfoId, record.getDocInfoId()).eq(TDocHandle::getDeptId, LoginHelper.getDeptId()));
            record.setDocHandleId(tDocHandleVo == null ? null : tDocHandleVo.getDocHandleId());
        }
        return TableDataInfo.build(result);
    }

    /**
     * 查询档案上报列表
     */
    @Override
    public List<TDocInfoTableVo> queryList(TDocInfoQuery bo) {
        QueryWrapper<TDocInfo> qw = buildQueryWrapper(bo);
        return baseMapper.selectDocHandleList(qw);
    }

    private QueryWrapper<TDocInfo> buildQueryWrapper(TDocInfoQuery bo) {
        QueryWrapper<TDocInfo> qw = Wrappers.query();
        qw.like(StringUtils.isNotBlank(bo.getCopName()), "tci.cop_name", bo.getCopName());
        qw.like(StringUtils.isNotBlank(bo.getCopOwner()), "tci.cop_owner", bo.getCopOwner());
        qw.like(StringUtils.isNotBlank(bo.getCopPhone()), "tci.cop_phone", bo.getCopPhone());
        qw.eq("tdi.del_flag", Constants.DEL_FLAG_NORMAL);
        qw.eq("tci.del_flag", Constants.DEL_FLAG_NORMAL);
        if (!LoginHelper.isAdmin()) {
            qw.like("tdi.depts", LoginHelper.getDeptId());
        }
        qw.orderByDesc("tdi.create_time");
        return qw;
    }

    /**
     * 新增档案上报
     */
    @Override
    public Boolean insertByBo(TDocHandleBo bo) {
        TDocHandle add = BeanUtil.toBean(bo, TDocHandle.class);
        add.setDeptId(LoginHelper.getDeptId());
        if (bo.getDocHandleId() != null) {
            //修改
            return baseMapper.updateById(add) > 0;
        }
        boolean flag = baseMapper.insert(add) > 0;

        return flag;
    }

    /**
     * 修改档案上报
     */
    @Override
    public Boolean updateByBo(TDocHandleBo bo) {
        TDocHandle update = BeanUtil.toBean(bo, TDocHandle.class);
        update.setDeptId(LoginHelper.getDeptId());
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(TDocHandle entity) {
        //TODO 做一些数据校验,如唯一约束
    }


    /**
     * 批量删除档案上报
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
