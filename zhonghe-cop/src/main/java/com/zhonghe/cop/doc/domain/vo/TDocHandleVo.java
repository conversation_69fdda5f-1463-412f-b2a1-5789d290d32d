package com.zhonghe.cop.doc.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;


/**
 * 档案上报视图对象 t_doc_handle
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
@Data
@ApiModel("档案上报视图对象")
@ExcelIgnoreUnannotated
public class TDocHandleVo {

    private static final long serialVersionUID = 1L;

    /**
     * 档案上报ID
     */
    @ExcelProperty(value = "档案上报ID")
    @ApiModelProperty("档案上报ID")
    private Long docHandleId;

    /**
     * 说明
     */
    @ExcelProperty(value = "说明")
    @ApiModelProperty("说明")
    private String note;

    /**
     * 档案ID（对应t_doc_info表中的主键）
     */
    @ExcelProperty(value = "档案ID")
    @ApiModelProperty("档案ID（对应t_doc_info表中的主键）")
    private Long docInfoId;


    /**
     * 部门ID
     */
    @ExcelProperty(value = "部门ID")
    @ApiModelProperty("部门ID")
    private Long deptId;

    /**
     * 部门名称
     */
    @ExcelProperty(value = "部门名称")
    @ApiModelProperty("部门名称")
    private String deptName;

    /**
     * 处理时间
     */
    @ExcelProperty(value = "处理时间")
    @ApiModelProperty("处理时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date dealDate;

    /**
     * 事件描述
     */
    @ExcelProperty(value = "事件描述")
    @ApiModelProperty("事件描述")
    private String dealDescription;

    /**
     * 处理依据
     */
    @ExcelProperty(value = "处理依据")
    @ApiModelProperty("处理依据")
    private String dealBasis;

    /**
     * 处理措施
     */
    @ExcelProperty(value = "处理措施")
    @ApiModelProperty("处理措施")
    private String dealMeasure;

    /**
     * 处理结果
     */
    @ExcelProperty(value = "处理结果")
    @ApiModelProperty("处理结果")
    private String dealResult;

    /**
     * 事件启发
     */
    @ExcelProperty(value = "事件启发")
    @ApiModelProperty("事件启发")
    private String dealEnlighten;

    /**
     * 事件处置报告
     */
    @ExcelProperty(value = "事件处置报告")
    @ApiModelProperty("事件处置报告")
    private String dealReport;

    /**
     * 文书附件
     */
    @ApiModelProperty("文书附件")
    private List<TDocAccVo> docAccs;


}
