package com.zhonghe.cop.doc.service;

import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.cop.doc.domain.bo.TDocAccBo;
import com.zhonghe.cop.doc.domain.bo.TDocInfoBo;
import com.zhonghe.cop.doc.domain.query.TDocInfoQuery;
import com.zhonghe.cop.doc.domain.table.TDocInfoTableVo;
import com.zhonghe.cop.doc.domain.vo.QueryDocInfoVo;
import com.zhonghe.cop.doc.domain.vo.TDocHandleVo;

import java.util.Collection;
import java.util.List;

/**
 * 档案基本Service接口
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
public interface ITDocInfoService {

    /**
     * 查询档案基本
     */
    QueryDocInfoVo queryById(Long docInfoId);

    /**
     * 查询档案基本列表
     */
    TableDataInfo<TDocInfoTableVo> queryPageList(TDocInfoQuery bo, PageQuery pageQuery);

    /**
     * 查询档案基本列表
     */
    List<TDocInfoTableVo> queryList(TDocInfoQuery bo);

    /**
     * 修改档案基本
     */
    Boolean insertByBo(TDocInfoBo bo);

    /**
     * 修改档案基本
     */
    Boolean updateByBo(TDocInfoBo bo);

    /**
     * 校验并批量删除档案基本信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 附件上传
     *
     * @param bo
     * @return
     */
    Boolean docAcc(TDocAccBo bo);

    /**
     * 查询上报内容
     *
     * @param docInfoId
     * @return
     */
    List<TDocHandleVo> queryReport(Long docInfoId);
}
