package com.zhonghe.cop.doc.mapper;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhonghe.common.core.mapper.BaseMapperPlus;
import com.zhonghe.cop.doc.domain.TDocInfo;
import com.zhonghe.cop.doc.domain.table.TDocInfoTableVo;
import com.zhonghe.cop.doc.domain.vo.TDocInfoVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 档案基本Mapper接口
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
public interface TDocInfoMapper extends BaseMapperPlus<TDocInfoMapper, TDocInfo, TDocInfoVo> {

    /**
     * 分页查询档案基本列表
     *
     * @param build
     * @param lqw
     * @return
     */
    Page<TDocInfoTableVo> selectDocInfoList(Page<TDocInfo> build, @Param(Constants.WRAPPER) Wrapper<TDocInfo> lqw);

    /**
     * 查询档案基本列表
     */
    List<TDocInfoTableVo> selectDocInfoList(@Param(Constants.WRAPPER) Wrapper<TDocInfo> lqw);

}
