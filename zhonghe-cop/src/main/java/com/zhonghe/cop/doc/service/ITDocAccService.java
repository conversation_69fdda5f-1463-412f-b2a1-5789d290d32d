package com.zhonghe.cop.doc.service;

import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.cop.doc.domain.bo.TDocAccBo;
import com.zhonghe.cop.doc.domain.vo.TDocAccVo;

import javax.servlet.http.HttpServletResponse;
import java.util.Collection;
import java.util.List;

/**
 * 档案文书附件Service接口
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
public interface ITDocAccService {

    /**
     * 查询档案文书附件
     */
    TDocAccVo queryById(Long docAccId);

    /**
     * 查询档案文书附件列表
     */
    TableDataInfo<TDocAccVo> queryPageList(TDocAccBo bo, PageQuery pageQuery);

    /**
     * 查询档案文书附件列表
     */
    List<TDocAccVo> queryList(TDocAccBo bo);

    /**
     * 修改档案文书附件
     */
    Boolean insertByBo(TDocAccBo bo);

    /**
     * 修改档案文书附件
     */
    Boolean updateByBo(TDocAccBo bo);

    /**
     * 校验并批量删除档案文书附件信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据文书id查询附件
     *
     * @param docId
     * @return
     */
    List<TDocAccVo> queryListByDocId(Long docId);

    /**
     * 下载附件
     *
     * @param docAccId
     * @param response
     */
    void download(Long docAccId, HttpServletResponse response);
}
