package com.zhonghe.cop.bam.domain.bo;

import com.zhonghe.common.core.domain.BaseEntity;
import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


/**
 * 模板业务对象 t_tem_template
 *
 * <AUTHOR>
 * @date 2023-12-10
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("模板业务对象")

public class TTemTemplateBo extends BaseEntity {

    private static final long serialVersionUID = 7929218186346522321L;
    /**
     * 模板ID
     */
    @ApiModelProperty(value = "模板ID", required = true)
    @NotNull(message = "模板ID不能为空", groups = {EditGroup.class})
    private Long templateId;


    /**
     * 说明
     */
    @ApiModelProperty(value = "说明", required = true)
    private String note;

    /**
     * 模板类型ID
     */
    @ApiModelProperty(value = "模板类型ID", required = true)
    @NotNull(message = "模板类型ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long templateTypeId;

    /**
     * 模板缩写
     */
    @ApiModelProperty(value = "模板缩写", required = true)
    @NotBlank(message = "模板缩写不能为空", groups = {AddGroup.class, EditGroup.class})
    private String templateShort;

    /**
     * 模板内容
     */
    @ApiModelProperty(value = "模板内容", required = true)
    @NotBlank(message = "模板内容不能为空", groups = {AddGroup.class, EditGroup.class})
    private String templateContent;

    /**
     * 模板排序
     */
    @ApiModelProperty(value = "模板排序")
    private Integer templateOrder;


}
