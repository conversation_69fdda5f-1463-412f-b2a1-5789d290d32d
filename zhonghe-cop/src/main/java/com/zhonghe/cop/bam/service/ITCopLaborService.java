package com.zhonghe.cop.bam.service;

import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.cop.bam.domain.bo.TCopLaborBo;
import com.zhonghe.cop.bam.domain.vo.TCopLaborVo;

import java.util.Collection;
import java.util.List;

/**
 * 劳务人员Service接口
 *
 * <AUTHOR>
 * @date 2023-12-21
 */
public interface ITCopLaborService {

    /**
     * 查询劳务人员
     */
    TCopLaborVo queryById(Long copLaborId);

    /**
     * 查询劳务人员列表
     */
    TableDataInfo<TCopLaborVo> queryPageList(TCopLaborBo bo, PageQuery pageQuery);

    /**
     * 查询劳务人员列表
     */
    List<TCopLaborVo> queryList(TCopLaborBo bo);

    /**
     * 企业ID查询劳务人员列表
     *
     * @param copId
     * @return
     */
    List<TCopLaborVo> queryList(Long copId);

    /**
     * 修改劳务人员
     */
    Boolean insertByBo(TCopLaborBo bo);

    /**
     * 修改劳务人员
     */
    Boolean updateByBo(TCopLaborBo bo);

    /**
     * 校验并批量删除劳务人员信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);
}
