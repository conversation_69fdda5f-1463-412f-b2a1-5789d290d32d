package com.zhonghe.cop.bam.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaCheckPermission;
import com.zhonghe.common.annotation.Anonymous;
import com.zhonghe.common.annotation.Log;
import com.zhonghe.common.annotation.RepeatSubmit;
import com.zhonghe.common.core.controller.BaseController;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import com.zhonghe.common.enums.BusinessType;
import com.zhonghe.common.excel.DefautExcelResult;
import com.zhonghe.common.excel.ExcelResult;
import com.zhonghe.common.exception.ServiceException;
import com.zhonghe.common.helper.LoginHelper;
import com.zhonghe.common.utils.poi.ExcelUtil;
import com.zhonghe.common.utils.spring.SpringUtils;
import com.zhonghe.cop.bam.domain.bo.AddCopContactBo;
import com.zhonghe.cop.bam.domain.bo.CancelCopBo;
import com.zhonghe.cop.bam.domain.bo.TCopInformationBo;
import com.zhonghe.cop.bam.domain.bo.TCopLaborBo;
import com.zhonghe.cop.bam.domain.excel.AddTCopInformationExcel;
import com.zhonghe.cop.bam.domain.excel.CancelTCopInformationExcel;
import com.zhonghe.cop.bam.domain.excel.EditTCopInformationExcel;
import com.zhonghe.cop.bam.domain.query.TCopInformationQuery;
import com.zhonghe.cop.bam.domain.vo.CopGeomVo;
import com.zhonghe.cop.bam.domain.vo.TCopInformationVo;
import com.zhonghe.cop.bam.domain.vo.TCopLaborVo;
import com.zhonghe.cop.bam.domain.vo.TMesContactVo;
import com.zhonghe.cop.bam.event.UpdateCopGeomEvent;
import com.zhonghe.cop.bam.listener.AddTCopInformationImportListener;
import com.zhonghe.cop.bam.listener.CancelTCopInformationImportListener;
import com.zhonghe.cop.bam.listener.EditTCopInformationImportListener;
import com.zhonghe.cop.bam.service.AsyncTaskService;
import com.zhonghe.cop.bam.service.ITCopLaborService;
import com.zhonghe.cop.bam.service.TICopInformationService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;

/**
 * 企业信息
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
@Slf4j
@Validated
@Api(value = "企业信息控制器", tags = {"企业信息管理"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/cop/copInformation")
public class TCopInformationController extends BaseController {

    private final TICopInformationService iTCopInformationService;
    private final ITCopLaborService iTCopLaborService;
    private final AsyncTaskService asyncTaskService;

    /**
     * 查询企业信息列表
     */
    @ApiOperation("查询企业信息列表(cop:copInformation:list)")
    @SaCheckPermission("cop:copInformation:list")
    @GetMapping("/list")
    public TableDataInfo<TCopInformationVo> list(TCopInformationQuery bo, PageQuery pageQuery) {
        return iTCopInformationService.queryPageList(bo, pageQuery);
    }

    /**
     * 在库企业查询
     */
    @ApiOperation("在库企业查询(cop:copInformation:list)")
    @SaCheckPermission("cop:copInformation:list")
    @GetMapping("/query")
    public R<Boolean> queryCopName(@NotBlank(message = "查询企业名称不能为空") @RequestParam String copName) {
        return R.ok(iTCopInformationService.queryCopName(copName));
    }


    /*
     *----------------------------------------企业信息excel导入----------------------------------------
     */

    /**
     * 企业信息excel新增文件导入
     */
    @Anonymous
    @ApiOperation("企业信息excel新增文件导入(cop:copInformation:import)")
    //@SaCheckPermission("cop:copInformation:import")
    @Log(title = "企业信息", businessType = BusinessType.IMPORT)
    @PostMapping("/excel/addFile")
    public R<ExcelResult<AddTCopInformationExcel>> insertFile(@RequestParam("file") MultipartFile file) throws Exception {
        // 获取其后缀
        String extension = Objects.requireNonNull(file.getOriginalFilename()).substring(file.getOriginalFilename().lastIndexOf(".") + 1);
        if (!("xls".equals(extension) || "xlsx".equals(extension))) {
            throw new ServiceException("文件格式有误,请检查上传文件格式!");
        }
        ExcelResult<AddTCopInformationExcel> result = ExcelUtil.importExcel(file.getInputStream(), AddTCopInformationExcel.class, new AddTCopInformationImportListener(new DefautExcelResult<>()));
        return R.ok(result);
    }

    /**
     * 全量企业信息excel更新文件导入
     */
    @Anonymous
    @ApiOperation("全量企业信息excel更新文件导入(cop:copInformation:import)")
    //@SaCheckPermission("cop:copInformation:import")
    @Log(title = "企业信息", businessType = BusinessType.IMPORT)
    @PostMapping("/excel/editFile")
    public R<ExcelResult<EditTCopInformationExcel>> updateFile(@RequestParam("file") MultipartFile file) throws Exception {
        // 获取其后缀
        String extension = Objects.requireNonNull(file.getOriginalFilename()).substring(file.getOriginalFilename().lastIndexOf(".") + 1);
        if (!("xls".equals(extension) || "xlsx".equals(extension))) {
            throw new ServiceException("文件格式有误,请检查上传文件格式!");
        }
        ExcelResult<EditTCopInformationExcel> result =
            ExcelUtil.importExcel(file.getInputStream(), EditTCopInformationExcel.class, new EditTCopInformationImportListener(new DefautExcelResult<>()));
        return R.ok(result);
    }

    /**
     * 企业信息excel注销文件导入
     */
    @Anonymous
    @ApiOperation("企业信息excel注销文件导入(cop:copInformation:import)")
    //@SaCheckPermission("cop:copInformation:import")
    @Log(title = "企业信息", businessType = BusinessType.IMPORT)
    @PostMapping("/excel/cancelFile")
    public R<ExcelResult<CancelTCopInformationExcel>> cancelFile(@RequestParam("file") MultipartFile file) throws Exception {
        // 获取其后缀
        String extension = Objects.requireNonNull(file.getOriginalFilename()).substring(file.getOriginalFilename().lastIndexOf(".") + 1);
        if (!("xls".equals(extension) || "xlsx".equals(extension))) {
            throw new ServiceException("文件格式有误,请检查上传文件格式!");
        }
        ExcelResult<CancelTCopInformationExcel> result = ExcelUtil.importExcel(file.getInputStream(), CancelTCopInformationExcel.class, new CancelTCopInformationImportListener(new DefautExcelResult<>()));
        return R.ok(result);
    }

    /**
     * ----------------------------------------企业信息excel数据批量导入----------------------------------------
     */
    /**
     * 企业信息excel新增数据批量导入
     */
    @RepeatSubmit()
    @Anonymous
    @ApiOperation("企业信息excel新增数据批量导入(cop:copInformation:import)")
    //@SaCheckPermission("cop:copInformation:import")
    @Log(title = "企业信息", businessType = BusinessType.IMPORT)
    @PostMapping("/excel/addData")
    public R<Void> excelAddData(@RequestBody List<AddTCopInformationExcel> bo) {
        return toAjax(iTCopInformationService.excelAddData(bo) ? 1 : 0);
    }

    /**
     * 全量企业信息excel更新数据批量导入
     */
    @Anonymous
    @RepeatSubmit()
    @ApiOperation("全量企业信息excel更新数据批量导入(cop:copInformation:import)")
    //@SaCheckPermission("cop:copInformation:import")
    @Log(title = "企业信息导入", businessType = BusinessType.IMPORT)
    @PostMapping("/excel/editData")
    public R<Void> excelEditData(@RequestBody List<EditTCopInformationExcel> bo) {
        String userId = LoginHelper.getUsername();
        log.info("用户{}开始异步处理Excel编辑数据，数据量: {}", userId, bo.size());
        String taskId = asyncTaskService.createTask("COP_EXCEL_UPDATE", userId, bo.size());
        CompletableFuture<Boolean> future = iTCopInformationService.excelEditDataAsync(bo);
        asyncTaskService.executeWithTracking(taskId, future);
        return R.ok("任务已提交，任务ID: " + taskId);
    }

    /**
     * 企业信息excel注销数据批量导入
     */
    @RepeatSubmit()
    @Anonymous
    @ApiOperation("企业信息excel注销数据批量导入(cop:copInformation:import)")
    //@SaCheckPermission("cop:copInformation:import")
    @Log(title = "企业信息", businessType = BusinessType.IMPORT)
    @PostMapping("/excel/cancelData")
    public R<Void> excelCancelData(@RequestBody List<CancelTCopInformationExcel> bo) {
        return toAjax(iTCopInformationService.excelCancelData(bo) ? 1 : 0);
    }


    /**
     * ----------------------------------------企业信息excel模板下载----------------------------------------
     */

    /**
     * 下载企业批量新增标准格式
     */
    @SaCheckLogin
    @ApiOperation("下载企业批量新增标准格式")
    @GetMapping("/addTemplateDownload")
    public void addTemplateDownload(HttpServletResponse response) {
        ExcelUtil.exportExcel(new ArrayList<>(), "企业批量新增标准格式", AddTCopInformationExcel.class, response);
    }

    /**
     * 下载企业批量更新标准格式
     */
    @SaCheckLogin
    @ApiOperation("下载企业批量更新标准格式")
    @GetMapping("/editTemplateDownload")
    public void editTemplateDownload(HttpServletResponse response) {
        ExcelUtil.exportExcel(new ArrayList<>(), "企业批量更新标准格式", EditTCopInformationExcel.class, response);
    }

    /**
     * 下载企业批量注销标准格式
     */
    @SaCheckLogin
    @ApiOperation("下载企业批量注销标准格式")
    @GetMapping("/cancelTemplateDownload")
    public void cancelTemplateDownload(HttpServletResponse response) {
        ExcelUtil.exportExcel(new ArrayList<>(), "企业批量注销标准格式", CancelTCopInformationExcel.class, response);
    }


    /**
     * 导出企业信息列表
     */
    @ApiOperation("导出企业信息列表(cop:copInformation:export)")
    @SaCheckPermission("cop:copInformation:export")
    @Log(title = "企业信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(TCopInformationQuery bo, HttpServletResponse response) {
        List<TCopInformationVo> list = iTCopInformationService.queryList(bo);
        ExcelUtil.exportExcel(list, "企业信息", TCopInformationVo.class, response);
    }

    /**
     * 获取企业信息详细信息
     */
    @ApiOperation("获取企业信息详细信息(cop:copInformation:query)")
    @SaCheckPermission("cop:copInformation:query")
    @GetMapping("/{copId}")
    public R<TCopInformationVo> getInfo(@ApiParam("主键")
                                        @NotNull(message = "主键不能为空")
                                        @PathVariable("copId") Long copId) {
        return R.ok(iTCopInformationService.queryById(copId));
    }

    /**
     * 根据企业地址查询坐标信息
     */
    @ApiOperation("根据企业地址查询坐标信息(cop:copInformation:query)")
    @SaCheckPermission("cop:copInformation:query")
    @PostMapping("/getAddress")
    public R<Map<String, Object>> getAddressInfo(@ApiParam("企业名称")
                                                 @NotBlank(message = "企业名称不能为空")
                                                 @RequestParam("copName") String copName) {
        return R.ok(iTCopInformationService.getAddressInfo(copName));
    }

    /**
     * 根据经纬坐标查询附近企业
     */
    @ApiOperation("根据经纬坐标查询最近的企业(cop:copInformation:query)")
    @SaCheckPermission("cop:copInformation:query")
    @GetMapping("/getNearby")
    public R<List<CopGeomVo>> getNearby(@ApiParam("经度")
                                        @NotNull(message = "经度不能为空")
                                        @RequestParam("lon") Double lon,
                                        @ApiParam("纬度")
                                        @NotNull(message = "纬度不能为空")
                                        @RequestParam("lat") Double lat) {
        return R.ok(iTCopInformationService.getNearby(lon, lat));
    }

    /**
     * 新增企业信息
     */
    @ApiOperation("新增企业信息(cop:copInformation:add)")
    @SaCheckPermission("cop:copInformation:add")
    @Log(title = "企业信息", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody TCopInformationBo bo) {
        return toAjax(iTCopInformationService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改企业信息
     */
    @ApiOperation("修改企业信息(cop:copInformation:edit)")
    @SaCheckPermission("cop:copInformation:edit")
    @Log(title = "企业信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody TCopInformationBo bo) {
        return toAjax(iTCopInformationService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除企业信息
     */
    @ApiOperation("删除企业信息(cop:copInformation:remove)")
    @SaCheckPermission("cop:copInformation:remove")
    @Log(title = "企业信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{copIds}")
    public R<Void> remove(@ApiParam("主键串")
                          @NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] copIds) {
        return toAjax(iTCopInformationService.deleteWithValidByIds(Arrays.asList(copIds), true) ? 1 : 0);
    }

    /**
     * 注销企业信息
     */
    @ApiOperation("注销企业信息(cop:copInformation:cancel)")
    @SaCheckPermission("cop:copInformation:cancel")
    @Log(title = "企业信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/cancel")
    public R<Void> cancel(@Validated @RequestBody CancelCopBo bo) {
        return toAjax(iTCopInformationService.cancel(bo) ? 1 : 0);
    }

    /**
     * 恢复企业信息
     */
    @ApiOperation("恢复企业信息(cop:copInformation:recovery)")
    @SaCheckPermission("cop:copInformation:recovery")
    @Log(title = "企业信息", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/recovery/{copIds}")
    public R<Void> recovery(@ApiParam("主键串")
                            @NotEmpty(message = "主键不能为空")
                            @PathVariable Long[] copIds) {
        return toAjax(iTCopInformationService.recovery(Arrays.asList(copIds)) ? 1 : 0);
    }


    /**
     * 查询企业联系人列表
     */
    @ApiOperation("查询企业联系人列表(cop:copInformation:contact:list)")
    @SaCheckPermission("cop:copInformation:contact:list")
    @GetMapping("/contact/list/{copId}")
    public R<List<TMesContactVo>> contactList(@PathVariable("copId") @NotNull(message = "企业ID不能为空") @ApiParam("企业ID") Long copId) {
        return R.ok(iTCopInformationService.contactList(copId));
    }

    /**
     * 新增企业联系人
     */
    @ApiOperation("新增企业联系人(cop:copInformation:contact:add)")
    @SaCheckPermission("cop:copInformation:contact:add")
    @Log(title = "企业联系人", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/contact")
    public R<Void> addContact(@Validated(AddGroup.class) @RequestBody AddCopContactBo bo) {
        return toAjax(iTCopInformationService.insertContact(bo) ? 1 : 0);
    }

    /**
     * 修改企业联系人
     */
    @ApiOperation("修改企业联系人(cop:copInformation:contact:edit)")
    @SaCheckPermission("cop:copInformation:contact:edit")
    @Log(title = "企业联系人", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/contact")
    public R<Void> editContact(@Validated(EditGroup.class) @RequestBody AddCopContactBo bo) {
        return toAjax(iTCopInformationService.updateConract(bo) ? 1 : 0);
    }

    /**
     * 删除企业联系人
     */
    @ApiOperation("删除企业联系人(cop:copInformation:contact:remove)")
    @SaCheckPermission("cop:copInformation:contact:remove")
    @Log(title = "删除企业联系人", businessType = BusinessType.DELETE)
    @DeleteMapping("/contact/{mesContactIds}")
    public R<Void> removeContact(@ApiParam("主键串")
                                 @NotEmpty(message = "主键不能为空")
                                 @PathVariable Long[] mesContactIds) {
        return toAjax(iTCopInformationService.deleteContact(Arrays.asList(mesContactIds), true) ? 1 : 0);
    }

    /**
     * 查询劳务人员列表
     */
    @ApiOperation("查询劳务人员列表(cop:copLabor:list)")
    @SaCheckPermission("cop:copLabor:list")
    @GetMapping("/copLabor/{copId}")
    public R<List<TCopLaborVo>> copLaborList(@NotNull(message = "企业ID不能为空") @ApiParam("企业ID") @PathVariable("copId") Long copId) {
        return R.ok(iTCopLaborService.queryList(copId));
    }

    /**
     * 新增劳务人员
     */
    @ApiOperation("新增劳务人员(cop:copLabor:add)")
    @SaCheckPermission("cop:copLabor:add")
    @Log(title = "劳务人员", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("copLabor")
    public R<Void> addCopLabor(@Validated(AddGroup.class) @RequestBody TCopLaborBo bo) {
        return toAjax(iTCopLaborService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改劳务人员
     */
    @ApiOperation("修改劳务人员(cop:copLabor:edit)")
    @SaCheckPermission("cop:copLabor:edit")
    @Log(title = "劳务人员", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("copLabor")
    public R<Void> editCopLabor(@Validated(EditGroup.class) @RequestBody TCopLaborBo bo) {
        return toAjax(iTCopLaborService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除劳务人员
     */
    @ApiOperation("删除劳务人员(cop:copLabor:remove)")
    @SaCheckPermission("cop:copLabor:remove")
    @Log(title = "劳务人员", businessType = BusinessType.DELETE)
    @DeleteMapping("/copLabor/{copLaborIds}")
    public R<Void> removeCopLabor(@ApiParam("主键串")
                                  @NotEmpty(message = "主键不能为空")
                                  @PathVariable Long[] copLaborIds) {
        return toAjax(iTCopLaborService.deleteWithValidByIds(Arrays.asList(copLaborIds), true) ? 1 : 0);
    }


    /**
     * 市场主体企业导入
     */
    @ApiOperation("市场主体企业导入(cop:copInformation:import)")
    @Anonymous
    @Log(title = "市场主体企业导入", businessType = BusinessType.INSERT)
    @PostMapping(value = "/importSCZTCop")
    public void importSCZTCop(MultipartFile file,
                              @ApiParam("1规上2上市3高新") @NotBlank(message = "类型不能为空") String type) {
        iTCopInformationService.importSCZTCop(file, type);
        R.ok();
    }

    /**
     * 批量更新企业地理位置
     */
    @Anonymous
    @RequestMapping(value = "/updateCopGeom")
    public void updateGeom() {
        SpringUtils.publishEvent(new UpdateCopGeomEvent(this));
        R.ok();
    }

}
