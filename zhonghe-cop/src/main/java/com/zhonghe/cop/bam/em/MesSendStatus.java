package com.zhonghe.cop.bam.em;

import lombok.Getter;

/**
 * @Author: lpg
 * @Date: 2023/12/11/09:03
 * @Description:
 */
@Getter
public enum MesSendStatus {
    /**
     * 未发送
     */
    NO(0, "未发送"),
    /**
     * 已发送
     */
    YES(1, "已发送"),

    /**
     * 发送失败
     */
    FAIL(2, "发送失败"),
    /**
     * 部分失败
     */
    PART_FAIL(3, "部分失败");


    private Integer code;
    private String name;

    MesSendStatus(Integer code, String name) {
        this.code = code;
        this.name = name;
    }


}
