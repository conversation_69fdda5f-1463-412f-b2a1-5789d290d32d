package com.zhonghe.cop.bam.service;

import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.common.core.domain.entity.SysUser;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.cop.bam.domain.bo.TMesRecordBo;
import com.zhonghe.cop.bam.domain.query.TMesRecordQuery;
import com.zhonghe.cop.bam.domain.vo.TMesRecordVo;

import java.util.Collection;
import java.util.List;

/**
 * 发送记录Service接口
 *
 * <AUTHOR>
 * @date 2023-12-11
 */
public interface ITMesRecordService {

    /**
     * 查询发送记录
     */
    TMesRecordVo queryById(Long mesRecordId);

    /**
     * 查询发送记录列表
     */
    TableDataInfo<TMesRecordVo> queryPageList(TMesRecordQuery bo, PageQuery pageQuery);

    /**
     * 查询发送记录列表
     */
    List<TMesRecordVo> queryList(TMesRecordQuery bo);

    /**
     * 修改发送记录
     */
    Boolean insertByBo(TMesRecordBo bo);

    /**
     * 修改发送记录
     */
    Boolean updateByBo(TMesRecordBo bo);

    /**
     * 校验并批量删除发送记录信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据批次号查询发送记录
     *
     * @param batchNo
     * @return
     */
    TMesRecordVo queryByBatchNo(String batchNo);

    /**
     * 根据批次号删除发送记录
     *
     * @param list
     * @param b
     * @return
     */
    Boolean deleteWithValidByBatchNos(List<String> list, boolean b);

    /**
     * 发送用户消息
     *
     * @param sysUsers
     * @param message
     */
    void sendUserMessage(List<SysUser> sysUsers, String message);

    /**
     * 发送短信
     *
     * @param batchNo
     * @return
     */
    R<String> sendMes(String batchNo);

    /**
     * 重发短信
     *
     * @return
     */
    Boolean reissue();
}
