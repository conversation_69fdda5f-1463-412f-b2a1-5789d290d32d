package com.zhonghe.cop.bam.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.zhonghe.common.annotation.ExcelDictFormat;
import com.zhonghe.common.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;


/**
 * 通讯录，添加用户和企业时要往这里添加数据视图对象 t_mes_contact
 *
 * <AUTHOR>
 * @date 2023-12-08
 */
@Data
@ApiModel("通讯录，添加用户和企业时要往这里添加数据视图对象")
@ExcelIgnoreUnannotated
public class TMesContactVo implements TransPojo, Serializable {

    private static final long serialVersionUID = 1L;

    private Map<String, Object> transMap = new HashMap<>();

    /**
     * 通讯录ID
     */
    @ExcelProperty(value = "通讯录ID")
    @ApiModelProperty("通讯录ID")
    private Long mesContactId;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    @ApiModelProperty("备注")
    private String note;

    /**
     * 联系人描述
     */
    @ExcelProperty(value = "联系人描述")
    @ApiModelProperty("联系人描述")
    private String contactDescription;

    /**
     * 姓名
     */
    @ExcelProperty(value = "姓名")
    @ApiModelProperty("姓名")
    private String contactName;

    /**
     * 电话
     */
    @ExcelProperty(value = "电话")
    @ApiModelProperty("电话")
    private String contactNumber;

    /**
     * 联系人类型
     */
    @ExcelProperty(value = "联系人类型")
    @ApiModelProperty("联系人类型")
    @JsonSerialize(using = ToStringSerializer.class)
    @Trans(type = TransType.DICTIONARY, key = "cop_contact_type", ref = "contactTypeName")
    private Long contactTypeId;

    /**
     * 联系人类型名称
     */
    @ExcelProperty(value = "联系人类型名称")
    @ApiModelProperty("联系人类型名称")
    private String contactTypeName;


    /**
     * 联系人类别（默认0，0——用户、1——企业）
     */
    @ExcelProperty(value = "联系人类型")
    @ApiModelProperty("联系人类别（默认0，0——用户、1——企业）")
    @JsonSerialize(using = ToStringSerializer.class)
    private Integer contactType;


    /**
     * 联系人ID（对应企业ID或用户ID）
     */
    @ExcelProperty(value = "联系人ID", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "对=应T_COP_Information主键或T_USER_User主键")
    @ApiModelProperty("联系人ID（对应企业ID或用户ID）")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long contactId;
}
