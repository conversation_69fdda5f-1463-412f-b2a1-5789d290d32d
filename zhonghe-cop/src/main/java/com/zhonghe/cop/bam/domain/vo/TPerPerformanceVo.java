package com.zhonghe.cop.bam.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zhonghe.common.annotation.ExcelDictFormat;
import com.zhonghe.common.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 绩效管理视图对象 t_per_performance
 *
 * <AUTHOR>
 * @date 2023-12-11
 */
@Data
@ApiModel("绩效管理视图对象")
@ExcelIgnoreUnannotated
public class TPerPerformanceVo {

    private static final long serialVersionUID = 1L;

    /**
     * 绩效管理ID
     */
    @ExcelProperty(value = "绩效管理ID")
    @ApiModelProperty("绩效管理ID")
    private Long perPerformanceId;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    @ApiModelProperty("备注")
    private String note;

    /**
     * 汇总月份
     */
    @ExcelProperty(value = "汇总月份")
    @ApiModelProperty("汇总月份")
    private String repDate;

    /**
     * 部门ID
     */
    @ExcelProperty(value = "部门ID")
    @ApiModelProperty("部门ID")
    private Long deptId;

    /**
     * 是否上报（默认0，0——未上报、1——已上报）
     */
    @ExcelProperty(value = "是否上报", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "默=认0，0——未上报、1——已上报")
    @ApiModelProperty("是否上报（默认0，0——未上报、1——已上报）")
    private Long isDeal;


}
