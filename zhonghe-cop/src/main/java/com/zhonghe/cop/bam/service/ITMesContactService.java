package com.zhonghe.cop.bam.service;

import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.cop.bam.domain.bo.TMesContactBo;
import com.zhonghe.cop.bam.domain.vo.TMesContactVo;

import java.util.Collection;
import java.util.List;

/**
 * 通讯录，添加用户和企业时要往这里添加数据Service接口
 *
 * <AUTHOR>
 * @date 2023-12-08
 */
public interface ITMesContactService {

    /**
     * 查询通讯录，添加用户和企业时要往这里添加数据
     */
    TMesContactVo queryById(Long mesContactId);

    /**
     * 查询通讯录，添加用户和企业时要往这里添加数据列表
     */
    TableDataInfo<TMesContactVo> queryPageList(TMesContactBo bo, PageQuery pageQuery);

    /**
     * 查询通讯录，添加用户和企业时要往这里添加数据列表
     */
    List<TMesContactVo> queryList(TMesContactBo bo);

    /**
     * 修改通讯录，添加用户和企业时要往这里添加数据
     */
    Boolean insertByBo(TMesContactBo bo);

    /**
     * 修改通讯录，添加用户和企业时要往这里添加数据
     */
    Boolean updateByBo(TMesContactBo bo);

    /**
     * 校验并批量删除通讯录，添加用户和企业时要往这里添加数据信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据通讯录类型查询通讯录
     *
     * @param contactTypeId
     * @return
     */
    List<TMesContactVo> queryListByTypeId(Long contactTypeId);

    /**
     * 根据通讯录类型分页查询通讯录
     *
     * @param contactTypeId
     * @return
     */
    TableDataInfo<TMesContactVo> queryPageListByTypeId(Long contactTypeId, String contactName, PageQuery pageQuery);
}
