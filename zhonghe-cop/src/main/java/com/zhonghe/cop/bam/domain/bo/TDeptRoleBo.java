package com.zhonghe.cop.bam.domain.bo;

import com.zhonghe.common.core.domain.BaseEntity;
import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 部门职能业务对象
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("部门职能业务对象")
public class TDeptRoleBo extends BaseEntity {

    private static final long serialVersionUID = 5785545181385160750L;
    /**
     * 主键，自增ID
     */
    @ApiModelProperty(value = "主键，自增ID", required = true)
    @NotNull(message = "主键不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 部门/小组名称
     */
    @ApiModelProperty(value = "部门/小组名称", required = true)
    @NotBlank(message = "部门/小组名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String title;

    /**
     * 部门/小组职能描述
     */
    @ApiModelProperty(value = "部门/小组职能描述", required = true)
    @NotBlank(message = "部门/小组职能描述不能为空", groups = {AddGroup.class, EditGroup.class})
    private String content;

    /**
     * 排序字段，数值越小越靠前
     */
    @ApiModelProperty(value = "排序字段，数值越小越靠前")
    private Integer sortOrder;
}
