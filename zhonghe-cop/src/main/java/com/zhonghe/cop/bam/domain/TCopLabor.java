package com.zhonghe.cop.bam.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zhonghe.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 劳务人员对象 t_cop_labor
 *
 * <AUTHOR>
 * @date 2023-12-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("cop.t_cop_labor")
public class TCopLabor extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 劳务人员ID
     */
    @TableId
    private Long copLaborId;
    /**
     * 是否删除
     */
    @TableLogic
    private String delFlag;
    /**
     * 备注
     */
    private String note;
    /**
     * 备用字段1
     */
    private String by1;
    /**
     * 备用字段2
     */
    private Long by2;
    /**
     * 企业ID（默认0，对应T_COP_Information主键）
     */
    private Long copId;
    /**
     * 劳务公司
     */
    private String labourCop;
    /**
     * 姓名
     */
    private String lName;
    /**
     * 性别
     */
    private String lSex;
    /**
     * 身份证
     */
    private String idCard;
    /**
     * 部门
     */
    private String lDept;
    /**
     * 职务
     */
    private String lJob;

}
