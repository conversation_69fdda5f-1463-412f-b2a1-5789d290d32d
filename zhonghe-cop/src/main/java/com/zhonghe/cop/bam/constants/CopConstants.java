package com.zhonghe.cop.bam.constants;

import java.util.Arrays;
import java.util.List;

/**
 * @Author: lpg
 * @Date: 2024/03/01
 * @Description:
 */
public interface CopConstants {
    /**
     * 企业性质字典
     */
    String COP_PROPERTY = "cop_property";

    /**
     * 排除的部门
     */
    List<String> EXCLUDE_DEPT = Arrays.asList("党建办", "纪检监察办", "领导小组办公室");

    /**
     * 汇总月报表短信发送（每月1号)模板
     */
    String MONTH_REPORT_TEMPLATE = "%s月份涉及问题企业%s间、风险企业%s间、危险企业%s间、高危%s间（主要涉及欠社保费、欠税、欠租金），详情请查看企业风险预警系统。";

    /**
     * 是否发送未上报部门短信发送(每月26号)模板
     */
    String UNREPORTED_DEPT_TEMPLATE = "温馨提示：请各成员部门尚未报送问题企业信息的请履行职责按时反馈报送信息。";
    /**
     * 是否发送部门上报未处理短信提醒(每月5号)模板
     */
    String UNPROCESSED_DEPT_TEMPLATE = "温馨提示：请各成员部门尚未报送处理结果信息的请履行职责按时反馈报送信息。 ";

    /**
     * 是否发送汇总月报表短信发送（每月1号)
     */
    String IS_SEND_REP_GARREP = "is_send_rep_garrep";
    /**
     * 是否发送部门上报未处理短信提醒(每月5号)
     */
    String IS_SEND_DEPT_REP_NO_HANDLE = "is_send_dept_rep_no_handle";
    /**
     * 是否发送未上报部门短信发送(每月26号)
     */
    String IS_SEND_DEPT_REP_NO_REPORT = "is_send_dept_rep_no_report";

}
