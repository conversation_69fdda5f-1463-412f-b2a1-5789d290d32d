package com.zhonghe.cop.bam.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaCheckPermission;
import com.zhonghe.common.annotation.Log;
import com.zhonghe.common.annotation.RepeatSubmit;
import com.zhonghe.common.core.controller.BaseController;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import com.zhonghe.common.enums.BusinessType;
import com.zhonghe.common.utils.BeanCopyUtils;
import com.zhonghe.cop.bam.domain.TDeptRole;
import com.zhonghe.cop.bam.domain.bo.TDeptRoleBo;
import com.zhonghe.cop.bam.domain.query.TDeptRoleQuery;
import com.zhonghe.cop.bam.domain.vo.TDeptRoleVo;
import com.zhonghe.cop.bam.service.IdeptRoleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotEmpty;
import java.util.Arrays;
import java.util.List;

/**
 * 部门职能
 *
 * <AUTHOR>
 * @date 2023-12-10
 */
@Validated
@Api(value = "部门职能", tags = {"部门职能"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/cop/deptRole")
public class DeptRoleController extends BaseController {

    private final IdeptRoleService deptRoleService;

    /**
     * 查询部门职能列表
     */
    @ApiOperation("查询部门职能列表")
    @SaCheckLogin
    @GetMapping("/list")
    public R<List<TDeptRoleVo>> list(TDeptRoleQuery query) {
        return R.ok(deptRoleService.queryList(query));
    }

    /**
     * 新增部门职能
     */
    @ApiOperation("新增部门职能(cop:deptRole:add)")
    @SaCheckPermission("cop:deptRole:add")
    @Log(title = "部门职能", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody TDeptRoleBo bo) {
        return toAjax(deptRoleService.insertByBo(bo));
    }

    /**
     * 修改部门职能
     */
    @ApiOperation("修改部门职能(cop:deptRole:edit)")
    @SaCheckPermission("cop:deptRole:edit")
    @Log(title = "部门职能", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody TDeptRoleBo bo) {
        return toAjax(deptRoleService.updateByBo(bo));
    }

    /**
     * 删除部门职能
     */
    @ApiOperation("删除部门职能(cop:deptRole:remove)")
    @SaCheckPermission("cop:deptRole:remove")
    @Log(title = "部门职能", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(
        @ApiParam("主键串")
        @NotEmpty(message = "主键不能为空")
        @PathVariable Long[] ids) {
        return toAjax(deptRoleService.deleteWithValidByIds(Arrays.asList(ids)));
    }

    /**
     * 排序
     */
    @ApiOperation("排序(cop:deptRole:edit)")
    @SaCheckPermission("cop:deptRole:edit")
    @Log(title = "部门职能", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/sort")
    public R<Void> sort(@RequestBody List<TDeptRoleBo> bos) {
        return toAjax(deptRoleService.sort(BeanCopyUtils.copyList(bos, TDeptRole.class)));
    }
}
