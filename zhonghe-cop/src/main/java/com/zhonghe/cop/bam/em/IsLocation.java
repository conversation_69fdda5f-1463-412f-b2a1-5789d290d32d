package com.zhonghe.cop.bam.em;

import lombok.Getter;

/**
 * @Author: lpg
 * @Date: 2023/12/21/10:06
 * @Description: 是否定位
 */
@Getter
public enum IsLocation {
    /**
     * 否
     */
    NO(0, "否"),
    /**
     * 是
     */
    YES(1, "是");

    private final Integer code;
    private final String name;

    IsLocation(Integer code, String name) {
        this.code = code;
        this.name = name;
    }
}
