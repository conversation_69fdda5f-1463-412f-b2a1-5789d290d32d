package com.zhonghe.cop.bam.mapper;

import com.zhonghe.common.core.mapper.BaseMapperPlus;
import com.zhonghe.cop.bam.domain.TDeptMember;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

/**
 * 部门成员数据层
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Mapper
public interface TDeptMemberMapper extends BaseMapperPlus<TDeptMemberMapper, TDeptMember, TDeptMember> {

    /**
     * 查询当前最大的排序号
     *
     * @param parentId 父级ID
     * @return 最大排序号
     */
    @Select("SELECT MAX(sort_order) FROM cop.t_dept_member WHERE parent_id = #{parentId}")
    Integer selectMaxSortOrder(Long parentId);

}
