package com.zhonghe.cop.bam.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.tree.Tree;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.common.utils.TreeBuildUtils;
import com.zhonghe.cop.bam.domain.TDicOrg;
import com.zhonghe.cop.bam.domain.bo.TDicOrgBo;
import com.zhonghe.cop.bam.domain.vo.TDicOrgVo;
import com.zhonghe.cop.bam.mapper.TDicOrgMapper;
import com.zhonghe.cop.bam.service.ITDicOrgService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * 镇街机构Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-21
 */
@RequiredArgsConstructor
@Service
public class TDicOrgServiceImpl implements ITDicOrgService {

    private final TDicOrgMapper baseMapper;

    /**
     * 查询镇街机构
     */
    @Override
    public TDicOrgVo queryById(Long orgId) {
        return baseMapper.selectVoById(orgId);
    }

    /**
     * 查询镇街机构列表
     */
    @Override
    public TableDataInfo<TDicOrgVo> queryPageList(TDicOrgBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<TDicOrg> lqw = buildQueryWrapper(bo);
        Page<TDicOrgVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询镇街机构列表
     */
    @Override
    public List<TDicOrgVo> queryList(TDicOrgBo bo) {
        LambdaQueryWrapper<TDicOrg> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<TDicOrg> buildQueryWrapper(TDicOrgBo bo) {
        LambdaQueryWrapper<TDicOrg> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getOrgId() != null, TDicOrg::getOrgId, bo.getOrgId());
        lqw.eq(StringUtils.isNotBlank(bo.getNote()), TDicOrg::getNote, bo.getNote());
        lqw.like(StringUtils.isNotBlank(bo.getOrgName()), TDicOrg::getOrgName, bo.getOrgName());
        lqw.eq(StringUtils.isNotBlank(bo.getOrgCode()), TDicOrg::getOrgCode, bo.getOrgCode());
        lqw.eq(StringUtils.isNotBlank(bo.getOrgType()), TDicOrg::getOrgType, bo.getOrgType());
        lqw.eq(bo.getParentId() != null, TDicOrg::getParentId, bo.getParentId());
        return lqw;
    }

    /**
     * 新增镇街机构
     */
    @Override
    public Boolean insertByBo(TDicOrgBo bo) {
        TDicOrg add = BeanUtil.toBean(bo, TDicOrg.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setOrgId(add.getOrgId());
        }
        return flag;
    }

    /**
     * 修改镇街机构
     */
    @Override
    public Boolean updateByBo(TDicOrgBo bo) {
        TDicOrg update = BeanUtil.toBean(bo, TDicOrg.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(TDicOrg entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public List<Tree<Long>> buildOrgTreeSelect(TDicOrgBo bo) {
        List<TDicOrgVo> tDicOrgs = baseMapper.selectVoList(buildQueryWrapper(bo));
        return TreeBuildUtils.build(tDicOrgs, (org, tree) ->
            tree.setId(org.getOrgId())
                .setParentId(org.getParentId())
                .setName(org.getOrgName()));
    }

    /**
     * 批量删除镇街机构
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
