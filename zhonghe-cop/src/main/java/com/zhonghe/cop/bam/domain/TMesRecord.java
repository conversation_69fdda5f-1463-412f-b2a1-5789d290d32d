package com.zhonghe.cop.bam.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zhonghe.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;


/**
 * 发送记录对象 t_mes_record
 *
 * <AUTHOR>
 * @date 2023-12-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("cop.t_mes_record")
public class TMesRecord extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 发送记录ID
     */
    @TableId
    private Long mesRecordId;
    /**
     * 是否删除
     */
    @TableLogic
    private String delFlag;
    /**
     * 备注
     */
    private String note;
    /**
     * 发送号码
     */
    private String phone;
    /**
     * 发送内容
     */
    private String content;
    /**
     * 发送时间
     */
    private Date sendDate;
    /**
     * 发送类别（默认0，0——手动发送、1——系统发送）
     */
    private Integer sendType;
    /**
     * 发送人ID
     */
    private Long userId;
    /**
     * 发送对象
     */
    private String sendObject;
    /**
     * 联系人类型
     */
    private Long contactTypeId;
    /**
     * 发送部门
     */
    private Long deptId;

    /**
     * 发送批次号
     */
    private String batchNo;

    /**
     * 发送状态 0-未发送 1-发送成功 2-发送失败
     */
    private Integer sendStatus;

    /**
     * 发送消息
     */
    private String sendMessage;
}
