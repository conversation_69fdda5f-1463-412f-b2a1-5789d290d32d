package com.zhonghe.cop.bam.domain.bo;

import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;


/**
 * 镇街机构业务对象 t_dic_org
 *
 * <AUTHOR>
 * @date 2023-12-21
 */

@Data
@ApiModel("镇街机构业务对象")
public class TDicOrgBo implements Serializable {

    private static final long serialVersionUID = 5010044615657394376L;
    /**
     * 组织ID
     */
    @ApiModelProperty(value = "组织ID", required = true)
    @NotNull(message = "组织ID不能为空", groups = {EditGroup.class})
    private Long orgId;

    /**
     * 说明
     */
    @ApiModelProperty(value = "说明", required = true)
    @NotBlank(message = "说明不能为空", groups = {AddGroup.class, EditGroup.class})
    private String note;

    /**
     * 机构名称
     */
    @ApiModelProperty(value = "机构名称", required = true)
    @NotBlank(message = "机构名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String orgName;

    /**
     * 机构代码
     */
    @ApiModelProperty(value = "机构代码", required = true)
    @NotBlank(message = "机构代码不能为空", groups = {AddGroup.class, EditGroup.class})
    private String orgCode;

    /**
     * 机构类型
     */
    @ApiModelProperty(value = "机构类型", required = true)
    @NotBlank(message = "机构类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private String orgType;

    /**
     * 父组织ID
     */
    @ApiModelProperty(value = "父组织ID", required = true)
    @NotNull(message = "父组织ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long parentId;


}
