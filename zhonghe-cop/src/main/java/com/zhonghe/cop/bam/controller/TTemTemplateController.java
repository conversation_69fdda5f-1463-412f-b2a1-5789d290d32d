package com.zhonghe.cop.bam.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaCheckPermission;
import com.zhonghe.common.annotation.Log;
import com.zhonghe.common.annotation.RepeatSubmit;
import com.zhonghe.common.core.controller.BaseController;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import com.zhonghe.common.enums.BusinessType;
import com.zhonghe.common.utils.poi.ExcelUtil;
import com.zhonghe.cop.bam.domain.bo.TTemTemplateBo;
import com.zhonghe.cop.bam.domain.vo.TTemTemplateVo;
import com.zhonghe.cop.bam.service.ITTemTemplateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 语句模板
 *
 * <AUTHOR>
 * @date 2023-12-10
 */
@Validated
@Api(value = "模板控制器", tags = {"模板管理"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/cop/temTemplate")
public class TTemTemplateController extends BaseController {

    private final ITTemTemplateService iTTemTemplateService;

    /**
     * 查询模板列表
     */
    @ApiOperation("查询模板列表(cop:temTemplate:list)")
    @SaCheckPermission("cop:temTemplate:list")
    @GetMapping("/list")
    public TableDataInfo<TTemTemplateVo> list(TTemTemplateBo bo, PageQuery pageQuery) {
        return iTTemTemplateService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出模板列表
     */
    @ApiOperation("导出模板列表(cop:temTemplate:export)")
    @SaCheckPermission("cop:temTemplate:export")
    @Log(title = "模板", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(TTemTemplateBo bo, HttpServletResponse response) {
        List<TTemTemplateVo> list = iTTemTemplateService.queryList(bo);
        ExcelUtil.exportExcel(list, "模板", TTemTemplateVo.class, response);
    }

    /**
     * 获取模板详细信息
     */
    @ApiOperation("获取模板详细信息(cop:temTemplate:query)")
    @SaCheckPermission("cop:temTemplate:query")
    @GetMapping("/{templateId}")
    public R<TTemTemplateVo> getInfo(@ApiParam("主键")
                                     @NotNull(message = "主键不能为空")
                                     @PathVariable("templateId") Long templateId) {
        return R.ok(iTTemTemplateService.queryById(templateId));
    }

    /**
     * 新增模板
     */
    @ApiOperation("新增模板(cop:temTemplate:add)")
    @SaCheckPermission("cop:temTemplate:add")
    @Log(title = "模板", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody TTemTemplateBo bo) {
        return toAjax(iTTemTemplateService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改模板
     */
    @ApiOperation("修改模板(cop:temTemplate:edit)")
    @SaCheckPermission("cop:temTemplate:edit")
    @Log(title = "模板", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody TTemTemplateBo bo) {
        return toAjax(iTTemTemplateService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除模板
     */
    @ApiOperation("删除模板(cop:temTemplate:remove)")
    @SaCheckPermission("cop:temTemplate:remove")
    @Log(title = "模板", businessType = BusinessType.DELETE)
    @DeleteMapping("/{templateId}")
    public R<Void> remove(@ApiParam("主键串")
                          @NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] templateId) {
        return toAjax(iTTemTemplateService.deleteWithValidByIds(Arrays.asList(templateId), true) ? 1 : 0);
    }

    /**
     * 根据类型获取模板
     */
    @ApiOperation("根据类型获取模板(cop:temTemplate:query)")
    @SaCheckLogin
    @GetMapping("/type/{templateTypeId}")
    public R<List<TTemTemplateVo>> queryByType(@ApiParam("类型ID")
                                               @NotNull(message = "类型ID不能为空")
                                               @PathVariable("templateTypeId") Long templateTypeId) {
        return R.ok(iTTemTemplateService.queryByTypeId(templateTypeId));
    }
}
