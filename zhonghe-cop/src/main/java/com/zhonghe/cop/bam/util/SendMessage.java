package com.zhonghe.cop.bam.util;

import cn.hutool.core.date.DateUtil;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;

/**
 * @Author: srh
 * @Date: 2024/5/8
 * @Description: 短信发送工具
 */
public class SendMessage {

    private final static String requestUrl = "http://bgjkdx.dg.cn/servlet/SpInterface";
    private final static String SP_ID = "508906";
    private final static String PASSWORD = "BGjk508_0932";


    /**
     * 发送短信
     *
     * @param mobile     手机号
     * @param smsContent 短信内容
     * @return
     */
    public static String sendSms(String mobile, String smsContent) throws Exception {
        int random = (int) (Math.random() * 10000) + 1;
        String generateId = String.valueOf(random);
        String format = DateUtil.format(DateUtil.date(), "yyyyMMddHHmmss");
        System.out.println(format);
        String SMS_ID = SP_ID + format + generateId;
        //短信内容
//        String smsContent = "你好啊！今天是个好日子！";


        String requestData = "<?xml version= \"1.0\" encoding=\"GBK\" ?>" +
            "<REQUEST>" +
            "<TRANS_TYPE>SMS_DOWN_REQUEST</TRANS_TYPE>" +
            "<SP_ID>" + SP_ID + "</SP_ID>" + // 应用账号标识"
            "<PASSWORD>" + PASSWORD + "</PASSWORD>" + // 应用账号密码
            "<SEQ_NUM>" + SMS_ID.substring(0, 17) + "</SEQ_NUM>" + // 发送序列号
            "<SMS_ID></SMS_ID>" +// 短信的唯一标识
            "<MOBILE>" + mobile + "</MOBILE>" + // 短信接收号码，一次只能一个
            "<CONTENT><![CDATA[" + smsContent + "]]></CONTENT>" +// 短信内容
            "<DATETIME>" + DateUtil.format(DateUtil.date(), "yyyy-MM-dd HH:mm:ss") + "</DATETIME>" +// 时间格式:yyyy-MM-dd hh:mm:ss
            "<PRIORITY>1</PRIORITY>" + // 短信优先级
            "<EXTEND_CODE></EXTEND_CODE>" + // 短信子端口,定义行用于回复短信对应业端口后面端口，
            "</REQUEST>";

        String respData = "";

        HttpURLConnection httprequestUrlconnection = null;
        //try {
        boolean sendOk = false;
        int resendCount = 0;
        int result = 0;
        URL url = null;

        while (!sendOk && resendCount < 3) {
            url = new URL(requestUrl);
            httprequestUrlconnection = (HttpURLConnection) url.openConnection();
            httprequestUrlconnection.setDoOutput(true);
            httprequestUrlconnection.setRequestMethod("POST");
            httprequestUrlconnection.setRequestProperty("Content-Type", "application/xml");
            httprequestUrlconnection.getOutputStream().write(new String(requestData).getBytes("GBK"));
            httprequestUrlconnection.getOutputStream().flush();
            httprequestUrlconnection.getOutputStream().close();
            result = httprequestUrlconnection.getResponseCode();
            if (result == 200) {
                StringBuffer respStr = new StringBuffer("");
                InputStream is = httprequestUrlconnection.getInputStream();
                BufferedReader reader = new BufferedReader(new InputStreamReader(is));
                String currentLine;
                while ((currentLine = reader.readLine()) != null) {
                    if (currentLine.length() > 0) {
                        respStr.append(currentLine.trim());
                    }

                }
                sendOk = true;

                respData = respStr.toString();

                // 打印请求方法和 URL
                System.out.println(httprequestUrlconnection.getRequestMethod() + " " + url);
                // 打印请求头
                System.out.println("Content-Type: " + httprequestUrlconnection.getRequestProperty("Content-Type"));
                byte[] requestBytes = requestData.getBytes("GBK");
                // 打印请求体
                System.out.println("Request Body: " + new String(requestBytes, "GBK"));
                // 打印响应
                System.out.println("Response Code: " + result);
                System.out.println("Response Data: " + respData);
            } else {
                System.out.println("远程服务器连接失败,错误代码: " + result);
                resendCount++;
            }
        }
        //} catch (Exception e) {
        //    e.printStackTrace();
        //    System.out.println("向远程服务器提交请求数据失败: " + e.getMessage());
        //} finally {
        if (httprequestUrlconnection != null) {

            httprequestUrlconnection.disconnect();
        }
        //}

        return respData;
    }


}
