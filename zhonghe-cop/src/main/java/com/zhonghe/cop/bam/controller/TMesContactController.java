package com.zhonghe.cop.bam.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.zhonghe.common.annotation.Log;
import com.zhonghe.common.annotation.RepeatSubmit;
import com.zhonghe.common.core.controller.BaseController;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import com.zhonghe.common.enums.BusinessType;
import com.zhonghe.common.utils.poi.ExcelUtil;
import com.zhonghe.cop.bam.domain.bo.TMesContactBo;
import com.zhonghe.cop.bam.domain.vo.TMesContactVo;
import com.zhonghe.cop.bam.service.ITMesContactService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 通讯录
 *
 * <AUTHOR>
 * @date 2023-12-08
 */
@Validated
@Api(value = "通讯录控制器", tags = {"通讯录管理"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/cop/mesContact")
public class TMesContactController extends BaseController {

    private final ITMesContactService iTMesContactService;

    /**
     * 查询通讯录列表
     */
    @ApiOperation("查询通讯录列表(cop:mesContact:list)")
    @SaCheckPermission("cop:mesContact:list")
    @GetMapping("/list")
    public TableDataInfo<TMesContactVo> list(TMesContactBo bo, PageQuery pageQuery) {
        return iTMesContactService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出通讯录列表
     */
    @ApiOperation("导出通讯录列表(cop:mesContact:export)")
    @SaCheckPermission("cop:mesContact:export")
    @Log(title = "通讯录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(TMesContactBo bo, HttpServletResponse response) {
        List<TMesContactVo> list = iTMesContactService.queryList(bo);
        ExcelUtil.exportExcel(list, "通讯录", TMesContactVo.class, response);
    }

    /**
     * 获取通讯录
     */
    @ApiOperation("获取通讯录详细信息(cop:mesContact:query)")
    @SaCheckPermission("cop:mesContact:query")
    @GetMapping("/{mesContactIds}")
    public R<TMesContactVo> getInfo(@ApiParam("主键")
                                    @NotNull(message = "主键不能为空")
                                    @PathVariable("mesContactIds") Long mesContactIds) {
        return R.ok(iTMesContactService.queryById(mesContactIds));
    }

    /**
     * 新增通讯录
     */
    @ApiOperation("新增通讯录(cop:mesContact:add)")
    @SaCheckPermission("cop:mesContact:add")
    @Log(title = "通讯录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody TMesContactBo bo) {
        return toAjax(iTMesContactService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改通讯录
     */
    @ApiOperation("修改通讯录(cop:mesContact:edit)")
    @SaCheckPermission("cop:mesContact:edit")
    @Log(title = "通讯录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody TMesContactBo bo) {
        return toAjax(iTMesContactService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除通讯录
     */
    @ApiOperation("删除通讯录(cop:mesContact:remove)")
    @SaCheckPermission("cop:mesContact:remove")
    @Log(title = "通讯录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{mesContactIds}")
    public R<Void> remove(@ApiParam("主键串")
                          @NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] mesContactIds) {
        return toAjax(iTMesContactService.deleteWithValidByIds(Arrays.asList(mesContactIds), true) ? 1 : 0);
    }

    /**
     * 根据联系人类型查询通讯录列表
     */
    @ApiOperation("根据联系人类型查询通讯录列表(cop:mesContact:query)")
    @SaCheckPermission("cop:mesContact:query")
    @GetMapping("/type/{contactTypeId}")
    public R<TableDataInfo<TMesContactVo>> queryListByType(@ApiParam("联系人类型")
                                                           @NotNull(message = "联系人类型不能为空")
                                                           @PathVariable("contactTypeId") Long contactTypeId,
                                                           @ApiParam("联系人名称")
                                                           String contactName,
                                                           PageQuery pageQuery) {
        return R.ok(iTMesContactService.queryPageListByTypeId(contactTypeId, contactName, pageQuery));
    }
}
