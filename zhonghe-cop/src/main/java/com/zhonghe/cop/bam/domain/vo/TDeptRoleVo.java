package com.zhonghe.cop.bam.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 部门职能视图对象
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Data
@ApiModel("部门职能视图对象")
@ExcelIgnoreUnannotated
public class TDeptRoleVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键，自增ID
     */
    @ExcelProperty(value = "主键")
    @ApiModelProperty("主键，自增ID")
    private Long id;

    /**
     * 部门/小组名称
     */
    @ExcelProperty(value = "部门/小组名称")
    @ApiModelProperty("部门/小组名称")
    private String title;

    /**
     * 部门/小组职能描述
     */
    @ExcelProperty(value = "部门/小组职能描述")
    @ApiModelProperty("部门/小组职能描述")
    private String content;

    /**
     * 排序字段，数值越小越靠前
     */
    @ExcelProperty(value = "排序字段")
    @ApiModelProperty("排序字段，数值越小越靠前")
    private Integer sortOrder;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间")
    @ApiModelProperty("更新时间")
    private Date updateTime;

}
