package com.zhonghe.cop.bam.service;

import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.cop.bam.domain.bo.AddCopContactBo;
import com.zhonghe.cop.bam.domain.bo.CancelCopBo;
import com.zhonghe.cop.bam.domain.bo.TCopInformationBo;
import com.zhonghe.cop.bam.domain.excel.AddTCopInformationExcel;
import com.zhonghe.cop.bam.domain.excel.CancelTCopInformationExcel;
import com.zhonghe.cop.bam.domain.excel.EditTCopInformationExcel;
import com.zhonghe.cop.bam.domain.query.TCopInformationQuery;
import com.zhonghe.cop.bam.domain.vo.CopGeomVo;
import com.zhonghe.cop.bam.domain.vo.TCopInformationVo;
import com.zhonghe.cop.bam.domain.vo.TMesContactVo;
import org.springframework.web.multipart.MultipartFile;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 企业信息Service接口
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
public interface TICopInformationService {

    /**
     * 查询企业信息
     */
    TCopInformationVo queryById(Long copId);

    /**
     * 查询企业信息列表
     */
    TableDataInfo<TCopInformationVo> queryPageList(TCopInformationQuery bo, PageQuery pageQuery);

    /**
     * 查询企业信息列表
     */
    List<TCopInformationVo> queryList(TCopInformationQuery bo);

    /**
     * 修改企业信息
     */
    Boolean insertByBo(TCopInformationBo bo);

    /**
     * 修改企业信息
     */
    Boolean updateByBo(TCopInformationBo bo);

    /**
     * 校验并批量删除企业信息信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 查询企业信息列表
     *
     * @return
     */
    List<TMesContactVo> contactList(Long copId);

    /**
     * 添加企业联系人
     *
     * @param bo
     * @return
     */
    Boolean insertContact(AddCopContactBo bo);

    /**
     * 删除企业联系人
     *
     * @param bo
     * @return
     */
    Boolean updateConract(AddCopContactBo bo);

    /**
     * 删除企业联系人
     *
     * @param list
     * @param b
     * @return
     */
    Boolean deleteContact(List<Long> list, boolean b);

    /**
     * 查询注销企业信息列表
     */
    TableDataInfo<TCopInformationVo> queryCancelList(TCopInformationQuery bo, PageQuery pageQuery);

    /**
     * 注销企业
     *
     * @param bo
     * @return
     */
    Boolean cancel(CancelCopBo bo);

    /**
     * 恢复企业
     *
     * @param copIds
     * @return
     */
    Boolean recovery(List<Long> copIds);

    /**
     * 更新大屏企业图层地理位置数据
     *
     * @param copId
     */
    void updateBigMapCopGeom(Long copId);

    /**
     * 根据企业名称查询企业信息
     *
     * @param copName
     * @return
     */
    Map<String, Object> getAddressInfo(String copName);

    /**
     * excel新增企业备案
     *
     * @param bo
     * @return
     */
    Boolean excelAddData(List<AddTCopInformationExcel> bo);

    /**
     * excel修改企业备案
     *
     * @param bo
     * @return
     */
    Boolean excelEditData(List<EditTCopInformationExcel> bo);

    /**
     * excel注销企业备案
     *
     * @param bo
     * @return
     */
    Boolean excelCancelData(List<CancelTCopInformationExcel> bo);

    /**
     * 在库企业查询
     *
     * @param copName
     * @return
     */
    Boolean queryCopName(String copName);

    /**
     * 查询附近企业
     *
     * @param lon
     * @param lat
     * @return
     */
    List<CopGeomVo> getNearby(Double lon, Double lat);

    /**
     * 市场主体企业导入
     *
     * @param file
     * @param type
     */
    void importSCZTCop(MultipartFile file, String type);

    /**
     * 异步执行Excel编辑数据
     *
     * @param editTCopInformationExcels 编辑数据列表
     * @return CompletableFuture<Boolean>
     */
    CompletableFuture<Boolean> excelEditDataAsync(List<EditTCopInformationExcel> editTCopInformationExcels);

    /**
     * 异步执行Excel新增数据
     *
     * @param addTCopInformationExcels 新增数据列表
     * @return CompletableFuture<Boolean>
     */
    CompletableFuture<Boolean> excelAddDataAsync(List<AddTCopInformationExcel> addTCopInformationExcels);

    /**
     * 异步执行市场主体企业导入
     *
     * @param file 导入文件
     * @param type 导入类型
     * @return CompletableFuture<Void>
     */
    CompletableFuture<Void> importSCZTCopAsync(MultipartFile file, String type);
}
