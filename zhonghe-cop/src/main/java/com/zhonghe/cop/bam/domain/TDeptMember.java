package com.zhonghe.cop.bam.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;

/**
 * 领导小组成员表
 *
 * @TableName t_dept_member
 */
@Data
@TableName("cop.t_dept_member")
public class TDeptMember implements Serializable {

    private static final long serialVersionUID = -4908653912031961323L;
    /**
     * 主键，自增ID
     */
    @NotNull(message = "[主键，自增ID]不能为空")
    @ApiModelProperty("主键，自增ID")
    private Long id;
    /**
     * 父节点ID
     */
    @ApiModelProperty("父节点ID")
    private Long parentId;
    /**
     * 小组成员名称，可为人名或单位名
     */
    @ApiModelProperty("小组成员名称")
    private String name;
    /**
     * 排序字段
     */
    @ApiModelProperty("排序字段")
    private Integer sortOrder;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 创建人用户名或ID
     */
    private String createBy;
    /**
     * 更新人用户名或ID
     */
    private String updateBy;

}
