package com.zhonghe.cop.bam.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.zhonghe.common.annotation.ExcelDictFormat;
import com.zhonghe.common.convert.ExcelDictConvert;
import com.zhonghe.cop.bam.constants.CopConstants;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;


/**
 * 企业信息视图对象 t_cop_information
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
@Data
@ApiModel("企业信息视图对象")
@ExcelIgnoreUnannotated
public class TCopInformationVo implements TransPojo, Serializable {

    private static final long serialVersionUID = 1L;

    private Map<String, Object> transMap = new HashMap<>();

    /**
     * 企业ID
     */
    @ApiModelProperty("企业ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long copId;


    /**
     * 企业名称
     */
    @ExcelProperty(value = "企业名称")
    @ApiModelProperty("企业名称")
    private String copName;

    /**
     * 企业编号
     */
    @ApiModelProperty("企业编号")
    private String copCode;

    /**
     * 企业法人
     */
    @ExcelProperty(value = "企业法人")
    @ApiModelProperty("企业法人")
    private String copOwner;

    /**
     * 企业性质
     */
    @ExcelProperty(value = "企业性质")
    @ApiModelProperty("企业性质")
    private String copProperty;

    /**
     * 企业负责人
     */
    @ExcelProperty(value = "企业负责人")
    @ApiModelProperty("企业负责人")
    private String copIncharge;

    /*
     * 企业所属村社区
     */
    @ExcelProperty(value = "企业所属村社区")
    @ApiModelProperty("企业所属村社区")
    private String orgName;

    /**
     * 组织机构代码证
     */
    @ExcelProperty(value = "组织机构代码证")
    @ApiModelProperty("组织机构代码证")
    private String corporateCode;

    /**
     * 营业执照注册号
     */
    @ExcelProperty(value = "营业执照注册号")
    @ApiModelProperty("营业执照注册号")
    private String businessLicence;

    /**
     * 税务登记证编号
     */
    @ExcelProperty(value = "税务登记证编号")
    @ApiModelProperty("税务登记证编号")
    private String taxRegistration;

    /**
     * 企业人员规模
     */
    @ExcelProperty(value = "企业人员规模")
    @ApiModelProperty("企业人员规模")
    private Integer copPopulation;

    /**
     * 企业地址
     */
    @ExcelProperty(value = "企业地址")
    @ApiModelProperty("企业地址")
    private String copAddress;

    /**
     * 企业电话
     */
    @ExcelProperty(value = "企业电话")
    @ApiModelProperty("企业电话")
    private String copPhone;

    /**
     * 传真号码
     */
    @ExcelProperty(value = "传真号码")
    @ApiModelProperty("传真号码")
    private String copFax;

    /**
     * 办公场地属性（默认0，0——租用、1——自建、2——转租）
     */
    @ExcelProperty(value = "办公场地属性", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "默=认0，0——租用、1——自建、2——转租")
    @ApiModelProperty("办公场地属性（默认0，0——租用、1——自建、2——转租）")
    @JsonSerialize(using = ToStringSerializer.class)
    private Integer officeProperty;

    /**
     * 厂房业主
     */
    @ExcelProperty(value = "厂房业主")
    @ApiModelProperty("厂房业主")
    private String officeOwner;

    /**
     * 厂房业主电话
     */
    @ExcelProperty(value = "厂房业主电话")
    @ApiModelProperty("厂房业主电话")
    private String officeOwnerPhone;

    /**
     * 经营范围
     */
    @ExcelProperty(value = "经营范围")
    @ApiModelProperty("经营范围")
    private String scopeOfBusiness;

    /**
     * 企业性质ID
     */
    @ApiModelProperty("企业性质ID")
    @Trans(type = TransType.DICTIONARY, target = TCopInformationVo.class, key = CopConstants.COP_PROPERTY, ref = "copProperty")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long propertyId;


    /**
     * 机构ID
     */
    @ApiModelProperty("机构ID")
    private Long orgId;

    /**
     * 经度
     */
    @ApiModelProperty("经度")
    private String lon;

    /**
     * 纬度
     */
    @ApiModelProperty("纬度")
    private String lat;

    /**
     * 是否注销（默认0，0——未注销、1——已注销）
     */
    @ExcelDictFormat(readConverterExp = "默认0，0——未注销、1——已注销")
    @ApiModelProperty("是否注销（默认0，0——未注销、1——已注销）")
    private Integer isCancel;


    /**
     * 企业联系人
     */
    @ApiModelProperty("企业联系人")
    private String copContacts;

    /**
     * 厂房租赁期限
     */
    @ApiModelProperty("厂房租赁期限")
    private String leaseTerm;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String note;


    /**
     * 是否定位 （默认0，0——未定位、1——已定位）
     */
    @ApiModelProperty("是否定位 默认0，0——未定位、1——已定位）")
    private Integer isLocation;


    /**
     * 是否本镇企业
     */
    @ApiModelProperty(value = "是否本镇企业(0:否;1:是)")
    private Integer isLocal;

}
