package com.zhonghe.cop.bam.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 镇街机构视图对象 t_dic_org
 *
 * <AUTHOR>
 * @date 2023-12-21
 */
@Data
@ApiModel("镇街机构视图对象")
@ExcelIgnoreUnannotated
public class TDicOrgVo {

    private static final long serialVersionUID = 1L;

    /**
     * 组织ID
     */
    @ExcelProperty(value = "组织ID")
    @ApiModelProperty("组织ID")
    private Long orgId;

    /**
     * 说明
     */
    @ExcelProperty(value = "说明")
    @ApiModelProperty("说明")
    private String note;

    /**
     * 机构名称
     */
    @ExcelProperty(value = "机构名称")
    @ApiModelProperty("机构名称")
    private String orgName;

    /**
     * 机构代码
     */
    @ExcelProperty(value = "机构代码")
    @ApiModelProperty("机构代码")
    private String orgCode;

    /**
     * 机构类型
     */
    @ExcelProperty(value = "机构类型")
    @ApiModelProperty("机构类型")
    private String orgType;

    /**
     * 父组织ID
     */
    @ExcelProperty(value = "父组织ID")
    @ApiModelProperty("父组织ID")
    private Long parentId;


}
