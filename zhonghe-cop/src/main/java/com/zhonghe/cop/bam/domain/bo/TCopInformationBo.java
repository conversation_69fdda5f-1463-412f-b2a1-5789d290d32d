package com.zhonghe.cop.bam.domain.bo;

import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.Digits;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;


/**
 * 企业信息业务对象 t_cop_information
 *
 * <AUTHOR>
 * @date 2023-12-07
 */

@Data
@ApiModel("企业信息业务对象")
public class TCopInformationBo implements Serializable {

    private static final long serialVersionUID = -7902448243730916779L;
    /**
     * 企业ID
     */
    @ApiModelProperty(value = "企业ID", required = true)
    @NotNull(message = "企业ID不能为空", groups = {EditGroup.class})
    private Long copId;

    /**
     * 企业名称
     */
    @ApiModelProperty(value = "企业名称", required = true)
    @NotBlank(message = "企业名称不能为空", groups = {AddGroup.class, EditGroup.class})
    @Length(max = 200, message = "企业名称长度不能超过200个字符", groups = {AddGroup.class, EditGroup.class})
    private String copName;

    /**
     * 企业编号
     */
    @ApiModelProperty(value = "企业编号", required = true)
    @Length(max = 50, message = "企业编号长度不能超过50个字符", groups = {AddGroup.class, EditGroup.class})
    private String copCode;

    /**
     * 企业法人
     */
    @ApiModelProperty(value = "企业法人", required = true)
    @NotBlank(message = "企业法人不能为空", groups = {AddGroup.class, EditGroup.class})
    @Length(max = 50, message = "企业法人长度不能超过50个字符", groups = {AddGroup.class, EditGroup.class})
    private String copOwner;

    /**
     * 企业负责人
     */
    @ApiModelProperty(value = "企业负责人", required = true)
    @Length(max = 50, message = "企业负责人长度不能超过50个字符", groups = {AddGroup.class, EditGroup.class})
    private String copIncharge;

    /**
     * 组织机构代码证
     */
    @ApiModelProperty(value = "组织机构代码证", required = true)
    @Length(max = 50, message = "组织机构代码证长度不能超过50个字符", groups = {AddGroup.class, EditGroup.class})
    private String corporateCode;

    /**
     * 营业执照注册号
     */
    @ApiModelProperty(value = "营业执照注册号", required = true)
    @Length(max = 50, message = "营业执照注册号长度不能超过50个字符", groups = {AddGroup.class, EditGroup.class})
    private String businessLicence;

    /**
     * 税务登记证编号
     */
    @ApiModelProperty(value = "税务登记证编号", required = true)
    @Length(max = 50, message = "税务登记证编号长度不能超过50个字符", groups = {AddGroup.class, EditGroup.class})
    private String taxRegistration;

    /**
     * 企业人员规模
     */
    @ApiModelProperty(value = "企业人员规模")
    @Digits(integer = 10, fraction = 0, message = "企业人员规模必须为整数", groups = {AddGroup.class, EditGroup.class})
    private Integer copPopulation;

    /**
     * 企业地址
     */
    @ApiModelProperty(value = "企业地址", required = true)
    @Length(max = 200, message = "企业地址长度不能超过200个字符", groups = {AddGroup.class, EditGroup.class})
    private String copAddress;

    /**
     * 企业电话
     */
    @ApiModelProperty(value = "企业电话", required = true)
    @NotBlank(message = "企业电话不能为空", groups = {AddGroup.class, EditGroup.class})
    @Length(max = 20, message = "企业电话长度不能超过20个字符", groups = {AddGroup.class, EditGroup.class})
    private String copPhone;

    /**
     * 企业传真
     */
    @ApiModelProperty(value = "企业传真", required = true)
    @Length(max = 20, message = "企业传真长度不能超过20个字符", groups = {AddGroup.class, EditGroup.class})
    private String copFax;

    /**
     * 办公场地属性（默认0，0——租用、1——自建、2——转租）
     */
    @ApiModelProperty(value = "办公场地属性（默认0，0——租用、1——自建、2——转租）", required = true)
    private Integer officeProperty;

    /**
     * 厂房业主
     */
    @ApiModelProperty(value = "厂房业主", required = true)
    @Length(max = 20, message = "厂房业主长度不能超过20个字符", groups = {AddGroup.class, EditGroup.class})
    private String officeOwner;

    /**
     * 厂房业主电话
     */
    @ApiModelProperty(value = "厂房业主电话", required = true)
    @Length(max = 20, message = "厂房业主电话长度不能超过20个字符", groups = {AddGroup.class, EditGroup.class})
    private String officeOwnerPhone;

    /**
     * 企业性质ID
     */
    @ApiModelProperty(value = "企业性质ID", required = true)
    @NotNull(message = "企业性质ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long propertyId;

    /**
     * 机构ID
     */
    @ApiModelProperty(value = "机构ID")
    private Long orgId;

    /**
     * 经度
     */
    @ApiModelProperty(value = "经度")
    private String lon;

    /**
     * 纬度
     */
    @ApiModelProperty(value = "纬度")
    private String lat;

    /**
     * 经营范围
     */
    @ApiModelProperty(value = "经营范围", required = true)
    private String scopeOfBusiness;

    /**
     * 企业联系人
     */
    @ApiModelProperty(value = "企业联系人", required = true)
    private String copContacts;

    /**
     * 厂房租赁期限
     */
    @ApiModelProperty(value = "厂房租赁期限", required = true)
    private String leaseTerm;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Length(max = 2000, message = "备注长度不能超过2000个字符", groups = {AddGroup.class, EditGroup.class})
    private String note;

    /**
     * 是否本镇企业
     */
    @ApiModelProperty(value = "是否本镇企业(0:否;1:是)")
    private Integer isLocal;


}
