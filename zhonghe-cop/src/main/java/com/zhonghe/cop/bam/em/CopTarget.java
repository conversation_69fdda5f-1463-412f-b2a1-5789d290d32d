package com.zhonghe.cop.bam.em;

import com.zhonghe.cop.rep.domain.TRepGarrep;
import lombok.Getter;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: lpg
 * @Date: 2025/07/03
 * @Description:
 */
@Getter
public enum CopTarget {

    PROPAY_MONTH("propaymonth", "拖欠工资月数${value}个月，"),
    PROPAY_POPULATION("propaypopulation", "拖欠工资涉及人数${value}人，"),
    PROPAY_MONEY("propaymoney", "拖欠工资金额${value}元；"),

    PROTAX_MONTH("protaxmonth", "拖欠税款月数${value}个月，"),
    PROTAX_MONEY("protaxmoney", "拖欠税款金额${value}元；"),

    PROINSURE_MONTH("proinsuremonth", "拖欠社保月数${value}个月，"),
    PROINSURE_MONEY("proinsuremoney", "拖欠社保金额${value}元；"),

    PROWATER_MONTH("prowatermonth", "拖欠水费月数${value}个月，"),
    PROWATER_MONEY("prowatermoney", "拖欠水费金额${value}元；"),

    PROENERGY_MONTH("proenergymonth", "拖欠电费月数${value}个月，"),
    PROENERGY_MONEY("proenergymoney", "拖欠电费金额${value}元；"),

    PRORENT_MONTH("prorentmonth", "拖欠租金月数${value}个月，"),
    PRORENT_MONEY("prorentmoney", "拖欠租金金额${value}元；"),
    OTHER("other", "其他：${value}；");

    private final String fieldName;
    private final String template;

    CopTarget(String fieldName, String template) {
        this.fieldName = fieldName;
        this.template = template;
    }

    /**
     * 根据字段名查找对应的枚举
     *
     * @param fieldName 字段名
     * @return 对应的枚举，如果没找到返回null
     */
    public static CopTarget getByFieldName(String fieldName) {
        for (CopTarget target : values()) {
            if (target.fieldName.equals(fieldName)) {
                return target;
            }
        }
        return null;
    }

    /**
     * 通过反射获取对象字段值并格式化输出
     *
     * @param obj 目标对象
     * @return 格式化后的字符串列表
     */
    public static List<String> formatFromObject(Object obj) {
        List<String> result = new ArrayList<>();

        if (obj == null) {
            return result;
        }

        try {
            Field[] fields = obj.getClass().getDeclaredFields();

            for (Field field : fields) {
                field.setAccessible(true);
                String fieldName = field.getName();

                CopTarget target = getByFieldName(fieldName);
                if (target != null) {
                    Object value = field.get(obj);

                    // 检查值是否有效（不为null且不为0）
                    if (isValidValue(value)) {
                        result.add(target.format(value));
                    }
                }
            }
        } catch (IllegalAccessException e) {
            // 处理反射异常
            e.printStackTrace();
        }

        return result;
    }

    /**
     * 判断值是否有效
     *
     * @param value 值
     * @return 是否有效
     */
    private static boolean isValidValue(Object value) {
        if (value == null) {
            return false;
        }

        if (value instanceof BigDecimal) {
            BigDecimal decimal = (BigDecimal) value;
            return decimal.compareTo(BigDecimal.ZERO) > 0;
        }

        if (value instanceof Number) {
            return ((Number) value).doubleValue() > 0;
        }

        return true;
    }

    public static void main(String[] args) {
        TRepGarrep garrep = new TRepGarrep();
        garrep.setPropaymonth(new BigDecimal("1.00"));
        garrep.setPropaypopulation(new BigDecimal("1.00"));
        garrep.setPropaymoney(new BigDecimal("1.00"));

        System.out.println(formatFromObject(garrep));

    }

    public String getFieldName() {
        return fieldName;
    }

    public String getTemplate() {
        return template;
    }

    /**
     * 格式化消息
     *
     * @param value 值
     * @return 格式化后的字符串
     */
    public String format(Object value) {
        if (value instanceof BigDecimal) {
            BigDecimal decimal = (BigDecimal) value;
            value = decimal.stripTrailingZeros().toPlainString();
        }
        return template.replace("${value}", String.valueOf(value));
    }
}
