package com.zhonghe.cop.bam.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zhonghe.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;


/**
 * 企业注销记录对象 t_cop_cancel_record
 *
 * <AUTHOR>
 * @date 2023-12-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("cop.t_cop_cancel_record")
public class TCopCancelRecord extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 企业注销记录ID
     */
    @TableId
    private Long copCancelRecordId;
    /**
     * 是否删除
     */
    @TableLogic
    private String delFlag;
    /**
     * 说明
     */
    private String note;
    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 注销企业ID
     */
    private Long copId;
    /**
     * 取消日期
     */
    private Date cancelDate;
    /**
     * 注销原因
     */
    private String cancelCause;

}
