package com.zhonghe.cop.bam.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.List;


/**
 * 用户菜单保存列 menu_page
 *
 * <AUTHOR>
 * @date 2023-11-06
 */
@Data
@TableName("cop.menu_page")
public class MenuPage {

    private static final long serialVersionUID = 1L;

    /**
     * 唯一标识
     */
    @TableId(value = "menu_page_id")
    private Long menuPageId;
    /**
     * 用户id
     */
    private Long menuPageUserId;
    /**
     * 菜单名称标识
     */
    private String menuPageName;
    /**
     * 存储列
     */
    private String menuPageColumn;
    /**
     * 存储列
     */
    @TableField(exist = false)
    private List<String> pageColumns;

}
