package com.zhonghe.cop.bam.service;

import cn.hutool.core.lang.tree.Tree;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.cop.bam.domain.bo.TDicOrgBo;
import com.zhonghe.cop.bam.domain.vo.TDicOrgVo;

import java.util.Collection;
import java.util.List;

/**
 * 镇街机构Service接口
 *
 * <AUTHOR>
 * @date 2023-12-21
 */
public interface ITDicOrgService {

    /**
     * 查询镇街机构
     */
    TDicOrgVo queryById(Long orgId);

    /**
     * 查询镇街机构列表
     */
    TableDataInfo<TDicOrgVo> queryPageList(TDicOrgBo bo, PageQuery pageQuery);

    /**
     * 查询镇街机构列表
     */
    List<TDicOrgVo> queryList(TDicOrgBo bo);

    /**
     * 修改镇街机构
     */
    Boolean insertByBo(TDicOrgBo bo);

    /**
     * 修改镇街机构
     */
    Boolean updateByBo(TDicOrgBo bo);

    /**
     * 校验并批量删除镇街机构信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    List<Tree<Long>> buildOrgTreeSelect(TDicOrgBo bo);
}
