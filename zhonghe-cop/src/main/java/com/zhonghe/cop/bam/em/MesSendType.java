package com.zhonghe.cop.bam.em;

import lombok.Getter;

/**
 * @Author: lpg
 * @Date: 2023/12/11/11:39
 * @Description:
 */
@Getter
public enum MesSendType {
    /**
     * 手动发送
     */
    MANUAL(0, "手动发送"),
    /**
     * 系统发送
     */
    SYSTEM(1, "系统发送");

    private Integer code;
    private String name;

    MesSendType(Integer code, String name) {
        this.code = code;
        this.name = name;
    }
}
