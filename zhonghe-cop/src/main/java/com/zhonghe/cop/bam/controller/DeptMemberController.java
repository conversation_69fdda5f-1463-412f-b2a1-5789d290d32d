package com.zhonghe.cop.bam.controller;

import cn.dev33.satoken.annotation.SaCheckLogin;
import cn.dev33.satoken.annotation.SaCheckPermission;
import com.zhonghe.common.annotation.Log;
import com.zhonghe.common.annotation.RepeatSubmit;
import com.zhonghe.common.core.controller.BaseController;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import com.zhonghe.common.enums.BusinessType;
import com.zhonghe.cop.bam.domain.bo.TDeptMemberBo;
import com.zhonghe.cop.bam.domain.vo.TDeptMemberVo;
import com.zhonghe.cop.bam.service.IDeptMemberService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotEmpty;
import java.util.Arrays;
import java.util.List;

/**
 * 部门成员
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Validated
@Api(value = "部门成员管理", tags = {"部门成员管理"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/cop/deptMember")
public class DeptMemberController extends BaseController {

    private final IDeptMemberService deptMemberService;

    /**
     * 查询部门成员列表
     */
    @ApiOperation("查询部门成员列表")
    @SaCheckLogin
    @GetMapping("/list")
    public R<List<TDeptMemberVo>> list() {
        return R.ok(deptMemberService.queryTreeList());
    }


    /**
     * 新增部门成员
     */
    @ApiOperation("新增部门成员(cop:deptMember:add)")
    @SaCheckPermission("cop:deptMember:add")
    @Log(title = "部门成员", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody TDeptMemberBo bo) {
        return toAjax(deptMemberService.insertByBo(bo));
    }

    /**
     * 修改部门成员
     */
    @ApiOperation("修改部门成员(cop:deptMember:edit)")
    @SaCheckPermission("cop:deptMember:edit")
    @Log(title = "部门成员", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody TDeptMemberBo bo) {
        return toAjax(deptMemberService.updateByBo(bo));
    }

    /**
     * 删除部门成员
     */
    @ApiOperation("删除部门成员(cop:deptMember:remove)")
    @SaCheckPermission("cop:deptMember:remove")
    @Log(title = "部门成员", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(
        @ApiParam("主键串")
        @NotEmpty(message = "主键不能为空")
        @PathVariable Long[] ids) {
        return toAjax(deptMemberService.deleteWithValidByIds(Arrays.asList(ids)));
    }

    /**
     * 排序
     */
    @ApiOperation("排序(cop:deptMember:edit)")
    @SaCheckPermission("cop:deptMember:edit")
    @Log(title = "部门成员", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PostMapping("/sort")
    public R<Void> sort(@RequestBody List<TDeptMemberVo> bos) {
        return toAjax(deptMemberService.sort(bos));
    }
}
