package com.zhonghe.cop.bam.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.zhonghe.common.annotation.Log;
import com.zhonghe.common.annotation.RepeatSubmit;
import com.zhonghe.common.core.controller.BaseController;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import com.zhonghe.common.enums.BusinessType;
import com.zhonghe.common.utils.poi.ExcelUtil;
import com.zhonghe.cop.bam.domain.bo.TMesRecordBo;
import com.zhonghe.cop.bam.domain.query.TMesRecordQuery;
import com.zhonghe.cop.bam.domain.vo.TMesRecordVo;
import com.zhonghe.cop.bam.service.ITMesRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 发送记录
 *
 * <AUTHOR>
 * @date 2023-12-11
 */
@Validated
@Api(value = "发送记录控制器", tags = {"发送记录管理"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/cop/mesRecord")
public class TMesRecordController extends BaseController {

    private final ITMesRecordService iTMesRecordService;

    /**
     * 查询发送记录列表
     */
    @ApiOperation("查询发送记录列表(cop:mesRecord:list)")
    @SaCheckPermission("cop:mesRecord:list")
    @GetMapping("/list")
    public TableDataInfo<TMesRecordVo> list(TMesRecordQuery bo, PageQuery pageQuery) {
        return iTMesRecordService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出发送记录列表
     */
    @ApiOperation("导出发送记录列表(cop:mesRecord:export)")
    @SaCheckPermission("cop:mesRecord:export")
    @Log(title = "发送记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(TMesRecordQuery bo, HttpServletResponse response) {
        List<TMesRecordVo> list = iTMesRecordService.queryList(bo);
        ExcelUtil.exportExcel(list, "发送记录", TMesRecordVo.class, response);
    }

    /**
     * 获取发送记录详细信息
     */
    @ApiOperation("获取发送记录详细信息(cop:mesRecord:query)")
    @SaCheckPermission("cop:mesRecord:query")
    @GetMapping("/{batchNo}")
    public R<TMesRecordVo> getInfo(@ApiParam("主键")
                                   @NotNull(message = "主键不能为空")
                                   @PathVariable("batchNo") String batchNo) {
        return R.ok(iTMesRecordService.queryByBatchNo(batchNo));
    }

    /**
     * 新增发送记录
     */
    @ApiOperation("新增发送记录(cop:mesRecord:add)")
    @SaCheckPermission("cop:mesRecord:add")
    @Log(title = "发送记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody TMesRecordBo bo) {
        return toAjax(iTMesRecordService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改发送记录
     */
    @ApiOperation("修改发送记录(cop:mesRecord:edit)")
    @SaCheckPermission("cop:mesRecord:edit")
    @Log(title = "发送记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody TMesRecordBo bo) {
        return toAjax(iTMesRecordService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除发送记录
     */
    @ApiOperation("删除发送记录(cop:mesRecord:remove)")
    @SaCheckPermission("cop:mesRecord:remove")
    @Log(title = "发送记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{batchNos}")
    public R<Void> remove(@ApiParam("主键串")
                          @NotEmpty(message = "主键不能为空")
                          @PathVariable String[] batchNos) {
        return toAjax(iTMesRecordService.deleteWithValidByBatchNos(Arrays.asList(batchNos), true) ? 1 : 0);
    }

    /**
     * 发送消息
     */
    @ApiOperation("发送消息(cop:mesRecord:send)")
    @SaCheckPermission("cop:mesRecord:send")
    @Log(title = "发送记录", businessType = BusinessType.OTHER)
    @PostMapping("/send/{batchNo}")
    public R<String> sendMes(@PathVariable("batchNo") String batchNo) {
        return iTMesRecordService.sendMes(batchNo);
    }

    /**
     * 发送每月一号汇总短信
     */
    @ApiOperation("发送每月一号汇总短信(cop:mesRecord:reissue)")
    @SaCheckPermission("cop:mesRecord:reissue")
    @Log(title = "发送每月一号汇总短信", businessType = BusinessType.OTHER)
    @PostMapping("/reissue")
    public R<Void> reissue() {
        return toAjax(iTMesRecordService.reissue() ? 1 : 0);
    }

}
