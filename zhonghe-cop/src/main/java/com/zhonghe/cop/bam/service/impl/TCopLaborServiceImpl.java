package com.zhonghe.cop.bam.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.cop.bam.domain.TCopLabor;
import com.zhonghe.cop.bam.domain.bo.TCopLaborBo;
import com.zhonghe.cop.bam.domain.vo.TCopLaborVo;
import com.zhonghe.cop.bam.mapper.TCopLaborMapper;
import com.zhonghe.cop.bam.service.ITCopLaborService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * 劳务人员Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-21
 */
@RequiredArgsConstructor
@Service
public class TCopLaborServiceImpl implements ITCopLaborService {

    private final TCopLaborMapper baseMapper;

    /**
     * 查询劳务人员
     */
    @Override
    public TCopLaborVo queryById(Long copLaborId) {
        return baseMapper.selectVoById(copLaborId);
    }

    /**
     * 查询劳务人员列表
     */
    @Override
    public TableDataInfo<TCopLaborVo> queryPageList(TCopLaborBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<TCopLabor> lqw = buildQueryWrapper(bo);
        Page<TCopLaborVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    @Override
    public List<TCopLaborVo> queryList(Long copId) {
        return baseMapper.selectVoList(Wrappers.<TCopLabor>lambdaQuery().eq(TCopLabor::getCopId, copId));
    }

    /**
     * 查询劳务人员列表
     */
    @Override
    public List<TCopLaborVo> queryList(TCopLaborBo bo) {
        LambdaQueryWrapper<TCopLabor> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<TCopLabor> buildQueryWrapper(TCopLaborBo bo) {
        LambdaQueryWrapper<TCopLabor> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getCopLaborId() != null, TCopLabor::getCopLaborId, bo.getCopLaborId());
        lqw.eq(StringUtils.isNotBlank(bo.getNote()), TCopLabor::getNote, bo.getNote());
        lqw.eq(bo.getCopId() != null, TCopLabor::getCopId, bo.getCopId());
        lqw.eq(StringUtils.isNotBlank(bo.getLabourCop()), TCopLabor::getLabourCop, bo.getLabourCop());
        //lqw.like(StringUtils.isNotBlank(bo.getLName()), TCopLabor::getLName, bo.getLName());
        lqw.eq(StringUtils.isNotBlank(bo.getLSex()), TCopLabor::getLSex, bo.getLSex());
        lqw.eq(StringUtils.isNotBlank(bo.getIdCard()), TCopLabor::getIdCard, bo.getIdCard());
        lqw.eq(StringUtils.isNotBlank(bo.getLDept()), TCopLabor::getLDept, bo.getLDept());
        lqw.eq(StringUtils.isNotBlank(bo.getLJob()), TCopLabor::getLJob, bo.getLJob());
        return lqw;
    }

    /**
     * 新增劳务人员
     */
    @Override
    public Boolean insertByBo(TCopLaborBo bo) {
        TCopLabor add = BeanUtil.toBean(bo, TCopLabor.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setCopLaborId(add.getCopLaborId());
        }
        return flag;
    }

    /**
     * 修改劳务人员
     */
    @Override
    public Boolean updateByBo(TCopLaborBo bo) {
        TCopLabor update = BeanUtil.toBean(bo, TCopLabor.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(TCopLabor entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 批量删除劳务人员
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
