package com.zhonghe.cop.bam.listener;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zhonghe.common.excel.ExcelListener;
import com.zhonghe.common.excel.ExcelResult;
import com.zhonghe.common.exception.ServiceException;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.common.utils.spring.SpringUtils;
import com.zhonghe.cop.bam.domain.TCopInformation;
import com.zhonghe.cop.bam.domain.excel.AddTCopInformationExcel;
import com.zhonghe.cop.bam.mapper.TCopInformationMapper;
import com.zhonghe.cop.bam.mapper.TDicOrgMapper;
import com.zhonghe.system.service.ISysDictDataService;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @Author: lpg
 * @Date: 2023/12/27/16:36
 * @Description: 企业备案导入监听
 */
@Slf4j
public class AddTCopInformationImportListener extends AnalysisEventListener<AddTCopInformationExcel> implements ExcelListener<AddTCopInformationExcel> {

    //校验表头
    private static final String[] HEADERS = {
        "主体名称",
        "法定代表人/负责人/经营者",
        "主体类型",
        "统一社会信用代码/注册号",
        "企业电话"
    };
    private static final String html = "<span style='color:red'>(%s)</span>";
    private final TCopInformationMapper tCopInformationMapper;
    private final ISysDictDataService iSysDictDataService;
    private final TDicOrgMapper tDicOrgMapper;
    private ExcelResult<AddTCopInformationExcel> excelResult;
    /**
     * 成功解析的数据
     */
    private List<AddTCopInformationExcel> successList = new ArrayList<>();

    /**
     * 失败解析的数据
     */
    private List<AddTCopInformationExcel> errorList = new ArrayList<>();

    public AddTCopInformationImportListener(ExcelResult<AddTCopInformationExcel> excelResult) {
        tCopInformationMapper = SpringUtils.getBean(TCopInformationMapper.class);
        iSysDictDataService = SpringUtils.getBean(ISysDictDataService.class);
        tDicOrgMapper = SpringUtils.getBean(TDicOrgMapper.class);
        this.excelResult = excelResult;
    }


    @Override
    public void invokeHead(Map<Integer, ReadCellData<?>> headMap, AnalysisContext context) {
        log.info("解析到一条头数据:{}", JSON.toJSONString(headMap));
        // 校验表头
        if (CollectionUtil.isEmpty(headMap)) {
            throw new ServiceException("导入的模板不符合,请检查后重新导入!");
        }
        for (int i = 0; i < HEADERS.length; i++) {
            if (!HEADERS[i].equals(headMap.get(i).getStringValue())) {
                throw new ServiceException("导入的模板不符合,请检查后重新导入!");
            }
        }
    }

    @Override
    public void invoke(AddTCopInformationExcel tCopInformationExcel, AnalysisContext analysisContext) {
        log.info("新增企业备案导入监听");

        //检查导入数据中企业名称是否有重复
        if (successList.stream().anyMatch(a -> a.getCopName().equals(tCopInformationExcel.getCopName()))) {
            //企业名称重复
            tCopInformationExcel.setCopName(tCopInformationExcel.getCopName() + String.format(html, "企业名称重复"));
            errorList.add(tCopInformationExcel);
            return;
        }

        if (StringUtils.isBlank(tCopInformationExcel.getCopName())) {
            //企业名称不能为空
            tCopInformationExcel.setCopName(String.format(html, "企业名称不能为空"));
            errorList.add(tCopInformationExcel);
        }

        boolean exists = tCopInformationMapper.exists(Wrappers.<TCopInformation>lambdaQuery().eq(TCopInformation::getCopName, tCopInformationExcel.getCopName()));
        // 判断企业是否存在
        if (exists) {
            //企业已存在 返回前端html标签
            tCopInformationExcel.setCopName(tCopInformationExcel.getCopName() + String.format(html, "企业已存在"));
            errorList.add(tCopInformationExcel);
            return;
        }
        //判断企业性质是否正确
        if (StringUtils.isBlank(tCopInformationExcel.getCopProperty())) {
            //企业性质不能为空
            tCopInformationExcel.setCopProperty(String.format(html, "企业性质不能为空"));
            errorList.add(tCopInformationExcel);
            return;
        }
        /*String copProperty = tCopInformationExcel.getCopProperty();
        List<SysDictData> sysDictData = iSysDictDataService.selectListByDictCode(CopConstants.COP_PROPERTY);
        //从字典数据中过滤出来，有的话取出一条数据
        SysDictData sysDictData1 = sysDictData.stream().filter(a -> a.getDictLabel().equals(copProperty)).findFirst().orElse(null);
        if (sysDictData1 == null) {
            //企业性质不正确
            tCopInformationExcel.setCopProperty(tCopInformationExcel.getCopProperty() + String.format(html, "企业性质不正确"));
            errorList.add(tCopInformationExcel);
            return;
        }*/
        //将成功解析的数据放入到集合中
        successList.add(tCopInformationExcel);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        log.info("企业备案导入完成");
    }

    @Override
    public ExcelResult<AddTCopInformationExcel> getExcelResult() {
        excelResult.getList().addAll(successList);
        excelResult.getErrorList().addAll(errorList);
        return excelResult;
    }
}
