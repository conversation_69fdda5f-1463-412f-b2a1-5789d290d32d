package com.zhonghe.cop.bam.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zhonghe.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 镇街机构对象 t_dic_org
 *
 * <AUTHOR>
 * @date 2023-12-21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("cop.t_dic_org")
public class TDicOrg extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 组织ID
     */
    @TableId
    private Long orgId;
    /**
     * 说明
     */
    private String note;
    /**
     * 机构名称
     */
    private String orgName;
    /**
     * 机构代码
     */
    private String orgCode;
    /**
     * 机构类型
     */
    private String orgType;
    /**
     * 父组织ID
     */
    private Long parentId;
    /**
     * 是否删除
     */
    @TableLogic
    private String delFlag;

}
