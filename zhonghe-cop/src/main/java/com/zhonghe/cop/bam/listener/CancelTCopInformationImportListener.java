package com.zhonghe.cop.bam.listener;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zhonghe.common.excel.ExcelListener;
import com.zhonghe.common.excel.ExcelResult;
import com.zhonghe.common.exception.ServiceException;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.common.utils.spring.SpringUtils;
import com.zhonghe.cop.bam.domain.TCopInformation;
import com.zhonghe.cop.bam.domain.excel.CancelTCopInformationExcel;
import com.zhonghe.cop.bam.mapper.TCopInformationMapper;
import com.zhonghe.cop.bam.mapper.TDicOrgMapper;
import com.zhonghe.system.service.ISysDictDataService;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @Author: lpg
 * @Date: 2023/12/27/16:36
 * @Description: 企业备案导入监听
 */
@Slf4j
public class CancelTCopInformationImportListener extends AnalysisEventListener<CancelTCopInformationExcel> implements ExcelListener<CancelTCopInformationExcel> {

    //校验表头
    private static final String[] HEADERS = {
        "原企业名称",
        "注销原因",
        "注销日期"
    };
    private static final String html = "<span style='color:red'>(%s)</span>";

    private final TCopInformationMapper tCopInformationMapper;
    private final ISysDictDataService iSysDictDataService;
    private final TDicOrgMapper tDicOrgMapper;
    private ExcelResult<CancelTCopInformationExcel> excelResult;
    /**
     * 成功解析的数据
     */
    private List<CancelTCopInformationExcel> successList = new ArrayList<>();
    /**
     * 失败解析的数据
     */
    private List<CancelTCopInformationExcel> errorList = new ArrayList<>();

    public CancelTCopInformationImportListener(ExcelResult<CancelTCopInformationExcel> excelResult) {
        tCopInformationMapper = SpringUtils.getBean(TCopInformationMapper.class);
        iSysDictDataService = SpringUtils.getBean(ISysDictDataService.class);
        tDicOrgMapper = SpringUtils.getBean(TDicOrgMapper.class);
        this.excelResult = excelResult;
    }

    @Override
    public void invokeHead(Map<Integer, ReadCellData<?>> headMap, AnalysisContext context) {
        log.info("解析到一条头数据:{}", JSON.toJSONString(headMap));
        // 校验表头
        if (CollectionUtil.isEmpty(headMap)) {
            throw new ServiceException("导入的模板不符合,请检查后重新导入!");
        }
        for (int i = 0; i < HEADERS.length; i++) {
            if (!HEADERS[i].equals(headMap.get(i).getStringValue())) {
                throw new ServiceException("导入的模板不符合,请检查后重新导入!");
            }
        }
    }

    @Override
    public void invoke(CancelTCopInformationExcel tCopInformationExcel, AnalysisContext analysisContext) {
        log.info("修改企业备案导入监听");

        //检验企业名称是否重复
        if (successList.stream().anyMatch(tCopInformationExcel1 -> tCopInformationExcel1.getCopName().equals(tCopInformationExcel.getCopName()))) {
            tCopInformationExcel.setCopName(String.format(html, "企业名称重复"));
            errorList.add(tCopInformationExcel);
            return;
        }

        /*
         * 企业名称验证
         */
        if (StringUtils.isBlank(tCopInformationExcel.getCopName())) {
            //企业名称不能为空
            tCopInformationExcel.setCopName(String.format(html, "企业名称不能为空"));
            errorList.add(tCopInformationExcel);
            return;
        }
        TCopInformation tCopInformation = tCopInformationMapper.selectOne(Wrappers.<TCopInformation>lambdaQuery().eq(TCopInformation::getCopName, tCopInformationExcel.getCopName()));
        // 判断企业是否存在
        if (tCopInformation == null) {
            //企业不存在 返回前端html标签
            tCopInformationExcel.setCopName(String.format(html, "企业不存在"));
            errorList.add(tCopInformationExcel);
            return;
        }
        //注销原因不能为空
        if (StringUtils.isBlank(tCopInformationExcel.getCancelCause())) {
            tCopInformationExcel.setCancelCause(String.format(html, "注销原因不能为空"));
            errorList.add(tCopInformationExcel);
            return;
        }
        //注销日期不能为空
        if (StringUtils.isBlank(tCopInformationExcel.getCancelDate())) {
            tCopInformationExcel.setCancelDate(String.format(html, "注销日期不能为空"));
            errorList.add(tCopInformationExcel);
            return;
        }
        //判断注销日期是否正确
        try {
            DateUtil.parse(tCopInformationExcel.getCancelDate(), "yyyy/MM/dd");
        } catch (Exception e) {
            tCopInformationExcel.setCancelDate(tCopInformationExcel.getCancelDate() + String.format(html, "注销日期格式不正确, 示例: 2000-01-01"));
            errorList.add(tCopInformationExcel);
            return;
        }

        //将成功解析的数据放入到集合中
        successList.add(tCopInformationExcel);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        log.info("企业备案导入完成");
    }

    @Override
    public ExcelResult<CancelTCopInformationExcel> getExcelResult() {
        excelResult.getList().addAll(successList);
        excelResult.getErrorList().addAll(errorList);
        return excelResult;
    }
}
