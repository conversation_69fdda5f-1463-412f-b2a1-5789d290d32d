package com.zhonghe.cop.bam.service.impl;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zhonghe.common.core.domain.entity.SysDept;
import com.zhonghe.common.core.domain.entity.SysUser;
import com.zhonghe.common.utils.DateUtils;
import com.zhonghe.cop.bam.constants.CopConstants;
import com.zhonghe.cop.bam.service.ISysSendSmsService;
import com.zhonghe.cop.bam.service.ITMesRecordService;
import com.zhonghe.cop.rep.domain.TRepReport;
import com.zhonghe.cop.rep.em.ResultAuditingStatus;
import com.zhonghe.cop.rep.mapper.TRepReportMapper;
import com.zhonghe.cop.rep.service.ITRepGarrepService;
import com.zhonghe.system.mapper.SysDeptMapper;
import com.zhonghe.system.mapper.SysUserMapper;
import com.zhonghe.system.service.ISysConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: lpg
 * @Date: 2024/06/13
 * @Description:
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ISysSendSmsServiceImpl implements ISysSendSmsService {
    private final ITRepGarrepService itRepGarrepService;
    private final SysDeptMapper sysDeptMapper;
    private final TRepReportMapper tRepReportMapper;
    private final SysUserMapper sysUserMapper;
    private final ISysConfigService iSysConfigService;
    private final ITMesRecordService itMesRecordService;

    /**
     * 汇总月报表短信发送（每月1号)
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sendMonthReport() {
        //是否开启汇总月报表短信发送（每月1号)
        String config = iSysConfigService.selectConfigByKey(CopConstants.IS_SEND_REP_GARREP);
        if (!Convert.toBool(config)) {
            log.info("汇总月报表短信发送（每月1号) 配置未开启");
            return;
        }
        log.info("汇总月报表短信发送（每月1号)开始发送");

        String message = itRepGarrepService.buildFirstMonthMes();

        List<SysUser> sysUsers = sysUserMapper.selectList(Wrappers.<SysUser>lambdaQuery().isNotNull(SysUser::getPhonenumber));

        itMesRecordService.sendUserMessage(sysUsers, message);
    }

    /**
     * 是否发送未上报部门短信发送(每月26号)
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sendUnreportedDept() {
        String config = iSysConfigService.selectConfigByKey(CopConstants.IS_SEND_DEPT_REP_NO_REPORT);
        if (!Convert.toBool(config)) {
            log.info("未上报部门短信发送(每月26号) 配置未开启");
            return;
        }
        log.info("未上报部门短信发送(每月26号)开始发送");
        List<SysDept> sysDepts = sysDeptMapper.selectList(Wrappers.query());
        //部门名称，党建办、纪检监察办、领导小组办公室排除
        List<SysDept> repDept = sysDepts.stream().filter(sysDept ->
            !CopConstants.EXCLUDE_DEPT.contains(sysDept.getDeptName())).collect(Collectors.toList());
        //查询本月的上报数据
        List<TRepReport> tRepReports = tRepReportMapper.selectList(Wrappers.<TRepReport>lambdaQuery().
            apply("to_char(rep_date,'yyyy-MM') = {0}", DateUtil.date().toString(DateUtils.YYYY_MM)));
        //查询未上报部门
        List<SysDept> unreportedDept = repDept.stream()
            .filter(sysDept -> tRepReports.stream().noneMatch(tRepReport -> tRepReport.getDeptId().equals(sysDept.getDeptId()))).collect(Collectors.toList());

        //查询部门用户
        List<Long> deptIds = unreportedDept.stream().map(SysDept::getDeptId).collect(Collectors.toList());

        if (deptIds.isEmpty()) {
            return;
        }
        List<SysUser> sysUsers = sysUserMapper.selectList(Wrappers.<SysUser>lambdaQuery()
            .isNotNull(SysUser::getPhonenumber)
            .in(SysUser::getDeptId, deptIds));

        String message = String.format(CopConstants.UNREPORTED_DEPT_TEMPLATE);

        itMesRecordService.sendUserMessage(sysUsers, message);
    }

    /**
     * 是否发送部门上报未处理短信提醒(每月5号)
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void sendUnprocessedDept() {
        String config = iSysConfigService.selectConfigByKey(CopConstants.IS_SEND_DEPT_REP_NO_HANDLE);
        if (!Convert.toBool(config)) {
            log.info("未上报部门短信发送（每月26号) 配置未开启");
            return;
        }
        log.info("未上报部门短信发送（每月26号)开始发送");
        //查询上报数据 = 上月 + 本月
        List<TRepReport> tRepReports = tRepReportMapper.selectList(Wrappers.<TRepReport>lambdaQuery()
            .apply("to_char(rep_date,'yyyy-MM') in ({0},{1})", DateUtil.offsetMonth(new Date(), -1).toString(DateUtils.YYYY_MM),
                DateUtil.date().toString(DateUtils.YYYY_MM)));
        //查询未处理部门
        List<SysDept> unprocessedDept = tRepReports.stream().filter(tRepReport -> ObjectUtil.isNotNull(tRepReport.getResultAuditing()) &&
                tRepReport.getResultAuditing().equals(ResultAuditingStatus.UNHANDLED.getCode()))
            .map(tRepReport -> sysDeptMapper.selectById(tRepReport.getDeptId())).collect(Collectors.toList());

        //查询部门用户
        List<Long> deptIds = unprocessedDept.stream().map(SysDept::getDeptId).collect(Collectors.toList());

        if (deptIds.isEmpty()) {
            return;
        }
        List<SysUser> sysUsers = sysUserMapper.selectList(Wrappers.<SysUser>lambdaQuery()
            .isNotNull(SysUser::getPhonenumber)
            .in(SysUser::getDeptId, deptIds));

        String message = String.format(CopConstants.UNPROCESSED_DEPT_TEMPLATE);

        itMesRecordService.sendUserMessage(sysUsers, message);
    }
}
