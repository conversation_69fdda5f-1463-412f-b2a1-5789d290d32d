package com.zhonghe.cop.bam.mapper;


import com.zhonghe.common.core.mapper.BaseMapperPlus;
import com.zhonghe.cop.bam.domain.TPerPerformance;
import com.zhonghe.cop.bam.domain.query.TPerPerformanceQuery;
import com.zhonghe.cop.bam.domain.vo.DeptPerformanceVo;
import com.zhonghe.cop.bam.domain.vo.TPerPerformanceVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 绩效管理Mapper接口
 *
 * <AUTHOR>
 * @date 2023-12-11
 */
public interface TPerPerformanceMapper extends BaseMapperPlus<TPerPerformanceMapper, TPerPerformance, TPerPerformanceVo> {

    /**
     * 查询绩效管理列表
     *
     * @param bo
     * @return
     */
    List<DeptPerformanceVo> selectDeptPerformance(@Param("ew") TPerPerformanceQuery bo);
}
