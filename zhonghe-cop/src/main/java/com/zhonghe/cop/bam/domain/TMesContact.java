package com.zhonghe.cop.bam.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zhonghe.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 通讯录，添加用户和企业时要往这里添加数据对象 t_mes_contact
 *
 * <AUTHOR>
 * @date 2023-12-08
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("cop.t_mes_contact")
public class TMesContact extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 通讯录ID
     */
    @TableId
    private Long mesContactId;
    /**
     * 是否删除
     */
    @TableLogic
    private String delFlag;
    /**
     * 备注
     */
    private String note;
    /**
     * 联系人描述
     */
    private String contactDescription;
    /**
     * 姓名
     */
    private String contactName;
    /**
     * 电话
     */
    private String contactNumber;
    /**
     * 联系人类型（对应t_dic_contacttype主键）
     */
    private Long contactTypeId;
    /**
     * 联系人类别（默认0，0——用户、1——企业）
     */
    private Integer contactType;
    /**
     * 联系人ID（对应T_COP_Information主键或T_USER_User主键）
     */
    private Long contactId;

}
