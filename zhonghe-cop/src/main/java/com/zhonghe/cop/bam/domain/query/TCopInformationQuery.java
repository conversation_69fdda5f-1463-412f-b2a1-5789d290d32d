package com.zhonghe.cop.bam.domain.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;


/**
 * 企业信息业务对象 t_cop_information
 *
 * <AUTHOR>
 * @date 2023-12-07
 */

@Data
@ApiModel("企业信息查询对象")
public class TCopInformationQuery implements Serializable {

    private static final long serialVersionUID = 1532812846901066127L;
    /**
     * 企业名称
     */
    @ApiModelProperty(value = "企业名称")
    private String copName;

    /**
     * 企业法人
     */
    @ApiModelProperty(value = "企业法人")
    private String copOwner;

    /**
     * 企业性质ID
     */
    @ApiModelProperty(value = "企业性质ID")
    private Long propertyId;

    /**
     * 所属村社区
     */
    @ApiModelProperty(value = "所属村社区")
    private String orgName;

    /**
     * 是否定位
     */
    @ApiModelProperty(value = "是否定位")
    private Integer isLocation;

    /**
     * 是否注销
     */
    @ApiModelProperty(value = "是否注销(0:否;1:是)")
    private Integer isCancel = 0;

    /**
     * 是否本镇企业
     */
    @ApiModelProperty(value = "是否本镇企业(0:否;1:是)")
    private Integer isLocal;

}
