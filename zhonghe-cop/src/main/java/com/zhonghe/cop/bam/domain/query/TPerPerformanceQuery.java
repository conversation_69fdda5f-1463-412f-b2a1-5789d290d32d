package com.zhonghe.cop.bam.domain.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 绩效管理业务对象 t_per_performance
 *
 * <AUTHOR>
 * @date 2023-12-11
 */

@Data
@ApiModel("绩效管理业务对象")
public class TPerPerformanceQuery {

    private static final long serialVersionUID = 5922022882745571065L;

    /**
     * 汇总年份
     */
    @ApiModelProperty(value = "汇总年份")
    private Integer year;
    /**
     * 部门ID
     */
    @ApiModelProperty(value = "部门ID")
    private Long deptId;


}
