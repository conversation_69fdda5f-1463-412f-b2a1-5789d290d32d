package com.zhonghe.cop.bam.service;

import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.cop.bam.domain.bo.TTemTemplateBo;
import com.zhonghe.cop.bam.domain.vo.TTemTemplateVo;

import java.util.Collection;
import java.util.List;

/**
 * 模板Service接口
 *
 * <AUTHOR>
 * @date 2023-12-10
 */
public interface ITTemTemplateService {

    /**
     * 查询模板
     */
    TTemTemplateVo queryById(Long templateId);

    /**
     * 查询模板列表
     */
    TableDataInfo<TTemTemplateVo> queryPageList(TTemTemplateBo bo, PageQuery pageQuery);

    /**
     * 查询模板列表
     */
    List<TTemTemplateVo> queryList(TTemTemplateBo bo);

    /**
     * 修改模板
     */
    Boolean insertByBo(TTemTemplateBo bo);

    /**
     * 修改模板
     */
    Boolean updateByBo(TTemTemplateBo bo);

    /**
     * 校验并批量删除模板信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据模板类型查询模板
     *
     * @param templateTypeId
     * @return
     */
    List<TTemTemplateVo> queryByTypeId(Long templateTypeId);
}
