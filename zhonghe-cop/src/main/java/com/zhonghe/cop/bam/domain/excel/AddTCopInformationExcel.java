package com.zhonghe.cop.bam.domain.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: lpg
 * @Date: 2023/12/27/16:33
 * @Description:
 */
@Data
public class AddTCopInformationExcel {

    /**
     * 主体名称
     */
    @ExcelProperty(value = "主体名称")
    @ApiModelProperty("主体名称")
    private String copName;


    /**
     * 法定代表人/负责人/经营者
     */
    @ExcelProperty(value = "法定代表人/负责人/经营者")
    @ApiModelProperty("法定代表人/负责人/经营者")
    private String copOwner;

    /**
     * 主体类型
     */
    @ExcelProperty(value = "主体类型")
    @ApiModelProperty("主体类型")
    private String copProperty;

    /**
     * 组织机构代码证
     */
    @ExcelProperty(value = "统一社会信用代码/注册号")
    @ApiModelProperty("统一社会信用代码/注册号")
    private String corporateCode;


    /**
     * 主体联系电话
     */
    @ExcelProperty(value = "企业电话")
    @ApiModelProperty("企业电话")
    private String copPhone;


}
