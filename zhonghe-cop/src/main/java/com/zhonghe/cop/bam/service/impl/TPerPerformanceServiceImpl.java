package com.zhonghe.cop.bam.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.domain.entity.SysDept;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.utils.DateUtils;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.cop.bam.domain.TPerPerformance;
import com.zhonghe.cop.bam.domain.bo.TPerPerformanceBo;
import com.zhonghe.cop.bam.domain.query.TPerPerformanceQuery;
import com.zhonghe.cop.bam.domain.vo.DeptPerformanceVo;
import com.zhonghe.cop.bam.domain.vo.TPerPerformanceVo;
import com.zhonghe.cop.bam.mapper.TPerPerformanceMapper;
import com.zhonghe.cop.bam.service.ITPerPerformanceService;
import com.zhonghe.cop.rep.em.IsDeal;
import com.zhonghe.system.mapper.SysDeptMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 绩效管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-11
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class TPerPerformanceServiceImpl implements ITPerPerformanceService {

    private final TPerPerformanceMapper baseMapper;
    private final SysDeptMapper sysDeptMapper;

    /**
     * 查询绩效管理
     */
    @Override
    public TPerPerformanceVo queryById(Long perPerformanceId) {
        return baseMapper.selectVoById(perPerformanceId);
    }

    /**
     * 查询绩效管理列表
     */
    @Override
    public TableDataInfo<TPerPerformanceVo> queryPageList(TPerPerformanceBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<TPerPerformance> lqw = buildQueryWrapper(bo);
        Page<TPerPerformanceVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    @Override
    public List<DeptPerformanceVo> queryList(TPerPerformanceQuery bo) {
        if (bo.getYear() == null) {
            bo.setYear(DateUtil.year(new Date()));
        }
        List<DeptPerformanceVo> deptPerformanceVos = baseMapper.selectDeptPerformance(bo);
        for (DeptPerformanceVo deptPerformanceVo : deptPerformanceVos) {
            //如果12个月出现一次减10分
            Field[] declaredFields = deptPerformanceVo.getClass().getDeclaredFields();
            for (int i = 0; i < declaredFields.length; i++) {
                declaredFields[i].setAccessible(true);
                try {
                    if (i > 3 && (Integer) declaredFields[i].get(deptPerformanceVo) == 0) {
                        deptPerformanceVo.setScore(deptPerformanceVo.getScore() - 10);
                    }
                } catch (IllegalAccessException e) {
                    throw new RuntimeException(e);
                }
            }

        }
        //部门名称，党建办、纪检监察办、领导小组办公室排除
        deptPerformanceVos.removeIf(deptPerformanceVo ->
            "党建办".equals(deptPerformanceVo.getDeptName())
                || "纪检监察办".equals(deptPerformanceVo.getDeptName())
                || "领导小组办公室".equals(deptPerformanceVo.getDeptName())
                || "党政综合办".equals(deptPerformanceVo.getDeptName())
                || "领导班子".equals(deptPerformanceVo.getDeptName())
                || "工作专班办公室".equals(deptPerformanceVo.getDeptName())
        );
        return deptPerformanceVos;
    }

    /**
     * 查询绩效管理列表
     */
    @Override
    public List<TPerPerformanceVo> queryList(TPerPerformanceBo bo) {
        LambdaQueryWrapper<TPerPerformance> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<TPerPerformance> buildQueryWrapper(TPerPerformanceBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<TPerPerformance> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getPerPerformanceId() != null, TPerPerformance::getPerPerformanceId, bo.getPerPerformanceId());
        lqw.eq(StringUtils.isNotBlank(bo.getNote()), TPerPerformance::getNote, bo.getNote());
        lqw.eq(StringUtils.isNotBlank(bo.getRepDate()), TPerPerformance::getRepDate, bo.getRepDate());
        lqw.eq(bo.getDeptId() != null, TPerPerformance::getDeptId, bo.getDeptId());
        lqw.eq(bo.getIsDeal() != null, TPerPerformance::getIsDeal, bo.getIsDeal());
        return lqw;
    }

    /**
     * 新增绩效管理
     */
    @Override
    public Boolean insertByBo(TPerPerformanceBo bo) {
        TPerPerformance add = BeanUtil.toBean(bo, TPerPerformance.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setPerPerformanceId(add.getPerPerformanceId());
        }
        return flag;
    }

    /**
     * 修改绩效管理
     */
    @Override
    public Boolean updateByBo(TPerPerformanceBo bo) {
        TPerPerformance update = BeanUtil.toBean(bo, TPerPerformance.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(TPerPerformance entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deptPerformanceTask() {
        List<SysDept> sysDepts = sysDeptMapper.selectList();
        List<TPerPerformance> tPerPerformances = new ArrayList<>();
        //删除当月上报数据
        baseMapper.delete(Wrappers.<TPerPerformance>lambdaQuery().eq(TPerPerformance::getRepDate, DateUtil.parse(DateUtils.getDate(), DateUtils.YYYY_MM_DD)));

        for (SysDept sysDept : sysDepts) {
            TPerPerformance tPerPerformance = new TPerPerformance();
            tPerPerformance.setDeptId(sysDept.getDeptId());
            tPerPerformance.setRepDate(DateUtil.parse(DateUtils.getDate(), DateUtils.YYYY_MM_DD));
            //设置未上报
            tPerPerformance.setIsDeal(IsDeal.NO.getCode());
            tPerPerformances.add(tPerPerformance);
        }
        baseMapper.insertBatch(tPerPerformances);
    }

    /**
     * 批量删除绩效管理
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
