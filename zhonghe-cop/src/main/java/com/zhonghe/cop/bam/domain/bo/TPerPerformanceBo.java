package com.zhonghe.cop.bam.domain.bo;

import com.zhonghe.common.core.domain.BaseEntity;
import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


/**
 * 绩效管理业务对象 t_per_performance
 *
 * <AUTHOR>
 * @date 2023-12-11
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("绩效管理业务对象")
public class TPerPerformanceBo extends BaseEntity {

    private static final long serialVersionUID = 5922022882745571065L;
    /**
     * 绩效管理ID
     */
    @ApiModelProperty(value = "绩效管理ID", required = true)
    @NotNull(message = "绩效管理ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long perPerformanceId;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注", required = true)
    @NotBlank(message = "备注不能为空", groups = {AddGroup.class, EditGroup.class})
    private String note;

    /**
     * 汇总月份
     */
    @ApiModelProperty(value = "汇总月份", required = true)
    @NotBlank(message = "汇总月份不能为空", groups = {AddGroup.class, EditGroup.class})
    private String repDate;

    /**
     * 部门ID
     */
    @ApiModelProperty(value = "部门ID", required = true)
    @NotNull(message = "部门ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long deptId;

    /**
     * 是否上报（默认0，0——未上报、1——已上报）
     */
    @ApiModelProperty(value = "是否上报（默认0，0——未上报、1——已上报）", required = true)
    @NotNull(message = "是否上报（默认0，0——未上报、1——已上报）不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long isDeal;


}
