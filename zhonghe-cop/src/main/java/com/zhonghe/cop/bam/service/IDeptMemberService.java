package com.zhonghe.cop.bam.service;

import com.zhonghe.cop.bam.domain.bo.TDeptMemberBo;
import com.zhonghe.cop.bam.domain.vo.TDeptMemberVo;

import java.util.Collection;
import java.util.List;

/**
 * 部门成员服务接口
 *
 * @Author: lpg
 * @Date: 2025/07/14
 * @Description: 部门成员管理服务
 */
public interface IDeptMemberService {

    /**
     * 查询部门成员树结构
     *
     * @return 部门成员树列表
     */
    List<TDeptMemberVo> queryTreeList();

    /**
     * 查询所有部门成员列表
     *
     * @return 部门成员列表
     */
    List<TDeptMemberVo> queryList();

    /**
     * 根据ID查询部门成员信息
     *
     * @param id 部门成员ID
     * @return 部门成员信息
     */
    TDeptMemberVo queryById(Long id);

    /**
     * 新增部门成员
     *
     * @param bo 部门成员业务对象
     * @return 结果
     */
    Boolean insertByBo(TDeptMemberBo bo);

    /**
     * 修改部门成员
     *
     * @param bo 部门成员业务对象
     * @return 结果
     */
    Boolean updateByBo(TDeptMemberBo bo);

    /**
     * 删除部门成员
     *
     * @param ids 部门成员ID集合
     * @return 结果
     */
    Boolean deleteWithValidByIds(Collection<Long> ids);

    int sort(List<TDeptMemberVo> tDeptMembers);
}
