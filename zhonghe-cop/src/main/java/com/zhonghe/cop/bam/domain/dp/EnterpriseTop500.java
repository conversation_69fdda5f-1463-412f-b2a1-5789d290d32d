package com.zhonghe.cop.bam.domain.dp;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zhonghe.system.mappedtypes.GeometryTypeHandler;
import lombok.Data;
import net.postgis.jdbc.geometry.Geometry;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author: lpg
 * @Date: 2024/01/10
 * @Description:
 */
@Deprecated
@Data
@TableName("cop.sl_enterprise_top_500")
public class EnterpriseTop500 implements Serializable {
    private static final long serialVersionUID = 3527258340713032504L;
    /**
     * 500强企业ID
     */
    @TableId
    private Long enterpriseId;

    /**
     * 企业名称
     */
    private String name;

    /**
     * 企业类型
     */
    private String enterpriseType;

    /**
     * 企业地址
     */
    private String address;

    /**
     * 法人
     */
    private String legalPerson;

    /**
     * 企业电话
     */
    private String phone;

    /**
     * 地理位置
     */
    @TableField(typeHandler = GeometryTypeHandler.class)
    private Geometry geom;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;

    /**
     * 状态
     */
    private String status;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新者
     */
    private String updateBy;
}
