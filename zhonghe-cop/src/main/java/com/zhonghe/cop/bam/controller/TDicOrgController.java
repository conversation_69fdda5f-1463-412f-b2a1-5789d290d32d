package com.zhonghe.cop.bam.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.lang.tree.Tree;
import com.zhonghe.common.annotation.Anonymous;
import com.zhonghe.common.annotation.Log;
import com.zhonghe.common.annotation.RepeatSubmit;
import com.zhonghe.common.core.controller.BaseController;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import com.zhonghe.common.enums.BusinessType;
import com.zhonghe.common.utils.poi.ExcelUtil;
import com.zhonghe.cop.bam.domain.bo.TDicOrgBo;
import com.zhonghe.cop.bam.domain.vo.TDicOrgVo;
import com.zhonghe.cop.bam.service.ITDicOrgService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 镇街机构
 *
 * <AUTHOR>
 * @date 2023-12-21
 */
@Validated
@Api(value = "镇街机构控制器", tags = {"镇街机构管理"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/cop/dicOrg")
public class TDicOrgController extends BaseController {

    private final ITDicOrgService iTDicOrgService;

    /**
     * 查询镇街机构列表
     */
    @ApiOperation("查询镇街机构列表")
    @SaCheckPermission("cop:dicOrg:list")
    @GetMapping("/list")
    public TableDataInfo<TDicOrgVo> list(TDicOrgBo bo, PageQuery pageQuery) {
        return iTDicOrgService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询镇街机构树
     */
    @ApiOperation("查询镇街机构树")
    @Anonymous
    @GetMapping("/treeSelector")
    public R<List<Tree<Long>>> treeSelector(TDicOrgBo bo) {
        return R.ok(iTDicOrgService.buildOrgTreeSelect(bo));
    }

    /**
     * 导出镇街机构列表
     */
    @ApiOperation("导出镇街机构列表")
    @SaCheckPermission("cop:dicOrg:export")
    @Log(title = "镇街机构", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(TDicOrgBo bo, HttpServletResponse response) {
        List<TDicOrgVo> list = iTDicOrgService.queryList(bo);
        ExcelUtil.exportExcel(list, "镇街机构", TDicOrgVo.class, response);
    }

    /**
     * 获取镇街机构详细信息
     */
    @ApiOperation("获取镇街机构详细信息")
    @SaCheckPermission("cop:dicOrg:query")
    @GetMapping("/{orgId}")
    public R<TDicOrgVo> getInfo(@ApiParam("主键")
                                @NotNull(message = "主键不能为空")
                                @PathVariable("orgId") Long orgId) {
        return R.ok(iTDicOrgService.queryById(orgId));
    }

    /**
     * 新增镇街机构
     */
    @ApiOperation("新增镇街机构")
    @SaCheckPermission("cop:dicOrg:add")
    @Log(title = "镇街机构", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody TDicOrgBo bo) {
        return toAjax(iTDicOrgService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改镇街机构
     */
    @ApiOperation("修改镇街机构")
    @SaCheckPermission("cop:dicOrg:edit")
    @Log(title = "镇街机构", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody TDicOrgBo bo) {
        return toAjax(iTDicOrgService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除镇街机构
     */
    @ApiOperation("删除镇街机构")
    @SaCheckPermission("cop:dicOrg:remove")
    @Log(title = "镇街机构", businessType = BusinessType.DELETE)
    @DeleteMapping("/{orgIds}")
    public R<Void> remove(@ApiParam("主键串")
                          @NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] orgIds) {
        return toAjax(iTDicOrgService.deleteWithValidByIds(Arrays.asList(orgIds), true) ? 1 : 0);
    }
}
