package com.zhonghe.cop.bam.service;

import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.cop.bam.domain.bo.TPerPerformanceBo;
import com.zhonghe.cop.bam.domain.query.TPerPerformanceQuery;
import com.zhonghe.cop.bam.domain.vo.DeptPerformanceVo;
import com.zhonghe.cop.bam.domain.vo.TPerPerformanceVo;

import java.util.Collection;
import java.util.List;

/**
 * 绩效管理Service接口
 *
 * <AUTHOR>
 * @date 2023-12-11
 */
public interface ITPerPerformanceService {

    /**
     * 查询绩效管理
     */
    TPerPerformanceVo queryById(Long perPerformanceId);

    /**
     * 查询绩效管理列表
     */
    TableDataInfo<TPerPerformanceVo> queryPageList(TPerPerformanceBo bo, PageQuery pageQuery);

    /**
     * 查询绩效管理列表
     */
    List<TPerPerformanceVo> queryList(TPerPerformanceBo bo);

    /**
     * 查询绩效管理列表
     *
     * @param bo
     * @return
     */
    List<DeptPerformanceVo> queryList(TPerPerformanceQuery bo);

    /**
     * 修改绩效管理
     */
    Boolean insertByBo(TPerPerformanceBo bo);

    /**
     * 修改绩效管理
     */
    Boolean updateByBo(TPerPerformanceBo bo);

    /**
     * 校验并批量删除绩效管理信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 插入部门考核数据
     */
    void deptPerformanceTask();

}
