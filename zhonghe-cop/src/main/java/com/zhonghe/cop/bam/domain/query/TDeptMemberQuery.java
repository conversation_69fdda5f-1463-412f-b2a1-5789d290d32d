package com.zhonghe.cop.bam.domain.query;

import com.zhonghe.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 部门成员查询对象
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("部门成员查询对象")
public class TDeptMemberQuery extends BaseEntity {

    private static final long serialVersionUID = -7583772210318138923L;
    /**
     * 父节点ID
     */
    @ApiModelProperty("父节点ID")
    private Long parentId;

    /**
     * 小组成员名称，可为人名或单位名
     */
    @ApiModelProperty("小组成员名称")
    private String name;
}
