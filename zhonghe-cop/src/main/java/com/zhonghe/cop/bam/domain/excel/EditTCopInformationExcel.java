package com.zhonghe.cop.bam.domain.excel;

import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: lpg
 * @Date: 2023/12/27/16:33
 * @Description:
 */
@Data
public class EditTCopInformationExcel {
    /**
     * 序号
     */
    @ExcelProperty(value = "序号")
    @ApiModelProperty("序号")
    private String serialNumber;

    /**
     * 统一社会信用代码/注册号
     */
    @ExcelProperty(value = "统一社会信用代码/注册号")
    @ApiModelProperty("统一社会信用代码/注册号")
    private String corporateCode;
    /**
     * 主体名称
     */
    @ExcelProperty(value = "主体名称")
    @ApiModelProperty("主体名称")
    private String copName;

    /**
     * 住所/经营场所/驻在场所
     */
    @ExcelProperty(value = "住所/经营场所/驻在场所")
    @ApiModelProperty("住所/经营场所/驻在场所")
    private String copAddress;

    /**
     * 主体类型
     */
    @ExcelProperty(value = "主体类型")
    @ApiModelProperty("主体类型")
    private String copProperty;

    /**
     * 法定代表人/负责人/经营者
     */
    @ExcelProperty(value = "法定代表人/负责人/经营者")
    @ApiModelProperty("法定代表人/负责人/经营者")
    private String copOwner;

    /**
     * 主体联系电话
     */
    @ExcelProperty(value = "主体联系电话")
    @ApiModelProperty("主体联系电话")
    private String copTel;

    /**
     * 联络员
     */
    @ExcelProperty(value = "联络员")
    @ApiModelProperty("联络员")
    private String copContacts;

    /**
     * 联络员联系电话
     */
    @ExcelProperty(value = "联络员联系电话")
    @ApiModelProperty("联络员联系电话")
    private String copContactsPhone;

    /**
     * 法定代表人/负责人/经营者联系电话
     */
    @ExcelProperty(value = "法定代表人/负责人/经营者联系电话")
    @ApiModelProperty("法定代表人/负责人/经营者联系电话")
    private String copPhone;
}
