package com.zhonghe.cop.bam.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zhonghe.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;


/**
 * 绩效管理对象 t_per_performance
 *
 * <AUTHOR>
 * @date 2023-12-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("cop.t_per_performance")
public class TPerPerformance extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 绩效管理ID
     */
    @TableId
    private Long perPerformanceId;
    /**
     * 是否删除
     */
    @TableLogic
    private String delFlag;
    /**
     * 备注
     */
    private String note;
    /**
     * 汇总月份
     */
    private Date repDate;
    /**
     * 部门ID
     */
    private Long deptId;
    /**
     * 是否上报（默认0，0——未上报、1——已上报）
     */
    private Integer isDeal;

}
