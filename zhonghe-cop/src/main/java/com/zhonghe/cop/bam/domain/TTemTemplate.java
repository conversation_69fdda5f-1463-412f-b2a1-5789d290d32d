package com.zhonghe.cop.bam.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zhonghe.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 模板对象 t_tem_template
 *
 * <AUTHOR>
 * @date 2023-12-10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("cop.t_tem_template")
public class TTemTemplate extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 模板ID
     */
    @TableId
    private Long templateId;

    /**
     * 是否删除0否1是
     */
    @TableLogic
    private String delFlag;
    /**
     * 说明
     */
    private String note;
    /**
     * 模板类型ID
     */
    private Long templateTypeId;
    /**
     * 模板缩写
     */
    private String templateShort;
    /**
     * 模板内容
     */
    private String templateContent;
    /**
     * 模板排序
     */
    private Integer templateOrder;

}
