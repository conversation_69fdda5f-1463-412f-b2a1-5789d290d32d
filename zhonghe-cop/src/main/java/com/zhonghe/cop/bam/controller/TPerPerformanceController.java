package com.zhonghe.cop.bam.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.zhonghe.common.core.controller.BaseController;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.cop.bam.domain.query.TPerPerformanceQuery;
import com.zhonghe.cop.bam.domain.vo.DeptPerformanceVo;
import com.zhonghe.cop.bam.service.ITPerPerformanceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 绩效管理
 *
 * <AUTHOR>
 * @date 2023-12-11
 */
@Validated
@Api(value = "绩效管理控制器", tags = {"绩效管理管理"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/cop/perPerformance")
public class TPerPerformanceController extends BaseController {

    private final ITPerPerformanceService iTPerPerformanceService;

    /**
     * 查询部门绩效列表
     */
    @ApiOperation("查询部门绩效列表(cop:perPerformance:list)")
    @SaCheckPermission("cop:perPerformance:list")
    @GetMapping("/list")
    public R<List<DeptPerformanceVo>> list(TPerPerformanceQuery bo) {
        return R.ok(iTPerPerformanceService.queryList(bo));
    }
}
