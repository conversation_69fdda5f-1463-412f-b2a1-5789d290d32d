package com.zhonghe.cop.bam.service;

import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.cop.bam.domain.TDeptRole;
import com.zhonghe.cop.bam.domain.bo.TDeptRoleBo;
import com.zhonghe.cop.bam.domain.query.TDeptRoleQuery;
import com.zhonghe.cop.bam.domain.vo.TDeptRoleVo;

import java.util.Collection;
import java.util.List;

/**
 * 部门职能服务接口
 *
 * @Author: lpg
 * @Date: 2025/07/14
 * @Description: 部门职能管理服务
 */
public interface IdeptRoleService {

    /**
     * 查询部门职能列表
     *
     * @param query     查询条件
     * @param pageQuery 分页参数
     * @return 分页结果
     */
    TableDataInfo<TDeptRoleVo> queryPageList(TDeptRoleQuery query, PageQuery pageQuery);

    /**
     * 查询部门职能列表
     *
     * @param query 查询条件
     * @return 部门职能列表
     */
    List<TDeptRoleVo> queryList(TDeptRoleQuery query);

    /**
     * 根据ID查询部门职能信息
     *
     * @param id 部门职能ID
     * @return 部门职能信息
     */
    TDeptRoleVo queryById(Long id);

    /**
     * 新增部门职能
     *
     * @param bo 部门职能业务对象
     * @return 结果
     */
    Boolean insertByBo(TDeptRoleBo bo);

    /**
     * 修改部门职能
     *
     * @param bo 部门职能业务对象
     * @return 结果
     */
    Boolean updateByBo(TDeptRoleBo bo);

    /**
     * 删除部门职能
     *
     * @param ids 部门职能ID集合
     * @return 结果
     */
    Boolean deleteWithValidByIds(Collection<Long> ids);

    int sort(List<TDeptRole> tDeptRoles);
}
