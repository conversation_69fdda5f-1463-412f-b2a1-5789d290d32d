package com.zhonghe.cop.bam.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhonghe.common.constant.Constants;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.domain.entity.SysDictData;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.exception.ServiceException;
import com.zhonghe.common.helper.LoginHelper;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.common.utils.poi.ExcelUtil;
import com.zhonghe.cop.bam.constants.CopConstants;
import com.zhonghe.cop.bam.domain.TCopCancelRecord;
import com.zhonghe.cop.bam.domain.TCopInformation;
import com.zhonghe.cop.bam.domain.TCopLabor;
import com.zhonghe.cop.bam.domain.TMesContact;
import com.zhonghe.cop.bam.domain.YwDgsgajNewBzdzxx2;
import com.zhonghe.cop.bam.domain.ZsjEntregInfo;
import com.zhonghe.cop.bam.domain.bo.AddCopContactBo;
import com.zhonghe.cop.bam.domain.bo.CancelCopBo;
import com.zhonghe.cop.bam.domain.bo.TCopInformationBo;
import com.zhonghe.cop.bam.domain.dp.EnterpriseHighTech;
import com.zhonghe.cop.bam.domain.dp.EnterpriseListed;
import com.zhonghe.cop.bam.domain.dp.EnterpriseScale;
import com.zhonghe.cop.bam.domain.dp.EnterpriseTop500;
import com.zhonghe.cop.bam.domain.dto.SsztDto;
import com.zhonghe.cop.bam.domain.excel.AddTCopInformationExcel;
import com.zhonghe.cop.bam.domain.excel.CancelTCopInformationExcel;
import com.zhonghe.cop.bam.domain.excel.EditTCopInformationExcel;
import com.zhonghe.cop.bam.domain.query.TCopInformationQuery;
import com.zhonghe.cop.bam.domain.vo.CopGeomVo;
import com.zhonghe.cop.bam.domain.vo.TCopInformationVo;
import com.zhonghe.cop.bam.domain.vo.TMesContactVo;
import com.zhonghe.cop.bam.em.ContactClass;
import com.zhonghe.cop.bam.em.ContactType;
import com.zhonghe.cop.bam.em.IsCancel;
import com.zhonghe.cop.bam.em.IsLocal;
import com.zhonghe.cop.bam.em.IsLocation;
import com.zhonghe.cop.bam.mapper.TCopCancelRecordMapper;
import com.zhonghe.cop.bam.mapper.TCopInformationMapper;
import com.zhonghe.cop.bam.mapper.TCopLaborMapper;
import com.zhonghe.cop.bam.mapper.TDicOrgMapper;
import com.zhonghe.cop.bam.mapper.TMesContactMapper;
import com.zhonghe.cop.bam.mapper.YwDgsgajNewBzdzxx2Mapper;
import com.zhonghe.cop.bam.mapper.ZsjEntregInfoMapper;
import com.zhonghe.cop.bam.mapper.dp.EnterpriseHighTechMapper;
import com.zhonghe.cop.bam.mapper.dp.EnterpriseListedMapper;
import com.zhonghe.cop.bam.mapper.dp.EnterpriseScaleMapper;
import com.zhonghe.cop.bam.mapper.dp.EnterpriseTop500Mapper;
import com.zhonghe.cop.bam.service.TICopInformationService;
import com.zhonghe.cop.rep.domain.DangerousEnterprises;
import com.zhonghe.cop.rep.domain.HighRiskEnterprises;
import com.zhonghe.cop.rep.domain.ProblemEnterprises;
import com.zhonghe.cop.rep.domain.RiskEnterprises;
import com.zhonghe.cop.rep.domain.excel.ImportSCZTCop;
import com.zhonghe.cop.rep.mapper.DangerousEnterprisesMapper;
import com.zhonghe.cop.rep.mapper.HighRiskEnterprisesMapper;
import com.zhonghe.cop.rep.mapper.ProblemEnterprisesMapper;
import com.zhonghe.cop.rep.mapper.RiskEnterprisesMapper;
import com.zhonghe.system.service.ISysDictDataService;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import net.postgis.jdbc.geometry.Point;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 企业信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TCopInformationServiceImpl implements TICopInformationService {

    private final TCopInformationMapper baseMapper;
    private final TMesContactMapper mesContactMapper;
    private final TCopLaborMapper copLaborMapper;
    private final TCopCancelRecordMapper copCancelRecordMapper;
    private final YwDgsgajNewBzdzxx2Mapper ywDgsgajNewBzdzxx2Mapper;
    private final ZsjEntregInfoMapper zsjEntregInfoMapper;
    private final ISysDictDataService iSysDictDataService;
    private final TDicOrgMapper tDicOrgMapper;


    private final ProblemEnterprisesMapper problemEnterprisesMapper;
    private final RiskEnterprisesMapper riskEnterprisesMapper;
    private final DangerousEnterprisesMapper dangerousEnterprisesMapper;
    private final HighRiskEnterprisesMapper highRiskEnterprisesMapper;

    private final EnterpriseHighTechMapper enterpriseHighTechMapper;
    private final EnterpriseListedMapper enterpriseListedMapper;
    private final EnterpriseScaleMapper enterpriseScaleMapper;
    private final EnterpriseTop500Mapper enterpriseTop500Mapper;

    private final ISysDictDataService sysDictDataService;


    /**
     * 查询企业信息
     */
    @Override
    public TCopInformationVo queryById(Long copId) {
        return baseMapper.selectVoById(copId);
    }

    /**
     * 查询企业信息列表
     */
    @Override
    public TableDataInfo<TCopInformationVo> queryPageList(TCopInformationQuery bo, PageQuery pageQuery) {
        QueryWrapper<TCopInformation> qw = buildQueryWrapper(bo);
        Page<TCopInformationVo> result = baseMapper.selectCopList(pageQuery.build(), qw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询企业信息列表
     */
    @Override
    public List<TCopInformationVo> queryList(TCopInformationQuery bo) {
        QueryWrapper<TCopInformation> lqw = buildQueryWrapper(bo);
        return baseMapper.selectCopList(lqw);
    }

    private QueryWrapper<TCopInformation> buildQueryWrapper(TCopInformationQuery bo) {
        QueryWrapper<TCopInformation> qw = Wrappers.query();
        qw.like(StringUtils.isNotBlank(bo.getCopName()), "tci.cop_name", bo.getCopName());
        qw.like(StringUtils.isNotBlank(bo.getCopOwner()), "tci.cop_owner", bo.getCopOwner());
        qw.eq(bo.getPropertyId() != null, "tci.property_id", bo.getPropertyId());
        qw.like(StringUtils.isNotBlank(bo.getOrgName()), "tci.org_name", bo.getOrgName());
        qw.eq(bo.getIsCancel() != null, "tci.is_cancel", bo.getIsCancel());
        qw.eq(bo.getIsLocation() != null, "tci.is_location", bo.getIsLocation());
        qw.eq(bo.getIsLocal() != null, "tci.is_local", bo.getIsLocal());
        qw.eq("tci.del_flag", Constants.DEL_FLAG_NORMAL);
        qw.orderByDesc("tci.create_time");
        return qw;
    }

    /**
     * 新增企业信息
     */
    @Override
    public Boolean insertByBo(TCopInformationBo bo) {
        TCopInformation add = BeanUtil.toBean(bo, TCopInformation.class);

        validEntityBeforeSave(add);
        if (StringUtils.isNotBlank(bo.getLon()) && StringUtils.isNotBlank(bo.getLat())) {
            add.setGeom(new Point(Double.parseDouble(bo.getLon()), Double.parseDouble(bo.getLat())));
            add.setIsLocation(IsLocation.YES.getCode());
        }
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setCopId(add.getCopId());
        }

        //更新大屏图层信息
        updateBigMapCopGeom(bo.getCopId());

        //新增企业信息时，要往通讯录表中添加一条数据
        TMesContact tMesContact = new TMesContact();
        //企业ID
        tMesContact.setContactId(add.getCopId());
        //姓名
        tMesContact.setContactName(bo.getCopOwner());
        //电话
        tMesContact.setContactNumber(bo.getCopPhone());
        //类型
        tMesContact.setContactTypeId(Long.valueOf(ContactType.COP.getCode()));
        //类别
        tMesContact.setContactType(ContactClass.COP.getCode());
        //备注
        tMesContact.setNote(bo.getCopName());

        mesContactMapper.insert(tMesContact);
        return flag;
    }

    /**
     * 修改企业信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(TCopInformationBo bo) {
        TCopInformation update = BeanUtil.toBean(bo, TCopInformation.class);
        validEntityBeforeSave(update);
        if (StringUtils.isNotBlank(bo.getLon()) && StringUtils.isNotBlank(bo.getLat())) {
            update.setGeom(new Point(Double.parseDouble(bo.getLon()), Double.parseDouble(bo.getLat())));
            update.setIsLocation(IsLocation.YES.getCode());
        }
        boolean flag = baseMapper.updateById(update) > 0;
        if (flag) {
            //更新大屏市场主体信息
            updateDpsszt(bo.getCopId());
            //更新大屏图层信息
            updateBigMapCopGeom(bo.getCopId());

        }
        return flag;
    }

    @Override
    public void updateBigMapCopGeom(Long copId) {
        TCopInformation tCopInformation = baseMapper.selectById(copId);
        //获取企业性质
        String copProperty = iSysDictDataService.selectDictLabel(CopConstants.COP_PROPERTY, String.valueOf(tCopInformation.getPropertyId()));

        ProblemEnterprises problemEnterprises = problemEnterprisesMapper.selectById(copId);
        if (problemEnterprises != null) {
            problemEnterprises.setGeom(tCopInformation.getGeom());
            problemEnterprises.setEnterpriseAddress(tCopInformation.getCopAddress());
            problemEnterprises.setContactPhone(tCopInformation.getCopPhone());
            problemEnterprises.setLegalPerson(tCopInformation.getCopOwner());
            problemEnterprises.setEnterpriseProperty(copProperty);
            problemEnterprisesMapper.updateById(problemEnterprises);
            return;
        }
        RiskEnterprises riskEnterprises = riskEnterprisesMapper.selectById(copId);
        if (riskEnterprises != null) {
            riskEnterprises.setGeom(tCopInformation.getGeom());
            riskEnterprises.setEnterpriseAddress(tCopInformation.getCopAddress());
            riskEnterprises.setContactPhone(tCopInformation.getCopPhone());
            riskEnterprises.setLegalPerson(tCopInformation.getCopOwner());
            riskEnterprises.setEnterpriseProperty(copProperty);
            riskEnterprisesMapper.updateById(riskEnterprises);
            return;
        }
        DangerousEnterprises dangerousEnterprises = dangerousEnterprisesMapper.selectById(copId);
        if (dangerousEnterprises != null) {
            dangerousEnterprises.setGeom(tCopInformation.getGeom());
            dangerousEnterprises.setEnterpriseAddress(tCopInformation.getCopAddress());
            dangerousEnterprises.setContactPhone(tCopInformation.getCopPhone());
            dangerousEnterprises.setLegalPerson(tCopInformation.getCopOwner());
            dangerousEnterprises.setEnterpriseProperty(copProperty);
            dangerousEnterprisesMapper.updateById(dangerousEnterprises);
            return;
        }
        HighRiskEnterprises highRiskEnterprises = highRiskEnterprisesMapper.selectById(copId);
        if (highRiskEnterprises != null) {
            highRiskEnterprises.setGeom(tCopInformation.getGeom());
            highRiskEnterprises.setEnterpriseAddress(tCopInformation.getCopAddress());
            highRiskEnterprises.setContactPhone(tCopInformation.getCopPhone());
            highRiskEnterprises.setLegalPerson(tCopInformation.getCopOwner());
            highRiskEnterprises.setEnterpriseProperty(copProperty);
            highRiskEnterprisesMapper.updateById(highRiskEnterprises);
        }

    }

    public void updateDpsszt(Long copId) {
        TCopInformation information = baseMapper.selectById(copId);
        SsztDto ssztDto = new SsztDto();
        ssztDto.setEnterpriseId(information.getCopId());
        ssztDto.setName(information.getCopName());
        ssztDto.setAddress(information.getCopAddress());
        ssztDto.setLegalPerson(information.getCopOwner());
        ssztDto.setPhone(information.getCopPhone());
        ssztDto.setGeom(information.getGeom());

        EnterpriseHighTech enterpriseHighTech = BeanUtil.toBean(ssztDto, EnterpriseHighTech.class);
        EnterpriseListed enterpriseListed = BeanUtil.toBean(ssztDto, EnterpriseListed.class);
        EnterpriseScale enterpriseScale = BeanUtil.toBean(ssztDto, EnterpriseScale.class);
        EnterpriseTop500 enterpriseTop500 = BeanUtil.toBean(ssztDto, EnterpriseTop500.class);


        enterpriseHighTechMapper.update(enterpriseHighTech, Wrappers.<EnterpriseHighTech>lambdaQuery().eq(EnterpriseHighTech::getName, information.getCopName()));
        enterpriseListedMapper.update(enterpriseListed, Wrappers.<EnterpriseListed>lambdaQuery().eq(EnterpriseListed::getName, information.getCopName()));
        enterpriseScaleMapper.update(enterpriseScale, Wrappers.<EnterpriseScale>lambdaQuery().eq(EnterpriseScale::getName, information.getCopName()));
        enterpriseTop500Mapper.update(enterpriseTop500,
            Wrappers.<EnterpriseTop500>lambdaQuery().eq(EnterpriseTop500::getName, information.getCopName()));

    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(TCopInformation entity) {
        boolean nameExists = baseMapper.exists(Wrappers.<TCopInformation>lambdaQuery()
            .eq(TCopInformation::getCopName, entity.getCopName())
            .ne(entity.getCopId() != null, TCopInformation::getCopId, entity.getCopId()));
        if (nameExists) {
            throw new ServiceException("企业名称已存在");
        }

        if (StringUtils.isNotBlank(entity.getCorporateCode())) {
            boolean codeExists = baseMapper.exists(Wrappers.<TCopInformation>lambdaQuery()
                .eq(TCopInformation::getCorporateCode, entity.getCorporateCode())
                .ne(entity.getCopId() != null, TCopInformation::getCopId, entity.getCopId()));
            if (codeExists) {
                throw new ServiceException("组织机构代码证已存在");
            }
        }
    }

    @Override
    @DS("data")
    public Map<String, Object> getAddressInfo(String copName) {
        Map<String, Object> map = new HashMap<>();
        //查询市场主体库
        List<ZsjEntregInfo> zsjEntregInfos = zsjEntregInfoMapper.selectList(Wrappers.<ZsjEntregInfo>lambdaQuery().like(ZsjEntregInfo::getEntname, copName));
        if (CollectionUtils.isEmpty(zsjEntregInfos)) {
            throw new ServiceException("查询不到企业信息");
        }
        //获取企业地址
        String address = zsjEntregInfos.get(0).getDom();
        //查询二标四实库
        List<YwDgsgajNewBzdzxx2> ywDgsgajNewBzdzxx2 = ywDgsgajNewBzdzxx2Mapper.selectList(Wrappers.<YwDgsgajNewBzdzxx2>lambdaQuery().like(YwDgsgajNewBzdzxx2::getDzqc, address));
        map.put("address", address);
        if (CollectionUtils.isNotEmpty(ywDgsgajNewBzdzxx2)) {
            map.put("lon", ywDgsgajNewBzdzxx2.get(0).getZxjd());
            map.put("lat", ywDgsgajNewBzdzxx2.get(0).getZxwd());
        }
        return map;
    }

    @Override
    @Transactional
    public Boolean recovery(List<Long> copIds) {

        List<TCopInformation> tCopInformations = baseMapper.selectBatchIds(copIds);

        for (TCopInformation tCopInformation : tCopInformations) {
            if (IsCancel.NO.getCode().equals(tCopInformation.getIsCancel())) {
                throw new ServiceException("企业未注销");
            }
            tCopInformation.setIsCancel(IsCancel.NO.getCode());
        }

        return baseMapper.updateBatchById(tCopInformations);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancel(CancelCopBo bo) {
        List<TCopInformation> tCopInformations = baseMapper.selectBatchIds(bo.getCopIds());
        for (TCopInformation tCopInformation : tCopInformations) {
            if (IsCancel.YES.getCode().equals(tCopInformation.getIsCancel())) {
                throw new ServiceException("企业已注销");
            }
            tCopInformation.setIsCancel(IsCancel.YES.getCode());
        }
        //记录注销信息
        List<TCopCancelRecord> copCancelRecords = new ArrayList<>();
        for (Long copId : bo.getCopIds()) {
            TCopCancelRecord copCancelRecord = new TCopCancelRecord();
            copCancelRecord.setCopId(copId);
            copCancelRecord.setCancelCause(bo.getCancelCause());
            copCancelRecord.setCancelDate(bo.getCancelDate());
            copCancelRecord.setUserId(LoginHelper.getUserId());
            copCancelRecords.add(copCancelRecord);
        }
        //批量插入注销信息
        copCancelRecordMapper.insertBatch(copCancelRecords);

        return baseMapper.updateBatchById(tCopInformations);
    }


    @Override
    public TableDataInfo<TCopInformationVo> queryCancelList(TCopInformationQuery bo, PageQuery pageQuery) {
        QueryWrapper<TCopInformation> qw = buildQueryWrapper(bo);
        //已注销
        qw.eq("tci.is_cancel", IsCancel.YES.getCode());
        Page<TCopInformationVo> result = baseMapper.selectCopList(pageQuery.build(), qw);
        return TableDataInfo.build(result);
    }

    @Override
    public Boolean deleteContact(List<Long> ids, boolean b) {
        return mesContactMapper.deleteBatchIds(ids) > 0;
    }

    @Override
    public Boolean updateConract(AddCopContactBo bo) {
        TMesContact tMesContact = BeanUtil.toBean(bo, TMesContact.class);

        return mesContactMapper.updateById(tMesContact) > 0;
    }

    @Override
    public Boolean insertContact(AddCopContactBo bo) {
        TMesContact tMesContact = BeanUtil.toBean(bo, TMesContact.class);
        //企业ID
        tMesContact.setContactId(bo.getCopId());
        //类别
        tMesContact.setContactType(ContactClass.COP.getCode());

        return mesContactMapper.insert(tMesContact) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean excelEditData(List<EditTCopInformationExcel> editTCopInformationExcels) {

        List<SysDictData> sysDictDatas = iSysDictDataService.selectListByDictCode(CopConstants.COP_PROPERTY);

        List<TCopInformation> tCopInformation = baseMapper.selectList(Wrappers.<TCopInformation>lambdaQuery()
            .eq(TCopInformation::getIsLocal, IsLocal.YES.getCode())
            .eq(TCopInformation::getIsCancel, IsCancel.NO.getCode()));
        //1.如果全量数据导入更新，在库企业不在导入数据中的，将其注销
        for (TCopInformation copInformation : tCopInformation) {
            if (editTCopInformationExcels.stream().noneMatch(a -> a.getCorporateCode().equals(copInformation.getCorporateCode()))) {
                copInformation.setIsCancel(IsCancel.YES.getCode());
                copInformation.setBy1(DateUtil.format(new Date(), "yyyy年MM月dd日") + "导入数据，注销该企业");
            }
        }
        baseMapper.updateBatchById(tCopInformation);

        //2。重新获取未注销的企业信息
        tCopInformation = baseMapper.selectList(Wrappers.<TCopInformation>lambdaQuery().eq(TCopInformation::getIsCancel, IsCancel.NO.getCode()));

        List<TCopInformation> updates = new ArrayList<>();
        List<TCopInformation> adds = new ArrayList<>();

        for (EditTCopInformationExcel bo : editTCopInformationExcels) {
            String copProperty = bo.getCopProperty();
            SysDictData sysDictData = sysDictDatas.stream().filter(a -> a.getDictLabel().equals(copProperty)).findFirst().orElse(null);

            List<TCopInformation> informations = tCopInformation.stream().filter(a -> a.getCorporateCode().equals(bo.getCorporateCode())).collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(informations)) {
                TCopInformation update = informations.get(0);
                //更新
                if (sysDictData != null && StringUtils.isNotBlank(sysDictData.getDictValue())) {
                    update.setPropertyId(Long.valueOf(sysDictData.getDictValue()));
                }
                if (StringUtils.isNotBlank(bo.getCopName())) {
                    update.setCopName(bo.getCopName());
                }
                if (StringUtils.isNotBlank(bo.getCopOwner())) {
                    update.setCopOwner(bo.getCopOwner());
                }
                if (StringUtils.isNotBlank(bo.getCopContacts())) {
                    update.setCopContacts(bo.getCopContacts());
                }
                if (StringUtils.isNotBlank(bo.getCopPhone())) {
                    update.setCopPhone(bo.getCopPhone());
                }
                if (StringUtils.isNotBlank(bo.getCopAddress())) {
                    update.setCopAddress(bo.getCopAddress());
                }
                updates.add(update);
            } else {
                //新增
                TCopInformation add = BeanUtil.toBean(bo, TCopInformation.class);
                if (sysDictData != null && StringUtils.isNotBlank(sysDictData.getDictValue())) {
                    add.setPropertyId(Long.valueOf(sysDictData.getDictValue()));
                }
                //设置未定位
                add.setIsLocation(IsLocation.NO.getCode());
                //设置未注销
                add.setIsCancel(IsCancel.NO.getCode());
                //设置本地企业
                add.setIsLocal(IsLocal.YES.getCode());
                adds.add(add);
            }
        }
        baseMapper.insertBatch(adds);
        boolean flag = baseMapper.updateBatchById(updates);

        //更新地理坐标
        //SpringUtils.publishEvent(new UpdateCopGeomEvent(this));
        return flag;
    }

    /**
     * 异步执行Excel编辑数据
     */
    @Override
    @Async
    @Transactional(rollbackFor = Exception.class)
    public CompletableFuture<Boolean> excelEditDataAsync(List<EditTCopInformationExcel> editTCopInformationExcels) {
        log.info("开始异步处理Excel编辑数据，数据量: {}", editTCopInformationExcels.size());
        try {
            Boolean result = excelEditData(editTCopInformationExcels);
            log.info("异步处理Excel编辑数据完成，结果: {}", result);
            return CompletableFuture.completedFuture(result);
        } catch (Exception e) {
            log.error("异步处理Excel编辑数据失败", e);
            return CompletableFuture.completedFuture(false);
        }
    }


    @SneakyThrows
    @Override
    @Transactional
    public void importSCZTCop(MultipartFile file, String type) {
        List<ImportSCZTCop> list = ExcelUtil.importExcel(file.getInputStream(), ImportSCZTCop.class, false).getList();
        if (CollectionUtils.isEmpty(list)) {
            throw new ServiceException("导入失败，未解析到有效数据");
        }
        if (list.stream().anyMatch(a -> StringUtils.isBlank(a.getCopName()))) {
            throw new ServiceException("企业名称不能为空");
        }

        // 执行导入逻辑
        processImportSCZTCop(list, type);
    }

    /**
     * 异步执行市场主体企业导入
     */
    @Override
    @Async
    @SneakyThrows
    @Transactional
    public CompletableFuture<Void> importSCZTCopAsync(MultipartFile file, String type) {
        log.info("开始异步处理市场主体企业导入，类型: {}", type);
        try {
            importSCZTCop(file, type);
            log.info("异步处理市场主体企业导入完成");
            return CompletableFuture.completedFuture(null);
        } catch (Exception e) {
            log.error("异步处理市场主体企业导入失败", e);
            throw e;
        }
    }

    /**
     * 处理市场主体企业导入的核心逻辑
     */
    private void processImportSCZTCop(List<ImportSCZTCop> list, String type) {
        if (type.equals("1")) {
            //先清除excel不存在的数据
            enterpriseScaleMapper.selectList().forEach(a -> {
                if (list.stream().noneMatch(b -> b.getCopName().equals(a.getName()))) {
                    enterpriseScaleMapper.delete(Wrappers.<EnterpriseScale>lambdaQuery().eq(EnterpriseScale::getName, a.getName()));
                }
            });
            //规上企业
            for (ImportSCZTCop importSCZTCop : list) {
                //查询企业是否存在
                EnterpriseScale enterpriseScale =
                    enterpriseScaleMapper.selectOne(Wrappers.<EnterpriseScale>lambdaQuery().eq(EnterpriseScale::getName,
                        importSCZTCop.getCopName()));
                //查询企业库
                List<TCopInformation> tCopInformation =
                    baseMapper.selectList(Wrappers.<TCopInformation>lambdaQuery().eq(TCopInformation::getCopName,
                        importSCZTCop.getCopName()));

                if (enterpriseScale == null) {
                    enterpriseScale = new EnterpriseScale();
                    enterpriseScale.setName(importSCZTCop.getCopName());

                    if (CollectionUtils.isNotEmpty(tCopInformation)) {
                        enterpriseScale.setAddress(tCopInformation.get(0).getCopAddress());
                        enterpriseScale.setLegalPerson(tCopInformation.get(0).getCopOwner());
                        enterpriseScale.setPhone(tCopInformation.get(0).getCopPhone());
                        enterpriseScale.setGeom(tCopInformation.get(0).getGeom());
                        enterpriseScale.setEnterpriseType("规上");
                        enterpriseScale.setEnterpriseId(tCopInformation.get(0).getCopId());
                    }
                    enterpriseScaleMapper.insert(enterpriseScale);
                } else {
                    if (CollectionUtils.isNotEmpty(tCopInformation)) {
                        enterpriseScale.setAddress(tCopInformation.get(0).getCopAddress());
                        enterpriseScale.setLegalPerson(tCopInformation.get(0).getCopOwner());
                        enterpriseScale.setPhone(tCopInformation.get(0).getCopPhone());
                        enterpriseScale.setGeom(tCopInformation.get(0).getGeom());
                        enterpriseScale.setEnterpriseType("规上");
                        enterpriseScale.setEnterpriseId(tCopInformation.get(0).getCopId());
                    }
                    enterpriseScaleMapper.update(enterpriseScale, Wrappers.<EnterpriseScale>lambdaQuery()
                        .eq(EnterpriseScale::getName, importSCZTCop.getCopName()));
                }

            }
        } else if (type.equals("2")) {
            //上市企业
            enterpriseListedMapper.selectList().forEach(a -> {
                if (list.stream().noneMatch(b -> b.getCopName().equals(a.getName()))) {
                    enterpriseListedMapper.delete(Wrappers.<EnterpriseListed>lambdaQuery().eq(EnterpriseListed::getName, a.getName()));
                }
            });
            for (ImportSCZTCop importSCZTCop : list) {
                //查询企业是否存在
                EnterpriseListed enterpriseListed =
                    enterpriseListedMapper.selectOne(Wrappers.<EnterpriseListed>lambdaQuery().eq(EnterpriseListed::getName,
                        importSCZTCop.getCopName()));
                //查询企业库
                List<TCopInformation> tCopInformation =
                    baseMapper.selectList(Wrappers.<TCopInformation>lambdaQuery().eq(TCopInformation::getCopName,
                        importSCZTCop.getCopName()));

                if (enterpriseListed == null) {
                    enterpriseListed = new EnterpriseListed();
                    enterpriseListed.setName(importSCZTCop.getCopName());

                    if (CollectionUtils.isNotEmpty(tCopInformation)) {
                        enterpriseListed.setAddress(tCopInformation.get(0).getCopAddress());
                        enterpriseListed.setLegalPerson(tCopInformation.get(0).getCopOwner());
                        enterpriseListed.setPhone(tCopInformation.get(0).getCopPhone());
                        enterpriseListed.setGeom(tCopInformation.get(0).getGeom());
                        enterpriseListed.setEnterpriseType(tCopInformation.get(0).getPropertyId().toString());
                    }
                    enterpriseListedMapper.insert(enterpriseListed);
                } else {
                    if (CollectionUtils.isNotEmpty(tCopInformation)) {
                        enterpriseListed.setAddress(tCopInformation.get(0).getCopAddress());
                        enterpriseListed.setLegalPerson(tCopInformation.get(0).getCopOwner());
                        enterpriseListed.setPhone(tCopInformation.get(0).getCopPhone());
                        enterpriseListed.setGeom(tCopInformation.get(0).getGeom());
                    }
                    enterpriseListedMapper.update(enterpriseListed, Wrappers.<EnterpriseListed>lambdaQuery()
                        .eq(EnterpriseListed::getName, importSCZTCop.getCopName()));
                }
            }
        } else if (type.equals("3")) {
            //高新企业
            enterpriseHighTechMapper.selectList().forEach(a -> {
                if (list.stream().noneMatch(b -> b.getCopName().equals(a.getName()))) {
                    enterpriseHighTechMapper.delete(Wrappers.<EnterpriseHighTech>lambdaQuery().eq(EnterpriseHighTech::getName, a.getName()));
                }
            });
            for (ImportSCZTCop importSCZTCop : list) {
                //查询企业是否存在
                EnterpriseHighTech enterpriseHighTech =
                    enterpriseHighTechMapper.selectOne(Wrappers.<EnterpriseHighTech>lambdaQuery().eq(EnterpriseHighTech::getName,
                        importSCZTCop.getCopName()));
                //查询企业库
                List<TCopInformation> tCopInformation =
                    baseMapper.selectList(Wrappers.<TCopInformation>lambdaQuery().eq(TCopInformation::getCopName,
                        importSCZTCop.getCopName()));

                if (enterpriseHighTech == null) {
                    enterpriseHighTech = new EnterpriseHighTech();
                    enterpriseHighTech.setName(importSCZTCop.getCopName());

                    if (CollectionUtils.isNotEmpty(tCopInformation)) {
                        enterpriseHighTech.setAddress(tCopInformation.get(0).getCopAddress());
                        enterpriseHighTech.setLegalPerson(tCopInformation.get(0).getCopOwner());
                        enterpriseHighTech.setPhone(tCopInformation.get(0).getCopPhone());
                        enterpriseHighTech.setGeom(tCopInformation.get(0).getGeom());
                        enterpriseHighTech.setEnterpriseType("高新");
                        enterpriseHighTech.setEnterpriseType(tCopInformation.get(0).getPropertyId().toString());
                    }
                    enterpriseHighTechMapper.insert(enterpriseHighTech);
                } else {
                    if (CollectionUtils.isNotEmpty(tCopInformation)) {
                        enterpriseHighTech.setAddress(tCopInformation.get(0).getCopAddress());
                        enterpriseHighTech.setLegalPerson(tCopInformation.get(0).getCopOwner());
                        enterpriseHighTech.setPhone(tCopInformation.get(0).getCopPhone());
                        enterpriseHighTech.setGeom(tCopInformation.get(0).getGeom());
                        enterpriseHighTech.setEnterpriseType("高新");
                    }
                    enterpriseHighTechMapper.update(enterpriseHighTech, Wrappers.<EnterpriseHighTech>lambdaQuery()
                        .eq(EnterpriseHighTech::getName, importSCZTCop.getCopName()));
                }
            }
        }

    }

    @Override
    @DS("data")
    public List<CopGeomVo> getNearby(Double lon, Double lat) {
        return ywDgsgajNewBzdzxx2Mapper.getNearby(lon, lat);
    }

    @Override
    public Boolean queryCopName(String copName) {
        return baseMapper.exists(Wrappers.<TCopInformation>lambdaQuery().eq(TCopInformation::getCopName, copName));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean excelCancelData(List<CancelTCopInformationExcel> cancelTCopInformationExcels) {
        for (CancelTCopInformationExcel bo : cancelTCopInformationExcels) {
            TCopInformation tCopInformation = baseMapper.selectOne(Wrappers.<TCopInformation>lambdaQuery()
                .eq(TCopInformation::getCopName, bo.getCopName()));

            CancelCopBo cancelCopBo = new CancelCopBo();
            //企业ID
            cancelCopBo.setCopIds(Collections.singletonList(tCopInformation.getCopId()));
            //注销原因
            cancelCopBo.setCancelCause(bo.getCancelCause());
            //注销日期
            cancelCopBo.setCancelDate(DateUtil.parse(bo.getCancelDate(), "yyyy/MM/dd"));

            cancel(cancelCopBo);
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean excelAddData(List<AddTCopInformationExcel> addTCopInformationExcels) {
        List<SysDictData> sysDictDatas = iSysDictDataService.selectListByDictCode(CopConstants.COP_PROPERTY);

        List<TCopInformation> tCopInformations = new ArrayList<>();
        for (AddTCopInformationExcel bo : addTCopInformationExcels) {
            TCopInformation tCopInformation = BeanUtil.toBean(bo, TCopInformation.class);

            String copProperty = bo.getCopProperty();

            //从字典数据中过滤出来，有的话取出一条数据
            SysDictData sysDictData = sysDictDatas.stream().filter(a -> a.getDictLabel().equals(copProperty)).findFirst().orElse(null);
            if (sysDictData == null) {
                //新增字典数据
                SysDictData dictData = new SysDictData();
                dictData.setDictType(CopConstants.COP_PROPERTY);
                dictData.setDictLabel(copProperty);
                //字典值
                dictData.setDictValue(String.valueOf(sysDictDatas.size() + 1));
                dictData.setDictSort(sysDictDatas.size() + 1);
                dictData.setStatus(Constants.STATUS);
                boolean flag = sysDictDataService.insertDictData(dictData) > 0;
                if (flag) {
                    sysDictData = dictData;
                    sysDictDatas.add(sysDictData);
                }
            }
            tCopInformation.setPropertyId(Long.valueOf(sysDictData.getDictValue()));

            //设置未定位
            tCopInformation.setIsLocation(IsLocation.NO.getCode());
            //设置未注销
            tCopInformation.setIsCancel(IsCancel.NO.getCode());

            tCopInformations.add(tCopInformation);
        }
        //SpringUtils.publishEvent(new UpdateCopGeomEvent(this));

        return baseMapper.insertBatch(tCopInformations);
    }

    /**
     * 异步执行Excel新增数据
     */
    @Override
    @Async
    @Transactional(rollbackFor = Exception.class)
    public CompletableFuture<Boolean> excelAddDataAsync(List<AddTCopInformationExcel> addTCopInformationExcels) {
        log.info("开始异步处理Excel新增数据，数据量: {}", addTCopInformationExcels.size());
        try {
            Boolean result = excelAddData(addTCopInformationExcels);
            log.info("异步处理Excel新增数据完成，结果: {}", result);
            return CompletableFuture.completedFuture(result);
        } catch (Exception e) {
            log.error("异步处理Excel新增数据失败", e);
            return CompletableFuture.completedFuture(false);
        }
    }

    @Override
    public List<TMesContactVo> contactList(Long copId) {
        return mesContactMapper.selectVoList(Wrappers.<TMesContact>lambdaQuery().eq(TMesContact::getContactId, copId));
    }

    /**
     * 批量删除企业信息
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            baseMapper.selectBatchIds(ids).forEach(vo -> {
                //只有注销的才能删除
                if (IsCancel.NO.getCode().equals(vo.getIsCancel())) {
                    throw new ServiceException("企业未注销");
                }
            });
            //删除企业信息时，要删除通讯录表中的数据
            mesContactMapper.delete(Wrappers.<TMesContact>lambdaQuery().in(TMesContact::getContactId, ids));
            //删除企业信息时，要删除劳务人员表中的数据
            copLaborMapper.delete(Wrappers.<TCopLabor>lambdaQuery().in(TCopLabor::getCopId, ids));
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
