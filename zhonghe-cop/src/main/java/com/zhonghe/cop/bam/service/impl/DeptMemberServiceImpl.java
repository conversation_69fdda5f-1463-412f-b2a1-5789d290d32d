package com.zhonghe.cop.bam.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zhonghe.common.utils.BeanCopyUtils;
import com.zhonghe.cop.bam.domain.TDeptMember;
import com.zhonghe.cop.bam.domain.bo.TDeptMemberBo;
import com.zhonghe.cop.bam.domain.vo.TDeptMemberVo;
import com.zhonghe.cop.bam.mapper.TDeptMemberMapper;
import com.zhonghe.cop.bam.service.IDeptMemberService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 部门成员服务实现类
 *
 * @Author: lpg
 * @Date: 2025/07/14
 * @Description: 部门成员服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DeptMemberServiceImpl implements IDeptMemberService {

    private final TDeptMemberMapper baseMapper;

    /**
     * 查询部门成员树结构
     */
    @Override
    public List<TDeptMemberVo> queryTreeList() {
        List<TDeptMemberVo> allMembers = queryList();
        if (allMembers == null || allMembers.isEmpty()) {
            return new ArrayList<>();
        }

        return buildTree(allMembers);
    }

    /**
     * 查询所有部门成员列表
     */
    @Override
    public List<TDeptMemberVo> queryList() {
        LambdaQueryWrapper<TDeptMember> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(TDeptMember::getParentId)
            .orderByAsc(TDeptMember::getSortOrder);
        return baseMapper.selectVoList(lqw, TDeptMemberVo.class);
    }

    /**
     * 构建树形结构
     */
    private List<TDeptMemberVo> buildTree(List<TDeptMemberVo> allMembers) {
        Map<Long, List<TDeptMemberVo>> parentIdMap = allMembers.stream()
            .collect(Collectors.groupingBy(TDeptMemberVo::getParentId));

        allMembers.forEach(member -> {
            List<TDeptMemberVo> children = parentIdMap.get(member.getId());
            member.setChildren(children);
        });

        return allMembers.stream()
            .filter(member -> member.getParentId() == null || member.getParentId() == 0)
            .collect(Collectors.toList());
    }

    /**
     * 根据ID查询部门成员信息
     */
    @Override
    public TDeptMemberVo queryById(Long id) {
        return baseMapper.selectVoById(id, TDeptMemberVo.class);
    }

    /**
     * 新增部门成员
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(TDeptMemberBo bo) {
        TDeptMember add = BeanCopyUtils.copy(bo, TDeptMember.class);
        if (add != null && add.getParentId() == null) {
            add.setParentId(0L);
        }

        if (add != null && add.getParentId() != 0) {
            int level = getNodeLevel(add.getParentId());
            if (level >= 4) {
                throw new RuntimeException("只允许添加三级节点，当前操作将创建第" + (level + 1) + "级节点");
            }
        }

        // 如果没有设置排序字段，默认为当前父节点下最大排序号+1
        if (add.getSortOrder() == null) {
            Integer maxSortOrder = baseMapper.selectMaxSortOrder(add.getParentId());
            add.setSortOrder(maxSortOrder == null ? 1 : maxSortOrder + 1);
        }

        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 计算节点的层级
     *
     * @param nodeId 节点ID
     * @return 层级数（1表示第一级，依此类推）
     */
    private int getNodeLevel(Long nodeId) {
        int level = 1;
        Long currentId = nodeId;

        while (currentId != null && currentId != 0) {
            TDeptMember parent = baseMapper.selectById(currentId);
            if (parent == null) {
                break;
            }

            currentId = parent.getParentId();
            level++;
        }

        return level;
    }

    /**
     * 修改部门成员
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(TDeptMemberBo bo) {
        TDeptMember update = BeanCopyUtils.copy(bo, TDeptMember.class);
        return baseMapper.updateById(update) > 0;
    }

    @Override
    public int sort(List<TDeptMemberVo> bos) {
        List<TDeptMember> list = new ArrayList<>();
        flatTree(bos, list, 0L);
        return baseMapper.updateBatchById(list) ? list.size() : 0;
    }

    //平铺树结构
    private void flatTree(List<TDeptMemberVo> bos, List<TDeptMember> list, Long parentId) {
        for (TDeptMemberVo bo : bos) {
            TDeptMember member = BeanCopyUtils.copy(bo, TDeptMember.class);
            member.setParentId(parentId);
            list.add(member);
            if (bo.getChildren() != null && !bo.getChildren().isEmpty()) {
                flatTree(bo.getChildren(), list, member.getId());
            }

        }
    }

    /**
     * 删除部门成员
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteWithValidByIds(Collection<Long> ids) {
        List<Long> allDeleteIds = new ArrayList<>(ids);
        for (Long id : ids) {
            collectChildrenIds(id, allDeleteIds);
        }
        return baseMapper.deleteBatchIds(allDeleteIds) > 0;
    }

    /**
     * 递归收集所有子节点ID
     */
    private void collectChildrenIds(Long parentId, List<Long> ids) {
        LambdaQueryWrapper<TDeptMember> lqw = Wrappers.lambdaQuery();
        lqw.eq(TDeptMember::getParentId, parentId);
        List<TDeptMember> children = baseMapper.selectList(lqw);

        if (children != null && !children.isEmpty()) {
            for (TDeptMember child : children) {
                ids.add(child.getId());
                collectChildrenIds(child.getId(), ids);
            }
        }
    }
}
