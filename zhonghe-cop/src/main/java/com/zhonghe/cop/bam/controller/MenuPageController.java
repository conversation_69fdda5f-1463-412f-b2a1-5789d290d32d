package com.zhonghe.cop.bam.controller;

import com.zhonghe.common.annotation.RepeatSubmit;
import com.zhonghe.common.core.controller.BaseController;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.cop.bam.domain.MenuPage;
import com.zhonghe.cop.bam.service.IMenuPageService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;

/**
 * 用户菜单保存列
 *
 * <AUTHOR>
 * @date 2023-10-27
 */
@Validated
@Api(value = "用户菜单保存列", tags = {"用户菜单保存列"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/menuPage")
public class MenuPageController extends BaseController {

    private final IMenuPageService iMenuPageService;

    /**
     * 获取用户菜单保存列
     */
    @ApiOperation("获取用户菜单保存列")
    @GetMapping("/getColumn")
    public R<MenuPage> getColumn(@NotNull(message = "主键不能为空")
                                 @RequestParam("menuPageName") String menuPageName) {
        return R.ok(iMenuPageService.getColumn(menuPageName));
    }

    /**
     * 保存用户菜单保存列
     */
    @ApiOperation("保存用户菜单保存列")
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@RequestBody MenuPage menuPage) {
        return toAjax(iMenuPageService.saveMenuPage(menuPage) ? 1 : 0);
    }

}
