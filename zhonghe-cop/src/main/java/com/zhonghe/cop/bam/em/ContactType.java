package com.zhonghe.cop.bam.em;

import lombok.Getter;

/**
 * @Author: lpg
 * @Date: 2023/12/10/11:49
 * @Description:
 */
@Getter
public enum ContactType {
    /**
     * 企业群体
     * 领导小组办公室
     * 成员部门负责人
     * 成员部门联络员
     * 镇领导班子
     */
    COP(0, "企业群体"),
    LEADER(1, "领导小组办公室"),
    MEMBER_LEADER(2, "成员部门负责人"),
    MEMBER_CONTACT(3, "成员部门联络员"),
    TOWN_LEADER(4, "镇领导班子");

    private final Integer code;
    private final String name;

    ContactType(Integer code, String name) {
        this.code = code;
        this.name = name;
    }
}
