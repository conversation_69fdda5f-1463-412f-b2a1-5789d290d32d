package com.zhonghe.cop.bam.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.HashMap;
import java.util.Map;


/**
 * 模板视图对象 t_tem_template
 *
 * <AUTHOR>
 * @date 2023-12-10
 */
@Data
@ApiModel("模板视图对象")
@ExcelIgnoreUnannotated
public class TTemTemplateVo implements TransPojo {

    private static final long serialVersionUID = 1L;
    private Map<String, Object> transMap = new HashMap<>();

    /**
     * 模板ID
     */
    @ExcelProperty(value = "模板ID")
    @ApiModelProperty("模板ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long templateId;

    /**
     * 说明
     */
    @ExcelProperty(value = "说明")
    @ApiModelProperty("说明")
    private String note;

    /**
     * 模板类型ID
     */
    @ExcelProperty(value = "模板类型ID")
    @ApiModelProperty("模板类型ID")
    @Trans(type = TransType.DICTIONARY, key = "cop_template_type", ref = "templateTypeName")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long templateTypeId;

    /**
     * 模板类型名称
     */
    @ApiModelProperty("模板类型名称")
    private String templateTypeName;

    /**
     * 模板缩写
     */
    @ExcelProperty(value = "模板缩写")
    @ApiModelProperty("模板缩写")
    private String templateShort;

    /**
     * 模板内容
     */
    @ExcelProperty(value = "模板内容")
    @ApiModelProperty("模板内容")
    private String templateContent;

    /**
     * 模板排序
     */
    @ExcelProperty(value = "模板排序")
    @ApiModelProperty("模板排序")
    private Integer templateOrder;


}
