package com.zhonghe.cop.bam.listener;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.metadata.data.ReadCellData;
import com.alibaba.fastjson.JSON;
import com.zhonghe.common.core.domain.entity.SysDictData;
import com.zhonghe.common.excel.ExcelListener;
import com.zhonghe.common.excel.ExcelResult;
import com.zhonghe.common.exception.ServiceException;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.common.utils.spring.SpringUtils;
import com.zhonghe.cop.bam.domain.excel.EditTCopInformationExcel;
import com.zhonghe.cop.bam.mapper.TCopInformationMapper;
import com.zhonghe.cop.bam.mapper.TDicOrgMapper;
import com.zhonghe.system.service.ISysDictDataService;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @Author: lpg
 * @Date: 2023/12/27/16:36
 * @Description: 企业备案导入监听
 */
@Slf4j
public class EditTCopInformationImportListener extends AnalysisEventListener<EditTCopInformationExcel> implements ExcelListener<EditTCopInformationExcel> {

    //校验表头
    private static final String[] HEADERS = {
        "序号",
        "统一社会信用代码/注册号",
        "主体名称",
        "住所/经营场所/驻在场所",
        "主体类型",
        "法定代表人/负责人/经营者",
        "主体联系电话",
        "联络员",
        "联络员联系电话",
        "法定代表人/负责人/经营者联系电话"
    };
    private static final String HTML = "<span style='color:red'>(%s)</span>";
    private final TCopInformationMapper tCopInformationMapper;
    private final ISysDictDataService iSysDictDataService;
    private final TDicOrgMapper tDicOrgMapper;
    /**
     * 字典数据
     */
    List<SysDictData> sysDictData;
    private ExcelResult<EditTCopInformationExcel> excelResult;
    /**
     * 成功解析的数据
     */
    private List<EditTCopInformationExcel> successList = new ArrayList<>();
    /**
     * 失败解析的数据
     */
    private List<EditTCopInformationExcel> errorList = new ArrayList<>();

    public EditTCopInformationImportListener(ExcelResult<EditTCopInformationExcel> excelResult) {
        tCopInformationMapper = SpringUtils.getBean(TCopInformationMapper.class);
        iSysDictDataService = SpringUtils.getBean(ISysDictDataService.class);
        tDicOrgMapper = SpringUtils.getBean(TDicOrgMapper.class);
        sysDictData = iSysDictDataService.selectListByDictCode("cop_property");
        this.excelResult = excelResult;
    }

    @Override
    public void invokeHead(Map<Integer, ReadCellData<?>> headMap, AnalysisContext context) {
        log.info("解析到一条头数据:{}", JSON.toJSONString(headMap));
        // 校验表头
        if (CollectionUtil.isEmpty(headMap)) {
            throw new ServiceException("导入的模板不符合,请检查后重新导入!");
        }
        for (int i = 0; i < HEADERS.length; i++) {
            if (!HEADERS[i].equals(headMap.get(i).getStringValue())) {
                throw new ServiceException("导入的模板不符合,请检查后重新导入!");
            }
        }
    }

    @Override
    public void invoke(EditTCopInformationExcel tCopInformationExcel, AnalysisContext analysisContext) {
        log.info("修改企业备案导入监听...");
        /**
         * 企业名称验证
         */
        if (StringUtils.isBlank(tCopInformationExcel.getCopName())) {
            //企业名称不能为空
            tCopInformationExcel.setCopName(String.format(HTML, "主体名称不能为空"));
            errorList.add(tCopInformationExcel);
            return;
        }
        //企业机构代码不能为空
        if (StringUtils.isBlank(tCopInformationExcel.getCorporateCode())) {
            //企业性质不能为空
            tCopInformationExcel.setCorporateCode(String.format(HTML, "统一社会信用代码/注册号不能为空"));
            errorList.add(tCopInformationExcel);
            return;
        }
        //判断企业性质是否正确
        if (StringUtils.isBlank(tCopInformationExcel.getCopProperty())) {
            //企业性质不能为空
            tCopInformationExcel.setCopProperty(String.format(HTML, "企业性质不能为空"));
            errorList.add(tCopInformationExcel);
            return;
        }
        //检验企业名称是否重复
        if (successList.stream().anyMatch(tCopInformationExcel1 -> tCopInformationExcel1.getCopName().equals(tCopInformationExcel.getCopName()))) {
            tCopInformationExcel.setCopName(String.format(HTML, "主体名称重复"));
            errorList.add(tCopInformationExcel);
            return;
        }
        //检验企业代码是否重复
        if (successList.stream().anyMatch(tCopInformationExcel1 -> tCopInformationExcel1.getCorporateCode().equals(tCopInformationExcel.getCorporateCode()))) {
            tCopInformationExcel.setCorporateCode(String.format(HTML, "统一社会信用代码/注册号重复"));
            errorList.add(tCopInformationExcel);
            return;
        }
        String copProperty = tCopInformationExcel.getCopProperty();

        //从字典数据中过滤出来，有的话取出一条数据
        /*SysDictData sysDictData1 = sysDictData.stream().filter(a -> a.getDictLabel().equals(copProperty)).findFirst().orElse(null);
        if (sysDictData1 == null) {
            //企业性质不正确
            tCopInformationExcel.setCopProperty(tCopInformationExcel.getCopProperty() + String.format(HTML, "企业性质不正确"));
            errorList.add(tCopInformationExcel);
            return;
        }*/

        //将成功解析的数据放入到集合中
        successList.add(tCopInformationExcel);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        log.info("企业备案导入完成");
    }

    @Override
    public ExcelResult<EditTCopInformationExcel> getExcelResult() {
        excelResult.getList().addAll(successList);
        excelResult.getErrorList().addAll(errorList);
        return excelResult;
    }
}
