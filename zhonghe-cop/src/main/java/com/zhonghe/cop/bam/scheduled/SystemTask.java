package com.zhonghe.cop.bam.scheduled;

import com.zhonghe.cop.bam.service.ISysSendSmsService;
import com.zhonghe.cop.bam.service.ITPerPerformanceService;
import com.zhonghe.cop.plan.service.ITPlaInfoService;
import com.zhonghe.cop.rep.service.ITRepGarrepService;
import com.zhonghe.cop.rep.service.ITRepReportService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * @Author: lpg
 * @Date: 2023/12/25/10:10
 * @Description:
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SystemTask {
    private final ITPerPerformanceService perPerformanceService;
    private final ITRepGarrepService iTRepGarrepService;
    private final ITRepReportService itRepReportService;
    private final ITPlaInfoService plaInfoService;
    private final ISysSendSmsService iSysSendSmsService;

    // 每月一号执行
    @Scheduled(cron = "0 0 0 1 * ?")
    public void deptPerformanceTask() {
        //部门绩效考核任务(每月一号插入数据)
        perPerformanceService.deptPerformanceTask();
        //清除不在上个月的所有图层数据
        iTRepGarrepService.removeNotInBeforeMonthBigMapData();
        //连续2个月因欠薪而高危的企业进行预案
        plaInfoService.planTask();
    }

    /**
     * 汇总月报表短信发送（每月1号8点)
     */
    @Scheduled(cron = "0 0 8 1 * ?")
    public void sendMonthReport() {
        iSysSendSmsService.sendMonthReport();
    }

    /**
     * 发送未上报部门短信发送(每月26号8点)
     */
    @Scheduled(cron = "0 0 8 26 * ?")
    public void sendUnreportedDept() {
        iSysSendSmsService.sendUnreportedDept();
    }

    /**
     * 发送部门上报未处理短信提醒(每月5号8点)
     */
    @Scheduled(cron = "0 0 8 5 * ?")
    public void sendUnprocessedDept() {
        iSysSendSmsService.sendUnprocessedDept();
    }

}
