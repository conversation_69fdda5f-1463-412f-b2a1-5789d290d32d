package com.zhonghe.cop.bam.domain.bo;

import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;


/**
 * 通讯录业务对象 t_mes_contact
 *
 * <AUTHOR>
 * @date 2023-12-08
 */
@Data
@ApiModel("通讯录业务对象")
public class AddCopContactBo implements Serializable {

    private static final long serialVersionUID = -4434230245539142758L;
    /**
     * 通讯录ID
     */
    @ApiModelProperty(value = "通讯录ID", required = true)
    @NotNull(message = "通讯录ID不能为空", groups = {EditGroup.class})
    private Long mesContactId;

    /**
     * 企业ID
     */
    @ApiModelProperty(value = "企业ID", required = true)
    @NotNull(message = "企业ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long copId;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Length(max = 2000, message = "备注长度不能超过2000个字符", groups = {AddGroup.class, EditGroup.class})
    private String note;

    /**
     * 联系人描述
     */
    @ApiModelProperty(value = "联系人描述")
    @Length(max = 2000, message = "联系人描述长度不能超过2000个字符", groups = {AddGroup.class, EditGroup.class})
    private String contactDescription;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名", required = true)
    @NotBlank(message = "姓名不能为空", groups = {AddGroup.class, EditGroup.class})
    private String contactName;

    /**
     * 电话
     */
    @ApiModelProperty(value = "电话", required = true)
    @NotBlank(message = "电话不能为空", groups = {AddGroup.class, EditGroup.class})
    private String contactNumber;

    /**
     * 联系人类型ID
     */
    @ApiModelProperty(value = "联系人类型", required = true)
    @NotNull(message = "联系人类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long contactTypeId;


}
