package com.zhonghe.cop.bam.mapper;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhonghe.common.core.mapper.BaseMapperPlus;
import com.zhonghe.cop.bam.domain.TCopInformation;
import com.zhonghe.cop.bam.domain.vo.TCopInformationVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 企业信息Mapper接口
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
public interface TCopInformationMapper extends BaseMapperPlus<TCopInformationMapper, TCopInformation, TCopInformationVo> {
    /**
     * 查询企业信息列表
     *
     * @param build
     * @param lqw
     * @return
     */
    Page<TCopInformationVo> selectCopList(Page<TCopInformation> build, @Param(Constants.WRAPPER) Wrapper<TCopInformation> lqw);

    /**
     * 查询企业信息列表
     *
     * @param lqw
     * @return
     */
    List<TCopInformationVo> selectCopList(@Param(Constants.WRAPPER) Wrapper<TCopInformation> lqw);

    /**
     * 查询企业信息
     *
     * @param copId
     * @return
     */
    TCopInformationVo selectVoById(@Param("copId") Long copId);

    List<TCopInformation> selectListDel();
}
