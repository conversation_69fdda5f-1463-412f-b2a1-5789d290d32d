package com.zhonghe.cop.bam.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.zhonghe.common.annotation.ExcelDictFormat;
import com.zhonghe.common.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 劳务人员视图对象 t_cop_labor
 *
 * <AUTHOR>
 * @date 2023-12-21
 */
@Data
@ApiModel("劳务人员视图对象")
@ExcelIgnoreUnannotated
public class TCopLaborVo implements java.io.Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 劳务人员ID
     */
    @ExcelProperty(value = "劳务人员ID")
    @ApiModelProperty("劳务人员ID")
    private Long copLaborId;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    @ApiModelProperty("备注")
    private String note;

    /**
     * 企业ID（默认0，对应T_COP_Information主键）
     */
    @ExcelProperty(value = "企业ID", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "默=认0，对应T_COP_Information主键")
    @ApiModelProperty("企业ID（默认0，对应T_COP_Information主键）")
    private Long copId;

    /**
     * 劳务公司
     */
    @ExcelProperty(value = "劳务公司")
    @ApiModelProperty("劳务公司")
    @JsonProperty("labourCop")
    private String labourCop;

    /**
     * 姓名
     */
    @ExcelProperty(value = "姓名")
    @ApiModelProperty("姓名")
    @JsonProperty("lName")
    private String lName;

    /**
     * 性别
     */
    @ExcelProperty(value = "性别")
    @ApiModelProperty("性别")
    @JsonProperty("lSex")
    private String lSex;

    /**
     * 身份证
     */
    @ExcelProperty(value = "身份证")
    @ApiModelProperty("身份证")
    @JsonProperty("idCard")
    private String idCard;

    /**
     * 部门
     */
    @ExcelProperty(value = "部门")
    @ApiModelProperty("部门")
    @JsonProperty("lDept")
    private String lDept;

    /**
     * 职务
     */
    @ExcelProperty(value = "职务")
    @ApiModelProperty("职务")
    @JsonProperty("lJob")
    private String lJob;


}
