package com.zhonghe.cop.bam.mapper;

import com.zhonghe.common.core.mapper.BaseMapperPlus;
import com.zhonghe.cop.bam.domain.TDeptRole;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

/**
 * 部门职能数据层
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Mapper
public interface TDeptRoleMapper extends BaseMapperPlus<TDeptRoleMapper, TDeptRole, TDeptRole> {

    /**
     * 查询当前最大的排序号
     *
     * @return 最大排序号
     */
    @Select("SELECT MAX(sort_order) FROM cop.t_dept_role")
    Integer selectMaxSortOrder();

}
