package com.zhonghe.cop.bam.mapper;

import com.zhonghe.common.core.mapper.BaseMapperPlus;
import com.zhonghe.cop.bam.domain.YwDgsgajNewBzdzxx2;
import com.zhonghe.cop.bam.domain.vo.CopGeomVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 东莞市二标四实标准地址信息新表Mapper接口
 *
 * <AUTHOR>
 * @date 2023-12-21
 */
public interface YwDgsgajNewBzdzxx2Mapper extends BaseMapperPlus<YwDgsgajNewBzdzxx2, YwDgsgajNewBzdzxx2, YwDgsgajNewBzdzxx2> {

    /**
     * 查询附近的企业
     *
     * @param lon
     * @param lat
     * @return
     */
    @Select("SELECT  dzqc as cop_address,zxjd as lon,zxwd as lat\n" +
        "FROM public.yw_dgsgaj_new_bzdzxx2\n" +
        "ORDER BY POWER((zxjd - #{lon}), 2) + POWER((zxwd - #{lat}), 2)\n" +
        "LIMIT 1")
    List<CopGeomVo> getNearby(@Param("lon") Double lon, @Param("lat") Double lat);
}
