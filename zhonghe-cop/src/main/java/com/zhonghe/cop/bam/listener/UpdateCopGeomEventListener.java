package com.zhonghe.cop.bam.listener;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.cop.bam.domain.TCopInformation;
import com.zhonghe.cop.bam.em.IsCancel;
import com.zhonghe.cop.bam.em.IsLocation;
import com.zhonghe.cop.bam.event.UpdateCopGeomEvent;
import com.zhonghe.cop.bam.mapper.TCopInformationMapper;
import com.zhonghe.cop.bam.service.TICopInformationService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.postgis.jdbc.geometry.Point;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * @Author: lpg
 * @Date: 2024/01/30
 * @Description:
 */
@Slf4j
@Component
@AllArgsConstructor
public class UpdateCopGeomEventListener {

    private final TICopInformationService copInformationService;
    private final TCopInformationMapper copInformationMapper;

    /**
     * 更新未注销企业地理位置
     */
    @EventListener
    public void updateNoCancalCopGeom(UpdateCopGeomEvent event) {
        List<TCopInformation> tCopInformations = copInformationMapper.selectList(Wrappers.<TCopInformation>lambdaQuery()
            .isNull(TCopInformation::getGeom)
            .eq(TCopInformation::getIsCancel, IsCancel.NO.getCode()));

        // 限制处理数量，避免一次性处理过多数据
        List<TCopInformation> processData = tCopInformations.size() > 2000 ?
            tCopInformations.subList(0, 2000) : tCopInformations;

        List<CompletableFuture<TCopInformation>> futures = new ArrayList<>();
        int totalCount = processData.size();
        int processedCount = 0;

        log.info("开始处理企业地理位置信息，总数量：{}", totalCount);

        for (TCopInformation tCopInformation : processData) {
            final int currentIndex = ++processedCount;
            if (StringUtils.isNotBlank(tCopInformation.getCopName())) {
                // 创建异步任务，返回处理结果
                CompletableFuture<TCopInformation> future = CompletableFuture.supplyAsync(() -> {
                    try {
                        Map<String, Object> addressInfo = copInformationService.getAddressInfo(tCopInformation.getCopName());
                        if (addressInfo != null && addressInfo.get("lon") != null && addressInfo.get("lat") != null) {
                            // 创建新对象避免并发修改原对象
                            TCopInformation updateInfo = new TCopInformation();
                            updateInfo.setCopId(tCopInformation.getCopId());
                            updateInfo.setGeom(new Point(Double.parseDouble(addressInfo.get("lon").toString()),
                                Double.parseDouble(addressInfo.get("lat").toString())));
                            updateInfo.setIsLocation(IsLocation.YES.getCode());
                            if (addressInfo.get("address") != null) {
                                updateInfo.setCopAddress(addressInfo.get("address").toString());
                            }

                            // 显示处理进度
                            double progress = (double) currentIndex / totalCount * 100;
                            if (currentIndex % 100 == 0 || currentIndex == totalCount) {
                                log.info("处理进度：{}/{} ({:.2f}%) - 企业：{}",
                                    currentIndex, totalCount, progress, tCopInformation.getCopName());
                            }

                            return updateInfo;
                        } else {
                            // 显示处理进度（失败的情况）
                            double progress = (double) currentIndex / totalCount * 100;
                            if (currentIndex % 100 == 0 || currentIndex == totalCount) {
                                log.info("处理进度：{}/{} ({:.2f}%) - 企业：{} (未获取到地址信息)",
                                    currentIndex, totalCount, progress, tCopInformation.getCopName());
                            }
                        }
                    } catch (Exception e) {
                        log.error("更新未注销企业地理位置失败，企业名称：{} - 进度：{}/{}",
                            tCopInformation.getCopName(), currentIndex, totalCount, e);
                    }
                    return null;
                });
                futures.add(future);
            } else {
                // 企业名称为空的情况也要计入进度
                double progress = (double) currentIndex / totalCount * 100;
                if (currentIndex % 100 == 0 || currentIndex == totalCount) {
                    log.info("处理进度：{}/{} ({:.2f}%) - 企业名称为空，跳过处理",
                        currentIndex, totalCount, progress);
                }
            }
        }

        log.info("所有异步任务已提交，等待执行完成...");

        // 等待所有异步任务完成并收集结果
        List<TCopInformation> updates = futures.stream()
            .map(CompletableFuture::join)
            .filter(Objects::nonNull)
            .collect(Collectors.toList());

        // 批量更新
        if (!updates.isEmpty()) {
            log.info("开始批量更新企业地理位置信息，成功处理数量：{}/{}", updates.size(), totalCount);
            copInformationMapper.updateBatchById(updates);
            log.info("批量更新企业地理位置信息完成！成功更新：{}，总处理：{}，成功率：{:.2f}%",
                updates.size(), totalCount, (double) updates.size() / totalCount * 100);
        } else {
            log.warn("没有成功处理的企业地理位置信息，跳过批量更新");
        }
    }

}
