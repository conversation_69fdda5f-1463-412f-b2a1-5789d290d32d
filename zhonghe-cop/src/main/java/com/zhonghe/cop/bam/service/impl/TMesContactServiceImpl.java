package com.zhonghe.cop.bam.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.helper.IdGeneratorHelper;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.cop.bam.domain.TMesContact;
import com.zhonghe.cop.bam.domain.bo.TMesContactBo;
import com.zhonghe.cop.bam.domain.vo.TMesContactVo;
import com.zhonghe.cop.bam.em.ContactClass;
import com.zhonghe.cop.bam.em.ContactType;
import com.zhonghe.cop.bam.mapper.TMesContactMapper;
import com.zhonghe.cop.bam.service.ITMesContactService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * 通讯录，添加用户和企业时要往这里添加数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-08
 */
@RequiredArgsConstructor
@Service
public class TMesContactServiceImpl implements ITMesContactService {

    private final TMesContactMapper baseMapper;

    /**
     * 查询通讯录，添加用户和企业时要往这里添加数据
     */
    @Override
    public TMesContactVo queryById(Long mesContactId) {
        return baseMapper.selectVoById(mesContactId);
    }

    /**
     * 查询通讯录，添加用户和企业时要往这里添加数据列表
     */
    @Override
    public TableDataInfo<TMesContactVo> queryPageList(TMesContactBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<TMesContact> lqw = buildQueryWrapper(bo);
        Page<TMesContactVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询通讯录，添加用户和企业时要往这里添加数据列表
     */
    @Override
    public List<TMesContactVo> queryList(TMesContactBo bo) {
        LambdaQueryWrapper<TMesContact> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<TMesContact> buildQueryWrapper(TMesContactBo bo) {
        LambdaQueryWrapper<TMesContact> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getMesContactId() != null, TMesContact::getMesContactId, bo.getMesContactId());
        lqw.eq(StringUtils.isNotBlank(bo.getNote()), TMesContact::getNote, bo.getNote());
        lqw.eq(StringUtils.isNotBlank(bo.getContactDescription()), TMesContact::getContactDescription, bo.getContactDescription());
        lqw.like(StringUtils.isNotBlank(bo.getContactName()), TMesContact::getContactName, bo.getContactName());
        lqw.like(StringUtils.isNotBlank(bo.getContactNumber()), TMesContact::getContactNumber, bo.getContactNumber());
        lqw.eq(bo.getContactTypeId() != null, TMesContact::getContactTypeId, bo.getContactTypeId());
        lqw.eq(bo.getContactType() != null, TMesContact::getContactType, bo.getContactType());
        lqw.orderByDesc(TMesContact::getCreateTime);
        return lqw;
    }

    /**
     * 新增通讯录，添加用户和企业时要往这里添加数据
     */
    @Override
    public Boolean insertByBo(TMesContactBo bo) {
        TMesContact add = BeanUtil.toBean(bo, TMesContact.class);
        if (ContactType.COP.getCode().equals(Integer.parseInt(String.valueOf(add.getContactTypeId())))) {
            add.setContactType(ContactClass.COP.getCode());
        }
        add.setContactType(ContactClass.USER.getCode());
        //新增时生成联系人ID
        Long nextId = IdGeneratorHelper.next();

        add.setMesContactId(nextId);
        add.setContactId(nextId);
        add.setNote("手动新增");
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setMesContactId(add.getMesContactId());
        }
        return flag;
    }

    /**
     * 修改通讯录，添加用户和企业时要往这里添加数据
     */
    @Override
    public Boolean updateByBo(TMesContactBo bo) {
        TMesContact update = BeanUtil.toBean(bo, TMesContact.class);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(TMesContact entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public TableDataInfo<TMesContactVo> queryPageListByTypeId(Long contactTypeId, String contactName, PageQuery pageQuery) {
        IPage<TMesContactVo> page = baseMapper.selectVoPage(pageQuery.build(), Wrappers.lambdaQuery(TMesContact.class)
            .isNotNull(TMesContact::getContactNumber)
            .like(StringUtils.isNotBlank(contactName), TMesContact::getContactName, contactName)
            .eq(TMesContact::getContactTypeId, contactTypeId));
        return TableDataInfo.build(page);
    }

    @Override
    public List<TMesContactVo> queryListByTypeId(Long contactTypeId) {
        return baseMapper.selectVoList(Wrappers.lambdaQuery(TMesContact.class)
            .isNotNull(TMesContact::getContactNumber)
            .eq(TMesContact::getContactTypeId, contactTypeId));
    }

    /**
     * 批量删除通讯录，添加用户和企业时要往这里添加数据
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
