package com.zhonghe.cop.bam.domain.bo;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;


/**
 * 劳务人员业务对象 t_cop_labor
 *
 * <AUTHOR>
 * @date 2023-12-21
 */
@Data
@ApiModel("劳务人员业务对象")
public class TCopLaborBo implements Serializable {

    private static final long serialVersionUID = 7401909440324709591L;
    /**
     * 劳务人员ID
     */
    @ApiModelProperty(value = "劳务人员ID", required = true)
    @NotNull(message = "劳务人员ID不能为空", groups = {EditGroup.class})
    private Long copLaborId;

    /**
     * 企业ID（默认0，对应T_COP_Information主键）
     */
    @ApiModelProperty(value = "企业ID", required = true)
    @NotNull(message = "企业ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long copId;

    /**
     * 劳务公司
     */
    @ApiModelProperty(value = "劳务公司", required = true)
    @NotBlank(message = "劳务公司不能为空", groups = {AddGroup.class, EditGroup.class})
    @JsonProperty("labourCop")
    private String labourCop;

    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名", required = true)
    @NotBlank(message = "姓名不能为空", groups = {AddGroup.class, EditGroup.class})
    @JsonProperty("lName")
    private String lName;

    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    @JsonProperty("lSex")
    private String lSex;

    /**
     * 身份证
     */
    @ApiModelProperty(value = "身份证")
    @JsonProperty("idCard")
    private String idCard;

    /**
     * 部门
     */
    @ApiModelProperty(value = "部门")
    @JsonProperty("lDept")
    private String lDept;

    /**
     * 职务
     */
    @ApiModelProperty(value = "职务")
    @JsonProperty("lJob")
    private String lJob;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    @Length(max = 255, message = "备注长度不能超过255个字符", groups = {AddGroup.class, EditGroup.class})
    private String note;


}
