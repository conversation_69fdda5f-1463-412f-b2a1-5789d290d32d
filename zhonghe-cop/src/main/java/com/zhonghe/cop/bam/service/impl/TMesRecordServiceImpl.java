package com.zhonghe.cop.bam.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhonghe.common.constant.Constants;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.common.core.domain.entity.SysUser;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.exception.ServiceException;
import com.zhonghe.common.helper.LoginHelper;
import com.zhonghe.common.utils.DateUtils;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.cop.bam.constants.CopConstants;
import com.zhonghe.cop.bam.domain.TMesContact;
import com.zhonghe.cop.bam.domain.TMesRecord;
import com.zhonghe.cop.bam.domain.bo.TMesRecordBo;
import com.zhonghe.cop.bam.domain.query.TMesRecordQuery;
import com.zhonghe.cop.bam.domain.vo.TMesContactVo;
import com.zhonghe.cop.bam.domain.vo.TMesRecordVo;
import com.zhonghe.cop.bam.em.MesSendStatus;
import com.zhonghe.cop.bam.em.MesSendType;
import com.zhonghe.cop.bam.mapper.TMesContactMapper;
import com.zhonghe.cop.bam.mapper.TMesRecordMapper;
import com.zhonghe.cop.bam.service.ITMesContactService;
import com.zhonghe.cop.bam.service.ITMesRecordService;
import com.zhonghe.cop.bam.util.SendMessage;
import com.zhonghe.cop.rep.service.ITRepGarrepService;
import com.zhonghe.system.mapper.SysUserMapper;
import com.zhonghe.system.service.ISysConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 发送记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-11
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class TMesRecordServiceImpl implements ITMesRecordService {

    private final TMesRecordMapper baseMapper;
    private final ITMesContactService iTMesContactService;
    private final ITRepGarrepService itRepGarrepService;
    private final TMesContactMapper tMesContactMapper;
    private final SysUserMapper sysUserMapper;
    private final ISysConfigService iSysConfigService;

    /**
     * 查询发送记录
     */
    @Override
    public TMesRecordVo queryById(Long mesRecordId) {
        return baseMapper.selectVoById(mesRecordId);
    }

    /**
     * 查询发送记录列表
     */
    @Override
    public TableDataInfo<TMesRecordVo> queryPageList(TMesRecordQuery bo, PageQuery pageQuery) {
        QueryWrapper<TMesRecord> lqw = buildQueryWrapper(bo);
        Page<TMesRecordVo> result = baseMapper.selectMesRecordList(pageQuery.build(), lqw);

        for (TMesRecordVo record : result.getRecords()) {
            record.setUserIds(Convert.toStrArray(record.getUserIdArr()));

            //查询批次号是否存在发送失败的记录
            List<TMesRecord> tMesRecords = baseMapper.selectList(Wrappers.<TMesRecord>lambdaQuery()
                .eq(TMesRecord::getBatchNo, record.getBatchNo()));
            //判断已发送、未发送、发送失败、部分失败
            boolean allSuccess = tMesRecords.stream().allMatch(a -> MesSendStatus.YES.getCode().equals(a.getSendStatus()));
            if (allSuccess) {
                record.setSendStatus(MesSendStatus.YES.getCode());
            } else {
                record.setSendStatus(MesSendStatus.NO.getCode());
            }
            boolean anyFail = tMesRecords.stream().anyMatch(a -> MesSendStatus.FAIL.getCode().equals(a.getSendStatus()));
            if (anyFail) {
                record.setSendStatus(MesSendStatus.PART_FAIL.getCode());
            }
            boolean allFail = tMesRecords.stream().allMatch(a -> MesSendStatus.FAIL.getCode().equals(a.getSendStatus()));
            if (allFail) {
                record.setSendStatus(MesSendStatus.FAIL.getCode());
            }


        }
        return TableDataInfo.build(result);
    }

    /**
     * 查询发送记录列表
     */
    @Override
    public List<TMesRecordVo> queryList(TMesRecordQuery bo) {
        QueryWrapper<TMesRecord> qw = buildQueryWrapper(bo);
        return baseMapper.selectMesRecordList(qw);
    }

    private QueryWrapper<TMesRecord> buildQueryWrapper(TMesRecordQuery bo) {
        QueryWrapper<TMesRecord> qw = Wrappers.query();
        qw.like(StringUtils.isNotBlank(bo.getSendObject()), "t1.send_object", bo.getSendObject());
        qw.eq(bo.getSendStatus() != null, "tmr.send_status", bo.getSendStatus());
        qw.eq("tmr.del_flag", Constants.DEL_FLAG_NORMAL);
        qw.orderByDesc("tmr.send_date");
        return qw;
    }

    /**
     * 新增发送记录
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(TMesRecordBo bo) {
        List<TMesRecord> tMesRecords = new ArrayList<>();
        String batchNo = UUID.randomUUID().toString();
        if (bo.getBatchNo() != null) {
            batchNo = bo.getBatchNo();
        }
        Map<Long, TMesContactVo> contactVoMap = iTMesContactService.queryListByTypeId(bo.getContactTypeId()).stream()
            .collect(Collectors.toMap(TMesContactVo::getContactId, Function.identity()));
        if (bo.getUserIds() != null) {
            for (Long userId : bo.getUserIds()) {
                TMesRecord add = BeanUtil.toBean(bo, TMesRecord.class);
                //设置用户ID
                add.setUserId(userId);
                //设置手动发送
                add.setSendType(MesSendType.MANUAL.getCode());
                //设置批次号
                add.setBatchNo(batchNo);
                //设置未发送
                add.setSendStatus(MesSendStatus.NO.getCode());
                add.setDeptId(LoginHelper.getDeptId());
                TMesContactVo tMesContactVo = contactVoMap.get(userId);
                if (ObjectUtil.isNotNull(tMesContactVo)) {
                    //设置发送对象
                    add.setSendObject(tMesContactVo.getContactName());
                    //设置发送手机号
                    add.setPhone(tMesContactVo.getContactNumber());
                }

                tMesRecords.add(add);
            }
        }
        return baseMapper.insertBatch(tMesRecords);
    }

    /**
     * 修改发送记录
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(TMesRecordBo bo) {
        deleteWithValidByBatchNos(Convert.toList(String.class, bo.getBatchNo()), true);
        return insertByBo(bo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean reissue() {
        //发送每月一号汇总短信
        String config = iSysConfigService.selectConfigByKey(CopConstants.IS_SEND_REP_GARREP);
        if (!Convert.toBool(config)) {
            throw new ServiceException("汇总月报表短信发送（每月1号)未开启");
        }
        log.info("汇总月报表短信发送（每月1号)开始发送");

        String message = itRepGarrepService.buildFirstMonthMes();

        List<SysUser> sysUsers = sysUserMapper.selectList(Wrappers.<SysUser>lambdaQuery().isNotNull(SysUser::getPhonenumber));

        sendUserMessage(sysUsers, message);

        return true;
    }

    @Override
    public void sendUserMessage(List<SysUser> sysUsers, String message) {
        String batchNo = UUID.randomUUID().toString();


        List<TMesRecord> tMesRecords = new LinkedList<>();

        sysUsers.stream().filter(a -> StringUtils.isNotBlank(a.getPhonenumber())).forEach(user -> {
            TMesRecord tMesRecord = new TMesRecord();
            tMesRecord.setSendObject(user.getNickName());
            tMesRecord.setPhone(user.getPhonenumber());
            tMesRecord.setUserId(user.getUserId());
            //TODO内容
            tMesRecord.setContent(message);
            tMesRecord.setSendType(MesSendType.SYSTEM.getCode());
            tMesRecord.setSendStatus(MesSendStatus.NO.getCode());
            tMesRecord.setBatchNo(batchNo);

            tMesRecords.add(tMesRecord);
        });
        baseMapper.insertBatch(tMesRecords);
        //发送短信
        sendSMS(tMesRecords, DateUtils.getNowDate(), new LinkedHashMap<>());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<String> sendMes(String batchNo) {
        List<TMesRecord> tMesRecords = baseMapper.selectList(Wrappers.<TMesRecord>lambdaQuery().eq(TMesRecord::getBatchNo, batchNo));

        Map<String, String> failMap = new LinkedHashMap<>();

        Date date = DateUtils.getNowDate();
        //发送短信
        sendSMS(tMesRecords, date, failMap);

        String phones = tMesRecords.stream().map(TMesRecord::getPhone).collect(Collectors.joining(","));
        if (!failMap.isEmpty()) {
            return R.fail(failMap.toString());
        } else {
            return R.ok(phones + " 信息发送成功!");
        }
    }

    private void sendSMS(List<TMesRecord> tMesRecords, Date date, Map<String, String> failMap) {
        for (TMesRecord tMesRecord : tMesRecords) {
            if (!Objects.equals(tMesRecord.getSendStatus(), MesSendStatus.YES.getCode())) {
                tMesRecord.setSendStatus(MesSendStatus.YES.getCode());
                tMesRecord.setSendDate(date);
                try {
                    // TODO: 调用短信服务提供商的API发送短信
                    SendMessage.sendSms(tMesRecord.getPhone(), tMesRecord.getContent());
                    tMesRecord.setSendMessage("发送成功");
                } catch (Exception e) {
                    tMesRecord.setSendStatus(MesSendStatus.FAIL.getCode());
                    String errorMessage = "信息发送失败，失败原因：" + e.getMessage();
                    tMesRecord.setSendMessage(errorMessage);
                    failMap.put(tMesRecord.getPhone(), errorMessage);
                    //TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    //return R.fail(tMesRecord.getPhone() + " 信息发送失败，失败原因：" + e.getMessage());
                }
            }
        }
        baseMapper.updateBatchById(tMesRecords);
    }

    @Override
    public Boolean deleteWithValidByBatchNos(List<String> batchNos, boolean b) {
        for (String batchNo : batchNos) {
            TMesRecordVo tMesRecordVo = queryByBatchNo(batchNo);
            if (tMesRecordVo != null && MesSendStatus.YES.getCode().equals(tMesRecordVo.getSendStatus())) {
                throw new RuntimeException("短信已经发送！");
            }
        }
        return baseMapper.delete(Wrappers.<TMesRecord>lambdaQuery().in(TMesRecord::getBatchNo, batchNos)) > 0;
    }

    @Override
    public TMesRecordVo queryByBatchNo(String batchNo) {
        TMesRecordVo tMesRecordVo = baseMapper.selectVoByBatchNo(batchNo);
        if (tMesRecordVo != null) {
            String[] userIds = Convert.toStrArray(tMesRecordVo.getUserIdArr());
            tMesRecordVo.setUserIds(userIds);

            //查询已选择的联系人
            List<Long> contactIds = Arrays.stream(userIds).map(Long::parseLong).collect(Collectors.toList());
            List<TMesContactVo> tMesContacts = tMesContactMapper.selectVoList(Wrappers.<TMesContact>lambdaQuery().in(TMesContact::getContactId, contactIds));
            tMesRecordVo.setTMesContacts(tMesContacts);

            List<TMesRecordVo> tMesRecords = baseMapper.selectVoList(Wrappers.<TMesRecord>lambdaQuery().eq(TMesRecord::getBatchNo, batchNo).orderByDesc(TMesRecord::getCreateTime));
            tMesRecordVo.setTMesRecord(tMesRecords);
        }

        return tMesRecordVo;
    }

    /**
     * 批量删除发送记录
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
