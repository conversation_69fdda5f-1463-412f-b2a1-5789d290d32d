package com.zhonghe.cop.bam.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.cop.bam.domain.TTemTemplate;
import com.zhonghe.cop.bam.domain.bo.TTemTemplateBo;
import com.zhonghe.cop.bam.domain.vo.TTemTemplateVo;
import com.zhonghe.cop.bam.mapper.TTemTemplateMapper;
import com.zhonghe.cop.bam.service.ITTemTemplateService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 模板Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-10
 */
@RequiredArgsConstructor
@Service
public class TTemTemplateServiceImpl implements ITTemTemplateService {

    private final TTemTemplateMapper baseMapper;

    /**
     * 查询模板
     */
    @Override
    public TTemTemplateVo queryById(Long templateId) {
        return baseMapper.selectVoById(templateId);
    }

    /**
     * 查询模板列表
     */
    @Override
    public TableDataInfo<TTemTemplateVo> queryPageList(TTemTemplateBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<TTemTemplate> lqw = buildQueryWrapper(bo);
        Page<TTemTemplateVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询模板列表
     */
    @Override
    public List<TTemTemplateVo> queryList(TTemTemplateBo bo) {
        LambdaQueryWrapper<TTemTemplate> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<TTemTemplate> buildQueryWrapper(TTemTemplateBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<TTemTemplate> lqw = Wrappers.lambdaQuery();
        lqw.eq(StringUtils.isNotBlank(bo.getNote()), TTemTemplate::getNote, bo.getNote());
        lqw.eq(bo.getTemplateTypeId() != null, TTemTemplate::getTemplateTypeId, bo.getTemplateTypeId());
        lqw.eq(StringUtils.isNotBlank(bo.getTemplateShort()), TTemTemplate::getTemplateShort, bo.getTemplateShort());
        lqw.eq(StringUtils.isNotBlank(bo.getTemplateContent()), TTemTemplate::getTemplateContent, bo.getTemplateContent());
        lqw.eq(bo.getTemplateOrder() != null, TTemTemplate::getTemplateOrder, bo.getTemplateOrder());
        //先按时间排序，再按排序号排序
        lqw.orderByDesc(TTemTemplate::getCreateTime).orderByAsc(TTemTemplate::getTemplateOrder);
        return lqw;
    }

    /**
     * 新增模板
     */
    @Override
    public Boolean insertByBo(TTemTemplateBo bo) {
        TTemTemplate add = BeanUtil.toBean(bo, TTemTemplate.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setTemplateId(add.getTemplateId());
        }
        return flag;
    }

    /**
     * 修改模板
     */
    @Override
    public Boolean updateByBo(TTemTemplateBo bo) {
        TTemTemplate update = BeanUtil.toBean(bo, TTemTemplate.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(TTemTemplate entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public List<TTemTemplateVo> queryByTypeId(Long templateTypeId) {
        return baseMapper.selectVoList(Wrappers.lambdaQuery(TTemTemplate.class).eq(TTemTemplate::getTemplateTypeId, templateTypeId)
            .orderByAsc(TTemTemplate::getTemplateOrder));
    }

    /**
     * 批量删除模板
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
