package com.zhonghe.cop.bam.domain.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.zhonghe.system.mappedtypes.GeometryTypeHandler;
import lombok.Data;
import net.postgis.jdbc.geometry.Geometry;

/**
 * @Author: lpg
 * @Date: 2024/01/10
 * @Description: 商事主体
 */
@Data
public class SsztDto implements java.io.Serializable {
    private static final long serialVersionUID = -2729867564447781428L;

    /**
     * 企业ID
     */
    private Long enterpriseId;

    /**
     * 企业名称
     */
    private String name;

    /**
     * 企业类型
     */
    private String enterpriseType;

    /**
     * 企业地址
     */
    private String address;

    /**
     * 法人
     */
    private String legalPerson;

    /**
     * 企业电话
     */
    private String phone;

    /**
     * 地理位置
     */
    @TableField(typeHandler = GeometryTypeHandler.class)
    private Geometry geom;
}
