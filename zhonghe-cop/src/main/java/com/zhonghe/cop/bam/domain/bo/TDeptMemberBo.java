package com.zhonghe.cop.bam.domain.bo;

import com.zhonghe.common.core.domain.BaseEntity;
import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 部门成员业务对象
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("部门成员业务对象")
public class TDeptMemberBo extends BaseEntity {

    private static final long serialVersionUID = -3544291214478091140L;
    /**
     * 主键，自增ID
     */
    @ApiModelProperty(value = "主键，自增ID", required = true)
    @NotNull(message = "主键不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 父节点ID
     */
    @ApiModelProperty(value = "父节点ID")
    private Long parentId;

    /**
     * 小组成员名称，可为人名或单位名
     */
    @ApiModelProperty(value = "小组成员名称", required = true)
    @NotBlank(message = "小组成员名称不能为空", groups = {AddGroup.class, EditGroup.class})
    private String name;

    /**
     * 排序字段
     */
    @ApiModelProperty(value = "排序字段")
    private Integer sortOrder;
}
