package com.zhonghe.cop.bam.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;


/**
 * 发送记录视图对象 t_mes_record
 *
 * <AUTHOR>
 * @date 2023-12-11
 */
@Data
@ApiModel("发送记录视图对象")
@ExcelIgnoreUnannotated
public class TMesRecordVo {

    private static final long serialVersionUID = 1L;

    /**
     * 发送批次号
     */
    @ExcelProperty(value = "发送批次号")
    @ApiModelProperty("发送批次号")
    private String batchNo;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    @ApiModelProperty("备注")
    private String note;

    /**
     * 发送号码
     */
    @ExcelProperty(value = "发送号码")
    @ApiModelProperty("发送号码")
    private String phone;

    /**
     * 发送内容
     */
    @ExcelProperty(value = "发送内容")
    @ApiModelProperty("发送内容")
    private String content;

    /**
     * 发送时间
     */
    @ExcelProperty(value = "发送时间")
    @ApiModelProperty("发送时间")
    private Date sendDate;

    /**
     * 发送状态 0-未发送 1-已发送 2-发送失败
     */
    @ExcelProperty(value = "发送状态")
    @ApiModelProperty("发送状态")
    private Integer sendStatus;

    /**
     * 发送消息
     */
    @ExcelProperty(value = "发送消息")
    @ApiModelProperty("发送消息")
    private String sendMessage;

    /**
     * 发送类型
     */
    @ExcelProperty(value = "发送类型")
    @ApiModelProperty("发送类型")
    private Integer sendType;

    /**
     * 发送用户ID集合
     */
    @ExcelProperty(value = "发送用户ID集合")
    @ApiModelProperty("发送用户ID集合")
    private String[] userIds;
    /**
     * 联系人类型
     */
    @ExcelProperty(value = "联系人类型")
    @ApiModelProperty("联系人类型")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long contactTypeId;


    /**
     * 发送对象
     */
    @ExcelProperty(value = "发送对象")
    @ApiModelProperty("发送对象")
    private String sendObject;

    /**
     * 发送人
     */
    @ExcelProperty(value = "发送人")
    @ApiModelProperty("发送人")
    private String userIdArr;

    @ApiModelProperty("发送列表")
    private List<TMesContactVo> tMesContacts;

    @ApiModelProperty("发送列表")
    private List<TMesRecordVo> tMesRecord;

}
