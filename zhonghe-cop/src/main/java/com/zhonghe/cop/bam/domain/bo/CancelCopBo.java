package com.zhonghe.cop.bam.domain.bo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Author: lpg
 * @Date: 2023/12/21/14:22
 * @Description:
 */
@Data
public class CancelCopBo implements Serializable {
    private static final long serialVersionUID = 844653160896820434L;
    /**
     * 注销企业ID
     */
    @NotEmpty(message = "注销企业ID不能为空")
    private List<Long> copIds;
    /**
     * 注销时间
     */
    @NotNull(message = "注销时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date cancelDate;
    /**
     * 注销原因
     */
    @NotEmpty(message = "注销原因不能为空")
    @Length(max = 2000, message = "注销原因长度不能超过2000个字符")
    private String cancelCause;
}
