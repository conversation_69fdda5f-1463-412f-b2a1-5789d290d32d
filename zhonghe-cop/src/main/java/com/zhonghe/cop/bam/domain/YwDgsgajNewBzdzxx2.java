package com.zhonghe.cop.bam.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: lpg
 * @Date: 2023/12/22/10:12
 * @Description: 东莞市二标四实标准地址信息新
 */
@Data
@TableName("public.yw_dgsgaj_new_bzdzxx2")
public class YwDgsgajNewBzdzxx2 implements Serializable {
    private static final long serialVersionUID = -2066093805045957799L;


    /**
     * 数据id
     */
    @TableId(value = "data_id")
    private String dataId;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 系统主键
     */
    private String systemid;

    /**
     * 所属行政区划
     */
    private String ssxzqh;

    /**
     * 所属行政区划代码
     */
    private String ssxzqhdm;

    /**
     * 所属乡镇街道
     */
    private String ssxzjd;

    /**
     * 所属乡镇街道代码
     */
    private String ssxzjddm;

    /**
     * 所属派出所
     */
    private String sspcs;

    /**
     * 所属派出所代码
     */
    private String sspcsdm;

    /**
     * 所属警务室
     */
    private String ssjws;

    /**
     * 所属警务室代码
     */
    private String ssjwsdm;

    /**
     * 所属社区、村（居）委会
     */
    private String sssqcjwh;

    /**
     * 所属社区、村（居）委会代码
     */
    private String sssqcjwhdm;

    /**
     * 所属网格代码
     */
    private String sswgdm;

    /**
     * 所属街路巷
     */
    private String ssjlx;

    /**
     * 所属街路巷代码
     */
    private String ssjlxdm;

    /**
     * 所属小区
     */
    private String sszzxq;

    /**
     * 所属小区代码
     */
    private String sszzxqdm;

    /**
     * 所属房屋id
     */
    private String ssfwid;

    /**
     * 门牌号码代码
     */
    private String mphmdm;

    /**
     * 门牌号码
     */
    private String mphm;

    /**
     * 门牌别名名称
     */
    private String mpbmmc;

    /**
     * 楼牌号码代码
     */
    private String lphmdm;

    /**
     * 楼牌号码
     */
    private String lphm;

    /**
     * 楼牌地址别称
     */
    private String lpdzbc;

    /**
     * 楼牌单元数
     */
    private Integer lpdys;

    /**
     * 单元代码
     */
    private String dydm;

    /**
     * 单元号数
     */
    private String dyhm;

    /**
     * 房屋代码
     */
    private String fwdm;

    /**
     * 房屋号数
     */
    private String fwhs;

    /**
     * 房间状态
     */
    private String fjzt;

    /**
     * 是否附属门牌
     */
    private String sffsmp;

    /**
     * 规格大小
     */
    private String ggdx;

    /**
     * 地址名称
     */
    private String dzmc;

    /**
     * 地址元素类型
     */
    private String dzyslx;

    /**
     * 门楼牌类型
     */
    private String pzlx;

    /**
     * 地址代码
     */
    private String dzdm;

    /**
     * 父级地址代码
     */
    private String fjdzdm;

    /**
     * 地址全称
     */
    private String dzqc;

    /**
     * 地址详址
     */
    private String dzxz;

    /**
     * 智网系统编号
     */
    private String zwxtbh;

    /**
     * 外系统编号
     */
    private String wxtbh;

    /**
     * 中心经度
     */
    private BigDecimal zxjd;

    /**
     * 中心纬度
     */
    private BigDecimal zxwd;

    /**
     * 生命状态
     */
    private String smzt;

    /**
     * 起用时间
     */
    private Date qysj;

    /**
     * 停用时间
     */
    private Date tysj;

    /**
     * 是否出租屋
     */
    private String sfczw;

    /**
     * 是否单体楼
     */
    private String sfdtl;

    /**
     * 是否建筑物
     */
    private String sfjzw;

    /**
     * 是否生成制牌数据
     */
    private String sfsczpsj;

    /**
     * 是否悬挂正式门牌
     */
    private String sfxgzsmp;

    /**
     * 门牌主从类型
     */
    private String mpzclx;

    /**
     * 数据来源
     */
    private String sjly;

    /**
     * 注销状态
     */
    private String zxzt;

    /**
     * 创建时间
     */
    private Date createdtime;

    /**
     * 修改时间
     */
    private Date lastupdatedtime;

    /**
     * 入库时间
     */
    private Date rksj;

    /**
     * 政务入库时间
     */
    private String zwRksj;

    /**
     * 公安入库时间
     */
    private String gaRksj;

    /**
     * 新增时间
     */
    private Date addTime;

    /**
     * 更新时间
     */
    private Date cdTime;

    /**
     * 批次号
     */
    private String cdBatch;

    /**
     * 操作方式
     */
    private String cdOperation;

    /**
     * 扩展1
     */
    private String extend1;

    /**
     * 扩展2
     */
    private String extend2;

    /**
     * 数据区域
     */
    private String dataArea;

    /**
     * ZMP
     */
    private String zmp;

    /**
     * YDZMC
     */
    private String ydzmc;

    /**
     * 运营方技术字段,非业务字段,请忽略并勿使用
     */
    @TableField(fill = FieldFill.INSERT)
    private Date dmpShareCdTimeIdatat;

    /**
     * 用数库自动配置的入库时间技术字段,非业务字段,请勿挂接
     */
    @TableField(fill = FieldFill.INSERT)
    private Date dataSharingCdTimeIdatat;

}
