package com.zhonghe.cop.bam.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.utils.BeanCopyUtils;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.cop.bam.domain.TDeptRole;
import com.zhonghe.cop.bam.domain.bo.TDeptRoleBo;
import com.zhonghe.cop.bam.domain.query.TDeptRoleQuery;
import com.zhonghe.cop.bam.domain.vo.TDeptRoleVo;
import com.zhonghe.cop.bam.mapper.TDeptRoleMapper;
import com.zhonghe.cop.bam.service.IdeptRoleService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;

/**
 * 部门职能服务实现类
 *
 * @Author: lpg
 * @Date: 2025/07/14
 * @Description: 部门职能服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DeptRoleServiceImpl implements IdeptRoleService {

    private final TDeptRoleMapper baseMapper;

    /**
     * 查询部门职能列表（分页）
     */
    @Override
    public TableDataInfo<TDeptRoleVo> queryPageList(TDeptRoleQuery query, PageQuery pageQuery) {
        LambdaQueryWrapper<TDeptRole> lqw = buildQueryWrapper(query);
        Page<TDeptRoleVo> page = baseMapper.selectVoPage(pageQuery.build(), lqw, TDeptRoleVo.class);
        return TableDataInfo.build(page);
    }

    /**
     * 查询部门职能列表（不分页）
     */
    @Override
    public List<TDeptRoleVo> queryList(TDeptRoleQuery query) {
        LambdaQueryWrapper<TDeptRole> lqw = buildQueryWrapper(query);
        return baseMapper.selectVoList(lqw, TDeptRoleVo.class);
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<TDeptRole> buildQueryWrapper(TDeptRoleQuery query) {
        LambdaQueryWrapper<TDeptRole> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(query.getTitle()), TDeptRole::getTitle, query.getTitle());
        lqw.like(StringUtils.isNotBlank(query.getContent()), TDeptRole::getContent, query.getContent());
        lqw.orderByAsc(TDeptRole::getSortOrder);
        return lqw;
    }

    /**
     * 根据ID查询部门职能信息
     */
    @Override
    public TDeptRoleVo queryById(Long id) {
        return baseMapper.selectVoById(id, TDeptRoleVo.class);
    }

    /**
     * 新增部门职能
     */
    @Override
    public Boolean insertByBo(TDeptRoleBo bo) {
        TDeptRole add = BeanCopyUtils.copy(bo, TDeptRole.class);
        // 如果没有设置排序字段，默认为最大排序号+1
        if (add.getSortOrder() == null) {
            // 查询当前最大的排序号
            Integer maxSortOrder = baseMapper.selectMaxSortOrder();
            add.setSortOrder(maxSortOrder == null ? 1 : maxSortOrder + 1);
        }
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改部门职能
     */
    @Override
    public Boolean updateByBo(TDeptRoleBo bo) {
        TDeptRole update = BeanCopyUtils.copy(bo, TDeptRole.class);
        return baseMapper.updateById(update) > 0;
    }

    @Override
    public int sort(List<TDeptRole> tDeptRoles) {
        return baseMapper.updateBatchById(tDeptRoles) ? 1 : 0;
    }

    /**
     * 删除部门职能
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids) {
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
