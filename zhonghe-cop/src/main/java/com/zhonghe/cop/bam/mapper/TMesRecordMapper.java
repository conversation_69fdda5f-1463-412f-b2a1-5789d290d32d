package com.zhonghe.cop.bam.mapper;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhonghe.common.core.mapper.BaseMapperPlus;
import com.zhonghe.cop.bam.domain.TMesRecord;
import com.zhonghe.cop.bam.domain.vo.TMesRecordVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 发送记录Mapper接口
 *
 * <AUTHOR>
 * @date 2023-12-11
 */
public interface TMesRecordMapper extends BaseMapperPlus<TMesRecordMapper, TMesRecord, TMesRecordVo> {

    /**
     * 分页查询发送记录
     *
     * @param build
     * @param lqw
     * @return
     */
    Page<TMesRecordVo> selectMesRecordList(Page<TMesRecord> build, @Param(Constants.WRAPPER) Wrapper<TMesRecord> lqw);

    /**
     * 查询发送记录列表
     *
     * @param lqw
     * @return
     */
    List<TMesRecordVo> selectMesRecordList(@Param(Constants.WRAPPER) Wrapper<TMesRecord> lqw);

    /**
     * 根据批次号查询发送记录
     *
     * @param batchNo
     * @return
     */
    TMesRecordVo selectVoByBatchNo(@Param("batchNo") String batchNo);
}
