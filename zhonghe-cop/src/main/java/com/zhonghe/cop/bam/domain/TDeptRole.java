package com.zhonghe.cop.bam.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.zhonghe.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 领导小组部门职能表
 *
 * @TableName t_dept_role
 */
@Data
@TableName("cop.t_dept_role")
public class TDeptRole extends BaseEntity implements Serializable {

    private static final long serialVersionUID = -5759027857584575807L;
    /**
     * 主键，自增ID
     */
    @ApiModelProperty("主键，自增ID")
    private Long id;
    /**
     * 部门/小组名称
     */
    @ApiModelProperty("部门/小组名称")
    private String title;
    /**
     * 部门/小组职能描述
     */
    @ApiModelProperty("部门/小组职能描述")
    private String content;
    /**
     * 排序字段，数值越小越靠前
     */
    @ApiModelProperty("排序字段，数值越小越靠前")
    private Integer sortOrder;

}
