package com.zhonghe.cop.bam.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 部门成员视图对象
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Data
@ApiModel("部门成员视图对象")
@ExcelIgnoreUnannotated
public class TDeptMemberVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键，自增ID
     */
    @ExcelProperty(value = "主键")
    @ApiModelProperty("主键，自增ID")
    private Long id;

    /**
     * 父节点ID
     */
    @ExcelProperty(value = "父节点ID")
    @ApiModelProperty("父节点ID")
    private Long parentId;

    /**
     * 小组成员名称，可为人名或单位名
     */
    @ExcelProperty(value = "小组成员名称")
    @ApiModelProperty("小组成员名称")
    private String name;

    /**
     * 排序字段
     */
    @ExcelProperty(value = "排序字段")
    @ApiModelProperty("排序字段")
    private Integer sortOrder;

    /**
     * 创建时间
     */
    @ExcelProperty(value = "创建时间")
    @ApiModelProperty("创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ExcelProperty(value = "更新时间")
    @ApiModelProperty("更新时间")
    private Date updateTime;

    /**
     * 子节点
     */
    @ApiModelProperty("子节点")
    private List<TDeptMemberVo> children = new ArrayList<>();
}
