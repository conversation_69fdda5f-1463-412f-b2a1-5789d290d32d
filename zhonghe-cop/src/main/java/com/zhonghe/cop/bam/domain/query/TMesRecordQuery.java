package com.zhonghe.cop.bam.domain.query;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 发送记录查询对象
 *
 * <AUTHOR>
 * @date 2023-12-11
 */

@Data
@ApiModel("发送记录查询对象")
public class TMesRecordQuery {


    /**
     * 发送对象
     */
    @ApiModelProperty(value = "发送对象")
    private String sendObject;

    /**
     * 发送状态 0-未发送 1-已发送 2-发送失败
     */
    @ApiModelProperty(value = "发送状态")
    private Integer sendStatus;

}
