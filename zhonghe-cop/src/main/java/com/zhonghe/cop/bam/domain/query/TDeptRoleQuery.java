package com.zhonghe.cop.bam.domain.query;

import com.zhonghe.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 部门职能查询对象
 *
 * <AUTHOR>
 * @date 2025-07-14
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("部门职能查询对象")
public class TDeptRoleQuery extends BaseEntity {

    private static final long serialVersionUID = 6499195897548674665L;
    /**
     * 部门/小组名称
     */
    @ApiModelProperty("部门/小组名称")
    private String title;

    /**
     * 部门/小组职能描述
     */
    @ApiModelProperty("部门/小组职能描述")
    private String content;

}
