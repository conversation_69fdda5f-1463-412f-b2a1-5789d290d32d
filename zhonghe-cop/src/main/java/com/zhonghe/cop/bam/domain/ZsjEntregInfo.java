package com.zhonghe.cop.bam.domain;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 东莞市市场主体基本信息 实体类
 */
@Data
@TableName("public.zsj_entreg_info")
public class ZsjEntregInfo implements Serializable {
    private static final long serialVersionUID = -2066093805045957799L;

    /**
     * 主键
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 主体身份代码
     */
    private String pripid;

    /**
     * 统一社会信用代码
     */
    private String uniscid;

    /**
     * 注册号
     */
    private String regno;

    /**
     * 市场主体名称
     */
    private String entname;

    /**
     * 市场主体类型
     */
    private String typename;

    /**
     * 经营范围
     */
    private String opscope;

    /**
     * 登记管辖机关
     */
    private String aicid;

    /**
     * 注册资本
     */
    private String regcap;

    /**
     * 国别
     */
    private String country;

    /**
     * 成立日期
     */
    private Date estdate;

    /**
     * 核准日期
     */
    private Date apprdate;

    /**
     * 经营状态
     */
    private String opstate;

    /**
     * 法定代表人姓名
     */
    private String lerepname;

    /**
     * 经营(驻在)期限自
     */
    private Date opfrom;

    /**
     * 经营(驻在)期限至
     */
    private Date opto;

    /**
     * 行业门类
     */
    private String industryphy;

    /**
     * 行业代码
     */
    private String industryco;

    /**
     * 场所
     */
    private String dom;

    /**
     * 住所地址编码
     */
    private String faxnumber;

    /**
     * 从业人数
     */
    private String empnum;

    /**
     * 更新时间
     */
    private String datDt;

    /**
     * etl日期
     */
    private String etlDt;

    /**
     * 开始时间
     */
    private String gldmStaDt;

    /**
     * 结束时间
     */
    private String gldmEndDt;

    /**
     * 删除标志
     */
    private String gldmDelFlag;

    /**
     * 代理主键
     */
    private String dsesUuid;

    /**
     * 行业代码编码
     */
    private String industrycoVal;

    /**
     * 预留字段一
     */
    private String reservedField1;

    /**
     * 预留字段二
     */
    private String reservedField2;

    /**
     * 用数库自动配置的入库时间技术字段,非业务字段,请勿挂接
     */
    @TableField(fill = FieldFill.INSERT)
    private Date dataSharingCdTimeIdatat;
}
