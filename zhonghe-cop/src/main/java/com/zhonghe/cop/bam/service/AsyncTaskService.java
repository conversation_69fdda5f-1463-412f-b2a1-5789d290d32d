package com.zhonghe.cop.bam.service;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 异步任务状态管理服务
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
@Slf4j
@Service
public class AsyncTaskService {

    /**
     * 任务状态缓存
     */
    private final Map<String, AsyncTaskInfo> taskCache = new ConcurrentHashMap<>();

    /**
     * 创建异步任务
     */
    public String createTask(String taskType, String userId, int dataCount) {
        String taskId = generateTaskId(taskType);
        AsyncTaskInfo taskInfo = new AsyncTaskInfo(taskId, taskType, userId);
        taskInfo.setDataCount(dataCount);
        taskCache.put(taskId, taskInfo);
        log.info("创建异步任务: {}, 类型: {}, 用户: {}, 数据量: {}", taskId, taskType, userId, dataCount);
        return taskId;
    }

    /**
     * 更新任务状态
     */
    public void updateTaskStatus(String taskId, TaskStatus status, String message) {
        AsyncTaskInfo taskInfo = taskCache.get(taskId);
        if (taskInfo != null) {
            taskInfo.setStatus(status);
            taskInfo.setMessage(message);
            taskInfo.setUpdateTime(LocalDateTime.now());
            log.info("更新任务状态: {}, 状态: {}, 消息: {}", taskId, status.getDescription(), message);
        }
    }

    /**
     * 设置任务结果
     */
    public void setTaskResult(String taskId, Object result) {
        AsyncTaskInfo taskInfo = taskCache.get(taskId);
        if (taskInfo != null) {
            taskInfo.setResult(result);
            taskInfo.setStatus(TaskStatus.SUCCESS);
            taskInfo.setUpdateTime(LocalDateTime.now());
            log.info("设置任务结果: {}, 结果: {}", taskId, result);
        }
    }

    /**
     * 设置任务错误
     */
    public void setTaskError(String taskId, String errorMessage) {
        AsyncTaskInfo taskInfo = taskCache.get(taskId);
        if (taskInfo != null) {
            taskInfo.setErrorMessage(errorMessage);
            taskInfo.setStatus(TaskStatus.FAILED);
            taskInfo.setUpdateTime(LocalDateTime.now());
            log.error("设置任务错误: {}, 错误信息: {}", taskId, errorMessage);
        }
    }

    /**
     * 获取任务信息
     */
    public AsyncTaskInfo getTaskInfo(String taskId) {
        return taskCache.get(taskId);
    }

    /**
     * 获取用户的所有任务
     */
    public Map<String, AsyncTaskInfo> getUserTasks(String userId) {
        Map<String, AsyncTaskInfo> userTasks = new ConcurrentHashMap<>();
        taskCache.forEach((taskId, taskInfo) -> {
            if (userId.equals(taskInfo.getUserId())) {
                userTasks.put(taskId, taskInfo);
            }
        });
        return userTasks;
    }

    /**
     * 执行异步任务并跟踪状态
     */
    public <T> CompletableFuture<T> executeWithTracking(String taskId, CompletableFuture<T> future) {
        updateTaskStatus(taskId, TaskStatus.RUNNING, "任务开始执行");

        return future.whenComplete((result, throwable) -> {
            if (throwable != null) {
                setTaskError(taskId, throwable.getMessage());
            } else {
                setTaskResult(taskId, result);
            }
        });
    }

    /**
     * 生成任务ID
     */
    private String generateTaskId(String taskType) {
        return taskType + "_" + System.currentTimeMillis() + "_" + Thread.currentThread().getId();
    }

    /**
     * 定时清理过期任务（每小时执行一次）
     */
    @Scheduled(fixedRate = 3600000) // 1小时
    public void cleanExpiredTasks() {
        LocalDateTime expireTime = LocalDateTime.now().minusHours(24); // 24小时过期

        taskCache.entrySet().removeIf(entry -> {
            AsyncTaskInfo taskInfo = entry.getValue();
            return taskInfo.getCreateTime().isBefore(expireTime);
        });

        log.debug("清理过期异步任务完成，当前任务数量: {}", taskCache.size());
    }

    /**
     * 获取任务统计信息
     */
    public Map<String, Object> getTaskStatistics() {
        Map<String, Object> statistics = new ConcurrentHashMap<>();

        long totalTasks = taskCache.size();
        long pendingTasks = taskCache.values().stream()
            .mapToLong(task -> task.getStatus() == TaskStatus.PENDING ? 1 : 0)
            .sum();
        long runningTasks = taskCache.values().stream()
            .mapToLong(task -> task.getStatus() == TaskStatus.RUNNING ? 1 : 0)
            .sum();
        long successTasks = taskCache.values().stream()
            .mapToLong(task -> task.getStatus() == TaskStatus.SUCCESS ? 1 : 0)
            .sum();
        long failedTasks = taskCache.values().stream()
            .mapToLong(task -> task.getStatus() == TaskStatus.FAILED ? 1 : 0)
            .sum();

        statistics.put("totalTasks", totalTasks);
        statistics.put("pendingTasks", pendingTasks);
        statistics.put("runningTasks", runningTasks);
        statistics.put("successTasks", successTasks);
        statistics.put("failedTasks", failedTasks);

        return statistics;
    }

    /**
     * 取消任务
     */
    public boolean cancelTask(String taskId) {
        AsyncTaskInfo taskInfo = taskCache.get(taskId);
        if (taskInfo != null && taskInfo.getStatus() == TaskStatus.PENDING) {
            updateTaskStatus(taskId, TaskStatus.FAILED, "任务已取消");
            return true;
        }
        return false;
    }

    /**
     * 删除任务
     */
    public boolean deleteTask(String taskId) {
        AsyncTaskInfo removed = taskCache.remove(taskId);
        if (removed != null) {
            log.info("删除任务: {}", taskId);
            return true;
        }
        return false;
    }

    /**
     * 任务状态枚举
     */
    public enum TaskStatus {
        PENDING("待处理"),
        RUNNING("执行中"),
        SUCCESS("成功"),
        FAILED("失败");

        private final String description;

        TaskStatus(String description) {
            this.description = description;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 异步任务信息
     */
    @Data
    public static class AsyncTaskInfo {
        private String taskId;
        private String taskType;
        private TaskStatus status;
        private String message;
        private LocalDateTime createTime;
        private LocalDateTime updateTime;
        private Object result;
        private String errorMessage;
        private int dataCount;
        private String userId;

        public AsyncTaskInfo(String taskId, String taskType, String userId) {
            this.taskId = taskId;
            this.taskType = taskType;
            this.userId = userId;
            status = TaskStatus.PENDING;
            createTime = LocalDateTime.now();
            updateTime = LocalDateTime.now();
        }
    }
}
