package com.zhonghe.cop.bam.domain.bo;

import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


/**
 * 发送记录业务对象 t_mes_record
 *
 * <AUTHOR>
 * @date 2023-12-11
 */

@Data
@ApiModel("发送记录业务对象")
public class TMesRecordBo implements Serializable {

    private static final long serialVersionUID = 1776847231116913513L;
    /**
     * 发送记录ID
     */
    @ApiModelProperty(value = "发送记录ID", required = true)
    @NotNull(message = "发送记录ID不能为空", groups = {EditGroup.class})
    private String batchNo;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String note;


    /**
     * 发送内容
     */
    @ApiModelProperty(value = "发送内容", required = true)
    @NotBlank(message = "发送内容不能为空", groups = {AddGroup.class, EditGroup.class})
    private String content;

    /**
     * 发送人ID
     */
    @ApiModelProperty(value = "发送人ID", required = true)
    @NotEmpty(message = "发送人ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private List<Long> userIds = new ArrayList<>();

    /**
     * 联系人类型
     */
    @ApiModelProperty("联系人类型")
    @NotNull(message = "联系人类型不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long contactTypeId;


}
