package com.zhonghe.cop.bam.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 企业注销记录视图对象 t_cop_cancel_record
 *
 * <AUTHOR>
 * @date 2023-12-21
 */
@Data
@ApiModel("企业注销记录视图对象")
@ExcelIgnoreUnannotated
public class TCopCancelRecordVo {

    private static final long serialVersionUID = 1L;

    /**
     * 企业注销记录ID
     */
    @ExcelProperty(value = "企业注销记录ID")
    @ApiModelProperty("企业注销记录ID")
    private Long copCancelRecordId;

    /**
     * 说明
     */
    @ExcelProperty(value = "说明")
    @ApiModelProperty("说明")
    private String note;

    /**
     * 用户ID
     */
    @ExcelProperty(value = "用户ID")
    @ApiModelProperty("用户ID")
    private Long userId;

    /**
     * 注销企业ID
     */
    @ExcelProperty(value = "注销企业ID")
    @ApiModelProperty("注销企业ID")
    private Long copId;

    /**
     * 取消日期
     */
    @ExcelProperty(value = "取消日期")
    @ApiModelProperty("取消日期")
    private String cancelDate;

    /**
     * 注销原因
     */
    @ExcelProperty(value = "注销原因")
    @ApiModelProperty("注销原因")
    private String cancelCause;


}
