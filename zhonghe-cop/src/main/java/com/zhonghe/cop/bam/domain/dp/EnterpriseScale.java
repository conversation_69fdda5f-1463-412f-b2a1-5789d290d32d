package com.zhonghe.cop.bam.domain.dp;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zhonghe.system.mappedtypes.GeometryTypeHandler;
import lombok.Data;
import net.postgis.jdbc.geometry.Geometry;

import java.util.Date;

/**
 * @Author: lpg
 * @Date: 2024/01/10
 * @Description: 规上企业实体类
 */
@Data
@TableName("cop.sl_enterprise_scale")
public class EnterpriseScale implements java.io.Serializable {

    private static final long serialVersionUID = 4172016659360532880L;
    /**
     * 规上企业ID
     */
    @TableId
    private Long enterpriseId;

    /**
     * 企业名称
     */
    private String name;

    /**
     * 企业类型
     */
    private String enterpriseType;

    /**
     * 企业地址
     */
    private String address;

    /**
     * 法人
     */
    private String legalPerson;

    /**
     * 企业电话
     */
    private String phone;

    /**
     * 地理位置
     */
    @TableField(typeHandler = GeometryTypeHandler.class)
    private Geometry geom;

    /**
     * 删除标志
     */
    @TableLogic
    private String delFlag;

    /**
     * 状态
     */
    private String status;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新者
     */
    private String updateBy;

}
