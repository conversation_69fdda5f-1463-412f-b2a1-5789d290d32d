package com.zhonghe.cop.bam.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.zhonghe.common.core.domain.model.LoginUser;
import com.zhonghe.common.helper.LoginHelper;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.cop.bam.domain.MenuPage;
import com.zhonghe.cop.bam.mapper.MenuPageMapper;
import com.zhonghe.cop.bam.service.IMenuPageService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.stream.Collectors;

/**
 * @Author: lpg
 * @Date: 2023/12/13/14:21
 * @Description:
 */
@Service
@RequiredArgsConstructor
public class MenuPageServiceImpl implements IMenuPageService {

    private final MenuPageMapper menuPageMapper;

    @Override
    public MenuPage getColumn(String menuPageName) {
        //获取当前用户
        LoginUser loginUser = LoginHelper.getLoginUser();

        MenuPage menuPage = menuPageMapper.selectOne(new LambdaQueryWrapper<MenuPage>()
            .eq(MenuPage::getMenuPageName, menuPageName)
            .eq(MenuPage::getMenuPageUserId, loginUser.getUserId()));

        if (ObjectUtil.isNotNull(menuPage)) {
            if (StringUtils.isBlank(menuPage.getMenuPageColumn())) {
                return menuPage;
            }
            menuPage.setPageColumns(StrUtil.split(menuPage.getMenuPageColumn(), ','));
        }

        return menuPage;
    }

    @Override
    public Boolean saveMenuPage(MenuPage menuPage) {
        //获取当前用户
        LoginUser loginUser = LoginHelper.getLoginUser();

        boolean exists = menuPageMapper.exists(new LambdaQueryWrapper<MenuPage>()
            .eq(MenuPage::getMenuPageName, menuPage.getMenuPageName())
            .eq(MenuPage::getMenuPageUserId, loginUser.getUserId()));

        if (!exists) {
            menuPage.setMenuPageUserId(loginUser.getUserId());
            String pageColumns = menuPage.getPageColumns().stream().collect(Collectors.joining(","));
            menuPage.setMenuPageColumn(pageColumns);
            return menuPageMapper.insert(menuPage) > 0;
        } else {
            LambdaUpdateWrapper<MenuPage> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(MenuPage::getMenuPageName, menuPage.getMenuPageName())
                .eq(MenuPage::getMenuPageUserId, loginUser.getUserId())
                .set(MenuPage::getMenuPageColumn, menuPage.getPageColumns().stream().collect(Collectors.joining(",")));
            return menuPageMapper.update(null, updateWrapper) > 0;
        }

    }
}
