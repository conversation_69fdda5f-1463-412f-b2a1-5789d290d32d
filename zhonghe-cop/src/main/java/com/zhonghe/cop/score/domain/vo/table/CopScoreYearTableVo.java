package com.zhonghe.cop.score.domain.vo.table;

import com.fhs.core.trans.anno.Trans;
import com.fhs.core.trans.constant.TransType;
import com.fhs.core.trans.vo.TransPojo;
import com.zhonghe.cop.bam.constants.CopConstants;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * @Author: lpg
 * @Date: 2023/12/21/16:25
 * @Description:
 */
@Data
public class CopScoreYearTableVo implements TransPojo, Serializable {
    private static final long serialVersionUID = 6185411776535699454L;

    private Map<String, Object> transMap = new HashMap<>();
    /**
     * 企业ID
     */
    @ApiModelProperty(value = "copId")
    private Long copId;
    /**
     * 企业名称
     */
    @ApiModelProperty(value = "企业名称")
    private String copName;
    /**
     * 法人代表
     */
    @ApiModelProperty(value = "法人代表")
    private String copOwner;
    /**
     * 企业电话
     */
    @ApiModelProperty(value = "企业电话")
    private String copPhone;
    /**
     * 企业性质
     */
    @ApiModelProperty(value = "企业性质")
    @Trans(type = TransType.DICTIONARY, key = CopConstants.COP_PROPERTY, ref = "copProperty")
    private String propertyId;
    /**
     * 单位性质
     */
    @ApiModelProperty(value = "单位性质")
    private String copProperty;
    /**
     * 企业地址
     */
    @ApiModelProperty(value = "企业地址")
    private String copAddress;
    /**
     * 企业年度信用评分
     */
    @ApiModelProperty(value = "企业年度信用评分")
    private Integer copScore;
}
