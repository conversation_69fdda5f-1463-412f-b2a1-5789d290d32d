package com.zhonghe.cop.score.domain.bo;

import com.zhonghe.common.core.domain.BaseEntity;
import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


/**
 * 企业年度评分业务对象 t_cop_score_year
 *
 * <AUTHOR>
 * @date 2023-12-07
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("企业年度评分业务对象")
public class TCopScoreYearBo extends BaseEntity {

    private static final long serialVersionUID = -3592028311689245308L;
    /**
     * 企业年度评分ID
     */
    @ApiModelProperty(value = "企业年度评分ID", required = true)
    @NotNull(message = "企业年度评分ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long copScoreYearId;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注", required = true)
    @NotBlank(message = "备注不能为空", groups = {AddGroup.class, EditGroup.class})
    private String note;

    /**
     * 企业ID
     */
    @ApiModelProperty(value = "企业ID", required = true)
    @NotNull(message = "企业ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long copId;

    /**
     * 企业年度信用评分（默认120分，每年复位）
     */
    @ApiModelProperty(value = "企业年度信用评分（默认120分，每年复位）", required = true)
    @NotNull(message = "企业年度信用评分（默认120分，每年复位）不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long copScore;

    /**
     * 评分时间（年度）
     */
    @ApiModelProperty(value = "评分时间（年度）", required = true)
    @NotBlank(message = "评分时间（年度）不能为空", groups = {AddGroup.class, EditGroup.class})
    private String scoreDate;


}
