package com.zhonghe.cop.score.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zhonghe.common.annotation.ExcelDictFormat;
import com.zhonghe.common.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 企业年度评分视图对象 t_cop_score_year
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
@Data
@ApiModel("企业年度评分视图对象")
@ExcelIgnoreUnannotated
public class TCopScoreYearVo {

    private static final long serialVersionUID = 1L;

    /**
     * 企业年度评分ID
     */
    @ExcelProperty(value = "企业年度评分ID")
    @ApiModelProperty("企业年度评分ID")
    private Long copScoreYearId;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    @ApiModelProperty("备注")
    private String note;

    /**
     * 企业ID
     */
    @ExcelProperty(value = "企业ID")
    @ApiModelProperty("企业ID")
    private Long copId;

    /**
     * 企业年度信用评分（默认120分，每年复位）
     */
    @ExcelProperty(value = "企业年度信用评分", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "默=认120分，每年复位")
    @ApiModelProperty("企业年度信用评分（默认120分，每年复位）")
    private Long copScore;

    /**
     * 评分时间（年度）
     */
    @ExcelProperty(value = "评分时间", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "年=度")
    @ApiModelProperty("评分时间（年度）")
    private String scoreDate;


}
