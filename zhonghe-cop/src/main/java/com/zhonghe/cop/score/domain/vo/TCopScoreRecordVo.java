package com.zhonghe.cop.score.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.zhonghe.common.annotation.ExcelDictFormat;
import com.zhonghe.common.convert.ExcelDictConvert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


/**
 * 企业信用扣分记录视图对象 t_cop_score_record
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
@Data
@ApiModel("企业信用扣分记录视图对象")
@ExcelIgnoreUnannotated
public class TCopScoreRecordVo {

    private static final long serialVersionUID = 1L;

    /**
     * 企业分数ID
     */
    @ExcelProperty(value = "企业分数ID")
    @ApiModelProperty("企业分数ID")
    private Long copScoreRecordId;

    /**
     * 备注
     */
    @ExcelProperty(value = "备注")
    @ApiModelProperty("备注")
    private String note;

    /**
     * 部门上报表ID（对应t_rep_report主键）
     */
    @ExcelProperty(value = "部门上报表ID", converter = ExcelDictConvert.class)
    @ExcelDictFormat(readConverterExp = "对=应t_rep_report主键")
    @ApiModelProperty("部门上报表ID（对应t_rep_report主键）")
    private Long repReportId;

    /**
     * 企业ID
     */
    @ExcelProperty(value = "企业ID")
    @ApiModelProperty("企业ID")
    private Long copId;

    /**
     * 所扣分值
     */
    @ExcelProperty(value = "所扣分值")
    @ApiModelProperty("所扣分值")
    private Long deductScore;


}
