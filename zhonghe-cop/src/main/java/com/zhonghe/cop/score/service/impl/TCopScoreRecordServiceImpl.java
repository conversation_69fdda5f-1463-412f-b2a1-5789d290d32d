package com.zhonghe.cop.score.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.cop.rep.em.Level;
import com.zhonghe.cop.score.domain.TCopScoreRecord;
import com.zhonghe.cop.score.domain.TCopScoreYear;
import com.zhonghe.cop.score.domain.bo.TCopScoreRecordBo;
import com.zhonghe.cop.score.domain.vo.TCopScoreRecordVo;
import com.zhonghe.cop.score.mapper.TCopScoreRecordMapper;
import com.zhonghe.cop.score.mapper.TCopScoreYearMapper;
import com.zhonghe.cop.score.service.ITCopScoreRecordService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 企业信用扣分记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
@RequiredArgsConstructor
@Service
public class TCopScoreRecordServiceImpl implements ITCopScoreRecordService {

    private final TCopScoreRecordMapper baseMapper;
    private final TCopScoreYearMapper copScoreYearMapper;

    public static void main(String[] args) {
        System.out.println(DateUtil.beginOfMonth(new Date()));
    }

    /**
     * 查询企业信用扣分记录
     */
    @Override
    public TCopScoreRecordVo queryById(Long copScoreRecordId) {
        return baseMapper.selectVoById(copScoreRecordId);
    }

    /**
     * 查询企业信用扣分记录列表
     */
    @Override
    public TableDataInfo<TCopScoreRecordVo> queryPageList(TCopScoreRecordBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<TCopScoreRecord> lqw = buildQueryWrapper(bo);
        Page<TCopScoreRecordVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询企业信用扣分记录列表
     */
    @Override
    public List<TCopScoreRecordVo> queryList(TCopScoreRecordBo bo) {
        LambdaQueryWrapper<TCopScoreRecord> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<TCopScoreRecord> buildQueryWrapper(TCopScoreRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<TCopScoreRecord> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getCopScoreRecordId() != null, TCopScoreRecord::getCopScoreRecordId, bo.getCopScoreRecordId());
        lqw.eq(StringUtils.isNotBlank(bo.getNote()), TCopScoreRecord::getNote, bo.getNote());
        lqw.eq(bo.getRepReportId() != null, TCopScoreRecord::getRepReportId, bo.getRepReportId());
        lqw.eq(bo.getCopId() != null, TCopScoreRecord::getCopId, bo.getCopId());
        lqw.eq(bo.getDeductScore() != null, TCopScoreRecord::getDeductScore, bo.getDeductScore());
        return lqw;
    }

    /**
     * 新增企业信用扣分记录
     */
    @Override
    public Boolean insertByBo(TCopScoreRecordBo bo) {
        TCopScoreRecord add = BeanUtil.toBean(bo, TCopScoreRecord.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setCopScoreRecordId(add.getCopScoreRecordId());
        }
        return flag;
    }

    /**
     * 修改企业信用扣分记录
     */
    @Override
    public Boolean updateByBo(TCopScoreRecordBo bo) {
        TCopScoreRecord update = BeanUtil.toBean(bo, TCopScoreRecord.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(TCopScoreRecord entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    @Transactional
    public void deleteByRepReportIds(Collection<Long> ids) {
        baseMapper.delete(Wrappers.<TCopScoreRecord>lambdaQuery().in(TCopScoreRecord::getRepReportId, ids));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deductionScore(Long copId, Integer level, Long repReportId) {
        //查询扣分记录
        TCopScoreRecord scoreRecord = baseMapper.selectOne(Wrappers.<TCopScoreRecord>lambdaQuery()
            .eq(TCopScoreRecord::getCopId, copId)
            .eq(TCopScoreRecord::getRepReportId, repReportId));

        //根据风险等级进行扣分
        TCopScoreRecord tCopScoreRecord = TCopScoreRecord.builder()
            .copId(copId)
            .repReportId(repReportId)
            .deductScore(Level.getDeductScoreByCode(level))
            .build();
        if (ObjectUtil.isNull(scoreRecord)) {
            baseMapper.insert(tCopScoreRecord);
        } else {
            //设置扣分记录ID
            tCopScoreRecord.setCopScoreRecordId(scoreRecord.getCopScoreRecordId());
            baseMapper.updateById(tCopScoreRecord);
        }
        //查询企业当年年度评分是否存在
        boolean exists = copScoreYearMapper.exists(Wrappers.<TCopScoreYear>lambdaQuery()
            .eq(TCopScoreYear::getCopId, copId)
            .apply("DATE_PART('year', score_date) = {0}", DateUtil.year(new Date())));
        if (!exists) {
            //新增企业年度评分
            copScoreYearMapper.insert(TCopScoreYear.builder()
                .copId(copId)
                .copScore(120)
                .scoreDate(DateUtil.beginOfMonth(new Date()))
                .build());
        }
        //修改企业年度评分
        copScoreYearMapper.updateScore(copId, tCopScoreRecord.getDeductScore(), DateUtil.year(new Date()));
    }

    /**
     * 批量删除企业信用扣分记录
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
