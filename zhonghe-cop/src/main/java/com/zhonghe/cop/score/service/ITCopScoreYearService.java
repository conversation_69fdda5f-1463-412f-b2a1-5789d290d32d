package com.zhonghe.cop.score.service;

import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.cop.score.domain.bo.TCopScoreYearBo;
import com.zhonghe.cop.score.domain.query.CopScoreYearQuery;
import com.zhonghe.cop.score.domain.vo.CopRiskVo;
import com.zhonghe.cop.score.domain.vo.TCopScoreYearInfoVo;
import com.zhonghe.cop.score.domain.vo.TCopScoreYearVo;
import com.zhonghe.cop.score.domain.vo.table.CopScoreYearTableVo;

import java.util.Collection;
import java.util.List;

/**
 * 企业年度评分Service接口
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
public interface ITCopScoreYearService {

    /**
     * 查询企业年度评分
     */
    TCopScoreYearInfoVo queryById(Long copScoreYearId);

    /**
     * 查询企业年度评分列表
     */
    TableDataInfo<TCopScoreYearVo> queryPageList(TCopScoreYearBo bo, PageQuery pageQuery);

    /**
     * 查询企业年度评分列表
     */
    List<TCopScoreYearVo> queryList(TCopScoreYearBo bo);

    /**
     * 修改企业年度评分
     */
    Boolean insertByBo(TCopScoreYearBo bo);

    /**
     * 修改企业年度评分
     */
    Boolean updateByBo(TCopScoreYearBo bo);

    /**
     * 校验并批量删除企业年度评分信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 企业风险历年评级
     *
     * @param copId
     * @return
     */
    List<CopRiskVo> queryRisk(Long copId);

    /**
     * 查询当前年度评分
     *
     * @return
     */
    TableDataInfo<CopScoreYearTableVo> queryNowYearPage(CopScoreYearQuery query, PageQuery pageQuery);

    /**
     * 查询当前年度评分列表
     *
     * @param query
     * @return
     */
    List<CopScoreYearTableVo> queryNowYearList(CopScoreYearQuery query);

    /**
     * 重置评分
     */
    void resetScore();
}
