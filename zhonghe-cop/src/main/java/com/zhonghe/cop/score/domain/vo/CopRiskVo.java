package com.zhonghe.cop.score.domain.vo;

import com.zhonghe.cop.score.domain.dto.DeductScoreRecordDto;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Author: lpg
 * @Date: 2023/12/07/16:50
 * @Description: 企业信用扣分记录视图对象
 */
@Data
public class CopRiskVo {
    /**
     * 年
     */
    @ApiModelProperty(value = "年")
    private Integer year;
    /**
     * 总扣分
     */
    @ApiModelProperty(value = "总扣分")
    private Integer totalDeductScore;

    /**
     * 扣分记录
     */
    @ApiModelProperty(value = "扣分记录")
    private List<DeductScoreRecordDto> deductScoreRecord;


}
