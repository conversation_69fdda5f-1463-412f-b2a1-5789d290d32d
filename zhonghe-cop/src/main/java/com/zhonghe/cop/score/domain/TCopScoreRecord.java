package com.zhonghe.cop.score.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zhonghe.common.core.domain.BaseEntity;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 企业信用扣分记录对象 t_cop_score_record
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("cop.t_cop_score_record")
@Builder
@JsonSerialize
public class TCopScoreRecord extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 企业分数ID
     */
    @TableId
    private Long copScoreRecordId;
    /**
     * 是否删除
     */
    @TableLogic
    private String delFlag;
    /**
     * 备注
     */
    private String note;
    /**
     * 部门上报表ID（对应t_rep_report主键）
     */
    private Long repReportId;
    /**
     * 企业ID
     */
    private Long copId;
    /**
     * 所扣分值
     */
    private Integer deductScore;

}
