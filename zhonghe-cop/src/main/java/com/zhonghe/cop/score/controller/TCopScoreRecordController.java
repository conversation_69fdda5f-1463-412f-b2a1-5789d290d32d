package com.zhonghe.cop.score.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.zhonghe.common.annotation.Log;
import com.zhonghe.common.annotation.RepeatSubmit;
import com.zhonghe.common.core.controller.BaseController;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import com.zhonghe.common.enums.BusinessType;
import com.zhonghe.common.utils.poi.ExcelUtil;
import com.zhonghe.cop.score.domain.bo.TCopScoreRecordBo;
import com.zhonghe.cop.score.domain.vo.TCopScoreRecordVo;
import com.zhonghe.cop.score.service.ITCopScoreRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.List;

/**
 * 企业信用扣分记录Controller
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
@Validated
@Api(value = "企业信用扣分记录控制器", tags = {"企业信用扣分记录管理"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/cop/copScoreRecord")
public class TCopScoreRecordController extends BaseController {

    private final ITCopScoreRecordService iTCopScoreRecordService;

    /**
     * 查询企业信用扣分记录列表
     */
    @ApiOperation("查询企业信用扣分记录列表")
    @SaCheckPermission("cop:copScoreRecord:list")
    @GetMapping("/list")
    public TableDataInfo<TCopScoreRecordVo> list(TCopScoreRecordBo bo, PageQuery pageQuery) {
        return iTCopScoreRecordService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出企业信用扣分记录列表
     */
    @ApiOperation("导出企业信用扣分记录列表")
    @SaCheckPermission("cop:copScoreRecord:export")
    @Log(title = "企业信用扣分记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(TCopScoreRecordBo bo, HttpServletResponse response) {
        List<TCopScoreRecordVo> list = iTCopScoreRecordService.queryList(bo);
        ExcelUtil.exportExcel(list, "企业信用扣分记录", TCopScoreRecordVo.class, response);
    }

    /**
     * 获取企业信用扣分记录详细信息
     */
    @ApiOperation("获取企业信用扣分记录详细信息")
    @SaCheckPermission("cop:copScoreRecord:query")
    @GetMapping("/{copScoreRecordId}")
    public R<TCopScoreRecordVo> getInfo(@ApiParam("主键")
                                        @NotNull(message = "主键不能为空")
                                        @PathVariable("copScoreRecordId") Long copScoreRecordId) {
        return R.ok(iTCopScoreRecordService.queryById(copScoreRecordId));
    }

    /**
     * 新增企业信用扣分记录
     */
    @ApiOperation("新增企业信用扣分记录")
    @SaCheckPermission("cop:copScoreRecord:add")
    @Log(title = "企业信用扣分记录", businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Void> add(@Validated(AddGroup.class) @RequestBody TCopScoreRecordBo bo) {
        return toAjax(iTCopScoreRecordService.insertByBo(bo) ? 1 : 0);
    }

    /**
     * 修改企业信用扣分记录
     */
    @ApiOperation("修改企业信用扣分记录")
    @SaCheckPermission("cop:copScoreRecord:edit")
    @Log(title = "企业信用扣分记录", businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody TCopScoreRecordBo bo) {
        return toAjax(iTCopScoreRecordService.updateByBo(bo) ? 1 : 0);
    }

    /**
     * 删除企业信用扣分记录
     */
    @ApiOperation("删除企业信用扣分记录")
    @SaCheckPermission("cop:copScoreRecord:remove")
    @Log(title = "企业信用扣分记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{copScoreRecordIds}")
    public R<Void> remove(@ApiParam("主键串")
                          @NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] copScoreRecordIds) {
        return toAjax(iTCopScoreRecordService.deleteWithValidByIds(Arrays.asList(copScoreRecordIds), true) ? 1 : 0);
    }
}
