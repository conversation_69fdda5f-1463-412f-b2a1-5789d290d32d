package com.zhonghe.cop.score.domain.bo;

import com.zhonghe.common.core.domain.BaseEntity;
import com.zhonghe.common.core.validate.AddGroup;
import com.zhonghe.common.core.validate.EditGroup;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


/**
 * 企业信用扣分记录业务对象 t_cop_score_record
 *
 * <AUTHOR>
 * @date 2023-12-07
 */

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel("企业信用扣分记录业务对象")
public class TCopScoreRecordBo extends BaseEntity {

    private static final long serialVersionUID = -6610431849575858203L;
    /**
     * 企业分数ID
     */
    @ApiModelProperty(value = "企业分数ID", required = true)
    @NotNull(message = "企业分数ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long copScoreRecordId;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注", required = true)
    @NotBlank(message = "备注不能为空", groups = {AddGroup.class, EditGroup.class})
    private String note;

    /**
     * 部门上报表ID（对应t_rep_report主键）
     */
    @ApiModelProperty(value = "部门上报表ID（对应t_rep_report主键）", required = true)
    @NotNull(message = "部门上报表ID（对应t_rep_report主键）不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long repReportId;

    /**
     * 企业ID
     */
    @ApiModelProperty(value = "企业ID", required = true)
    @NotNull(message = "企业ID不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long copId;

    /**
     * 所扣分值
     */
    @ApiModelProperty(value = "所扣分值", required = true)
    @NotNull(message = "所扣分值不能为空", groups = {AddGroup.class, EditGroup.class})
    private Long deductScore;


}
