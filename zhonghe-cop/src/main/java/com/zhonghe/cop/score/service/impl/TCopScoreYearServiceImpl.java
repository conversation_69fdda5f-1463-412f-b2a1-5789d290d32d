package com.zhonghe.cop.score.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhonghe.common.constant.Constants;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.common.utils.StringUtils;
import com.zhonghe.cop.bam.domain.vo.TCopInformationVo;
import com.zhonghe.cop.bam.em.IsCancel;
import com.zhonghe.cop.bam.mapper.TCopInformationMapper;
import com.zhonghe.cop.score.domain.TCopScoreYear;
import com.zhonghe.cop.score.domain.bo.TCopScoreYearBo;
import com.zhonghe.cop.score.domain.dto.DeductScoreRecordDto;
import com.zhonghe.cop.score.domain.query.CopScoreYearQuery;
import com.zhonghe.cop.score.domain.vo.CopRiskVo;
import com.zhonghe.cop.score.domain.vo.TCopScoreYearInfoVo;
import com.zhonghe.cop.score.domain.vo.TCopScoreYearVo;
import com.zhonghe.cop.score.domain.vo.table.CopScoreYearTableVo;
import com.zhonghe.cop.score.mapper.TCopScoreRecordMapper;
import com.zhonghe.cop.score.mapper.TCopScoreYearMapper;
import com.zhonghe.cop.score.service.ITCopScoreYearService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 企业年度评分Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
@RequiredArgsConstructor
@Service
public class TCopScoreYearServiceImpl implements ITCopScoreYearService {

    private final TCopScoreYearMapper baseMapper;
    private final TCopScoreRecordMapper copScoreRecordMapper;
    private final TCopInformationMapper copInformationMapper;

    private QueryWrapper<TCopScoreYear> buildQueryWrapper(CopScoreYearQuery query) {
        QueryWrapper<TCopScoreYear> qw = new QueryWrapper<>();
        qw.apply("to_char(tcsy.score_date, 'yyyy') = {0}", query.getYear());
        qw.like(StringUtils.isNotBlank(query.getCopName()), "tci.cop_name", query.getCopName());
        qw.eq("tci.is_cancel", IsCancel.NO.getCode());
        qw.eq("tci.del_flag", Constants.DEL_FLAG_NORMAL);
        qw.eq("tcsy.del_flag", Constants.DEL_FLAG_NORMAL);
        qw.orderByDesc("tcsy.create_time");
        return qw;
    }

    /**
     * 查询企业年度评分
     */
    @Override
    public TCopScoreYearInfoVo queryById(Long copId) {
        TCopInformationVo tCopInformationVo = copInformationMapper.selectVoById(copId);
        List<CopRiskVo> copRiskVos = queryRisk(copId).stream().sorted(Comparator.comparing(CopRiskVo::getYear)).collect(Collectors.toList());
        return TCopScoreYearInfoVo.builder().copInformation(tCopInformationVo).copRisks(copRiskVos).build();
    }

    /**
     * 查询企业年度评分列表
     */
    @Override
    public TableDataInfo<TCopScoreYearVo> queryPageList(TCopScoreYearBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<TCopScoreYear> lqw = buildQueryWrapper(bo);
        Page<TCopScoreYearVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询企业年度评分列表
     */
    @Override
    public List<TCopScoreYearVo> queryList(TCopScoreYearBo bo) {
        LambdaQueryWrapper<TCopScoreYear> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<TCopScoreYear> buildQueryWrapper(TCopScoreYearBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<TCopScoreYear> lqw = Wrappers.lambdaQuery();
        lqw.eq(bo.getCopScoreYearId() != null, TCopScoreYear::getCopScoreYearId, bo.getCopScoreYearId());
        lqw.eq(StringUtils.isNotBlank(bo.getNote()), TCopScoreYear::getNote, bo.getNote());
        lqw.eq(bo.getCopId() != null, TCopScoreYear::getCopId, bo.getCopId());
        lqw.eq(bo.getCopScore() != null, TCopScoreYear::getCopScore, bo.getCopScore());
        lqw.eq(StringUtils.isNotBlank(bo.getScoreDate()), TCopScoreYear::getScoreDate, bo.getScoreDate());
        return lqw;
    }

    /**
     * 新增企业年度评分
     */
    @Override
    public Boolean insertByBo(TCopScoreYearBo bo) {
        TCopScoreYear add = BeanUtil.toBean(bo, TCopScoreYear.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setCopScoreYearId(add.getCopScoreYearId());
        }
        return flag;
    }

    /**
     * 修改企业年度评分
     */
    @Override
    public Boolean updateByBo(TCopScoreYearBo bo) {
        TCopScoreYear update = BeanUtil.toBean(bo, TCopScoreYear.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(TCopScoreYear entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    @Override
    public List<CopScoreYearTableVo> queryNowYearList(CopScoreYearQuery query) {
        QueryWrapper<TCopScoreYear> qw = buildQueryWrapper(query);
        List<CopScoreYearTableVo> copScoreYeas = baseMapper.queryNowYearList(qw);
        //返回分数最高的前十条数据
        List<CopScoreYearTableVo> result = copScoreYeas.stream().sorted((o1, o2) -> o2.getCopScore().compareTo(o1.getCopScore())).limit(10).collect(Collectors.toList());
        return result;
    }

    @Override
    public void resetScore() {

    }

    @Override
    public TableDataInfo<CopScoreYearTableVo> queryNowYearPage(CopScoreYearQuery query, PageQuery pageQuery) {
        QueryWrapper<TCopScoreYear> qw = buildQueryWrapper(query);
        Page<CopScoreYearTableVo> copScoreYearTableVos = baseMapper.queryNowYearList(qw, pageQuery.build());
        return TableDataInfo.build(copScoreYearTableVos);
    }

    @Override
    public List<CopRiskVo> queryRisk(Long copId) {
        List<CopRiskVo> copRiskVos = new ArrayList<>();
        List<TCopScoreYear> tCopScoreYears = baseMapper.selectList(
            Wrappers.<TCopScoreYear>lambdaQuery().eq(TCopScoreYear::getCopId, copId)
        );

        for (TCopScoreYear tCopScoreYear : tCopScoreYears) {
            CopRiskVo copRiskVo = new CopRiskVo();
            int year = DateUtil.year(tCopScoreYear.getScoreDate());
            copRiskVo.setYear(year);

            List<DeductScoreRecordDto> allRecords = copScoreRecordMapper
                .selectCopScoreRecordByCopIdAndYear(copId, year);

            Map<Integer, DeductScoreRecordDto> latestRecordPerMonth = allRecords.stream()
                .collect(Collectors.toMap(
                    record -> DateUtil.month(record.getRepDate()),
                    Function.identity(),
                    (oldRecord, newRecord) -> newRecord.getRepDate().after(oldRecord.getRepDate()) ? newRecord : oldRecord
                ));

            BigDecimal totalDeductScore = BigDecimal.ZERO;

            for (DeductScoreRecordDto record : latestRecordPerMonth.values()) {
                if (record.getDeductScore() != null) {
                    totalDeductScore = totalDeductScore.add(record.getDeductScore());
                }
            }
            // 限制最大不能超过 120
            BigDecimal fullScore = new BigDecimal("120");
            totalDeductScore = totalDeductScore.min(fullScore);
//            BigDecimal finalScore = fullScore.subtract(totalDeductScore).max(BigDecimal.ZERO).min(fullScore);

            copRiskVo.setTotalDeductScore(totalDeductScore.intValue());
            // 或 copRiskVo.setFinalScore(finalScore);

            copRiskVo.setDeductScoreRecord(new ArrayList<>(latestRecordPerMonth.values()));
            copRiskVos.add(copRiskVo);
        }

        return copRiskVos;
    }

    /**
     * 批量删除企业年度评分
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }
}
