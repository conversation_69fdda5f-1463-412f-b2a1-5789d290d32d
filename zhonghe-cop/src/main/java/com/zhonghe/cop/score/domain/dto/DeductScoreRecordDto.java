package com.zhonghe.cop.score.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @Author: lpg
 * @Date: 2023/12/07/17:55
 * @Description:
 */
@Data
public class DeductScoreRecordDto {
    /**
     * 存在问题
     */
    @ApiModelProperty(value = "存在问题")
    private String problem;
    /**
     * 报表月份
     */
    @ApiModelProperty(value = "报表月份")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date repDate;
    /**
     * 企业风险等级
     */
    @ApiModelProperty(value = "企业风险等级")
    private String level;
    /**
     * 扣分
     */
    @ApiModelProperty(value = "扣分")
    private BigDecimal deductScore;

}
