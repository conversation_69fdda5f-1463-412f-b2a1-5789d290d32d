package com.zhonghe.cop.score.mapper;


import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhonghe.common.core.mapper.BaseMapperPlus;
import com.zhonghe.cop.score.domain.TCopScoreYear;
import com.zhonghe.cop.score.domain.vo.TCopScoreYearVo;
import com.zhonghe.cop.score.domain.vo.table.CopScoreYearTableVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 企业年度评分Mapper接口
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
public interface TCopScoreYearMapper extends BaseMapperPlus<TCopScoreYearMapper, TCopScoreYear, TCopScoreYearVo> {

    /**
     * 查询企业年度评分列表
     *
     * @param
     * @return
     */
    List<CopScoreYearTableVo> queryNowYearList(@Param(Constants.WRAPPER) QueryWrapper<TCopScoreYear> build);

    Page<CopScoreYearTableVo> queryNowYearList(@Param(Constants.WRAPPER) QueryWrapper<TCopScoreYear> qw, Page<TCopScoreYear> page);

    int updateScore(@Param("copId") Long copId, @Param("deductScore") Integer deductScore, @Param("year") Integer year);
}
