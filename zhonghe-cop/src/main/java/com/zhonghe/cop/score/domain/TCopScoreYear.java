package com.zhonghe.cop.score.domain;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zhonghe.common.core.domain.BaseEntity;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;


/**
 * 企业年度评分对象 t_cop_score_year
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("cop.t_cop_score_year")
@Builder
@JsonSerialize
public class TCopScoreYear extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 企业年度评分ID
     */
    @TableId
    private Long copScoreYearId;
    /**
     * 是否删除
     */
    @TableLogic
    private String delFlag;
    /**
     * 备注
     */
    private String note;
    /**
     * 企业ID
     */
    private Long copId;
    /**
     * 企业年度信用评分（默认120分，每年复位）
     */
    private Integer copScore;
    /**
     * 评分时间（年度）
     */
    private Date scoreDate;

}
