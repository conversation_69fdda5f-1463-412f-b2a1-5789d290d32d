package com.zhonghe.cop.score.mapper;


import com.zhonghe.common.core.mapper.BaseMapperPlus;
import com.zhonghe.cop.score.domain.TCopScoreRecord;
import com.zhonghe.cop.score.domain.dto.DeductScoreRecordDto;
import com.zhonghe.cop.score.domain.vo.TCopScoreRecordVo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 企业信用扣分记录Mapper接口
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
public interface TCopScoreRecordMapper extends BaseMapperPlus<TCopScoreRecordMapper, TCopScoreRecord, TCopScoreRecordVo> {

    /**
     * 查询企业信用扣分记录列表
     *
     * @param copId
     * @param year
     * @return
     */
    List<DeductScoreRecordDto> selectCopScoreRecordByCopIdAndYear(@Param("copId") Long copId, @Param("year") int year);
}
