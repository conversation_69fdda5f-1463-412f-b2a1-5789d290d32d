package com.zhonghe.cop.score.service;

import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.cop.score.domain.bo.TCopScoreRecordBo;
import com.zhonghe.cop.score.domain.vo.TCopScoreRecordVo;

import java.util.Collection;
import java.util.List;

/**
 * 企业信用扣分记录Service接口
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
public interface ITCopScoreRecordService {

    /**
     * 查询企业信用扣分记录
     */
    TCopScoreRecordVo queryById(Long copScoreRecordId);

    /**
     * 查询企业信用扣分记录列表
     */
    TableDataInfo<TCopScoreRecordVo> queryPageList(TCopScoreRecordBo bo, PageQuery pageQuery);

    /**
     * 查询企业信用扣分记录列表
     */
    List<TCopScoreRecordVo> queryList(TCopScoreRecordBo bo);

    /**
     * 修改企业信用扣分记录
     */
    Boolean insertByBo(TCopScoreRecordBo bo);

    /**
     * 修改企业信用扣分记录
     */
    Boolean updateByBo(TCopScoreRecordBo bo);

    /**
     * 校验并批量删除企业信用扣分记录信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 企业风险扣分
     *
     * @param copId
     * @param level
     * @param repReportId
     */
    void deductionScore(Long copId, Integer level, Long repReportId);

    /**
     * 根据上报id删除
     *
     * @param ids
     */
    void deleteByRepReportIds(Collection<Long> ids);
}
