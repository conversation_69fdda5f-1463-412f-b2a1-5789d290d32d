package com.zhonghe.cop.score.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.zhonghe.common.core.controller.BaseController;
import com.zhonghe.common.core.domain.PageQuery;
import com.zhonghe.common.core.domain.R;
import com.zhonghe.common.core.page.TableDataInfo;
import com.zhonghe.cop.score.domain.query.CopScoreYearQuery;
import com.zhonghe.cop.score.domain.vo.TCopScoreYearInfoVo;
import com.zhonghe.cop.score.domain.vo.table.CopScoreYearTableVo;
import com.zhonghe.cop.score.service.ITCopScoreYearService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 企业年度评分Controller
 *
 * <AUTHOR>
 * @date 2023-12-07
 */
@Validated
@Api(value = "企业年度评分控制器", tags = {"企业年度评分管理"})
@RequiredArgsConstructor
@RestController
@RequestMapping("/cop/copScoreYear")
public class TCopScoreYearController extends BaseController {

    private final ITCopScoreYearService iTCopScoreYearService;


    /**
     * 查询企业年度评分列表
     */
    @ApiOperation("查询企业年度评分列表(cop:copScoreYear:list)")
    @SaCheckPermission("cop:copScoreYear:list")
    @GetMapping("/list")
    public TableDataInfo<CopScoreYearTableVo> queryNowYearPage(CopScoreYearQuery query, PageQuery pageQuery) {
        return iTCopScoreYearService.queryNowYearPage(query, pageQuery);
    }

    /**
     * 查询企业年度评分统计
     */
    @ApiOperation("查询企业年度评分统计(cop:copScoreYear:list)")
    @SaCheckPermission("cop:copScoreYear:list")
    @GetMapping("/statistics")
    public R<List<CopScoreYearTableVo>> queryNowYearList(CopScoreYearQuery query) {
        return R.ok(iTCopScoreYearService.queryNowYearList(query));
    }

    /**
     * 查询企业年度评分信息
     */
    @ApiOperation("查询企业年度评分(cop:copScoreYear:query)")
    @SaCheckPermission("cop:copScoreYear:query")
    @GetMapping("/{copId}")
    public R<TCopScoreYearInfoVo> queryById(@ApiParam("企业ID")
                                            @NotNull(message = "企业ID不能为空")
                                            @PathVariable("copId") Long copId) {
        return R.ok(iTCopScoreYearService.queryById(copId));
    }

}
