<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhonghe.cop.score.mapper.TCopScoreRecordMapper">
    <resultMap type="com.zhonghe.cop.score.domain.TCopScoreRecord" id="TCopScoreRecordResult">
        <result property="copScoreRecordId" column="cop_score_record_id"/>
        <result property="createTime" column="create_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="note" column="note"/>
        <result property="repReportId" column="rep_report_id"/>
        <result property="copId" column="cop_id"/>
        <result property="deductScore" column="deduct_score"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>

    <select id="selectCopScoreRecordByCopIdAndYear"
            resultType="com.zhonghe.cop.score.domain.dto.DeductScoreRecordDto">
        SELECT trr.note as problem, trr.rep_date, trr.level, tcsr.deduct_score
        FROM cop.t_rep_report trr
                 LEFT JOIN cop.t_cop_score_record tcsr ON tcsr.rep_report_id = trr.rep_report_id
        WHERE tcsr.cop_id = #{copId}
          AND EXTRACT(YEAR FROM trr.rep_date) = #{year}
          AND trr.del_flag = '0'
          AND tcsr.del_flag = '0'
        order by trr.rep_date desc;
    </select>
</mapper>
