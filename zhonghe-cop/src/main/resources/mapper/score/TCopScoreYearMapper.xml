<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhonghe.cop.score.mapper.TCopScoreYearMapper">
    <resultMap type="com.zhonghe.cop.score.domain.TCopScoreYear" id="TCopScoreYearResult">
        <result property="copScoreYearId" column="cop_score_year_id"/>
        <result property="createTime" column="create_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="note" column="note"/>
        <result property="copId" column="cop_id"/>
        <result property="copScore" column="cop_score"/>
        <result property="scoreDate" column="score_date"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>
    <update id="updateScore">
        update cop.t_cop_score_year
        set cop_score = (cop_score - #{deductScore})
        where cop_id = #{copId}
          and DATE_PART('year', score_date) = #{year}
    </update>
    <select id="queryNowYearList" resultType="com.zhonghe.cop.score.domain.vo.table.CopScoreYearTableVo">
        select tci.cop_id
             , tci.cop_name
             , tci.cop_phone
             , tci.property_id
             , tci.cop_owner
             , tci.cop_address
             , tcsy.cop_score
        from cop.t_cop_information tci
                 inner join cop.t_cop_score_year tcsy
                            on tci.cop_id = tcsy.cop_id
                                ${ew.customSqlSegment}
    </select>
</mapper>
