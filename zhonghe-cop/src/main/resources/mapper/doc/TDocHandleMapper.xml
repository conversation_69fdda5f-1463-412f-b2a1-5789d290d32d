<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhonghe.cop.doc.mapper.TDocHandleMapper">
    <resultMap type="com.zhonghe.cop.doc.domain.TDocHandle" id="TDocHandleResult">
        <result property="docHandleId" column="doc_handle_id"/>
        <result property="createTime" column="create_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="note" column="note"/>
        <result property="docInfoId" column="doc_info_id"/>
        <result property="deptId" column="dep_id"/>
        <result property="dealDate" column="deal_date"/>
        <result property="dealDescription" column="deal_description"/>
        <result property="dealBasis" column="deal_basis"/>
        <result property="dealMeasure" column="deal_measure"/>
        <result property="dealResult" column="deal_result"/>
        <result property="dealEnlighten" column="deal_enlighten"/>
        <result property="dealReport" column="deal_report"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>

    <select id="selectReportList" resultType="com.zhonghe.cop.doc.domain.vo.TDocHandleVo">
        select tdh.*
             , tci.cop_name
        from cop.t_doc_handle tdh
                 left join cop.t_doc_info tdi
                           on tdh.doc_info_id = tdi.doc_info_id
                 left join cop.t_cop_information tci
                           on tdi.cop_id = tci.cop_id
        where tdh.del_flag = '0'
          and tdi.del_flag = '0'
          and tci.del_flag = '0'
          and tdh.doc_info_id = #{docInfoId}
        order by tdh.deal_date desc
    </select>

    <select id="selectDocHandleList" resultType="com.zhonghe.cop.doc.domain.table.TDocInfoTableVo">
        select tdi.doc_info_id
             , tdi.doc_date
             , tdi.deal_description
             , tci.cop_name
             , tci.cop_owner
             , tci.cop_phone
             , tci.cop_address
             , tci.property_id
        from cop.t_doc_info tdi
                 left join cop.t_cop_information tci
                           on tdi.cop_id = tci.cop_id
                               ${ew.customSqlSegment}
    </select>
</mapper>
