<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhonghe.cop.bam.mapper.TMesRecordMapper">

    <resultMap type="com.zhonghe.cop.bam.domain.TMesRecord" id="TMesRecordResult">
        <result property="mesRecordId" column="mes_record_id"/>
        <result property="createTime" column="create_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="note" column="note"/>
        <result property="phone" column="phone"/>
        <result property="content" column="content"/>
        <result property="sendDate" column="send_date"/>
        <result property="sendType" column="send_type"/>
        <result property="userId" column="user_id"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="deptId" column="dept_id"/>
    </resultMap>
    <select id="selectMesRecordList" resultType="com.zhonghe.cop.bam.domain.vo.TMesRecordVo">
        select distinct tmr.batch_no,
                        t1.user_id_arr,
                        t1.send_object,
                        t1.content,
                        t1.phone,
                        tmr.send_date,
                        tmr.send_type,
                        tmr.contact_type_id,
                        t1.send_message
        from (select tmr.batch_no,
                     STRING_AGG(distinct cast(tmr.user_id as varchar), ',')                          as user_id_arr,
                     STRING_AGG(distinct cast(tmr.send_object as varchar), ',')                      as send_object,
                     STRING_AGG(distinct cast(tmr.content as varchar), ',')                          as content,
                     STRING_AGG(distinct cast(tmr.phone as varchar), ',')                            as phone,
                     STRING_AGG(distinct cast(tmr.phone || ':' || tmr.send_message as varchar), ',') as send_message
              from cop.t_mes_record tmr
                       left join public.sys_user su on tmr.user_id = su.user_id
              where tmr.batch_no is not null
                and tmr.del_flag = '0'
              GROUP BY tmr.batch_no) t1
                 left join cop.t_mes_record tmr on t1.batch_no = tmr.batch_no
            ${ew.getCustomSqlSegment}
    </select>
    <select id="selectVoByBatchNo" resultType="com.zhonghe.cop.bam.domain.vo.TMesRecordVo">
        select distinct tmr.batch_no,
                        t1.user_id_arr,
                        t1.send_object,
                        t1.phone,
                        t1.content,
                        tmr.contact_type_id,
                        tmr.send_type
        from (select tmr.batch_no,
                     STRING_AGG(distinct cast(tmr.user_id as varchar), ',')     as user_id_arr,
                     STRING_AGG(distinct cast(tmr.send_object as varchar), ',') as send_object,
                     STRING_AGG(distinct cast(tmr.phone as varchar), ',')       as phone,
                     STRING_AGG(distinct cast(tmr.content as varchar), ',')     as content
              from cop.t_mes_record tmr
              where tmr.batch_no = #{batchNo}
                and tmr.del_flag = '0'
              GROUP BY tmr.batch_no) t1
                 left join cop.t_mes_record tmr on t1.batch_no = tmr.batch_no
    </select>


</mapper>
