<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhonghe.cop.bam.mapper.TCopInformationMapper">
    <resultMap type="com.zhonghe.cop.bam.domain.TCopInformation" id="TCopInformationResult">
        <result property="copId" column="cop_id"/>
        <result property="createTime" column="create_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="note" column="note"/>
        <result property="copName" column="cop_name"/>
        <result property="copCode" column="cop_code"/>
        <result property="copOwner" column="cop_owner"/>
        <result property="copIncharge" column="cop_incharge"/>
        <result property="corporateCode" column="corporate_code"/>
        <result property="businessLicence" column="business_licence"/>
        <result property="taxRegistration" column="tax_registration"/>
        <result property="copPopulation" column="cop_population"/>
        <result property="copAddress" column="cop_address"/>
        <result property="copPhone" column="cop_phone"/>
        <result property="copFax" column="cop_fax"/>
        <result property="officeProperty" column="office_property"/>
        <result property="officeOwner" column="office_owner"/>
        <result property="officeOwnerPhone" column="office_owner_phone"/>
        <result property="propertyId" column="property_id"/>
        <result property="orgId" column="org_id"/>
        <result property="lon" column="lon"/>
        <result property="lat" column="lat"/>
        <result property="isCancel" column="is_cancel"/>
        <result property="scopeOfBusiness" column="scope_of_business"/>
        <result property="copContacts" column="cop_contacts"/>
        <result property="leaseTerm" column="lease_term"/>
        <result property="by1" column="by1"/>
        <result property="by2" column="by2"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="geom" column="geom"/>
    </resultMap>
    <select id="selectCopList" resultType="com.zhonghe.cop.bam.domain.vo.TCopInformationVo">
        select tci.*, tdo.org_name
        from cop.t_cop_information tci
                 left join cop.t_dic_org tdo on tci.org_id = tdo.org_id
            ${ew.customSqlSegment}
    </select>
    <select id="selectVoById" resultType="com.zhonghe.cop.bam.domain.vo.TCopInformationVo">
        select tci.*, tdo.org_name
        from cop.t_cop_information tci
                 left join cop.t_dic_org tdo on tci.org_id = tdo.org_id
        where tci.cop_id = #{copId}
    </select>
    <select id="selectListDel" resultType="com.zhonghe.cop.bam.domain.TCopInformation">
        select *
        from cop.t_cop_information_1
    </select>
</mapper>
