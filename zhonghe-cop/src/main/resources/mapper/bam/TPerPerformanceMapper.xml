<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhonghe.cop.bam.mapper.TPerPerformanceMapper">

    <resultMap type="com.zhonghe.cop.bam.domain.TPerPerformance" id="TPerPerformanceResult">
        <result property="perPerformanceId" column="per_performance_id"/>
        <result property="createTime" column="create_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="note" column="note"/>
        <result property="repDate" column="rep_date"/>
        <result property="deptId" column="dept_id"/>
        <result property="isDeal" column="is_deal"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>
    <select id="selectDeptPerformance" resultType="com.zhonghe.cop.bam.domain.vo.DeptPerformanceVo"
            parameterType="com.zhonghe.cop.bam.domain.query.TPerPerformanceQuery">
        select t1.*, d.dept_name
        from (SELECT tpp.dept_id,
        EXTRACT(YEAR FROM tpp.rep_date) AS year,
        MAX(CASE
        WHEN EXTRACT(MONTH FROM tpp.rep_date) = 1 AND tpp.is_deal = 1 THEN 1
        ELSE 0 END) AS january,
        MAX(CASE
        WHEN EXTRACT(MONTH FROM tpp.rep_date) = 2 AND tpp.is_deal = 1 THEN 1
        ELSE 0 END) AS february,
        MAX(CASE
        WHEN EXTRACT(MONTH FROM tpp.rep_date) = 3 AND tpp.is_deal = 1 THEN 1
        ELSE 0 END) AS march,
        MAX(CASE
        WHEN EXTRACT(MONTH FROM tpp.rep_date) = 4 AND tpp.is_deal = 1 THEN 1
        ELSE 0 END) AS april,
        MAX(CASE WHEN EXTRACT(MONTH FROM tpp.rep_date) = 5 AND tpp.is_deal = 1 THEN 1 ELSE 0 END) AS may,
        MAX(CASE WHEN EXTRACT(MONTH FROM tpp.rep_date) = 6 AND tpp.is_deal = 1 THEN 1 ELSE 0 END) AS june,
        MAX(CASE WHEN EXTRACT(MONTH FROM tpp.rep_date) = 7 AND tpp.is_deal = 1 THEN 1 ELSE 0 END) AS july,
        MAX(CASE
        WHEN EXTRACT(MONTH FROM tpp.rep_date) = 8 AND tpp.is_deal = 1 THEN 1
        ELSE 0 END) AS august,
        MAX(CASE
        WHEN EXTRACT(MONTH FROM tpp.rep_date) = 9 AND tpp.is_deal = 1 THEN 1
        ELSE 0 END) AS september,
        MAX(CASE
        WHEN EXTRACT(MONTH FROM tpp.rep_date) = 10 AND tpp.is_deal = 1 THEN 1
        ELSE 0 END) AS october,
        MAX(CASE
        WHEN EXTRACT(MONTH FROM tpp.rep_date) = 11 AND tpp.is_deal = 1 THEN 1
        ELSE 0 END) AS november,
        MAX(CASE
        WHEN EXTRACT(MONTH FROM tpp.rep_date) = 12 AND tpp.is_deal = 1 THEN 1
        ELSE 0 END) AS december
        FROM cop.t_per_performance tpp
        WHERE tpp.del_flag = '0'
        <if test="ew.year != null and ew.year != ''">
            and EXTRACT(YEAR FROM tpp.rep_date) IN (${ew.year})
        </if>
        <if test="ew.deptId != null and ew.deptId != ''">
            and tpp.dept_id = #{ew.deptId}
        </if>
        GROUP BY tpp.dept_id, year
        ORDER BY tpp.dept_id, year) t1
        INNER JOIN "public".sys_dept d on t1.dept_id = d.dept_id
        order by d.order_num
    </select>


</mapper>
