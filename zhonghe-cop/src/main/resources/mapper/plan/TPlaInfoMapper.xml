<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhonghe.cop.plan.mapper.TPlaInfoMapper">
    <resultMap type="com.zhonghe.cop.plan.domain.TPlaInfo" id="TPlaInfoResult">
        <result property="plaInfoId" column="pla_info_id"/>
        <result property="createTime" column="create_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="note" column="note"/>
        <result property="repDate" column="rep_date"/>
        <result property="copId" column="cop_id"/>
        <result property="repGarrepId" column="rep_garrep_id"/>
        <result property="isPlan" column="is_plan"/>
        <result property="depts" column="depts"/>
        <result property="despacho" column="despacho"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>

    <select id="selectPlanInfoList" resultType="com.zhonghe.cop.plan.domain.vo.table.TPlaInfoTableVo">
        select tpi.pla_info_id
             , tpi.is_plan
             , tpi.create_time
             , tci.cop_name
             , tci.cop_owner
             , tci.cop_phone
             , tci.property_id
        from cop.t_pla_info tpi
                 left join cop.t_cop_information tci
                           on tpi.cop_id = tci.cop_id
                               ${ew.customSqlSegment}
    </select>
</mapper>
