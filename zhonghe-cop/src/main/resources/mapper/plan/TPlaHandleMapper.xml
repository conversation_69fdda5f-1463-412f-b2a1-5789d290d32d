<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhonghe.cop.plan.mapper.TPlaHandleMapper">
    <resultMap type="com.zhonghe.cop.plan.domain.TPlaHandle" id="TPlaHandleResult">
        <result property="plaHandleId" column="pla_handle_id"/>
        <result property="createTime" column="create_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="note" column="note"/>
        <result property="plaInfoId" column="pla_info_id"/>
        <result property="depId" column="dep_id"/>
        <result property="copCondition" column="cop_condition"/>
        <result property="planMeasure" column="plan_measure"/>
        <result property="planBasis" column="plan_basis"/>
        <result property="planHelp" column="plan_help"/>
        <result property="planOther" column="plan_other"/>
    </resultMap>

    <select id="selectPlaHandleList" resultType="com.zhonghe.cop.plan.domain.vo.table.TPlaHandleTableVo">
        select tpi.pla_info_id,
               tpi.is_plan,
               tpi.rep_date,
               tpi.despacho,
               tci.cop_name,
               tci.cop_owner,
               tci.cop_phone,
               tci.property_id
        from cop.t_pla_info tpi
                 left join cop.t_cop_information tci
                           on tpi.cop_id = tci.cop_id
                               ${ew.customSqlSegment}
    </select>
</mapper>
