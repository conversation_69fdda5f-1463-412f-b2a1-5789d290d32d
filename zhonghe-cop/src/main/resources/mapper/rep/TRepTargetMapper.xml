<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhonghe.cop.rep.mapper.TRepTargetMapper">
    <resultMap type="com.zhonghe.cop.rep.domain.TRepTarget" id="TRepTargetResult">
        <result property="repTargetId" column="rep_target_id"/>
        <result property="createTime" column="create_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="note" column="note"/>
        <result property="tarName" column="tar_name"/>
        <result property="tarValueType" column="tar_value_type"/>
        <result property="tarCode" column="tar_code"/>
        <result property="tarType" column="tar_type"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>

    <select id="selectListByDeptId" resultMap="TRepTargetResult">
        select trt.*
        from cop.t_rep_target trt
                 inner join cop.t_rep_deptar trd on trt.rep_target_id = trd.rep_target_id
        where trd.dep_id = #{deptId}
          and trt.tar_value_type = 0
          and trt.del_flag = '0'
          and trd.del_flag = '0'
        order by trd.order
    </select>
</mapper>
