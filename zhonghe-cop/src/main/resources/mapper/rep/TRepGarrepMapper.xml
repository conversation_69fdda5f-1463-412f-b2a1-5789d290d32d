<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhonghe.cop.rep.mapper.TRepGarrepMapper">
    <resultMap type="com.zhonghe.cop.rep.domain.TRepGarrep" id="TRepGarrepResult">
        <result property="repGarrepId" column="rep_garrep_id"/>
        <result property="createTime" column="create_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="note" column="note"/>
        <result property="repDate" column="rep_date"/>
        <result property="copId" column="cop_id"/>
        <result property="levelAll" column="level_all"/>
        <result property="level" column="level"/>
        <result property="propaymonth" column="propaymonth"/>
        <result property="propaypopulation" column="propaypopulation"/>
        <result property="propaymoney" column="propaymoney"/>
        <result property="protaxmonth" column="protaxmonth"/>
        <result property="protaxmoney" column="protaxmoney"/>
        <result property="proinsuremonth" column="proinsuremonth"/>
        <result property="proinsuremoney" column="proinsuremoney"/>
        <result property="prowatermonth" column="prowatermonth"/>
        <result property="prowatermoney" column="prowatermoney"/>
        <result property="proenergymonth" column="proenergymonth"/>
        <result property="proenergymoney" column="proenergymoney"/>
        <result property="prorentmonth" column="prorentmonth"/>
        <result property="prorentmoney" column="prorentmoney"/>
        <result property="problem" column="problem"/>
        <result property="copoversee" column="copoversee"/>
        <result property="copadvice" column="copadvice"/>
        <result property="copdeal" column="copdeal"/>
        <result property="copresult" column="copresult"/>
        <result property="copafterwork" column="copafterwork"/>
        <result property="cophelp" column="cophelp"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>

    <select id="selectRepGarrepList" resultType="com.zhonghe.cop.rep.domain.vo.table.TRepGarrepTableVo">
        select trg.rep_garrep_id
             , trg.copoversee
             , trg.copadvice
             , trg.copdeal
             , trg.copresult
             , trg.copafterwork
             , trg.cophelp
             , tci.cop_id
             , tci.cop_name
             , tci.cop_owner
             , tci.cop_phone
             , tci.property_id
             , tci.cop_address
             , tci.corporate_code
             , tci.business_licence
             , trg.level
             , trg.rep_date
             , trg.propaymonth
             , trg.propaymonths
             , trg.propaypopulation
             , trg.propaymoney
             , trg.protaxmonth
             , trg.protaxmonths
             , trg.protaxmoney
             , trg.proinsuremonth
             , trg.proinsuremonths
             , trg.proinsuremoney
             , trg.prowatermonth
             , trg.prowatermonths
             , trg.prowatermoney
             , trg.proenergymonth
             , trg.proenergymonths
             , trg.proenergymoney
             , trg.prorentmonth
             , trg.prorentmonths
             , trg.prorentmoney
             , trg.other
             , trg.problem
             , trg.note
        from cop.t_rep_garrep trg
                 left join cop.t_cop_information tci
                           on trg.cop_id = tci.cop_id
                 left join cop.t_dic_org tdo on tci.org_id = tdo.org_id
            ${ew.customSqlSegment}
    </select>
    <select id="selectRepReportList" resultType="com.zhonghe.cop.rep.domain.vo.TRepReportVo">
        select trr.rep_report_id
             , trr.cop_id
             , trr.rep_date
             , trr.copoversee
             , trr.copadvice
             , trr.copdeal
             , trr.copresult
             , trr.copafterwork
             , trr.cophelp
             , trr.level
             , trr.note
             , trr.problem
             , trr.despacho
             , trr.auditing
             , trr.auditing_comment
             , trr.rep_date
             , trr.result_auditing
             , trr.result_auditing_comment
             , d.dept_name
        from cop.t_rep_report trr
                 left join public.sys_dept d on trr.dept_id = d.dept_id
        where trr.del_flag = '0'
          and trr.cop_id = #{copId}
          AND TO_CHAR(trr.rep_date, 'YYYY-MM') = #{repDate}
    </select>

    <select id="copLevelReportList" resultType="com.zhonghe.cop.rep.domain.vo.CopLevelVo">
        select t1.level, count(t1.cop_id) as count
        from (select trg.cop_id, trg.level, trg.rep_date, trg.del_flag
            from cop.t_rep_garrep trg
            left join cop.t_cop_information tci
            on tci.cop_id = trg.cop_id) t1
        where to_char(t1.rep_date
            , 'yyyy-MM') = #{yearMonth}
          and t1.del_flag = '0'
        group by t1.level
    </select>

    <select id="copReportList" resultType="com.zhonghe.cop.rep.domain.vo.CopReportVo">
        select trg.cop_id,trg.level,trg.rep_date,tci.cop_name ,tci.lon ,tci.lat
        from cop.t_rep_garrep trg inner join cop.t_cop_information tci
        on tci.cop_id = trg.cop_id and trg.del_flag = '0'
        where to_char(trg.rep_date, 'yyyy-MM') = #{yearMonth}
        <if test="copName != null and copName != ''">
            and tci.cop_name like concat('%',#{copName},'%')
        </if>
    </select>
    <select id="select2MonthCop" resultType="com.zhonghe.cop.bam.domain.vo.TCopInformationVo">
        select tci.*
        from cop.t_cop_information tci
                 inner join (SELECT cop_id
                             FROM (SELECT cop_id,
                                          rep_date,
                                          ROW_NUMBER() OVER (PARTITION BY cop_id ORDER BY rep_date) AS rn
                                   FROM cop.t_rep_garrep trg
                                   WHERE trg.rep_date >= trg.rep_date - INTERVAL '1 month'
                                     and to_char(rep_date
                                       , 'yyyy-MM') =
                                       to_char(current_date - INTERVAL '1 month'
                                       , 'yyyy-MM')
                                     and trg.level = 4) subquery
                             WHERE rn = 1) t on tci.cop_id = t.cop_id
    </select>
</mapper>
