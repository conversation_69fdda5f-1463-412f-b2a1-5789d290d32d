<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhonghe.cop.rep.mapper.TRepReportMapper">
    <resultMap type="com.zhonghe.cop.rep.domain.TRepReport" id="TRepReportResult">
        <result property="repReportId" column="rep_report_id"/>
        <result property="createTime" column="create_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="note" column="note"/>
        <result property="repDate" column="rep_date"/>
        <result property="userId" column="user_id"/>
        <result property="copId" column="cop_id"/>
        <result property="depId" column="dep_id"/>
        <result property="tarId" column="tar_id"/>
        <result property="tarDecimal" column="tar_decimal"/>
        <result property="tarNvarchar" column="tar_nvarchar"/>
        <result property="auditing" column="auditing"/>
        <result property="resultAuditing" column="result_auditing"/>
        <result property="level" column="level"/>
        <result property="batchNum" column="batch_num"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>

    <select id="selectRepReportList" resultType="com.zhonghe.cop.rep.domain.vo.table.TRepReportTableVo">
        select trr.rep_report_id
             , trr.copoversee
             , trr.copadvice
             , trr.copdeal
             , trr.copresult
             , trr.copafterwork
             , trr.cophelp
             , tci.cop_id
             , tci.cop_name
             , tci.cop_owner
             , tci.cop_phone
             , tci.property_id
             , trr.level
             , trr.problem
             , trr.despacho
             , trr.auditing
             , trr.rep_date
             , trr.result_auditing
             , trr.finish_status
             , trr.propaymonth
             , trr.propaymonths
             , trr.propaypopulation
             , trr.propaymoney
             , trr.protaxmonth
             , trr.protaxmonths
             , trr.protaxmoney
             , trr.proinsuremonth
             , trr.proinsuremonths
             , trr.proinsuremoney
             , trr.prowatermonth
             , trr.prowatermonths
             , trr.prowatermoney
             , trr.proenergymonth
             , trr.proenergymonths
             , trr.proenergymoney
             , trr.prorentmonth
             , trr.prorentmonths
             , trr.prorentmoney
             , trr.other
             , d.dept_name
        from cop.t_rep_report trr
                 left join cop.t_cop_information tci
                           on trr.cop_id = tci.cop_id
                 left join public.sys_dept d on trr.dept_id = d.dept_id
            ${ew.customSqlSegment}
    </select>

    <select id="queryMonthReport" resultType="com.zhonghe.cop.rep.domain.vo.MonthReportVo">
        SELECT EXTRACT(YEAR FROM trr.rep_date) AS year,
               COUNT(*)                                                               AS total,
               SUM(CASE WHEN EXTRACT(MONTH FROM trr.rep_date) = 2 THEN 1 ELSE 0 END)  AS january,
               SUM(CASE WHEN EXTRACT(MONTH FROM trr.rep_date) = 3 THEN 1 ELSE 0 END)  AS february,
               SUM(CASE WHEN EXTRACT(MONTH FROM trr.rep_date) = 4 THEN 1 ELSE 0 END)  AS march,
               SUM(CASE WHEN EXTRACT(MONTH FROM trr.rep_date) = 5 THEN 1 ELSE 0 END)  AS april,
               SUM(CASE WHEN EXTRACT(MONTH FROM trr.rep_date) = 6 THEN 1 ELSE 0 END)  AS may,
               SUM(CASE WHEN EXTRACT(MONTH FROM trr.rep_date) = 7 THEN 1 ELSE 0 END)  AS june,
               SUM(CASE WHEN EXTRACT(MONTH FROM trr.rep_date) = 8 THEN 1 ELSE 0 END)  AS july,
               SUM(CASE WHEN EXTRACT(MONTH FROM trr.rep_date) = 9 THEN 1 ELSE 0 END)  AS august,
               SUM(CASE WHEN EXTRACT(MONTH FROM trr.rep_date) = 10 THEN 1 ELSE 0 END) AS september,
               SUM(CASE WHEN EXTRACT(MONTH FROM trr.rep_date) = 11 THEN 1 ELSE 0 END) AS october,
               SUM(CASE WHEN EXTRACT(MONTH FROM trr.rep_date) = 12 THEN 1 ELSE 0 END) AS november,
               SUM(CASE WHEN EXTRACT(MONTH FROM trr.rep_date) = 1 THEN 1 ELSE 0 END)  AS december
        FROM cop.t_rep_garrep trr
        WHERE EXTRACT (YEAR FROM trr.rep_date) IN (#{year} - 1
            , #{year})
          and trr.del_flag = '0'
        GROUP BY year
        ORDER BY year desc;
    </select>

    <select id="queryQuarterReport" resultType="com.zhonghe.cop.rep.domain.vo.QuarterReportVo">
        SELECT EXTRACT(YEAR FROM trr.rep_date) AS year,
               COUNT(*)                                                                                 AS total,
               COUNT(CASE WHEN EXTRACT(MONTH FROM trr.rep_date) BETWEEN 1 AND 3 THEN 1 ELSE NULL END)   AS q1,
               COUNT(CASE WHEN EXTRACT(MONTH FROM trr.rep_date) BETWEEN 4 AND 6 THEN 1 ELSE NULL END)   AS q2,
               COUNT(CASE WHEN EXTRACT(MONTH FROM trr.rep_date) BETWEEN 7 AND 9 THEN 1 ELSE NULL END)   AS q3,
               COUNT(CASE WHEN EXTRACT(MONTH FROM trr.rep_date) BETWEEN 10 AND 12 THEN 1 ELSE NULL END) AS q4
        FROM cop.t_rep_garrep trr
        WHERE EXTRACT (YEAR FROM trr.rep_date) IN (#{year}
            , #{year} - 1)
        GROUP BY year
        ORDER BY year desc;
    </select>

    <select id="queryYearReport" resultType="com.zhonghe.cop.rep.domain.vo.YearReportVo">
        SELECT EXTRACT(YEAR FROM trr.rep_date) AS year,
               COUNT(*)                        AS total
        FROM cop.t_rep_garrep trr
        WHERE EXTRACT (YEAR FROM trr.rep_date) BETWEEN #{startYear}
          AND #{endYear}
          AND trr.del_flag = '0'
        GROUP BY year
        ORDER BY year;
    </select>
    <select id="dataFormat" resultType="com.zhonghe.cop.rep.domain.dto.CopLevelDto">
        SELECT distinct t.cop_id       as enterprise_id,
                        t.cop_name     as enterprise_name,
                        t.cop_owner    as legal_person,
                        t.cop_phone    as contact_phone,
                        t.cop_address  as enterprise_address,
                        t.cop_property as enterprise_property,
                        t.geom         AS geom,
                        t.problem,
                        t.copadvice,
                        t.copafterwork,
                        t.copdeal,
                        t.cophelp,
                        t.copoversee,
                        t.copresult
        FROM (select tci.*,
                     tdc.cop_property,
                     trr.problem,
                     trr.copadvice,
                     trr.copafterwork,
                     trr.copdeal,
                     trr.cophelp,
                     trr.copoversee,
                     trr.copresult
              from cop.t_rep_garrep trr
                       left join cop.T_COP_Information tci on trr.cop_id = tci.cop_id
                       left join cop.T_DIC_CopLevel coplv on trr."level" = coplv.level_value
                       left join cop.t_dic_copproperty tdc on tdc.property_id = tci.property_id


              where DATE_PART('year', trr.rep_date) = #{year}
                and coplv.level_name = #{level}
                and tci.geom IS not null
              ORDER BY trr.create_time desc) t;
    </select>
</mapper>
