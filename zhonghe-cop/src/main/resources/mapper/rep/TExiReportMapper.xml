<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhonghe.cop.rep.mapper.TExiReportMapper">
    <resultMap type="com.zhonghe.cop.rep.domain.TExiReport" id="TExiReportResult">
        <result property="exiReportId" column="exi_report_id"/>
        <result property="createTime" column="create_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="note" column="note"/>
        <result property="deptId" column="dept_id"/>
        <result property="copId" column="cop_id"/>
        <result property="description" column="description"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>

    <select id="selectExiReportList" resultType="com.zhonghe.cop.rep.domain.vo.table.TExiReportTableVo">
        select ter.exi_report_id, tci.cop_name, d.dept_name, ter.description, ter.despacho
        from cop.t_exi_report ter
                 left join cop.t_cop_information tci on ter.cop_id = tci.cop_id
                 left join public.sys_dept d on ter.dept_id = d.dept_id
            ${ew.customSqlSegment}
    </select>
</mapper>
