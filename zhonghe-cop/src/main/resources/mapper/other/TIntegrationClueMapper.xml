<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
    PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.zhonghe.cop.other.mapper.BmqzjProWoFormFinishPgMapper">


    <select id="selectPageList" resultType="com.zhonghe.cop.other.domain.vo.BmqzjProWoFormFinishPgVo">
        SELECT d1.*,
               CASE
                   WHEN d2.data_id IS NOT NULL THEN d2.state
                   ELSE d1.state
                   END AS state
        FROM public.bmqzj_pro_wo_form_pg_0vs d1
                 LEFT JOIN
             public.bmqzj_pro_wo_form_finish_pg d2
             ON d1.data_id = d2.data_id
                 ${ew.customSqlSegment}
    </select>
</mapper>
